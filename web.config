﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <connectionStrings>
    <!--系統DB, 測試區:140.96.1.105, 上線區:ITRIDPS-->
    <!--add name="ConnString" connectionString="Data Source=140.96.1.105,5555;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient" /-->
    <add name="ConnString" connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@20250308@**********" providerName="System.Data.SqlClient"/>
    <add name="CS_APMS" connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=APMS;User ID=pubAPMS;Password=************************" providerName="System.Data.SqlClient"/>
    <add name="norcontConnectionString" connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=norcont;Persist Security Info=True;User ID=pubnorcont;Password=************************" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <!--<add key="TestEmpNo" value="950327"/>-->
    <!-- 模擬 Admin EmpNo(總系統負責人)，以開啟某些功能。 -->
  </appSettings>
  <!--
    如需 web.config 變更的說明，請參閱 http://go.microsoft.com/fwlink/?LinkId=235367。

    您可以在 <httpRuntime> 標記上設定下列屬性。
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" validate="false"/>
    </httpHandlers>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="Microsoft.Build.Framework, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91"/>
      </buildProviders>
    </compilation>
    <httpRuntime maxRequestLength="1024000" executionTimeout="6000" requestValidationMode="2.0"/>
    <customErrors mode="Off"/>
    <!--加長 Session Timeout時間為1小時-->
    <sessionState timeout="60" mode="InProc" cookieless="false"/>
    <machineKey validationKey="F2114380046EEFF45DEB5CF94C2BFA67B19FBAC09ADE68089FE74C6FF11AE6C5FC55668A625E4DB055E35E03C97930914E0B40FC5311AC6C34441AC7AB0EA7E8" decryptionKey="1091E6F95C460985A72B3C62682C0334F3FC3873E81391128190452AF61A2A5E" validation="SHA1" decryption="AES"/>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="**********"/>
      </requestFiltering>
    </security>
  </system.webServer>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91"/>
    </handlers>
  </system.webServer>
</configuration>