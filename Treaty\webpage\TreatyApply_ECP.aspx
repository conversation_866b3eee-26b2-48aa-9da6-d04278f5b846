﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApply_ECP.aspx.cs" Inherits="TreatyApply_ECP" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <%--<script type="text/javascript" src="../Scripts/autoheight.js"></script>--%>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            // $("#txt_doc").val(compare);
        }

        var obj_employee;
        /* 單人挑選 */
        function Find_Empno_single(obj) {
            obj_employee = obj;
            $.colorbox({
                href: "../../Comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx"
                , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                //, title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../Comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });
            return false;
        }
        function Find_Empno_single2(obj) {
            obj_employee = obj;
            $.colorbox({
                href: "../../Comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx"
                , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                //, title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../Comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback2);
                }
            });
            return false;
        }
        function Find_empno_kw(obj) {
            obj_employee = obj;
            var strURL = "../../Comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($(obj_employee).closest('td').find('.c_com_cname').val());
            $.getJSON(strURL + '&callback=?', jsonp_callback1);
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    //alert("查無此人 或 空值!");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($(obj_employee).closest('td').find('.c_com_cname').val())
                        , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                        //, title: '單人挑選'
                        , onClosed: function () {
                            $(obj_employee).closest('td').find('.c_com_cname').val('');
                            $(obj_employee).closest('td').find('.c_com_empno').val('');
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback1);
                        }
                    });
                    break;
                default:
                    $(obj_employee).closest('td').find('.c_com_cname').val(data.c_com_cname);
                    $(obj_employee).closest('td').find('.c_com_empno').val(data.c_com_empno);
                //__doPostBack('renew', '');
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    //alert("查無此人 或 空值!");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($(obj_employee).closest('td').find('.c_com_cname').val())
                        , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                        //, title: '單人挑選'
                        , onClosed: function () {
                            $('input[id$=TB_會計承辦人]').val('');
                            $('input[id$=TB_會計承辦人_no]').val('');
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    });
                    break;
                default:
                    $('input[id$=TB_會計承辦人]').val(data.c_com_cname);
                    $('input[id$=TB_會計承辦人_no]').val(data.c_com_empno);
            }
        }
        /* 多人挑選 */
        function Find_Empno_multi(obj, arg_sw) {
            $(".ajax_mesg_multi").colorbox({
                href: "../Comp/EmployeeMultiSelect/EmployeeWindow.aspx?hfValue=" + $('#' + obj).val()
                , iframe: true, width: "780px", height: "525px", transition: "none", opacity: "0.5", overlayClose: false
                //, title: '多人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../Comp/EmployeeMultiSelect/ret_employee_kw.aspx';
                    if (arg_sw == "3") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback3);
                    }
                }
            });
            return false;
        }
        function jsonp_callback3(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    break;
                default:
                    $('input[id*=empnamelist]').val(data.c_com_cname);
                    $('input[id*=empnolist]').val(data.c_com_empno);
            }
        }

        function advancd_search()//進階查詢
        {

            if (advancesearch.className.indexOf("ADV_off") > 0) {
                advancesearch.className = advancesearch.className.replace(/ADV_off/g, "ADV_on");
            }
            else {
                advancesearch.className = advancesearch.className.replace(/ADV_on/g, "ADV_off");

            }
        }
    </script>
    <style type="text/css">
        .mask {
            z-index: 9999;
            position: fixed;
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-content: center;
            flex-wrap: wrap;
            /*background-color: #000;
            opacity: 0.5;*/
        }

        .ADV_on {
            display: inline
        }

        .ADV_off {
            display: none
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>

                <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="UpdatePanel1" DisplayAfter="50">
                    <ProgressTemplate>
                        <div class="mask">
                            <font color='red' style="margin: 40%"><b> <asp:Image ID="Image1" runat="server" ImageUrl="../images/roller.gif" />Processing........</b></font>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
                <div class="stripeMe" style="margin-left: 15px; margin-top: 25px">
                    <div style="text-align: right; color: red;"><b>「內部機密不得外流」</b></div>
                    <div class="font-title font-bold font-size3">預覽流程及送簽 </div>

                    <div style="text-align: right;">
                        <asp:PlaceHolder ID="PH_view" runat="server" Visible="false">
                            <a href="#" onclick="window.open('base_InCome_modify.aspx?c_id=<%=ViewState["c_id"].ToString()%>');">檢視契約履約管理系統收款資料</a>
                        </asp:PlaceHolder>
                        <asp:Button ID="btnAddFirst" runat="server" OnClick="btnAddFirst_Click" Text="加簽第一關" CssClass="btnGen" />&nbsp;&nbsp;&nbsp;&nbsp;
                    </div>

                    <asp:GridView ID="gvList_signflow" runat="server" AutoGenerateColumns="False" Width="90%" CellPadding="0" CellSpacing="0" ShowHeaderWhenEmpty="True" OnRowDataBound="gvList_signflow_RowDataBound" OnRowCommand="gvList_signflow_RowCommand" CssClass="auto-style1">
                        <HeaderStyle HorizontalAlign="Center" Wrap="false" />
                        <AlternatingRowStyle CssClass="alt" />
                        <Columns>
                            <asp:TemplateField HeaderText="功能" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="10%">
                                <ItemTemplate>
                                    <asp:HiddenField ID="gv_hf_rowsn" runat="server" Value='<%# Eval("rowsn") %>' />
                                    <asp:LinkButton ID="lbtnNew" runat="server" CommandArgument='<%# Eval("rowsn") %>' CommandName="CMD_AddRow" Seq='<%# Eval("Seq") %>'>新增</asp:LinkButton>
                                    <asp:LinkButton ID="lbtnDel" runat="server" CommandArgument='<%# Eval("rowsn") + ";" + Eval("ActRoleName") %>' CommandName="CMD_DelRow" Seq='<%# Eval("Seq") %>'>刪除</asp:LinkButton>
                                    <%--<asp:LinkButton ID="lbtnNotice" runat="server" Enabled="false" ForeColor="Gray">副知</asp:LinkButton>--%>
                                </ItemTemplate>
                                <HeaderStyle Width="10%"></HeaderStyle>
                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="順序">
                                <HeaderStyle Width="10%"></HeaderStyle>
                                <ItemTemplate>
                                    <asp:Literal ID="lit_Seq" runat="server" Text='<%# Server.HtmlEncode(Eval("Seq").ToString()) %>'></asp:Literal>
                                </ItemTemplate>
                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="簽辦順序" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="15%">
                                <ItemTemplate>
                                    <asp:Literal ID="lit_SignType" runat="server" Text='<%# Server.HtmlEncode(Eval("SignType").ToString()) %>' Visible="false"></asp:Literal>
                                    <asp:Literal ID="lit_SignSigle" runat="server" Text='<%# Server.HtmlEncode(Eval("SignSigle").ToString()) %>' Visible="false"></asp:Literal>
                                    <asp:Literal ID="lit_IS_LOCK" runat="server" Text='<%# Server.HtmlEncode(Eval("IS_LOCK").ToString()) %>' Visible="false"></asp:Literal>
                                    <asp:Literal ID="lit_ActRoleName" runat="server" Text='<%# Server.HtmlEncode(Eval("ActRoleName").ToString()) %>'></asp:Literal>
                                </ItemTemplate>
                                <HeaderStyle Width="15%"></HeaderStyle>
                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="簽核類型" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="15%">
                                <ItemTemplate>
                                    <%# GetSignClassName(Server.HtmlEncode(Eval("SignClass").ToString())) %>
                                    <asp:HiddenField ID="hf_SignClass" runat="server" Value='<%# Server.HtmlEncode(Eval("SignClass").ToString()) %>' />
                                </ItemTemplate>
                                <HeaderStyle Width="15%"></HeaderStyle>
                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="簽核人員" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="30%">
                                <ItemTemplate>
                                    <asp:Panel ID="pnl_Empno" runat="server">
                                        <asp:TextBox ID="txt_com_cname" runat="server" CssClass="inputsizeSS c_com_cname textboxReadonly" Text='<%# Server.HtmlEncode(Eval("RecUserName").ToString().Trim()) %>'
                                            onchange="Find_empno_kw(this);"></asp:TextBox>
                                        <asp:LinkButton ID="btn_find1" runat="server" Style="vertical-align: middle" Seq='<%# Eval("Seq") %>'
                                            OnClientClick="return Find_Empno_single(this);"><img src="../../images/icon-lookdetail.png" /></asp:LinkButton>
                                        <asp:TextBox ID="txt_com_empno" runat="server" class="inputsizeSS c_com_empno textboxReadonly" Text='<%# Server.HtmlEncode(Eval("RecUserID").ToString().Trim()) %>'></asp:TextBox>
                                    </asp:Panel>
                                    <asp:Label ID="lbl_Empno1" runat="server" Visible="false" CssClass="c_com_cname"></asp:Label>
                                </ItemTemplate>

                                <HeaderStyle Width="30%"></HeaderStyle>

                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                            </asp:TemplateField>
                        </Columns>
                    </asp:GridView>


                    <div class="left" style="text-align: left">
                        <asp:Literal ID="LT_業管人員" runat="server"></asp:Literal><br />
                        <font color='red'>注意:簽核只檢查最後一關為會簽人員最大者，中間簽核順序請自行依情況斟酌調整!!</font>
                        <div id="advancesearch" class="gentablenoline font-normal margin5TB ADV_off" title="進階查詢">
                            <asp:TextBox ID="TB_pass_check" runat="server" AUTOCOMPLETE="OFF" TextMode="Password"></asp:TextBox>
                        </div>
                    </div>
                    <div class="right" style="text-align: right">
                        <a href="#" id="advancesearchopen" onclick="advancd_search()" style="text-decoration: none; font-size: 10px; margin-left: -5px; color: white">.</a>


                        <asp:Button ID="btnReGen" runat="server" CssClass="btnGen" Text="重新產生簽核流程" OnClientClick="return confirm('是否重新產生簽核流程?');return false;" OnClick="btnReGen_Click" />
                        <asp:Button ID="btnLeave" runat="server" CssClass="btnGen" Text="取消" OnClientClick="parent.$.colorbox.close();" />
                        <asp:Button ID="btnSign" runat="server" CssClass="btnGen" Text="送簽" OnClick="btnSign_Click" OnClientClick="return confirm('請確認 簽核流程 是否正確?');return false;" />&nbsp;&nbsp;&nbsp;&nbsp;

                      

                        <%--<img alt="loading" src="../../images/loading_black.gif" id="imgsign" style="display: none;" />--%>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
</body>
</html>
