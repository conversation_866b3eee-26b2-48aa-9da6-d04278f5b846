﻿<%@ Control Language="C#" AutoEventWireup="true" CodeFile="calendar.ascx.cs" Inherits="calendar" %>
<script type="text/javascript" src="../../scripts/jquery-3.2.1.js"></script>
<script type="text/javascript" src="../../scripts/jquery-ui.min.js"></script>
<script type="text/javascript" src="../../Comp/Calendar/js/jquery-ui-timepicker-addon.js"></script>

<link rel="stylesheet" href="../Comp/Calendar/css/timepicker.css" />
<script>
    function timePicker(tbID, isImgTrigger, isOnlyDate, hfID) {
        $(document).ready(function () {
            if (isOnlyDate == '1') {
                $(tbID).datepicker({
                    closeText: '確定',
                    changeYear: true,
                    changeMonth: true,
                    dateFormat: 'yy/mm/dd',
                    showButtonPanel: true,
                    currentText: '今天',
                    onClose: function () { $(hfID).val($(tbID).val()); }
                });
            }
            else {
                $(tbID).datetimepicker({
                    controlType: 'myControl',
                    //controlType: 'select',
                    closeText: '確定',
                    changeYear: true,
                    changeMonth: true,
                    dateFormat: 'yy/mm/dd',
                    timeFormat: 'HH:mm',
                    onClose: function () { $(hfID).val($(tbID).val()); }
                    //minDateTime: new Date('2014/04/15 12:00')
                    //onSelect: function () { if ($('.timePicker').val() > '2014/04/18 10:00') { alert('超過時間'); } }
                });
            }

            if (isImgTrigger == '1') {
                $(tbID).datetimepicker("option", "showOn", "button");
                $(tbID).datetimepicker("option", "buttonImage", "./Comp/Calendar/images/icon_calendar.png");
                $(tbID).datetimepicker("option", "buttonImageOnly", true);
            }
        });
    }

    // hack to add clear button
    // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
    //wrap up the redraw function with our new shiz
    var dpFunc = $.datepicker._generateHTML; //record the original
    $.datepicker._generateHTML = function (inst) {
        var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original

        thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context

        //locate the button panel and add our button - with a custom css class.
        $('.ui-datepicker-buttonpane', thishtml).append(
            $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
            ).click(function () {
                inst.input.val(''); //attr value chrome not work
                inst.input.attr('value', '');
                inst.input.datepicker('hide');
            })
        );

        thishtml = thishtml.children(); //remove the wrapper div

        return thishtml; //assume okay to return a jQuery
    };

</script>
<asp:TextBox ID="timeTb" runat="server" Height="18px" Width="83px"></asp:TextBox>
<asp:TextBox ID="timeHdTb" runat="server" Text="" Style="display: none"></asp:TextBox>