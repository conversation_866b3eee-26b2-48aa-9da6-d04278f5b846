﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_Lawer.aspx.cs" Inherits="TreatyCase2_Lawer" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("更新成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 40px">



                <tr>
                    <td class="td_right">委任日期：</td>
                    <td>
                        <asp:TextBox ID="TB_recordate" class="pickdate inputex inputsizeS" runat="server" Width="80px" MaxLength="8" />
                    </td>
                </tr>

                <tr>
                    <td class="td_right">委任律師：</td>
                    <td>
                        <asp:TextBox ID="TB_Lawer" class="inputex inputsizeS" runat="server" Width="80px" MaxLength="8" />
                    </td>
                </tr>
                <tr>
                    <td class="td_right">摘要：</td>
                    <td>
                        <asp:TextBox ID="TB_Docu" runat="server" Width="544px" Height="43px" TextMode="MultiLine"></asp:TextBox></td>
                </tr>
                <tr>
                    <td class="td_right" colspan="2">
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>


            </table>

            <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

        </span>

        <script type="text/javascript">
            $(document).ready(function () {
                $(function () {
                    $(".pickdate").datepicker({
                        changeMonth: true,
                        changeYear: true,
                        dateFormat: 'yymmdd',
                        monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                        dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                        showButtonPanel: true,
                        closeText: '關閉',
                        currentText: '移至今天'

                    });

                    // hack to add clear button
                    // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                    //wrap up the redraw function with our new shiz
                    var dpFunc = $.datepicker._generateHTML; //record the original
                    $.datepicker._generateHTML = function (inst) {
                        var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                        thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                        //locate the button panel and add our button - with a custom css class.
                        $('.ui-datepicker-buttonpane', thishtml).append(
                            $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                            ).click(function () {
                                inst.input.attr('value', '');
                                inst.input.datepicker('hide');
                            })
                        );
                        thishtml = thishtml.children(); //remove the wrapper div
                        return thishtml; //assume okay to return a jQuery
                    };
                    // 增加清除按鈕 -End				
                });
            });
        </script>
    </form>
</body>
</html>
