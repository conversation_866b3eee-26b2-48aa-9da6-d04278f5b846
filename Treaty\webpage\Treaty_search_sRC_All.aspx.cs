﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Web.UI;
using System.Web.UI.WebControls;
using Treaty_report;

public partial class Treaty_webpage_Treaty_search_sRC_All : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }

    protected void Page_Load(object sender, EventArgs e)
    {
        tbx_promoter_name.Attributes.Add("onChange", string.Format("Find_empno_kw('{0}',1);", tbx_promoter_name.ClientID));

        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["sortField"] = "";
            ViewState["sortorder"] = "";
           // tbx_promoter_name.Attributes["readonly"] = "readonly";
           // tbx_handle_name.Attributes["readonly"] = "readonly";

            Bind_Orgcd();
            Bind_ContType();
            Bind_Status();
            Bind_Year();
        }
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        foreach (ListItem item in cbxCaseClass.Items)
            item.Selected = false;

        ddl_orgcd.SelectedIndex = 0;
        tbx_compname.Text = "";
        ddl_conttype.SelectedIndex = 0;
        tbx_promoter_name.Text = "";
        tbx_promoter_empno.Text = "";
        ddl_org_status.SelectedIndex = 0;
        tbx_handle_name.Text = "";
        tbx_handle_empno.Text = "";
        ddl_year.SelectedIndex = 0;
        tbx_kw.Text = "";
        ddl_amend.SelectedIndex = 0;
        cb_sRC.Checked = false;
    }

    protected void btnExcel_Click(object sender, EventArgs e)
    {
        DataTable dt = GetSearchData();

        if (dt != null && dt.Rows.Count >= 1)
        {
            DataTable dtExport = new DataTable();
            dtExport.Columns.Add("案源");
            dtExport.Columns.Add("單位");
            dtExport.Columns.Add("客戶名稱");
            dtExport.Columns.Add("契約名稱");
            dtExport.Columns.Add("契約性質");
            dtExport.Columns.Add("契約金額");
            dtExport.Columns.Add("契約起日");
            dtExport.Columns.Add("契約迄日");
            dtExport.Columns.Add("規劃案號");
            dtExport.Columns.Add("草約編號");
            dtExport.Columns.Add("單位承辦人");
            dtExport.Columns.Add("法務承辦人");
            dtExport.Columns.Add("單位處理情形");
            dtExport.Columns.Add("單位說明");
            dtExport.Columns.Add("收件日期");
            dtExport.Columns.Add("法務意見");

            foreach (DataRow dr in dt.Rows)
            {
                DataRow drBody = dtExport.NewRow();
                drBody["案源"] = dr["case_className"];
                drBody["單位"] = dr["單位別"];
                drBody["客戶名稱"] = dr["compname"];
                drBody["契約名稱"] = dr["casename"];
                drBody["契約性質"] = dr["契約性質"];
                drBody["契約金額"] = string.Format("{0:###,###}", dr["amount"]);
                drBody["契約起日"] = dr["contsdate"];
                drBody["契約迄日"] = dr["contedate"];
                drBody["規劃案號"] = dr["pre_contno"];
                drBody["草約編號"] = dr["actcontno"];
                drBody["單位承辦人"] = dr["promoter_name"];
                drBody["單位處理情形"] = dr["單位處理情形"];
                drBody["法務承辦人"] = dr["handle_name"];
                drBody["單位說明"] = dr["單位說明"];
                drBody["收件日期"] = dr["收件日期"];
                drBody["法務意見"] = dr["法務意見"];

                dtExport.Rows.Add(drBody);
            }

            CreateExcel(dtExport);
        }
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {
        Bind_Data("", "");
    }

    #region SGV
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }

    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.PageIndex = e.NewPageIndex;

        // SGV_search.DataBind();
    }

    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            Response.Redirect("./TechCase_view.aspx?tt_seno=" + e.CommandArgument.ToString());
        }
    }

    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lbl_remark = (Label)e.Row.FindControl("lbl_remark");
            if (lbl_remark != null)
                lbl_remark.Attributes.Add("rel", "Treaty_search_sRC_All_tip_View.aspx?columnName=org_remark&case_id=" + DataBinder.Eval(e.Row.DataItem, "case_id").ToString());


            Label lbl_betsum = (Label)e.Row.FindControl("lbl_betsum");
            if (lbl_betsum != null)
                lbl_betsum.Attributes.Add("rel", "Treaty_search_sRC_All_tip_View.aspx?columnName=betsum&case_id=" + DataBinder.Eval(e.Row.DataItem, "case_id").ToString());
        }
    }

    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    #endregion

    private void Bind_Data(string str_sortField, string str_sort)
    {
        if (IsDangerWord(ddl_orgcd.SelectedValue) || (ddl_orgcd.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddl_conttype.SelectedValue) || (ddl_conttype.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");

        if (IsDangerWord(tbx_compname.Text.ToUpper())) Response.Redirect("../danger.aspx");

        if (tbx_compname.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");

        DataTable dt = GetSearchData();
        SGV_search.DataSource = dt;
        SGV_search.DataBind();
    }

    private DataTable GetSearchData()
    {
        List<string> case_classList = new List<string>();
        foreach (ListItem item in cbxCaseClass.Items)
        {
            if (item.Selected)
                case_classList.Add(item.Value);
        }
        string case_class = string.Join(",", case_classList);

        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "search");

            sqlCmd.Parameters.AddWithValue("@case_class", oRCM.SQLInjectionReplaceAll(case_class));//案源
            sqlCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(ddl_orgcd.SelectedValue));//單位別
            sqlCmd.Parameters.AddWithValue("@compName", oRCM.SQLInjectionReplaceAll(tbx_compname.Text.Trim()));//客戶名稱
            sqlCmd.Parameters.AddWithValue("@conttype", oRCM.SQLInjectionReplaceAll(ddl_conttype.SelectedValue));//契約性質
            sqlCmd.Parameters.AddWithValue("@promoter_name", oRCM.SQLInjectionReplaceAll(tbx_promoter_name.Text));//單位承辦人員
            sqlCmd.Parameters.AddWithValue("@org_status", oRCM.SQLInjectionReplaceAll(ddl_org_status.SelectedValue));//單位處理情形
            sqlCmd.Parameters.AddWithValue("@handle_name", oRCM.SQLInjectionReplaceAll(tbx_handle_name.Text));//法務承辦人
            sqlCmd.Parameters.AddWithValue("@case_year", oRCM.SQLInjectionReplaceAll(ddl_year.Text));//年度
            sqlCmd.Parameters.AddWithValue("@kw", oRCM.SQLInjectionReplaceAll(tbx_kw.Text));//關鍵字查詢
            sqlCmd.Parameters.AddWithValue("@is_sRC", cb_sRC.Checked ? "1" : "0");//報院條件
            sqlCmd.Parameters.AddWithValue("@is_amend", oRCM.SQLInjectionReplaceAll(ddl_amend.SelectedValue));//修約類

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        return dt;
    }

    private void Bind_Orgcd()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "orgcd");

            try
            {
                DataTable dt = new DataTable();

                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

                ddl_orgcd.DataSource = dt;
                ddl_orgcd.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                //oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Bind_ContType()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "conttype");

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddl_conttype.DataSource = dt;
                ddl_conttype.DataBind();
                ddl_conttype.Items.Insert(0, new ListItem(" ", ""));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Bind_Status()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "init_status");

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddl_org_status.DataSource = dt;
                ddl_org_status.DataBind();
                ddl_org_status.Items.Insert(0, new ListItem(" ", ""));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Bind_Year()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "init_year");

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddl_year.DataSource = dt;
                ddl_year.DataBind();
                ddl_year.Items.Insert(0, new ListItem(" ", ""));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void CreateExcel(DataTable dataSource)
    {
        string filePath = Server.MapPath("../../Template/內部查詢明細_sRC_All.xlsx");
        if (File.Exists(filePath) == false)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('找不到樣板檔，無法匯出檔案');
        </script>
        ", false);
            return;
        }

        asposeExcel excel = new asposeExcel("內部查詢明細_sRC_All.xlsx");
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();
        sheet.Cells.ImportDataTable(dataSource, true, "B7");
        //sheet.Cells.Merge(dataSource.Rows.Count + 9, 0, 2, 17); //第幾行開始 , 起始位置 , 合併幾列 ,合併幾欄
        excel.PutValue(sheet, dataSource.Rows.Count + 9, 0, "工業技術研究院機密資料 禁止複製、轉載、外流 │ITRI CONFIDENTIAL DOCUMENT DO NOT COPY OR DISTRIBUTE");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        //針對表頭style控制
        objStyle.HorizontalAlignment = Aspose.Cells.TextAlignmentType.Center;
        objStyle.Pattern = Aspose.Cells.BackgroundType.Solid;
        objStyle.ForegroundColor = Color.LightGreen;

        sheet.Cells.ApplyRowStyle(6, objStyle, objStyleFlag);

        ExportExcel("報表.xlsx", excel.exportExcel());
    }

    private void ExportExcel(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);

        Response.Clear();
        Response.ContentType = "Application/vnd.ms-excel";
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }
}