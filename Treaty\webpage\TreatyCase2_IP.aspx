﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_IP.aspx.cs" Inherits="Treaty_webpage_TreatyCase2_IP" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("新增成功!");
            parent.$.fn.colorbox.close();
        }
        function Find_IP() {
            $(".ajax_ip").colorbox({
                href: './TreatyCase2_IP_Find.aspx',
                title: '挑選專利'
                , iframe: true, width: "850px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../subap/ret_ip.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callbackIP);

                }
            });

        }
        function jsonp_callbackIP(data) {
            switch (data.c_compcname) {
                case "error0":
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    $("#TB_IpName").val(data.c_pntcnn);
                    $("#DDL_country").val(data.c_applynation);
                    $("#TB_IpNo").val(data.c_cerpntno);
                    $("#TB_cerpntno").val(data.c_patentno);
                    break;
            }
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .auto-style1 {
            width: 390px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 40px; width: 503px;">
                <tr>
                    <td class="td_right">智權型態：</td>
                    <td class="auto-style1">
                        <asp:RadioButtonList ID="RBL_ip_type" runat="server" AutoPostBack="True" RepeatDirection="Horizontal" DataTextField="subtype_desc" DataValueField="code_subtype" BorderWidth="0px" OnSelectedIndexChanged="RBL_ip_type_SelectedIndexChanged"></asp:RadioButtonList>
                        <%--<asp:SqlDataSource ID="SDS_ip_type" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">專利件號：</td>
                    <td class="auto-style1">
                        <asp:TextBox ID="TB_cerpntno" runat="server" class="inputex inputsizeM"></asp:TextBox>
                        <a onclick="javascript:Find_IP();" class="ajax_ip" title="挑選專利" id="BT_IP" runat="server">
                            <img id="img_promoter_name" border="0" class="ajax_mesg btn_mouseout" src="../images/icon_search.gif" /></a>
                        <div style="display: none">
                            <asp:TextBox ID="TB_IpNo" runat="server" class="inputex inputsizeM"></asp:TextBox>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">國別：</td>
                    <td class="auto-style1">
                        <asp:DropDownList ID="DDL_country" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True">
                            <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                        </asp:DropDownList>
                        <%--<asp:SqlDataSource ID="SDS_country" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                    </td>
                </tr>
                <tr>
                    <td class="td_right"><font color="#ff0000">*</font>名稱：</td>
                    <td class="auto-style1">
                        <asp:TextBox ID="TB_IpName" runat="server" class="inputex inputsizeL" Width="400px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right"></td>
                    <td class="auto-style1">
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>
            </table>
            <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        </span>

    </form>
</body>
</html>
