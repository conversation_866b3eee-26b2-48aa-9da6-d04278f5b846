﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCaseQ_view.aspx.cs" Inherits="TreatyCaseQ_view" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/jquery-migrate-1.2.1.js"></script>
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />

    <script type="text/javascript" src="../Scripts/autosize.min.js"></script>
    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
          , title: '客戶相關契約資料'
          , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
          }
            });
        }
        function treaty_Inspect(seno, tci_no) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Inspect.aspx?seno=" + seno + "&tci_no=" + tci_no
                , title: '案件審查'
          , iframe: true, width: "720px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("Inspect_renew", 0);
          }
            });
        }
        function treaty_fileup() {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileUpZ.aspx?contno=" + ($("#DDL_SeqSn option:selected").text()).replace("-", "") + "&seno=" + $("#DDL_SeqSn").val()
          , title: '結案後檔案上傳'
          , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("file_renew", 0);
          }
            });
        }
        function DeleteCase() {
            alert("案件已刪除!");
            location.replace("./TreatyApply.aspx");
        }
        function treatyCancle(seno) {
            if (confirm('確定要取消需求?\\t <<取消前請知會法務人員>> ')) {
                $(".ajax_mesg").colorbox({
                    href: "./TreatyCase_Cancle.aspx?seno=" + $("#DDL_SeqSn").val()
              , title: '議約需求取消'
              , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
              , onClosed: function () {
                  $('html, body').css('overflow', '');
                  reflash_topic("case_renew", 0);
              }
                });
            }
        }
        function treatyCaseAssign(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assign.aspx?seno=" + seno
          , title: '指派法務人員承辦'
          , iframe: true, width: "900px", height: "700px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("case_renew", 0);
          }
            });
        }
        function SatisfView(seno) {
            $(".ajax_mesg").colorbox({
                href: "./SatisfactionView.aspx?seno=" + seno
          , title: '滿意度'
          , iframe: true, width: "900px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("case_renew", 0);
          }
            });
        }
        function EndCase(seno) {
            alert('已結案!');
            location.replace('./TreatyCase_view.aspx?seno=' + seno);
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_tratycase_info" runat="server">案件紀錄</asp:Literal>
                                    <asp:Button class="ajax_mesg genbtnS" ID="BT_Inspect2" runat="server" Text="案件審查" Visible="False" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit" Text="編輯" OnClick="btEdit_Click" Visible="False" />
                                    <asp:Button ID="BT_caseAssign0" runat="server" class="ajax_mesg genbtnS" Text="分案指派" Visible="False" />
                                    <asp:Button runat="server" class="genbtnS" ID="BT_End1" Text="結案通知" OnClick="BT_End_Click" Visible="false"></asp:Button>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">

                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:DropDownList ID="DDL_SeqSn" runat="server" Width="160px"  DataTextField="contno" DataValueField="seno" AutoPostBack="True" OnSelectedIndexChanged="DDL_SeqSn_SelectedIndexChanged"></asp:DropDownList>
                                            <asp:Label ID="txtComplexNo" runat="server" Text="" Visible="false"></asp:Label>
                                            (舊案號:
                                            <asp:Label ID="txtOldContno" runat="server"></asp:Label>)
                        
                        <%--<asp:SqlDataSource ID="SDS_DDL_SeqSn" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <asp:PlaceHolder ID="案件語文" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                            </td>
                                            <td class="width40">
                                                <asp:Label ID="LB_language" runat="server" Text=""></asp:Label>
                                                <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" Visible="false" />
                                                <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" Visible="false" />
                                                <asp:RadioButton ID="rb_language_other" runat="server" Text="其他" GroupName="ContractLang" Visible="false" />
                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label>&nbsp;
                                            <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server" Width="95px"></asp:Label>
                                            &nbsp;
                       分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server" Width="110px"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="LB_hec_flag" runat="server" Text="【重大效益案件】<br />" Font-Bold="True" ForeColor="Red" Visible="false"></asp:Label>
                                            <asp:Label ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">

                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None"  OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="廠商編號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_company" runat="server" Text='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_cname" HeaderText="廠商中文名稱">
                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_ename" HeaderText="廠商英文名稱">
                                                                <HeaderStyle Width="350px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            案件性質</td>
                                        <td class="lineheight03">
                                            <asp:Label ID="LB_case_style" runat="server" Text=""></asp:Label>
                                            <asp:DropDownList ID="DDL_case_style" runat="server"  DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false"></asp:DropDownList>
                                            <%--<asp:SqlDataSource ID="SDS_case_style" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <asp:PlaceHolder ID="案件分類" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">案件分類</div>
                                            </td>
                                            <td>
                                                <asp:Label ID="LB_ContType" runat="server" Text=""></asp:Label>
                                                <asp:DropDownList ID="ddlContType" runat="server" Width="165px"  DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True" Visible="false">
                                                    <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                                </asp:DropDownList>
                                                <%--<asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>

                                    <tr>
                                        <asp:PlaceHolder ID="預定期間" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">預定期間</div>
                                            </td>
                                            <td>
                                                <asp:Label ID="txt_contsdate" runat="server" />&nbsp;至&nbsp;
                                                <asp:Label ID="txt_contedate" runat="server"></asp:Label>
                                            </td>
                                        </asp:PlaceHolder>
                                        <asp:PlaceHolder ID="契約預估金額" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">契約預估金額</div>
                                            </td>
                                            <td>
                                                <asp:Label ID="LB_ContMoneyType" runat="server" Text=""></asp:Label>&nbsp;
                    <asp:DropDownList ID="ddlContMoneyType" runat="server" Width="144px" DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false" />
                                                <%--<asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  " />--%>
                                                <asp:Label ID="txtContMoney" runat="server" />
                                                &nbsp;元
                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>

                                    <tr runat="server" id="案號股別" visible="false">
                                        <td align="right">
                                            <div class="font-title titlebackicon">案號股別</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="TB_案號股別" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">案件緣由與目的</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txtSignReason" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                            <asp:Image ID="img_txtSignReason" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_SignReason" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人意見彙整</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:TextBox ID="txt_betsum" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                            <asp:Image ID="Image2" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_betsum" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務備註</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:TextBox ID="txtManageNote" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                            <asp:Image ID="Image4" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Button ID="BT_FileUp" class="ajax_mesg genbtnS" runat="server" Text="結件後補掛附件" Visible="false" />
                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" >
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="修改概要">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="280px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="審查">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_inspect" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_inspect").ToString()) %>'></asp:Label>
                                                                <asp:Label ID="LB_tcdf_type" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_type").ToString()) %>' Visible="false"></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="30px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="常用版本">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_3" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="50px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>


                                    <asp:PlaceHolder ID="PL_Inspect" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">審查資訊</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:Button class="ajax_mesg genbtnS" ID="BT_Inspect" runat="server" Text="案件審查" Visible="False" />
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False"  OnRowDataBound="GV_Inspect_RowDataBound" Width="610px">
                                                        <Columns>
                                                            <asp:BoundField DataField="tci_order" HeaderText="順序">
                                                                <ItemStyle HorizontalAlign="Center" Width="45px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tci_inspect_desc" HeaderText="簽核意見">
                                                                <ItemStyle Width="350px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="簽核狀態">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_Istatus" runat="server" Text='<%# Server.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tci_inspect_time" HeaderText="簽核日期">
                                                                <ItemStyle HorizontalAlign="Left" Width="250px" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Inspect" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                                                    <asp:SqlDataSource ID="SDS_Inspect_count" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_tc_manage_note" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">預估完成日展延</div>
                                            </td>
                                            <td colspan="3">
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Defer" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" >
                                                        <Columns>
                                                            <asp:BoundField DataField="tcd_defer_date" HeaderText="展延後預估完成日">
                                                                <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_keyin_date" HeaderText="提出展延日">
                                                                <ItemStyle HorizontalAlign="Center" Width="120px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_desc" HeaderText="展延原因">
                                                                <ItemStyle Width="530px" HorizontalAlign="Left" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無展延! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Defer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                            </div>
                        </div>

                        <div class="twocol margin5TB">
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_infoHandel" runat="server">歷次承辦人資訊</asp:Literal>
                                    &nbsp;&nbsp;
                        <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_historyRecord" runat="server">歷次修改紀錄</asp:Literal><a href="#dialog06" class="inlineS"></a>
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print" Text="列印" OnClick="BT_Print_Click" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Tag" Text="列印檔案標籤" OnClick="BT_Print_Tag_Click"  Visible="false"/>
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Excel" Text="列印檔案標籤_Excel" OnClick="BT_Print_Excel_Click" Visible="false"/>
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit2" Text="編輯" OnClick="btEdit_Click" Visible="False" />
                                    <asp:Button runat="server" class="genbtnS" ID="BT_End" Text="結案" OnClick="BT_End_Click" Visible="false"></asp:Button>
                                    <asp:Button runat="server" class="genbtnS" ID="btnDelete" Text="刪除" OnClick="btnDelete_Click" Visible="False" />
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">分案主管</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件/分案日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>&nbsp;&nbsp;/&nbsp;&nbsp;<asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>|<asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">進度</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal><asp:DropDownList ID="DDL_Degree" runat="server" Visible="false"  DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_DDL_degree" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">預估完成日</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_expect_close_date" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">處理天數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_process_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">產出文件數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_contract_count" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">需求結件日</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_case_closedate" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改人</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>|<asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->

                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc1:Foot runat="server" ID="Foot" />

        <%--<asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_report" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_report_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            tinymce.init({
                selector: '#txt_betsum',
                width: "800",
                height: "500",
                menubar: false,
                statusbar: false,
                toolbar: false,
                menubar: false,
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
                readonly: 1
            });
            $(document).ready(function () {
                $(".help_betsum").attr("title", $("#txt_betsum").val());
                $(".help_betsum").attr("class", "itemhint");
                $(".help_SignReason").attr("title", $("#txtSignReason").val());
                $(".help_SignReason").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txtManageNote").val());
                $(".help_manage_note").attr("class", "itemhint");

                $('a.iterm_dymanic').cluetip({ width: '830px', showTitle: false, ajaxCache: false });
                $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_company').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });

                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                jQuery('#Form1').validationEngine({});

            });

        </script>

    </form>
</body>
</html>
