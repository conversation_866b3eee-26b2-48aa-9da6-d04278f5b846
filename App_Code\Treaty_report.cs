﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;
using Aspose.Words;
using Aspose.Cells;
using Aspose.Pdf;

/// <summary>
/// myAspose 的摘要描述
/// </summary>
namespace Treaty_report
{
    /// <summary>
    /// Aspose Word 類別 
    /// </summary>
    public class asposeWord
    {
        System.Web.HttpServerUtility Server = System.Web.HttpContext.Current.Server;

        string tempPath = "~/Template/";   //樣板檔路徑

        Aspose.Words.Document doc = null;
        Aspose.Words.DocumentBuilder builder = null;
        Aspose.Words.PageSetup pagesetup = null;
        Aspose.Words.DocumentBuilder Builder = null;

        //public asposeWord()
        //{

        //}

        public asposeWord(string templateFileName)
        {
            try
            {
                Aspose.Words.License license = new Aspose.Words.License();
                license.SetLicense(Server.MapPath("~/Aspose.Total.lic"));
                Aspose.Pdf.License license2 = new Aspose.Pdf.License();
                license2.SetLicense(Server.MapPath("~/Aspose.Total.lic"));

                this.doc = new Aspose.Words.Document(Server.MapPath(tempPath + templateFileName));
                this.builder = new Aspose.Words.DocumentBuilder(this.doc);
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("錯誤訊息：{0}", ex.Message));
            }
        }

        /// <summary>
        /// <para>取得Document物件</para>
        /// </summary>
        public Aspose.Words.Document getDocument()
        {
            return doc;
        }

        /// <summary>
        /// <para>取得DocumentBuilder物件</para>
        /// </summary>
        public Aspose.Words.DocumentBuilder getDocumentBuilder()
        {
            return builder;
        }

        /// <summary>
        /// <para>取得樣板檔路徑</para>
        /// </summary>
        public string getTemplatePath()
        {
            return tempPath;
        }

        /// <summary>
        /// <para>Insert html tag by merge field</para>
        /// </summary>
        public void insertHTML(string fieldName, string html)
        {
            builder.MoveToMergeField(fieldName);
            builder.InsertHtml("<span style='font-family:新細明體;'>" + html + "</span>");
        }

        #region 頁首及頁尾設定
        /// <summary>
        /// <para>頁首及頁尾設定</para>
        /// </summary>
        public void pageSetting(string title, string confidetial, string timestmp)
        {
            #region 設定版面
            doc.ViewOptions.ViewType = Aspose.Words.Settings.ViewType.PageLayout;

            //自動將所有table展開100%寬
            builder.RowFormat.AllowAutoFit = true;

            Aspose.Words.PageSetup pageSetup = builder.PageSetup;
            pageSetup.PaperSize = PaperSize.A4;
            pageSetup.Orientation = Aspose.Words.Orientation.Portrait;
            pageSetup.TopMargin = ConvertUtil.InchToPoint(1.0);
            pageSetup.BottomMargin = ConvertUtil.InchToPoint(1.0);
            pageSetup.LeftMargin = ConvertUtil.InchToPoint(0.5);
            pageSetup.RightMargin = ConvertUtil.InchToPoint(0.5);

            string imageFileName = Server.MapPath("~/images/logo.jpg");
            string imageSec1 = Server.MapPath("~/images/RESTRICTED.png");
            string imageSec2 = Server.MapPath("~/images/CONFIDENTIAL.png");
            string imageSec3 = Server.MapPath("~/images/STRICTLY CONFIDENTIAL.png");
            double tableWidth = pageSetup.PageWidth - pageSetup.LeftMargin - pageSetup.RightMargin;
            pageSetup.DifferentFirstPageHeaderFooter = false;

            pageSetup.HeaderDistance = 25;
            builder.MoveToHeaderFooter(Aspose.Words.HeaderFooterType.HeaderPrimary);
            builder.StartTable();
            builder.InsertCell();
            builder.CellFormat.Width = tableWidth / 3;
            builder.CellFormat.Borders.LineStyle = LineStyle.None;
            builder.CellFormat.Borders.LineWidth = 0;
            builder.ParagraphFormat.Alignment = ParagraphAlignment.Left;
            builder.InsertImage(imageFileName, Aspose.Words.Drawing.RelativeHorizontalPosition.Page, 30, Aspose.Words.Drawing.RelativeVerticalPosition.Page, 20, 120, 28, Aspose.Words.Drawing.WrapType.Inline);
            builder.CurrentParagraph.ParagraphFormat.Alignment = ParagraphAlignment.Left;

            builder.InsertCell();
            builder.CellFormat.Width = tableWidth / 3;
            builder.CellFormat.Borders.LineStyle = LineStyle.None;
            builder.CellFormat.Borders.LineWidth = 0;
            builder.ParagraphFormat.Alignment = ParagraphAlignment.Center;
            builder.Font.Name = "新細明體";
            builder.CurrentParagraph.ParagraphBreakFont.Name = "新細明體";
            builder.Font.Bold = true;
            builder.Font.Size = 14;
            builder.Write(title);
            builder.CurrentParagraph.ParagraphFormat.Alignment = ParagraphAlignment.Center;

            builder.InsertCell();
            builder.CellFormat.Width = tableWidth / 3;
            builder.CellFormat.Borders.LineStyle = LineStyle.None;
            builder.CellFormat.Borders.LineWidth = 0;
            builder.ParagraphFormat.Alignment = ParagraphAlignment.Right;
            builder.Font.Name = "新細明體";
            builder.CurrentParagraph.ParagraphBreakFont.Name = "新細明體";
            //builder.Font.Bold = true;
            builder.Font.Size = 10;
            //builder.Font.Color = System.Drawing.Color.Red;

            if (confidetial == "02") //機密
            {
                builder.InsertImage(imageSec2, Aspose.Words.Drawing.RelativeHorizontalPosition.Page, 30, Aspose.Words.Drawing.RelativeVerticalPosition.Page, 20, 80, 28, Aspose.Words.Drawing.WrapType.Inline);
            }
            else if (confidetial == "03") //極機密
            {
                builder.InsertImage(imageSec3, Aspose.Words.Drawing.RelativeHorizontalPosition.Page, 30, Aspose.Words.Drawing.RelativeVerticalPosition.Page, 20, 100, 28, Aspose.Words.Drawing.WrapType.Inline);
            }
            else if (confidetial == "01") //限閱
            {
                builder.InsertImage(imageSec1, Aspose.Words.Drawing.RelativeHorizontalPosition.Page, 30, Aspose.Words.Drawing.RelativeVerticalPosition.Page, 20, 64, 28, Aspose.Words.Drawing.WrapType.Inline);
            }

            builder.InsertBreak(BreakType.LineBreak);

            builder.Write("列印日期: " + DateTime.Now.ToString("yyyy/MM/dd"));

            builder.InsertBreak(BreakType.LineBreak);

            //builder.Write("版次: ");

            builder.InsertBreak(BreakType.LineBreak);

            builder.Font.ClearFormatting();
            builder.Font.Name = "新細明體";
            builder.CurrentParagraph.ParagraphBreakFont.Name = "新細明體";
            builder.Font.Bold = false;
            builder.Font.Size = 10;

            builder.CurrentParagraph.ParagraphFormat.Alignment = ParagraphAlignment.Right;
            builder.EndRow();
            builder.EndTable();

            builder.MoveToHeaderFooter(Aspose.Words.HeaderFooterType.FooterPrimary);
            builder.ParagraphFormat.Alignment = ParagraphAlignment.Center;

            builder.Font.Size = 10;
            builder.Font.Name = "新細明體";
            builder.CurrentParagraph.ParagraphBreakFont.Name = "新細明體";
            builder.Write(@"工業技術研究院機密資料 禁止複製、轉載、外流 ITRI CONFIDENTIAL DOCUMENT DO NOT COPY OR DISTRIBUTE");

            builder.Font.Size = 10;
            builder.Writeln("");
            builder.Font.Name = "新細明體";
            builder.Write("頁 ");
            builder.InsertField("PAGE", "");
            builder.Write("/");
            builder.InsertField("NUMPAGES", "");

            builder.Font.Size = 8;
            builder.Write("　　　");
            builder.Font.Name = "新細明體";
            builder.Write(timestmp);

            builder.MoveToDocumentEnd();

            //設定Row格式
            Aspose.Words.Tables.RowFormat rowFormat = builder.RowFormat;
            rowFormat.Borders.LineWidth = 1;
            #endregion
        }

        public void pageSetting(Aspose.Words.Document Doc)
        {
            Builder = new DocumentBuilder(Doc);
            Builder.RowFormat.AllowAutoFit = true;
            //設定版面
            pagesetup = Builder.PageSetup;
            pagesetup.PaperSize = PaperSize.A4;
            pagesetup.Orientation = Aspose.Words.Orientation.Portrait;
            pagesetup.TopMargin = ConvertUtil.InchToPoint(1.0);
            pagesetup.BottomMargin = ConvertUtil.InchToPoint(1.0);
            pagesetup.LeftMargin = ConvertUtil.InchToPoint(0.5);
            pagesetup.RightMargin = ConvertUtil.InchToPoint(0.5);
            pagesetup.DifferentFirstPageHeaderFooter = false;
            pagesetup.HeaderDistance = 25;

            Builder = null;
            pagesetup = null;
        }
        #endregion

        #region 匯出檔案類型
        /// <summary>
        /// <para>匯出Word檔</para>
        /// </summary>
        public MemoryStream exportWord()
        {
            MemoryStream ms = new MemoryStream();
            doc.Save(ms, Aspose.Words.SaveFormat.Docx);
            return ms;
        }

        /// <summary>
        /// <para>匯出PDF檔</para>
        /// </summary>
        public MemoryStream exportPDF()
        {
            MemoryStream ms = new MemoryStream();
            doc.Save(ms, Aspose.Words.SaveFormat.Pdf);
            return ms;
        }
        #endregion    
    }

    /// <summary>
    /// Aspose Excel 類別
    /// </summary>
    public class asposeExcel
    {
        System.Web.HttpServerUtility Server = System.Web.HttpContext.Current.Server;

        string tempPath = "~/Template/";   //樣板檔路徑

        Aspose.Cells.Workbook workbook = null;
        Aspose.Cells.Worksheet worksheet = null;

        public asposeExcel(string templateFileName)
        {
            try
            {
                Aspose.Cells.License license = new Aspose.Cells.License();
                license.SetLicense(Server.MapPath("~/Aspose.Total.lic"));

                this.workbook = new Aspose.Cells.Workbook(Server.MapPath(tempPath + templateFileName));
                this.worksheet = this.workbook.Worksheets[0];   //預設存取第一個sheet
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("錯誤訊息：{0}", ex.Message));
            }
        }

        public asposeExcel()
        {
            try
            {
                Aspose.Cells.License license = new Aspose.Cells.License();
                license.SetLicense(Server.MapPath("~/Aspose.Total.lic"));

                this.workbook = new Aspose.Cells.Workbook();
                this.worksheet = this.workbook.Worksheets[0];   //預設存取第一個sheet
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("錯誤訊息：{0}", ex.Message));
            }
        }

        /// <summary>
        /// <para>取得Workbook物件</para>
        /// </summary>
        public Aspose.Cells.Workbook getWorkbook()
        {
            return workbook;
        }

        /// <summary>
        /// <para>取得Worksheet物件</para>
        /// </summary>
        public Aspose.Cells.Worksheet getWorksheet()
        {
            return worksheet;
        }

        #region 匯出檔案類型
        /// <summary>
        /// <para>匯出Excel檔</para>
        /// </summary>
        public MemoryStream exportExcel()
        {
            MemoryStream ms = new MemoryStream();
            workbook.Save(ms, Aspose.Cells.SaveFormat.Xlsx);
            return ms;
        }

        /// <summary>
        /// <para>匯出PDF檔</para>
        /// </summary>
        public MemoryStream exportPDF()
        {
            MemoryStream ms = new MemoryStream();
            workbook.Save(ms, Aspose.Cells.SaveFormat.Pdf);
            return ms;
        }
        #endregion

        /// <summary>
        /// 加入/修改字串到儲存格
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="CellName"></param>
        /// <param name="val"></param>
        public void PutValue(Worksheet worksheet, string CellName, string val)
        {
            worksheet.Cells[CellName].PutValue(val);
        }

        /// <summary>
        /// 加入字串到儲存格
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="row"></param>
        /// <param name="column"></param>
        /// <param name="val"></param>
        public void PutValue(Worksheet worksheet, int row, int column, string val)
        {
            worksheet.Cells[row, column].PutValue(val);
        }
    }
}