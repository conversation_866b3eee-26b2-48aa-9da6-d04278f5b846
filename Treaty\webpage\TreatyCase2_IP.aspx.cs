﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

public partial class Treaty_webpage_TreatyCase2_IP : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            if (Request["seno"] != null)
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
            }
            //ViewState["seno"] = 47;
            if (ViewState["seno"] == null)
                Response.Redirect("../NoAuthRight.aspx");
            BindIP();
            Bindcountry();
            RBL_ip_type.SelectedValue = "1";
        }
    }
    private void BindIP()
    {
        //SDS_ip_type.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'18' ";
        //SDS_ip_type.DataBind();
        //RBL_ip_type.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", "");
            sqlCmd.Parameters.AddWithValue("@code_type", "18");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                RBL_ip_type.DataSource = dt;
                RBL_ip_type.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bindcountry()
    {
        //SDS_country.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'25' ";
        //SDS_country.DataBind();
        //DDL_country.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", "");
            sqlCmd.Parameters.AddWithValue("@code_type", "25");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_country.DataSource = dt;
                DDL_country.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (TB_IpNo.Text.ToUpper().IndexOf("SCRIPT") >= 0) Response.Redirect("../danger.aspx");
        if (TB_IpName.Text.ToUpper().IndexOf("SCRIPT") >= 0) Response.Redirect("../danger.aspx");
        if (RBL_ip_type.SelectedValue.Length > 2) Response.Redirect("../danger.aspx");
        if (DDL_country.SelectedValue.Length > 2) Response.Redirect("../danger.aspx");
        
        //SDS_SC.InsertParameters.Clear();
        //SDS_SC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_SC.InsertCommand = "esp_TreatyCase2_IP_modify";
        //SDS_SC.InsertParameters.Add("seno", ViewState["seno"].ToString());
        //SDS_SC.InsertParameters.Add("ip_type", RBL_ip_type.SelectedValue);
        //SDS_SC.InsertParameters.Add("ipno", TB_IpNo.Text);
        //SDS_SC.InsertParameters.Add("ipname", TB_IpName.Text);
        //SDS_SC.InsertParameters.Add("country", DDL_country.SelectedValue);
        //SDS_SC.InsertParameters.Add("patentno", TB_cerpntno.Text);
        //SDS_SC.InsertParameters.Add("mode", "Insert");
        //SDS_SC.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_IP_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ip_type", RBL_ip_type.SelectedValue);
            sqlCmd.Parameters.AddWithValue("@ipno", oRCM.SQLInjectionReplaceAll(TB_IpNo.Text));
            sqlCmd.Parameters.AddWithValue("@ipname", oRCM.SQLInjectionReplaceAll(TB_IpName.Text));
            sqlCmd.Parameters.AddWithValue("@country", DDL_country.SelectedValue);
            sqlCmd.Parameters.AddWithValue("@patentno", oRCM.SQLInjectionReplaceAll(TB_cerpntno.Text));
            sqlCmd.Parameters.AddWithValue("@mode", "Insert");


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        string script = "<script language='javascript'>close_win();</script>";
        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
    }
    protected void RBL_ip_type_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (RBL_ip_type.SelectedValue == "1")
        {
            BT_IP.Visible = true;
            TB_IpNo.ReadOnly = true;
            TB_IpName.ReadOnly = true;
            TB_IpName.Enabled = false;
            DDL_country.Enabled = false;
        }
        else
        {
            BT_IP.Visible = false;
            TB_IpNo.ReadOnly = false;
            TB_IpName.ReadOnly = false;
            TB_IpName.Enabled = true;
            DDL_country.Enabled = true;
        }
        TB_IpNo.Text = "";
        TB_IpName.Text = "";
        TB_cerpntno.Text = "";
        DDL_country.SelectedValue = "";
    }
}