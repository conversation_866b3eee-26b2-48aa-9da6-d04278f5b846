﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_Inspect.aspx.cs" Inherits="Treaty_webpage_TechCase_Inspect" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>案件審查</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />

    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">

        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            $("#txt_doc").val(compare);
        }

        function close_win() {
            alert("審查完成!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">

            <table style="margin-left: 15px; margin-top: 25px">
                <tr>
                    <td class="td_right">簽核狀態：</td>
                    <td>
                        <asp:Label ID="LB_inspectName" runat="server" Text="Label" Visible="false"></asp:Label>
                        <asp:RadioButtonList ID="RB_inspect_flag" runat="server" RepeatDirection="Horizontal" Width="150px" BorderWidth="0" Height="30px" AutoPostBack="True" OnSelectedIndexChanged="RB_inspect_flag_SelectedIndexChanged">
                            <asp:ListItem Value="1" Selected="True">同意</asp:ListItem>
                            <asp:ListItem Value="2">不同意</asp:ListItem>
                        </asp:RadioButtonList>
                        &emsp;如有建議修改之文檔，請勾「不同意」。
                    </td>
                </tr>

                <tr>
                    <td class="td_right" nowrap="nowrap"><span class="font-red">*</span>簽核意見：</td>
                    <td>
                        <asp:TextBox ID="TB_Inspect" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="RFV_Inspect" runat="server" ErrorMessage="必填" ControlToValidate="TB_Inspect" ForeColor="Red"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr id="tr_file_up" runat="server" visible="false">
                    <td class="td_right">上傳檔案：</td>
                    <td>
                        <div style="text-align: right; margin: 5px">
                            <asp:FileUpload ID="FU_up" runat="server" Width="546px" class="genbtnS" AllowMultiple="true" />

                            <asp:Button ID="BT_FileUp" runat="server" Text="上傳" class="genbtnS" OnClick="BT_FileUp_Click" CausesValidation="false" />
                        </div>
                        <asp:GridView ID="GV_File" runat="server" AutoGenerateColumns="False" Width="100%" OnRowCommand="GV_File_RowCommand">
                            <Columns>
                                <asp:TemplateField HeaderText="功能">
                                    <ItemTemplate>
                                        <asp:Label ID="lbl_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                        <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tcdf_no") %>' ForeColor="Red" CausesValidation="false">刪除</asp:LinkButton>
                                        <div style="display: none">
                                            <%--<asp:LinkButton ID="lnkbtn_Edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Eval("tcdf_no") %>'>維護</asp:LinkButton>--%>
                                        </div>
                                    </ItemTemplate>
                                    <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="附件名稱">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>' CausesValidation="false"> </asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="550px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </td>
                </tr>
                <tr>
                    <td class="td_right" colspan="2">
                        <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" /></td>
                </tr>
            </table>
            <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        </span>
    </form>
</body>
</html>
