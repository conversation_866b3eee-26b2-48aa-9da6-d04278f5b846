﻿using System.Collections.Generic;
using System.Data;
using System.IO;
using NPOI.HSSF.UserModel;

namespace Engage
{
	/// <summary>
	/// Summary description for NPOI_Excel
	/// </summary>
	public class NPOI_Excel
	{
		/// <summary>
		/// 從位元流讀取資料到DataTable.
		/// </summary>
		/// <param name="ExcelFileStream">The excel file stream.</param>
		/// <param name="SheetName">Sheet Name.</param>
		/// <param name="HeaderRowIndex">Index of the header row.</param>
		/// <returns></returns>
		public static DataTable RenderDataTableFromExcel(Stream ExcelFileStream, string SheetName, int HeaderRowIndex)
		{
			HSSFWorkbook workbook = new HSSFWorkbook(ExcelFileStream);
			HSSFSheet sheet = (HSSFSheet)workbook.GetSheet(SheetName);

			DataTable table = new DataTable();

			HSSFRow headerRow = (HSSFRow)sheet.GetRow(HeaderRowIndex);
			int cellCount = headerRow.LastCellNum;

			for (int i = headerRow.FirstCellNum; i < cellCount; i++)
			{
				DataColumn column = new DataColumn(headerRow.GetCell(i).StringCellValue);
				table.Columns.Add(column);
			}

			int rowCount = sheet.LastRowNum;
			for (int i = 1; i <= sheet.LastRowNum; i++)
			{
				HSSFRow row = (HSSFRow)sheet.GetRow(i);
				DataRow dataRow = table.NewRow();

				for (int j = row.FirstCellNum; j < cellCount; j++)
					dataRow[j] = row.GetCell(j).ToString();
			}

			ExcelFileStream.Close();
			workbook = null;
			sheet = null;
			return table;
		}

		/// <summary>
		/// 從位元流讀取資料到DataTable.
		/// </summary>
		/// <param name="ExcelFileStream">The excel file stream.</param>
		/// <param name="SheetName">Sheet Index.</param> 
		/// <param name="HeaderRowIndex">Index of the header row.</param>
		/// <param name="HaveHeader">if set to <c>true</c> [have header].</param>
		/// <returns></returns> 
		public static DataTable RenderDataTableFromExcel(Stream ExcelFileStream, int SheetIndex, int HeaderRowIndex)
		{
			HSSFWorkbook workbook = new HSSFWorkbook(ExcelFileStream);
			HSSFSheet sheet = (HSSFSheet)workbook.GetSheetAt(SheetIndex);

			DataTable table = new DataTable();

			HSSFRow headerRow = (HSSFRow)sheet.GetRow(HeaderRowIndex);
			int cellCount = headerRow.LastCellNum;

			for (int i = headerRow.FirstCellNum; i < cellCount; i++)
			{
				DataColumn column = new DataColumn(headerRow.GetCell(i).StringCellValue);
				table.Columns.Add(column);
			}

			int rowCount = sheet.LastRowNum;
			for (int i = 1; i <= sheet.LastRowNum; i++)
			{
				HSSFRow row = (HSSFRow)sheet.GetRow(i);
				DataRow dataRow = table.NewRow();

				for (int j = row.FirstCellNum; j < cellCount; j++)
				{
					if (row.GetCell(j) != null)
						dataRow[j] = row.GetCell(j).ToString();
				}

				table.Rows.Add(dataRow);
			}

			ExcelFileStream.Close();
			workbook = null;
			sheet = null;
			return table;
		}

		/// <summary>
		/// 從位元流讀取資料到Dictionary,key值為sheet名稱,value值為sheet的資料(datatable)
		/// </summary>
		/// <param name="ExcelFileStream"></param>
		/// <param name="HeaderRowIndex"></param>
		/// <returns></returns>
		public static Dictionary<string, DataTable> RenderDictionaryFromExcel(Stream ExcelFileStream, int HeaderRowIndex)
		{
			Dictionary<string, DataTable> dicRetrun = new Dictionary<string, DataTable>();

			HSSFWorkbook workbook = new HSSFWorkbook(ExcelFileStream);
			int SheetIndex = 0;//要讀取的excel sheet的值,一開始為0

			while (SheetIndex != -1)
			{
				HSSFSheet sheet = null;
				try
				{
					sheet = (HSSFSheet)workbook.GetSheetAt(SheetIndex);
					DataTable table = new DataTable();

					HSSFRow headerRow = (HSSFRow)sheet.GetRow(HeaderRowIndex);
					int cellCount = headerRow.LastCellNum;

					for (int i = headerRow.FirstCellNum; i < cellCount; i++)
					{
						DataColumn column = new DataColumn(headerRow.GetCell(i).StringCellValue);
						table.Columns.Add(column);
					}

					int rowCount = sheet.LastRowNum;
					for (int i = 1; i <= sheet.LastRowNum; i++)
					{
						HSSFRow row = (HSSFRow)sheet.GetRow(i);
						DataRow dataRow = table.NewRow();

						for (int j = row.FirstCellNum; j < cellCount; j++)
						{
							if (row.GetCell(j) != null)
								dataRow[j] = row.GetCell(j).ToString();
						}

						table.Rows.Add(dataRow);
					}

					dicRetrun.Add(workbook.GetSheetName(SheetIndex), table);


				}
				catch // (Exception ex)
				{
					SheetIndex = -1;
				}


				SheetIndex = SheetIndex == -1 ? SheetIndex : (++SheetIndex);
			}

			ExcelFileStream.Close();
			workbook = null;

			return dicRetrun;
		}

	}
}