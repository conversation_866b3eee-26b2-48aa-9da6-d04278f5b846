﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using Treaty_report;

public partial class Search_inner_YearReport : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        if (str == "") return true;
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();

            /*        SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
                      SDS_auth.SelectCommand = " select count(m_sno) from treaty_buztbl where  emp_no=@empno and year_report_flag='1' ";
                      SDS_auth.SelectParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
                      for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                      {
                          SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                      }
                      SDS_auth.DataBind();
                      System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                      if (dv_auth.Count ==0 )
                      {
                          Response.Redirect("../NoAuthRight.aspx");
                      }
            */
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = " select count(m_sno) from treaty_buztbl where  emp_no=@empno and year_report_flag='1' ";
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            oCmd.CommandType = CommandType.Text;
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "myTable");
            if (ds != null && ds.Tables[0].Rows.Count == 0)
            {
                Response.Redirect("../NoAuthRight.aspx");
            }
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;

            BindCaseStyle();
            BindContType();
            BindOrg();
            BindHandelList();
            Bindyear();
        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.lbHtml));
    }

    private void Bindyear()
    {

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select distinct tc_year from treaty_case  order by tc_year  desc";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_Year.DataSource = dt;
                DDL_Year.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }

    private void BindCaseStyle()
    {
        //SDS_CaseStyle.SelectCommand = "exec esp_treatyCase_codetable   '' ,'16' ";
        //SDS_CaseStyle.DataBind();
        //ddlCaseStyle.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_treatyCase_codetable   '' ,'16','case_class' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlCaseStyle.DataSource = dt;
                ddlCaseStyle.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        ddlCaseStyle.Items.Insert(0, new ListItem(" ", ""));

    }
    private void BindContType()
    {
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_treatyCase_codetable   '' ,'10' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        ddlContType.Items.Insert(0, new ListItem(" ", ""));
    }
    private void BindOrg()
    {
        //SDS_Orgcd.SelectCommand = "exec esp_Search_inner_statistic_orglist '" + SQLInjectionReplaceAll(ViewState["empno"].ToString() )+ "' ";
        //SDS_Orgcd.DataBind();
        //CBL_org.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_Search_inner_statistic_orglist ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                CBL_org.DataSource = dt;
                CBL_org.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindHandelList()
    {
        //SDS_handelList.SelectCommand = "exec esp_Search_inner_statistic_handle_list '" + SQLInjectionReplaceAll(ViewState["empno"].ToString() )+ "' ";
        //SDS_handelList.DataBind();
        //CBL_handelList.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_Search_inner_statistic_handle_list ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                CBL_handelList.DataSource = dt;
                CBL_handelList.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    //private void Binddata(string str_sortField, string str_sort)
    //{
    //    SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
    //    ssoUser.GetEmpInfo();

    //    SDS_search.SelectParameters.Clear();
    //    SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;


    //    SDS_search.DataBind();

    //}

    private string GetSelectOrgcd()
    {
        string strResult = string.Empty;
        foreach (ListItem li in CBL_org.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("'{0}',", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetSelectStatus()
    {
        string strResult = string.Empty;
        foreach (ListItem it in CBL_Status.Items)
        {
            if (it.Selected)
                strResult += string.Format("'{0}',", it.Value);
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetSelectHandle()
    {
        string strResult = string.Empty;
        foreach (ListItem li in CBL_handelList.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("{0},", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetCaseClass()
    {
        string strResult = string.Empty;
        foreach (ListItem li in cbxCaseClass.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("{0},", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }

    private DataSet GetData()
    {
        if (!IsNumber((GetSelectOrgcd().Replace(",", "").Replace("'", "")))) Response.Redirect("../danger.aspx");
        if (!IsNatural_Number(GetSelectHandle().Replace(",", "").Replace("'", ""))) Response.Redirect("../danger.aspx");
        if (!IsNumber(DDL_Year.SelectedValue)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlContType.SelectedValue) || (ddlContType.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlCaseStyle.SelectedValue) || (ddlCaseStyle.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (!IsNumber(ddl_amend.SelectedValue)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxPromoterName.Text) || (ddlCaseStyle.SelectedValue.Length > 10)) Response.Redirect("../danger.aspx");
        if (!IsNumber((ddlImportant.SelectedValue.Replace("-", "")))) Response.Redirect("../danger.aspx");
        if (!IsNumber((GetSelectStatus().Replace(",", "").Replace("'", "")))) Response.Redirect("../danger.aspx");
        if (!IsNatural_Number(GetCaseClass().Replace(",", ""))) Response.Redirect("../danger.aspx");
        if (!IsNumber(ddlShowCase.SelectedValue)) Response.Redirect("../danger.aspx");
        if ((txtReceiveSDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtReceiveEDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveEDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseSDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseEDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtKeyWord.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (txtCompname.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtCompname.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtCloseEDate.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtCloseSDate.Text)) Response.Redirect("../danger.aspx");

        /*
                SDS_search.SelectParameters.Clear();
                SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                SDS_search.SelectCommand = "esp_Search_inner_YearReport";
                SDS_search.SelectParameters.Add("empno", ViewState["empno"].ToString());
                SDS_search.SelectParameters.Add("QYear", DDL_Year.SelectedValue); //將舊案的流水號存入
                SDS_search.SelectParameters.Add("QSendSDate", txtReceiveSDate.Text.Trim());//洽案(契約)名稱
                SDS_search.SelectParameters.Add("QSendEDate", txtReceiveEDate.Text.Trim());
                SDS_search.SelectParameters.Add("QStatus", GetSelectStatus());
                SDS_search.SelectParameters.Add("QOrgcd", GetSelectOrgcd());
                SDS_search.SelectParameters.Add("QHandle", GetSelectHandle());
                SDS_search.SelectParameters.Add("CompName", txtCompname.Text);
                SDS_search.SelectParameters.Add("Promoter", tbxPromoterName.Text);
                SDS_search.SelectParameters.Add("MostImportant", ddlImportant.SelectedValue);
                SDS_search.SelectParameters.Add("Class", GetCaseClass());
                SDS_search.SelectParameters.Add("case_flag", IIf(CB_常用版本.Checked, "1", ""));
                SDS_search.SelectParameters.Add("TopOne", ddlShowCase.SelectedValue);
                SDS_search.SelectParameters.Add("KeyWords", txtKeyWord.Text);
                SDS_search.SelectParameters.Add("is_amend", ddl_amend.SelectedValue);
                SDS_search.SelectParameters.Add("QCaseStyle", ddlCaseStyle.SelectedValue);
                SDS_search.SelectParameters.Add("QConttype", ddlContType.SelectedValue);
                SDS_search.SelectParameters.Add("QCloseSDate", txtCloseSDate.Text.Trim());
                SDS_search.SelectParameters.Add("QCloseEDate", txtCloseEDate.Text.Trim());
                for (int i = 0; i < this.SDS_search.SelectParameters.Count; i++)
                {
                    SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
                }
                SDS_search.DataBind();
                System.Data.DataView dv = (DataView)SDS_search.Select(new DataSourceSelectArguments());
                if (dv  != null)
                {
                    StringBuilder sbHtml = new StringBuilder();
                    sbHtml.Append("<table  class='TbStatistic' >");
                    int tr_count = 0;
                    //sbHtml.Append("<tr align='center'><td>姓名</td><td>01</td><td>02</td><td>03</td><td>04</td><td>05</td><td>06</td><td>07</td><td>08</td><td>09</td><td>10</td><td>11</td><td>12</td><td>合計天數</td><td>合計件數</td><td>平均天數</td></tr>");
                    foreach (DataRowView rowView in dv)
                    {
                        DataRow row = rowView.Row;
                        if (tr_count!=0)
                            sbHtml.Append("<tr  align='right'>");
                        else
                            sbHtml.Append("<tr  align='center'>");

                        for (int i = 0; i < dv.Table.Columns.Count; i++)
                        {
                            sbHtml.Append(string.Format("<td>{0}</td>", row[i]));
                        }
                        sbHtml.Append("</tr>");
                        tr_count++;
                    }
                    sbHtml.Append("</table>");
                    lbHtml.Text = sbHtml.ToString();
                    LB_Excel.Visible = true;
                }
                else
                {
                     LB_Excel.Visible=false ;
                }
        */
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_YearReport";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd.Parameters.AddWithValue("QYear", oRCM.SQLInjectionReplaceAll(DDL_Year.SelectedValue)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("QSendSDate", oRCM.SQLInjectionReplaceAll(txtReceiveSDate.Text.Trim()));//洽案(契約)名稱
        oCmd.Parameters.AddWithValue("QSendEDate", oRCM.SQLInjectionReplaceAll(txtReceiveEDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("QStatus", oRCM.SQLInjectionReplaceAll(GetSelectStatus()));
        oCmd.Parameters.AddWithValue("QOrgcd", oRCM.SQLInjectionReplaceAll(GetSelectOrgcd()));
        oCmd.Parameters.AddWithValue("QHandle", oRCM.SQLInjectionReplaceAll(GetSelectHandle()));
        oCmd.Parameters.AddWithValue("CompName", oRCM.SQLInjectionReplaceAll(txtCompname.Text));
        oCmd.Parameters.AddWithValue("Promoter", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
        oCmd.Parameters.AddWithValue("MostImportant", oRCM.SQLInjectionReplaceAll(ddlImportant.SelectedValue));
        oCmd.Parameters.AddWithValue("Class", oRCM.SQLInjectionReplaceAll(GetCaseClass()));
        oCmd.Parameters.AddWithValue("case_flag", oRCM.SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
        oCmd.Parameters.AddWithValue("TopOne", oRCM.SQLInjectionReplaceAll(ddlShowCase.SelectedValue));
        oCmd.Parameters.AddWithValue("KeyWords", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
        oCmd.Parameters.AddWithValue("is_amend", oRCM.SQLInjectionReplaceAll(ddl_amend.SelectedValue));
        oCmd.Parameters.AddWithValue("QCaseStyle", oRCM.SQLInjectionReplaceAll(ddlCaseStyle.SelectedValue));
        oCmd.Parameters.AddWithValue("QConttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));
        oCmd.Parameters.AddWithValue("QCloseSDate", oRCM.SQLInjectionReplaceAll(txtCloseSDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("QCloseEDate", oRCM.SQLInjectionReplaceAll(txtCloseEDate.Text.Trim()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");

        return ds;
    }

    private void DoSearch()
    {
        DataSet ds = GetData();
        DataView dv = ds.Tables[0].DefaultView;
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
        {
            StringBuilder sbHtml = new StringBuilder();
            sbHtml.Append("<table  class='TbStatistic' >");
            int tr_count = 0;
            //sbHtml.Append("<tr align='center'><td>姓名</td><td>01</td><td>02</td><td>03</td><td>04</td><td>05</td><td>06</td><td>07</td><td>08</td><td>09</td><td>10</td><td>11</td><td>12</td><td>合計天數</td><td>合計件數</td><td>平均天數</td></tr>");
            foreach (DataRowView rowView in dv)
            {
                DataRow row = rowView.Row;
                if (tr_count != 0)
                    sbHtml.Append("<tr  align='right'>");
                else
                    sbHtml.Append("<tr  align='center'>");

                for (int i = 0; i < dv.Table.Columns.Count; i++)
                {
                    sbHtml.Append(string.Format("<td>{0}</td>", Server.HtmlEncode(row[i].ToString())));
                }
                sbHtml.Append("</tr>");
                tr_count++;
            }
            sbHtml.Append("</table>");
            lbHtml.Text = sbHtml.ToString();
            LB_Excel.Visible = true;
        }
        else
        {
            LB_Excel.Visible = false;
        }
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {
        DoSearch();
    }
    //protected void SDS_search_Selected(object sender, SqlDataSourceStatusEventArgs e)
    //{
    //    ViewState["RowCount"] = e.AffectedRows;
    //}
    protected void LB_Excel_Click(object sender, EventArgs e)
    {
        asposeExcel excel = new asposeExcel();
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();

        DataTable dataSource = GetData().Tables[0];

        sheet.Cells.ImportDataTable(dataSource, false, "A1");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        ExportExcel("年報.xlsx", excel.exportExcel());

        //Response.Clear();
        //Response.Charset = "big5";
        //Response.AddHeader(
        //    "Content-Disposition",
        //    "attachment;filename=" + Server.UrlEncode("年報.xls")
        //    );
        //Response.ContentType = "application/vnd.ms-excel";
        //Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");
        //Response.Write("<html><body>");
        //Response.Write("<meta http-equiv=Content-Type content=text/html; charset=big5>"); //避免亂碼
        //Response.Write("<style type=text/css>");
        //Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
        //Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
        //Response.Write("</style>");
        //Response.Write(lbHtml.Text);
        //Response.Write("</body></html>");
        //Response.End();

    }
    //匯出EXCEL
    private void ExportExcel(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);

        Response.Clear();
        Response.ContentType = "Application/vnd.ms-excel";
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }

}