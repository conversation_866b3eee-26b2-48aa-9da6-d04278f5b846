﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_ECP.aspx.cs" Inherits="Treaty_webpage_TechCase_ECP" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <%--<script type="text/javascript" src="../Scripts/autoheight.js"></script>--%>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            // $("#txt_doc").val(compare);
        }

        var obj_employee;
        /* 單人挑選 */
        function Find_Empno_single(obj) {
            obj_employee = obj;
            $.colorbox({
                href: "../../Comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx"
                , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                //, title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../Comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });
            return false;
        }

        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    //alert("查無此人 或 空值!");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($(obj_employee).closest('td').find('.c_com_cname').val())
                        , iframe: true, width: "780px", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                        //, title: '單人挑選'
                        , onClosed: function () {
                            $(obj_employee).closest('td').find('.c_com_cname').val('');
                            $(obj_employee).closest('td').find('.c_com_empno').val('');
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback1);
                        }
                    });
                    break;
                default:
                    $(obj_employee).closest('td').find('.c_com_cname').val(data.c_com_cname);
                    $(obj_employee).closest('td').find('.c_com_empno').val(data.c_com_empno);
                //__doPostBack('renew', '');
            }
        }

        function advancd_search()//進階查詢
        {

            if (advancesearch.className.indexOf("ADV_off") > 0) {
                advancesearch.className = advancesearch.className.replace(/ADV_off/g, "ADV_on");
            }
            else {
                advancesearch.className = advancesearch.className.replace(/ADV_on/g, "ADV_off");

            }
        }

        function tech_fileup(seno) {
            $(".ajax_attfileinfo").colorbox({
                href: "./TechCase_FileUp.aspx?tt_seno=" + seno
                , title: '檔案上傳'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }

        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function CheckBoxClicked(chk) {
            var checkboxes = document.querySelectorAll('input[type="checkbox"][name$="cbfile"]');
            checkboxes.forEach(function (cb) {
                if (cb !== chk) {
                    cb.checked = false;
                }
            });
        }


    </script>
    <style type="text/css">
        .mask {
            z-index: 9999;
            position: fixed;
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-content: center;
            flex-wrap: wrap;
            /*background-color: #000;
            opacity: 0.5;*/
        }

        .ADV_on {
            display: inline;
        }

        .ADV_off {
            display: none;
        }
    </style>

    <script type="text/javascript">
        $(document).ready(function () {

        });
    </script>
</head>
<body>
    <form id="form1" runat="server">

        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="UpdatePanel1" DisplayAfter="50">
                    <ProgressTemplate>
                        <div class="mask">
                            <font color='red' style="margin: 40%"><b>
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/roller.gif" />Processing........</b></font>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
                <div class="stripeMe" style="margin-left: 15px; margin-top: 25px">
                    <div style="text-align: right; color: red;"><b>「內部機密不得外流」</b></div>
                    <div class="font-title font-bold font-size3">預覽流程及送簽 </div>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="100" align="right">
                                <div class="font-title titlebackicon">理由說明</div>
                            </td>
                            <td>
                                <asp:DropDownList ID="DDL_reason" runat="server" AutoPostBack="true" OnSelectedIndexChanged="DDL_reason_SelectedIndexChanged">
                                </asp:DropDownList>
                                <asp:TextBox ID="txt_reason" runat="server" class="inputex width100" MaxLength="500" Rows="5" TextMode="MultiLine"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <div class="font-title titlebackicon">簽核人員</div>
                            </td>
                            <td>
                                <div class="twocol margin5TB">
                                    <div class="right">
                                        <asp:Button ID="btnAddFirst" runat="server" OnClick="btnAddFirst_Click" Text="加簽第一關" CssClass="genbtnS" />
                                    </div>
                                </div>
                                <span class="stripeMe">
                                    <asp:GridView ID="gvList_signflow" runat="server" AutoGenerateColumns="False" Width="90%" CellPadding="0" CellSpacing="0" ShowHeaderWhenEmpty="True" OnRowDataBound="gvList_signflow_RowDataBound" OnRowCommand="gvList_signflow_RowCommand">
                                        <HeaderStyle HorizontalAlign="Center" Wrap="false" />
                                        <AlternatingRowStyle CssClass="alt" />
                                        <Columns>
                                            <asp:TemplateField HeaderText="功能" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="10%">
                                                <ItemTemplate>
                                                    <asp:HiddenField ID="gv_hf_rowsn" runat="server" Value='<%# Eval("rowsn") %>' />
                                                    <asp:LinkButton ID="lbtnNew" runat="server" CommandArgument='<%# Eval("rowsn") %>' CommandName="CMD_AddRow" Seq='<%# Eval("Seq") %>'>新增</asp:LinkButton>
                                                    <asp:LinkButton ID="lbtnDel" runat="server" CommandArgument='<%# Eval("rowsn") + ";" + Eval("ActRoleName") %>' CommandName="CMD_DelRow" Seq='<%# Eval("Seq") %>'>刪除</asp:LinkButton>
                                                    <%--<asp:LinkButton ID="lbtnNotice" runat="server" Enabled="false" ForeColor="Gray">副知</asp:LinkButton>--%>
                                                </ItemTemplate>
                                                <HeaderStyle Width="10%"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="順序">
                                                <HeaderStyle Width="10%"></HeaderStyle>
                                                <ItemTemplate>
                                                    <asp:Literal ID="lit_Seq" runat="server" Text='<%# Server.HtmlEncode(Eval("Seq").ToString()) %>'></asp:Literal>
                                                </ItemTemplate>
                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="簽辦角色" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="15%">
                                                <ItemTemplate>
                                                    <asp:Literal ID="lit_SignType" runat="server" Text='<%# Server.HtmlEncode(Eval("SignType").ToString()) %>' Visible="false"></asp:Literal>
                                                    <asp:Literal ID="lit_SignSigle" runat="server" Text='<%# Server.HtmlEncode(Eval("SignSigle").ToString()) %>' Visible="false"></asp:Literal>
                                                    <asp:Literal ID="lit_IS_LOCK" runat="server" Text='<%# Server.HtmlEncode(Eval("IS_LOCK").ToString()) %>' Visible="false"></asp:Literal>
                                                    <asp:Literal ID="lit_ActRoleName" runat="server" Text='<%# Server.HtmlEncode(Eval("ActRoleName").ToString()) %>' Visible="false"></asp:Literal>
                                                    <asp:DropDownList ID="ddl_ecprole" runat="server"></asp:DropDownList>
                                                </ItemTemplate>
                                                <HeaderStyle Width="15%"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="簽核類型" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="15%">
                                                <ItemTemplate>
                                                    <%# GetSignClassName(Server.HtmlEncode(Eval("SignClass").ToString())) %>
                                                    <asp:HiddenField ID="hf_SignClass" runat="server" Value='<%# Server.HtmlEncode(Eval("SignClass").ToString()) %>' />
                                                </ItemTemplate>
                                                <HeaderStyle Width="15%"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="簽核人員" ItemStyle-HorizontalAlign="Center" HeaderStyle-Width="30%">
                                                <ItemTemplate>
                                                    <asp:Panel ID="pnl_Empno" runat="server">
                                                        <asp:TextBox ID="txt_com_cname" runat="server" CssClass="inputsizeSS c_com_cname textboxReadonly" Text='<%# Server.HtmlEncode(Eval("RecUserName").ToString().Trim()) %>'></asp:TextBox>
                                                        <asp:LinkButton ID="btn_find1" runat="server" Style="vertical-align: middle" Seq='<%# Eval("Seq") %>'
                                                            OnClientClick="return Find_Empno_single(this);"><img src="../../images/icon-lookdetail.png" /></asp:LinkButton>
                                                        <asp:TextBox ID="txt_com_empno" runat="server" class="inputsizeSS c_com_empno textboxReadonly" Text='<%# Server.HtmlEncode(Eval("RecUserID").ToString().Trim()) %>'></asp:TextBox>
                                                    </asp:Panel>
                                                    <asp:Label ID="lbl_Empno1" runat="server" Visible="false" CssClass="c_com_cname"></asp:Label>
                                                </ItemTemplate>

                                                <HeaderStyle Width="30%"></HeaderStyle>

                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <div class="font-title titlebackicon">參考檔案</div>
                            </td>
                            <td>
                                <div class="twocol margin5TB">
                                    <div class="right">
                                        <asp:Button ID="btn_FileUp" runat="server" Text="新增檔案" class="genbtnS ajax_attfileinfo" />

                                    </div>
                                </div>

                                <span class="stripeMe">
                                    <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" Width="90%" OnRowCommand="gv_data_RowCommand" OnRowDataBound="gv_data_RowDataBound">
                                        <AlternatingRowStyle CssClass="alt" />
                                        <Columns>
                                            <asp:TemplateField HeaderText="功能">
                                                <ItemTemplate>
                                                    <asp:CheckBox ID="cbAdd" runat="server" OnCheckedChanged="cbAdd_CheckedChanged" AutoPostBack="true" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' />
                                                    <asp:HiddenField ID="HiddenField1" runat="server" Value='<%# Eval("tcdf_no") %>' />
                                                </ItemTemplate>
                                                <HeaderStyle Width="30px" Wrap="False" />
                                                <ItemStyle HorizontalAlign="Center" />
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="文件名稱">
                                                <ItemTemplate>
                                                    <asp:LinkButton ID="LB_filename" runat="server" Text='<%# Server.HtmlEncode(Eval("檔名").ToString()) %>' CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' CommandName="xDownload"></asp:LinkButton>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="檔案類型">
                                                <ItemTemplate>
                                                    <asp:Label ID="lab_type" runat="server" Text='<%# Server.HtmlEncode(Eval("檔案類型").ToString()) %>'></asp:Label>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="PDF預覽檢視">
                                                <ItemTemplate>
                                                    <asp:CheckBox ID="cbfile" runat="server" OnClick="CheckBoxClicked(this)" AutoPostBack="true" OnCheckedChanged="cbAdd_CheckedChanged" />
                                                </ItemTemplate>
                                                <ItemStyle HorizontalAlign="Center" />
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="排序">
                                                <ItemTemplate>
                                                    <asp:DropDownList ID="ddl_order" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_order_SelectedIndexChanged">
                                                    </asp:DropDownList>
                                                </ItemTemplate>
                                                <ItemStyle HorizontalAlign="Center" />
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>
                                </span>
                            </td>
                        </tr>

                    </table>

                    <div class="left" style="text-align: left">
                        <asp:Literal ID="LT_業管人員" runat="server"></asp:Literal><br />
                         
                        <div id="advancesearch" class="gentablenoline font-normal margin5TB ADV_off" title="進階查詢">
                            <asp:TextBox ID="TB_pass_check" runat="server" AUTOCOMPLETE="OFF" TextMode="Password"></asp:TextBox>
                        </div>
                    </div>
                    <div class="right" style="text-align: right">
                        <a href="#" id="advancesearchopen" onclick="advancd_search()" style="text-decoration: none; font-size: 10px; margin-left: -5px; color: white">.</a>
                        <asp:HiddenField ID="h_ECP_success" runat="server" />
                        <asp:Button ID="btnReGen" runat="server" CssClass="genbtnS" Text="重新產生簽核流程" OnClientClick="return confirm('是否重新產生簽核流程?');return false;" OnClick="btnReGen_Click" />
                        <asp:Button ID="btnLeave" runat="server" CssClass="genbtnS" Text="取消" OnClientClick="parent.$.colorbox.close();" />
                        <asp:Button ID="btnSign" runat="server" CssClass="genbtnS" Text="送簽" OnClick="btnSign_Click" OnClientClick="return confirm('請確認 簽核流程 是否正確?');return false;" />&nbsp;&nbsp;&nbsp;&nbsp;
                    </div>

                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
</body>
</html>
