/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[8],{399:function(ia,y,e){e.r(y);var fa=e(1),x=e(426),ha=e(427),ea;(function(e){e[e.EXTERNAL_XFDF_NOT_REQUESTED=0]="EXTERNAL_XFDF_NOT_REQUESTED";e[e.EXTERNAL_XFDF_NOT_AVAILABLE=1]="EXTERNAL_XFDF_NOT_AVAILABLE";e[e.EXTERNAL_XFDF_AVAILABLE=2]="EXTERNAL_XFDF_AVAILABLE"})(ea||(ea={}));ia=function(){function e(e){this.aa=e;this.state=ea.EXTERNAL_XFDF_NOT_REQUESTED}e.prototype.w6=function(){var e=this;return function(w,x,r){return Object(fa.b)(e,
void 0,void 0,function(){var e,f,n,y,z,ba,da,ha=this,ia;return Object(fa.d)(this,function(h){switch(h.label){case 0:if(this.state!==ea.EXTERNAL_XFDF_NOT_REQUESTED)return[3,2];e=this.aa.getDocument().uq();return[4,this.S4(e)];case 1:f=h.ea(),n=this.A0(f),this.zE=null!==(ia=null===n||void 0===n?void 0:n.parse())&&void 0!==ia?ia:null,this.state=null===this.zE?ea.EXTERNAL_XFDF_NOT_AVAILABLE:ea.EXTERNAL_XFDF_AVAILABLE,h.label=2;case 2:if(this.state===ea.EXTERNAL_XFDF_NOT_AVAILABLE)return r(w),[2];y=new DOMParser;
z=y.parseFromString(w,"text/xml");x.forEach(function(e){ha.merge(z,ha.zE,e-1)});ba=new XMLSerializer;da=ba.serializeToString(z);r(da);return[2]}})})}};e.prototype.SH=function(e){this.S4=e};e.prototype.fe=function(){this.zE=void 0;this.state=ea.EXTERNAL_XFDF_NOT_REQUESTED};e.prototype.A0=function(e){return e?Array.isArray(e)?new x.a(e):"string"!==typeof e?null:(new DOMParser).parseFromString(e,"text/xml").querySelector("xfdf > add")?new x.a(e):new ha.a(e):null};e.prototype.merge=function(e,w,x){var r=
this;0===x&&(this.O8(e,w.Bn),this.Q8(e,w.lE));var h=w.ca[x];h&&(this.S8(e,h.Il),this.U8(e,h.UU,w.Vt),this.T8(e,h.page,x),this.P8(e,h.VM));h=this.aa.Ob();if(x===h-1){var f=w.Vt;Object.keys(f).forEach(function(h){f[h].GF||r.tQ(e,h,f[h])})}};e.prototype.O8=function(e,w){null!==w&&(e=this.jt(e),this.Oo(e,"calculation-order",w))};e.prototype.Q8=function(e,w){null!==w&&(e=this.jt(e),this.Oo(e,"document-actions",w))};e.prototype.S8=function(e,w){var x=this,r=this.it(e.querySelector("xfdf"),"annots");Object.keys(w).forEach(function(e){x.Oo(r,
'[name="'+e+'"]',w[e])})};e.prototype.U8=function(e,w,x){var r=this;if(0!==w.length){var h=this.jt(e);w.forEach(function(f){var n=f.getAttribute("field"),w=x[n];w&&(r.tQ(e,n,w),r.Oo(h,"null",f))})}};e.prototype.tQ=function(e,w,x){var r=this.jt(e);null!==x.Vy&&this.Oo(r,'ffield [name="'+w+'"]',x.Vy);e=this.it(e.querySelector("xfdf"),"fields");w=w.split(".");this.iH(e,w,0,x.value);x.GF=!0};e.prototype.T8=function(e,w,x){null!==w&&(e=this.jt(e),e=this.it(e,"pages"),this.Oo(e,'[number="'+(x+1)+'"]',w))};
e.prototype.P8=function(e,w){Object.keys(w).forEach(function(w){(w=e.querySelector('annots [name="'+w+'"]'))&&w.parentElement.removeChild(w)})};e.prototype.iH=function(e,w,x,r){if(x===w.length)w=document.createElementNS("","value"),w.textContent=r,this.Oo(e,"value",w);else{var h=w[x];this.it(e,'[name="'+h+'"]',"field").setAttribute("name",h);e=e.querySelectorAll('[name="'+h+'"]');1===e.length?this.iH(e[0],w,x+1,r):(h=this.G3(e),this.iH(x===w.length-1?h:this.Oda(e,h),w,x+1,r))}};e.prototype.G3=function(e){for(var w=
null,x=0;x<e.length;x++){var r=e[x];if(0===r.childElementCount||1===r.childElementCount&&"value"===r.children[0].tagName){w=r;break}}return w};e.prototype.Oda=function(e,w){for(var x=0;x<e.length;x++)if(e[x]!==w)return e[x];return null};e.prototype.Oo=function(e,w,x){w=e.querySelector(w);null!==w&&e.removeChild(w);e.appendChild(x)};e.prototype.jt=function(e){var w=e.querySelector("pdf-info");if(null!==w)return w;w=this.it(e.querySelector("xfdf"),"pdf-info");w.setAttribute("xmlns","http://www.pdftron.com/pdfinfo");
w.setAttribute("version","2");w.setAttribute("import-version","4");return w};e.prototype.it=function(e,w,x){var r=e.querySelector(w);if(null!==r)return r;r=document.createElementNS("",x||w);e.appendChild(r);return r};return e}();y["default"]=ia},412:function(ia,y){ia=function(){function e(){}e.prototype.Lx=function(e){var x={Bn:null,lE:null,Vt:{},ca:{}};e=(new DOMParser).parseFromString(e,"text/xml");x.Bn=e.querySelector("pdf-info calculation-order");x.lE=e.querySelector("pdf-info document-actions");
x.Vt=this.M9(e);x.ca=this.Y9(e);return x};e.prototype.M9=function(e){var x=e.querySelector("fields");e=e.querySelectorAll("pdf-info > ffield");if(null===x&&null===e)return{};var y={};this.qZ(y,x);this.nZ(y,e);return y};e.prototype.qZ=function(e,x){if(null!==x&&x.children){for(var y=[],ea=0;ea<x.children.length;ea++){var da=x.children[ea];y.push({name:da.getAttribute("name"),element:da})}for(;0!==y.length;)for(x=y.shift(),ea=0;ea<x.element.children.length;ea++)da=x.element.children[ea],"value"===da.tagName?
e[x.name]={value:da.textContent,Vy:null,GF:!1}:da.children&&y.push({name:x.name+"."+da.getAttribute("name"),element:da})}};e.prototype.nZ=function(e,x){x.forEach(function(x){var y=x.getAttribute("name");e[y]?e[y].Vy=x:e[y]={value:null,Vy:x,GF:!1}})};e.prototype.Y9=function(e){var x=this,y={};e.querySelectorAll("pdf-info widget").forEach(function(e){var da=parseInt(e.getAttribute("page"),10)-1;x.Pz(y,da);y[da].UU.push(e)});e.querySelectorAll("pdf-info page").forEach(function(e){var da=parseInt(e.getAttribute("number"),
10)-1;x.Pz(y,da);y[da].page=e});this.BO(e).forEach(function(e){var da=parseInt(e.getAttribute("page"),10),ba=e.getAttribute("name");x.Pz(y,da);y[da].Il[ba]=e});this.nO(e).forEach(function(e){var da=parseInt(e.getAttribute("page"),10);e=e.textContent;x.Pz(y,da);y[da].VM[e]=!0});return y};e.prototype.Pz=function(e,x){e[x]||(e[x]={Il:{},VM:{},UU:[],page:null})};return e}();y.a=ia},426:function(ia,y,e){var fa=e(1),x=e(0);e.n(x);ia=function(e){function y(x){var y=e.call(this)||this;y.s3=Array.isArray(x)?
x:[x];return y}Object(fa.c)(y,e);y.prototype.parse=function(){var e=this,y={Bn:null,lE:null,Vt:{},ca:{}};this.s3.forEach(function(w){y=Object(x.merge)(y,e.Lx(w))});return y};y.prototype.BO=function(e){var x=[];e.querySelectorAll("add > *").forEach(function(e){x.push(e)});e.querySelectorAll("modify > *").forEach(function(e){x.push(e)});return x};y.prototype.nO=function(e){return e.querySelectorAll("delete > *")};return y}(e(412).a);y.a=ia},427:function(ia,y,e){var fa=e(1);ia=function(e){function x(x){var y=
e.call(this)||this;y.t3=x;return y}Object(fa.c)(x,e);x.prototype.parse=function(){return this.Lx(this.t3)};x.prototype.BO=function(e){return e.querySelectorAll("annots > *")};x.prototype.nO=function(){return[]};return x}(e(412).a);y.a=ia}}]);}).call(this || window)
