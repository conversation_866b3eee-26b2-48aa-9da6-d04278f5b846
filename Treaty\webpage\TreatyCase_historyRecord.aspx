﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_historyRecord.aspx.cs" Inherits="Treaty_webpage_TreatyCase_historyRecord" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />

    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            /*Code to copy the gridview header with style*/
            var gridHeader = $('#<%=SGV_log.ClientID%>').clone(true);
            /*Code to remove first ror which is header row*/
            $(gridHeader).find("tr:gt(0)").remove();
            $('#<%=SGV_log.ClientID%> tr th').each(function (i) {
                /* Here Set Width of each th from gridview to new table th */
                $("th:nth-child(" + (i + 1) + ")", gridHeader).css('width', ($(this).width()).toString() + "px");
            });
            $("#controlHead").append(gridHeader);
            $('#controlHead').css('position', 'absolute');
            $('#controlHead').css('top', $('#<%=SGV_log.ClientID%>').offset().top);
        });
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None"
                OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorted="SGV_log_Sorted" OnSorting="SGV_log_Sorting">
                <HeaderStyle CssClass="fixedheadertable" />
                <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                <Columns>
                    <asp:BoundField DataField="tcmr_IP" HeaderText="IP" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" Width="80px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcmr_modify_emp_name" HeaderText="人員" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcmr_modify_date" HeaderText="日期" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" Width="180px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcmr_type" HeaderText="原因" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" Width="100px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcmr_note" HeaderText="Note" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" Width="250px" />
                    </asp:BoundField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
            </cc1:SmartGridView>
        </span>
        <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />--%>
    </form>
</body>
</html>
