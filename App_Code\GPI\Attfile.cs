﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Attfile 的摘要描述
    /// </summary>
    public class Attfile : GPI.mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;
        private string _filetype;
        private int _gpiid;
        private int _seqsn;
        private string _filename;
        private int _ver;
        private string _filetxt;
        private string _file_url;
        private string _empno;
        private string _IP; //20200915 IRENE

        #endregion

        #region 建構子

        public Attfile()
        {
            _errorMessage = String.Empty;
            _seqsn = 0;
            _filetype = String.Empty;
            _gpiid = 0;
            _filename = String.Empty;
            _ver = 0;
            _filetxt = String.Empty;
            _file_url = String.Empty;
            _empno = String.Empty;
            _IP = String.Empty; //20200915 IRENE
        }

        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }
        /// <summary>
        /// 流水號
        /// </summary>
        public int Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }

        /// <summary>
        /// 文件類型(GI:基本資料、GB:規劃構想、GO:投標檢核 投標文件、GS:投標檢核 內部作業文件)
        /// </summary>
        public string FileType
        {
            get { return _filetype; }
            set { _filetype = value; }
        }

        /// <summary>
        /// 檔案流水號
        /// </summary>
        public int GpiId
        {
            get { return _gpiid; }
            set { _gpiid = value; }
        }
        /// <summary>
        /// 檔案名稱
        /// </summary>
        public string FileName
        {
            get { return _filename; }
            set { _filename = value; }
        }
        /// <summary>
        /// 版次
        /// </summary>
        public int Ver
        {
            get { return _ver; }
            set { _ver = value; }
        }
        /// <summary>
        /// 電子檔說明
        /// </summary>
        public string FileTxt
        {
            get { return _filetxt; }
            set { _filetxt = value; }
        }

        /// <summary>
        /// 檔案儲存位置含檔名
        /// </summary>
        public string File_URL
        {
            get { return _file_url; }
            set { _file_url = value; }
        }
        /// <summary>
        /// 工號
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        /// <summary>
        /// IP
        /// 2020/09 IRENE ADD
        /// </summary>
        public string IP
        {
            get { return _IP; }
            set { _IP = value; }
        }
        #endregion

        #region 公有函式
        public bool Insert()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_file_insert";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_ver", _ver);
            oCmd.Parameters.AddWithValue("@gpi_filetype", _filetype);
            oCmd.Parameters.AddWithValue("@gpi_filename", _filename);
            oCmd.Parameters.AddWithValue("@gpi_file_url", _file_url);
            oCmd.Parameters.AddWithValue("@gpi_keyinempno", _empno);
            oCmd.Parameters.AddWithValue("@IP", _IP); //20200921 IRENE ADD

            try
            {
                _gpiid = int.Parse(this.getTopOne(oCmd, CommandType.StoredProcedure));
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool InsertLog()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_file_download";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_filename", _filename);
            oCmd.Parameters.AddWithValue("@gpi_keyinempno", _empno);
            oCmd.Parameters.AddWithValue("@IP", _IP); //20200915 IRENE ADD

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 刪除檔案
        /// </summary>
        /// <returns>Y/N</returns>
        public string Delete()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_file_delete";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_filename", _filename);
            oCmd.Parameters.AddWithValue("@gpi_keyinempno", _empno);
            oCmd.Parameters.AddWithValue("@IP", _IP); //20200915 IRENE ADD

            return this.getTopOne(oCmd, CommandType.StoredProcedure);
        }
        public bool UpdateFileTxt()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
UPDATE gpi_attfile1 
SET gpi_filetxt=@gpi_filetxt 
WHERE gpi_seqsn=@gpi_seqsn AND gpi_filetype=@gpi_filetype
";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_filetype", _filetype);
            oCmd.Parameters.AddWithValue("@gpi_filetxt", _filetxt);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public DataTable GetFileList()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_attfile1_by_seqsn";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_ver", _ver);
            oCmd.Parameters.AddWithValue("@filetype", _filetype);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        public DataTable GetFile()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT gpi_attfile1.*,
Convert(Char(8),gpi_uploaddate,112) AS gpi_uploaddate_8 ,
main_gpino,
main_modify,
main_status
FROM gpi_attfile1
LEFT JOIN gpi_main ON  gpi_seqsn = main_seqsn
WHERE 1 = 1";

            if (_seqsn != 0)
            {
                oCmd.CommandText += " AND gpi_seqsn = @gpi_seqsn";
                oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            }
            if (_filetype != string.Empty)
            {
                oCmd.CommandText += " AND gpi_filetype = @gpi_filetype";
                oCmd.Parameters.AddWithValue("@gpi_filetype", _filetype);
            }
            if (_gpiid != 0)
            {
                oCmd.CommandText += " AND gpi_id = @gpi_id";
                oCmd.Parameters.AddWithValue("@gpi_id", _gpiid);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion
    }
}