﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_view.aspx.cs" Inherits="TreatyCase2_view" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot.ascx" TagName="Foot" TagPrefix="uc2" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.defaultvalue-1.0.js"></script>

    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>

    <script src="../Scripts/bootstrap-datepicker/moment.min.js"></script>
    <script src="../Scripts/bootstrap-datepicker/bootstrap-datepicker.js"></script>
    <script type="text/javascript">

        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);

        }
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("./colorbox_close.aspx");
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function Find_Empno(obj, arg_sw) {
            $(".ajax_mesg").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback2);
                    }
                }
            });

            $(".ajax_kw").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });

        }
        function Find_empno_kw(obj, arg_sw) {
            var strURL = "../../comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val());
            if (arg_sw == "1") {
                $('#txt_promoter_empno').val("");
                $('#txt_promoter_name').val("");
                $('#txtTel').val("");
                $('#txtOrgAbbrName').val("");
                $('#x_dept').val("");
                $('#txt_req_dept').val("");
                $('#txtOrgAbbrName').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback1);
            }
            if (arg_sw == "2") {
                $('#h_px_empno').val("");
                $('#txt_px_name').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    //Find_Empno("txt_promoter_name", "1");
                    break;
                default:
                    $('#txt_promoter_empno').val(data.c_com_empno);
                    $('#txt_promoter_name').val(data.c_com_cname);
                    $('#txtTel').val(data.c_com_telext);
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
                    $('#x_dept').val(data.c_com_deptid.substr(3, 5));
                    $('#txt_req_dept').html(data.c_com_deptid);
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }
        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url,
                title: '挑選客戶'
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

                }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else {
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    }
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function find_contact(seno) {
            $(".ajax_contact").colorbox({
                href: './TreatyCase2_contactAdd.aspx?seno=' + seno,
                title: '新增聯絡人'
                , iframe: true, width: "400px", height: "300px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("contact_renew", 0);
                }
            });
        }
        function find_contract(seno) {
            $(".ajax_contract").colorbox({
                href: './TreatyCase2_Contract_Find.aspx?seno=' + seno,
                title: '新增契約'
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("contract_renew", 0);
                }
            });
        }
        function find_Court(seno) {
            $(".ajax_Court").colorbox({
                href: './TreatyCase2_CourtAdd.aspx?seno=' + seno,
                title: '新增聯絡人'
                , iframe: true, width: "400px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Court_renew", 0);
                }
            });
        }
        function find_ip(seno) {
            $(".ajax_impeach").colorbox({
                href: './TreatyCase2_IP.aspx?seno=' + seno,
                title: '新增專利'
                , iframe: true, width: "900px", height: "680px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("impeach_renew", 0);
                }
            });
        }
        function find_Evidence(seno, subseno) {
            $(".ajax_Evidence").colorbox({
                href: './TreatyCase2_Evidence.aspx?seno=' + seno + "&sub_seno=" + subseno,
                title: '新增侵權證據資料'
                , iframe: true, width: "700px", height: "400px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Evidence_renew", 0);
                }
            });
        }
        function find_Treaty(seno) {
            $(".ajax_treaty").colorbox({
                href: './TreatyCase2_Treaty_Find.aspx?seno=' + seno,
                title: '新增議約'
                , iframe: true, width: "900px", height: "680px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("FU_renew", 0);
                }
            });
        }
        function Treaty_Modify(seno, sub_seno) {
            Ftitle = "維護 後續契約簽訂情形";
            Freflash = "FU_renew";
            $(".ajax_treaty").colorbox({
                href: './TreatyCase2_treaty.aspx?seno=' + seno + '&sub_seno=' + sub_seno,
                title: Ftitle
                , iframe: true, width: "870px", height: "440px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic(Freflash, 0);
                }
            });
        }
        function Efile(seno, sub_seno, FType) {
            var Ftitle = "";
            var Freflash = "";
            if (FType == "IE") {
                Ftitle = "新增/維護 侵權證據檔案";
                Freflash = "Evidence_renew";
            }

            if (FType == "DL") {
                Ftitle = "新增/維護 委任律師檔案";
                Freflash = "DL_renew";
            }

            if (FType == "CR") {
                Ftitle = "新增/維護 處理紀錄檔案";
                Freflash = "CR_renew";
            }
            if (FType == "FU") {
                Ftitle = "新增/維護 後續契約簽訂情形檔案";
                Freflash = "FU_renew";
            }
            $(".ajax_XFile").colorbox({
                href: './TreatyCase2_XFile.aspx?seno=' + seno + "&sub_seno=" + sub_seno + "&FType=" + FType,
                title: Ftitle
                , iframe: true, width: "870px", height: "440px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic(Freflash, 0);
                }
            });
        }
        function EfileCoentent(seno, sub_seno, FType) {
            if (FType == "CR") {
                Ftitle = "新增/維護 處理紀錄檔案";
                Freflash = "file_renew";
            }
            $(".ajax_XFileModify").colorbox({
                href: './TreatyCase2_XFileModify.aspx?fid=' + sub_seno + "&FType=" + FType,
                title: Ftitle
                , iframe: true, width: "870px", height: "440px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic(Freflash, 0);
                }
            });
        }
        function Lawer_Modify(seno, sub_seno) {
            Ftitle = "維護 委任律師";
            Freflash = "Lawer_renew";
            $(".ajax_Lawer").colorbox({
                href: './TreatyCase2_Lawer.aspx?seno=' + seno + '&sub_seno=' + sub_seno,
                title: Ftitle
                , iframe: true, width: "870px", height: "440px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic(Freflash, 0);
                }
            });
        }
        function Precord_Modify(seno, sub_seno) {
            Ftitle = "新增/維護 處理紀錄";
            Freflash = "CR_renew";
            $(".ajax_PRecord").colorbox({
                href: './TreatyCase2_PRecord.aspx?seno=' + seno + '&sub_seno=' + sub_seno,
                title: Ftitle
                , iframe: true, width: "870px", height: "440px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic(Freflash, 0);
                }
            });
        }


        function Find_Prjno() {
            $(".ajax_proj").colorbox({
                href: '../subap/Qry_Projno.aspx',
                title: '計畫代號查詢'
                , iframe: true, width: "650px", height: "540px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../subap/ret_Projno.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callbackProj);
                }
            });
        }
        function jsonp_callbackProj(data) {
            switch (data.c_pojno) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    $("#TB_PlanBudget").val(data.c_pojno);
                    break;
            }
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function treaty_fileup(contno, seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase2_FileUp.aspx?contno=" + contno + "&seno=" + seno
                , title: '檔案上傳'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }
        function treaty_cop(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase2_cop.aspx?seno=" + seno
                , title: '協同法務 新增/維護'
                , iframe: true, width: "400px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("cop_renew", 0);
                }
            });
        }
        function treaty_defert(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase2_Defer.aspx?seno=" + seno
                , title: '議約展延'
                , iframe: true, width: "750px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Defer_renew", 0);
                }
            });
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light"></div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td>
                                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                                        </td>
                                        <td align="right">
                                            <span class="gentable font-normal">
                                                <asp:Button ID="BT_rework" runat="server" Text="案件重開" class="genbtnS" OnClick="BT_rework_Click" />
                                                <asp:Image ID="Image1" runat="server" Height="25px" ImageUrl="../images/CONFIDENTIAL.png" Width="85px" />
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:Label ID="txtComplexNo" runat="server"></asp:Label>[舊案號:<asp:Label ID="txtOldContno" runat="server" Width="150px"></asp:Label>]</td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">收文日期</div>
                                        </td>
                                        <td class="width40">
                                            <asp:Label ID="txt_accept_date" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label><div style="display: none">
                                                <asp:TextBox ID="txt_req_dept" runat="server" />
                                            </div>
                                            &nbsp;
                        <asp:Label ID="x_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server"></asp:Label>
                                            分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="txt_name" runat="server" Width="608px"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">

                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="統號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_company" runat="server" Text='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="60px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="跳票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_退換票" runat="server" Text='<%# Eval("tmp_退換票") %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資本額逾½">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資本額" runat="server" Text='<%# Eval("tmp_資本額") %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資產遭查封">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資產遭查封" runat="server" Text='<%# Eval("tmp_資產遭查封") %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="抽換票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_抽換票" runat="server" Text='<%# Eval("tmp_抽換票")%>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="其他風險">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_其他風險" runat="server" Text='<%# Eval("tmp_其他風險") %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="含陸資">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_含陸資" runat="server" Text='<%# Eval("陸資") %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>

                                                            <asp:TemplateField HeaderText="廠商名稱">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_cName" runat="server" Text='<%# Server.HtmlEncode(Eval("comp_cname").ToString()) %>'></asp:Label><br />
                                                                    <asp:Label ID="LB_eName" runat="server" Text='<%# Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="500px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="120px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="代表人">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="TB_comp_chairman" runat="server" Width="80px" Text='<%# Server.HtmlEncode(Eval("comp_chairman").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">地址</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="TB_Addr" runat="server" /></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">聯絡人</div>
                                        </td>
                                        <td colspan="3">
                                            <span class="stripeMe">
                                                <cc1:SmartGridView ID="SGV_contact" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_contact_RowCommand">
                                                    <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                    <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                    <AlternatingRowStyle CssClass="TRowEven" />
                                                    <Columns>
                                                        <asp:BoundField DataField="tc2c_compname" HeaderText="公司">
                                                            <HeaderStyle Width="250px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tc2c_name" HeaderText="姓名">
                                                            <HeaderStyle Width="80px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tc2c_tel" HeaderText="電話">
                                                            <HeaderStyle Width="80px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tc2c_mail" HeaderText="mail">
                                                            <HeaderStyle Width="100px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                    </Columns>
                                                    <EmptyDataTemplate>
                                                        <!--當找不到資料時則顯示「無資料」-->
                                                        <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無聯絡人資料，請新增!"></asp:Label>
                                                    </EmptyDataTemplate>
                                                    <FooterStyle BackColor="White" />
                                                    <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                </cc1:SmartGridView>
                                            </span>
                                            <%--<asp:SqlDataSource ID="SDS_contact" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">備註</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="TB_ManageNote" runat="server" Height="60px"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">權侵權檢舉</div>
                                        </td>
                                        <td colspan="3">

                                            <span class="stripeMe">
                                                <asp:GridView ID="GV_impeach" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="GV_impeach_RowCommand" OnRowDataBound="GV_impeach_RowDataBound">
                                                    <Columns>

                                                        <asp:BoundField DataField="tc2p_patentno" HeaderText="件編號">
                                                            <ItemStyle Width="100px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tc2p_ipno" HeaderText="專利証號">
                                                            <ItemStyle Width="100px" HorizontalAlign="right" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="ip" HeaderText="智權型態">
                                                            <ItemStyle Width="60px" HorizontalAlign="center" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tc2p_name" HeaderText="名稱">
                                                            <ItemStyle Width="400px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="country" HeaderText="國別">
                                                            <ItemStyle Width="50px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無本院智權資料! </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_impeach" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                            <hr />

                                            <div class="left">◆侵權證據資料： </div>
                                            <span class="stripeMe">
                                                <asp:GridView ID="GV_Evidence" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="GV_Evidence_RowCommand" OnRowDataBound="GV_Evidence_RowDataBound">
                                                    <Columns>
                                                        <asp:BoundField DataField="Evidence" HeaderText="證據類型">
                                                            <ItemStyle Width="50px" HorizontalAlign="center" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="tcie_name" HeaderText="名稱">
                                                            <ItemStyle Width="400px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:TemplateField HeaderText="附件">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_FileCount" runat="server" Text='<%#Server.HtmlEncode(Eval("fileCount").ToString()) %>' Visible="false"></asp:Label>
                                                                <asp:Label ID="LB_sub_seno" runat="server" Text='<%#Server.HtmlEncode(Eval("tcie_sub_seno").ToString()) %>' Visible="false"></asp:Label>
                                                                <asp:ImageButton ID="Evidence_no" runat="server" Class="ajax_XFile" ImageUrl="../images/null.gif" Height="20px"></asp:ImageButton>
                                                            </ItemTemplate>
                                                            <ItemStyle HorizontalAlign="center" Width="20px" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無侵權證據資料! </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_Evidence" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                            <hr />
                                            <div class="left">◆侵權檢舉說明：<asp:Label ID="TB_tort_impeach" runat="server"></asp:Label></div>

                                            <hr />
                                            <div class="left">
                                                ◆經費支應： 預算計畫代號<asp:Label ID="TB_PlanBudget" runat="server"></asp:Label>
                                                &nbsp;
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            權利金收取</td>
                                        <td colspan="3">
                                            <span class="stripeMe">
                                                <asp:GridView ID="GV_Contract" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="GV_Contract_RowCommand" OnRowDataBound="GV_Contract_RowDataBound">
                                                    <Columns>
                                                        <asp:BoundField DataField="actcontno2" HeaderText="編號">
                                                            <ItemStyle Width="120px" HorizontalAlign="center" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="promo_contname" HeaderText="名稱">
                                                            <ItemStyle Width="300px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="promo_totalfee" HeaderText="契約金額">
                                                            <ItemStyle Width="100px" HorizontalAlign="right" />
                                                        </asp:BoundField>
                                                        <asp:TemplateField HeaderText="契約期間">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_sday" runat="server" Text='<%# Server.HtmlEncode(Eval("promo_contsdate").ToString()) %>'></asp:Label><br />
                                                                <asp:Label ID="LB_eday" runat="server" Text='<%# Server.HtmlEncode(Eval("promo_contedate").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <ItemStyle HorizontalAlign="center" Width="100px" />
                                                        </asp:TemplateField>
                                                        <asp:BoundField DataField="promo_projleader" HeaderText="計畫主持人">
                                                            <ItemStyle Width="80px" HorizontalAlign="center" />
                                                        </asp:BoundField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無契約資料! </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                            </span>
                                            <%--<asp:SqlDataSource ID="SDS_Contract" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            <div class="left">◆案由摘要：<asp:Label ID="TB_RoySummary" runat="server"></asp:Label></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            訴訟</td>
                                        <td colspan="3">
                                            <div class="left">◆起訴日期：<asp:Label ID="TB_prosecution_date" runat="server"></asp:Label></div>
                                            <hr />
                                            <div class="left">
                                                ◆法律/檢察署： 
                        <table width="100%" style="border-width: 1px; border-style: solid; border-color: #CCDDFF;">
                            <asp:Repeater ID="RT_法律檢察署" runat="server" OnItemDataBound="RT_法律檢察署_ItemDataBound" OnItemCommand="RT_法律檢察署_ItemCommand">
                                <ItemTemplate>
                                    <tr>
                                        <td>
                                            <asp:Label ID="LB_Court" runat="server"></asp:Label></td>
                                        <td>
                                            <asp:Label ID="LB_Year" runat="server"></asp:Label>&nbsp;年度</td>
                                        <td>
                                            <asp:Label ID="LB_Word" runat="server"></asp:Label>&nbsp;字</td>
                                        <td>第&nbsp;<asp:Label ID="LB_No" runat="server"></asp:Label>&nbsp;號</td>
                                        <td>
                                            <asp:Label ID="LB_Stock" runat="server"></asp:Label>&nbsp;股別</td>
                                    </tr>
                                </ItemTemplate>
                            </asp:Repeater>
                            <%--<asp:SqlDataSource ID="SDS_法律檢察署" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                        </table>
                                            </div>
                                            <hr />
                                            <div class="left">
                                                <table boder="0" width="100%">
                                                    <tr>
                                                        <td>◆案件類型：</td>
                                                        <td align="left">
                                                            <asp:RadioButtonList ID="RBL_litigation_type" runat="server" RepeatDirection="Horizontal" Enabled="false">
                                                                <asp:ListItem Value="1">民事</asp:ListItem>
                                                                <asp:ListItem Value="2">刑事</asp:ListItem>
                                                                <asp:ListItem Value="3">行政</asp:ListItem>
                                                            </asp:RadioButtonList>
                                                        </td>
                                                        <td>案由<asp:Label ID="TB_Litigation_name" runat="server"></asp:Label>
                                                            訴訟標的<asp:Label ID="TB_Litigation_target" runat="server"></asp:Label>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <hr />
                                            <div class="left">
                                                ◆委任律師事務所： 
                            <span class="stripeMe">
                                <asp:GridView ID="GV_Lawer" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="GV_Lawer_RowCommand" OnRowDataBound="GV_Lawer_RowDataBound">
                                    <Columns>
                                        <asp:BoundField DataField="tc2l_lawer" HeaderText="委任律師">
                                            <ItemStyle Width="100px" HorizontalAlign="center" />
                                        </asp:BoundField>
                                        <asp:BoundField DataField="tc2l_date" HeaderText="日期">
                                            <ItemStyle Width="100px" HorizontalAlign="Left" />
                                        </asp:BoundField>
                                        <asp:BoundField DataField="tc2l_content" HeaderText="摘要">
                                            <ItemStyle Width="400px" HorizontalAlign="Left" />
                                        </asp:BoundField>
                                        <asp:TemplateField HeaderText="附件">
                                            <ItemTemplate>
                                                <asp:Label ID="LB_委任律師_FileCount" runat="server" Text='<%#Server.HtmlEncode(Eval("fileCount").ToString()) %>' Visible="false"></asp:Label>
                                                <asp:Label ID="LB_委任律師_sub_seno" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2l_sub_seno").ToString()) %>' Visible="false"></asp:Label>
                                                <asp:ImageButton ID="Lawer_no" runat="server" Class="ajax_XFile" ImageUrl="../images/null.gif" Height="20px"></asp:ImageButton>
                                            </ItemTemplate>
                                            <ItemStyle HorizontalAlign="center" Width="20px" />
                                        </asp:TemplateField>
                                    </Columns>
                                    <EmptyDataTemplate>無侵權證據資料! </EmptyDataTemplate>
                                    <PagerSettings Position="Bottom" />
                                    <PagerStyle HorizontalAlign="Left" />
                                </asp:GridView>
                            </span>
                                                <%--<asp:SqlDataSource ID="SDS_Lawer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </div>
                                            <hr />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">處理紀錄</div>
                                        </td>
                                        <td align="left" colspan="3">
                                            <span class="stripeMe">
                                                <span class="gentable font-normal">
                                                    <asp:GridView ID="gv_處理紀錄" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_處理紀錄_RowCommand" OnRowDataBound="gv_處理紀錄_RowDataBound">
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="日期">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_日期" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2r_recordate").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="80px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="紀錄情形">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_紀錄情形" runat="server" Text='<%#Server.HtmlEncode(Eval("type").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="紀錄">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_紀錄" runat="server" Text='<%#Server.HtmlEncode(Eval("item").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="下次庭期">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_下次庭期" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2r_nextdate","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="80px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="摘要">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_摘要" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2r_docu").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="450px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Left" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="附件">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_處理紀錄_FileCount" runat="server" Text='<%#Server.HtmlEncode(Eval("fileCount").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:Label ID="LB_處理紀錄_sub_seno" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2r_sub_seno").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:ImageButton ID="IB_處理紀錄_no" runat="server" Class="ajax_XFile" ImageUrl="../images/null.gif" Height="20px"></asp:ImageButton>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="center" Width="20px" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無資料 </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_處理紀錄" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">後續契約簽訂情形</div>
                                        </td>
                                        <td align="left" colspan="3">
                                            <span class="stripeMe">
                                                <span class="gentable font-normal">
                                                    <asp:GridView ID="gv_後續契約簽訂情形" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_後續契約簽訂情形_RowCommand" OnRowDataBound="gv_後續契約簽訂情形_RowDataBound">
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="議約編號">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_議約編號" runat="server" Text='<%#Server.HtmlEncode(Eval("caseName").ToString()) %>'></asp:Label>
                                                                    <asp:Label ID="LB_後續契約簽訂情形_議約_seno" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2u_treaty_seno").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:ImageButton ID="Lawer_後續契約簽訂情形_議約_no" runat="server" Class="ajax_tpr" ImageUrl="../images/null.gif" Height="20px"></asp:ImageButton>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="left" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="摘要">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_後續契約簽訂情形_摘要" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2u_abstract").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="left" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="附件">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_後續契約簽訂情形_FileCount" runat="server" Text='<%#Server.HtmlEncode(Eval("fileCount").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:Label ID="LB_後續契約簽訂情形_sub_seno" runat="server" Text='<%#Server.HtmlEncode(Eval("tc2u_sub_seno").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:ImageButton ID="IB_後續契約簽訂情形_no" runat="server" Class="ajax_XFile" ImageUrl="../images/null.gif" Height="20px"></asp:ImageButton>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="center" Width="20px" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無資料 </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_後續契約簽訂情形" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td align="left" colspan="3">

                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="說明">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="300px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="50px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}")) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">預估完成日展延</div>
                                        </td>
                                        <td align="left" colspan="3">
                                            <span class="stripeMe">
                                                <asp:GridView ID="GV_Defer" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowDataBound="GV_Defer_RowDataBound">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="展延後預估完成日">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_defer_date" runat="server" Text='<%# Server.HtmlEncode(Eval("tc2d_defer_date").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="提出展延日">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_keyin_date" runat="server" Text='<%# Server.HtmlEncode(Eval("tc2d_keyin_date").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <ItemStyle HorizontalAlign="Center" Width="120px" />
                                                        </asp:TemplateField>
                                                        <asp:BoundField DataField="tc2d_desc" HeaderText="展延原因">
                                                            <ItemStyle Width="550px" HorizontalAlign="Left" />
                                                        </asp:BoundField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無展延! </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_Defer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>

                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                                &nbsp;
	           <asp:Button runat="server" Text="編輯" class="ajax_mesg genbtnS" ID="btnEdit" Visible="False" OnClick="btnEdit_Click"></asp:Button>
                            </div>
                        </div>
                        <div class="uplineT1">
                            <span class="gentablenoline">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">分案主管</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔/分案日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>
                                            / 
                                            <asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">進度</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal><asp:DropDownList ID="DDL_Degree" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_DDL_degree" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">協同法務承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_cop" runat="server"></asp:Literal><%--<asp:SqlDataSource ID="SDS_cop" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">處理天數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_process_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">產出文件數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_contract_count" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">預估/需求結件日</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_expect_close_date" runat="server"></asp:Literal>
                                            / 
                                            <asp:Literal ID="lb_case_closedate" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改人</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- uplineT1 -->
                    </div>
                    <!-- fixwidth -->
                </div>
                <!-- WrapperContent -->
            </div>
            <!-- WrapperBody -->

            <uc2:Foot runat="server" ID="Foot" />
            <%--            <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_CloseCase" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>

    <script type="text/javascript">
        $(document).ready(function () {

            $(".inputhint").tooltip({
                position: { my: "left+10 bottom+40", at: "left bottom " },
                tooltipClass: "custom-tooltip-styling",
                //讓tooltips內可以放置HTML CODE
                content: function () { return $(this).prop('title'); }
            });
            //說明dialog
            $("#pagehow01").dialog({
                modal: true,
                position: ["center", 100],
                width: 500,
                height: 300,
                autoOpen: false,
                show: { duration: 300 },
                hide: { duration: 300 }
            });
            // $("#txtSignReason").change(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //});
            //$("img.help_txtSignReason").mouseover(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //}); 
        });





    </script>


</body>
</html>
