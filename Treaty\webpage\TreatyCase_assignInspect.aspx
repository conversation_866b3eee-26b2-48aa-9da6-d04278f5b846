﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_assignInspect.aspx.cs" Inherits="Treaty_webpage_TreatyCase_assignInspect" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>新增審查人</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("指定成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 15px">
                <tr>
                    <td class="td_right">新增審查人：</td>
                    <td>
                        <asp:DropDownList ID="DDL_AssignInspect" runat="server" DataTextField="empName" DataValueField="empNo" Height="20px" Width="145px"></asp:DropDownList>&nbsp;&nbsp;
                        <asp:Button ID="BT_Save" runat="server" Text="新增" class="genbtnS" OnClick="BT_Save_Click" />
                    </td>
                </tr>
            </table>
            <%--   <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        </span>
    </form>
</body>
</html>
