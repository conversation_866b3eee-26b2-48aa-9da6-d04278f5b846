﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TreatyCase_Inspect : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["seno"] = "680";
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
            }
            ViewState["tci_no"] = "544";
            if (Request.QueryString["tci_no"] != null)
            {
                if (!IsNumber(Request.QueryString["tci_no"]) || (Request.QueryString["tci_no"].Length == 0) || (Request.QueryString["tci_no"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tci_no"] = Request.QueryString["tci_no"].ToString();
            }
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            LB_inspectName.Text = Server.HtmlEncode(ssoUser.empName);
            databinding();
        }
    }
    protected void databinding()
    {
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.Text;
        //this.SDS_SC.SelectCommand = "select rtrim(emp_no) empNo  ,rtrim(emp_name) empName  from  treaty_buztbl order by emp_group";
        //this.SDS_SC.DataBind();
        //DDL_AssignInspect.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select rtrim(emp_no) empNo ,rtrim(emp_name) empName  from  treaty_buztbl order by emp_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_AssignInspect.DataSource = dt;
                DDL_AssignInspect.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (DDL_AssignInspect.SelectedValue.Length > 7)
            Response.Redirect("../danger.aspx");

        //if ((RB_inspect_flag.SelectedValue == "2") && (TB_Inspect.Text == ""))
        //{
        //    StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('簽核不同意時 簽核意見 必須填寫 !');</script>");
        //    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        //}
        //else
        //{
        if (TB_Inspect.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");
        //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.InsertCommand = "esp_TreatyCase_Inspect_update";
        //this.SDS_SC.InsertParameters.Add("tci_no", TypeCode.String, ViewState["tci_no"].ToString());
        //this.SDS_SC.InsertParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //this.SDS_SC.InsertParameters.Add("inspect_desc", TypeCode.String,TB_Inspect.Text);
        //this.SDS_SC.InsertParameters.Add("inspect_flag", TypeCode.String,RB_inspect_flag.SelectedValue);
        //this.SDS_SC.InsertParameters.Add("next_inspector_empno", TypeCode.String,IIf(RB_inspect_flag.SelectedValue=="2","",DDL_AssignInspect.SelectedValue));
        //this.SDS_SC.InsertParameters.Add("next_inspector_name", TypeCode.String, IIf(RB_inspect_flag.SelectedValue=="2","",DDL_AssignInspect.SelectedItem.Text));
        //this.SDS_SC.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_Inspect_update";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("tci_no", oRCM.SQLInjectionReplaceAll(ViewState["tci_no"].ToString()));
            sqlCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("inspect_desc", oRCM.SQLInjectionReplaceAll(TB_Inspect.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("inspect_flag", RB_inspect_flag.SelectedValue);
            sqlCmd.Parameters.AddWithValue("next_inspector_empno", IIf(RB_inspect_flag.SelectedValue == "2", "", DDL_AssignInspect.SelectedValue));
            sqlCmd.Parameters.AddWithValue("next_inspector_name", IIf(RB_inspect_flag.SelectedValue == "2", "", DDL_AssignInspect.SelectedItem.Text));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        //}
    }
    protected void RB_inspect_flag_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (RB_inspect_flag.SelectedValue == "2")
        {
            DDL_AssignInspect.Visible = false;
            DDL_AssignInspect.SelectedValue = "";
        }
        else
            DDL_AssignInspect.Visible = true;

    }
}