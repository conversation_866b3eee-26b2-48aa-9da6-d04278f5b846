﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;

namespace Engage
{
	/// <summary>
	/// Summary description for Utility
	/// </summary>
	public class Utility
	{
		public Utility() {}


		/// <summary>
		/// 顯示完訊息後導至網頁
		/// </summary>
		/// <param name="msg">輸出的訊息</param>
		/// <param name="path">導至錨點</param>
		static public void Alert_ToAnchor(Control control, string msg, string anchor)
		{
			string script = string.Format(@"
					$(document).ready(function () 
					{{
						alert('{0}');
						$('{1}').click();
					}});
				", msg, anchor);
			ScriptManager.RegisterStartupScript(control, control.GetType(), "msg", script, true);
		}

		static public void OpenJqueryDialog(Control control, string clientDialog)
		{
			string script = string.Format(@"
					$(document).ready(function () 
					{{
						$('{0}').click();
					}});
				", clientDialog);
			ScriptManager.RegisterStartupScript(control, control.GetType(), "msg", script, true);
		}

	}
}