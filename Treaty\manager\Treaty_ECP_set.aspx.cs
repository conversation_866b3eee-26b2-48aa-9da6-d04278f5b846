﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_ECP_set : Treaty.common   //System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();

            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = " select count(*) from treaty_buztbl where emp_group='0' and emp_no=@empno ";
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            oCmd.CommandType = CommandType.Text;
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "myTable");
            if (ds != null && ds.Tables[0].Rows.Count == 0)
            {
                Response.Redirect("../NoAuthRight.aspx");
            }
            if (ds != null && ds.Tables[0].Rows.Count >= 1)
            {
                ViewState["SYS"] = ssoUser.empNo;
            }

            BindOrglist();
            BindData();

            txt_send_name.Attributes.Add("readOnly", "readonly");
            txt_sign_name.Attributes.Add("readOnly", "readonly");

        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_search));
    }

    private void BindOrglist()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_ECP_set";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@mode", "signorglist");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_orglist.DataSource = dt;
                DDL_orglist.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void BindData()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_ECP_set";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@ECP_TYPE", oRCM.SQLInjectionReplaceAll(rbl_TYPE.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@mode", "search");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                if ((ViewState["sortField"] + "") != "")
                    dt.DefaultView.Sort = ViewState["sortField"].ToString() + " " + ViewState["sortorder"].ToString();

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void ClearInput()
    {
        foreach (Control ctrl in Form1.Controls)
        {
            if (ctrl is DropDownList)
            {
                ((DropDownList)ctrl).SelectedIndex = 0;
            }
            if (ctrl is ListBox)
            {
                ((ListBox)ctrl).SelectedIndex = 0;
            }
            if (ctrl is TextBox)
            {
                ((TextBox)ctrl).Text = "";
            }
        }
    }

    protected void btnAdd_Click(object sender, EventArgs e)
    {
        string org = DDL_orglist.SelectedValue;
        if (org == "00")
            org = "";
        if (LBX_deptlist1.SelectedValue != "")
            org = LBX_deptlist1.SelectedValue;
        if (LBX_deptlist2.SelectedValue != "")
            org = LBX_deptlist2.SelectedValue;

        string str_error = "";
        if (txt_sign_name.Text == "")
        {
            str_error += "★簽核人員 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_sign_name').validationEngine('showPrompt', '★簽核人員 必須輸入','','',true); $('#txt_sign_name').click(function () { $('#txt_sign_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_sign_name", script_alert);
        }

        if (rbl_TYPE.SelectedValue == "1" && (org == ""))
        {
            str_error += "★簽核單位 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#LBX_deptlist1').validationEngine('showPrompt', '★簽核單位 必須輸入','','',true); $('#LBX_deptlist1').click(function () { $('#LBX_deptlist1').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "LBX_deptlist1", script_alert);
        }

        if (rbl_TYPE.SelectedValue == "2" && txt_send_name.Text == "")
        {
            str_error += "★送簽人員 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_send_name').validationEngine('showPrompt', '★送簽人員 必須輸入','','',true); $('#txt_send_name').click(function () { $('#txt_send_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_send_name", script_alert);
        }

        if (rbl_TYPE.SelectedValue == "3" && txt_sign_empno.Text == "")
        {
            str_error += "★送簽人員 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_sign_name').validationEngine('showPrompt', '★簽核人員必須輸入','','',true); $('#txt_sign_name').click(function () { $('#txt_sign_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_sign_name", script_alert);
        }
        if (rbl_TYPE.SelectedValue == "4" && txt_sign_empno.Text == "")
        {
            str_error += "★送簽人員 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_sign_name').validationEngine('showPrompt', '★簽核人員必須輸入','','',true); $('#txt_sign_name').click(function () { $('#txt_sign_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_sign_name", script_alert);
        }
        if (str_error != "")
        {
            string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
            return;
        }

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_ECP_set";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@ECP_TYPE", oRCM.SQLInjectionReplaceAll(rbl_TYPE.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@ECP_org", oRCM.SQLInjectionReplaceAll(org));
            sqlCmd.Parameters.AddWithValue("@ECP_send", oRCM.SQLInjectionReplaceAll(txt_send_empno.Text));
            sqlCmd.Parameters.AddWithValue("@ECP_sign", oRCM.SQLInjectionReplaceAll(txt_sign_empno.Text));
            sqlCmd.Parameters.AddWithValue("@mode", "add");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlCmd.ExecuteNonQuery();

                ClearInput();
                BindData();

                ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
<script type='text/javascript'>
alert('新增成功');
</script>", false);
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }

    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {

        BindData();
        SGV_search.PageIndex = e.NewPageIndex;
    }

    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {

    }

    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            DropDownList ddlHec = (DropDownList)e.Row.FindControl("DDL_Hec");

            if ((ddlHec != null))
            {
                ddlHec.Visible = true;
                ddlHec.Attributes["caseno"] = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "ECP_id")).Trim();
                ddlHec.SelectedValue = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "排序"));
            }
        }

    }

    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = e.SortDirection;
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            string a = ViewState["sortorder"].ToString();
            if (ViewState["sortorder"].ToString() == "ASC")
                ViewState["sortorder"] = "DESC";
            else
                ViewState["sortorder"] = "ASC";
        }
        SGV_search.PageIndex = 0;
        BindData();
    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            string sortExpression = ViewState["sortField"] + "";
            string sortDirection = ViewState["sortorder"] + "";
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == sortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (sortDirection == "ASC") //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";

                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }

    #region 單位

    protected void DDL_orglist_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 組
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        this.LBX_deptlist2.Items.Clear();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_ECP_set";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@單位", oRCM.SQLInjectionReplaceAll(DDL_orglist.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@mode", "組");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                LBX_deptlist1.DataSource = dt;
                LBX_deptlist1.DataBind();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void LBX_deptlist1_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 部門
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        this.LBX_deptlist2.Items.Clear();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_ECP_set";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@單位", oRCM.SQLInjectionReplaceAll(DDL_orglist.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@組", oRCM.SQLInjectionReplaceAll(LBX_deptlist1.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@mode", "部門");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                LBX_deptlist2.DataSource = dt;
                LBX_deptlist2.DataBind();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion     
    }

    #endregion

    protected void rbl_TYPE_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (rbl_TYPE.SelectedValue == "1")
        {
            tr_ECP_org.Visible = true;
            tr_ECP_send.Visible = false;
            txt_send_empno.Text = "";
            txt_send_name.Text = "";
        }
        else if (rbl_TYPE.SelectedValue == "2")
        {
            tr_ECP_send.Visible = true;
            tr_ECP_org.Visible = false;
            DDL_orglist.SelectedIndex = 0;
            LBX_deptlist1.Items.Clear();
            LBX_deptlist2.Items.Clear();
        }
        else
        {
            tr_ECP_org.Visible = false;
            tr_ECP_send.Visible = false;
        }
        BindData();
    }

    protected void LB_del_Click(object sender, EventArgs e)
    {
        LinkButton LB_del = (LinkButton)sender;

        int ECP_id = 0;
        int.TryParse(LB_del.CommandArgument, out ECP_id);

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_ECP_set";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@ECP_id", ECP_id);
            sqlCmd.Parameters.AddWithValue("@mode", "del");

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlCmd.ExecuteNonQuery();

                ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('刪除成功');</script>", false);


            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        BindData();
    }

    protected void HecUpdate(object sender, System.EventArgs e)
    {
        string Value = ((DropDownList)sender).Attributes["caseno"];
        string 排序 = ((DropDownList)sender).SelectedValue;

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        if (IsDangerWord(排序)) Response.Redirect("../danger.aspx");
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString);
        oCmd.CommandText = "esp_ECP_set";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@ECP_id", oRCM.SQLInjectionReplaceAll(Value));
        oCmd.Parameters.AddWithValue("@排序", oRCM.SQLInjectionReplaceAll(排序));
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        oCmd.Parameters.AddWithValue("@mode", oRCM.SQLInjectionReplaceAll("up_order"));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();
        BindData();
    }


}