﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase_valuation_inspact : Treaty.common  
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull,string mode)
    { 
        if(mode== "Select")
        {
            foreach (Parameter parameter in dataSource.SelectParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if(mode == "Insert")
        {
            foreach (Parameter parameter in dataSource.InsertParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if(mode == "Updat")
        {
            foreach (Parameter parameter in dataSource.UpdateParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if(mode == "Delete")
        {
            foreach (Parameter parameter in dataSource.DeleteParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
    }

    public string tc_seno
    {
        get
        {
            if (ViewState["tc_seno"] == null)
            {
                if (Request.QueryString["seno"] == null)
                    Response.Redirect("../error.aspx");
                if (!IsNumber(Request.QueryString["seno"]))
                    Response.Redirect("../error.aspx");
                ViewState["tc_seno"] = Request.QueryString["seno"];
            }
            return ViewState["tc_seno"].ToString();
        }
    }
    //public string tci_seno
    //{
    //    get
    //    {
    //        if (ViewState["tci_seno"] == null)
    //        {
    //            if (Request.QueryString["seno"] == null)
    //                Response.Redirect("../error.aspx");
    //            if (!IsNumber(Request.QueryString["seno"]))
    //                Response.Redirect("../error.aspx");
    //            ViewState["tci_seno"] = Request.QueryString["seno"];
    //        }
    //        return ViewState["tci_seno"].ToString();
    //    }
    //}
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;

            //databinding_審查人();
            databinding();
        }
    }
    //protected void databinding_審查人() //審查人
    //{
    //    SDS_SC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
    //    SDS_SC.SelectParameters.Clear();
    //    SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
    //    SDS_SC.SelectCommand = "esp_TreatyCase_valuation";
    //    SDS_SC.SelectParameters.Add("empno", SQLInjectionReplaceAll("empno"));
    //    SDS_SC.SelectParameters.Add("mode", SQLInjectionReplaceAll("inspact_p"));
    //    SDS_SC.DataBind();
    //    ConvertSqlParametersEmptyStringToNull(SDS_SC, false, "Select");
    //    DDL_AssignInspect.DataBind();
    //}
    protected void databinding() 
    {
        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandText = "esp_TreatyCase_valuation";
        //oCmd_1.CommandType = CommandType.StoredProcedure;
        //oCmd_1.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
        //oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("版本", "0");
        //oCmd_1.Parameters.AddWithValue("mode", "view_inspect");
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        //DataSet ds_1 = new DataSet();
        //oda_1.Fill(ds_1, "myTable");
        //DataView dv = ds_1.Tables[0].DefaultView;

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@版本","0");
            sqlCmd.Parameters.AddWithValue("@mode","view_inspect");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv == null)
        {

        }
        else
        {
            if (dv.Count == 0)
            {
                // LB_tcii_seno.Text = dv[0]["tcii_seno"].ToString().Trim();
                Response.Redirect("../noAuthright.aspx");
            }
            else
            {
                ViewState["tci_seno"] = Server.HtmlEncode(dv[0]["tci_seno"].ToString().Trim());
                LB_tcii_seno.Text = Server.HtmlEncode(dv[0]["tcii_seno"].ToString().Trim());
            }
        }
    }

    protected void BT_Save_Click(object sender, EventArgs e)
    {

        string str_error = "";
        if (TB_審查意見.Text == "")
        {
            str_error += "審查意見未填\\n";
        }
        if (DDL_審查結果.SelectedValue == "")
        {
            str_error += "請挑選審查結果\\n";
        }
        if (str_error == "")
        {
            SqlCommand oCmd_1 = new SqlCommand();
            oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd_1.CommandText = "esp_TreatyCase_valuation";
            oCmd_1.CommandType = CommandType.StoredProcedure;
            oCmd_1.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            oCmd_1.Parameters.AddWithValue("tci_seno", oRCM.SQLInjectionReplaceAll(ViewState["tci_seno"].ToString()));
            oCmd_1.Parameters.AddWithValue("tcii_seno", oRCM.SQLInjectionReplaceAll(LB_tcii_seno.Text.Trim())   );
            oCmd_1.Parameters.AddWithValue("審查意見", oRCM.SQLInjectionReplaceAll(TB_審查意見.Text.Trim()));
            oCmd_1.Parameters.AddWithValue("審查結果", oRCM.SQLInjectionReplaceAll(DDL_審查結果.SelectedValue));
            oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            oCmd_1.Parameters.AddWithValue("mode", "inspect");
            SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            DataSet ds_1 = new DataSet();
            oda_1.Fill(ds_1, "myTable");
            ds_1.Dispose();
            oCmd_1.Dispose();
            oda_1.Dispose();
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('" + str_error + "');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }

    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            //SqlCommand oCmd_1 = new SqlCommand();
            //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            //oCmd_1.CommandText = "esp_TreatyCase_valuation";
            //oCmd_1.CommandType = CommandType.StoredProcedure;
            //oCmd_1.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            //oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            //oCmd_1.Parameters.AddWithValue("fid", oRCM.SQLInjectionReplaceAll(ViewState["fid"].ToString()));
            //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            //DataSet ds_1 = new DataSet();
            //oda_1.Fill(ds_1, "myTable");
            //DataView dv = ds_1.Tables[0].DefaultView;

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_valuation";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(tc_seno));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(ViewState["fid"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = Server.HtmlEncode(dv[0]["tcdf_url"].ToString().Trim());
                str_filename = Server.HtmlEncode(dv[0]["tcdf_filename"].ToString().Trim());
                if (str_file_url != "")
                {
                    Response.Clear();
                    Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                    Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                    Response.Flush();
                    Response.End();
                }
            }

        }
    }

    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");

                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
            }
        }
    }
     
}