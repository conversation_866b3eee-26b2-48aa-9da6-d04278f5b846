﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class sso : System.Web.UI.Page// clsBasePage
{
    RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if(!IsPostBack)
        {
        }
    }
    protected void btn_change_Click(object sender, EventArgs e)
    {
        Session["NewSSOEmpno"] = txt_empno.Text;
        Response.Redirect("../search/Search_contract.aspx");
    }
    protected void txt_empno_TextChanged(object sender, EventArgs e)
    {
        DataTable dt = new DataTable();
        try
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@" SELECT top 1  * from common..comper where com_empno=@empno");
                oCmd.Parameters.AddWithValue("@empno", txt_empno.Text);
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.Text;
                oCmd.CommandTimeout = 0;

                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                oda.Fill(dt);
            }
        }
        catch (Exception ex)
        {
            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString, Request, Response, ex);

            oRCM.ErrorExceptionDataToDB(logMail);
        }
        //SDS_base.SelectParameters.Clear();
        //SDS_base.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
        //SDS_base.SelectCommand = " SELECT top 1  * from common..comper where com_empno=@empno";
        //SDS_base.SelectParameters.Add("empno", txt_empno.Text);
        //for (int i = 0; i < this.SDS_base.SelectParameters.Count; i++)
        //{
        //    SDS_base.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_base.DataBind();
        System.Data.DataView dv = dt.DefaultView;// (DataView)SDS_base.Select(new DataSourceSelectArguments());
        if (dv.Count >= 1)
        {
            lbl_empname.Text = dv[0]["com_cname"].ToString().Trim();

        }
        else
        {
            lbl_empname.Text = "";
            txt_empno.Text = "";
        }
    }
}