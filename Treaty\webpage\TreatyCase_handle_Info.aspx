﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_handle_Info.aspx.cs" Inherits="Treaty_webpage_TreatyCase_handle_Info" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />

</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" Style="margin-left: 10px"
                OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" Font-Size="Small">
                <HeaderStyle CssClass="fixedheadertable" />
                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                <Columns>
                    <asp:BoundField DataField="orgName" SortExpression="orgName" HeaderText="單位">
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemStyle Width="30px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="洽案(契約)" SortExpression="contno">
                        <ItemTemplate>
                            <asp:Label ID="LB_contno" runat="server" Text='<%# Server.HtmlEncode(Eval("contno").ToString()) %>'></asp:Label><br />
                            <asp:Label ID="LB_Name" runat="server" Text='<%# Server.HtmlEncode(Eval("caseName").ToString()) %>'></asp:Label><br />
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Left" Width="350px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="compname" SortExpression="compname" HeaderText="客戶">
                        <ItemStyle HorizontalAlign="Left" Width="130px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="分案日<hr>預估結案日" SortExpression="assign_date">
                        <ItemTemplate>
                            <asp:Label ID="Label_assign_date" runat="server" Text='<%# Server.HtmlEncode(Eval("assign_date").ToString()) %>'></asp:Label>
                            <asp:Label ID="Label_prefinish_date" runat="server" Text='<%# Server.HtmlEncode(Eval("prefinish_date").ToString()) %>'></asp:Label>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Left" Width="70px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="conttype" SortExpression="conttype" HeaderText="契約性質">
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
            </cc1:SmartGridView>
        </span>
        <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />--%>
    </form>
</body>
</html>
