﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_Inspect : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["tt_seno"] = "680";
            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }
            ViewState["tti_no"] = "544";
            if (Request.QueryString["tti_no"] != null)
            {
                if (!IsNumber(Request.QueryString["tti_no"]) || (Request.QueryString["tti_no"].Length == 0) || (Request.QueryString["tti_no"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tti_no"] = Request.QueryString["tti_no"].ToString();
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            LB_inspectName.Text = Server.HtmlEncode(ssoUser.empName);

            Bind_Auth();
            Bind_Doc_File();
        }
    }

    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (TB_Inspect.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");

        #region --- insert ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_Inspect_update_v2";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tti_no", oRCM.SQLInjectionReplaceAll(ViewState["tti_no"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@inspect_desc", oRCM.SQLInjectionReplaceAll(TB_Inspect.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@inspect_flag", RB_inspect_flag.SelectedValue);
            sqlCmd.Parameters.AddWithValue("@next_inspector_empno", "");
            sqlCmd.Parameters.AddWithValue("@next_inspector_name", "");


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }

    protected void BT_FileUp_Click(object sender, EventArgs e)
    {
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        string path = oRCM.GetValidPathPart(FilePathString, "TechCase");
        path = oRCM.GetValidPathPart(path, DateTime.Now.Year.ToString());

        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);
        if (Request.ServerVariables["HTTP_VIA"] != null)
        {
            ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
        }
        if (FU_up.PostedFile.ContentLength > 0)
        {
            string upFileName = FU_up.FileName.Replace("&", "＆").Replace("­", "－");
            string str_FileName = "\\DR_" + ViewState["tt_seno"].ToString() + "_" + strPreRandom + "_" +
                                    Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("--", "－－") +
                                    Path.GetExtension(upFileName);

            FU_up.SaveAs(path.Replace("/", "").Replace("..", "") + oRCM.SQLInjectionReplaceAll(str_FileName));

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@mode", "inspect_Fup");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tti_no", oRCM.SQLInjectionReplaceAll(ViewState["tti_no"].ToString()));
                sqlCmd.Parameters.AddWithValue("@file_url", oRCM.SQLInjectionReplaceAll(path.Replace("/", "").Replace("..", "") + str_FileName));
                sqlCmd.Parameters.AddWithValue("@inspect", "0");
                sqlCmd.Parameters.AddWithValue("@fd_name", oRCM.SQLInjectionReplaceAll(upFileName.Replace("--", "－－")));
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@filetype", "R");
                
                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }

        Bind_Doc_File();
    }

    protected void RB_inspect_flag_SelectedIndexChanged(object sender, EventArgs e)
    {
        tr_file_up.Visible = RB_inspect_flag.SelectedValue == "2";

        if (tr_file_up.Visible == false)
        {
            foreach (GridViewRow row in GV_File.Rows)
            {
                LinkButton lnkbtn_Del =(LinkButton) row.FindControl("lnkbtn_Del");
                string str_file_url = "";
                string str_filename = "";

                DataTable dt = File_View(lnkbtn_Del.CommandArgument.ToString());
                DataView dv = dt.DefaultView;
                if (dv.Count >= 1)
                {
                    str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                    str_filename = dv[0]["tcdf_doc"].ToString().Trim();
                }
                FileInfo fi = new FileInfo(str_file_url);
                if (fi.Exists)
                {
                    fi.Delete();
                }
                File_Del(lnkbtn_Del.CommandArgument.ToString());

            }
            Bind_Doc_File();
        }
    }

    protected void GV_File_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_doc"].ToString().Trim();
            }
            FileInfo fi = new FileInfo(str_file_url);
            if (fi.Exists)
            {
                fi.Delete();
            }
            File_Del(e.CommandArgument.ToString());

            Bind_Doc_File();
        }

        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tcdf_doc"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }

    private void Bind_Auth()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Auth");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            string str_auth = dv_auth[0]["RW"].ToString();
            if (str_auth == "X")
                Response.Redirect("../NoAuthRight.aspx");

            ViewState["RW"] = Server.HtmlEncode(dv_auth[0]["RW"].ToString());
            ViewState["Role"] = Server.HtmlEncode(dv_auth[0]["Role"].ToString());
            ViewState["Role2"] = Server.HtmlEncode(dv_auth[0]["Role2"].ToString());
            ViewState["分案權限"] = Server.HtmlEncode(dv_auth[0]["分案權限"].ToString());
            ViewState["檢視意見彙整"] = Server.HtmlEncode(dv_auth[0]["檢視意見彙整"].ToString());
        }
        else
        {
            Response.Redirect("../NoAuthRight.aspx");
        }
    }

    /// <summary>
    /// 附件資料
    /// </summary>
    private void Bind_Doc_File()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@mode", "inspect_his_file");

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tti_no", oRCM.SQLInjectionReplaceAll(ViewState["tti_no"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
         
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

                DataView dv = new DataView(dt);
                dv.RowFilter = "tcdf_filetype='修改建議'"; // query example = "id = 10"
                GV_File.DataSource = dv;
                GV_File.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    private DataTable File_View(string tcdf_no)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");
            sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    private void File_Del(string tcdf_no)
    {
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_Del");
            sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
}