/*!
 * jQ<PERSON>y clueTip plugin v1.2.7
 *
 * Date: Sat Oct 13 17:17:47 2012 EDT
 * Requires: jQuery v1.3+
 *
 * Copyright 2012, <PERSON>
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 *
 *
 * Examples can be found at http://plugins.learningjquery.com/cluetip/demo/
 *
*/
(function(c){c.cluetip={version:"1.2.7",template:'<div><div class="cluetip-outer"><h3 class="cluetip-title ui-widget-header ui-cluetip-header"></h3><div class="cluetip-inner ui-widget-content ui-cluetip-content"></div></div><div class="cluetip-extra"></div></div>',setup:{insertionType:"appendTo",insertionElement:"body"},defaults:{multiple:false,width:275,height:"auto",cluezIndex:97,positionBy:"auto",topOffset:15,leftOffset:15,snapToEdge:false,local:false,localPrefix:null,localIdSuffix:null,hideLocal:true,
attribute:"rel",titleAttribute:"title",splitTitle:"",escapeTitle:false,showTitle:true,cluetipClass:"default",hoverClass:"",waitImage:true,cursor:"help",arrows:false,dropShadow:true,dropShadowSteps:6,sticky:false,mouseOutClose:false,delayedClose:50,activation:"hover",clickThrough:true,tracking:false,closePosition:"top",closeText:"Close",truncate:0,fx:{open:"show",openSpeed:""},hoverIntent:{sensitivity:3,interval:50,timeout:0},onActivate:function(){return true},onShow:function(){},onHide:function(){},
ajaxCache:true,ajaxProcess:function(m){return m=m.replace(/<(script|style|title)[^<]+<\/(script|style|title)>/gm,"").replace(/<(link|meta)[^>]+>/g,"")},ajaxSettings:{dataType:"html"},debug:false}};var E,P={},ma=0,T=0;c.fn.attrProp=c.fn.prop||c.fn.attr;c.fn.cluetip=function(m,q){function U(z,k){var l=z||"";k=k||"";if(typeof k=="object")c.each(k,function(j,d){l+="-"+j+"-"+d});else if(typeof k=="string")l+=k;return l}function V(z,k,l){l="";k=k.dropShadow&&k.dropShadowSteps?+k.dropShadowSteps:0;if(c.support.boxShadow){if(k)l=
"1px 1px "+k+"px rgba(0,0,0,0.5)";z.css(c.support.boxShadow,l);return false}l=z.find(".cluetip-drop-shadow");if(k==l.length)return l;l.remove();l=[];for(var j=0;j<k;)l[j++]='<div style="top:'+j+"px;left:"+j+'px;"></div>';return l=c(l.join("")).css({position:"absolute",backgroundColor:"#000",zIndex:W-1,opacity:0.1}).addClass("cluetip-drop-shadow").prependTo(z)}var e,h,I,w,J,ga;if(typeof m=="object"){q=m;m=null}if(m=="destroy"){var X=this.data("cluetip");if(X){c(X.selector).remove();c.removeData(this,
"title");c.removeData(this,"cluetip");c.removeData(this,"cluetipMoc")}c(document).unbind(".cluetip");return this.unbind(".cluetip")}q=c.extend(true,{},c.cluetip.defaults,q||{});ma++;var W;X=c.cluetip.backCompat||!q.multiple?"cluetip":"cluetip-"+ma;var ha="#"+X,A=c.cluetip.backCompat?"#":".",aa=c.cluetip.setup.insertionType,ra=c.cluetip.setup.insertionElement||"body";aa=/appendTo|prependTo|insertBefore|insertAfter/.test(aa)?aa:"appendTo";e=c(ha);if(!e.length){e=c(c.cluetip.template)[aa](ra).attr("id",
X).css({position:"absolute",display:"none"});W=+q.cluezIndex;I=e.find(A+"cluetip-outer").css({position:"relative",zIndex:W});h=e.find(A+"cluetip-inner");w=e.find(A+"cluetip-title");e.bind("mouseenter mouseleave",function(z){c(this).data("entered",z.type==="mouseenter")})}E=c("#cluetip-waitimage");if(!E.length&&q.waitImage){E=c("<div></div>").attr("id","cluetip-waitimage").css({position:"absolute"});E.insertBefore(e).hide()}var sa=(parseInt(e.css("paddingLeft"),10)||0)+(parseInt(e.css("paddingRight"),
10)||0);this.each(function(z){function k(){return false}function l(b,g){var f=b.status;g.beforeSend(b.xhr,g);if(f=="error")g[f](b.xhr,b.textStatus);else f=="success"&&g[f](b.data,b.textStatus,b.xhr);g.complete(b.xhr,g.textStatus)}var j=this,d=c(this),a=c.extend(true,{},q,c.metadata?d.metadata():c.meta?d.data():d.data("cluetip")||{}),K=false,Q=false,F=null,p=a[a.attribute]||(a.attribute=="href"?d.attr(a.attribute):d.attrProp(a.attribute)||d.attr(a.attribute)),Y=a.cluetipClass;W=+a.cluezIndex;d.data("cluetip",
{title:j.title,zIndex:W,selector:ha});a.arrows&&!e.find(".cluetip-arrows").length&&e.append('<div class="cluetip-arrows ui-state-default"></div>');if(!p&&!a.splitTitle&&!m)return true;if(a.local&&a.localPrefix)p=a.localPrefix+p;a.local&&a.hideLocal&&p&&c(p+":first").hide();var o=parseInt(a.topOffset,10),G=parseInt(a.leftOffset,10),t,ia,ba=isNaN(parseInt(a.height,10))?"auto":/\D/g.test(a.height)?a.height:a.height+"px",Z,ja,na,x,u,L,$,ka=parseInt(a.width,10)||275,r=ka+sa+a.dropShadowSteps,M=this.offsetWidth,
B,n,s,R,N,C=a.attribute!="title"?d.attr(a.titleAttribute)||"":"";if(a.splitTitle){N=C.split(a.splitTitle);C=a.showTitle||N[0]===""?N.shift():""}if(a.escapeTitle)C=C.replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;");d.bind("mouseenter mouseleave",function(b){var g=d.data("cluetip");g.entered=b.type==="entered";d.data("cluetip",g)});var da=function(b){var g,f;if(a.onActivate.call(j,b)===false)return false;Q=true;e=c(ha).css({position:"absolute"});I=e.find(A+"cluetip-outer");h=e.find(A+
"cluetip-inner");w=e.find(A+"cluetip-title");J=e.find(A+"cluetip-arrows");e.removeClass().css({width:ka});p==d.attr("href")&&d.css("cursor",a.cursor);a.hoverClass&&d.addClass(a.hoverClass);ja=x=d.offset().top;na=ja+d.innerHeight();B=d.offset().left;M=d.innerWidth();if(b.type==focus){s=B+M/2+G;e.css({left:n});L=x+o}else{s=b.pageX;L=b.pageY}if(j.tagName.toLowerCase()!="area"){Z=c(document).scrollTop();R=c(window).width()}if(a.positionBy=="fixed"){n=M+B+G;e.css({left:n})}else{n=M>B&&B>r||B+M+r+G>R?B-
r-G:M+B+G;if(j.tagName.toLowerCase()=="area"||a.positionBy=="mouse"||M+r>R)if(s+20+r>R){e.addClass("cluetip-"+Y);n=s-r-G>=0?s-r-G-parseInt(e.css("marginLeft"),10)+parseInt(h.css("marginRight"),10):s-r/2}else n=s+G;g=n<0?b.pageY+o:b.pageY;if(n<0||a.positionBy=="bottomTop"||a.positionBy=="topBottom")n=s+r/2>R?R/2-r/2:Math.max(s-r/2,0)}J.css({zIndex:d.data("cluetip").zIndex+1});e.css({left:n,zIndex:d.data("cluetip").zIndex});ia=c(window).height();if(m){if(typeof m=="function")m=m.call(j);h.html(m);S(g)}else if(N){b=
N.length;h.html(b?N[0]:"");if(b>1)for(var i=1;i<b;i++)h.append('<div class="split-body">'+N[i]+"</div>");S(g)}else if(!a.local&&p.indexOf("#")!==0)if(/\.(jpe?g|tiff?|gif|png)(?:\?.*)?$/i.test(p)){h.html('<img src="'+p+'" alt="'+C+'" />');S(g)}else{var v=a.ajaxSettings.beforeSend,ca=a.ajaxSettings.error,H=a.ajaxSettings.success,oa=a.ajaxSettings.complete;f=U(p,a.ajaxSettings.data);b=c.extend(true,{},a.ajaxSettings,{cache:a.ajaxCache,url:p,beforeSend:function(y,D){v&&v.call(j,y,e,h,D);I.children().empty();
a.waitImage&&E.css({top:L+20,left:s+20,zIndex:d.data("cluetip").zIndex-1}).show()},error:function(y,D){if(q.ajaxCache&&!P[f])P[f]={status:"error",textStatus:D,xhr:y};if(Q)ca?ca.call(j,y,D,e,h):h.html("<i>sorry, the contents could not be loaded</i>")},success:function(y,D,O){if(q.ajaxCache&&!P[f])P[f]={status:"success",data:y,textStatus:D,xhr:O};K=a.ajaxProcess.call(j,y);if(typeof K=="object"&&K!==null){C=K.title;K=K.content}if(Q){H&&H.call(j,y,D,e,h);h.html(K)}},complete:function(y,D){oa&&oa.call(j,
y,D,e,h);var O=h[0].getElementsByTagName("img");T=O.length;for(var la=0,ta=O.length;la<ta;la++)O[la].complete&&T--;if(T&&!c.browser.opera)c(O).bind("load.ct error.ct",function(){T--;if(T===0){E.hide();c(O).unbind(".ct");Q&&S(g)}});else{E.hide();Q&&S(g)}}});P[f]?l(P[f],b):c.ajax(b)}else if(a.local){b=c(p+(/^#\S+$/.test(p)?"":":eq("+z+")")).clone(true).show();a.localIdSuffix&&b.attr("id",b[0].id+a.localIdSuffix);h.html(b);S(g)}},S=function(b){var g,f;g=C||a.showTitle&&"&nbsp;";var i="";f="";var v=false;
v={bottom:function(H){H.appendTo(h)},top:function(H){H.prependTo(h)},title:function(H){H.prependTo(w)}};e.addClass("cluetip-"+Y);if(a.truncate){var ca=h.text().slice(0,a.truncate)+"...";h.html(ca)}g?w.show().html(g):w.hide();if(a.sticky){if(v[a.closePosition]){g=c('<div class="cluetip-close"><a href="#">'+a.closeText+"</a></div>");v[a.closePosition](g);g.bind("click.cluetip",function(){ea();return false})}if(a.mouseOutClose){d.unbind("mouseleave.cluetipMoc");e.unbind("mouseleave.cluetipMoc");if(a.mouseOutClose==
"both"||a.mouseOutClose=="cluetip"||a.mouseOutClose===true)e.bind("mouseleave.cluetipMoc",pa);if(a.mouseOutClose=="both"||a.mouseOutClose=="link")d.bind("mouseleave.cluetipMoc",pa)}}I.css({zIndex:d.data("cluetip").zIndex,overflow:ba=="auto"?"visible":"auto",height:ba});t=ba=="auto"?Math.max(e.outerHeight(),e.height()):parseInt(ba,10);u=x;$=Z+ia;v=n<s&&Math.max(n,0)+r>s;if(a.positionBy=="fixed")u=x-a.dropShadowSteps+o;else if(a.positionBy=="topBottom"||a.positionBy=="bottomTop"||v){if(a.positionBy==
"topBottom")f=x+t+o<$&&L-Z<t+o?"bottom":"top";else if(a.positionBy=="bottomTop"||v)f=x+t+o>$&&L-Z>t+o?"top":"bottom";if(a.snapToEdge)if(f=="top")u=ja-t-o;else{if(f=="bottom")u=na+o}else if(f=="top")u=L-t-o;else if(f=="bottom")u=L+o}else u=x+t+o>$?t>=ia?Z:$-t-o:d.css("display")=="block"||j.tagName.toLowerCase()=="area"||a.positionBy=="mouse"?b-o:x-a.dropShadowSteps;if(f==="")f=n<B?"left":"right";g=" clue-"+f+"-"+Y+" cluetip-"+Y;if(Y=="rounded")g+=" ui-corner-all";e.css({top:u+"px"}).attrProp({className:"cluetip ui-widget ui-widget-content ui-cluetip"+
g});if(a.arrows){if(/(left|right)/.test(f)){f=e.height()-J.height();i=n>=0&&b>0?x-u-a.dropShadowSteps:0;i=f>i?i:f;i+="px"}J.css({top:i}).show()}else J.hide();(ga=V(e,a))&&ga.length&&ga.hide().css({height:t,width:ka,zIndex:d.data("cluetip").zIndex-1}).show();F||e.hide();clearTimeout(F);F=null;e[a.fx.open](a.fx.openSpeed||0);c.fn.bgiframe&&e.bgiframe();a.onShow.call(j,e,h)},fa=function(){Q=false;E.hide();if(!a.sticky||/click|toggle/.test(a.activation))if(a.delayedClose>0){clearTimeout(F);F=null;F=setTimeout(ea,
a.delayedClose)}a.hoverClass&&d.removeClass(a.hoverClass)},ea=function(b){b=b&&b.data("cluetip")?b:d;var g=b.data("cluetip")&&b.data("cluetip").selector,f=c(g||"div.cluetip"),i=f.find(A+"cluetip-inner"),v=f.find(A+"cluetip-arrows");f.hide().removeClass();a.onHide.call(b[0],f,i);if(g){b.removeClass("cluetip-clicked");b.css("cursor","")}g&&C&&b.attrProp(a.titleAttribute,C);a.arrows&&v.css({top:""})},pa=function(){var b=this;clearTimeout(F);F=setTimeout(function(){var g=d.data("cluetip").entered,f=e.data("entered"),
i=false;if(a.mouseOutClose=="both"&&(g||f))i=true;else if((a.mouseOutClose===true||a.mouseOutClose=="cluetip")&&f)i=true;else if(a.mouseOutClose=="link"&&g)i=true;i||ea.call(b)},a.delayedClose)};c(document).unbind("hideCluetip.cluetip").bind("hideCluetip.cluetip",function(b){ea(c(b.target))});if(/click|toggle/.test(a.activation))d.bind("click.cluetip",function(b){if(e.is(":hidden")||!d.is(".cluetip-clicked")){da(b);c(".cluetip-clicked").removeClass("cluetip-clicked");d.addClass("cluetip-clicked")}else fa(b);
return false});else if(a.activation=="focus"){d.bind("focus.cluetip",function(b){d.attrProp("title","");da(b)});d.bind("blur.cluetip",function(b){d.attrProp("title",d.data("cluetip").title);fa(b)})}else{d[a.clickThrough?"unbind":"bind"]("click.cluetip",k);var qa=function(b){if(a.tracking){var g=n-b.pageX,f=u?u-b.pageY:x-b.pageY;d.bind("mousemove.cluetip",function(i){e.css({left:i.pageX+g,top:i.pageY+f})})}};c.fn.hoverIntent&&a.hoverIntent?d.hoverIntent({sensitivity:a.hoverIntent.sensitivity,interval:a.hoverIntent.interval,
over:function(b){da(b);qa(b)},timeout:a.hoverIntent.timeout,out:function(b){fa(b);d.unbind("mousemove.cluetip")}}):d.bind("mouseenter.cluetip",function(b){da(b);qa(b)}).bind("mouseleave.cluetip",function(b){fa(b);d.unbind("mousemove.cluetip")});d.bind("mouseover.cluetip",function(){d.attrProp("title","")}).bind("mouseleave.cluetip",function(){d.attrProp("title",d.data("cluetip").title)})}});return this};(function(){c.support=c.support||{};for(var m=document.createElement("div").style,q=["boxShadow"],
U=["moz","Moz","webkit","o"],V=0,e=q.length;V<e;V++){var h=q[V],I=h.charAt(0).toUpperCase()+h.slice(1);if(typeof m[h]!=="undefined")c.support[h]=h;else for(var w=0,J=U.length;w<J;w++)if(typeof m[U[w]+I]!=="undefined"){c.support[h]=U[w]+I;break}}})();c.fn.cluetip.defaults=c.cluetip.defaults})(jQuery);
