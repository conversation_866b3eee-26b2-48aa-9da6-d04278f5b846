﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_ECP_print : Treaty.common
{

    public List<SqlParameter> sqlParamList = new List<SqlParameter>();
    public string Ecp_guid
    {
        set { ViewState["Ecp_guid"] = value; }
        get
        {
            if (ViewState["Ecp_guid"] == null)
            {
                string guid = Request.QueryString["guid"];
                if (string.IsNullOrEmpty(guid) || !Check_calendarID(guid))
                {
                    Response.Redirect("../danger.aspx");
                }
                ViewState["Ecp_guid"] = oRCM.SQLInjectionReplaceAll(guid);
            }

            return ViewState["Ecp_guid"].ToString();
        }
    }
    //public string Seno
    //{
    //    set { ViewState["seno"] = value; }
    //    get
    //    {
    //        if (ViewState["seno"] == null)
    //        {
    //            string seno = Request.QueryString["seno"];
    //            if (string.IsNullOrEmpty(seno) || !IsNumber(seno))
    //            {
    //                Response.Redirect("../error.aspx");
    //            }
    //            ViewState["seno"] = oRCM.SQLInjectionReplaceAll(seno);
    //        }
    //        return ViewState["seno"].ToString();
    //    }
    //}
    //public string Ver
    //{
    //    set { ViewState["ver"] = value; }
    //    get
    //    {
    //        if (ViewState["ver"] == null)
    //        {
    //            string ver = Request.QueryString["ver"];
    //            if (string.IsNullOrEmpty(ver) || !IsNumber(ver))
    //            {
    //                Response.Redirect("../error.aspx");
    //            }
    //            ViewState["ver"] = oRCM.SQLInjectionReplaceAll(ver);
    //        }
    //        return ViewState["ver"].ToString();

    //    }
    //}
    //public static bool IsNumber(string strNumber)
    //{
    //    System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
    //    return r.IsMatch(strNumber);
    //}
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            BindData();
            // 確保在每次頁面加載時都註冊 LinkButton 控件           
            ScriptManager scriptManager = ScriptManager.GetCurrent(this.Page);
            scriptManager.RegisterPostBackControl(gv_data);
        }
    }


    public DataSet GetData()
    {
        DataTable dt = new DataTable();

        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("@signGUID", oRCM.SQLInjectionReplaceAll(Ecp_guid)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("View_RP_content")));
        dt = get_SP();
        dt.TableName = "原因"; // 設定名稱
        DataTable dataTable1Copy = dt.Copy(); // 創建副本


        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("@signGUID", oRCM.SQLInjectionReplaceAll(Ecp_guid)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("View_RP_sign")));
        dt.Clear();
        dt = get_SP();
        dt.TableName = "myTable";
        DataTable dataTable2Copy = dt.Copy();

        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("@signGUID", oRCM.SQLInjectionReplaceAll(Ecp_guid)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("View_RP_file")));
        dt.Clear();
        dt = get_SP();
        dt.TableName = "檔案";
        DataTable dataTable3Copy = dt.Copy();

        DataSet dataSource = new DataSet();
        dataSource.Tables.Add(dataTable1Copy);
        dataSource.Tables.Add(dataTable2Copy);
        dataSource.Tables.Add(dataTable3Copy);

        return dataSource;
    }


    private void BindData()
    {
        if (Html())
        {
            DataSet dataSource = GetData();
            DataTable dt = new DataTable();
            dt = dataSource.Tables[0];
            if (dt.Rows.Count > 0)
            {
                txt_reason.Text = dt.Rows[0]["原因說明"].ToString().Trim();
            }
            dt.Clear();
            dt = dataSource.Tables[1];
            if (dt.Rows.Count > 0)
            {
                gvList_signflow.DataSource = dt;
                gvList_signflow.DataBind();

            }
            dt.Clear();
            dt = dataSource.Tables[2];
            if (dt.Rows.Count > 0)
            {
                gv_data.DataSource = dt;
                gv_data.DataBind();

            }
        }
    }

    protected void gvList_signflow_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowIndex < 0)
            return;
        //簽核人員        
        Label lbl_Empno1 = (Label)e.Row.FindControl("lbl_Empno1");
        lbl_Empno1.Text = string.Format("{0}({1})", DataBinder.Eval(e.Row.DataItem, "簽核人"), DataBinder.Eval(e.Row.DataItem, "簽核人工號"));
    }

    protected void gv_data_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string args = e.CommandArgument.ToString();
            if (!string.IsNullOrEmpty(args))
            {
                DownloadFile(args);
            }
            else
            {
                string script = string.Format("alert('檔案不存在！');");
                ScriptManager.RegisterStartupScript(this, this.GetType(), "downloadFile", script, true);

            }
        }
    }

    protected void btnPrint_Click(object sender, EventArgs e)
    {
        if (Html())
        {
            DataSet DataSet = GetData();
            ExportPdfFile(DataSet);
        }
    }


    //套用設定好word樣板
    private void ExportPdfFile(DataSet dataSource)
    {

        //Tuple<string, string> outputFileNames = this.RetrievePdfOutputFileNames();
        string sampleFileName = "境外實施自主管理表簽核.docx";
        string pdfFileName = "境外實施自主管理表簽核.pdf";
        Treaty_report.asposeWord word = new Treaty_report.asposeWord(sampleFileName);
        Aspose.Words.DocumentBuilder documentBuilder = word.getDocumentBuilder();
        Aspose.Words.Document document = word.getDocument();
        DateTime dt = DateTime.Now;
        documentBuilder.MoveToMergeField("印表日期");
        documentBuilder.Write(dt.ToShortDateString().ToString());
        documentBuilder.MoveToMergeField("原因說明");
        documentBuilder.Write(txt_reason.Text);

        DataTable dt原因 = dataSource.Tables[0];
        documentBuilder.MoveToMergeField("需求單位");
        documentBuilder.Write(dt原因.Rows[0]["需求單位"].ToString());
        documentBuilder.MoveToMergeField("需求部門");
        documentBuilder.Write(dt原因.Rows[0]["需求部門"].ToString());
        documentBuilder.MoveToMergeField("單位承辦人");
        documentBuilder.Write(dt原因.Rows[0]["單位承辦人"].ToString());
        documentBuilder.MoveToMergeField("分機");
        documentBuilder.Write(dt原因.Rows[0]["分機"].ToString());
        documentBuilder.MoveToMergeField("案名");
        documentBuilder.Write(dt原因.Rows[0]["案名"].ToString());
        documentBuilder.MoveToMergeField("簽約對象");
        documentBuilder.Write(dt原因.Rows[0]["簽約對象"].ToString());

        documentBuilder.MoveToDocumentStart();

        document.MailMerge.ExecuteWithRegions(dataSource);

        this.ExportPDF(pdfFileName, word.exportPDF());
    }


    private void ExportPDF(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);
        Response.Clear();
        Response.ContentType = this.RetrieveFileContentType(fileExtension);
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");
        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);
        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }


    /// <summary>
    /// 取得檔案類型
    /// </summary>
    /// <param name="fileExtension">副檔名</param>
    private string RetrieveFileContentType(string fileExtension)
    {
        if (fileExtension.Equals(".docx"))
            return "Application/msword";
        else if (fileExtension.Equals(".xlsx"))
            return "Application/vnd.ms-excel";
        else if (fileExtension.Equals(".pdf"))
            return "Application/pdf";
        return string.Empty;
    }


    private void DownloadFile(string tcdf_no)
    {      
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("file_view")));
        DataTable dt = new DataTable();
        dt = get_SP_File();
        if (dt.Rows.Count > 0)
        {
            string filePath = dt.Rows[0]["tcdf_url"].ToString().Trim();
            if (System.IO.File.Exists(filePath))
            {
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(dt.Rows[0]["tcdf_doc"].ToString().Trim(), Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(filePath)));
                Response.Flush();
                Response.End();
            }
            else
            {
                Response.End();
            }
        }
    }

    public DataTable get_SP_File()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_treaty_TechCase_modify";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));

        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;

    }

    public DataTable get_SP()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }

    public void do_Edit()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();

        SqlCommand oCmd = new SqlCommand();
        //動態設定SqlParameters
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());


        runScalar(oCmd);
    }

    public bool Html()
    {

        string errUrl = "../danger.aspx";
        #region 特殊字元判斷-有MasterPage
        foreach (Control ctrl in Page.Form.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is DropDownList)
                {
                    DropDownList objTextBox = (DropDownList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is RadioButtonList)
                {
                    RadioButtonList objTextBox = (RadioButtonList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is TextBox)
                {
                    TextBox objTextBox = (TextBox)c;
                    if ((Base64.danger_word(objTextBox.Text)) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }
            }
        }
        #endregion

        return true;
    }
}