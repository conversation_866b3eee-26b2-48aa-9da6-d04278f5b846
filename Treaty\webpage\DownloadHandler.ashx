﻿<%@ WebHandler Language="C#" Class="DownloadHandler" %>

using System;
using System.Web;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
public class DownloadHandler : IHttpHandler
{

    private SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
    private RemoveCheckMax oRCM = new RemoveCheckMax();
    List<SqlParameter> sqlParamList = new List<SqlParameter>();
    //string UpLoadPath = System.Configuration.ConfigurationManager.AppSettings["FilePathString"];
    public void ProcessRequest(HttpContext context)
    {

        string Sseno = context.Request["seno"] != null ? context.Request["seno"].ToString() : "";

        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(Sseno)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("file_view")));
        DataTable dt = new DataTable();
        dt = get_SP();
        if (dt.Rows.Count > 0)
        {
            //string filePath = string.Format("{0}\\{1}", UpLoadPath, dt.Rows[0]["tcdf_doc"].ToString().Trim());
            string filePath = dt.Rows[0]["tcdf_url"].ToString().Trim();
            if (System.IO.File.Exists(filePath))
            {

                context.Response.Clear();
                context.Response.AppendHeader("Content-Disposition", "attachment; filename=" + dt.Rows[0]["tcdf_doc"].ToString().Trim());
                context.Response.WriteFile(filePath);
                context.Response.Flush();
                context.Response.End();
            }
            else
            {
                context.Response.End();
            }
        }
        else
        {
            context.Response.Write("<br /><br />檔案不存在！");            
        }
    }


    public DataTable get_SP()
    {
        Treaty.common com = new Treaty.common();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_treaty_TechCase_modify";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());
        dt = com.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }

    public bool IsReusable
    {
        get
        {
            return false;
        }
    }

}