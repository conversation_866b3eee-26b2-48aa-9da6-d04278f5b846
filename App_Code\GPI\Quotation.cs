﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Quotation 的摘要描述
    /// </summary>
    public class Quotation : GPI.mySQLHelper
    {
        #region  私有變數
        private string _errorMessage;

        private int _seqsn;
        private string _planno;
        private int _ver;
        private string _gpino;

        //檢核檔
        private string chk_guid;
        private string chk_process;
        private string chk_process_memo;
        private int chk_tc_seno;
        private string chk_printdate;
        private string chk_no1;
        private string chk_no1_reason;
        private string chk_no1_out;
        private string chk_no2;
        private string chk_no2_reason;
        private string chk_no31;
        private string chk_no31_reason;
        private string chk_no4;
        private string chk_no4_reason;
        private string chk_no5;
        private string chk_no5_reason;
        private string chk_no5_org;
        private int chk_no5_amt;
        private string chk_no6;
        private string chk_no7;
        private string chk_no7_org;
        private string chk_no8;
        private int chk_no51_pert;
        private string chk_no51_item;
        private string chk_no51_reason;
        private string chk_no51_desc;
        private string chk_no52;
        private string chk_no53;
        private string chk_no53_reason;
        private string chk_no53_7_item;
        private string chk_no32;
        private string chk_outplan;
        private string chk_outplan_res;
        private string chk_no53_ver;
        private string chk_src_confirm;
        private string chk_src_modify_memo;
        private string chk_reason;
        private string chk_risk;
        private string chk_import;
        private string chk_memo;

        //檢核問卷檔
        private string sply_quno;
        private string sply_quname;
        private string sply_no;
        private string sply_qurate;

        //簽核
        private string parentGUID;
        private string formNo;
        private int seq;
        private string actRoleName;
        private string recUserID;
        private string signType;
        private string signSigle;
        private string noticeEmpno;
        private string signClass;
        private string iS_LOCK;

        private string _keyinempno;
        private string _keyinempname;
        private string _keyindate;
        private string _modempno;
        private string _modempname;
        private string _moddate;
        private string _modify;
        private string _delempno;
        private string _delempname;
        private string _delete;


        #endregion

        #region  建構子
        public Quotation()
        {
            _errorMessage = String.Empty;

            _seqsn = 0;
            _planno = String.Empty;
            _gpino = String.Empty;
            _ver = 0;

            //檢核檔
            chk_guid = String.Empty;
            chk_process = String.Empty;
            chk_process_memo = String.Empty;
            chk_tc_seno = 0;
            chk_printdate = String.Empty;
            chk_no1 = String.Empty;
            chk_no1_reason = String.Empty;
            chk_no1_out = String.Empty;
            chk_no2 = String.Empty;
            chk_no2_reason = String.Empty;
            chk_no31 = String.Empty;
            chk_no31_reason = String.Empty;
            chk_no4 = String.Empty;
            chk_no4_reason = String.Empty;
            chk_no5 = String.Empty;
            chk_no5_reason = String.Empty;
            chk_no5_org = String.Empty;
            chk_no5_amt = 0;
            chk_no6 = String.Empty;
            chk_no7 = String.Empty;
            chk_no7_org = String.Empty;
            chk_no8 = String.Empty;
            chk_no51_pert = 0;
            chk_no51_item = String.Empty;
            chk_no51_reason = String.Empty;
            chk_no51_desc = String.Empty;
            chk_no52 = String.Empty;
            chk_no53 = String.Empty;
            chk_no53_reason = String.Empty;
            chk_no53_7_item = String.Empty;
            chk_no32 = String.Empty;
            chk_outplan = String.Empty;
            chk_outplan_res = String.Empty;
            chk_no53_ver = String.Empty;
            chk_src_confirm = String.Empty;
            chk_src_modify_memo = String.Empty;
            chk_reason = String.Empty;
            chk_risk = String.Empty;
            chk_import = String.Empty;
            chk_memo = String.Empty;

            //檢核問卷檔
            sply_quno = String.Empty;
            sply_quname = String.Empty;
            sply_no = String.Empty;
            sply_qurate = String.Empty;

            //簽核
            parentGUID = String.Empty;
            formNo = String.Empty;
            seq = 0;
            actRoleName = String.Empty;
            recUserID = String.Empty;
            signType = String.Empty;
            signSigle = String.Empty;
            noticeEmpno = String.Empty;
            signClass = String.Empty;
            iS_LOCK = String.Empty;

            _keyinempno = String.Empty;
            _keyinempname = String.Empty;
            _keyindate = String.Empty;
            _modempno = String.Empty;
            _modempname = String.Empty;
            _moddate = String.Empty;
            _modify = String.Empty;
            _delempno = String.Empty;
            _delempname = String.Empty;
            _delete = String.Empty;

        }
        #endregion

        #region  公有屬性
        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 標案流水號
        /// </summary>
        public int Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }
        /// <summary>
        /// 標案案號
        /// </summary>
        public string Planno
        {
            get { return _planno; }
            set { _planno = value; }
        }
        /// <summary>
        /// 規劃案號
        /// </summary>
        public string Gpino
        {
            get { return _gpino; }
            set { _gpino = value; }
        }

        /// <summary>
        /// 版本
        /// </summary>
        public int Ver
        {
            get { return _ver; }
            set { _ver = value; }
        }

        #region 檢核檔
        /// <summary>
        /// 表單Guid
        /// </summary>
        public string Chk_guid
        {
            get { return chk_guid; }
            set { chk_guid = value; }
        }
        /// <summary>
        /// 單位處理情形
        /// </summary>
        public string Chk_process
        {
            get { return chk_process; }
            set { chk_process = value; }
        }
        /// <summary>
        /// 單位說明
        /// </summary>
        public string Chk_process_memo
        {
            get { return chk_process_memo; }
            set { chk_process_memo = value; }
        }
        /// <summary>
        /// 草約編號流水號
        /// </summary>
        public int Chk_tc_seno
        {
            get { return chk_tc_seno; }
            set { chk_tc_seno = value; }
        }

        /// <summary>
        /// 檢核表列印日期
        /// </summary>
        public string Chk_printdate
        {
            get { return chk_printdate; }
            set { chk_printdate = value; }
        }
        /// <summary>
        /// 屬國內業者未能立即銜接之相關技術或服務計畫
        /// </summary>
        public string Chk_no1
        {
            get { return chk_no1; }
            set { chk_no1 = value; }
        }
        /// <summary>
        /// 原因說明
        /// </summary>
        public string Chk_no1_reason
        {
            get { return chk_no1_reason; }
            set { chk_no1_reason = value; }
        }

        /// <summary>
        /// 且本案本院已執行?年
        /// </summary>
        public string Chk_no1_out
        {
            get { return chk_no1_out; }
            set { chk_no1_out = value; }
        }
        /// <summary>
        /// 屬政府重要施政計畫
        /// </summary>
        public string Chk_no2
        {
            get { return chk_no2; }
            set { chk_no2 = value; }
        }
        /// <summary>
        /// 原因說明
        /// </summary>
        public string Chk_no2_reason
        {
            get { return chk_no2_reason; }
            set { chk_no2_reason = value; }
        }
        /// <summary>
        /// 執行本案對本院技術是否有提升之效果
        /// </summary>
        public string Chk_no31
        {
            get { return chk_no31; }
            set { chk_no31 = value; }
        }
        /// <summary>
        /// 提升之效果原因
        /// </summary>
        public string Chk_no31_reason
        {
            get { return chk_no31_reason; }
            set { chk_no31_reason = value; }
        }
        /// <summary>
        /// 招標文件(含契約)標示主要部份或應自行履行之部分委外執行
        /// </summary>
        public string Chk_no4
        {
            get { return chk_no4; }
            set { chk_no4 = value; }
        }
        /// <summary>
        /// 委外執行原因
        /// </summary>
        public string Chk_no4_reason
        {
            get { return chk_no4_reason; }
            set { chk_no4_reason = value; }
        }
        /// <summary>
        /// 屬規劃案件，可能影響本院承接後續執行工作
        /// </summary>
        public string Chk_no5
        {
            get { return chk_no5; }
            set { chk_no5 = value; }
        }
        /// <summary>
        /// 不影響的原因
        /// </summary>
        public string Chk_no5_reason
        {
            get { return chk_no5_reason; }
            set { chk_no5_reason = value; }
        }
        /// <summary>
        /// 可能影響單位
        /// </summary>
        public string Chk_no5_org
        {
            get { return chk_no5_org; }
            set { chk_no5_org = value; }
        }
        /// <summary>
        /// 預估影響金額
        /// </summary>
        public int Chk_no5_amt
        {
            get { return chk_no5_amt; }
            set { chk_no5_amt = value; }
        }
        /// <summary>
        /// 院內有潛在競爭單位
        /// </summary>
        public string Chk_no6
        {
            get { return chk_no6; }
            set { chk_no6 = value; }
        }
        /// <summary>
        /// 為免造成「與民爭利」困擾，請先評估國內是否有執行本標案能力之單位參與競標
        /// </summary>
        public string Chk_no7
        {
            get { return chk_no7; }
            set { chk_no7 = value; }
        }
        /// <summary>
        /// 其他不會「與民爭利」之法人機構、學校或協會
        /// </summary>
        public string Chk_no7_org
        {
            get { return chk_no7_org; }
            set { chk_no7_org = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Chk_no8
        {
            get { return chk_no8; }
            set { chk_no8 = value; }
        }
        /// <summary>
        /// 分包比例
        /// </summary>
        public int Chk_no51_pert
        {
            get { return chk_no51_pert; }
            set { chk_no51_pert = value; }
        }
        /// <summary>
        /// 分包對象
        /// </summary>
        public string Chk_no51_item
        {
            get { return chk_no51_item; }
            set { chk_no51_item = value; }
        }
        /// <summary>
        /// 分包原因
        /// </summary>
        public string Chk_no51_reason
        {
            get { return chk_no51_reason; }
            set { chk_no51_reason = value; }
        }
        /// <summary>
        /// 分包工作項目
        /// </summary>
        public string Chk_no51_desc
        {
            get { return chk_no51_desc; }
            set { chk_no51_desc = value; }
        }
        /// <summary>
        /// 院內有潛在競爭單位
        /// </summary>
        public string Chk_no52
        {
            get { return chk_no52; }
            set { chk_no52 = value; }
        }
        /// <summary>
        /// 是否屬應報院核定之特殊案件
        /// </summary>
        public string Chk_no53
        {
            get { return chk_no53; }
            set { chk_no53 = value; }
        }
        /// <summary>
        /// 特殊案件的原因
        /// </summary>
        public string Chk_no53_reason
        {
            get { return chk_no53_reason; }
            set { chk_no53_reason = value; }
        }
        /// <summary>
        /// 契約含下列條款之保密條款
        /// </summary>
        public string Chk_no53_7_item
        {
            get { return chk_no53_7_item; }
            set { chk_no53_7_item = value; }
        }
        /// <summary>
        /// 本案是否列為策略性退出計畫
        /// </summary>
        public string Chk_outplan
        {
            get { return chk_outplan; }
            set { chk_outplan = value; }
        }
        /// <summary>
        /// 原因說明
        /// </summary>
        public string Chk_outplan_res
        {
            get { return chk_outplan_res; }
            set { chk_outplan_res = value; }
        }
        /// <summary>
        /// 報院版本
        /// </summary>
        public string Chk_no53_ver
        {
            get { return chk_no53_ver; }
            set { chk_no53_ver = value; }
        }
        /// <summary>
        /// 報院條件確認 
        /// </summary>
        public string Chk_src_confirm
        {
            get { return chk_src_confirm; }
            set { chk_src_confirm = value; }
        }
        /// <summary>
        /// 調整報院條件說明
        /// </summary>
        public string Chk_src_modify_memo
        {
            get { return chk_src_modify_memo; }
            set { chk_src_modify_memo = value; }
        }
        /// <summary>
        /// 投標之必要理由
        /// </summary>
        public string Chk_reason
        {
            get { return chk_reason; }
            set { chk_reason = value; }
        }
        /// <summary>
        /// 評估執行風險 
        /// </summary>
        public string Chk_risk
        {
            get { return chk_risk; }
            set { chk_risk = value; }
        }
        /// <summary>
        /// 承接此案之重要性及意義
        /// </summary>
        public string Chk_import
        {
            get { return chk_import; }
            set { chk_import = value; }
        }
        /// <summary>
        /// 簽辦說明
        /// </summary>
        public string Chk_memo
        {
            get { return chk_memo; }
            set { chk_memo = value; }
        }

        #endregion

        #region 檢核問卷檔
        /// <summary>
        /// 題目
        /// </summary>
        public string Sply_quno
        {
            get { return sply_quno; }
            set { sply_quno = value; }
        }
        /// <summary>
        /// 廠商名稱
        /// </summary>
        public string Sply_quname
        {
            get { return sply_quname; }
            set { sply_quname = value; }
        }

        /// <summary>
        /// 客戶代碼
        /// </summary>
        public string Sply_no
        {
            get { return sply_no; }
            set { sply_no = value; }
        }

        /// <summary>
        /// 占契約金額比例
        /// </summary>
        public string Sply_qurate
        {
            get { return sply_qurate; }
            set { sply_qurate = value; }
        }

        #endregion

        #region 簽核
        /// <summary>
        /// 
        /// </summary>
        public string PARENTGUID
        {
            get { return parentGUID; }
            set { parentGUID = value; }
        }

        /// <summary>
        /// 表單類型
        /// </summary>
        public string FormNo
        {
            get { return formNo; }
            set { formNo = value; }
        }

        /// <summary>
        /// 簽辦順序
        /// </summary>
        public int Seq
        {
            get { return seq; }
            set { seq = value; }
        }

        /// <summary>
        /// 簽辦角色
        /// </summary>
        public string ActRoleName
        {
            get { return actRoleName; }
            set { actRoleName = value; }
        }

        /// <summary>
        /// 工號
        /// </summary>
        public string RecUserID
        {
            get { return recUserID; }
            set { recUserID = value; }
        }

        /// <summary>
        /// 預設=1，依序簽核，不可變更
        /// </summary>
        public string SignType
        {
            get { return signType; }
            set { signType = value; }
        }

        /// <summary>
        /// 預設=Y，單一簽核，不可變更
        /// </summary>
        public string SignSigle
        {
            get { return signSigle; }
            set { signSigle = value; }
        }

        /// <summary>
        /// 通知人員
        /// </summary>
        public string NoticeEmpno
        {
            get { return noticeEmpno; }
            set { noticeEmpno = value; }
        }

        /// <summary>
        /// 簽核類別
        /// </summary>
        public string SignClass
        {
            get { return signClass; }
            set { signClass = value; }
        }

        /// <summary>
        /// 是否可以變更(Y:不可/N:可)
        /// </summary>
        public string IS_LOCK
        {
            get { return iS_LOCK; }
            set { iS_LOCK = value; }
        }

        #endregion

        /// <summary>
        /// 填寫人工號
        /// </summary>
        public string KeyinEmpNo
        {
            get { return _keyinempno; }
            set { _keyinempno = value; }
        }
        /// <summary>
        /// 填寫人姓名
        /// </summary>
        public string KeyinEmpName
        {
            get { return _keyinempname; }
            set { _keyinempname = value; }
        }
        /// <summary>
        /// 填寫日期
        /// </summary>
        public string KeyinDate
        {
            get { return _keyindate; }
            set { _keyindate = value; }
        }
        /// <summary>
        /// 修改人工號
        /// </summary>
        public string ModEmpNo
        {
            get { return _modempno; }
            set { _modempno = value; }
        }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModEmpName
        {
            get { return _modempname; }
            set { _modempname = value; }
        }
        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModDate
        {
            get { return _moddate; }
            set { _moddate = value; }
        }
        /// <summary>
        /// 是否可修改Y. 是 N. 否, Default:Y
        /// </summary>
        public string Modify
        {
            get { return _modify; }
            set { _modify = value; }
        }
        /// <summary>
        /// 刪除人工號
        /// </summary>
        public string DelEmpNo
        {
            get { return _delempno; }
            set { _delempno = value; }
        }
        /// <summary>
        /// 刪除人姓名
        /// </summary>
        public string DelEmpName
        {
            get { return _delempname; }
            set { _delempname = value; }
        }
        /// <summary>
        /// 刪除註記(Default:N)
        /// </summary>
        public string Main_Delete
        {
            get { return _delete; }
            set { _delete = value; }
        }

        #endregion

        #region  公有函式
        /// <summary>
        /// 取得明細
        /// </summary>
        /// <returns></returns>
        public DataTable GetDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@ver", _ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// 取得規劃構想筆數
        /// </summary>
        /// <returns></returns>
        public int Get_design()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select count(plan_seqsn) as plan_count from gpi_plan where plan_seqsn = @seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            return int.Parse(this.getTopOne(oCmd, CommandType.Text));
        }
        /// <summary>
        /// 取得是否有成本構想資料可列印投標檢核表
        /// </summary>
        /// <returns></returns>
        public bool Get_cost()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_cost_null";

            oCmd.Parameters.AddWithValue("@seqno", _seqsn);
            SqlParameter rtn_code = oCmd.Parameters.Add("@rtn_code", SqlDbType.VarChar, 5);
            rtn_code.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_code.Value.ToString() == "2";
        }
        /// <summary>
        /// 取得契約需求申請訊息
        /// </summary>
        /// <returns></returns>
        public DataTable GetTreatyMsg()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_check_treaty_apply";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得議約狀態
        /// </summary>
        /// <returns></returns>
        public DataTable Get_treaty_ANMR_request()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"esp_treaty_ANMR_request";

            oCmd.Parameters.AddWithValue("@contno", _gpino);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        #region 各單位檢查事項
        /// <summary>
        /// 取得分包商廠資料
        /// </summary>
        /// <returns></returns>
        public DataTable GetSply()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT sply_id,sply_seqsn,sply_quno,sply_no,sply_quname,sply_qurate
FROM engagedb.dbo.gpi_sply 
WHERE sply_seqsn = @main_seqsn AND sply_quno = @sply_quno
";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@sply_quno", sply_quno);

            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.CommandText.Replace("engagedb", "engage_his");
                oCmd.CommandText += " AND sply_ver = @ver ";
                oCmd.Parameters.AddWithValue("@ver", _ver);
            }


            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 取得分包商廠資料
        /// </summary>
        /// <returns></returns>
        public DataTable GetSply51()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sply_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@main_ver", _ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// 新增/編輯一筆分包廠商資料
        /// </summary>
        /// <returns></returns>
        public bool Update_Sply()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_customer_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@sply_quno", sply_quno);
            oCmd.Parameters.AddWithValue("@sply_quname", sply_quname);
            oCmd.Parameters.AddWithValue("@sply_no", sply_no);
            oCmd.Parameters.AddWithValue("@sply_qurate", sply_qurate);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        /// <summary>
        /// 刪除一筆分包廠商資料
        /// </summary>
        /// <returns></returns>
        public bool Delete_Sply()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
Delete gpi_sply 
where sply_seqsn = @main_seqsn and sply_no = @sply_no and sply_quno = @sply_quno
";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@sply_quno", sply_quno);
            oCmd.Parameters.AddWithValue("@sply_no", sply_no);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        /// <summary>
        /// 取得設定的報院條件
        /// </summary>
        /// <returns></returns>
        public DataTable Get_reason53()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chk_reason53";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得設定的保密條款
        /// </summary>
        /// <returns></returns>
        public DataTable Get_reason53_7()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chk_reason53_7";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得設定的報院條件
        /// </summary>
        /// <returns></returns>
        public DataTable Get_sign_sRC_DSP()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sign_sRC_DSP";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@chk_ver", _ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// 取得填寫狀況
        /// </summary>
        /// <returns></returns>
        public DataTable Get_chkdata_check()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_check";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 更新各單位檢查事項
        /// </summary>
        /// <returns></returns>
        public bool Update_chkdata()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@chk_printdate", chk_printdate);
            oCmd.Parameters.AddWithValue("@chk_no1", chk_no1);
            oCmd.Parameters.AddWithValue("@chk_no1_reason", chk_no1_reason);
            oCmd.Parameters.AddWithValue("@chk_receive", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no1_out", chk_no1_out);
            oCmd.Parameters.AddWithValue("@chk_no2", chk_no2);
            oCmd.Parameters.AddWithValue("@chk_no2_reason", chk_no2_reason);
            oCmd.Parameters.AddWithValue("@chk_no3", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no3_out", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no3_reason", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no31", chk_no31);
            oCmd.Parameters.AddWithValue("@chk_no31_reason", chk_no31_reason);
            oCmd.Parameters.AddWithValue("@chk_no4", chk_no4);
            oCmd.Parameters.AddWithValue("@chk_no4_reason", chk_no4_reason);
            oCmd.Parameters.AddWithValue("@chk_no5", chk_no5);
            oCmd.Parameters.AddWithValue("@chk_no5_reason", chk_no5_reason);
            oCmd.Parameters.AddWithValue("@chk_no5_org", chk_no5_org);
            oCmd.Parameters.AddWithValue("@chk_no5_amt", chk_no5_amt);
            oCmd.Parameters.AddWithValue("@chk_no6", chk_no6);
            oCmd.Parameters.AddWithValue("@chk_no7", chk_no7);
            oCmd.Parameters.AddWithValue("@chk_no7_org", chk_no7_org);
            oCmd.Parameters.AddWithValue("@chk_no8", chk_no8);
            oCmd.Parameters.AddWithValue("@chk_no51_pert", chk_no51_pert);
            oCmd.Parameters.AddWithValue("@chk_no51_item", chk_no51_item);
            oCmd.Parameters.AddWithValue("@chk_no51_reason", chk_no51_reason);
            oCmd.Parameters.AddWithValue("@chk_no51_desc", chk_no51_desc);
            oCmd.Parameters.AddWithValue("@chk_no52", chk_no52);
            oCmd.Parameters.AddWithValue("@chk_no53", chk_no53);
            oCmd.Parameters.AddWithValue("@chk_no53_reason", chk_no53_reason);
            oCmd.Parameters.AddWithValue("@chk_no53_7_item", chk_no53_7_item);
            oCmd.Parameters.AddWithValue("@chk_modempno", _modempno);
            oCmd.Parameters.AddWithValue("@chk_no32", chk_no32);
            oCmd.Parameters.AddWithValue("@chk_no32_1", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no32_2", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_no32_3", string.Empty);
            oCmd.Parameters.AddWithValue("@chk_outplan", chk_outplan);
            oCmd.Parameters.AddWithValue("@chk_outplan_res", chk_outplan_res);
            oCmd.Parameters.AddWithValue("@chk_no53_ver", chk_no53_ver);
            oCmd.Parameters.AddWithValue("@chk_src_confirm", chk_src_confirm);
            oCmd.Parameters.AddWithValue("@chk_src_modify_memo", chk_src_modify_memo);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        /// <summary>
        /// 檢查是否已填寫「契約需求申請」
        /// </summary>
        /// <returns></returns>
        public string Get_treaty_exists()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_treaty_exists_qry";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            SqlParameter exists = oCmd.Parameters.Add("@exists", SqlDbType.VarChar, 1);
            exists.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return exists.Value.ToString();
        }
        /// <summary>
        /// 檢查是否報院核定之特殊案件 
        /// </summary>
        /// <returns></returns>
        public string Get_chk_no53()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chk_no53";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            return this.getTopOne(oCmd, CommandType.StoredProcedure);
        }

        /// <summary>
        /// 新增報院條件
        /// </summary>
        /// <returns></returns>
        public bool Insert_sign_sRC(string src_val)
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sign_sRC_insert";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ess_ver", _ver);//標案版本
            oCmd.Parameters.AddWithValue("@ess_src_ver", chk_no53_ver);//報院版本
            oCmd.Parameters.AddWithValue("@ess_src_val", src_val);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 刪除報院條件
        /// </summary>
        /// <returns></returns>
        public bool Delete_sign_sRC()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sign_sRC_delete";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ess_ver", _ver);//標案版本

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 取得報為版本
        /// </summary>
        /// <returns></returns>
        public string GetTcsVer()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT MAX(tcs_ver) FROM v_gpi_no53
";

            string ver = this.getTopOne(oCmd, CommandType.Text);
            return ver;
        }
        /// <summary>
        /// 更新未約定本院責任上限
        /// </summary>
        /// <returns></returns>
        public bool Update_chkdata_report_risk()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_report_risk";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", _ver);
            oCmd.Parameters.AddWithValue("@chk_reason", chk_reason);
            oCmd.Parameters.AddWithValue("@chk_risk", chk_risk);
            oCmd.Parameters.AddWithValue("@chk_import", chk_import);
            oCmd.Parameters.AddWithValue("@empno", _modempno);
            
            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 儲存簽辦說明
        /// </summary>
        /// <returns></returns>
        public bool Update_chkdata_chk_memo()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_chk_memo";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", _ver);
            oCmd.Parameters.AddWithValue("@chk_memo", chk_memo);
            oCmd.Parameters.AddWithValue("@empno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        #endregion

        #region 簽核
        /// <summary>
        /// 取得簽核意見
        /// </summary>
        /// <returns></returns>
        public DataTable GetECP_command()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_command";

            oCmd.Parameters.AddWithValue("@GUID", chk_guid);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得簽核流程
        /// </summary>
        /// <returns></returns>
        public DataTable GetECP_flow()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_preflow_select";

            oCmd.Parameters.AddWithValue("@PARENTGUID", chk_guid);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 刪除簽核流程(產生簽核流程前的刪除)
        /// </summary>
        /// <returns></returns>
        public bool Delete_signflow()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_del_preflow";
            oCmd.Parameters.AddWithValue("@guid", chk_guid);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@empno", _keyinempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 刪除簽核流程(送簽前的刪除)
        /// </summary>
        /// <returns></returns>
        public bool Delete_signflow2()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_preflow_delete";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@PARENTGUID", parentGUID);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 產生簽核流程
        /// </summary>
        /// <returns></returns>
        public bool Insert_signflow()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_preflow_insert";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@PARENTGUID", parentGUID);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@Seq",seq );
            oCmd.Parameters.AddWithValue("@ActRoleName", actRoleName);
            oCmd.Parameters.AddWithValue("@RecUserID",recUserID );
            oCmd.Parameters.AddWithValue("@SignType",signType );
            oCmd.Parameters.AddWithValue("@SignSigle",signSigle );
            oCmd.Parameters.AddWithValue("@NoticeEmpno",noticeEmpno );
            oCmd.Parameters.AddWithValue("@SignClass",signClass );
            oCmd.Parameters.AddWithValue("@IS_LOCK", iS_LOCK);
            oCmd.Parameters.AddWithValue("@modempno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 產生簽核預覽流程
        /// </summary>
        /// <returns>表單Guid</returns>
        public string ReGen_signflow()
        {
            string guid = string.Empty;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_gen_preflow";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@keyinemp", _keyinempno);
            SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
            rtn_flag.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            //成功則回傳guid
            if (rtn_flag.Value.ToString() == "1")
            {
                switch (formNo)
                {
                    case "GPI01":
                        DataTable dt = GetDetail();
                        if (dt.Rows.Count >0)
                        {
                            guid = dt.Rows[0]["chk_guid"].ToString ();
                        }
                        break;
                    case "GPI02":
                        Signcont mySigncont = new Signcont();
                        mySigncont.Of_id = _seqsn;
                        DataTable dt_signcont = mySigncont.Get_officeicalseal();
                        if (dt_signcont.Rows.Count > 0)
                        {
                            guid = dt_signcont.Rows[0]["of_guid"].ToString();
                        }
                        break;
                }
                
            }

            return guid;

        }
        /// <summary>
        /// ECP送簽Web Service成功送簽後更新
        /// </summary>
        /// <returns></returns>
        public bool Update_signed(string ecp_no)
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_Send_After_upadate";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@guid", chk_guid);
            oCmd.Parameters.AddWithValue("@ecp_no", ecp_no);
            oCmd.Parameters.AddWithValue("@empno", _keyinempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 更新檢核表列印日期
        /// </summary>
        /// <returns></returns>
        public bool Update_printdate()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
update gpi_chkdata set chk_modify='N', chk_printdate = @printdate where chk_seqsn = @seqsn
";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@printdate", DateTime.Today.ToString ("yyyyMMdd"));

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 更新紙本簽核
        /// </summary>
        /// <returns></returns>
        public string Update_topaper()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_updatedata_to_paper";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@modempno", _modempno);
            SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
            rtn_flag.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_flag.Value.ToString();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public bool Update_Send_before()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_Send_before_upadate";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@FormNo", formNo);
            oCmd.Parameters.AddWithValue("@guid", parentGUID);
            oCmd.Parameters.AddWithValue("@ecp_no", DBNull.Value);
            oCmd.Parameters.AddWithValue("@empno", _keyinempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 標案取得轉紙本ECP connection
        /// </summary>
        /// <returns></returns>
        public DataTable Get_Topaper_GPI()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_topaper_GPI";

            oCmd.Parameters.AddWithValue("@PARENTGUID", chk_guid);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 送簽失敗後刪除ECP資料庫中的流程
        /// </summary>
        /// <returns></returns>
        public bool Delete_del_GPI_FlowD()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_del_GPI_FlowD";

            oCmd.Parameters.AddWithValue("@GUID", parentGUID);

            try
            {
                this.Execute2(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        #region 草約
        /// <summary>
        /// 取得挑選的草約清單
        /// </summary>
        /// <returns></returns>
        public DataTable Get_choose_treaty()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT tc_seno,tcdt_conttype ,tc_planno + tc_ver + tc_seqsn AS chk_contno, tcdt_name, tcdt_sdate, tcdt_edate, tc_seqsn, tc_handle_name, tc_case_closedate, tc_betsum, tc_sRC_ver
FROM v_gpi_sign_choose_treaty_case treaty
WHERE tc_planno=@planno
order by tc_planno,tc_seqsn
";

            oCmd.Parameters.AddWithValue("@planno", _planno);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        /// 更新草約資訊
        /// </summary>
        /// <returns></returns>
        public bool Update_choose_treaty()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
pr_gpi_treaty_save @main_seqsn = @main_seqsn,
                   @chk_process = @chk_process,
                   @chk_process_memo = @chk_process_memo,
                   @chk_tc_seno = @chk_tc_seno,
                   @chk_modempno = @chk_modempno

exec pr_gpi_sign_sRC_set @seqsn = @main_seqsn
";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@chk_process", chk_process);
            oCmd.Parameters.AddWithValue("@chk_process_memo", chk_process_memo);
            oCmd.Parameters.AddWithValue("@chk_tc_seno", chk_tc_seno);
            oCmd.Parameters.AddWithValue("@chk_modempno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        #endregion

        /// <summary>
        /// 取得版本
        /// </summary>
        /// <returns></returns>
        public int GetVer()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT chk_ver FROM gpi_chkdata
WHERE chk_seqsn = @chk_seqsn
";
            oCmd.Parameters.AddWithValue("@chk_seqsn", _seqsn);

            string data = this.getTopOne(oCmd, CommandType.Text);
            int ver = 1;//投標檢核預設為「1」
            if (data != string.Empty)
            {
                ver = int.Parse(data);
            }
            return ver;
        }

        //
        /// <summary>
        /// 取得列印檢核表查詢報表 URL
        /// </summary>
        /// <returns></returns>
        public string Get_chkdata_rpt()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_chkdata_rpt";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@formno", formNo);
            SqlParameter rpt_url = oCmd.Parameters.Add("@rpt_url", SqlDbType.VarChar, 1000);
            rpt_url.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rpt_url.Value.ToString();
        }

        #region 列印投標檢核表(added by cary)
        /// <summary>
        /// report_gpi_showquestionnaire
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_showquestionnaire()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_showquestionnaire";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_no51mut
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_no51mut()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_no51mut";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_no6mut
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_no6mut()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_no6mut";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_no7mut
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_no7mut()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_no7mut";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_double
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_double()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_double";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_contend_cust
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_contend_cust()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_contend_cust";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_reportFooter
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_reportFooter(string orgcd)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_reportFooter";

            oCmd.Parameters.AddWithValue("@type", "1");
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_ecp_command1
        /// </summary>
        /// <returns></returns>
        public DataTable report_ecp_command1()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_ecp_command";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@formno", "GPI01");
            oCmd.Parameters.AddWithValue("@flag", 1);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_ecp_command2
        /// </summary>
        /// <returns></returns>
        public DataTable report_ecp_command2()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_ecp_command";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@formno", "GPI01");
            oCmd.Parameters.AddWithValue("@flag", 2);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        #endregion

        #endregion
    }
}