﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_contactAdd.aspx.cs" Inherits="Treaty_webpage_TreatyCase2_contactAdd" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("新增成功!");
            parent.$.fn.colorbox.close();
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 40px">
                <tr>
                    <td class="td_right">公司：</td>
                    <td>
                        <asp:TextBox ID="TB_compname" runat="server" class="inputsizeL"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">姓名：</td>
                    <td>
                        <asp:TextBox ID="TB_name" runat="server" class="inputsizeM"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">電話：</td>
                    <td>
                        <asp:TextBox ID="TB_tel" runat="server" class="inputsizeM"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">mail：</td>
                    <td>
                        <asp:TextBox ID="TB_mail" runat="server" class="inputsizeM"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right" colspan="2">
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>
            </table>

            <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

        </span>

        <script type="text/javascript">

</script>
    </form>
</body>
</html>
