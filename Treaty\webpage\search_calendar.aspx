﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="search_calendar.aspx.cs" Inherits="Search_calendar" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<%@ Register Src="../userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="../userControl/Foot.ascx" TagPrefix="uc2" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";
        var ret_url = escape("../subap/colorbox_close.aspx");
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }

        .Mask {
            display: none;
            position: fixed;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 2;
            top: 0;
            left: 0;
            opacity: 0.5;
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                    </div>
                    <div class="fixwidth">
                        <br />
                        <asp:DropDownList ID="DDL_year" runat="server" DataTextField="xYear" DataValueField="xYear" AutoPostBack="True" OnSelectedIndexChanged="DDL_year_SelectedIndexChanged"></asp:DropDownList>
                        <%-- <asp:SqlDataSource ID="SDS_year" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                        ／
         <asp:Button ID="BT_addNextYear" runat="server" class="genbtn" Text="產生下一年行事曆" OnClick="BT_addNextYear_Click"></asp:Button>／
         <asp:TextBox ID="TB_date" class="pickdate inputex inputsizeM text-input" runat="server" Width="90px" MaxLength="10" />
                        <asp:Button ID="BT_addNewDay" runat="server" class="genbtn" Text="新增" OnClick="BT_addNewDay_Click"></asp:Button>
                    </div>
                    <!-- fixwidth -->
                    <div class="fixwidth">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td colspan="6">
                                    <div class="twocol margin5TB">
                                        <span class="stripeMe">
                                            <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                                <FooterStyle BackColor="White" />
                                                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                <Columns>

                                                    <asp:TemplateField HeaderText="休假日">
                                                        <ItemTemplate>
                                                            <asp:ImageButton ID="IB_xID" runat="server" CommandName="del" CommandArgument='<%# Eval("xID")  %>' ImageUrl="../images/del.gif" Class="ajax_mesg" />
                                                            <asp:Label ID="LB_assign_day" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("xDate").ToString())) %>'></asp:Label>
                                                            <asp:Label ID="Label1" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("Weekday").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <ItemStyle HorizontalAlign="Left" Width="600px" />
                                                    </asp:TemplateField>

                                                </Columns>
                                                <EmptyDataTemplate>
                                                    <!--當找不到資料時則顯示「無資料」-->
                                                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                </EmptyDataTemplate>
                                                <FooterStyle BackColor="White" />
                                                <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                            </cc1:SmartGridView>
                                            <%-- <asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- fixwidth -->

                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc2:Foot runat="server" ID="Foot" />
        <%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yy/mm/dd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });

        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
