﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Search_inner_YearReport.aspx.cs" Inherits="Search_inner_YearReport" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="../userControl/Foot.ascx" TagPrefix="uc2" TagName="Foot" %>
<%@ Register Src="../userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        //$(function () { $('a.iterm_file').cluetip({ activation: 'click', width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' }); });
        function file_View(fid) {
            $(".iterm_file").colorbox({
                href: "./TreatyCase_AllFile.aspx?contno=" + fid
            , title: '議約案件相關檔案'
            , iframe: true, width: "880px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false
            , onClosed: function () {
                $('html, body').css('overflow', '');
            }
            });
        }
        function doNewCase(seno, contno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_NewVer.aspx?contno=" + contno
                , iframe: true, width: "440px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    if ($.colorbox.data != "undefined") {
                        if ($.colorbox.data != "") {
                            if (contno.indexOf("Q") > 0)
                                window.location = "./TreatyApplyQ.aspx?seno=" + seno + "&newver=" + $.colorbox.data;
                            else
                                window.location = "./TreatyApply.aspx?seno=" + seno + "&newver=" + $.colorbox.data;
                        }
                    }
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .empty {
            color: #aaa;
        }

        table.TbStatistic {
            color: #333333;
            border-width: 1px;
            border-color: #666666;
            border-collapse: collapse;
            width: 100%;
        }

            table.TbStatistic tr {
                border-width: 1px;
                border-style: solid;
                border-color: #666666;
                background-color: #dedede;
            }

            table.TbStatistic td {
                border-width: 1px;
                border-style: solid;
                border-color: #666666;
                background-color: #ffffff;
            }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">單位別：</td>
                                        <td colspan="5">
                                            <asp:CheckBoxList ID="CBL_org" runat="server" Width="100%" RepeatDirection="Horizontal" RepeatColumns="9" DataTextField="orgcd_name" DataValueField="orgcd">
                                            </asp:CheckBoxList><%--<asp:SqlDataSource ID="SDS_Orgcd" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right'>承辦人：</td>
                                        <td colspan="5">

                                            <asp:CheckBoxList ID="CBL_handelList" runat="server" Width="100%" RepeatDirection="Horizontal" RepeatColumns="9" DataTextField="empname" DataValueField="empno">
                                            </asp:CheckBoxList>
                                            <%--<asp:SqlDataSource ID="SDS_handelList" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%></td>
                                    </tr>
                                    <tr>
                                        <td align='right'>年度：</td>
                                        <td>
                                            <asp:DropDownList ID="DDL_Year" runat="server" Width="101px" DataTextField="tc_year" DataValueField="tc_year"></asp:DropDownList>

                                            <%--<asp:SqlDataSource ID="SDS_year" runat="server" ConnectionString="<%$ ConnectionStrings:ConnString %>" SelectCommand="select distinct tc_year from treaty_case  order by tc_year  desc" />--%>
                                        </td>
                                        <td align='right'>案件類型：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlCaseStyle" runat="server" Width="150px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_CaseStyle" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align='right'>契約性質：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right' nowrap>
                                            <span class="gentable font-normal">修約類：</span></td>
                                        <td>
                                            <span class="gentable font-normal">
                                                <asp:DropDownList ID="ddl_amend" runat="server">
                                                    <asp:ListItem Value="0">全部</asp:ListItem>
                                                    <asp:ListItem Value="1">是</asp:ListItem>
                                                    <asp:ListItem Value="2">否</asp:ListItem>
                                                </asp:DropDownList></span></td>
                                        <td align='right'>單位承辦人員：</td>
                                        <td>
                                            <asp:TextBox ID="tbxPromoterName" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right' nowrap>重大效益案件：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlImportant" runat="server">
                                                <asp:ListItem Value="">全部</asp:ListItem>
                                                <asp:ListItem Value="1">是</asp:ListItem>
                                                <asp:ListItem Value="0">否</asp:ListItem>
                                            </asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right'>案源：</td>
                                        <td colspan="3">
                                            <asp:CheckBoxList ID="cbxCaseClass" runat="server" RepeatDirection="Horizontal" BorderWidth="0px" RepeatLayout="Flow" RepeatColumns="6">
                                                <asp:ListItem Value="N">洽案</asp:ListItem>
                                                <asp:ListItem Value="R">標案</asp:ListItem>
                                                <asp:ListItem Value="M">NDA</asp:ListItem>
                                                <asp:ListItem Value="A">國外無收入</asp:ListItem>
                                                <asp:ListItem Value="F">國內無收入</asp:ListItem>
                                                <asp:ListItem Value="S">新創事業</asp:ListItem>
                                                <asp:ListItem Value="L">智權糾紛</asp:ListItem>
                                                <asp:ListItem Value="Q">契約及法律問題</asp:ListItem>
                                                <asp:ListItem Value="T">其它</asp:ListItem>
                                            </asp:CheckBoxList>
                                            <span style="vertical-align: top;">&nbsp;&nbsp;&nbsp;
                                                        <asp:CheckBox ID="CB_常用版本" runat="server" Text="常用版本" />
                                            </span>

                                        </td>
                                        <td align='right'>案件顯示：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlShowCase" runat="server" Width="96px">
                                                <asp:ListItem Value="0" Selected="True">全部件次</asp:ListItem>
                                                <asp:ListItem Value="1">最新件次</asp:ListItem>
                                            </asp:DropDownList></td>
                                    </tr>
                                    <tr>
                                        <td align='right'>狀態：</td>
                                        <td colspan="3">
                                            <asp:CheckBoxList ID="CBL_Status" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow" BorderStyle="None">
                                                <asp:ListItem Value="0">未結件&#160;&#160;&#160;</asp:ListItem>
                                                <asp:ListItem Value="1">結件&#160;&#160;&#160;</asp:ListItem>
                                                <asp:ListItem Value="2">需求取消&#160;&#160;&#160;</asp:ListItem>
                                                <asp:ListItem Value="3">逾期&#160;&#160;&#160;</asp:ListItem>
                                            </asp:CheckBoxList>
                                        </td>
                                        <td align='right'>收文日期：</td>
                                        <td>
                                            <asp:TextBox ID="txtReceiveSDate" runat="server" MaxLength="8" Width="75px" class="pickdate inputex inputsizeS"></asp:TextBox>
                                            &nbsp;~ 
		       		<asp:TextBox ID="txtReceiveEDate" runat="server" MaxLength="8" Width="75px" class="pickdate inputex inputsizeS"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">關鍵字查詢： </td>
                                        <td>
                                            <asp:TextBox ID="txtKeyWord" runat="server" Width="181px" MaxLength="1000"></asp:TextBox></td>
                                        <td align='right'>客戶名稱：</td>
                                        <td>
                                            <asp:TextBox ID="txtCompname" runat="server"></asp:TextBox></td>
                                        <td align='right'>結件日期：</td>
                                        <td>
                                            <asp:TextBox ID="txtCloseSDate" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox>
                                            &nbsp;~ 
		       		<asp:TextBox ID="txtCloseEDate" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox></td>
                                    </tr>
                                    <tr>

                                        <td colspan="6" class="td_right">
                                            <div style="float: left">
                                                <asp:LinkButton ID="LB_Excel" runat="server" OnClick="LB_Excel_Click" Visible="False">匯出 Excel</asp:LinkButton>
                                                |
                                            </div>



                                            <asp:Button ID="btnQuery" TabIndex="1" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery_Click"></asp:Button>
                                        </td>
                                    </tr>
                                </table>
                                <asp:Label ID="lbHtml" runat="server"></asp:Label>
                                <%--<asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelected="SDS_search_Selected" />--%>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <div style="display: none">
            <rsweb:ReportViewer ID="ReportViewer1" runat="server" Font-Names="Verdana" Font-Size="8pt" WaitMessageFont-Names="Verdana" WaitMessageFont-Size="14pt" Width="1460px" ShowExportControls="False" />
            <%--            <asp:SqlDataSource ID="SDS_Detail" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_Condition" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        </div>

        <uc2:Foot runat="server" ID="Foot" />
        <%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });
            $(document).ready(
             function () {
                 $(document).ready(function () { $('.headernews').scrollbox({ delay: 4 }); });
                 $(".itemhint").tooltip({
                     track: true,
                     position: { my: "left+15 center", at: "right center" },
                     //讓tooltips內可以放置HTML CODE
                     content: function () {
                         return $(this).prop('title');
                     }
                 });
                 $(".inputhint").tooltip({
                     position: { my: "left+10 bottom+40", at: "left bottom " },
                     tooltipClass: "custom-tooltip-styling",
                     //讓tooltips內可以放置HTML CODE
                     content: function () {
                         return $(this).prop('title');
                     }
                 });
                 //說明dialog
                 $("#pagehow01").dialog({
                     modal: true,
                     position: ["center", 100],
                     width: 500,
                     height: 300,
                     autoOpen: false,
                     show: {
                         duration: 300
                     },
                     hide: {
                         duration: 300
                     }
                 });

                 $(".itemhint").tooltip({
                     track: true,
                     position: { my: "left+15 center", at: "right center" },
                     //讓tooltips內可以放置HTML CODE
                     content: function () {
                         return $(this).prop('title');
                     }
                 });
                 //說明dialog
                 $("#pagehow01").dialog({
                     modal: true,
                     position: ["center", 100],
                     width: 500,
                     height: 300,
                     autoOpen: false,
                     show: {
                         duration: 300
                     },
                     hide: {
                         duration: 300
                     }
                 });

             });
            $(document).ready(function () {
                $(".accordionblock").hide();
                //個別按鈕操作
                $(".itemcontrolbtn").click(function () {
                    //切換子項顯示與隱藏
                    $(this).parent().parent().next(".accordionblock").slideToggle();
                    //ICON樣式切換
                    $(this).toggleClass("iconup");
                    //文字切換  ?:運算式是if else的快捷方式
                    //$(this).text($(this).text() == '展開項目' ? '收合項目' : '展開項目');
                });
                //全部展開
                $(".AllControlOpen").click(function () {
                    $(".accordionblock").slideDown();
                    //$(".itemcontrolbtn").text('收合項目');
                    $(".itemcontrolbtn").addClass("iconup")
                });
                //全部收合
                $(".AllControlClose").click(function () {
                    $(".accordionblock").slideUp();
                    //$(".itemcontrolbtn").text('展開項目');
                    $(".itemcontrolbtn").removeClass("iconup")
                });
            });
        </script>
    </form>
</body>
</html>
