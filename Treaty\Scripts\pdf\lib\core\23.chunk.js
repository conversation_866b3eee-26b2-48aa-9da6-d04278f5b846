/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[23],{400:function(ia){(function(){ia.exports={DR:function(){function y(e,f){this.scrollLeft=e;this.scrollTop=f}function e(e){if(null===e||"object"!==typeof e||void 0===e.behavior||"auto"===e.behavior||"instant"===e.behavior)return!0;if("object"===typeof e&&"smooth"===e.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+e.behavior+" is not a valid value for enumeration ScrollBehavior.");}function fa(e,h){if("Y"===
h)return e.clientHeight+f<e.scrollHeight;if("X"===h)return e.clientWidth+f<e.scrollWidth}function x(e,f){e=ba.getComputedStyle(e,null)["overflow"+f];return"auto"===e||"scroll"===e}function ha(e){var f=fa(e,"Y")&&x(e,"Y");e=fa(e,"X")&&x(e,"X");return f||e}function ea(e){var f=(h()-e.startTime)/468;var n=.5*(1-Math.cos(Math.PI*(1<f?1:f)));f=e.Xv+(e.x-e.Xv)*n;n=e.Yv+(e.y-e.Yv)*n;e.method.call(e.hB,f,n);f===e.x&&n===e.y||ba.requestAnimationFrame(ea.bind(ba,e))}function da(e,f,x){var n=h();if(e===w.body){var z=
ba;var aa=ba.scrollX||ba.pageXOffset;e=ba.scrollY||ba.pageYOffset;var ca=r.scroll}else z=e,aa=e.scrollLeft,e=e.scrollTop,ca=y;ea({hB:z,method:ca,startTime:n,Xv:aa,Yv:e,x:f,y:x})}var ba=window,w=document;if(!("scrollBehavior"in w.documentElement.style&&!0!==ba.Gfa)){var z=ba.HTMLElement||ba.Element,r={scroll:ba.scroll||ba.scrollTo,scrollBy:ba.scrollBy,sN:z.prototype.scroll||y,scrollIntoView:z.prototype.scrollIntoView},h=ba.performance&&ba.performance.now?ba.performance.now.bind(ba.performance):Date.now,
f=/MSIE |Trident\/|Edge\//.test(ba.navigator.userAgent)?1:0;ba.scroll=ba.scrollTo=function(f,h){void 0!==f&&(!0===e(f)?r.scroll.call(ba,void 0!==f.left?f.left:"object"!==typeof f?f:ba.scrollX||ba.pageXOffset,void 0!==f.top?f.top:void 0!==h?h:ba.scrollY||ba.pageYOffset):da.call(ba,w.body,void 0!==f.left?~~f.left:ba.scrollX||ba.pageXOffset,void 0!==f.top?~~f.top:ba.scrollY||ba.pageYOffset))};ba.scrollBy=function(f,h){void 0!==f&&(e(f)?r.scrollBy.call(ba,void 0!==f.left?f.left:"object"!==typeof f?f:
0,void 0!==f.top?f.top:void 0!==h?h:0):da.call(ba,w.body,~~f.left+(ba.scrollX||ba.pageXOffset),~~f.top+(ba.scrollY||ba.pageYOffset)))};z.prototype.scroll=z.prototype.scrollTo=function(f,h){if(void 0!==f)if(!0===e(f)){if("number"===typeof f&&void 0===h)throw new SyntaxError("Value could not be converted");r.sN.call(this,void 0!==f.left?~~f.left:"object"!==typeof f?~~f:this.scrollLeft,void 0!==f.top?~~f.top:void 0!==h?~~h:this.scrollTop)}else h=f.left,f=f.top,da.call(this,this,"undefined"===typeof h?
this.scrollLeft:~~h,"undefined"===typeof f?this.scrollTop:~~f)};z.prototype.scrollBy=function(f,h){void 0!==f&&(!0===e(f)?r.sN.call(this,void 0!==f.left?~~f.left+this.scrollLeft:~~f+this.scrollLeft,void 0!==f.top?~~f.top+this.scrollTop:~~h+this.scrollTop):this.scroll({left:~~f.left+this.scrollLeft,top:~~f.top+this.scrollTop,behavior:f.behavior}))};z.prototype.scrollIntoView=function(f){if(!0===e(f))r.scrollIntoView.call(this,void 0===f?!0:f);else{for(f=this;f!==w.body&&!1===ha(f);)f=f.parentNode||
f.host;var h=f.getBoundingClientRect(),n=this.getBoundingClientRect();f!==w.body?(da.call(this,f,f.scrollLeft+n.left-h.left,f.scrollTop+n.top-h.top),"fixed"!==ba.getComputedStyle(f).position&&ba.scrollBy({left:h.left,top:h.top,behavior:"smooth"})):ba.scrollBy({left:n.left,top:n.top,behavior:"smooth"})}}}}}})()}}]);}).call(this || window)
