﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myBulletin
	/// </summary>
	public class myBulletin : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _empno;
		private string _empname;

		#endregion

		#region 公有屬性

		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		public long BB_id = 0;
		/// <summary>
		/// 系統別,{洽案:F2-28}
		/// </summary>
		public string BB_sys = string.Empty;
		/// <summary>
		/// 類別, {1：訊息公告}
		/// </summary>
		public string BB_type = "1";
		public string BB_subject = string.Empty;
		public string BB_content = string.Empty;
		/// <summary>
		/// 是否顯示
		/// </summary>
		public string BB_isvisible = "1";
		public string BB_begindate = string.Empty;
		public string BB_enddate = string.Empty;
		public string BB_modempno = string.Empty;
		public int BB_order = 0;
		public string BB_service_down = string.Empty;

		/// <summary>
		/// 顯示模式, S:停機公告, Y:顯示, N:關閉
		/// </summary>
		public string ShowingMode = "Y";

		public long ba_id = 0;
		public string ba_uploaddate = string.Empty;
		public string ba_filename = string.Empty;
		public string ba_keyinempno = string.Empty;
		public string ba_keyinempname = string.Empty;
		public string ba_url;

		public string bs_id = string.Empty;
		public string bs_bb_id = string.Empty;
		public string bs_sys = string.Empty;
		public string bs_empno = string.Empty;
		#endregion

		public myBulletin() { }

		#region 取得FAQ的系統名稱、系統負責人、業務人員
		/// <summary>
		/// 取得FAQ的系統名稱、系統負責人、業務人員
		/// </summary>
		/// <returns></returns>
		public DataTable GetFAQSysOnwer()
		{
			string sql = @"select * from devform.dbo.v_infosys_forpubengage where iso_no = @BB_sys";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@BB_sys", this.BB_sys);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 訊息公告(swapdb.dbo.BulletinBoard)

		public DataTable BulletinGetAll()
		{
			string sql = @"
	select BB_id,BB_subject,BB_content,BB_order,BB_service_down
		,CONVERT(varchar,convert(datetime,BB_begindate),111) as BB_begindate
		,CONVERT(varchar,convert(datetime,BB_enddate),111) as BB_enddate
	 from swapdb.dbo.BulletinBoard where BB_sys = @BB_sys and BB_type = @BB_type";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@BB_sys", this.BB_sys);
			oCmd.Parameters.AddWithValue("@BB_type", this.BB_type);		//1：訊息公告

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable BulletinGetTop5()
		{
			string sql = @"
				select TOP 5 * from swapdb.dbo.BulletinBoard 
				  where BB_sys = @BB_sys and BB_type = @BB_type AND BB_isvisible=1 AND (CONVERT(char(8),getdate(),112) between BB_begindate and BB_enddate) 
				  ORDER BY BB_order DESC";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;


			oCmd.Parameters.AddWithValue("@BB_sys", this.BB_sys);
			oCmd.Parameters.AddWithValue("@BB_type", this.BB_type);		//1：訊息公告

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable BulletinGetOne()
		{
			string sql = @"select * from swapdb.dbo.BulletinBoard where BB_id = @BB_id";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;


			oCmd.Parameters.AddWithValue("@BB_id", this.BB_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool BulletinSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists (select * from swapdb.dbo.BulletinBoard where BB_id=@BB_id)
begin
	declare @order int
	select @order=isnull(MAX(BB_order),0)+1 from swapdb.dbo.BulletinBoard where BB_sys = @BB_sys and BB_type = @BB_type

	insert into swapdb.dbo.BulletinBoard
		(BB_sys,BB_type,BB_subject,BB_content,BB_crttime,BB_poster,BB_isvisible
		,BB_begindate,BB_enddate,BB_modtime,BB_modempno, BB_service_down, BB_order)
	select @BB_sys,@BB_type,@BB_subject,@BB_content,GETDATE(),@BB_poster,1
		,@BB_begindate,@BB_enddate,GETDATE(),@BB_modempno, @BB_service_down, @order
		
	select @BB_id = @@IDENTITY
end
else
begin
	update swapdb.dbo.BulletinBoard set
		 BB_subject=@BB_subject,
		 BB_content=@BB_content,
		 BB_isvisible=1,
		 BB_begindate=@BB_begindate,
		 BB_enddate=@BB_enddate,
		 BB_modtime=GETDATE(),
		 BB_modempno=@BB_modempno,
		 BB_service_down=@BB_service_down
	where BB_id=@BB_id	
end
select @BB_id
";

			oCmd.Parameters.AddWithValue("@BB_sys", BB_sys);
			oCmd.Parameters.AddWithValue("@BB_type", BB_type);
			oCmd.Parameters.AddWithValue("@BB_subject", BB_subject);
			oCmd.Parameters.AddWithValue("@BB_content", BB_content);
			oCmd.Parameters.AddWithValue("@BB_poster", BB_modempno);
			oCmd.Parameters.AddWithValue("@BB_begindate", BB_begindate.Replace("/", ""));
			oCmd.Parameters.AddWithValue("@BB_enddate", BB_enddate.Replace("/", ""));
			oCmd.Parameters.AddWithValue("@BB_modempno", BB_modempno);
			oCmd.Parameters.AddWithValue("@BB_service_down", BB_service_down);
			oCmd.Parameters.AddWithValue("@BB_id", BB_id);

			try
			{
				string retVal = this.getTopOne(oCmd, CommandType.Text);
				if (retVal != "")
					this.BB_id = Int64.Parse(retVal);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool BulletinDelete()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE swapdb.dbo.Bulletin_attfile WHERE ba_bb_id = @BB_id
DELETE swapdb.dbo.BulletinBoard WHERE BB_id = @BB_id
";

			oCmd.Parameters.AddWithValue("@BB_id", BB_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool BulletinCheckServiceDown()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select * from swapdb.dbo.BulletinBoard where BB_sys=@BB_sys and BB_type=@BB_type and BB_service_down='Y' and BB_id <> @BB_id)
	select 'Y' as Flag
else
	select 'N' as Flag
";

			oCmd.Parameters.AddWithValue("@BB_sys", BB_sys);
			oCmd.Parameters.AddWithValue("@BB_type", BB_type);
			oCmd.Parameters.AddWithValue("@BB_id", BB_id);

			bool success = false;
			try
			{
				string retVal = this.getTopOne(oCmd, CommandType.Text);
				success = retVal.Equals("Y");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 是否顯示「訊息公告」
		/// </summary>
		/// <returns></returns>
		public bool BulletinWindowShowing()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select * from swapdb.dbo.BulletinBoard where BB_sys = @BB_sys and BB_type = @BB_type 
			and BB_service_down = 'Y' and (CONVERT(varchar,GETDATE(),112) between BB_begindate and BB_enddate))
begin
	select 'S'
end
else
begin
	if not exists(select * from swapdb.dbo.Bulletin_set where bs_sys = @BB_sys and bs_empno = @empno)
		and exists(select * from swapdb.dbo.BulletinBoard where BB_sys = @BB_sys and BB_type = @BB_type 
					and BB_service_down <> 'Y' and (CONVERT(varchar,GETDATE(),112) between BB_begindate and BB_enddate))
	begin
		select 'Y'
	end
	else
	begin
		select 'N'
	end
end ";

			oCmd.Parameters.AddWithValue("@BB_sys", BB_sys);
			oCmd.Parameters.AddWithValue("@BB_type", BB_type);
			oCmd.Parameters.AddWithValue("@empno", this.EmpNo);

			bool success = false;
			try
			{
				string retVal = this.getTopOne(oCmd, CommandType.Text);
				this.ShowingMode = retVal;
				success = (retVal.Equals("S") || retVal.Equals("Y"));
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool BulletinUpdateOrder()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	update swapdb.dbo.BulletinBoard set BB_order = @BB_order, BB_modempno = @empno, BB_modtime=GETDATE() where BB_id = @BB_id ";

			oCmd.Parameters.AddWithValue("@BB_id", BB_id);
			oCmd.Parameters.AddWithValue("@BB_order", BB_order);
			oCmd.Parameters.AddWithValue("@empno", this.EmpNo);

			bool success = false;
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 附檔資訊(swapdb.dbo.Bulletin_attfile)

		/// <summary>
		/// 附檔資訊(取得所有附件檔)
		/// </summary>
		/// <returns></returns>
		public DataTable BulletinAttfileGetByMasterId()
		{
			string sql = @"SELECT * FROM swapdb.dbo.Bulletin_attfile WHERE ba_bb_id = @BB_id";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;


			oCmd.Parameters.AddWithValue("@BB_id", this.BB_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 附檔資訊(取得某一筆 By ba_id)
		/// </summary>
		/// <returns></returns>
		public DataTable BulletinAttfileGetById()
		{
			string sql = @"SELECT * FROM swapdb.dbo.Bulletin_attfile WHERE ba_id = @ba_id";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;


			oCmd.Parameters.AddWithValue("@ba_id", this.ba_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		#region BulletinAttfileInsert(), 附檔資訊(新增)

		public void BulletinAttfileInsert()
		{
			#region SQL
			string SQL = @"
	DECLARE @ba_id bigint

	INSERT INTO swapdb.dbo.[Bulletin_attfile]
           ([ba_bb_id],[ba_uploaddate],[ba_filename],[ba_keyinempno],[ba_keyinempname])
	SELECT @ba_bb_id,getdate(),@ba_filename,@ba_keyinempno,@ba_keyinempname

	select @ba_id=@@IDENTITY
	select @ba_id
";
			#endregion

			SqlParameter[] parms = {   
									   new SqlParameter("@ba_bb_id",SqlDbType.BigInt),
									   new SqlParameter("@ba_filename",SqlDbType.NVarChar),
									   new SqlParameter("@ba_keyinempno",SqlDbType.NVarChar),
									   new SqlParameter("@ba_keyinempname",SqlDbType.NVarChar)
								   };
			parms[0].Value = BB_id;
			parms[1].Value = ba_filename;
			parms[2].Value = ba_keyinempno;
			parms[3].Value = ba_keyinempname;

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = SQL;
			oCmd.Parameters.AddRange(parms);

			object obj = this.getTopOne(oCmd, CommandType.Text);
			if (obj != null)
				this.ba_id = int.Parse(obj.ToString());
		}

		#endregion		
	
		#region BulletinAttfileDeleteById(), 附檔資訊(刪除)

		public void BulletinAttfileDeleteById(string id)
		{
			#region SQL
			string SQL = @"DELETE swapdb.dbo.Bulletin_attfile WHERE ba_id=@id";
			#endregion

   			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = SQL;

			oCmd.Parameters.AddWithValue("@id", id);

			this.Execute(oCmd, CommandType.Text);
		}

        #endregion

        #endregion

        #region 訊息公告-設定(swapdb.dbo.Bulletin_set)
        /* 2018/03/01:Hugo(Modify)
         * (一)「下次登入不再顯示」打【V】規則維持不變, 將欄位 bs_today_first_login 清除成 Null
         * (二)當天已顯示一次，不要再顯示，處理方式：
         *  1.「當天第1次登入」，此工號無對應資料時，新增一筆資料，設定 bs_today_first_login = 「Y」
         *  2.「當天第2次登入」，此工號已有對應資料時，不顯示
         *  3. 判斷是否為過期, 若不是當天{DATEDIFF()>0}, 則為舊資料, 當天須顯示一次.
         */
        public bool BulletinSetHasExists()
		{
			string sql = @"
			select * from swapdb.dbo.Bulletin_set where bs_sys = @bs_sys and bs_empno = @bs_empno and isnull(bs_today_first_login,'') <> 'Y'
			";

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;


			oCmd.Parameters.AddWithValue("@bs_sys", this.bs_sys);
			oCmd.Parameters.AddWithValue("@bs_empno", this.bs_empno);		//1：訊息公告

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);

			return (dt.Rows.Count > 0);
		}

		public bool BulletinSetInsert()
		{
			string sql = @"
update swapdb.dbo.Bulletin_set set bs_date=getdate(), bs_today_first_login=null where bs_sys = @bs_sys and bs_empno = @bs_empno
";
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@bs_sys", this.bs_sys);
			oCmd.Parameters.AddWithValue("@bs_empno", this.bs_empno);

			bool success = false;
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool BulletinSetDelete()
		{
			string sql = @"
update swapdb.dbo.Bulletin_set set bs_date=getdate(), bs_today_first_login='Y' where bs_sys = @bs_sys and bs_empno = @bs_empno
";
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@bs_sys", this.bs_sys);
			oCmd.Parameters.AddWithValue("@bs_empno", this.bs_empno);

			bool success = false;
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

	}// end of class
}