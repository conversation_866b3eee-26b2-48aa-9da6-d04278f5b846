﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// 帳號資訊
    /// </summary>
    public class AccountInfo
    {
        public AccountInfo()
        {
            //
            // TODO: Add constructor logic here
            //
        }

        #region 工號
        /// <summary>
        /// 工號
        /// </summary>
        public static string EmpNo
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_No"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_No"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_No"] = value; }
        }
        #endregion

        #region 姓名
        /// <summary>
        /// 姓名
        /// </summary>
        public static string EmpName
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_Name"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_Name"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_Name"] = value; }
        }
        #endregion

        #region Email
        /// <summary>
        /// Email
        /// </summary>
        public static string Email
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_Email"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_Email"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_Email"] = value; }
        }
        #endregion

        #region 單位
        /// <summary>
        /// 單位
        /// </summary>
        public static string Orgcd
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_Orgcd"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_Orgcd"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_Orgcd"] = value; }
        }
        #endregion

        #region 部門
        /// <summary>
        /// 部門
        /// </summary>
        public static string Deptcd
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_Deptcd"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_Deptcd"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_Deptcd"] = value; }
        }
        #endregion

        #region 分機
        /// <summary>
        /// 分機
        /// </summary>
        public static string Telext
        {
            get
            {
                return System.Web.HttpContext.Current.Session["Emp_Telext"] == null ? String.Empty : System.Web.HttpContext.Current.Session["Emp_Telext"].ToString();
            }
            set { System.Web.HttpContext.Current.Session["Emp_Telext"] = value; }
        }
        #endregion

        #region 公有函式
        public static void LoginOut()
        {
            System.Web.HttpContext.Current.Session.Remove("Emp_No");
            System.Web.HttpContext.Current.Session.Remove("Emp_Name");
            System.Web.HttpContext.Current.Session.Remove("Emp_Email");
            System.Web.HttpContext.Current.Session.Remove("Emp_Orgcd");
            System.Web.HttpContext.Current.Session.Remove("Emp_Deptcd");
            System.Web.HttpContext.Current.Session.Remove("Emp_Telext");

        }
        #endregion
    }
}
