﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_manager_FileCopy : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {

    }

    protected void BT_copy_Click(object sender, EventArgs e)
    {

        string str_FileName = TB_file.Text.Replace("/", "").Replace(".....", ".").Replace("....", ".").Replace("...", ".").Replace("..", ".");
        try
        {
            File.Copy(TB_file.Text, str_FileName, true);
            LK_DL.Text = str_FileName;
        }
        catch (IOException iox)
        {
            LK_DL.Text = "error!!";
        }
    }

    protected void LK_DL_Click(object sender, EventArgs e)
    {

        if (File.Exists(LK_DL.Text.Replace("//", "/")))
        {
            Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(LK_DL.Text, Encoding.UTF8));
            Response.WriteFile(LK_DL.Text);
            Response.Flush();
            Response.End();           
        }
        else
        {
            LK_DL.Text = "無法下載!";
        }
    }

    protected void BT_read_Click(object sender, EventArgs e)
    {
        string str_File_id = "";
        string str_FileName = "";
        SDS_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_file.SelectParameters.Clear();
        SDS_file.SelectCommandType = SqlDataSourceCommandType.Text;
        SDS_file.SelectCommand = " select * from treaty_File_demo";
        for (int i = 0; i < SDS_file.SelectParameters.Count; i++)
        {
            SDS_file.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        this.SDS_file.DataBind();
        System.Data.DataView dvR = (DataView)SDS_file.Select(new DataSourceSelectArguments());
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                str_File_id = dvR[i]["tcdf_nox"].ToString();
                str_FileName = dvR[i]["tcdf_url"].ToString().Replace("/", "").Replace(".....", ".").Replace("....", ".").Replace("...", ".").Replace("..", ".").Replace("&", "＆");
                SDS_file.UpdateParameters.Clear();
                SDS_file.UpdateCommandType = SqlDataSourceCommandType.Text;
                SDS_file.UpdateCommand = " update treaty_File_demo set tcdf_url_new =@new  where tcdf_nox=@no ";
                SDS_file.UpdateParameters.Add("no", str_File_id);
                if (File.Exists(dvR[i]["tcdf_url"].ToString().Replace("//", "/")))
                {
                    try
                    {
                        File.Copy(dvR[i]["tcdf_url"].ToString(), str_FileName, true);
                        SDS_file.UpdateParameters.Add("new", str_FileName);
                    }
                    catch (IOException iox)
                    {
                        SDS_file.UpdateParameters.Add("new", "error 1");
                    }
                }
                else
                {
                    SDS_file.UpdateParameters.Add("new", "error 2");
                }

                for (int j = 0; j < SDS_file.UpdateParameters.Count; j++)
                {
                    SDS_file.UpdateParameters[j].ConvertEmptyStringToNull = false;
                }
                SDS_file.Update();

                str_File_id = "";
                str_FileName = "";
             }

            LK_DL.Text = "OK !";
        }
    }
}