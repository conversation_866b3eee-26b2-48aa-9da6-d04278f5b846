﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;
public partial class Search_calendar : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    } 
    protected void Page_Load(object sender, EventArgs e)
    {
      if (!IsPostBack) {
          if (lb_Subtitle.Text == String.Empty)
          {
              Breadcrumb myBreadcrumb = new Breadcrumb();
              lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
          }


        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;
        ViewState["empName"] = ssoUser.empName;
        BindYear();
        DDL_year.DataBind();
        DoSearch();
        TB_date.Attributes.Add("readOnly", "readonly");
       }
    }
 
    private void BindYear()
    {
        //SDS_year.SelectCommand = " SELECT DISTINCT LEFT(xDate, 4) AS xYear FROM  treaty_WeekendDate ORDER BY xYear DESC  ";
        //SDS_year.DataBind();
        //DDL_year.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT DISTINCT LEFT(xDate, 4) AS xYear FROM  treaty_WeekendDate ORDER BY xYear DESC  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_year.DataSource = dt;
                DDL_year.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void DoSearch()
	{   
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_search.SelectCommand = " SELECT xID, CONVERT(varchar, xDate) xDate, Weekday FROM treaty_WeekendDate where LEFT(xDate, 4)=" + DDL_year.SelectedValue + "  ORDER BY xDate DESC ";
        //SDS_search.DataBind();
        //SGV_search.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @" SELECT xID, CONVERT(varchar, xDate) xDate, Weekday FROM treaty_WeekendDate where LEFT(xDate, 4)=" + DDL_year.SelectedValue + "  ORDER BY xDate DESC ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        DoSearch();  
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "del")
        {
            //SDS_search.DeleteParameters.Clear();
            //SDS_search.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_search.DeleteCommand = " delete treaty_WeekendDate where  xID=@xID";
            //SDS_search.DeleteParameters.Add("xID", e.CommandArgument.ToString());
            //SDS_search.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = " delete treaty_WeekendDate where  xID=@xID";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@xID", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindYear();
            DDL_year.DataBind();
            DoSearch();
        }
    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        ImageButton lb_xID = (ImageButton)e.Row.FindControl("IB_xID");
        if ((lb_xID != null))
        {
            lb_xID.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
        }
     }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        SGV_search.PageIndex = 0;
        DoSearch();
    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    protected void DDL_CaseStatus_SelectedIndexChanged(object sender, EventArgs e)
    {
        DoSearch();
    }
    protected void DDL_year_SelectedIndexChanged(object sender, EventArgs e)
    {
        SGV_search.PageIndex = 0;
        DoSearch();
    }
    protected void BT_addNextYear_Click(object sender, EventArgs e)
    {
        //SDS_search.InsertParameters.Clear();
        //SDS_search.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
        //SDS_search.InsertCommand = " exec esp_Treaty_WeekendDate_AddNextYear";
        //SDS_search.Insert();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_Treaty_WeekendDate_AddNextYear";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        BindYear();
        DDL_year.DataBind();       
        DoSearch();
        string script = "<script language='javascript'>alert('行事曆產生成功！'); </script>";
        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
    }
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    protected void BT_addNewDay_Click(object sender, EventArgs e)
    {
        if  (TB_date.Text != "") 
        {
            if (CheckDateTimeType(TB_date.Text))
            {
                //SDS_search.InsertParameters.Clear();
                //SDS_search.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_search.InsertCommand = "esp_Treaty_WeekendDate_Add";
                //SDS_search.InsertParameters.Add("newDay", TB_date.Text);
                //SDS_search.Insert();


                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_Treaty_WeekendDate_Add";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@newDay", oRCM.SQLInjectionReplaceAll(TB_date.Text.Trim()));
                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion

                string script = "<script language='javascript'>alert('新增行事曆產生成功！'); </script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                BindYear();
                DDL_year.DataBind();
                DoSearch();
            }
            else
            {
                string script = "<script language='javascript'>alert('新增 行事曆有誤！'); </script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
            TB_date.Text = "";
        }
    }
}