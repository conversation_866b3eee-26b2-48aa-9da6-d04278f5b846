﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase_valuation_modify : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull, string mode)
    {
        if (mode == "Select")
        {
            foreach (Parameter parameter in dataSource.SelectParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Insert")
        {
            foreach (Parameter parameter in dataSource.InsertParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Updat")
        {
            foreach (Parameter parameter in dataSource.UpdateParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Delete")
        {
            foreach (Parameter parameter in dataSource.DeleteParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
    }
    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }

    public string tc_seno
    {
        get
        {
            if (ViewState["tc_seno"] == null)
            {
                if (Request.QueryString["seno"] == null)
                    Response.Redirect("../error.aspx");
                if (!IsNumber(Request.QueryString["seno"]))
                    Response.Redirect("../error.aspx");
                ViewState["tc_seno"] = Request.QueryString["seno"];
            }
            return ViewState["tc_seno"].ToString();
        }
    }
    public string tci_seno
    {
        get
        {
            if (ViewState["tci_seno"] == null)
            {
                if (Request.QueryString["seno"] == null)
                    Response.Redirect("../error.aspx");
                if (!IsNumber(Request.QueryString["seno"]))
                    Response.Redirect("../error.aspx");
                ViewState["tci_seno"] = Request.QueryString["seno"];
            }
            return ViewState["tci_seno"].ToString();
        }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;

            SqlCommand oCmd_1 = new SqlCommand();
            oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd_1.CommandText = "esp_TreatyCase_valuation";
            oCmd_1.CommandType = CommandType.StoredProcedure;
            oCmd_1.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            oCmd_1.Parameters.AddWithValue("mode", "change_degree");
            SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            DataSet ds_1 = new DataSet();
            oda_1.Fill(ds_1, "myTable");
            ds_1.Dispose();
            oCmd_1.Dispose();
            oda_1.Dispose();


            // databinding_審查人();
            btnFilesUpload2.Attributes.Add("onclick", "treaty_fileup(" + tc_seno + ");");

            databinding();
            BindData_vfile();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_vfile();
        }

    }
    ////protected void databinding_審查人() //審查人
    ////{
    ////    SDS_SC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
    ////    SDS_SC.SelectParameters.Clear();
    ////    SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
    ////    SDS_SC.SelectCommand = "esp_TreatyCase_valuation";
    ////    SDS_SC.SelectParameters.Add("empno", SQLInjectionReplaceAll("empno"));
    ////    SDS_SC.SelectParameters.Add("mode", SQLInjectionReplaceAll("inspact_p"));
    ////    SDS_SC.DataBind();
    ////    ConvertSqlParametersEmptyStringToNull(SDS_SC, false, "Select");
    ////    DDL_AssignInspect.DataBind();
    ////}
    protected void databinding()
    {
        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandText = "esp_TreatyCase_valuation";
        //oCmd_1.CommandType = CommandType.StoredProcedure;
        //oCmd_1.Parameters.AddWithValue("tc_seno", SQLInjectionReplaceAll(tc_seno));
        //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("版本", SQLInjectionReplaceAll("0"));
        //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("view"));

        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        //DataSet ds_1 = new DataSet();
        //oda_1.Fill(ds_1, "myTable");
        //DataView dv = ds_1.Tables[0].DefaultView;

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@版本", "0");
            sqlCmd.Parameters.AddWithValue("@mode", "view");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            if (dv[0]["進度"].ToString().Trim() == "3")
            {
                Response.Redirect("TreatyCase_valuation_view.aspx?seno=" + tc_seno);
            }

            LB_版本.Text = Server.HtmlEncode(dv[0]["版本"].ToString().Trim());
            TB_參考價.Text = Server.HtmlEncode(dv[0]["參考價"].ToString().Trim());
            DDL_盡職調查結果.SelectedValue = Server.HtmlEncode(dv[0]["盡職調查結果"].ToString().Trim());
            TB_底價.Text = Server.HtmlEncode(dv[0]["底價"].ToString().Trim());
            if (dv[0]["底價_無"].ToString().Trim() == "1")
            {
                CB_底價_無.Checked = true;
                TB_底價_無_說明.Text = Server.HtmlEncode(dv[0]["底價_無_說明"].ToString().Trim());
                TB_底價_無_說明.Visible = true;
            }
            TB_第三方鑑價.Text = Server.HtmlEncode(dv[0]["第三方鑑價"].ToString().Trim());
            if (dv[0]["第三方鑑價_無"].ToString().Trim() == "1")
            {
                CB_第三方鑑價_無.Checked = true;
                TB_第三方鑑價_無_說明.Text = Server.HtmlEncode(dv[0]["第三方鑑價_無_說明"].ToString().Trim());
                TB_第三方鑑價_無_說明.Visible = true;
            }
            TB_其他說明.Text = Server.HtmlEncode(dv[0]["其他說明"].ToString().Trim());
        }

    }
    private void BindData_vfile()
    {
        //SDS_vgv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_vgv_file.SelectParameters.Clear();
        //SDS_vgv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_vgv_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_valuation");
        //SDS_vgv_file.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(tc_seno));
        //SDS_vgv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
        //SDS_vgv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("file_view"));
        //ConvertSqlParametersEmptyStringToNull(SDS_vgv_file, false, "Select");
        //SDS_vgv_file.DataBind();
        //gv_vdoc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_vdoc_file.DataSource = dt;
                gv_vdoc_file.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }

    protected void BT_save_draft_Click(object sender, EventArgs e)
    {
        string str_error = "";
        if (TB_參考價.Text != "" && (!IsNumber(TB_參考價.Text)))
        {
            str_error += "參考價需填數字!\\n";
        }

        if (TB_底價.Text != "" && (!IsNumber(TB_底價.Text)))
        {
            str_error += "底價需填數字!\\n";
        }
        if (TB_第三方鑑價.Text != "" && (!IsNumber(TB_第三方鑑價.Text)))
        {
            str_error += "第三方鑑價需填數字!\\n";
        }

        if (CB_底價_無.Checked && TB_底價_無_說明.Text == "")
        {
            str_error += "底價_無_說明未填\\n";
        }
        if (CB_第三方鑑價_無.Checked && TB_第三方鑑價_無_說明.Text == "")
        {
            str_error += "第三方鑑價__無_說明未填\\n";
        }


        if (str_error == "")
        {

            //SDS_SC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_SC.UpdateCommand = "esp_TreatyCase_valuation";
            //SDS_SC.UpdateParameters.Add("tc_seno",     SQLInjectionReplaceAll(tc_seno)  );
            //SDS_SC.UpdateParameters.Add("tci_seno",    SQLInjectionReplaceAll(tci_seno) );
            //SDS_SC.UpdateParameters.Add("底價", SQLInjectionReplaceAll(TB_底價.Text.Trim())  );
            //SDS_SC.UpdateParameters.Add("底價_無", SQLInjectionReplaceAll(IIf(CB_底價_無.Checked, "1", "")));
            //SDS_SC.UpdateParameters.Add("底價_無_說明", SQLInjectionReplaceAll(TB_底價_無_說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("第三方鑑價", SQLInjectionReplaceAll(TB_第三方鑑價.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("第三方鑑價_無", SQLInjectionReplaceAll(IIf(CB_第三方鑑價_無.Checked, "1", "")));
            //SDS_SC.UpdateParameters.Add("第三方鑑價_無_說明", SQLInjectionReplaceAll(TB_第三方鑑價_無_說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("參考價", SQLInjectionReplaceAll(TB_參考價.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("盡職調查結果", SQLInjectionReplaceAll(DDL_盡職調查結果.SelectedValue));
            //SDS_SC.UpdateParameters.Add("其他說明", SQLInjectionReplaceAll(TB_其他說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("進度", SQLInjectionReplaceAll("1"));//草稿
            //SDS_SC.UpdateParameters.Add("mode", SQLInjectionReplaceAll("modify"));
            //ConvertSqlParametersEmptyStringToNull(SDS_SC, false, "Update");
            //SDS_SC.Update();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_valuation";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
                sqlCmd.Parameters.AddWithValue("@tci_seno", oRCM.SQLInjectionReplaceAll(tci_seno));
                sqlCmd.Parameters.AddWithValue("@底價", oRCM.SQLInjectionReplaceAll(TB_底價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@底價_無", oRCM.SQLInjectionReplaceAll(IIf(CB_底價_無.Checked, "1", "")));
                sqlCmd.Parameters.AddWithValue("@底價_無_說明", oRCM.SQLInjectionReplaceAll(TB_底價_無_說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價", oRCM.SQLInjectionReplaceAll(TB_第三方鑑價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價_無", oRCM.SQLInjectionReplaceAll(IIf(CB_第三方鑑價_無.Checked, "1", "")));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價_無_說明", oRCM.SQLInjectionReplaceAll(TB_第三方鑑價_無_說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@參考價", oRCM.SQLInjectionReplaceAll(TB_參考價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@盡職調查結果", oRCM.SQLInjectionReplaceAll(DDL_盡職調查結果.SelectedValue));
                sqlCmd.Parameters.AddWithValue("@其他說明", oRCM.SQLInjectionReplaceAll(TB_其他說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@進度", "1");//草稿
                sqlCmd.Parameters.AddWithValue("@mode", "modify");


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_Dwin();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('" + str_error + "');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }

    protected void BT_save_send_Click(object sender, EventArgs e)
    {
        string str_error = "";
        if (DDL_盡職調查結果.SelectedValue == "")
        {
            str_error = "盡職調查結果未選\\n";
        }

        if (TB_參考價.Text == "")
        {
            str_error += "參考價未填\\n";
        }
        if (TB_參考價.Text != "" && (!IsNumber(TB_參考價.Text)))
        {
            str_error += "參考價需填數字!\\n";
        }

        if (TB_底價.Text != "" && (!IsNumber(TB_底價.Text)))
        {
            str_error += "底價需填數字!\\n";
        }
        if (TB_第三方鑑價.Text != "" && (!IsNumber(TB_第三方鑑價.Text)))
        {
            str_error += "第三方鑑價需填數字!\\n";
        }
        if (CB_底價_無.Checked && TB_底價_無_說明.Text == "")
        {
            str_error += "底價_無_說明未填\\n";
        }
        if (CB_第三方鑑價_無.Checked && TB_第三方鑑價_無_說明.Text == "")
        {
            str_error += "第三方鑑價__無_說明未填\\n";
        }

        if (str_error == "")
        {
            //SDS_SC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_SC.UpdateCommand = "esp_TreatyCase_valuation";
            //SDS_SC.UpdateParameters.Add("tc_seno", SQLInjectionReplaceAll(tc_seno));
            //SDS_SC.UpdateParameters.Add("tci_seno", SQLInjectionReplaceAll(tci_seno));
            //SDS_SC.UpdateParameters.Add("底價", SQLInjectionReplaceAll(TB_底價.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("底價_無", SQLInjectionReplaceAll(IIf(CB_底價_無.Checked, "1", "")));
            //SDS_SC.UpdateParameters.Add("底價_無_說明", SQLInjectionReplaceAll(TB_底價_無_說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("第三方鑑價", SQLInjectionReplaceAll(TB_第三方鑑價.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("第三方鑑價_無", SQLInjectionReplaceAll(IIf(CB_第三方鑑價_無.Checked, "1", "")));
            //SDS_SC.UpdateParameters.Add("第三方鑑價_無_說明", SQLInjectionReplaceAll(TB_第三方鑑價_無_說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("參考價", SQLInjectionReplaceAll(TB_參考價.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("盡職調查結果", SQLInjectionReplaceAll(DDL_盡職調查結果.SelectedValue));
            //SDS_SC.UpdateParameters.Add("其他說明", SQLInjectionReplaceAll(TB_其他說明.Text.Trim()));
            //SDS_SC.UpdateParameters.Add("進度", SQLInjectionReplaceAll("3"));//送審
            //SDS_SC.UpdateParameters.Add("mode", SQLInjectionReplaceAll("modify"));
            //ConvertSqlParametersEmptyStringToNull(SDS_SC, false, "Update");
            //SDS_SC.Update();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_valuation";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
                sqlCmd.Parameters.AddWithValue("@tci_seno", oRCM.SQLInjectionReplaceAll(tci_seno));
                sqlCmd.Parameters.AddWithValue("@底價", oRCM.SQLInjectionReplaceAll(TB_底價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@底價_無", oRCM.SQLInjectionReplaceAll(IIf(CB_底價_無.Checked, "1", "")));
                sqlCmd.Parameters.AddWithValue("@底價_無_說明", oRCM.SQLInjectionReplaceAll(TB_底價_無_說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價", oRCM.SQLInjectionReplaceAll(TB_第三方鑑價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價_無", oRCM.SQLInjectionReplaceAll(IIf(CB_第三方鑑價_無.Checked, "1", "")));
                sqlCmd.Parameters.AddWithValue("@第三方鑑價_無_說明", oRCM.SQLInjectionReplaceAll(TB_第三方鑑價_無_說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@參考價", oRCM.SQLInjectionReplaceAll(TB_參考價.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@盡職調查結果", oRCM.SQLInjectionReplaceAll(DDL_盡職調查結果.SelectedValue));
                sqlCmd.Parameters.AddWithValue("@其他說明", oRCM.SQLInjectionReplaceAll(TB_其他說明.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@進度", "3");//送審
                sqlCmd.Parameters.AddWithValue("@mode", "modify");
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('" + str_error + "');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }

    protected void gv_vdoc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            //SqlCommand oCmd_1 = new SqlCommand();
            //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            //oCmd_1.CommandText = "esp_TreatyCase_valuation";
            //oCmd_1.CommandType = CommandType.StoredProcedure;
            //oCmd_1.Parameters.AddWithValue("tc_seno", SQLInjectionReplaceAll(tc_seno));
            //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            //oCmd_1.Parameters.AddWithValue("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("file_Download"));
            // SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            //DataSet ds_1 = new DataSet();
            //oda_1.Fill(ds_1, "myTable");
            //DataView dv = ds_1.Tables[0].DefaultView;

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_valuation";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "file_Download");
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = Server.HtmlEncode(dv[0]["tcdf_url"].ToString().Trim());
                str_filename = Server.HtmlEncode(dv[0]["tcdf_filename"].ToString().Trim());
                if (str_file_url != "")
                {
                    Response.Clear();
                    Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                    Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                    Response.Flush();
                    Response.End();
                }
            }
        }

        if (e.CommandName == "xDelete")
        {
            SqlCommand oCmd_1 = new SqlCommand();
            oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd_1.CommandText = "esp_TreatyCase_valuation";
            oCmd_1.CommandType = CommandType.StoredProcedure;
            oCmd_1.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(tc_seno));
            oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            oCmd_1.Parameters.AddWithValue("fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            oCmd_1.Parameters.AddWithValue("mode", "file_Del");
            SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            DataSet ds_1 = new DataSet();
            oda_1.Fill(ds_1, "myTable");
            ds_1.Dispose();
            oCmd_1.Dispose();
            oda_1.Dispose();
            BindData_vfile();
        }

    }

    protected void gv_vdoc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");

                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
            }
        }
    }

    protected void DDL_SeqSn_SelectedIndexChanged(object sender, EventArgs e)
    {
    }

    protected void CB_底價_無_CheckedChanged(object sender, EventArgs e)
    {
        if (CB_底價_無.Checked)
        {
            TB_底價_無_說明.Visible = true;
        }
        else
        {
            TB_底價_無_說明.Visible = false;
            TB_底價_無_說明.Text = "";
        }
    }

    protected void CB_第三方鑑價_無_CheckedChanged(object sender, EventArgs e)
    {
        if (CB_第三方鑑價_無.Checked)
        {
            TB_第三方鑑價_無_說明.Visible = true;
        }
        else
        {
            TB_第三方鑑價_無_說明.Visible = false;
            TB_第三方鑑價_無_說明.Text = "";
        }
    }
}