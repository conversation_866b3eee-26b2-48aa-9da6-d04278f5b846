﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApply_FileUp.aspx.cs" Inherits="TreatyApply_FileUp" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <%--<script type="text/javascript" src="../Scripts/autoheight.js"></script>--%>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            // $("#txt_doc").val(compare);

        }
        window.onsubmit = function () {
            var updateProgress = $find("<%= UpdateProgress1.ClientID %>");
            updateProgress.set_visible(true);
        }
    </script>
    <style type="text/css">
        .mask {
            z-index: 9999;
            position: fixed;
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-content: center;
            flex-wrap: wrap;
            /*background-color: #000;
            opacity: 0.5;*/
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="UpdatePanel1" DisplayAfter="50">
                    <ProgressTemplate>
                        <div class="mask">
                            <font color='red' style="margin: 40%"><b> <asp:Image ID="Image1" runat="server" ImageUrl="../images/roller.gif" />Processing........</b></font>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
                <div class="stripeMe">
                    <div style="display: none">
                        <asp:GridView ID="GridView1" runat="server"></asp:GridView>
                    </div>
                    <table style="margin-left: 15px; margin-top: 25px">
                        <tr>
                            <td class="td_right">上傳檔案：</td>
                            <td>
                                <asp:FileUpload ID="FU_up" runat="server" onpropertychange="TransferData(this.value);" Width="546px" class="genbtnS" /></td>
                        </tr>
                        <tr>
                            <td class="td_right">修改概要：</td>
                            <td>
                                <asp:TextBox ID="txt_filetxt" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox></td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <%--<asp:SqlDataSource ID="SDS_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                <div style="float: right">
                                    <asp:Button ID="BT_Save" runat="server" Text="上傳" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </ContentTemplate>
            <Triggers>
                <asp:PostBackTrigger ControlID="BT_Save" />
            </Triggers>
        </asp:UpdatePanel>
        <%--<asp:SqlDataSource ID="SDS_base" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
    </form>
</body>
</html>
