﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myAnnalsQry
	/// </summary>
	public class myAnnalsQry : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private string _empno;
		private string _empname;

		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		#endregion

		public string srh_login_id = string.Empty;
		public string srh_selectedyear1 = string.Empty;
		public string srh_selectedyear2 = string.Empty;
		public string srh_deptlevel = string.Empty;
		public string srh_deptlist = string.Empty;

		public string srh_conttype_code = string.Empty;
		public string srh_keyword = string.Empty;
		public string srh_compname = string.Empty;
		public string srh_source_class = string.Empty;
		public string srh_source_income = string.Empty;
		public string srh_execstatus = string.Empty;
		public string srh_body_flag = string.Empty;

		public string srh_estimfee1 = string.Empty;
		public string srh_estimfee2 = string.Empty;
		public string srh_suggestfee1 = string.Empty;
		public string srh_suggestfee2 = string.Empty;
		public string srh_contractfee1 = string.Empty;
		public string srh_contractfee2 = string.Empty;

		public string srh_registerdate1 = string.Empty;
		public string srh_registerdate2 = string.Empty;
		public string srh_presigndate1 = string.Empty;
		public string srh_presigndate2 = string.Empty;
		public string srh_actualsigndate1 = string.Empty;
		public string srh_actualsigndate2 = string.Empty;
		public string srh_stopdate1 = string.Empty;
		public string srh_stopdate2 = string.Empty;

		public string srh_successrate1 = string.Empty;
		public string srh_successrate2 = string.Empty;
		public string srh_industype = string.Empty;
		public string srh_techtype = string.Empty;
		public string srh_srvarea = string.Empty;

		public string srh_planerempno = string.Empty;
		public string srh_coplanerempno = string.Empty;
		public string srh_promodept = string.Empty;
		public string srh_promoempno = string.Empty;

		public string srh_codeptjoin = string.Empty;
		public string srh_subcontract = string.Empty;
		public string srh_buyequipment = string.Empty;
		public string srh_stockincome = string.Empty;

		public string srh_buserempno = string.Empty;

		public string srh_reportorg_flag = string.Empty;

        /// <summary>
        /// 新創事業 flag.
        /// </summary>
        public string srh_new_venture_flag = string.Empty;

        public myAnnalsQry()
		{
		}

		#region 取得 產業別 資料
		/// <summary>
		/// GetIndustryType,取得 產業別 資料
		/// </summary>
		/// <returns></returns>
		public DataSet GetIndustryType()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select code_value,code_valuedesc from engage_codetbl where code_type='102' and code_enabled=1 order by code_order";

			return this.getDataSet(oCmd, CommandType.Text);
		}
		#endregion

		#region 取得 服務地點 資料
		/// <summary>
		/// GetSrvarea,取得 服務地點 資料
		/// </summary>
		/// <returns></returns>
		public DataSet GetSrvarea()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select code_value,code_valuedesc from engage_codetbl where code_type='100' and code_enabled=1 order by code_order";

			return this.getDataSet(oCmd, CommandType.Text);
		}
		#endregion

		#region 取得「歷年洽案查詢」的列表
		/// <summary>
		/// 取得「歷年洽案查詢」的列表
		/// </summary>
		/// <returns></returns>
		public DataSet GetEngageAnnalsQuery(string ip)
		{
            SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_history_annalsquery";

			SqlParameter[] parms = {
									   new SqlParameter("@empno",			this.srh_login_id),
									   new SqlParameter("@conttype_code",	this.srh_conttype_code),
									   new SqlParameter("@keyword",			this.srh_keyword),
									   new SqlParameter("@compname",		this.srh_compname),
									   new SqlParameter("@source_class",	this.srh_source_class),
									   new SqlParameter("@source_income",	this.srh_source_income),
									   new SqlParameter("@execstatus",		this.srh_execstatus),
									   new SqlParameter("@estimfee1",		this.srh_estimfee1),
									   new SqlParameter("@estimfee2",		this.srh_estimfee2),
									   new SqlParameter("@suggestfee1",		this.srh_suggestfee1),
									   new SqlParameter("@suggestfee2",		this.srh_suggestfee2),
									   new SqlParameter("@contractfee1",	this.srh_contractfee1),
									   new SqlParameter("@contractfee2",	this.srh_contractfee2),
									   new SqlParameter("@registerdate1",	this.srh_registerdate1),
									   new SqlParameter("@registerdate2",	this.srh_registerdate2),
									   new SqlParameter("@presigndate1",	this.srh_presigndate1),
									   new SqlParameter("@presigndate2",	this.srh_presigndate2),
									   new SqlParameter("@actualsigndate1",	this.srh_actualsigndate1),
									   new SqlParameter("@actualsigndate2",	this.srh_actualsigndate2),
									   new SqlParameter("@stopdate1",		this.srh_stopdate1),
									   new SqlParameter("@stopdate2",		this.srh_stopdate2),
									   new SqlParameter("@successrate1",	this.srh_successrate1),
									   new SqlParameter("@successrate2",	this.srh_successrate2),
									   new SqlParameter("@industype",		this.srh_industype),
									   new SqlParameter("@techtype",		this.srh_techtype),
									   new SqlParameter("@srvarea",			this.srh_srvarea),
									   new SqlParameter("@planerempno",		this.srh_planerempno),
									   new SqlParameter("@coplanerempno",	this.srh_coplanerempno),
									   new SqlParameter("@promodept",		this.srh_promodept),
									   new SqlParameter("@promoempno",		this.srh_promoempno),
									   new SqlParameter("@codeptjoin",		this.srh_codeptjoin),
									   new SqlParameter("@subcontract",		this.srh_subcontract),
									   new SqlParameter("@buyequipment",	this.srh_buyequipment),
									   new SqlParameter("@stockincome",		this.srh_stockincome),
									   new SqlParameter("@selectedyear1",	this.srh_selectedyear1),
									   new SqlParameter("@selectedyear2",	this.srh_selectedyear2),
									   new SqlParameter("@deptlevel",		this.srh_deptlevel),
									   new SqlParameter("@deptlist",		this.srh_deptlist),
									   new SqlParameter("@buserempno",		this.srh_buserempno),
									   new SqlParameter("@bodyflag",		this.srh_body_flag),
									   new SqlParameter("@reportorgflag",	this.srh_reportorg_flag),
                                       new SqlParameter("@newventureflag",   this.srh_new_venture_flag),
                                       new SqlParameter("@IP",   ip)
                                   };

			oCmd.Parameters.AddRange(parms);
			return this.getDataSet(oCmd, CommandType.StoredProcedure);
		}
		#endregion

		#region 取得「歷年洽案查詢-匯出的欄位列表」
		/// <summary>
		/// 依據 empno 員工工號，取得「匯出的欄位列表」。
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public DataSet GetEngageAnnalsQueryExprot(string empno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_history_annalsquery_export";

			oCmd.Parameters.AddWithValue("@empno", empno);

			return this.getDataSet(oCmd, CommandType.StoredProcedure);
		}
		#endregion


		#region 洽案/標案預測簽約數計算
		/// <summary>
		/// 洽案/標案預測簽約數計算
		/// </summary>
		/// <returns></returns>
		public DataSet GetEngageForcastQuery(string ip)
		{
            SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_history_forecast";

			SqlParameter[] parms = {
									   new SqlParameter("@empno",			this.srh_login_id),
									   new SqlParameter("@year_start",		this.srh_selectedyear1),
									   new SqlParameter("@year_end",		this.srh_selectedyear2),
									   new SqlParameter("@org",				this.srh_deptlist),									   
									   new SqlParameter("@source_income",	this.srh_source_income),
									   new SqlParameter("@conttype",		this.srh_conttype_code),
									   new SqlParameter("@execstatus",		this.srh_execstatus),
                                       new SqlParameter("@IP",      ip)
                                   };

			oCmd.Parameters.AddRange(parms);
			return this.getDataSet(oCmd, CommandType.StoredProcedure);
		}
		#endregion

		#region 洽案/標案預測簽約數計算(匯出)
		/// <summary>
		/// 洽案/標案預測簽約數計算(匯出)
		/// </summary>
		/// <param name="empno"></param>
		/// <param name="org"></param>
		/// <param name="type"></param>
		/// <returns></returns>
		public DataSet GetEngagePreExprot(string empno,string org,string type)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_history_forecast_export";

			oCmd.Parameters.AddWithValue("@empno", empno);
			oCmd.Parameters.AddWithValue("@org", org);
			oCmd.Parameters.AddWithValue("@type", type);
			oCmd.CommandTimeout = 120;

			return this.getDataSet(oCmd, CommandType.StoredProcedure);
		}
		#endregion

	}

}