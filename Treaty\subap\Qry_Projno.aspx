﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Qry_Projno.aspx.cs" Inherits="Qry_Projno" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function close_win() {
            parent.$.fn.colorbox.close();
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <div style="padding: 15px; padding-top: 25;">
                <br />

                <asp:DropDownList ID="DDL_code2" runat="server" DataTextField="orgcd_name" DataValueField="orgcd" />
                關鍵字:<asp:TextBox ID="TB_mp" runat="server" Width="400px"></asp:TextBox>
                <asp:Button ID="BT_search" runat="server" OnClick="BT_search_Click" Text="查詢" />
                &nbsp; 
             		<cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False"
                         CellPadding="4" Width="600px"
                         GridLines="None"
                         EnableModelValidation="True" AllowPaging="True"
                         OnPageIndexChanged="SGV_company_PageIndexChanged"
                         OnPageIndexChanging="SGV_company_PageIndexChanging"
                         OnRowCreated="SGV_company_RowCreated"
                         OnRowCommand="SGV_company_RowCommand">
                         <FooterStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
                         <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                         <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
                         <Columns>
                             <asp:TemplateField HeaderText="計畫代號">
                                 <ItemTemplate>
                                     <asp:LinkButton ID="LB_comp" runat="server" CommandName="view_case" CommandArgument='<%# Server.HtmlEncode(Eval("pl_plan_no").ToString()) %>'>  <%#DataBinder.Eval(Container.DataItem, "pl_plan_no")%></asp:LinkButton>
                                 </ItemTemplate>
                                 <HeaderStyle HorizontalAlign="Left" />
                                 <ItemStyle HorizontalAlign="Left" Width="100px" Font-Size="Small" />
                             </asp:TemplateField>
                             <asp:BoundField DataField="pl_plan_name" HeaderText="公司名稱" ReadOnly="True">
                                 <HeaderStyle HorizontalAlign="Left" />
                                 <ItemStyle Width="500px" HorizontalAlign="Left" Font-Size="Small" />
                             </asp:BoundField>
                         </Columns>
                         <EmptyDataTemplate>
                             <!--當找不到資料時則顯示「查無資料」-->
                             <asp:Label ID="Label1" runat="server" ForeColor="Red" Text="查無資料!"></asp:Label>
                         </EmptyDataTemplate>
                         <FooterStyle BackColor="White" />
                         <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                         <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                         <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                     </cc1:SmartGridView>
            </div>
        </span>
        <%--        <asp:SqlDataSource ID="SDL_org" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand=" SELECT  org_orgcd AS orgcd, org_orgcd + '-' + org_abbr_chnm2 AS orgcd_name FROM  common..orgcod WHERE (org_status = 'A') and org_orgcd <>'00' " SelectCommandType="Text" >  </asp:SqlDataSource>
        <asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>

</body>
</html>
