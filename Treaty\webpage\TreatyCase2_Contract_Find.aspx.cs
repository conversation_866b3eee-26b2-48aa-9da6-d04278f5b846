﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class TreatyCase2_Contract_Find : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r;
        try
        {
            r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        }
        catch
        {
            return false;
        }
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                Binddata();
            }
        }
    }


    protected void Binddata()
    {
        //SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_search.SelectCommand = " select contno, actcontno,contname from v_pre_engage_use  where upper(actcontno) like '%'+@keyword+'%' or  upper(contname) like '%'+@keyword+'%'  ";
        //SDS_search.SelectParameters.Add("keyword",TB_KW.Text.ToUpper() );
        //for (int i = 0; i < this.SDS_search.SelectParameters.Count; i++)
        //{
        //    SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_search.DataBind();
        //SGV_search.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"select contno, actcontno,contname from v_pre_engage_use  where upper(actcontno) like '%'+@keyword+'%' or  upper(contname) like '%'+@keyword+'%'  ";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll(TB_KW.Text.ToUpper()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    protected void SGV_cop_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserView")
        {

            //SDS_Contract.InsertParameters.Clear();
            //SDS_Contract.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_Contract.InsertCommand = "esp_TreatyCase2_Contract";
            //SDS_Contract.InsertParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_Contract.InsertParameters.Add("sub_seno", "0");
            //SDS_Contract.InsertParameters.Add("contno", e.CommandArgument.ToString());
            //SDS_Contract.InsertParameters.Add("mode", "insert");
            //SDS_Contract.Insert();
            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase2_Contract";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@sub_seno", "0");
                sqlCmd.Parameters.AddWithValue("@contno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "Ins");
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            string script = "<script language='javascript'>close_win();</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata();
    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {

        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata();

    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }

        }
    }
    protected void BT_search_Click(object sender, EventArgs e)
    {
        Binddata();
    }
}