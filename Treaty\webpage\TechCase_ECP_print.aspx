﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_ECP_print.aspx.cs" Inherits="Treaty_webpage_TechCase_ECP_print" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <%--<script type="text/javascript" src="../Scripts/autoheight.js"></script>--%>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>

    <style type="text/css">
        .mask {
            z-index: 9999;
            position: fixed;
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-content: center;
            flex-wrap: wrap;
            /*background-color: #000;
            opacity: 0.5;*/
        }

        .ADV_on {
            display: inline;
        }

        .ADV_off {
            display: none;
        }
    </style>


</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="UpdatePanel1" DisplayAfter="50">
                    <ProgressTemplate>
                        <div class="mask">
                            <font color='red' style="margin: 40%"><b>
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/roller.gif" />Processing........</b></font>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
                <div class="stripeMe" style="margin-left: 15px; margin-top: 25px">
                    <div style="text-align: right; color: red;"><b>「內部機密不得外流」</b></div>
                    <div class="font-title font-bold font-size3">印表 </div>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="100" align="right">
                                <div class="font-title titlebackicon">說明</div>
                            </td>
                            <td>

                                <asp:TextBox ID="txt_reason" runat="server" class="inputex width100" MaxLength="500" Rows="5" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <div class="font-title titlebackicon">簽核人員</div>
                            </td>
                            <td>
                                <span class="stripeMe">
                                    <asp:GridView ID="gvList_signflow" runat="server" AutoGenerateColumns="False" Width="90%" CellPadding="0" CellSpacing="0" ShowHeaderWhenEmpty="True" OnRowDataBound="gvList_signflow_RowDataBound">
                                        <HeaderStyle HorizontalAlign="Center" Wrap="false" />
                                        <AlternatingRowStyle CssClass="alt" />
                                        <Columns>
                                            <asp:BoundField HeaderText="簽辦順序" DataField="簽辦順序" ItemStyle-HorizontalAlign="Center" />
                                            <asp:BoundField HeaderText="簽核角色" DataField="簽核角色" ItemStyle-HorizontalAlign="Center" />
                                            <asp:TemplateField HeaderText="簽核人員" ItemStyle-HorizontalAlign="Center">
                                                <ItemTemplate>
                                                    <asp:Label ID="lbl_Empno1" runat="server" CssClass="c_com_cname"></asp:Label>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:BoundField HeaderText="簽核時間" DataField="簽核時間" ItemStyle-HorizontalAlign="Center" />
                                            <asp:BoundField HeaderText="簽核狀態" DataField="簽核狀態" ItemStyle-HorizontalAlign="Center" />
                                            <asp:BoundField HeaderText="簽核意見" DataField="簽核意見" ItemStyle-HorizontalAlign="Center" />
                                        </Columns>
                                    </asp:GridView>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <div class="font-title titlebackicon">參考檔案</div>
                            </td>
                            <td>
                                <span class="stripeMe">
                                    <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" Width="90%" OnRowCommand="gv_data_RowCommand">
                                        <AlternatingRowStyle CssClass="alt" />
                                        <Columns>

                                            <asp:TemplateField HeaderText="文件名稱">
                                                <ItemTemplate>
                                                    <asp:LinkButton ID="LB_filename" runat="server" Text='<%# Server.HtmlEncode(Eval("檔名").ToString()) %>' CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' CommandName="xDownload"></asp:LinkButton>
                                                </ItemTemplate>
                                            </asp:TemplateField>
                                        </Columns>
                                    </asp:GridView>
                                </span>
                            </td>
                        </tr>
                    </table>
                    <div class="right" style="text-align: right">
                        <asp:Button ID="btnPrint" runat="server" CssClass="genbtnS" Text="印表" OnClick="btnPrint_Click" />
                    </div>
                </div>
            </ContentTemplate>
            <Triggers>
                <asp:PostBackTrigger ControlID="btnPrint" />
            </Triggers>
        </asp:UpdatePanel>

    </form>
</body>
</html>
