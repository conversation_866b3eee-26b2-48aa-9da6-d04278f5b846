﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Treaty_Search_Satisfaction_RPDetail" targetNamespace="http://tempuri.org/Treaty_Search_Satisfaction_RPDetail.xsd" xmlns:mstns="http://tempuri.org/Treaty_Search_Satisfaction_RPDetail.xsd" xmlns="http://tempuri.org/Treaty_Search_Satisfaction_RPDetail.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="ConnString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.ConnString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="esp_SatisfactionManager_search_gnsrTableAdapter" Name="esp_SatisfactionManager_search_gnsr">
            <MainSource>
              <DbSource ConnectionRef="ConnString (Web.config)" DbObjectName="engagedb.dbo.esp_SatisfactionManager_search_gnsr" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.esp_SatisfactionManager_search_gnsr</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="char" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@empno" Precision="0" ProviderType="Char" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="tmp_seno" DataSetColumn="tmp_seno" />
              <Mapping SourceColumn="tc_promoter_no" DataSetColumn="tc_promoter_no" />
              <Mapping SourceColumn="tc_promoter_name" DataSetColumn="tc_promoter_name" />
              <Mapping SourceColumn="tc_org_name" DataSetColumn="tc_org_name" />
              <Mapping SourceColumn="tc_handle_name" DataSetColumn="tc_handle_name" />
              <Mapping SourceColumn="tc_case_closedate" DataSetColumn="tc_case_closedate" />
              <Mapping SourceColumn="ts_friendly_yes" DataSetColumn="ts_friendly_yes" />
              <Mapping SourceColumn="ts_friendly_no" DataSetColumn="ts_friendly_no" />
              <Mapping SourceColumn="ts_friendly_normal" DataSetColumn="ts_friendly_normal" />
              <Mapping SourceColumn="ts_effective_1" DataSetColumn="ts_effective_1" />
              <Mapping SourceColumn="ts_effective_2" DataSetColumn="ts_effective_2" />
              <Mapping SourceColumn="ts_effective_3" DataSetColumn="ts_effective_3" />
              <Mapping SourceColumn="ts_effective_4" DataSetColumn="ts_effective_4" />
              <Mapping SourceColumn="ts_effective_5" DataSetColumn="ts_effective_5" />
              <Mapping SourceColumn="ts_effective_6" DataSetColumn="ts_effective_6" />
              <Mapping SourceColumn="ts_effective_6_note" DataSetColumn="ts_effective_6_note" />
              <Mapping SourceColumn="ts_satisfaction_high" DataSetColumn="ts_satisfaction_high" />
              <Mapping SourceColumn="ts_satisfaction_high_note" DataSetColumn="ts_satisfaction_high_note" />
              <Mapping SourceColumn="ts_satisfaction_yes" DataSetColumn="ts_satisfaction_yes" />
              <Mapping SourceColumn="ts_satisfaction_yes_note" DataSetColumn="ts_satisfaction_yes_note" />
              <Mapping SourceColumn="ts_satisfaction_normal" DataSetColumn="ts_satisfaction_normal" />
              <Mapping SourceColumn="ts_satisfaction_normal_note" DataSetColumn="ts_satisfaction_normal_note" />
              <Mapping SourceColumn="ts_satisfaction_no" DataSetColumn="ts_satisfaction_no" />
              <Mapping SourceColumn="ts_satisfaction_no_note" DataSetColumn="ts_satisfaction_no_note" />
              <Mapping SourceColumn="ts_satisfaction_low" DataSetColumn="ts_satisfaction_low" />
              <Mapping SourceColumn="ts_satisfaction_low_note" DataSetColumn="ts_satisfaction_low_note" />
              <Mapping SourceColumn="tmp_case_actno" DataSetColumn="tmp_case_actno" />
              <Mapping SourceColumn="ts_survery_valid" DataSetColumn="ts_survery_valid" />
              <Mapping SourceColumn="ts_process" DataSetColumn="ts_process" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Treaty_Search_Satisfaction_RPDetail" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="False">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="esp_SatisfactionManager_search_gnsr">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="tmp_seno" type="xs:long" minOccurs="0" />
              <xs:element name="tc_promoter_no" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_promoter_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_org_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_handle_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_case_closedate" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_yes" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_no" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_normal" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_1" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_2" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_3" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_4" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_5" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_6" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_6_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_high" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_high_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_yes" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_yes_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_normal" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_normal_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_no" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_no_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_low" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_low_note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_case_actno" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_valid" msdata:ReadOnly="true" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_process" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>