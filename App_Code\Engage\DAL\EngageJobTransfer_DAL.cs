﻿using System;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using DAL.Model;

namespace DAL
{
	/// <summary>
	/// EngageJobTransfer_DAL.工作移交查詢
	/// </summary>
	public class EngageJobTransfer_DAL
	{
		protected static string m_connString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnString"].ToString();

		public EngageJobTransfer_DAL()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		#region 「提供能查詢單位」資料
		/// <summary>
		/// GetOrgRightQueryByEmpno,依據 empno 員工工號，取得「提供能查詢單位」給 DropDownList_Org 使用。
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public DataSet GetOrgRightQueryByEmpno(string empno)
		{
			#region SQL
			string SQL = string.Format(@"
declare @sql_orgcd_out varchar(1000), @sql_where_out varchar(1000)
exec pr_engage_right_query @empno,@sql_orgcd_out output,@sql_where_out output

select @sql_orgcd_out = replace(@sql_orgcd_out,'org_abbr_chnm2','org_orgcd+''-''+org_abbr_chnm2 AS org_abbr_chnm2')

exec (@sql_orgcd_out)
  ");
			#endregion

			#region parms
			SqlParameter[] parms = { 
									   new SqlParameter("@empno",empno)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.Text, SQL, parms);
			return ds;
		}
		#endregion

		#region 「提供能查詢單位」資料,只提供登入者所屬單位
		/// <summary>
		/// GetOrgRightQueryByEmpno,依據 empno 員工工號，取得「提供能查詢單位」給 DropDownList_Org 使用。
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public DataSet GetOrgRightQueryByEmpnoSelf(string empno)
		{
			#region SQL
			string SQL = string.Format(@"
declare @sql_orgcd_out varchar(1000), @sql_where_out varchar(1000)
select @sql_orgcd_out = 'select org_orgcd, org_abbr_chnm2, org_status from common..comper JOIN common..orgcod ON com_orgcd = org_orgcd
where com_empno =''{0}'''
exec (@sql_orgcd_out)
select @sql_orgcd_out = replace(@sql_orgcd_out,'org_abbr_chnm2','org_orgcd+''-''+org_abbr_chnm2 AS org_abbr_chnm2')
exec (@sql_orgcd_out)", empno);
			#endregion

			#region parms
			SqlParameter[] parms = { 
									   new SqlParameter("@empno",empno)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.Text, SQL, parms);
			return ds;
		}
		#endregion


		#region GetEngageJobTransferQuery，依據 EngageJobTransfer_M
		public DataSet GetEngageJobTransferQuery(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",			theObj.srh_login_id),
									   new SqlParameter("@orgcd",			theObj.srh_orgcd),
									   new SqlParameter("@registerdate1",	theObj.srh_registerdate1),
									   new SqlParameter("@registerdate2",	theObj.srh_registerdate2),
									   new SqlParameter("@keyword",			theObj.srh_keyword),
									   new SqlParameter("@source_class",	theObj.srh_source_class),
									   new SqlParameter("@execstatus",		theObj.srh_execstatus),
									   new SqlParameter("@execdept",		theObj.srh_execdept),
									   new SqlParameter("@planerempno",		theObj.srh_planerempno),
									   new SqlParameter("@promoempno",		theObj.srh_promoempno),
									   new SqlParameter("@keyinempno",		theObj.srh_keyinempno),
									   new SqlParameter("@agency",			theObj.srh_agency)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_gpi_jobtransfer_query", parms);
			return ds;
		}
		#endregion

		#region EngageJobTransferQuery_log_insert，依據 EngageJobTransfer_M
		public DataSet EngageJobTransferQuery_log_insert(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@seqsn",			theObj.log_seqsn),
									   new SqlParameter("@infoclass",		theObj.log_infoclass),
									   new SqlParameter("@exec_empno",      theObj.log_exec_empno),
									   new SqlParameter("@exec_empname",   theObj.log_exec_empno),
									   new SqlParameter("@adjustclass",		theObj.log_adjustclass),
									   new SqlParameter("@postadjust_empno",theObj.log_postadjust_empno)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_gpi_jobtransfer_log_insert", parms);
			return ds;
		}
		#endregion

		public DataSet GetEngageJobTransferReQuery(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_gpi_jobtransfer_Requery", parms);
			return ds;
		}

		public DataSet GetEngageJobTransferQuery_Selected(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",			theObj.srh_login_id)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_selected", parms);
			return ds;
		}


		public void EngageJobTransferQuery_Update(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_update", parms);
		}


		public void EngageJobTransferQuery_SelectAll(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_SelectAll", parms);
		}


		public DataSet EngageJobTransferQuery_Checked(EngageJobTransfer_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_Checked", parms);
			return ds;
		}

		#region CheckAuthority, 是否為 "單位系統管理人" 或 "總系統負責人"
		/// <summary>
		/// 是否為 "單位系統管理人" 或 "總系統負責人"
		/// 是 : return true, 否: return false.
		/// Add by Hugo at the 2007/08/29
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public bool CheckAuthority(string empno)
		{
			string strSQL = @"
                declare @rtn_code1 varchar(1), @rtn_code2 varchar(1), @msg1  varchar(1000), @msg2  varchar(1000)
                exec  pr_engage_right_sql  @empno, @rtn_code1 output, @rtn_code2 output, @msg1 output, @msg2 output
                select @rtn_code2  
			";

			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		empno)
								   };
			#endregion

			object retValue = SqlHelper.ExecuteScalar(m_connString, CommandType.Text, strSQL, parms);
			return (retValue.ToString() == "1");
		}
		#endregion


		#region GetEngageJobTransferQueryTreaty，依據 EngageJobTransfer_Treaty_M
		public DataSet GetEngageJobTransferQueryTreaty(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",			theObj.srh_login_id),
									   new SqlParameter("@orgcd",			theObj.srh_orgcd),
									   new SqlParameter("@casetype",	    theObj.srh_source_class),
									   new SqlParameter("@registerdate1",	theObj.srh_registerdate1),
									   new SqlParameter("@registerdate2",	theObj.srh_registerdate2),
									   new SqlParameter("@degree",          theObj.srh_degree),
									   new SqlParameter("@keyword",			theObj.srh_keyword),									   
									   new SqlParameter("@handlempno",		theObj.srh_handlempno),
									   new SqlParameter("@promoempno",		theObj.srh_promoempno),
									   new SqlParameter("@keyinempno",		theObj.srh_keyinempno),
									   new SqlParameter("@agency",			theObj.srh_agency)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_treaty_jobtransfer_query", parms);
			return ds;
		}
		#endregion

		public DataSet GetEngageJobTransferReQueryTreaty(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_treaty_jobtransfer_Requery", parms);
			return ds;
		}

		public void EngageJobTransferQueryTreaty_Update(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_treaty_update", parms);
		}

		public void EngageJobTransferQuery_Treaty_SelectAll(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_treaty_SelectAll", parms);
		}

		public DataSet EngageJobTransferQuery_Treaty_Checked(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@empno",		theObj.srh_login_id),
									   new SqlParameter("@seqsn",		theObj.log_seqsn),
									   new SqlParameter("@infoclass",	theObj.log_infoclass),
									   new SqlParameter("@selected",	theObj.log_selected)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_tmp_jobtransfer_query_treaty_Checked", parms);
			return ds;
		}

		#region EngageJobTransferQuery_treaty_log_insert，依據 EngageJobTransfer_Treaty_M
		public DataSet EngageJobTransferQuery_treaty_log_insert(EngageJobTransfer_Treaty_M theObj)
		{
			#region parms
			SqlParameter[] parms = {
									   new SqlParameter("@seqsn",			theObj.log_seqsn),
									   new SqlParameter("@infoclass",		theObj.log_infoclass),
									   new SqlParameter("@exec_empno",      theObj.log_exec_empno),
									   new SqlParameter("@exec_empname",   theObj.log_exec_name),
									   new SqlParameter("@adjustclass",		theObj.log_adjustclass),
									   new SqlParameter("@postadjust_empno",theObj.log_postadjust_empno)
								   };
			#endregion

			DataSet ds = SqlHelper.ExecuteDataset(m_connString, CommandType.StoredProcedure, "pr_eng_gpi_jobtransfer_treaty_log_insert", parms);
			return ds;
		}
		#endregion
	}
}
