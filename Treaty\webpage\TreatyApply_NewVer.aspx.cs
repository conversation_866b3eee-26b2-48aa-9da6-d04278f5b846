﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

public partial class Treaty_webpage_TreatyApply_NewVer : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        ViewState["contno"] = "";
        if (Request.QueryString["contno"] != null)
        {
            if (!IsNatural_Number(Request.QueryString["contno"].Replace("-", "")) || (Request.QueryString["contno"].Length == 0) || (Request.QueryString["contno"].Length > 15))
                Response.Redirect("../danger.aspx");
            ViewState["contno"] = Request.QueryString["contno"].ToString();
        }
        if (ViewState["contno"] == null)
            Response.Redirect("../danger.aspx");
        //databinding("caseno", "ASC");
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.SelectCommand = "esp_treaty_new_version_flag";
        //this.SDS_SC.SelectParameters.Add("actno", ViewState["contno"].ToString());
        //this.SDS_SC.SelectParameters.Add("new_ver_flag", "0");
        //for (int i = 0; i < this.SDS_SC.SelectParameters.Count; i++)
        //{
        //    SDS_SC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_SC.DataBind();
        //System.Data.DataView dv_contnoToseno = (DataView)SDS_SC.Select(new DataSourceSelectArguments());


        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_new_version_flag";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("actno", oRCM.SQLInjectionReplaceAll(ViewState["contno"].ToString()));
            sqlCmd.Parameters.AddWithValue("new_ver_flag", "0");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_contnoToseno = dt.DefaultView;
        if (dv_contnoToseno.Count >= 1)
        {
            if (dv_contnoToseno[0][0].ToString() == "")
            {
                Response.Redirect("../danger.aspx");
            }
            else
            {
                switch (dv_contnoToseno[0][0].ToString())
                {
                    case "3": LT_Ver.Text = "屬原契約新議版本再次審查需求，請按「確定」。<br>屬一案多契約之新契約需求，請按「新增相關契約」。"; break;
                    case "2": LT_Ver.Text = "目前所選取的版次(可能存在於草稿中)，無法產生新件次，只能執行新版次。"; spNewCase.Visible = false; break;
                    case "1": LT_Ver.Text = "屬原契約新議版本再次審查需求，請按「確定」。"; spNewVer.Visible = false; break;
                    case "0": LT_Ver.Text = "不能執行新版件。"; spNewVer.Visible = false; spNewCase.Visible = false; break;
                }
            }
        }
    }
}

