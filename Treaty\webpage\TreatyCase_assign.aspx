﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_assign.aspx.cs" Inherits="Treaty_webpage_TreatyCase_assign" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <title>案件承辦指派</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />

    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />

    <script type="text/javascript">
        $(document).ready(function () {
            //if ($('#__EVENTTARGET').length <= 0 && $('#__EVENTARGUMENT').length <= 0) {
            //    $('#YOUR_ASPNET_FORMID').prepend('<input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="" /><input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="" />');
            //}

            //if (typeof __doPostBack == 'undefined') {
            //    __doPostBack = function (eventTarget, eventArgument) {
            //        object
            //        var theForm = document.forms['YOUR_ASPNET_FORMID'];
            //        if (!theForm) {
            //            theForm = document.YOUR_ASPNET_FORMID;
            //        }
            //        if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
            //            theForm.__EVENTTARGET.value = eventTarget;
            //            theForm.__EVENTARGUMENT.value = eventArgument;
            //            theForm.submit();
            //        }
            //    };
            //}

        });

        $(function () {
            $('a.iterm_dymanic_caseInfo').cluetip({
                tracking: true,
                sticky: true, width: '700px', showTitle: false, arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png" alt="" />',
                positionBy: 'fixed', topOffset: '5',
                ajaxSettings: { type: "POST" }
            });
        });
        function close_win() {
            alert("指派成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style>
        .ui-cluetip-header, .ui-cluetip-content {
            overflow: auto;
            max-heisht: 4em;
        }
    </style>
</head>
<body>

    <form id="form1" runat="server">
        <span class="stripeMe">
            <asp:RadioButtonList ID="RBL_1" runat="server" RepeatDirection="Horizontal" AutoPostBack="True" OnSelectedIndexChanged="RBL_1_SelectedIndexChanged" Style="border: 0px" Height="15px" Visible="false">
                <asp:ListItem Value="2" Selected="True">全部法務人員</asp:ListItem>
            </asp:RadioButtonList>

            <table style="margin-left: 15px; margin-top: 25px">
                <tr>
                    <td>
                        <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorting="SGV_log_Sorting" OnSorted="SGV_log_Sorted">
                            <FooterStyle BackColor="White" />
                            <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                            <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                            <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                            <Columns>
                                <asp:TemplateField HeaderText="承辦人">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LB_emp" runat="server" CommandName="CaseAssign" CommandArgument='<%# Eval("emp_no") %>' Text='<%# Server.HtmlEncode(Eval("emp_name").ToString()) %>' />
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Center" Width="45px" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="未結件契約">
                                    <ItemTemplate>
                                        <asp:Literal ID="LT_unX" runat="server"></asp:Literal>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="未結件糾紛案">
                                    <ItemTemplate>
                                        <asp:Literal ID="LT_unQ" runat="server"></asp:Literal>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="未結件法律問題研究">
                                    <ItemTemplate>
                                        <asp:Literal ID="LT_unL" runat="server"></asp:Literal>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:TemplateField>
                                <asp:BoundField DataField="f_7" HeaderText="7日內已完成件數" SortExpression="f_7">
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:BoundField>
                                <asp:BoundField DataField="f_30" HeaderText="30日內已完成件數" SortExpression="f_30">
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:BoundField>

                                <asp:BoundField DataField="mc" HeaderText="本月份已完成成案數">
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:BoundField>
                                <asp:BoundField DataField="cyt" HeaderText="本年度總案數">
                                    <ItemStyle HorizontalAlign="Right" />
                                </asp:BoundField>
                            </Columns>
                            <EmptyDataTemplate>
                                <!--當找不到資料時則顯示「無資料」-->
                                <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                            </EmptyDataTemplate>
                            <FooterStyle BackColor="White" />
                            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Center" VerticalAlign="Bottom" Font-Size="Small" />
                        </cc1:SmartGridView>
                        <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />--%>
                        <hr />
                    </td>
                </tr>

            </table>
        </span>
    </form>

</body>
</html>
