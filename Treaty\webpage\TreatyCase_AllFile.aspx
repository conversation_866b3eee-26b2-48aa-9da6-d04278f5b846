﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_AllFile.aspx.cs" Inherits="Treaty_webpage_TreatyCase_AllFile" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        var p = navigator.platform;

    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateTemplateFields="False" CellPadding="4" GridLines="None" Style="margin-left: 10px; margin-top: 30px;"
                OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorted="SGV_log_Sorted" OnSorting="SGV_log_Sorting" AutoGenerateColumns="False" Width="850px">
                <HeaderStyle CssClass="fixedheadertable" />
                <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                <Columns>
                    <asp:BoundField DataField="tmp_caseno" SortExpression="tmp_caseno" HeaderText="案號">
                        <ItemStyle HorizontalAlign="Left" Width="125px" />
                    </asp:BoundField>
                    <asp:TemplateField SortExpression="tcdf_doc" HeaderText="附件名稱">
                        <ItemTemplate>
                            <asp:LinkButton runat="server" ID="lbxOpenFile" CommandName="dl" Text='<%#DataBinder.Eval(Container.DataItem,"tcdf_doc")%>' CommandArgument='<%#DataBinder.Eval(Container.DataItem,"tmp_seno")+";"+DataBinder.Eval(Container.DataItem,"tcdf_no")%>' CausesValidation="false"> </asp:LinkButton>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Left" Width="300px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="tcdf_up_date" SortExpression="tcdf_up_date" HeaderText="上傳日期" DataFormatString="{0:yyyyMMdd}">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle Width="55px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcdf_filetxt" SortExpression="tcdf_filetxt" HeaderText="修改概要"></asp:BoundField>
                    <asp:TemplateField HeaderText="常用&lt;br&gt;版本" SortExpression="tcdf_up_flag_desc">
                        <ItemTemplate>
                            <asp:Label ID="Label2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle Width="35px" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="tcdf_up_empname" SortExpression="tcdf_up_empname" HeaderText="撰寫人">
                        <ItemStyle HorizontalAlign="Center" Width="50px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tcdf_inspect_name" SortExpression="tcdf_inspect" HeaderText="送審" ItemStyle-HorizontalAlign="Center">
                        <ItemStyle HorizontalAlign="Center" Width="40px"></ItemStyle>
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="申請&lt;br&gt;承辦單" SortExpression="tmp_type">
                        <ItemTemplate>
                            <asp:Label ID="Label1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_type_name").ToString()) %>'></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle Width="80px" />
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:TemplateField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
            </cc1:SmartGridView>
        </span>
        <%--        <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
