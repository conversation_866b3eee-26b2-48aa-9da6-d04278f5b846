﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
    <add name="ConnString" connectionString="Data Source=140.96.1.105,5555;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient"/>

  -->
<configuration>
	<connectionStrings>
    <!--
    <add name="CS_treaty"  connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient"/>
    <add name="common"     connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient"/>
    <add name="ConnString" connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient"/>


 -->    
     <add name="CS_treaty"  connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@20250308@**********" providerName="System.Data.SqlClient"/>    
    <add name="CS_treaty"  connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@20250308@**********" providerName="System.Data.SqlClient"/>
    <add name="common"     connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@20250308@**********" providerName="System.Data.SqlClient"/>
    <add name="pubbs"      connectionString="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@20250308@**********" providerName="System.Data.SqlClient"/>
    <add name="infomgtConn" connectionString="Data Source=SQL2K5T.ITRI.ds,5555;Initial Catalog=infomgt;User ID=pubinfomgt;Password=************************" providerName="System.Data.SqlClient"/>
	</connectionStrings>


	<system.web>
		<compilation debug="true" targetFramework="4.5">
			<assemblies>
				<add assembly="System.Data.Linq, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
			</assemblies>
		</compilation>
                <httpRuntime maxRequestLength="100000" executionTimeout="6000"  requestValidationMode="2.0"/> 
	</system.web>
	
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="**********"/> 
      </requestFiltering>
    </security>
	  <validation validateIntegratedModeConfiguration="false"/>
  </system.webServer>


  <appSettings>
    <add key="ServerSite" value="test"/>      <!--1.FAQ系統環境專用：test or online-->
    <add key="sysCode" value="F2-29"/>        <!--2.FAQ系統環境專用:向FAQ系統負責人(淑娟)申請取得的系統代碼，且必須為有效系統代碼才會記錄到FAQ。(若暫不寫入FAQ時，請給空字串。)-->
    <add key="CloseSendMail" value="close"/>  <!--4.安全掃描時，須關閉信件通知功能(open or close)-->
    <add key="ErrorPage" value="~/ExceptionMessage.aspx"/><!--5.錯誤時導向的頁面-->
    <add key="Language" value="false"/>       <!--6.是否有多國語言設定。注意：系統需有自己的使用者語言紀錄表(工號、語系)，才可使用為true。-->
    
    <add key="TestEmpNo"          value="880583"/>
		<!--  A70198 蘇桓玉  B10556 桂嫚婷  A20292 廖紫婷  A50612 李欣唐  鄭琳文B00159　980159 李怡秋　770687 賴世卿  B10124 王偉霖  模擬 Admin EmpNo(總系統負責人)，以開啟某些功能。 -->
    <add key="AdminEmpNo"         value="880583" /><!-- 有關年報查詢功能樊組長指示僅開放部分同仁查詢，以開啟某些功能。(洪寧、廖宜庭、陳奕伶) -->
    <add key="InnerReportEmpNo"   value="523439,880583" />
    <add key="ConnStringNDA"      value="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=ndadb;User ID=pubndadm;Password=***********************"/>
    <add key="ConnStringContract" value="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=contract;User ID=pubcontusr;Password=********************"/>
    <add key="ConnStringPatent"   value="Data Source=ITRIDPS.ITRI.ds,2830;Initial Catalog=patent;User ID=pubptadm;Password=********************"/>
 
    <add key="ChangeDB"           value="Y"/>
    <!-- 標案系統-標案附件檔路徑, 測試區:"http://itrietc.itri.org.tw/ontest_gpi/", 上線區："http://itrietc.itri.org.tw/gpi/"  -->

    <add key="gpi_files"          value="http://itrietc.itri.org.tw/ontest_gpi/" />
    <add key="FilePathString"     value="D:\\temp\treaty" /> 
 <!-- 設定議約上傳檔案路徑 
 	  <add key="FilePathString"     value="\\itri.ds\intratest\webdev5_amps" />
	  <add key="FilePathString"     value="D:\\temp\treaty" />
 

	  -->
    <!--<add key="FilePathString"     value="\\nas-2\Intraweb\up_ap9-amps\treaty" /> --><!-- 設定議約上傳檔案路徑  -->
    <add key="ReportServicePath"  value="http://Repsdev/ReportServer?/Engage" /><!-- Reporting Service Path -->
 <!--   
    <add key="EngageURL"          value="https://amps.itri.org.tw" />
    <add key="TreatyURL"          value="https://amps.itri.org.tw" />
    <add key="NDAURL"             value="https://itriap9.itri.org.tw" />  
    <add key="UNURL"              value="https://itriap9.itri.org.tw" /> 
    <add key="ONURL"              value="https://itriap9.itri.org.tw" />      
    <add key="gpi_files"          value="http://itrietc.itri.org.tw/ontest_gpi/" />
    <add key="customer_url"       value="https://itriap7.itri.org.tw/comnwebap/web_page" />
    <add key="CustAdd"            value="https://itriap7.itri.org.tw/cust/mgrcust_custadd.aspx" />
    <add key="Retaddr"            value="https://amps.itri.org.tw/treaty/subap/colorbox_close.aspx" />
-->   
   
    <add key="EngageURL"          value="https://amps.itri.org.tw" />
    <add key="TreatyURL"          value="https://amps.itri.org.tw" />

    <add key="NDAURL"             value="https://norcont.itri.org.tw" /> <!-- 檢視NDA的 網站位址  -->
    <add key="UNURL"              value="https://norcont.itri.org.tw/norcont" /><!-- 檢視國外契約的 網站位址  -->
    <add key="ONURL"              value="https://norcontin.itri.org.tw" /> <!-- 檢視國內契約的 網站位址  -->
    <add key="IndusURL"           value="https://indus.itri.org.tw" /><!-- 檢視國外契約的 網站位址  -->
    <add key="CURL"               value="https://indus.itri.org.tw" /> <!-- 檢視工服 網站位址  -->
    <add key="gpi_files"          value="http://itrietc.itri.org.tw/ontest_gpi/" />
    <add key="customer_url"       value="https://itriap7.itri.org.tw/comnwebap/web_page" />
    <add key="CustAdd"            value="https://itriap7.itri.org.tw/cust/mgrcust_custadd.aspx" />
    <add key="Retaddr"            value="https://amps.itri.org.tw/treaty/subap/colorbox_close.aspx" />   
   
    <!-- Reporting Service Path <add key="ReportServicePath" value="http://Repsprod/ReportServer?/Engage" />  -->
    
    <!-- Reporting Service Path -->
    <add key="ReportServicePath" value="http://Repsprod/ReportServer?/Engage" />   
    <add key="TREATY.TREATY01" value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/TREATY/TREATY01.asmx"/>
    <add key="TREATY.TREATY02" value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/TREATY/TREATY02.asmx"/>
    <add key="TREATY.sysid" value="3114"/>
    <add key="TREATY.secureid" value="107FDD0D-65B9-4CB8-9C88-45CBD9E6F01A"/>
    <add key="TREATY.CompareService1" value="https://webdev5.itri.org.tw/doccompare/DocCompareWS/CompareService1.asmx"/>
  </appSettings>


</configuration>
