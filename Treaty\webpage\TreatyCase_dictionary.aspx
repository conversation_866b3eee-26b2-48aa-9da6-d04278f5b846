﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_dictionary.aspx.cs" Inherits="TreatyCase_dictionary" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/clipboard.min.js"></script>
    <script>
        var clipboard = new Clipboard('.btn');


        // when text is copied into clipboard use it
        clipboard.on('success', function (e) {
            //$('#log').text('Text copied into clipboard is: <' + e.text + '>');
            //e.clearSelection();
        });
    </script>
    <style>
        .wrap {
            margin-left: 5px;
            margin-right: auto;
            margin-top: 25px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <div class="wrap">
            <div class="content">
                <span class="stripeMe">
                    <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None"
                        OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorted="SGV_log_Sorted" OnSorting="SGV_log_Sorting">
                        <HeaderStyle CssClass="fixedheadertable" />
                        <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                        <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                        <RowStyle BorderColor="Black" BorderWidth="1px" />
                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                        <Columns>
                            <asp:BoundField DataField="情境" HeaderText="情境" HeaderStyle-HorizontalAlign="Center">
                                <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                                <ItemStyle HorizontalAlign="Left" Width="150px" />
                            </asp:BoundField>

                            <asp:TemplateField HeaderText="常用法律意見">
                                <ItemTemplate>
                                    <asp:Label ID="LB_常用法律意見" runat="server" Text='<%# Server.HtmlEncode(Eval("常用法律意見").ToString()) %>'></asp:Label>
                                </ItemTemplate>
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemStyle HorizontalAlign="Left" Width="550px" Font-Size="Medium" />
                            </asp:TemplateField>
                            <asp:TemplateField HeaderText="">
                                <ItemTemplate>
                                    <button class='btn' data-clipboard-action="copy" data-clipboard-target="#<%# ((GridViewRow)Container).FindControl("LB_常用法律意見").ClientID %>">
                                        <img src='../images/clippy.png' alt='Copy to clipboard'></button>
                                </ItemTemplate>
                                <HeaderStyle HorizontalAlign="Center" />
                                <ItemStyle HorizontalAlign="Left" Width="30px" />
                            </asp:TemplateField>
                        </Columns>
                        <EmptyDataTemplate>
                            <!--當找不到資料時則顯示「無資料」-->
                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                        </EmptyDataTemplate>
                        <FooterStyle BackColor="White" />
                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                    </cc1:SmartGridView>
                </span>

            </div>
        </div>
        <p id="log"></p>
    </form>
    <%--  <asp:SqlDataSource ID="SDS_dictionary" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"  /> --%>
</body>
</html>
