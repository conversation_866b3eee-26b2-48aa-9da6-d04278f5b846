﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_Contract_Find.aspx.cs" Inherits="TreatyCase2_Contract_Find" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>新增契約</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>

    <script type="text/javascript">
        function close_win() {
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .auto-style1 {
            width: 800px;
        }
    </style>



</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 25px; border: 0px">
                <tr>
                    <td class="auto-style1">關鍵字：
                        <asp:TextBox ID="TB_KW" runat="server" class="inputsizeS"></asp:TextBox>&nbsp;
                         
                        <asp:Button ID="BT_search" runat="server" Text="查詢" class="genbtnS" OnClick="BT_search_Click" />
                    </td>
                </tr>
                <tr>
                    <td class="auto-style1">
                        <span class="stripeMe">
                            <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCommand="SGV_search_RowCommand" OnRowCreated="SGV_search_RowCreated">
                                <FooterStyle BackColor="White" />
                                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                <Columns>
                                    <asp:TemplateField HeaderText="契約編號">

                                        <ItemStyle HorizontalAlign="Left" Width="80px"></ItemStyle>
                                        <ItemTemplate>
                                            <asp:LinkButton ID="LB_contno" runat="server" CommandName="UserView" CommandArgument='<%# Eval("contno")  %>' Text='<%#   Server.HtmlEncode(Eval("actcontno").ToString()) %>'> </asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="contname" HeaderText="契約名稱">
                                        <ItemStyle HorizontalAlign="Left" Width="360px" />
                                    </asp:BoundField>

                                </Columns>
                                <EmptyDataTemplate>
                                    <!--當找不到資料時則顯示「無資料」-->
                                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="查無資料!"></asp:Label>
                                </EmptyDataTemplate>
                                <FooterStyle BackColor="White" />
                                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                            </cc1:SmartGridView>

                            <%--<asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_Contract %>" />
                            <asp:SqlDataSource ID="SDS_Contract" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                        </span>
                        <div style="display: none">
                            <asp:TextBox ID="TB_empNo" runat="server"></asp:TextBox>
                            <asp:TextBox ID="TB_empName" runat="server"></asp:TextBox>

                        </div>

                    </td>
                </tr>
            </table>

        </span>
    </form>
</body>
</html>
