﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCaseQ_modify.aspx.cs" Inherits="TreatyCaseQ_modify" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="../userControl/Foot.ascx" TagPrefix="uc2" TagName="Foot" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/jquery-migrate-1.2.1.js"></script>
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        function viewCase(seno) {
            var url = './TreatyCaseQ_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
          , title: '相關客戶契約資料'
          , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
          }
            });
        }
        function treaty_Inspect(seno, tci_no) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Inspect.aspx?seno=" + seno + "&tci_no=" + tci_no
          , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("Inspect_renew", 0);
          }
            });
        }
        function treaty_fileup(contno, seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileUp.aspx?contno=" + contno + "&seno=" + seno
          , title: '檔案上傳'
          , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("file_renew", 0);
          }
            });
        }
        function DeleteCase() {
            alert("案件已刪除!");
            location.replace("./default.aspx");
        }
        function treatyCancle(seno) {
            if (confirm('確定要取消需求?\\t <<取消前請知會法務人員>> ')) {
                $(".ajax_mesg").colorbox({
                    href: "./TreatyCase_Cancle.aspx?seno=" + $("#DDL_SeqSn").val()
              , title: '議約需求取消'
              , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
              , onClosed: function () {
                  $('html, body').css('overflow', '');
                  reflash_topic("case_renew", 0);
              }
                });
            }
        }
        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url,
                title: '挑選客戶'
               , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
               , onClosed: function () {
                   $('html, body').css('overflow', '');
                   var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                   $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

               }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileModify.aspx?fid=" + fid
            , title: '上傳檔案資料維護'
            , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
            , onClosed: function () {
                $('html, body').css('overflow', '');
                reflash_topic("file_renew", 0);
            }
            });
        }
        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assignInspect.aspx?seno=" + seno
          , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("Inspect_renew", 0);
          }
            });
        }
        function SendInspect(seno) {
            alert("案件已送審!\n法律問題第一個審查人預設直接PASS! ");
            location.replace("./TreatyCaseQ_view.aspx?seno=" + seno);
        }
        function treaty_defert(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Defer.aspx?seno=" + seno
              , title: '議約展延'
          , iframe: true, width: "750px", height: "350px", transition: "none", opacity: "0.5", overlayClose: false
          , onClosed: function () {
              $('html, body').css('overflow', '');
              reflash_topic("Defer_renew", 0);
          }
            });
        }
        function EndCase(seno) {
            alert('結案完成');
            location.replace('./TreatyCaseQ_view.aspx?seno=' + seno);
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" /></div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" /></div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_tratycase_info" runat="server">案件紀錄</asp:Literal>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="td_right" colspan="5">
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print" Text="列印" OnClick="BT_Print_Click" Visible="False" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Tag" Text="列印檔案標籤" OnClick="BT_Print_Tag_Click" Visible="False" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Excel" Text="列印檔案標籤_Excel" OnClick="BT_Print_Excel_Click" Visible="False" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btnDelete" Text="刪除" OnClick="btnDelete_Click" Visible="False" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit" Text="存檔" OnClick="btEdit_Click" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_End" Text="結案" OnClick="BT_End_Click" Visible="False" />
                                        </td>
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label></div>
                                        </td>
                                        <td class="width35">
                                            <asp:DropDownList ID="DDL_SeqSn" runat="server" Width="160px"  DataTextField="contno" DataValueField="seno" AutoPostBack="True" OnSelectedIndexChanged="DDL_SeqSn_SelectedIndexChanged"></asp:DropDownList>
                                            <asp:Label ID="txtComplexNo" runat="server" Text="" Visible="false"></asp:Label>
                                            (舊案號:
                                            <asp:Label ID="txtOldContno" runat="server"></asp:Label>)
                        
                        <%--<asp:SqlDataSource ID="SDS_DDL_SeqSn" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <asp:PlaceHolder ID="案件語文" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                            </td>
                                            <td class="width40">
                                                <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" class="radio-input" Checked="True" />
                                                <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" class="radio-input" />

                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label>&nbsp;
                                            <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server" Width="95px"></asp:Label>
                                            &nbsp;
                       分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server" Width="110px"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="LB_hec_flag" runat="server" Text="【重大效益案件】<br />" Font-Bold="True" ForeColor="Red" Visible="false"></asp:Label>
                                            <asp:TextBox ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <div class="left">
                                                    <asp:Button ID="BT_Customer" runat="server" class="ajax_mesg genbtnS" Text="新增" /></div>
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None"  OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <HeaderStyle Width="40px" ForeColor="Black"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'>刪除</asp:LinkButton>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="退換票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_退換票" runat="server" Text='<%# Server.HtmlEncode(Eval("tmp_退換票").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資本額逾½">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資本額" runat="server" Text='<%# Server.HtmlEncode(Eval("tmp_資本額").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資產遭查封">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資產遭查封" runat="server" Text='<%# Server.HtmlEncode(Eval("tmp_資產遭查封").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="抽換票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_抽換票" runat="server" Text='<%# Server.HtmlEncode(Eval("tmp_抽換票").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="20px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商編號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_company" runat="server" Text='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_cname" HeaderText="廠商中文名稱">
                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_ename" HeaderText="廠商英文名稱">
                                                                <HeaderStyle Width="350px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            案件性質</td>
                                        <td class="lineheight03">

                                            <asp:DropDownList ID="DDL_case_style" runat="server" Width="190px"  DataTextField="subtype_desc" DataValueField="code_subtype" AutoPostBack="True" OnSelectedIndexChanged="DDL_case_style_SelectedIndexChanged" RepeatDirection="Horizontal" RepeatColumns="2"></asp:DropDownList>
                                            <%--<asp:SqlDataSource ID="SDS_case_style" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <asp:PlaceHolder ID="案件分類" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">案件分類</div>
                                            </td>
                                            <td>
                                                <asp:DropDownList ID="ddlContType" runat="server" Width="200px"  DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True"></asp:DropDownList>
                                                <%--<asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>

                                    <tr>
                                        <asp:PlaceHolder ID="預定期間" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">預定期間</div>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txt_contsdate" runat="server" class="pickdate inputex inputsizeS text-input" runat="server" Width="80px" MaxLength="8" />&nbsp;至&nbsp; 
                   <asp:TextBox ID="txt_contedate" runat="server" class="pickdate inputex inputsizeS text-input" runat="server" Width="80px" MaxLength="8"></asp:TextBox>
                                            </td>
                                        </asp:PlaceHolder>
                                        <asp:PlaceHolder ID="契約預估金額" runat="server">
                                            <td align="right">
                                                <div class="font-title titlebackicon">契約預估金額</div>
                                            </td>

                                            <td>
                                                <asp:DropDownList ID="ddlContMoneyType" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" />&nbsp;
                    <%--<asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  " />--%>
                                                <asp:TextBox ID="txtContMoney" runat="server" class="inputex inputsizeS text-input" />
                                                &nbsp;元
                                            </td>

                                        </asp:PlaceHolder>
                                    </tr>

                                    <tr runat="server" id="案號股別" visible="false">
                                        <td align="right">
                                            <div class="font-title titlebackicon">案號股別</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="TB_案號股別" runat="server" Width="608px" class="inputex inputsizeS text-input" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">案件緣由與目的</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txtSignReason" runat="server" Width="608px" TextMode="MultiLine" Height="60px"></asp:TextBox>
                                            <asp:Image ID="img_txtSignReason" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_SignReason" />
                                        </td>
                                    </tr>


                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人意見彙整</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:TextBox ID="txt_betsum" runat="server" Width="608px" TextMode="MultiLine" Height="60px"></asp:TextBox>
                                            <asp:Image ID="Image2" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_betsum" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務備註</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:TextBox ID="txtManageNote" runat="server" Width="608px" TextMode="MultiLine" Height="60px"></asp:TextBox>
                                            <asp:Image ID="Image4" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Button class="ajax_mesg genbtnS" ID="BT_FileUp" runat="server" Text="檔案上傳"></asp:Button>
                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" >
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="功能">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_tcdf_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                                                <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>刪除</asp:LinkButton>
                                                                <asp:LinkButton ID="LB_edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>維護</asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                ◆<asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="修改概要">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="280px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="審查">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_inspect" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_inspect").ToString()) %>'></asp:Label>
                                                                <asp:Label ID="LB_tcdf_type" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_type").ToString()) %>' Visible="false"></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="30px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="常用版本">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_3" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="50px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>


                                    <asp:PlaceHolder ID="PL_Inspect" runat="server">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">審查資訊</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:CheckBox ID="CB_NotInspect" runat="server" Text="文件不須審查" Visible="false" />
                                                <asp:Button ID="BT_AddInspect" runat="server" Text="新增審查人" class="ajax_mesg genbtnS" />
                                                <asp:Button ID="BT_SendInspect" runat="server" Text="送出審查" class="genbtnS" OnClick="BT_SendInspect_Click" Visible="false" />
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False"  OnRowDataBound="GV_Inspect_RowDataBound" OnRowCommand="GV_Inspect_RowCommand" Width="650px">
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_tci_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tci_no").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Server.HtmlEncode(Eval("tci_no").ToString()) %>'>刪除</asp:LinkButton>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="順序">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="Lb_order" runat="server" Text='<%# Server.HtmlEncode(Eval("tci_order").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="50px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tci_inspect_desc" HeaderText="簽核意見">
                                                                <ItemStyle Width="350px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="簽核狀態">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_Istatus" runat="server" Text='<%# Server.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="85px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tci_inspect_time" HeaderText="簽核日期">
                                                                <ItemStyle HorizontalAlign="Left" Width="250px" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Inspect" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                                                    <asp:SqlDataSource ID="SDS_Inspect_count" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_tc_manage_note" runat="server">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">預估完成日展延</div>
                                            </td>
                                            <td colspan="3">
                                                <div class="left">
                                                    <asp:Button ID="BT_defert" runat="server" Text="新增展延" class="ajax_mesg genbtnS" />
                                                </div>
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Defer" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" >
                                                        <Columns>
                                                            <asp:BoundField DataField="tcd_defer_date" HeaderText="展延後預估完成日">
                                                                <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_keyin_date" HeaderText="提出展延日">
                                                                <ItemStyle HorizontalAlign="Center" Width="120px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_desc" HeaderText="展延原因">
                                                                <ItemStyle Width="530px" HorizontalAlign="Left" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無展延! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Defer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin5TB">
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_infoHandel" runat="server">歷次承辦人資訊</asp:Literal>
                                    &nbsp;&nbsp;
                        <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_historyRecord" runat="server">歷次修改紀錄</asp:Literal><a href="#dialog06" class="inlineS"></a>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">分案主管</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件/分案日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>&nbsp;&nbsp;/&nbsp;&nbsp;<asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>|<asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">進度</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal><asp:DropDownList ID="DDL_Degree" runat="server" Visible="false"  DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_DDL_degree" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">預估完成日</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_expect_close_date" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">處理天數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_process_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">產出文件數</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_contract_count" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">需求結件日</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_case_closedate" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改人</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>|<asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->

                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc2:Foot runat="server" ID="Foot" />


<%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            tinymce.init({
                selector: '#txt_betsum',
                width: "800",
                height: "500",
                language: 'zh_TW',
                //font_formats: "新細明體=Microsoft JhengHei;細明體=PMingLiU;標楷體=MingLiU;微軟正黑體=微軟正黑體 ;Arial=arial,helvetica,sans-serif;",| fontselect 
                fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 24pt 36pt",
                //content_css: 'css/content.css',
                content_css: '../Scripts/tinymce/skins/ui/oxide/content.min.css',
                toolbar: "insertfile undo redo | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor | fontsizeselect",
                statusbar: false,
                plugins: [' code', 'textcolor'],
            });


            $(document).ready(function () {
                $(".help_betsum").attr("title", $("#txt_betsum").val());
                $(".help_betsum").attr("class", "itemhint");
                $(".help_SignReason").attr("title", $("#txtSignReason").val());
                $(".help_SignReason").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txtManageNote").val());
                $(".help_manage_note").attr("class", "itemhint");

                $('a.iterm_dymanic').cluetip({ width: '830px', showTitle: false, ajaxCache: false });
                $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_company').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });

                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yy/mm/dd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
                jQuery('#Form1').validationEngine({});
            });

        </script>
    </form>
</body>
</html>
