﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_info.aspx.cs" Inherits="Treaty_webpage_TreatyCase_info" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None"
                OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorted="SGV_log_Sorted" OnSorting="SGV_log_Sorting">
                <HeaderStyle CssClass="fixedheadertable" />
                <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                <Columns>
                    <asp:BoundField DataField="caseno" HeaderText="案號" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tc_send_datetime" HeaderText="送件日期" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tc_case_closedate" HeaderText="需求結件日" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tc_process_date" HeaderText="處理天數" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Right" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tc_handle_name" HeaderText="法務承辦人" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle HorizontalAlign="Center"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" />
                    </asp:BoundField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
            </cc1:SmartGridView>
        </span>
        <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />--%>
    </form>

</body>
</html>
