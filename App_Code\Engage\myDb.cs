﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;

namespace Engage
{
	/// <summary>
	/// Summary description for myDb
	/// </summary>
	public class myDb : mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private long _seqsn;
		private int _ver = 0;

		private string _empno;
		private string _empname;
		private string _rec_emplist;
		private string _flow_type;
		private string _qry_orgcd;

        private string _dsp_code;
        private string _upload_rw;
        #endregion

        #region 公有屬性

        /// <summary>
        /// 是否有使用權限
        /// </summary>
        public string dsp_code
        {
            get { return _dsp_code; }
            //set { _dsp_code = value; }
        }

        /// <summary>
        ///  是否有上傳權限
        /// </summary>
        public string upload_rw
        {
            get { return _upload_rw; }
            //set { _upload_rw = value; }
        }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 流水號
		/// </summary>
		public long Seqsn
		{
			get { return _seqsn; }
			set { _seqsn = value; }
		}

		/// <summary>
		/// 版次
		/// </summary>
		public int Ver
		{
			get { return _ver; }
			set { _ver = value; }
		}
		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		/// <summary>
		/// 員工串列，工號以逗點相隔
		/// </summary>
		public string Rec_Emplist
		{
			get { return _rec_emplist; }
			set { _rec_emplist = value; }
		}

		/// <summary>
		/// 洽案系統--立案/停洽/規劃構想/成本訂價送簽及送審時共同呼叫
		/// F:立案, S:停洽, P:規劃構想, C:成本定價, R:契約簽辦
		/// </summary>
		public string FlowType
		{
			get { return _flow_type; }
			set { _flow_type = value; }
		}

		/// <summary>
		/// 欲查詢的單位
		/// </summary>
		public string QryOrgcd
		{
			get { return _qry_orgcd; }
			set { _qry_orgcd = value; }
		}

		/// <summary>
		/// 基本資料
		/// </summary>
		public engage_base iBase = new engage_base();
		public engage_visitor iVisitor = new engage_visitor();
		public engage_halt iHalt = new engage_halt();

		public engage_plan iPlan = new engage_plan();
		public engage_attfile1 iPlan_attfile = new engage_attfile1();
		public engage_authtechitem iPlan_authtechitem = new engage_authtechitem();

		public engage_cost iCost = new engage_cost();

		#region Attfile struct

		private string _ea_id = string.Empty;
		private string _ea_ver = string.Empty;
		private string _ea_filetype = "NA";
		private string _ea_doc = string.Empty;
		private DateTime _ea_uploaddate = DateTime.Now;
		private string _ea_filename = string.Empty;
		private string _ea_filetxt = string.Empty;
		private string _ea_keyinempno = string.Empty;
		private string _ea_keyinempname = string.Empty;

		private bool _ea_valid = false;	// 是否有效
		private string _ea_master_id = string.Empty;	//母檔id For attfile1

		private string _ea_tran_timestamp = string.Empty;

		/// <summary>
		/// 識別號
		/// </summary>
		public string ea_id
		{
			get { return _ea_id; }
			set { _ea_id = value; }
		}

		/// <summary>
		/// 版本
		/// </summary>
		public string ea_ver
		{
			get { return _ea_ver; }
			set { _ea_ver = value; }
		}

		/// <summary>
		/// 文件種類 (NA:規劃書)
		/// </summary>
		public string ea_filetype
		{
			get { return _ea_filetype; }
			set { _ea_filetype = value; }
		}

		/// <summary>
		/// 文件名稱
		/// </summary>
		public string ea_doc
		{
			get { return _ea_doc; }
			set { _ea_doc = value; }
		}

		/// <summary>
		/// 上傳日期
		/// </summary>
		public DateTime ea_uploaddate
		{
			get { return _ea_uploaddate; }
			set { _ea_uploaddate = value; }
		}

		/// <summary>
		/// 文件檔名
		/// </summary>
		public string ea_filename
		{
			get { return _ea_filename; }
			set { _ea_filename = value; }
		}

		/// <summary>
		/// 文件說明 (修改概要)
		/// </summary>
		public string ea_filetxt
		{
			get { return _ea_filetxt; }
			set { _ea_filetxt = value; }
		}

		/// <summary>
		/// 撰寫人工號
		/// </summary>
		public string ea_keyinempno
		{
			get { return _ea_keyinempno; }
			set { _ea_keyinempno = value; }
		}

		/// <summary>
		/// 撰寫人姓名
		/// </summary>
		public string ea_keyinempname
		{
			get { return _ea_keyinempname; }
			set { _ea_keyinempname = value; }
		}

		/// <summary>
		/// 是否有效
		/// </summary>
		public bool ea_valid
		{
			get { return _ea_valid; }
			set { _ea_valid = value; }
		}

		/// <summary>
		/// 母檔id
		/// </summary>
		public string ea_master_id
		{
			get { return _ea_master_id; }
			set { _ea_master_id = value; }
		}

		/// <summary>
		/// 產生時間戳記的Guid
		/// </summary>
		public string ea_tran_timestamp
		{
			get { return _ea_tran_timestamp; }
			set { _ea_tran_timestamp = value; }
		}

		#endregion

		#region 授權標的交付技術資料明細
		/// <summary>
		/// 技術資料明細識別號
		/// </summary>
		public string eati_id;
		/// <summary>
		/// 存放技術授權內容engage_techauth之識別號
		/// </summary>
		public string eati_eta_id;
		/// <summary>
		/// 技資編號
		/// </summary>
		public string eati_techno;
		/// <summary>
		/// 管理單位
		/// </summary>
		public string eati_org;
		/// <summary>
		/// 技資類型
		/// </summary>
		public string eati_techtype;
		/// <summary>
		/// 技資名稱
		/// </summary>
		public string eati_itemname;
		/// <summary>
		/// 產出日期
		/// </summary>
		public string eati_itemdate;
		#endregion

		public string sl_sysid = "";
		public string sl_function = "";
		public string sl_keyword = "";
		public string sl_ip = "";

		/// <summary>
		/// 未約定本院責任上限者
		/// </summary>
		public string eb_unlimit_liability = "";
		public string eb_unlimit_liability_memo = "";

        /// <summary>
        /// 民間業者自行委託
        /// </summary>
        public string eb_comp_cont = "";

        public string eb_memo = "";

        /// <summary>
        /// 配合契約系統-c_conttype_主項
        /// </summary>
        public string c_conttype_主項 = "";

        /// <summary>
        /// 配合契約系統-c_conttype_次項
        /// </summary>
        public string c_conttype_次項 = "";

        /// <summary>
        /// 配合契約系統-契約性質-收入來源
        /// </summary>
        public string c_conttype1 = "";

        /// <summary>
        /// 政府委辦(含國立學校非科專轉委託)
        /// </summary>
        public string eb_gov_school = "";


        #endregion

        public myDb()  
		{
			//
			// TODO: Add constructor logic here
			//
		}

		#region CodeTable相關
		/// <summary>
		/// 洽案系統, 依代碼類別取得代碼表資料
		/// </summary>
		/// <param name="type">類別</param>
		/// <returns></returns>
		public static DataTable GetCode(string type)
		{
            mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select rtrim(code_value) as code_value,rtrim(code_valuedesc) as code_desc, code_relationvalue from engage_codetbl where code_type=@code_type and code_enabled='1' order by code_order
";

			oCmd.Parameters.AddWithValue("@code_type", RemoveShell_myDb(SQLInjectionReplace_myDb(type)));

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
        public static DataTable GetCodeForC_codeTable(string type)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select rtrim(code_value) as code_value,rtrim(substring(code_valuedesc,1,35)) as code_desc, code_relationtype, code_relationvalue, code_order
  from c_codetbl 
 where code_type=@code_type and code_enabled='1' order by code_order
";

            oCmd.Parameters.AddWithValue("@code_type", type);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        /// 訪談系統, 依代碼類別取得代碼表資料
        /// </summary>
        /// <param name="type">類別</param>
        /// <returns></returns>
        public static DataTable GetCodeForVisit(string type)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select rtrim(code_value) as code_value,rtrim(code_valuedesc) as code_desc, code_relationvalue, code_order from visitdb.dbo.visit_codetbl where code_type=@code_type and code_enabled='1' order by code_order
";

			oCmd.Parameters.AddWithValue("@code_type", RemoveShell_myDb(SQLInjectionReplace_myDb(type)));

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 契約系統, 依代碼類別取得代碼表資料
		/// </summary>
		/// <param name="type">類別</param>
		/// <returns></returns>
		public static DataTable GetCodeForContract(string type)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select rtrim(code_value) as code_value, rtrim(code_valuedesc) as code_desc from contract.dbo.cont_codetbl where code_type=@code_type and code_enabled='1' order by code_order
";

			oCmd.Parameters.AddWithValue("@code_type", type);

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public static DataTable GetCodeForTreaty(string type)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select rtrim(code_subtype) as code_value, rtrim(subtype_desc) as code_desc  from treaty_code_table where code_type=@code_type and enable=1 order by display_order asc";

			oCmd.Parameters.AddWithValue("@code_type", type);

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		#endregion

		#region 人員相關
		/// <summary>
		/// 依工號取得人員資料
		/// </summary>
		/// <param name="empno">工號</param>
		/// <returns></returns>
		public static DataTable GetEmployee(string empno)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM common.dbo.comper
WHERE com_empno = @com_empno
";
			oCmd.Parameters.AddWithValue("@com_empno", empno);

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		#endregion

		#region 單位相關
		/// <summary>
		/// 取得單位資料
		/// </summary>
		/// <returns></returns>
		public static DataTable GetOrgcd()
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select org_orgcd as code_value,org_orgcd+'-'+org_abbr_chnm2 as code_desc 
from common.dbo.orgcod 
where org_status='A' and org_orgcd<>'00'
";
			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得單位名稱
		/// </summary>
		/// <returns></returns>
		public static string GetOrgName(string orgcd)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select rtrim(org_abbr_chnm2) as orgname from common.dbo.orgcod where org_orgcd=@orgcd
";
			oCmd.Parameters.AddWithValue("@orgcd", orgcd);

			return dalObject.getTopOne(oCmd, CommandType.Text);
		}

		/// <summary>
		/// 取得部門資料
		/// </summary>
		/// <returns></returns>
		public static DataTable GetDept(string keyword, string orgcd)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select top 500 org_orgcd,org_abbr_chnm2,dep_deptid,dep_deptcd,dep_deptname,org_abbr_egnm  
from common..orgcod
inner join common..depcod on org_orgcd=dep_orgcd
where 1 = 1";

			if (keyword != string.Empty)
			{
				oCmd.CommandText += @"
 and (upper(dep_deptid) like '%'+ @keyword +'%' or upper(dep_deptname) like '%'+ @keyword +'%')
";
				oCmd.Parameters.AddWithValue("@keyword", keyword.ToUpper());
			}
			if (orgcd != string.Empty)
			{
				oCmd.CommandText += @"
 and dep_orgcd = @orgcd
";
				oCmd.Parameters.AddWithValue("@orgcd", orgcd);
			}

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 契約相關
		/// <summary>
		/// 取得契約資料
		/// </summary>
		/// <returns></returns>
		public static DataTable GetContract(string keyword, string orgcd)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select actcontno, contno, contyear, s20_pojcname, cont_compname, org_abbr_chnm2,techtfr_contname 
from contract..v_contract_cp
where 1 = 1";

			if (keyword != string.Empty)
			{
				oCmd.CommandText += @"
 and (upper(actcontno) like '%'+ @keyword +'%' or upper(s20_pojcname) like '%'+ @keyword +'%' or upper(cont_compname) like '%'+ @keyword +'%')
";
				oCmd.Parameters.AddWithValue("@keyword", keyword.ToUpper());
			}
			if (orgcd != string.Empty)
			{
				oCmd.CommandText += @"
 and contorgcd = @orgcd
";
				oCmd.Parameters.AddWithValue("@orgcd", orgcd);
			}

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetCompanyCheckInfo(string compidno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT distinct (select rtrim(org_abbr_chnm2) from common..orgcod where org_orgcd = cci_orgcd) AS org_name, --cci_orgcd,
	cci_changetype, cci_checkamount, cci_checkno, cci_checkpaybank, cci_checkdate, 
	cci_changedate, cci_relationprojno, cci_relationinvno, cci_changereason, cci_compidno
FROM  visitdb..v_changecheckinfo_forEngage  
WHERE (cci_compidno = @compidno)
ORDER BY cci_checkdate ASC
";

			oCmd.Parameters.AddWithValue("@compidno", compidno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 取得客戶-分支機構
		/// <summary>
		/// 取得契約資料
		/// </summary>
		/// <returns></returns>
		public static DataTable GetCompBranch(string keyword)
		{
			mySQLHelper dalObject = new mySQLHelper();
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT top 100 cust.comp_idno,comp_cname,comp_facname 
FROM visitdb..cust cust,visitdb..cust_fac cust_fac 
WHERE cust.comp_idno = cust_fac.comp_idno and cust.comp_idno= @keyword";

			oCmd.Parameters.AddWithValue("@keyword", keyword);

			DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		/// <summary>
		/// 取得權限(00:無權限、10:可讀、11:可讀寫)
		/// </summary>
		/// <returns></returns>
		public string GetRight()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_right_rw";

			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@seqno", _seqsn);
			SqlParameter rtn_code = oCmd.Parameters.Add("@rtn_code", SqlDbType.VarChar, 5);
			rtn_code.Direction = ParameterDirection.Output;

			this.Execute(oCmd, CommandType.StoredProcedure);

			return rtn_code.Value.ToString();
		}

        #region 武漢肺炎疫情評估
        public string Get_COVID_19(string seqsn)
        {
            string result = "";
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
declare @rtn_code varchar(5)
exec [pr_engage_COVID-19] @seqsn,@rtn_code output
select @rtn_code
";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                    result = dt.Rows[0][0].ToString();
            }
            return result;
        }
        #endregion

        /// <summary>
        /// 取得權限 For 簽約版契約電子檔(false:無權限, true:有權限)
        /// </summary>
        /// <returns></returns>
        public bool GetRightForAttfile()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_right_att";

            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@seqno", _seqsn);
            SqlParameter rtn_code = oCmd.Parameters.Add("@right_att", SqlDbType.VarChar, 5);
            rtn_code.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_code.Value.ToString().Trim().Equals("Y");
        }

        /// <summary>
        /// 取得權限 For 報院案件補充資料
        /// </summary>
        /// <returns></returns>
        public void GetRightForSrc()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_pr_engage_報院案件補充資料";

            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            SqlParameter dsp_code = oCmd.Parameters.Add("@dsp_code", SqlDbType.VarChar, 5);
            dsp_code.Direction = ParameterDirection.Output;
            SqlParameter upload_rw = oCmd.Parameters.Add("@upload_rw", SqlDbType.NVarChar, 100);
            upload_rw.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            this._dsp_code = dsp_code.Value.ToString().Trim();
            this._upload_rw= upload_rw.Value.ToString().Trim();
        }

        /// <summary>
        ///   洽案系統 - 控制buttom是否顯示, 1:顯示/ 0:不顯示
        /// </summary>
        /// <returns></returns>
        public DataTable GetFlowDsp()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_flow_dsp";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}


		#region 基本資料 - 相關

		#region 取得基本資料明細

		/// <summary>
		/// 取得基本資料明細
		/// </summary>
		/// <returns></returns>
		public DataSet GetBaseDetail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_base_select_by_seqsn";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			//DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			//return dt;
			DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
			return ds;
		}

		public DataSet GetBaseDetail_his()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select *
		,planno=eb_year+eb_orgcd+eb_class+eb_sn
		,eb_pc_flag_desc=case eb_pc_flag when '2' then '與成本估算合併審查及簽核' when '3' then '一段式簽核(規劃構想+成本定價+契約簽辦)' else '' end
		,(select org_abbr_chnm2 from common.dbo.orgcod where org_orgcd=eb_orgcd) as org_orgname --單位 
		,(select code_valuedesc from engage_codetbl where code_type='052' and code_value=eb_execstatus) as execstatus_name --執行狀態
		,eb_success_rate_desc=(select rtrim(code_valuedesc) from engage_codetbl where code_type='103' and code_value=eb_success_rate)
		,image_status=case 
				when eb_execstatus='F1' then '1' 
				when eb_execstatus='F5' then '2'
				when SUBSTRING(eb_execstatus,1,1)='H' or SUBSTRING(eb_execstatus,1,1)='J' then '3'
				when SUBSTRING(eb_execstatus,1,1)='L' then '5'
				when SUBSTRING(eb_execstatus,1,1)='N' then '6'
				--when eb_execstatus='P3' then '7'
				when SUBSTRING(eb_execstatus,1,1)='P' then '7'
				else '1' end 
		,image_securelevel=case 
				when ISNULL(eb_securelevel,'')='01' then 'RESTRICTED.png'		--限閱
				when ISNULL(eb_securelevel,'')='02' then 'CONFIDENTIAL.png'		--機密
				when ISNULL(eb_securelevel,'')='03' then 'STRICTLY CONFIDENTIAL.png' --極機密
				else '' end
		,case when eb_treaty_flag='3' then '1' else '' end as image_treaty	--控制[議約]燈號
		,cost_status=(select cost_status from engage_cost where cost_seqsn = eb_seqsn)  
		,(select org_abbr_chnm2 from common.dbo.orgcod where org_orgcd=substring(eb_execdept,1,2)) as planer_orgname --主要規劃人單位
		,(select org_abbr_chnm2 from common.dbo.orgcod where org_orgcd=substring(eb_promodept,1,2)) as promo_orgname --推廣人員單位
        ,eb_estimfee_type,eb_estimfee_code,eb_estimfee
  from engage_his.dbo.engage_base 
  where eb_seqsn = @seqsn and eb_ver = @ver

select * from engage_his..engage_visitor where visit_seqsn = @seqsn and visit_ver = @ver
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", 2);

			DataSet ds = this.getDataSet(oCmd, CommandType.Text);
			return ds;
		}

		#endregion

		#region 委託客戶聯絡人

		/// <summary>
		/// 取得委託客戶聯絡人的列表
		/// </summary>
		/// <returns></returns>
		public DataTable GetVisitorList()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=eb_ver from engage_base where eb_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
begin
	select * from engage_visitor where visit_seqsn=@seqsn
end
else
begin
	select * from engage_his.dbo.engage_visitor where visit_seqsn=@seqsn and visit_ver=@ver
end
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 委託客戶聯絡人的刪除
		/// </summary>
		/// <param name="item_id"></param>
		/// <returns></returns>
		public bool DelVisitor(string item_id)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_visitor WHERE visit_id= @item_id;
";
			oCmd.Parameters.AddWithValue("@item_id", item_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 基本資料的新增
		/// <summary>
		/// 基本資料的新增, 呼叫 sp: [pr_addnew_engage]
		/// </summary>
		/// <returns></returns>
		public bool Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_addnew_engage";

			oCmd.Parameters.AddWithValue("@eb_orgcd", iBase.eb_orgcd);				//1
			oCmd.Parameters.AddWithValue("@eb_compidno", iBase.eb_compidno);		//2
			oCmd.Parameters.AddWithValue("@eb_compname", iBase.eb_compname);		//3
			oCmd.Parameters.AddWithValue("@eb_compbranch", iBase.eb_compbranch);	//4
			oCmd.Parameters.AddWithValue("@eb_srvarea", iBase.eb_srvarea);			//5
			oCmd.Parameters.AddWithValue("@eb_planname", iBase.eb_planname);		//6
			oCmd.Parameters.AddWithValue("@eb_tcehtype1", "");						//7
			oCmd.Parameters.AddWithValue("@eb_tcehtype2", "");						//8
			oCmd.Parameters.AddWithValue("@eb_industype", iBase.eb_industype);		//9
			oCmd.Parameters.AddWithValue("@eb_execdept", iBase.eb_execdept);		//10
			oCmd.Parameters.AddWithValue("@eb_execdeptnm", iBase.eb_execdeptnm);	//11
			oCmd.Parameters.AddWithValue("@eb_planer", iBase.eb_planer);
			oCmd.Parameters.AddWithValue("@eb_planerempno", iBase.eb_planerempno);
			oCmd.Parameters.AddWithValue("@eb_planerext", iBase.eb_planerext);
			oCmd.Parameters.AddWithValue("@eb_planermail", iBase.eb_planermail);
			oCmd.Parameters.AddWithValue("@eb_promoname", iBase.eb_promoname);
			oCmd.Parameters.AddWithValue("@eb_promoempno", iBase.eb_promoempno);
			oCmd.Parameters.AddWithValue("@eb_promoext", iBase.eb_promoext);
			oCmd.Parameters.AddWithValue("@eb_promomail", iBase.eb_promomail);
			oCmd.Parameters.AddWithValue("@eb_promodept", iBase.eb_promodept);
			oCmd.Parameters.AddWithValue("@eb_promodeptnm", iBase.eb_promodeptnm);
			oCmd.Parameters.AddWithValue("@eb_demand", iBase.eb_demand);
			oCmd.Parameters.AddWithValue("@eb_success_rate", iBase.eb_success_rate);
			oCmd.Parameters.AddWithValue("@eb_estimfee_type", iBase.eb_estimfee_type);
            oCmd.Parameters.AddWithValue("@eb_estimfee_code", iBase.eb_estimfee_code);
            oCmd.Parameters.AddWithValue("@eb_estimfee", iBase.eb_estimfee);
            oCmd.Parameters.AddWithValue("@eb_keyword", iBase.eb_keyword);
			oCmd.Parameters.AddWithValue("@eb_keyinempno", iBase.eb_keyinempno);
			oCmd.Parameters.AddWithValue("@eb_keyinname", iBase.eb_keyinname);
			oCmd.Parameters.AddWithValue("@eb_conttype_b0", iBase.eb_conttype_b0);
			oCmd.Parameters.AddWithValue("@eb_conttype_b1", iBase.eb_conttype_b1);
			oCmd.Parameters.AddWithValue("@eb_conttype_d4", iBase.eb_conttype_d4);
			oCmd.Parameters.AddWithValue("@eb_conttype_d5", iBase.eb_conttype_d5);
			oCmd.Parameters.AddWithValue("@eb_conttype_d7", iBase.eb_conttype_d7);
			oCmd.Parameters.AddWithValue("@eb_gov_assist", iBase.eb_gov_assist);
			oCmd.Parameters.AddWithValue("@eb_gov_plan", iBase.eb_gov_plan);
			oCmd.Parameters.AddWithValue("@eb_tenderidno", iBase.eb_tenderidno);
			oCmd.Parameters.AddWithValue("@eb_tendername", iBase.eb_tendername);
			oCmd.Parameters.AddWithValue("@eb_pretechauth", iBase.eb_pretechauth);
			oCmd.Parameters.AddWithValue("@eb_repeat", iBase.eb_repeat);
			oCmd.Parameters.AddWithValue("@eb_actcontno", iBase.eb_actcontno);
			oCmd.Parameters.AddWithValue("@eb_cont_contname", iBase.eb_cont_contname);
			oCmd.Parameters.AddWithValue("@eb_cont_compname", iBase.eb_cont_compname);
			oCmd.Parameters.AddWithValue("@eb_precontfdate", iBase.eb_precontfdate);
			oCmd.Parameters.AddWithValue("@eb_moea_cont", iBase.eb_moea_cont);
			oCmd.Parameters.AddWithValue("@eb_srib_cont", iBase.eb_srib_cont);
			oCmd.Parameters.AddWithValue("@eb_master_cont", iBase.eb_master_cont);
			oCmd.Parameters.AddWithValue("@eb_projno", iBase.eb_projno);
			oCmd.Parameters.AddWithValue("@eb_projname", iBase.eb_projname);
			oCmd.Parameters.AddWithValue("@eb_signhopedate", iBase.eb_signhopedate);
			oCmd.Parameters.AddWithValue("@eb_pat_agree", iBase.eb_pat_agree);
			oCmd.Parameters.AddWithValue("@eb_securelevel", iBase.eb_securelevel);
			oCmd.Parameters.AddWithValue("@eb_ipb", iBase.eb_ipb);
			oCmd.Parameters.AddWithValue("@eb_ipbi_percent", iBase.eb_ipbi_percent);
			oCmd.Parameters.AddWithValue("@eb_ipbc_percent", iBase.eb_ipbc_percent);
			oCmd.Parameters.AddWithValue("@eb_ipb_other_desc", iBase.eb_ipb_other_desc);
			oCmd.Parameters.AddWithValue("@eb_secure_memo", iBase.eb_secure_memo);
			oCmd.Parameters.AddWithValue("@eb_purchase", iBase.eb_purchase);		// --民營公開採購案
			oCmd.Parameters.AddWithValue("@eb_tech_key", iBase.eb_tech_key);
			oCmd.Parameters.AddWithValue("@eb_tech_domain1", iBase.eb_tech_domain1);
			oCmd.Parameters.AddWithValue("@eb_tech_domain2", iBase.eb_tech_domain2);
			oCmd.Parameters.AddWithValue("@eb_trl", iBase.eb_trl);
			oCmd.Parameters.AddWithValue("@eb_new_venture", iBase.eb_new_venture);		//61
			oCmd.Parameters.AddWithValue("@eb_tech_overlap", iBase.eb_tech_overlap);	//62
			oCmd.Parameters.AddWithValue("@eb_unlimit_liability", this.eb_unlimit_liability);			//63
			oCmd.Parameters.AddWithValue("@eb_unlimit_liability_memo", this.eb_unlimit_liability_memo); //64
            oCmd.Parameters.AddWithValue("@eb_comp_cont", this.eb_comp_cont);       //65
            oCmd.Parameters.AddWithValue("@c_conttype_主項", this.c_conttype_主項); //66
            oCmd.Parameters.AddWithValue("@c_conttype_次項", this.c_conttype_次項);	//67
            oCmd.Parameters.AddWithValue("@c_conttype1", this.c_conttype1);         //68
            oCmd.Parameters.AddWithValue("@eb_memo", this.eb_memo);                 //69

            
            oCmd.Parameters.AddWithValue("@eb_gov_school", this.eb_gov_school);     //70
            
            try
            {
				//新增，會回傳新的 seqsn
				string szRet = this.getTopOne(oCmd, CommandType.StoredProcedure);
				if (!string.IsNullOrEmpty(szRet))
				{
					long tmpValue = 0;
					if (long.TryParse(szRet, out tmpValue))
						this.Seqsn = tmpValue;
				}

				//協同規劃人
				this.CoPlanerSave();

				//新增協同規劃人後 call SP: [pr_engage_add_right]
				this.BaseToAddRight();

				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}
		#endregion

		#region 基本資料的更新
		/// <summary>
		/// 基本資料的更新, 呼叫 sp: [pr_update_engage]
		/// </summary>
		/// <returns></returns>
		public bool Update()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_update_engage";

			oCmd.Parameters.AddWithValue("@eb_orgcd", iBase.eb_orgcd);				//1
			oCmd.Parameters.AddWithValue("@eb_compidno", iBase.eb_compidno);		//2
			oCmd.Parameters.AddWithValue("@eb_compname", iBase.eb_compname);		//3
			oCmd.Parameters.AddWithValue("@eb_compbranch", iBase.eb_compbranch);	//4
			oCmd.Parameters.AddWithValue("@eb_srvarea", iBase.eb_srvarea);			//5
			oCmd.Parameters.AddWithValue("@eb_planname", iBase.eb_planname);		//6
			oCmd.Parameters.AddWithValue("@eb_tcehtype1", "");						//7
			oCmd.Parameters.AddWithValue("@eb_tcehtype2", "");						//8
			oCmd.Parameters.AddWithValue("@eb_industype", iBase.eb_industype);		//9
			oCmd.Parameters.AddWithValue("@eb_execdept", iBase.eb_execdept);		//10
			oCmd.Parameters.AddWithValue("@eb_execdeptnm", iBase.eb_execdeptnm);	//11
			oCmd.Parameters.AddWithValue("@eb_planer", iBase.eb_planer);
			oCmd.Parameters.AddWithValue("@eb_planerempno", iBase.eb_planerempno);
			oCmd.Parameters.AddWithValue("@eb_planerext", iBase.eb_planerext);
			oCmd.Parameters.AddWithValue("@eb_planermail", iBase.eb_planermail);
			oCmd.Parameters.AddWithValue("@eb_promoname", iBase.eb_promoname);
			oCmd.Parameters.AddWithValue("@eb_promoempno", iBase.eb_promoempno);
			oCmd.Parameters.AddWithValue("@eb_promoext", iBase.eb_promoext);
			oCmd.Parameters.AddWithValue("@eb_promomail", iBase.eb_promomail);
			oCmd.Parameters.AddWithValue("@eb_promodept", iBase.eb_promodept);
			oCmd.Parameters.AddWithValue("@eb_promodeptnm", iBase.eb_promodeptnm);
			oCmd.Parameters.AddWithValue("@eb_demand", iBase.eb_demand);
			oCmd.Parameters.AddWithValue("@eb_success_rate", iBase.eb_success_rate);
            oCmd.Parameters.AddWithValue("@eb_estimfee_type", iBase.eb_estimfee_type);
            oCmd.Parameters.AddWithValue("@eb_estimfee_code", iBase.eb_estimfee_code);
            oCmd.Parameters.AddWithValue("@eb_estimfee", iBase.eb_estimfee);
			oCmd.Parameters.AddWithValue("@eb_keyword", iBase.eb_keyword);
			oCmd.Parameters.AddWithValue("@eb_keyinempno", iBase.eb_keyinempno);
			oCmd.Parameters.AddWithValue("@eb_keyinname", iBase.eb_keyinname);
			oCmd.Parameters.AddWithValue("@eb_conttype_b0", iBase.eb_conttype_b0);
			oCmd.Parameters.AddWithValue("@eb_conttype_b1", iBase.eb_conttype_b1);
			oCmd.Parameters.AddWithValue("@eb_conttype_d4", iBase.eb_conttype_d4);
			oCmd.Parameters.AddWithValue("@eb_conttype_d5", iBase.eb_conttype_d5);
			oCmd.Parameters.AddWithValue("@eb_conttype_d7", iBase.eb_conttype_d7);
			oCmd.Parameters.AddWithValue("@eb_gov_assist", iBase.eb_gov_assist);
			oCmd.Parameters.AddWithValue("@eb_gov_plan", iBase.eb_gov_plan);
			oCmd.Parameters.AddWithValue("@eb_tenderidno", iBase.eb_tenderidno);
			oCmd.Parameters.AddWithValue("@eb_tendername", iBase.eb_tendername);
			oCmd.Parameters.AddWithValue("@eb_pretechauth", iBase.eb_pretechauth);
			oCmd.Parameters.AddWithValue("@eb_repeat", iBase.eb_repeat);
			oCmd.Parameters.AddWithValue("@eb_actcontno", iBase.eb_actcontno);
			oCmd.Parameters.AddWithValue("@eb_cont_contname", iBase.eb_cont_contname);
			oCmd.Parameters.AddWithValue("@eb_cont_compname", iBase.eb_cont_compname);
			oCmd.Parameters.AddWithValue("@eb_precontfdate", iBase.eb_precontfdate);
			oCmd.Parameters.AddWithValue("@eb_moea_cont", iBase.eb_moea_cont);
			oCmd.Parameters.AddWithValue("@eb_srib_cont", iBase.eb_srib_cont);
			oCmd.Parameters.AddWithValue("@eb_master_cont", iBase.eb_master_cont);
			oCmd.Parameters.AddWithValue("@eb_projno", iBase.eb_projno);
			oCmd.Parameters.AddWithValue("@eb_projname", iBase.eb_projname);
			oCmd.Parameters.AddWithValue("@eb_signhopedate", iBase.eb_signhopedate);
			oCmd.Parameters.AddWithValue("@eb_pat_agree", iBase.eb_pat_agree);
			oCmd.Parameters.AddWithValue("@eb_securelevel", iBase.eb_securelevel);
			oCmd.Parameters.AddWithValue("@eb_ipb", iBase.eb_ipb);
			oCmd.Parameters.AddWithValue("@eb_ipbi_percent", iBase.eb_ipbi_percent);
			oCmd.Parameters.AddWithValue("@eb_ipbc_percent", iBase.eb_ipbc_percent);
			oCmd.Parameters.AddWithValue("@eb_ipb_other_desc", iBase.eb_ipb_other_desc);
			oCmd.Parameters.AddWithValue("@eb_secure_memo", iBase.eb_secure_memo);
			oCmd.Parameters.AddWithValue("@eb_purchase", iBase.eb_purchase);		// --民營公開採購案
			oCmd.Parameters.AddWithValue("@eb_tech_key", iBase.eb_tech_key);
			oCmd.Parameters.AddWithValue("@eb_tech_domain1", iBase.eb_tech_domain1);
			oCmd.Parameters.AddWithValue("@eb_tech_domain2", iBase.eb_tech_domain2);
			oCmd.Parameters.AddWithValue("@eb_trl", iBase.eb_trl);
			oCmd.Parameters.AddWithValue("@eb_new_venture", iBase.eb_new_venture);		//61
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);					//62
			oCmd.Parameters.AddWithValue("@eb_pc_flag", iBase.eb_pc_flag);	//63
			oCmd.Parameters.AddWithValue("@eb_tech_overlap", iBase.eb_tech_overlap);					//64
			oCmd.Parameters.AddWithValue("@eb_unlimit_liability", this.eb_unlimit_liability);			//65
			oCmd.Parameters.AddWithValue("@eb_unlimit_liability_memo", this.eb_unlimit_liability_memo); //66
            oCmd.Parameters.AddWithValue("@eb_comp_cont", this.eb_comp_cont);       //67
            oCmd.Parameters.AddWithValue("@c_conttype_主項", this.c_conttype_主項); //68
            oCmd.Parameters.AddWithValue("@c_conttype_次項", this.c_conttype_次項);	//69
            oCmd.Parameters.AddWithValue("@c_conttype1", this.c_conttype1);         //70
            oCmd.Parameters.AddWithValue("@eb_memo", this.eb_memo);                 //71

            
            oCmd.Parameters.AddWithValue("@eb_gov_school", this.eb_gov_school);     //72
            

            try
            {
				this._returnMessage = this.getTopOne(oCmd, CommandType.StoredProcedure);

				//協同規劃人
				this.CoPlanerSave();

				//新增協同規劃人後 call SP: [pr_engage_add_right]
				this.BaseToAddRight();

				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}
		#endregion

		#region  基本資料的刪除
		/// <summary>
		/// 基本資料的刪除，呼叫 sp: [pr_del_engage]
		/// </summary>
		/// <returns></returns>
		public bool Delete()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_del_engage";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 基本資料的新增版次
		/// <summary>
		/// 基本資料的新增版次，呼叫 sp: [pr_engagebase_changever3]
		/// </summary>
		/// <returns></returns>
		public bool AddNewVer()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engagebase_changever3";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			SqlParameter rtn_code = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			rtn_code.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
				this.ReturnMessage = rtn_code.Value.ToString();
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 案件暫停
		/// <summary>
		/// 案件暫停，呼叫 sp: [pr_engage_halt_start]
		/// </summary>
		/// <returns></returns>
		public bool HaltStart()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_halt_start";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@startdate", iHalt.eh_startdate);
			oCmd.Parameters.AddWithValue("@haltcause1", iHalt.eh_haltcause1);
			oCmd.Parameters.AddWithValue("@haltcause2", iHalt.eh_haltcause2);
			oCmd.Parameters.AddWithValue("@haltcause3", iHalt.eh_haltcause3);
			oCmd.Parameters.AddWithValue("@haltcause4", iHalt.eh_haltcause4);
			oCmd.Parameters.AddWithValue("@haltmemo", iHalt.eh_haltmemo);
			oCmd.Parameters.AddWithValue("@rec_emplist", _rec_emplist);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 暫停結束，呼叫 sp: [pr_engage_halt_stop]
		/// </summary>
		/// <returns></returns>
		public bool HaltStop()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_halt_stop";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 取得案件暫停的列表
		/// <summary>
		/// 取得案件暫停的列表
		/// </summary>
		/// <returns></returns>
		public DataTable GetHaltList()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select * from engage_halt where eh_seqsn=@seqsn order by eh_serial
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 案件停洽
		/// <summary>
		/// 案件停洽
		/// </summary>
		/// <returns></returns>
		public bool StopCase()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
UPDATE engage_base SET
	eb_stopdate=CONVERT(varchar(8),GETDATE(),112),
	eb_stopcause1=@eb_stopcause1,
	eb_stopcause2=@eb_stopcause2,
	eb_stopcause3=@eb_stopcause3,
	eb_stopcause4=@eb_stopcause4,
	eb_stopcause5=@eb_stopcause5,
	eb_stopcause6=@eb_stopcause6,
	eb_stopcause7=@eb_stopcause7,
	eb_stopmemo=@eb_stopmemo,
	eb_modempno=@empno,
	eb_modname=@empname,
	eb_moddate=CONVERT(varchar(8),GETDATE(),112)
  WHERE eb_seqsn=@seqsn			
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@eb_stopcause1", iBase.eb_stopcause1);
			oCmd.Parameters.AddWithValue("@eb_stopcause2", iBase.eb_stopcause2);
			oCmd.Parameters.AddWithValue("@eb_stopcause3", iBase.eb_stopcause3);
			oCmd.Parameters.AddWithValue("@eb_stopcause4", iBase.eb_stopcause4);
			oCmd.Parameters.AddWithValue("@eb_stopcause5", iBase.eb_stopcause5);
			oCmd.Parameters.AddWithValue("@eb_stopcause6", iBase.eb_stopcause6);
			oCmd.Parameters.AddWithValue("@eb_stopcause7", iBase.eb_stopcause7);
			oCmd.Parameters.AddWithValue("@eb_stopmemo", iBase.eb_stopmemo);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 基本資料的直接立案
		/// <summary>
		/// 基本資料的直接立案，呼叫 sp: [pr_engage_create_case]
		/// </summary>
		/// <returns></returns>
		public bool CreateCase()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_create_case";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			msg.Direction = ParameterDirection.Output;

			SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
			rtn_flag.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				this.ReturnMessage = msg.Value.ToString();
				//success = true;
				success = (rtn_flag.Value.ToString() == "0");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion
		
		#region 送審、送簽欄位檢查
		/// <summary>
		/// 送審、送簽欄位檢查, 呼叫 sp: [pr_engage_pc_sendcheck]
		/// </summary>
		/// <returns></returns>
		public DataTable PC_SendCheck()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_pc_sendcheck";

			oCmd.Parameters.AddWithValue("@flow_type", _flow_type);
			oCmd.Parameters.AddWithValue("@seqno", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		#endregion

		/// <summary>
		/// 修正契約書送簽欄位檢查
		/// </summary>
		/// <returns></returns>
		public DataTable Cont_SendCheck()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_cont_sendcheck";

			oCmd.Parameters.AddWithValue("@flow_type", _flow_type);
			oCmd.Parameters.AddWithValue("@seqno", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		#region 協同規劃人
		/// <summary>
		/// 取得協同規劃人
		/// </summary>
		/// <returns></returns>
		public DataTable CoPlanerGet()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @ver_max int
select @ver_max = eb_ver from engage_base where eb_seqsn = @seqsn

if @ver = 0 OR @ver = @ver_max
begin
	SELECT * FROM engage_coplaner WHERE ecp_seqsn = @seqsn
end
else
begin
	SELECT * FROM engage_his..engage_coplaner WHERE ecp_seqsn = @seqsn and ecp_ver = @ver
end
 ";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 協同規劃人的存檔
		/// </summary>
		/// <returns></returns>
		public bool CoPlanerSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @ver int
select @ver=eb_ver from engage_base where eb_seqsn=@seqsn

delete from engage_coplaner where ecp_seqsn=@seqsn and ecp_coplanerempno not in (select [Value] from fn_split_no_repeat(@emplist, ';')) 

insert into engage_coplaner
	(ecp_seqsn, ecp_ver, ecp_coplaner, ecp_coplanerempno)
select
	@seqsn, @ver, RTRIM(com_cname), com_empno
  from common.dbo.comper
  join fn_split_no_repeat(@emplist, ';') on com_empno=[Value]
  where [Value] not in (select ecp_coplanerempno from engage_coplaner where ecp_seqsn=@seqsn)
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@emplist", _rec_emplist);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 取得該案件的西元年度
		/// <summary>
		/// 取得該案件的西元年度
		/// </summary>
		/// <returns></returns>
		public string GetCaseYear()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	select eb_year from engage_base where eb_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string retvalue = this.getTopOne(oCmd, CommandType.Text);
				return retvalue;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return "";
		}
		#endregion

		#region 取得洽案流水號
		/// <summary>
		/// 取得洽案流水號
		/// </summary>
		/// <returns></returns>
		public string GetCaseSeqsnByContno(string contno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	select eb_seqsn from engage_base where (eb_year+eb_orgcd+eb_class+eb_sn)=@contno
			";

			oCmd.Parameters.AddWithValue("@contno", contno);

			try
			{
				string retvalue = this.getTopOne(oCmd, CommandType.Text);
				return retvalue;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return "";
		}
		#endregion

		#region 增加相關人員權限
		/// <summary>
		/// 新增協同規畫人後 call SP: [pr_engage_add_right]
		/// </summary>
		/// <returns></returns>
		public void BaseToAddRight()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_add_right";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			this.Execute(oCmd, CommandType.StoredProcedure);
		}
		#endregion

		#endregion

		#region 規劃構想 - 相關

		#region 取得規劃構想
		/// <summary>
		/// 取得規劃構想的版本
		/// </summary>
		/// <returns></returns>
		public int GetPlanVer()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT ep_ver FROM engage_plan WHERE ep_seqsn = @seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string data = this.getTopOne(oCmd, CommandType.Text);
				int ver = 0;
				if (data != string.Empty)
				{
					ver = int.Parse(data);
				}
				return ver;
			}
			catch
			{
				return 0;
			}
		}

		/// <summary>
		/// 取得規劃構想明細
		/// </summary>
		/// <returns></returns>
		public DataTable GetPlanDetail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_plan_select_by_seqsn";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		/// <summary>
		/// 取得院內其他單位共同執行
		/// </summary>
		/// <returns></returns>
		public DataTable GetJoin()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @max_ver int
	select @max_ver=ep_ver from engage_plan where ep_seqsn = @seqsn

IF @ver = 0 OR @ver = @max_ver
BEGIN
	select a.* from engage_join a where ej_seqsn=@seqsn
END
ELSE
BEGIN
	select a.* from engage_his.dbo.engage_join a where ej_seqsn=@seqsn and ej_ver=@ver
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		

		/// <summary>
		/// 取得工作項目
		/// </summary>
		/// <returns></returns>
		public DataTable GetWorkitem()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @max_ver int
	select @max_ver=ep_ver from engage_plan where ep_seqsn = @seqsn

IF @ver = 0 OR @ver = @max_ver
BEGIN
	select ew_id, ew_seqsn, ew_serial, ew_memo
	from engage_workitem a
	where ew_seqsn=@seqsn
END
ELSE
BEGIN
	select ew_id, ew_seqsn, ew_serial, ew_memo
	from engage_his.dbo.engage_workitem a
	where ew_seqsn=@seqsn and ew_ver=@ver
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得技術授權內容
		/// </summary>
		/// <returns></returns>
		public DataTable GetTechAuth()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @max_ver int
	select @max_ver=ep_ver from engage_plan where ep_seqsn = @seqsn

IF @ver = 0 OR @ver = @max_ver
BEGIN
	select a.*
		,eta_authmethod_desc=case eta_authmethod when '1' then '非專屬' when '2' then '專屬' else '' end
		,eta_mature_desc= case eta_mature when '1' then '先期授權' when '2' then '成熟授權' else '' end
		,b.code_valuedesc as eta_resultfrom_desc
	 from engage_techauth a
     --舊倒車Start
	 join contract..cont_codetbl b on code_type='012' and a.eta_resultfrom=code_value
     --舊倒車End
     /*
     --新倒車Start
     join contractDB..c_codetbl b on code_type='012' and a.eta_resultfrom=code_value
     --新倒車End
     */
     
	 where eta_seqsn=@seqsn
END
ELSE
BEGIN
	select a.*
		,eta_authmethod_desc=case eta_authmethod when '1' then '非專屬' when '2' then '專屬' else '' end
		,eta_mature_desc= case eta_mature when '1' then '先期授權' when '2' then '成熟授權' else '' end
		,b.code_valuedesc as eta_resultfrom_desc
	 from engage_his.dbo.engage_techauth a
	 join contractDB..c_codetbl b on code_type='012' and a.eta_resultfrom=code_value
	 where eta_seqsn=@seqsn and eta_ver=@ver
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得技術授權內容 - 明細
		/// </summary>
		/// <returns></returns>
		public DataTable GetTechAuthItem()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @max_ver int
	select @max_ver=ep_ver from engage_plan where ep_seqsn = @seqsn

IF @ver = 0 OR @ver = @max_ver
BEGIN
	select a.*
			,eati_techtype_desc=case eati_techtype when '3' then '研究報告' when '5' then '對外發表資料' when '8' then '媒體資料' when '9' then '原型' when '7' then '引進技術資料' else '' end
			,eta_mature
	 from engage_authtechitem a
	 join engage_techauth on eta_id=eati_eta_id
	 where eati_seqsn=@seqsn and eati_eta_id=@eta_id
END
ELSE
BEGIN
	select a.*
			,eati_techtype_desc=case eati_techtype when '3' then '研究報告' when '5' then '對外發表資料' when '8' then '媒體資料' when '9' then '原型' when '7' then '引進技術資料' else '' end
			,eta_mature
	 from engage_his.dbo.engage_authtechitem a
	 join engage_his.dbo.engage_techauth on eta_id=eati_eta_id
	 where eati_seqsn=@seqsn and eati_eta_id=@eta_id and eati_ver=@ver
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@eta_id", iPlan_authtechitem.eati_eta_id);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得技術授權內容 - 明細
		/// </summary>
		/// <returns></returns>
		public DataTable GetAuthpat()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @max_ver int
	select @max_ver=ep_ver from engage_plan where ep_seqsn = @seqsn

IF @ver = 0 OR @ver = @max_ver
BEGIN
	 select eap_id, eap_seqsn, eap_patentno, 
			org_abbr_chnm2, eap_authmethod, p40_applynation, p40_pntcnn, p10_asorgnm,
			code_desc1, p40_cerdate, p40_cerpntno, code_desc2, eap_authmethod
			,eap_authmethod_desc=case eap_authmethod when '1' then '非專屬' when '2' then '專屬' when '3' then '讓與' when '4' then '特定領域專屬' else '' end
			,eap_contribute
			,eap_authitem1, eap_authitem2, eap_authitem3, eap_authitem4
			,eap_authitem_desc=case when eap_authitem1='1' then ',製造' else '' end + case when eap_authitem2='1' then ',販賣' else '' end  + case when eap_authitem3='1' then ',使用' else '' end  + case when eap_authitem4='1' then ',進口' else '' end
			--, a.* 
	  from engage_authpat a
	  left join patent.dbo.v_pat040_engage on eap_patentno=p40_patentno
	  where eap_seqsn=@seqsn
END
ELSE
BEGIN
	 select eap_id, eap_seqsn, eap_patentno, 
			org_abbr_chnm2, eap_authmethod, p40_applynation, p40_pntcnn, p10_asorgnm,
			code_desc1, p40_cerdate, p40_cerpntno, code_desc2, eap_authmethod
			,eap_authmethod_desc=case eap_authmethod when '1' then '非專屬' when '2' then '專屬' when '3' then '讓與' when '4' then '特定領域專屬' else '' end
			,eap_contribute
			,eap_authitem1, eap_authitem2, eap_authitem3, eap_authitem4
			,eap_authitem_desc=case when eap_authitem1='1' then ',製造' else '' end + case when eap_authitem2='1' then ',販賣' else '' end  + case when eap_authitem3='1' then ',使用' else '' end  + case when eap_authitem4='1' then ',進口' else '' end
			--, a.* 
	  from engage_his.dbo.engage_authpat a
	  left join patent.dbo.v_pat040_engage on eap_patentno=p40_patentno
	  where eap_seqsn=@seqsn and eap_ver=@ver
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion




		public bool SaveJoin(engage_join entity)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @orgcd char(2) ='', @orgcdnm nvarchar(30)=''
--select @orgcd=org_ @orgcdnm=rtrim(org_abbr_chnm2) from common.dbo.orgcod where org_orgcd=@ej_orgcd

	select @orgcd=org_orgcd,@orgcdnm=rtrim(org_abbr_chnm2)
	 from common..comper
	 join common..orgcod on com_orgcd = org_orgcd
	 where com_empno = @ej_empno


if @id = 0
begin
	declare @ver int
	select @ver=ep_ver from engage_plan where ep_seqsn=@seqsn

	insert into engage_join 
		(ej_seqsn, ej_ver, ej_orgcd, ej_orgcdnm, ej_empname, ej_empno, ej_memo)
	select @seqsn, @ver, @orgcd, @orgcdnm, @ej_empname, @ej_empno, @ej_memo

end
else
begin
	update engage_join set 
		ej_orgcd=@orgcd, ej_orgcdnm=@orgcdnm,
		ej_empname=@ej_empname, ej_empno=@ej_empno, ej_memo=@ej_memo 
	where ej_id=@id
end

if exists(select ej_id from engage_join where ej_seqsn=@seqsn)
	update engage_base set eb_join='1' where eb_seqsn=@seqsn
else
	update engage_base set eb_join='0' where eb_seqsn=@seqsn

update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@id", entity.ej_id);
			oCmd.Parameters.AddWithValue("@ep_modempno", iPlan.ep_modempno);
			oCmd.Parameters.AddWithValue("@ep_modname", iPlan.ep_modname);
			oCmd.Parameters.AddWithValue("@ej_orgcd", entity.ej_orgcd);
			//oCmd.Parameters.AddWithValue("@ej_orgcdnm", entity.ej_orgcdnm);
			oCmd.Parameters.AddWithValue("@ej_empname", entity.ej_empname);
			oCmd.Parameters.AddWithValue("@ej_empno", entity.ej_empno);
			oCmd.Parameters.AddWithValue("@ej_memo", entity.ej_memo);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool DelJoin(engage_join entity)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete from engage_join where ej_id=@id

if exists(select ej_id from engage_join where ej_seqsn=@seqsn)
	update engage_base set eb_join='1' where eb_seqsn=@seqsn
else
	update engage_base set eb_join='0' where eb_seqsn=@seqsn

update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ep_modempno", iPlan.ep_modempno);
			oCmd.Parameters.AddWithValue("@ep_modname", iPlan.ep_modname);
			oCmd.Parameters.AddWithValue("@id", entity.ej_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SaveWorkitem(engage_workitem entity)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @id = 0
begin
	declare @ver int
	select @ver=ep_ver from engage_plan where ep_seqsn=@seqsn
	insert into engage_workitem 
		(ew_seqsn, ew_ver, ew_serial, ew_memo)
	select @seqsn, @ver, @ew_serial, @ew_memo
end
else
begin
	update engage_workitem set ew_serial=@ew_serial, ew_memo=@ew_memo where ew_id=@id
end
update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@id", entity.ew_id);
			oCmd.Parameters.AddWithValue("@ew_serial", entity.ew_serial);
			oCmd.Parameters.AddWithValue("@ew_memo", entity.ew_memo);
			oCmd.Parameters.AddWithValue("@ep_modempno", iPlan.ep_modempno);
			oCmd.Parameters.AddWithValue("@ep_modname", iPlan.ep_modname);
			
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SaveTechauth(engage_techauth entity)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @id = 0
begin
	declare @ver int
	select @ver=ep_ver from engage_plan where ep_seqsn=@seqsn

	INSERT INTO [engagedb].[dbo].[engage_techauth]
           ([eta_seqsn],[eta_ver],[eta_techname],[eta_resultfrom]
           ,[eta_mature],[eta_authmethod]
           ,[eta_area1] ,[eta_authitem11],[eta_authitem12],[eta_authitem13],[eta_authitem14],[eta_authitem15],[eta_authitem16]
           ,[eta_area2] ,[eta_authitem21],[eta_authitem22],[eta_authitem23],[eta_authitem24],[eta_authitem25] ,[eta_authitem26]
           ,[eta_otherarea],[eta_contribute]
		   )
     SELECT
           @seqsn,@ver,@eta_techname,@eta_resultfrom
           ,@eta_mature, @eta_authmethod
           ,@eta_area1 ,@eta_authitem11,@eta_authitem12,@eta_authitem13,@eta_authitem14,@eta_authitem15,@eta_authitem16
           ,@eta_area2 ,@eta_authitem21,@eta_authitem22,@eta_authitem23,@eta_authitem24,@eta_authitem25,@eta_authitem26
           ,@eta_otherarea,@eta_contribute
end
else
begin
	UPDATE engage_techauth SET 
			eta_techname=@eta_techname, eta_resultfrom=@eta_resultfrom
           ,eta_mature=@eta_mature,eta_authmethod=@eta_authmethod
           ,eta_area1=@eta_area1 ,eta_authitem11=@eta_authitem11,eta_authitem12=@eta_authitem12,eta_authitem13=@eta_authitem13,eta_authitem14=@eta_authitem14,eta_authitem15=@eta_authitem15,eta_authitem16=@eta_authitem16
           ,eta_area2=@eta_area2 ,eta_authitem21=@eta_authitem21,eta_authitem22=@eta_authitem22,eta_authitem23=@eta_authitem23,eta_authitem24=@eta_authitem24,eta_authitem25=@eta_authitem25,eta_authitem26=@eta_authitem26
           ,eta_otherarea=@eta_otherarea,eta_contribute=@eta_contribute
		WHERE eta_id=@id
end
update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@id", entity.eta_id);
			oCmd.Parameters.AddWithValue("@eta_techname", entity.eta_techname);
			oCmd.Parameters.AddWithValue("@eta_resultfrom", entity.eta_resultfrom);
			oCmd.Parameters.AddWithValue("@eta_mature", entity.eta_mature);
			oCmd.Parameters.AddWithValue("@eta_authmethod", entity.eta_authmethod);
			oCmd.Parameters.AddWithValue("@eta_area1", entity.eta_area1);
			oCmd.Parameters.AddWithValue("@eta_authitem11", entity.eta_authitem11);
			oCmd.Parameters.AddWithValue("@eta_authitem12", entity.eta_authitem12);
			oCmd.Parameters.AddWithValue("@eta_authitem13", entity.eta_authitem13);
			oCmd.Parameters.AddWithValue("@eta_authitem14", entity.eta_authitem14);
			oCmd.Parameters.AddWithValue("@eta_authitem15", entity.eta_authitem15);
			oCmd.Parameters.AddWithValue("@eta_authitem16", entity.eta_authitem16);
			oCmd.Parameters.AddWithValue("@eta_area2", entity.eta_area2);
			oCmd.Parameters.AddWithValue("@eta_authitem21", entity.eta_authitem21);
			oCmd.Parameters.AddWithValue("@eta_authitem22", entity.eta_authitem22);
			oCmd.Parameters.AddWithValue("@eta_authitem23", entity.eta_authitem23);
			oCmd.Parameters.AddWithValue("@eta_authitem24", entity.eta_authitem24);
			oCmd.Parameters.AddWithValue("@eta_authitem25", entity.eta_authitem25);
			oCmd.Parameters.AddWithValue("@eta_authitem26", entity.eta_authitem26);
			oCmd.Parameters.AddWithValue("@eta_otherarea", entity.eta_otherarea);
			oCmd.Parameters.AddWithValue("@eta_contribute", entity.eta_contribute);
			oCmd.Parameters.AddWithValue("@ep_modempno", iPlan.ep_modempno);
			oCmd.Parameters.AddWithValue("@ep_modname", iPlan.ep_modname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SaveAuthpat(engage_authpat entity)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @id = 0
begin
	declare @ver int
	select @ver=ep_ver from engage_plan where ep_seqsn=@seqsn

	INSERT INTO [engagedb].[dbo].[engage_authpat]
           ([eap_seqsn],[eap_ver],[eap_patentno],[eap_authmethod]
           ,[eap_authitem1],[eap_authitem2],[eap_authitem3],[eap_authitem4]
           ,[eap_use],[eap_contribute])
     SELECT
           @seqsn,@ver,@eap_patentno,@eap_authmethod
           ,@eap_authitem1,@eap_authitem2,@eap_authitem3,@eap_authitem4
           ,eap_use=case when rtrim(p40_applynation)<>'TW' and (@eap_authitem1='1' or @eap_authitem3='1') then '1' else '0' end
           ,@eap_contribute      
        --舊契約Start
        from patent.dbo.v_pat040_engaage
        --舊契約End
        /*
        --新契約Start
        from patent.dbo.v_pat040_engage
        --新契約End      
        */
      where p40_patentno=@eap_patentno
end
else
begin
	UPDATE engage_authpat SET 
			eap_patentno=@eap_patentno,eap_authmethod=@eap_authmethod
			,eap_authitem1=@eap_authitem1,eap_authitem2=@eap_authitem2,eap_authitem3=@eap_authitem3,eap_authitem4=@eap_authitem4
			,eap_use=case when rtrim(p40_applynation)<>'TW' and (@eap_authitem1='1' or @eap_authitem3='1') then '1' else '0' end
			,eap_contribute=@eap_contribute
		FROM engage_authpat
        --舊契約Start
        JOIN patent.dbo.v_pat040_engaage ON eap_patentno=p40_patentno
        --舊契約End
        /*
        --新契約Start
		JOIN patent.dbo.v_pat040_engage ON eap_patentno=p40_patentno
        --新契約End      
        */
		WHERE eap_id=@id
end
update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@id", entity.eap_id);
			oCmd.Parameters.AddWithValue("@eap_patentno", entity.eap_patentno);
			oCmd.Parameters.AddWithValue("@eap_authmethod", entity.eap_authmethod);
			oCmd.Parameters.AddWithValue("@eap_authitem1", entity.eap_authitem1);
			oCmd.Parameters.AddWithValue("@eap_authitem2", entity.eap_authitem2);
			oCmd.Parameters.AddWithValue("@eap_authitem3", entity.eap_authitem3);
			oCmd.Parameters.AddWithValue("@eap_authitem4", entity.eap_authitem4);
			oCmd.Parameters.AddWithValue("@eap_contribute", entity.eap_contribute);
			oCmd.Parameters.AddWithValue("@ep_modempno", iPlan.ep_modempno);
			oCmd.Parameters.AddWithValue("@ep_modname", iPlan.ep_modname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}


		public bool SaveTechauthItem()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @eati_id = 0
begin
	declare @ver int
	select @ver=ep_ver from engage_plan where ep_seqsn=@seqsn

	INSERT INTO [engagedb].[dbo].[engage_authtechitem]
           (eati_seqsn, eati_eta_id, eati_ver, eati_techno,
		   eati_org, eati_techtype, eati_itemname, eati_itemdate
		   )
     SELECT
           @seqsn, @eati_eta_id, @ver, @eati_techno,
		   @eati_org, @eati_techtype, @eati_itemname, @eati_itemdate
end
else
begin
	UPDATE engage_authtechitem SET 
           eati_techno=@eati_techno,
		   eati_org=@eati_org, 
		   eati_techtype=@eati_techtype, 
		   eati_itemname=@eati_itemname, 
		   eati_itemdate=@eati_itemdate
		WHERE eati_id=@eati_id
end
update engage_plan set ep_modempno=@ep_modempno, ep_modname=@ep_modname, ep_moddate=convert(varchar(8),getdate(),112) where ep_seqsn=@seqsn
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ep_modempno", _empno);
			oCmd.Parameters.AddWithValue("@ep_modname", _empname);
			oCmd.Parameters.AddWithValue("@eati_id", eati_id);
			oCmd.Parameters.AddWithValue("@eati_eta_id", eati_eta_id);
			oCmd.Parameters.AddWithValue("@eati_techno", eati_techno);
			oCmd.Parameters.AddWithValue("@eati_org", eati_org);
			oCmd.Parameters.AddWithValue("@eati_techtype", eati_techtype);
			oCmd.Parameters.AddWithValue("@eati_itemname", eati_itemname);
			oCmd.Parameters.AddWithValue("@eati_itemdate", eati_itemdate);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}




		public bool DelAuthpat(int id)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete from engage_authpat where eap_id=@id
			";

			oCmd.Parameters.AddWithValue("@id", id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool AuthpatImport(string sql)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}


		#endregion

		#region 附檔上傳
		/// <summary>
		/// 取得文件(檔案)的列表, 依據 seqsn, filetype 
		/// </summary>
		/// <returns></returns>
		public DataTable GetFile()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	select ea_id, ea_seqsn, ea_ver, ea_master_id, ea_filetype, ea_doc,CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,ea_tran_timestamp ,ea_valid
	  from engage_attfile1
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn and ea_filetype=@filetype and ea_ver=@ea_ver and ea_master_id=@ea_master_id
end
else
begin
	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,null as ea_tran_timestamp ,ea_valid
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn and ea_filetype=@filetype
end";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			//oCmd.Parameters.AddWithValue("@filetype", iPlan_attfile.ea_filetype);
			oCmd.Parameters.AddWithValue("@filetype", ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_ver", ea_ver);
			oCmd.Parameters.AddWithValue("@ea_master_id", ea_master_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得文件(檔案)的最大版次
		/// </summary>
		/// <returns></returns>
		public DataTable GetFileByMaxVer()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	declare @max_ver int = 1
	if (@filetype='NH')
		select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

	select ea_id, ea_seqsn, ea_ver, ea_master_id, ea_filetype, ea_doc,CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,ea_tran_timestamp ,ea_valid
	  from engage_attfile1
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn and ea_filetype=@filetype and ea_ver=@max_ver
end
else
begin
	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,null as ea_tran_timestamp ,ea_valid
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn and ea_filetype=@filetype
			AND ea_id IN (SELECT max(ea_id) FROM engage_attfile2 WHERE ea_seqsn=@seqsn AND ea_filetype=@filetype GROUP BY ea_doc)
end";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@filetype", ea_filetype);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public DataTable GetFileByEaId()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	select ea_id, ea_seqsn, ea_ver, ea_master_id, ea_filetype, ea_doc,CONVERT(varchar,ea_uploaddate,111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,ea_tran_timestamp ,ea_valid
	  from engage_attfile1
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_id=@ea_id
end
else
begin
	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar,ea_uploaddate,111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,null as ea_tran_timestamp ,ea_valid
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_id=@ea_id
end";

			oCmd.Parameters.AddWithValue("@ea_id", ea_id);
			oCmd.Parameters.AddWithValue("@ea_filetype", ea_filetype);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetFile2_his()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @ea_doc nvarchar(200), @ea_ver tinyint

if exists(select 1 where @filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	select @ea_ver=ea_ver, @ea_doc=ea_doc from engage_attfile1 where ea_id=@ea_id 

	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar,ea_uploaddate,111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,ea_tran_timestamp ,ea_valid
	  from engage_attfile1
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn AND ea_filetype=@filetype
			and ea_doc=@ea_doc and ea_ver < @ea_ver

end
else
begin
	select @ea_ver=ea_ver, @ea_doc=ea_doc from engage_attfile2 where ea_id=@ea_id 

	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar,ea_uploaddate,111) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
			,null as ea_tran_timestamp ,ea_valid
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn AND ea_filetype=@filetype
			and ea_doc=@ea_doc and ea_ver < @ea_ver
end
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@filetype", _ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_id", _ea_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 附檔的新增
		/// </summary>
		/// <returns></returns>
		public bool AttfileInsert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @ea_id bigint, @ea_ver int

if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	if @ea_filetype = 'NH'
	begin
		exec [pr_engage_DOC_reset_timestamp_upload] @seqsn, @ea_master_id
		select @ea_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn 
	end
	else if @ea_filetype = 'NF'
		select @ea_ver=ecr1_ver from engage_cost_review1 where ecr1_seqsn=@seqsn 
	else
		SET @ea_ver = (SELECT isnull(MAX(ea_ver),0)+1 FROM engage_attfile1 WHERE ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_doc=@ea_doc)

	INSERT INTO engage_attfile1
		(ea_seqsn,ea_ver,ea_filetype,ea_doc,ea_uploaddate,ea_filename,ea_filetxt,ea_keyinempno,ea_keyinempname,ea_master_id)
	VALUES
		(@seqsn,@ea_ver,@ea_filetype,@ea_doc,getdate(),@ea_filename,@ea_filetxt,@ea_keyinempno,@ea_keyinempname,@ea_master_id)

	SELECT @ea_id=@@identity

	update engage_attfile1 SET ea_filename=ea_filetype+'-'+CAST(ea_seqsn AS varchar)+'-'+CAST(ea_id AS varchar)+'-'+ea_filename WHERE ea_id=@ea_id

	SELECT ea_filename FROM engage_attfile1 WHERE ea_id=@ea_id
 end
else if exists(select 1 where @ea_filetype in ('FC','FD'))
begin
	SET @ea_ver = (SELECT isnull(MAX(esc_ver),0) FROM engage_signcont WHERE esc_seqsn=@seqsn)

	INSERT INTO engage_attfile2
		(ea_seqsn,ea_ver,ea_filetype,ea_doc,ea_uploaddate,ea_filename,ea_filetxt,ea_keyinempno,ea_keyinempname)
	VALUES
		(@seqsn,@ea_ver,@ea_filetype,@ea_doc,getdate(),@ea_filename,@ea_filetxt,@ea_keyinempno,@ea_keyinempname)

	SELECT @ea_id=@@identity

	update engage_attfile2 SET ea_filename=ea_filetype+'-'+CAST(ea_seqsn AS varchar)+'-'+CAST(ea_id AS varchar)+'-'+ea_filename WHERE ea_id=@ea_id

	SELECT ea_filename FROM engage_attfile2 WHERE ea_id=@ea_id
end
else
begin
	SET @ea_ver = (SELECT isnull(MAX(ea_ver),0)+1 FROM engage_attfile2 WHERE ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_doc=@ea_doc)

	INSERT INTO engage_attfile2
		(ea_seqsn,ea_ver,ea_filetype,ea_doc,ea_uploaddate,ea_filename,ea_filetxt,ea_keyinempno,ea_keyinempname)
	VALUES
		(@seqsn,@ea_ver,@ea_filetype,@ea_doc,getdate(),@ea_filename,@ea_filetxt,@ea_keyinempno,@ea_keyinempname)

	SELECT @ea_id=@@identity

	update engage_attfile2 SET ea_filename=ea_filetype+'-'+CAST(ea_seqsn AS varchar)+'-'+CAST(ea_id AS varchar)+'-'+ea_filename WHERE ea_id=@ea_id

	SELECT ea_filename FROM engage_attfile2 WHERE ea_id=@ea_id
end
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ea_filetype", _ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_doc", _ea_doc);
			oCmd.Parameters.AddWithValue("@ea_filename", _ea_filename);
			oCmd.Parameters.AddWithValue("@ea_filetxt", _ea_filetxt);
			oCmd.Parameters.AddWithValue("@ea_keyinempno", _empno);
			oCmd.Parameters.AddWithValue("@ea_keyinempname", _empname);
			oCmd.Parameters.AddWithValue("@ea_master_id", ea_master_id);

			try
			{
				_ea_filename = this.getTopOne(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool AttfileInsertTimestamp()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @new_ea_id bigint

insert into engage_attfile1
	([ea_seqsn],[ea_ver],[ea_master_id],[ea_filetype],[ea_doc]
     ,[ea_uploaddate],[ea_filename],[ea_filetxt],[ea_keyinempno],[ea_keyinempname]
     ,[ea_file_url],[ea_tran_timestamp],[ea_valid])
select	[ea_seqsn],[ea_ver],[ea_master_id],[ea_filetype],[ea_doc]
		,[ea_uploaddate],[ea_doc],[ea_filetxt],@ea_keyinempno,@ea_keyinempname
		,[ea_file_url],@tran_timestamp,'1'
  from	engage_attfile1
  where ea_id = @ea_id
  
SELECT @new_ea_id=@@identity

update engage_attfile1 set
	ea_filename=ea_filetype+'-'+CAST(ea_seqsn AS varchar)+'-'+CAST(ea_id AS varchar)+'-'+ea_doc+'.pdf',
	ea_tran_timestamp=tc_planno+tc_ver+tc_seqsn+'_'+@tran_timestamp
  from	engage_signitem1 
  left join v_engage_sign_choose_treaty_case on esi1_tc_seno = tc_seno 
  where ea_id = @new_ea_id and ea_master_id = esi1_id
 
update	engage_signitem1 set
	esi1_tran_empno = ea_keyinempno,
	esi1_tran_timestamp = ea_tran_timestamp
  from	engage_attfile1
  where ea_id = @new_ea_id and ea_master_id = esi1_id	  

SELECT @new_ea_id as ea_id
";

			oCmd.Parameters.AddWithValue("@ea_id", _ea_id);
			oCmd.Parameters.AddWithValue("@ea_keyinempno", _ea_keyinempno);
			oCmd.Parameters.AddWithValue("@ea_keyinempname", _ea_keyinempname);
			oCmd.Parameters.AddWithValue("@tran_timestamp", _ea_tran_timestamp);

			try
			{
				_ea_id = this.getTopOne(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public string GetTimestampByEaId()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select ea_tran_timestamp from engage_attfile1 where ea_id = @ea_id";

			oCmd.Parameters.AddWithValue("@ea_id", _ea_id);
			return this.getTopOne(oCmd, CommandType.Text);
		}

		/// <summary>
		/// 附檔的更新-修改概要
		/// </summary>
		/// <returns></returns>
		public bool AttfileUpdate()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	update engage_attfile1 SET ea_filetxt=@ea_filetxt WHERE ea_id=@ea_id
end
else
begin
	update engage_attfile2 SET ea_filetxt=@ea_filetxt WHERE ea_id=@ea_id
end
";

			oCmd.Parameters.AddWithValue("@ea_id", ea_id);
			oCmd.Parameters.AddWithValue("@ea_filetype", ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_filetxt", ea_filetxt);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool AttfileDeleteById()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	if @ea_filetype = 'NH'
	begin
		declare @esi1_id bigint
		select @esi1_id=ea_master_id from engage_attfile1 where ea_id=@ea_id
		exec [pr_engage_DOC_reset_timestamp_upload] @seqsn, @esi1_id
	end

	delete from engage_attfile1 where ea_id=@ea_id
end
else
begin
	delete from engage_attfile2 where ea_id=@ea_id
end
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ea_filetype", ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_id", _ea_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool AttfileDelete()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	delete from engage_attfile1 where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_filename=@ea_filename	and ea_keyinempno=@ea_keyinempno
end
else
begin
	delete from engage_attfile2 where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_filename=@ea_filename	and ea_keyinempno=@ea_keyinempno
end
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ea_filetype", ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_filename", ea_filename);
			oCmd.Parameters.AddWithValue("@ea_keyinempno", _empno);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 下載檔案，記錄下載人的相關訊息
		/// </summary>
		/// <returns></returns>
		public bool InsertLog()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	INSERT INTO [engagedb].[dbo].[engagedb_log]
           (sl_id, sl_sysid,sl_empno,sl_function,sl_keyword,sl_createtime,sl_ip,
            sl_from,sl_ea_id,sl_ea_seqsn)
	select newid(),@sl_sysid, @empno, @sl_function, @sl_keyword, GETDATE(),@sl_ip,
			'attfile1', ea_id, ea_seqsn
	from	engage_attfile1 
	where ea_id=@ea_id
end
else
begin
	INSERT INTO [engagedb].[dbo].[engagedb_log]
           (sl_id,sl_sysid,sl_empno,sl_function,sl_keyword,sl_createtime,sl_ip,
            sl_from,sl_ea_id,sl_ea_seqsn)
	select newid(),@sl_sysid, @empno, @sl_function, @sl_keyword, GETDATE(),@sl_ip,
			'attfile2', ea_id, ea_seqsn
	from	engage_attfile2 
	where ea_id=@ea_id
end
			";

			oCmd.Parameters.AddWithValue("@ea_id", ea_id);
			oCmd.Parameters.AddWithValue("@ea_filetype", ea_filetype);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@sl_sysid", sl_sysid);
			oCmd.Parameters.AddWithValue("@sl_function", sl_function);
			oCmd.Parameters.AddWithValue("@sl_keyword", sl_keyword);
			oCmd.Parameters.AddWithValue("@sl_ip", sl_ip);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SaveFileList()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select 1 where @ea_filetype in ('NB', 'NC', 'ND', 'NF', 'NH'))
begin
	if @ea_filetype = 'NH'
	update engage_attfile1 set ea_valid='0'
		where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_id not in (select [Value] from fn_split_no_repeat(@emplist, ','))
				and ea_master_id=@ea_master_id
	
	update engage_attfile1 set ea_valid='1'
		where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_id in (select [Value] from fn_split_no_repeat(@emplist, ','))
				and ea_master_id=@ea_master_id
end
else
begin
	update engage_attfile2 set ea_valid='0'
		where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_id not in (select [Value] from fn_split_no_repeat(@emplist, ','))
	
	update engage_attfile2 set ea_valid='1'
		where ea_seqsn=@seqsn and ea_filetype=@ea_filetype and ea_id in (select [Value] from fn_split_no_repeat(@emplist, ','))
end
			";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@emplist", _rec_emplist);
			oCmd.Parameters.AddWithValue("@ea_filetype", iPlan_attfile.ea_filetype);
			oCmd.Parameters.AddWithValue("@ea_master_id", ea_master_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 文件清單 - 議約
		/// </summary>
		/// <returns></returns>
		public DataTable GetFileForTreaty()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @contno varchar(11)
select @contno=eb_year+eb_orgcd+eb_class+eb_sn from engage_base where eb_seqsn=@seqsn
exec [esp_treatyCase_getAllFiles_ANMR] @contno
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion


		#region 成本訂價,單位固定分攤比率
		/// <summary>
		/// 單位固定分攤比率, 適用一段式簽核{ecr_onestep_sign}, 規劃構想審查及簽核方式{ecr_plan_reviewtype: 2, 3}
		/// </summary>
		/// <returns></returns>
		public DataTable GetCostRate()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select * from engage_costrate where ecr_org=@orgcd 
";

			oCmd.Parameters.AddWithValue("@orgcd", _qry_orgcd);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion


		/// <summary>
		/// 業務管理＞表單參數設定
		/// </summary>
		/// <returns></returns>
		public DataTable GetFormContent(string orgcd, string type)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select * from engage_form where form_orgcd=@orgcd and form_type=@type";

			oCmd.Parameters.AddWithValue("@orgcd", orgcd);
			oCmd.Parameters.AddWithValue("@type", type);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			if(dt.Rows.Count == 0)
			{
				//當該單位無對應資料時代入00為預設值
				oCmd.Parameters["@orgcd"].Value = "00";
				dt = this.getDataTable(oCmd, CommandType.Text);
			}

			return dt;
		}

		/// <summary>
		/// 儲存 - 表單參數設定
		/// </summary>
		/// <param name="orgcd"></param>
		/// <param name="type"></param>
		/// <param name="content"></param>
		/// <returns></returns>
		public bool SaveFormContent(string orgcd, string type, string content)
		{
			bool success = false;

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @today varchar(8)
select @today = CONVERT(varchar, getdate(), 112)
if exists(select * from engage_form where form_orgcd=@orgcd and form_type=@type)
begin
	update engage_form 
	set
		form_content = @form_content,
		form_modempno = @empno,
		form_modname = @empname,
		form_moddate = @today
	where form_orgcd=@orgcd and form_type=@type	
end
else
begin
	insert into engage_form 
		(form_orgcd, form_type, form_content, form_keyinempno, form_keyinname, form_keyindate, form_modempno, form_modname, form_moddate)
	select
		@orgcd, @type, @form_content, @empno, @empname, @today, @empno, @empname, @today	
end
";

			oCmd.Parameters.AddWithValue("@orgcd", orgcd);
			oCmd.Parameters.AddWithValue("@type", type);
			oCmd.Parameters.AddWithValue("@form_content", content);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}


        public bool SaveExecstatusMemo(string memo, string indus_flag)
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
	update engage_base
	set
	    eb_modempno=@empno,
	    eb_modname=@empname,
	    eb_moddate=CONVERT(varchar(8),GETDATE(),112),
        eb_execstatus_memo=@memo, 
        eb_indus_flag=@indus_flag,
        eb_execstatus = case when @indus_flag = '1' then 'P3' else eb_execstatus end
      WHERE eb_seqsn=@seqsn and eb_execstatus in ('P1', 'P3')			
		
";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@empname", _empname);
            oCmd.Parameters.AddWithValue("@memo", memo);
            oCmd.Parameters.AddWithValue("@indus_flag", indus_flag);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
                throw ex;
            }
            return success;
        }


        /// <summary>
        /// 取得系統自訂的外部聯結
        /// </summary>
        /// <param name="connid"></param>
        /// <returns></returns>
        public string GetOuterDbConn(string connid)
        {
            string retstr = "";

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"SELECT connstr FROM [OUTERDBCONN] WHERE connid = @connid";

            oCmd.Parameters.AddWithValue("@connid", connid);

            try
            {
                retstr = this.getTopOne(oCmd, CommandType.Text);
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return retstr;
        }

        #region 移除 SQL Injection
        /// <summary>
        /// SQL Injection 防範之道, 共用的置換 function.
        /// </summary>
        /// <param name="inputString"></param>
        /// <returns></returns>
        public static string SQLInjectionReplace_myDb(string inputString)
        {
            //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
            //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
            return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
        }
        #endregion

        #region 移除Shell攻擊注入
        /// <summary>
        /// 移除Shell攻擊注入
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveShell_myDb(string str)
        {
            string strOri = str;
            if (!string.IsNullOrEmpty(strOri))
            {
                strOri = strOri.Replace("&", "");
                strOri = strOri.Replace("$", "");
                strOri = strOri.Replace(";", "");
                strOri = strOri.Replace("..", "");
            }

            return strOri;
        }
        #endregion
    }

}