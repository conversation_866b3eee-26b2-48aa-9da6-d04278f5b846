﻿using System;
using System.IO;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Engage
{
	/// <summary>
	/// Summary description for ControlManager
	/// </summary>
	public class ControlManager
	{
		#region 私有變數

		private bool isDisableWebButton = true;
		private bool isDisableHtmlButton = true;
		private bool isDisableWebDropDownList = true;
		private bool isDisableWebTextBox = true;
		private bool isDisableWebRadioButtonList = true;
		private bool isDisableWebRadioButton = true;
		private bool isDisableHtmlImage = true;
		private bool isDisableWebCheckBoxList = true;
		private bool isDisableWebCheckBox = true;
		private bool controlStatus;

		#endregion

		#region 公共屬性設定

		public bool IsDisableWebButton
		{
			get { return this.isDisableWebButton; }
			set { this.isDisableWebButton = value; }
		}

		public bool IsDisableHtmlButton
		{
			get { return this.isDisableHtmlButton; }
			set { this.isDisableHtmlButton = value; }
		}
		public bool IsDisableWebDropDownList
		{
			get { return this.isDisableWebDropDownList; }
			set { this.isDisableWebDropDownList = value; }
		}
		public bool IsDisableWebTextBox
		{
			get { return this.isDisableWebTextBox; }
			set { this.isDisableWebTextBox = value; }
		}
		public bool IsDisableWebRadioButtonList
		{
			get { return this.isDisableWebRadioButtonList; }
			set { this.isDisableWebRadioButtonList = value; }
		}
		public bool IsDisableWebRadioButton
		{
			get { return this.isDisableWebRadioButton; }
			set { this.isDisableWebRadioButton = value; }
		}
		public bool IsDisableWebCheckBoxList
		{
			get { return this.isDisableWebCheckBoxList; }
			set { this.isDisableWebCheckBoxList = value; }
		}
		public bool IsDisableWebCheckBox
		{
			get { return this.isDisableWebCheckBox; }
			set { this.isDisableWebCheckBox = value; }
		}
		public bool IsDisableHtmlImage
		{
			get { return this.isDisableHtmlImage; }
			set { this.isDisableHtmlImage = value; }
		}

		#endregion

		#region 公共之方法宣告

		public void EnableControls(ControlCollection control)
		{
			this.controlStatus = true;
			FindSetConrol(control);
		}
		public void DisableControls(ControlCollection control)
		{
			this.controlStatus = false;
			FindSetConrol(control);
		}

		public void DisableControlsStyle(ControlCollection control)
		{
			for (int count1 = 0; count1 < control.Count; count1++)
			{
				//判斷是否有子控制項
				if (control[count1].HasControls())
				{
					//呼叫是否Disable本控制項之函數
					this.ControlDisableChangeStyle(control[count1]);
					//呼叫自己					
					this.DisableControlsStyle(control[count1].Controls);

				}
				else
				{
					//呼叫是否Disable本控制項之函數
					this.ControlDisableChangeStyle(control[count1]);
				}
			}
		}

		#endregion

		#region 私有變數之方法


		private void FindSetConrol(ControlCollection control)
		{
			for (int count1 = 0; count1 < control.Count; count1++)
			{
				//判斷是否有子控制項
				if (control[count1].HasControls())
				{
					//呼叫是否Disable本控制項之函數
					this.ControlDisable(control[count1]);
					//呼叫自己
					if (this.controlStatus)
						this.EnableControls(control[count1].Controls);
					else
						this.DisableControls(control[count1].Controls);
				}
				else
				{
					//呼叫是否Disable本控制項之函數
					this.ControlDisable(control[count1]);
				}
			}

		}

		//將控制項Disable
		private void ControlDisable(Control control)
		{
			//Disable Button
			if (this.isDisableWebButton) this.DisableWebControlsButton(control);
			//Disable HtmlButton
			if (this.isDisableHtmlButton) this.DisableHtmlControlsHtmlInputButton(control);
			//Disable DropDownList
			if (this.isDisableWebDropDownList) this.DisableWebControlsDropDownList(control);
			//Disable TextBox
			if (this.isDisableWebTextBox) this.DisableWebControlsTextBox(control);
			//Disable RadioButtonList
			if (this.isDisableWebRadioButtonList) this.DisableWebControlsRadioButtonList(control);
			//Disable RadioButton
			if (this.isDisableWebRadioButton) this.DisableWebControlsRadioButton(control);
			//Disable CheckBoxList
			if (this.isDisableWebCheckBoxList) this.DisableWebControlsCheckBoxList(control);
			//Disable CheckBox
			if (this.isDisableWebCheckBox) this.DisableWebControlsCheckBox(control);
			//Disable HtmlImage
			if (this.isDisableHtmlImage) this.DisableHtmlControlsHtmlImage(control);

		}

		private void ControlDisableChangeStyle(Control control)
		{
			this.DisableWebControlsStyleTextBox(control);
		}

		#endregion

		#region 各控制項

		//Disable WebControls[Button]
		private void DisableWebControlsButton(Control control)
		{
			Button button = new Button();

			if (control.GetType() == button.GetType())
			{
				button = (Button)control;
				button.Enabled = this.controlStatus;
			}
		}

		//Disable HtmlControls[Button]
		private void DisableHtmlControlsHtmlInputButton(Control control)
		{
			System.Web.UI.HtmlControls.HtmlInputButton htmlInputButton = new System.Web.UI.HtmlControls.HtmlInputButton();

			if (control.GetType() == htmlInputButton.GetType())
			{
				htmlInputButton = (System.Web.UI.HtmlControls.HtmlInputButton)control;
				if (this.controlStatus == true)
					htmlInputButton.Disabled = false;
				else
					htmlInputButton.Disabled = true;
			}
		}

		//Disable WebControls[Button]
		private void DisableWebControlsCheckBox(Control control)
		{
			CheckBox checkBox = new CheckBox();

			if (control.GetType() == checkBox.GetType())
			{
				checkBox = (CheckBox)control;
				checkBox.Enabled = this.controlStatus;
			}
		}

		//Disable WebControls[DropDownList]
		private void DisableWebControlsDropDownList(Control control)
		{
			DropDownList dropDownList = new DropDownList();

			if (control.GetType() == dropDownList.GetType())
			{
				dropDownList = (DropDownList)control;
				dropDownList.Enabled = this.controlStatus;
			}

		}

		//Disable WebControls[TextBox]
		private void DisableWebControlsTextBox(Control control)
		{
			TextBox textBox = new TextBox();

			if (textBox.GetType() == control.GetType().BaseType || textBox.GetType() == control.GetType())
			{
				textBox = (TextBox)control;
				if (this.controlStatus)
				{
					textBox.BorderStyle = BorderStyle.NotSet;
					textBox.ReadOnly = !this.controlStatus;
				}
				else
				{
					textBox.BorderStyle = BorderStyle.None;
					textBox.ReadOnly = !this.controlStatus;
				}
			}
		}

		//Disable WebControls[RadioButtonList]
		private void DisableWebControlsRadioButtonList(Control control)
		{
			RadioButtonList radioButtonList = new RadioButtonList();

			if (radioButtonList.GetType() == control.GetType())
			{
				radioButtonList = (RadioButtonList)control;
				radioButtonList.Enabled = this.controlStatus;
			}
		}

		//Disable WebControls[RadioButton]
		private void DisableWebControlsRadioButton(Control control)
		{
			RadioButton radioButton = new RadioButton();

			if (radioButton.GetType() == control.GetType())
			{
				radioButton = (RadioButton)control;
				radioButton.Enabled = this.controlStatus;
			}
		}

		//Disable WebControls[CheckBoxList]
		private void DisableWebControlsCheckBoxList(Control control)
		{
			CheckBoxList checkBoxList = new CheckBoxList();

			if (checkBoxList.GetType() == control.GetType())
			{
				checkBoxList = (CheckBoxList)control;
				checkBoxList.Enabled = this.controlStatus;
			}
		}



		//Disable Html[Image]
		private void DisableHtmlControlsHtmlImage(Control control)
		{
			System.Web.UI.HtmlControls.HtmlImage htmlImage = new System.Web.UI.HtmlControls.HtmlImage();

			if (htmlImage.GetType() == control.GetType())
			{
				htmlImage = (System.Web.UI.HtmlControls.HtmlImage)control;
				htmlImage.Visible = this.controlStatus;
			}
		}

		#endregion

		private void DisableWebControlsStyleTextBox(Control control)
		{
			TextBox textBox = new TextBox();

			if (textBox.GetType() == control.GetType().BaseType || textBox.GetType() == control.GetType())
			{
				textBox = (TextBox)control;
				if (textBox.ReadOnly)
					textBox.Attributes["style"] = textBox.Attributes["style"] + ";BACKGROUND-COLOR: #ECE9D8";

			}
		}
	}

}