﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Business 的摘要描述
    /// </summary>
    public class Business : GPI.mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;
        private string _empno;

        private string mail_orgcd;
        private string mail_mailtype;
        private string mail_recname;
        private string mail_recanother;
        private string mail_ccname;
        private string mail_ccanother;
        private string mail_timetype;
        private int mail_days;

        //單位副主管簽核
        private int id;
        private string orgcd;
        private int bid_amt;
        private string formNo;
        private string roleclass;
        private string signclass;
        private int seq;
        private string ged_empno;

        #endregion

        #region 建構子

        public Business()
        {
            _errorMessage = String.Empty;
            _empno = String.Empty;

            mail_orgcd = String.Empty;
            mail_mailtype = String.Empty;
            mail_recname = String.Empty;
            mail_recanother = String.Empty;
            mail_ccname = String.Empty;
            mail_ccanother = String.Empty;
            mail_timetype = String.Empty;
            mail_days = 0;

            id = 0;
            orgcd = string.Empty;
            bid_amt = 0;
            formNo = "GPI01";
            roleclass = "單位副主管";
            signclass = "0";
            seq = 4;
            ged_empno = string.Empty;
        }

        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }
        /// <summary>
        /// 登入人員
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        /// <summary>
        /// 單位
        /// </summary>
        public string Mail_orgcd
        {
            get { return mail_orgcd; }
            set { mail_orgcd = value; }
        }
        /// <summary>
        /// Email名稱
        /// </summary>
        public string Mail_mailtype
        {
            get { return mail_mailtype; }
            set { mail_mailtype = value; }
        }
        /// <summary>
        /// 收件人
        /// </summary>
        public string Mail_recname
        {
            get { return mail_recname; }
            set { mail_recname = value; }
        }
        /// <summary>
        /// 其他固定人員(收件人)
        /// </summary>
        public string Mail_recanother
        {
            get { return mail_recanother; }
            set { mail_recanother = value; }
        }
        /// <summary>
        /// 副本
        /// </summary>
        public string Mail_ccname
        {
            get { return mail_ccname; }
            set { mail_ccname = value; }
        }
        /// <summary>
        /// 其他固定人員(副本)
        /// </summary>
        public string Mail_ccanother
        {
            get { return mail_ccanother; }
            set { mail_ccanother = value; }
        }
        /// <summary>
        /// 發信時間
        /// </summary>
        public string Mail_timetype
        {
            get { return mail_timetype; }
            set { mail_timetype = value; }
        }
        /// <summary>
        /// 發信天數
        /// </summary>
        public int Mail_days
        {
            get { return mail_days; }
            set { mail_days = value; }
        }

        /// <summary>
        /// 單位副主管簽核流水號
        /// </summary>
        public int Id
        {
            get { return id; }
            set { id = value; }
        }
        /// <summary>
        /// 單位副主管簽核流水號單位
        /// </summary>
        public string Orgcd
        {
            get { return orgcd; }
            set { orgcd = value; }
        }
        /// <summary>
        /// 單位副主管簽核金額
        /// </summary>
        public int Bid_amt
        {
            get { return bid_amt; }
            set { bid_amt = value; }
        }
        /// <summary>
        /// 單位副主管簽核單別
        /// </summary>
        public string FormNo
        {
            get { return formNo; }
            set { formNo = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Roleclass
        {
            get { return roleclass; }
            set { roleclass = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SignClass
        {
            get { return signclass; }
            set { signclass = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int Seq
        {
            get { return seq; }
            set { seq = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Ged_Empno
        {
            get { return ged_empno; }
            set { ged_empno = value; }
        }

        #endregion

        #region 公有函式
        public string GetRight()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_check_bu";

            oCmd.Parameters.AddWithValue("@empno", _empno);
            SqlParameter rtn_code = oCmd.Parameters.Add("@rtn_code", SqlDbType.VarChar, 2);
            rtn_code.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_code.Value.ToString();
        }
        public bool Update_mail_set()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_mail_set_update";

            oCmd.Parameters.AddWithValue("@mail_orgcd", mail_orgcd);
            oCmd.Parameters.AddWithValue("@mail_mailtype", mail_mailtype);
            oCmd.Parameters.AddWithValue("@mail_recname", mail_recname);
            oCmd.Parameters.AddWithValue("@mail_recanother", mail_recanother);
            oCmd.Parameters.AddWithValue("@mail_ccname", mail_ccname);
            oCmd.Parameters.AddWithValue("@mail_ccanother", mail_ccanother);
            oCmd.Parameters.AddWithValue("@mail_timetype", mail_timetype);
            oCmd.Parameters.AddWithValue("@mail_days", mail_days);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public DataTable Get_mail_type(string flage)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_mail_type";

            oCmd.Parameters.AddWithValue("@flage", flage);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public DataTable Get()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT [mail_orgcd]
      ,[mail_mailtype]
      ,[mail_recname]
      ,[mail_recanother]
      ,[mail_ccname]
      ,[mail_ccanother]
      ,[mail_timetype]
   ,[mail_days] FROM gpi_mailset WHERE mail_orgcd=@mail_orgcd 
AND mail_mailtype=@mail_mailtype
";

            oCmd.Parameters.AddWithValue("@mail_orgcd", mail_orgcd);
            oCmd.Parameters.AddWithValue("@mail_mailtype", mail_mailtype);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 發送未送出檢核表提醒(B)通知信
        /// </summary>
        /// <returns></returns>
        public bool SendMailB()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetMailBMessage";

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 發送未完成投標提醒(C)通知信
        /// </summary>
        /// <returns></returns>
        public bool SendMailC()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetMailCMessage";

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 發送投標後追蹤(D)通知信
        /// </summary>
        /// <returns></returns>
        public bool SendMailD()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetMailDMessage";

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        #region 單位副主管簽核設定

        public DataTable Get_roleSignClass()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_org_amt_qry";

            oCmd.Parameters.AddWithValue("@empno", _empno);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public DataTable Get_roleSignClass_org()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_org_amt_qry1";

            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public bool Update_org_amt()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_org_amt_update";
            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@amt", bid_amt);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Delete_org_amt()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
DELETE gpi_ecp_roleSignClass
WHERE orgcd = @orgcd 
    AND FormNo = @formNo
    AND roleclass = @roleclass
    AND SignClass = @signclass
    AND Seq = @seq

EXEC pr_gpi_empno_default_delete @orgcd = @orgcd
";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@formNo", formNo);
            oCmd.Parameters.AddWithValue("@roleclass", roleclass);
            oCmd.Parameters.AddWithValue("@signclass", signclass);
            oCmd.Parameters.AddWithValue("@seq", seq);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Update_ged_empno_delete()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_empno_default_delete";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Update_ged_empno_update()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_empno_default_update";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@ged_empno", ged_empno);
            oCmd.Parameters.AddWithValue("@keyinempno", _empno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        #endregion

        #endregion

    }
}