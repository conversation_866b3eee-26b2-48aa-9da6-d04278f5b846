﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_valuation_inspact.aspx.cs" Inherits="TreatyCase_valuation_inspact" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>案件審查</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("審查完成!");
            parent.$.fn.colorbox.close();
        }
        function treaty_fileup() {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_valuation_FileUp.aspx?seno=<%=Server.HtmlEncode(Request.QueryString["seno"]) %>"
                , title: '上傳檔案'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_valuation_FileUp.aspx?fid=" + fid + "&seno=<%=Server.HtmlEncode(Request.QueryString["seno"]) %>"
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 25px; width: 800px">
                <tr>
                    <td class="td_right">審查意見：<br />
                        <asp:Label ID="LB_tcii_seno" runat="server" Text="" Visible="false"></asp:Label></td>
                    <td>
                        <asp:TextBox ID="TB_審查意見" runat="server" Width="500px" Height="40px" TextMode="MultiLine"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">審查結果：</td>
                    <td>
                        <asp:DropDownList ID="DDL_審查結果" runat="server">
                            <asp:ListItem Value="">--請選擇--</asp:ListItem>
                            <asp:ListItem Value="Y">同意</asp:ListItem>
                            <asp:ListItem Value="N">不同意</asp:ListItem>
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <%--				    <td class="td_right">新增審查人：</td>
				    <td><asp:dropdownlist id="DDL_AssignInspect" runat="server" DataTextField="Text" DataValueField="Value"   DataSourceID="SDS_SC" Height="20px" Width="145px" AppendDataBoundItems="True">
                        <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
				        </asp:dropdownlist>(如需指定下一個審查人,請指定審查人)
                    </td>--%>
                </tr>
                <tr>
                    <td class="td_right" colspan="2">
                        <asp:Button ID="BT_Save" runat="server" Text="審查存檔" class="genbtnS" OnClick="BT_Save_Click" /></td>
                </tr>
                <asp:PlaceHolder ID="PH_file" runat="server" Visible="false">


                    <tr>
                        <td class="td_right" colspan="2">&nbsp;<asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                            <Columns>
                                <asp:TemplateField HeaderText="附件名稱">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LinkButton1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>'> </asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="300px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="說要">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="250px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="上傳人">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_4" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="60px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="上傳日期">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_date").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="70px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>
                            </Columns>
                            <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                            <PagerSettings Position="Bottom" />
                            <PagerStyle HorizontalAlign="Left" />
                        </asp:GridView>
                            <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                        </td>
                    </tr>
                </asp:PlaceHolder>
            </table>
        </span>
        <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"   />--%>
    </form>
</body>
</html>
