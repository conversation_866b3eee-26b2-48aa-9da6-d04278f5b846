﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_manager_tip_list : System.Web.UI.Page
{
    RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
            BindData();
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.BT_addMore));
        if (Request.Params.Get("__EVENTTARGET") == "renew")
        {
            BindData();
        }
    }

    private void BindData()
    {
        DataTable dt = new DataTable();
        try
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"esp_treaty_TechCase_tip");
                oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(DDL_type.SelectedValue));
                oCmd.Parameters.AddWithValue("@tip_title", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@tipContent", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("List"));
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.CommandTimeout = 0;

                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                oda.Fill(dt);
            }
        }
        catch (Exception ex)
        {
            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

            oRCM.ErrorExceptionDataToDB(logMail);
        }
        sgvList.DataSource = dt;
        sgvList.DataBind();
    }

    protected void sgvList_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.sgvList.PageIndex = e.NewPageIndex;
        BindData();
    }

    protected void sgvList_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            ImageButton ib_mod = (ImageButton)e.Row.FindControl("IB_mod");
            if (ib_mod != null)
            {
                ib_mod.Attributes.Add("onclick", "Tip(" + DataBinder.Eval(e.Row.DataItem, "tip_id") + ",'Modify');");
            }
            ImageButton ib_del = (ImageButton)e.Row.FindControl("IB_del");
            if (ib_del != null)
                ib_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

            Literal lt_view = (Literal)e.Row.FindControl("LT_view");
            if (lt_view != null)
                lt_view.Text = "<a class='iterm_dymanic' rel='./tip_View.aspx?tid=" + DataBinder.Eval(e.Row.DataItem, "tip_id") + "' ><img src='../../images/icon_tips.png' style='width:20px;height:20px'></img> </a>";
        }
    }

    protected void sgvList_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "Del")
        {
            try
            {
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_treaty_TechCase_tip");
                    oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                    oCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(""));
                    oCmd.Parameters.AddWithValue("@tip_title", oRCM.SQLInjectionReplaceAll(""));
                    oCmd.Parameters.AddWithValue("@tipContent", oRCM.SQLInjectionReplaceAll(""));
                    oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("Del"));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;
                    oCmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }

            BindData();
        }
    }

    protected void DDL_typw_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindData();
    }


    protected void sgvList_Sorting(object sender, GridViewSortEventArgs e)
    {
        sgvList.PageIndex = 0;
        BindData();
    }
}