﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AuthRight.aspx.cs" Inherits="AuthRight" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript">
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function Find_Empno(obj, arg_sw) {
            var xGuid = newGuid();
            var ret_url = escape("../subap/colorbox_close.aspx");
            $(".ajax_mesg").colorbox({
                href: "./subap/Qry_empno_cb.aspx?hfValue=" + $('#' + obj).val() + "&Commonkey=" + xGuid + "&SystemCode=F2-99&url=" + ret_url
                , iframe: true, width: "85%", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../subap/ret_employee.aspx?Commonkey=' + xGuid;
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '&callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '&callback=?', jsonp_callback2);
                    }
                    //$.fn.colorbox.close();
                }
            });
        }
        function Find_empno_kw(obj, arg_sw) {
            var xGuid = newGuid();
            var strURL = "./subap/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val()) + "&Commonkey=" + xGuid + "&SystemCode=F0-99";
            if (arg_sw == "1") {
                $('#txt_promoter_empno').val("");
                $('#txt_promoter_name').val("");
                $('#txtTel').val("");
                $('#txtOrgAbbrName').val("");
                $('#x_dept').val("");
                $('#txt_req_dept').val("");
                $('#txtOrgAbbrName').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback1);
            }
            if (arg_sw == "2") {
                $('#h_px_empno').val("");
                $('#txt_px_name').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#txt_promoter_empno').val("");
                    $('#txt_promoter_name').val("");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#txt_promoter_empno').val(data.c_com_empno);
                    $('#txt_promoter_name').val(data.c_com_cname);
                    $('#txtTel').val(data.c_com_telext);
                    $('#txtOrgAbbrName').val(data.c_com_orgcd);
                    $('#x_dept').val(data.c_com_deptcd);
                    $('#txt_req_dept').val(data.c_com_deptid);
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#txt_promoter_empno').val("");
                    $('#txt_promoter_name').val("");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <table border="0" width="800px">

            <tr>
                <td>人員:<asp:TextBox ID="txt_promoter_name" runat="server" Width="95px" class="inputex validate[required] text-input"></asp:TextBox><asp:HiddenField ID="txt_promoter_empno" runat="server" />
                    <a onclick="javascript:Find_Empno('txt_promoter_name','1');">
                        <img id="img_promoter_name" src="../images/ssearch.gif" border="0" class="ajax_mesg btn_mouseout" /></a>  </td>
                <td>角色:
                 <asp:DropDownList ID="DDL_role" runat="server">
                     <asp:ListItem Value="8">院幕僚</asp:ListItem>
                     <asp:ListItem Value="11">單位幕僚</asp:ListItem>
                     <asp:ListItem Value="16">業務負責人</asp:ListItem>
                 </asp:DropDownList>
                </td>
                <td>模組:
                 <asp:DropDownList ID="DDL_category" runat="server">
                     <asp:ListItem Value="C0100">人力</asp:ListItem>
                     <asp:ListItem Value="C0200">會計</asp:ListItem>
                     <asp:ListItem Value="C0300">行政</asp:ListItem>
                     <asp:ListItem Value="C0400">企研</asp:ListItem>
                     <asp:ListItem Value="C0500">產服</asp:ListItem>
                     <asp:ListItem Value="C0600">技轉</asp:ListItem>
                 </asp:DropDownList>
                    <asp:Button ID="BT_add" runat="server" Text="新增" OnClick="BT_add_Click" />
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <hr />
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <asp:TextBox ID="TB_kw" runat="server" Width="724px"></asp:TextBox>
                    <asp:Button ID="BT_search" runat="server" Text="查詢" OnClick="BT_search_Click" /></td>
            </tr>
        </table>


        <cc1:SmartGridView ID="gv_role_list" OnRowCommand="gv_role_list_RowCommand" OnRowDataBound="gv_role_list_RowDataBound" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" DataSourceID="SDS_gv_file" AllowSorting="True" OnSorting="gv_role_list_Sorting">
            <FooterStyle Font-Bold="True" ForeColor="Black" />
            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
            <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
            <AlternatingRowStyle CssClass="TRowEven" />
            <Columns>
                <asp:TemplateField HeaderText="維護">
                    <ItemTemplate>
                        <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("rk_id") %>'>刪除</asp:LinkButton>
                    </ItemTemplate>
                    <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                </asp:TemplateField>
                <asp:BoundField DataField="rk_Uname" HeaderText="人名">
                    <HeaderStyle Width="150px"></HeaderStyle>
                </asp:BoundField>
                <asp:BoundField DataField="rk_role" HeaderText="角色" SortExpression="rk_role">
                    <HeaderStyle Width="150px"></HeaderStyle>
                </asp:BoundField>
                <asp:BoundField DataField="rk_categoryName" HeaderText="模組">
                    <HeaderStyle Width="150px"></HeaderStyle>
                </asp:BoundField>
                <asp:BoundField DataField="rk_orglist" HeaderText="檢視單位">
                    <HeaderStyle Width="150px"></HeaderStyle>
                </asp:BoundField>
            </Columns>
            <EmptyDataTemplate>
                <!--當找不到資料時則顯示「無資料」-->
                <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料，請新增!"></asp:Label>
            </EmptyDataTemplate>
            <FooterStyle BackColor="White" />
            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
        </cc1:SmartGridView>
        <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:infomgtConn %>" ></asp:SqlDataSource>--%>
    </form>
</body>
</html>
