﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApply_NewVer.aspx.cs" Inherits="Treaty_webpage_TreatyApply_NewVer" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        parent.$.colorbox.data = "";
        function setReturnValue(retVal) {
            parent.$.colorbox.data = retVal;
            parent.$.colorbox.close();
        }

    </script>
</head>
<body>
    <form id="form1" runat="server">

        <table border="0" cellspacing="10" width="420px" style="margin-left: 5px; margin-top: 20px">
            <tr>
                <td colspan="2">
                    <asp:Literal ID="LT_Ver" runat="server"></asp:Literal></td>
            </tr>
            <tr>
                <td align="center" colspan="2">
                    <span id="spNewCase" runat="server"><a class="button" id="btn_newcase" onclick="setReturnValue(1);" href="#">確定</a></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span id="spNewVer" runat="server"><a href="#" class="button" onclick="setReturnValue(2);" id='btn_newver'>新增相關契約</a></span><br />
                </td>
            </tr>
        </table>
        <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
