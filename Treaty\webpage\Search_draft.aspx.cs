﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Search_draft : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDanger(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;
            ViewState["sortorder"] = "";
            ViewState["sortField"] = "";
            BindContType();
            BindOrg();
            DoSearch();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }
    private void BindContType()
    {
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        ddlContType.Items.Clear();
        SqlDataSource SDS_ContType = new SqlDataSource();
        SDS_ContType.ID = "SDS_ContType";
        SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_ContType.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        SDS_ContType.SelectCommand = "esp_treatyCase_codetable";
        SDS_ContType.SelectParameters.Add("code_group", "");
        SDS_ContType.SelectParameters.Add("code_type", "10");
        for (int i = 0; i < SDS_ContType.SelectParameters.Count; i++)
        {
            SDS_ContType.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        SDS_ContType.DataBind();
        ddlContType.DataSource = SDS_ContType;
        ddlContType.DataBind();
        ddlContType.Items.Insert(0, "");
        SDS_ContType.Dispose();
    }
    private void BindOrg()
    {
        ddlOrgcd.Items.Clear();
        SqlDataSource SDS_Orgcd = new SqlDataSource();
        SDS_Orgcd.ID = "SDS_Orgcd";
        SDS_Orgcd.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_Orgcd.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        SDS_Orgcd.SelectCommand = "esp_treaty_search_draft_orglist";
        SDS_Orgcd.SelectParameters.Add("emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        for (int i = 0; i < SDS_Orgcd.SelectParameters.Count; i++)
        {
            SDS_Orgcd.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        SDS_Orgcd.DataBind();
        ddlOrgcd.DataSource = SDS_Orgcd;
        ddlOrgcd.DataBind();
        ddlOrgcd.Items.Insert(0, new ListItem("--請選擇-- ", "00"));
        SDS_Orgcd.Dispose();
    }
    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        //if (str_sortField == "")
        //    SDS_search.SelectCommand = " SELECT  * from v_treaty_search_draft where tmp_uid ='" + ssoUser.empNo + "' order by tmp_case_actno DESC  ";
        //else
        //    SDS_search.SelectCommand = " SELECT  * from v_treaty_search_draft where tmp_uid ='" + ssoUser.empNo + "' order by  " + str_sortField + " " + str_sort;

        //SDS_search.DataBind();
        //SGV_search.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.Clear();
            if (str_sortField == "")
                sqlCmd.CommandText = " SELECT  * from v_treaty_search_draft where tmp_uid =@tmp_uid order by tmp_case_actno DESC  ";
            else
                sqlCmd.CommandText = " SELECT  * from v_treaty_search_draft where tmp_uid =@tmp_uid order by  " + oRCM.SQLInjectionReplaceAll(str_sortField) + " " + oRCM.SQLInjectionReplaceAll(str_sort);

            sqlCmd.Parameters.AddWithValue("@tmp_uid", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_search.DataSource = dt;
                SGV_search.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void DoSearch()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        if (ddlContType.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (ddlOrgcd.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (ddlContType.SelectedValue.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (ddlOrgcd.SelectedValue.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxCompName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        //if (tbxHandleName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxPromoterName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxReqDept.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxReqDept.Text.Length >= 8) Response.Redirect("../danger.aspx");
        if (txtKeyWord.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (IsDanger(txtKeyWord.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxReqDept.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxCompName.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(TB_contnoName.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(DDL_CaseStatus.SelectedValue)) Response.Redirect("../danger.aspx");
        string str_class = "";
        str_class += (cbxCaseClass.Items[0].Selected == true) ? cbxCaseClass.Items[0].Value : "";
        str_class += (cbxCaseClass.Items[1].Selected == true) ? cbxCaseClass.Items[1].Value : "";
        str_class += (cbxCaseClass.Items[2].Selected == true) ? cbxCaseClass.Items[2].Value : "";
        str_class += (cbxCaseClass.Items[3].Selected == true) ? cbxCaseClass.Items[3].Value : "";
        str_class += (cbxCaseClass.Items[4].Selected == true) ? cbxCaseClass.Items[4].Value : "";
        str_class += (cbxCaseClass.Items[5].Selected == true) ? cbxCaseClass.Items[5].Value : "";
        str_class += (cbxCaseClass.Items[6].Selected == true) ? cbxCaseClass.Items[6].Value : "";
        str_class += (cbxCaseClass.Items[7].Selected == true) ? cbxCaseClass.Items[6].Value : "";
        str_class += (cbxCaseClass.Items[8].Selected == true) ? cbxCaseClass.Items[6].Value : "";
        if (IsDanger(str_class)) Response.Redirect("../danger.aspx");


        string str_CaseStatus = DDL_CaseStatus.SelectedValue;
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_treaty_search_draft";
        //SDS_NR.UpdateParameters.Add("login", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_NR.UpdateParameters.Add("orgcd", SQLInjectionReplaceAll(ddlOrgcd.SelectedValue ) ); //將舊案的流水號存入
        //SDS_NR.UpdateParameters.Add("conttype", SQLInjectionReplaceAll(ddlContType.SelectedValue)  );//洽案(契約)名稱
        //SDS_NR.UpdateParameters.Add("contnoName", SQLInjectionReplaceAll(TB_contnoName.Text));        
        //SDS_NR.UpdateParameters.Add("company", SQLInjectionReplaceAll(tbxCompName.Text) );
        //SDS_NR.UpdateParameters.Add("handle_name", SQLInjectionReplaceAll("") );
        //SDS_NR.UpdateParameters.Add("promoter_name", SQLInjectionReplaceAll(tbxPromoterName.Text ));
        //SDS_NR.UpdateParameters.Add("class", SQLInjectionReplaceAll(str_class));
        //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplaceAll(str_CaseStatus));
        //SDS_NR.UpdateParameters.Add("req_dept", SQLInjectionReplaceAll(tbxReqDept.Text ));
        //SDS_NR.UpdateParameters.Add("kw", SQLInjectionReplaceAll(txtKeyWord.Text ));
        //SDS_NR.Update();

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_draft";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        oCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(ddlOrgcd.SelectedValue)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("@conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));//洽案(契約)名稱
        oCmd.Parameters.AddWithValue("@contnoName", oRCM.SQLInjectionReplaceAll(TB_contnoName.Text));
        oCmd.Parameters.AddWithValue("@company", oRCM.SQLInjectionReplaceAll(tbxCompName.Text));
        oCmd.Parameters.AddWithValue("@handle_name", "");
        oCmd.Parameters.AddWithValue("@promoter_name", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
        oCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
        oCmd.Parameters.AddWithValue("@tc_degree", oRCM.SQLInjectionReplaceAll(str_CaseStatus));
        oCmd.Parameters.AddWithValue("@req_dept", oRCM.SQLInjectionReplaceAll(tbxReqDept.Text));
        oCmd.Parameters.AddWithValue("@kw", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        DoSearch();
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void btnQuery1_Click(object sender, EventArgs e)
    {
        txtKeyWord.Text = "";
        DoSearch();
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            StringBuilder script;
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            switch (arg[1].Substring(6, 1))
            {
                case "A":
                case "C":
                case "F":
                case "N":
                case "M":
                case "R":
                case "T":
                case "U":
                case "S":
                    Response.Redirect("./TreatyApply_view.aspx?seno=" + arg[0]);
                    break;
                default:
                    script = new StringBuilder("<script type='text/javascript'> alert('請暫時由舊系統內部查詢進入');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
            }
        }

        if (e.CommandName == "New")
        {
            StringBuilder script;
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            switch (arg[1].Substring(6, 1))
            {
                case "A":
                case "C":
                case "N":
                case "M":
                case "R":
                case "T":
                case "U":
                case "S":
                    script = new StringBuilder("<script type='text/javascript'>doNewCase('" + arg[0] + "','" + arg[1] + "');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
                default:
                    script = new StringBuilder("<script type='text/javascript'> alert('請暫時由舊系統內部查詢進入');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
            }
        }

    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal lb = (Literal)e.Row.FindControl("LB_status");
        if ((lb != null) && (lb.Text != "3"))
        {
            if (lb.Text == "2") lb.Text = "草稿";
            if (lb.Text == "E") lb.Text = "簽核中";
            if (lb.Text == "P") lb.Text = "法務內稽中";
        }
    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    protected void DDL_CaseStatus1_SelectedIndexChanged(object sender, EventArgs e)
    {

    }
    protected void DDL_CaseStatus_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (DDL_CaseStatus.SelectedValue == "0")
            Response.Redirect("./default.aspx");
        else
        {
            DoSearch();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }


}