﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Default_x.aspx.cs" Inherits="Default_x" %>
<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register src="Comp/TreeNode.ascx" tagname="TreeNode" tagprefix="uc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
<title>全院計畫管理系統</title>
<link href="css/myITRIproject/jquery-ui-1.10.3.custom.min.css" rel="stylesheet" type="text/css" />
<link href="css/style.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="./js/jquery-1.10.2.min.js"></script>
<script type="text/javascript" src="./js/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" src="./js/jquery.scrollbox.js"></script><!-- 跑馬燈 -->
<script type="text/javascript" src="./js/navigation.js"></script><!-- 下拉選單 -->
    <script type="text/javascript">

        function doNCase(seno) {
            window.location = "./Engage/Base/caseBase.aspx?seqsn=" + seno;
        }

        function doRCase(seno) {
            window.location = "./GPI/BaseData/CaseDetail.aspx?seqsn=" + seno;
        }
        function doTCase(seno) {
            window.location = "./Treaty/webpage/TreatyCase_view.aspx?seno=" + seno;
        }


    </script>

    <style type="text/css">
        .auto-style1 {
            height: 66px;
        }
    </style>
</head>
<body>
<form id="form1" runat="server">
  <div class="WrapperBody">
	<div class="WrapperContent">
    	<div class="WrapperHeader fixwidth">
        	<div class="logo"><a href="#"><img src="images/logoAll.gif" border="0" /></a></div>
            <div class="headernews">
            <ul>
            	<li><span class="font-red">[本系統各項資料』為工業技術研究院機密資料 禁止複製、轉載、外流（含內容的洩密）』</span></li>
                <li><span class="font-red">ITRI CONFIDENTIAL DOCUMENT DO NOT COPY OR DISTRIBUTE</span></li>
                <li><span class="font-normal">重要提醒:101年10月1日『個資法』實施，請勿將聯絡人之「個人資訊」運用於該契約之計畫執行以外用途，如研討會、訓練課程等信函／ＤＭ發送對象。</span></li>
            </ul>
             
            </div><!-- headernews -->
            <div class="infolink font-normal">
             登入者：<asp:Literal ID="LT_loginName" runat="server"></asp:Literal>   <a href="https://itriweb.itri.org.tw">itriweb</a> |  <a href="https://msx.itri.org.tw/owa">itrimail</a> |  <a href="https://empfinder.itri.org.tw/WebPage/ED_QueryIndex.aspx">尋找工研人</a> |  <a href="https://itriweb.itri.org.tw/system/statist/siteCount.aspx?id=w00386">電子簽核</a> |  <a href="https://itrisharing.itri.org.tw">itrisharing</a>  
            </div><!-- infolink -->
            <div class="basemenu">
            <div class="twocol menuwidth">
	            <div class="left"> 
                </div><!-- left -->
                <div class="right">
                <!--{* 選單start *}-->
                     <uc1:TreeNode ID="TreeNode1" runat="server" />
                <!--{* 選單end *}--></div><!-- right -->
            </div><!-- twocol -->
            </div><!-- basemenu -->
        </div><!-- WrapperHeader -->
        <div class="WrapperMain">
            <div class="fixwidth">
                <div class="twocol underlineT1">
	                <div class="left">首頁>案件列表</div>
                    <div class="right">
                        <table width="470" border="0" cellspacing="5" cellpadding="0">
                          <tr>
                            <td width="80" align="right"><div class="font-title titlebackicon">關鍵字</div></td>
                            <td width="200"><asp:TextBox ID="TB_kw" runat="server" class="inputex width100 inputhint" title="ex：案號、名稱、客戶名稱(統編)" ></asp:TextBox>  </td>
                            <td> <asp:button id="btnQuery" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery_Click"></asp:button> 
                                &nbsp;<span class="font-normal font-size3 font-bold"><img src="images/icon-1301.gif" /><a href="#" id="advancesearchopen">進階查詢</a></span></td>
                          </tr>
                        </table>
                    </div><!-- right -->
                </div><!-- twocol -->
                <div class="twocol margin10TB">
                    <table width="100%" border="0" cellspacing="5" cellpadding="0" class="font-size3 font-bold">
                      <tr>
                        <td><div class="bgstepblue">洽案/標案</div></td>
                        <td><img src="images/icon-2601.gif" /></td>
                        <td><div class="bgstepblue">議約</div></td>
                        <td><img src="images/icon-2601.gif" /></td>
                        <td><div class="bgstep font-nowork">簽約</div></td>
                        <td><img src="images/icon-2601.gif" /></td>
                        <td><div class="bgstep font-nowork">計畫執行</div></td>
                        <td><img src="images/icon-2601.gif" /></td>
                        <td><div class="bgstepblue">滿意度</div></td>
                      </tr>
                    </table>
                </div>
                <div id="advancesearch" class="gentablenoline font-normal margin5TB">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td width="100" align="right" class="auto-style1"><div class="font-title titlebackicon">單位別</div></td>
                        <td class="auto-style1">
                            <asp:dropdownlist id="ddlOrgcd" runat="server" Width="150px" DataSourceID="SDS_Orgcd" DataTextField="orgcd_name" DataValueField="orgcd"  ></asp:dropdownlist>
                            <asp:SqlDataSource ID="SDS_Orgcd" runat="server" ConnectionString="<%$ ConnectionStrings:ConnString %>" SelectCommand="SELECT org_orgcd AS orgcd, org_orgcd + '-' + org_abbr_chnm2 AS orgcd_name FROM common.dbo.orgcod WHERE (org_status = 'A')"  /> </td>
                        </td>
                        <td width="100" align="right" class="auto-style1"><div class="font-title titlebackicon">條件</div></td>
                        <td class="auto-style1">
                            <asp:DropDownList ID="DDL_rule" runat="server">
                                <asp:ListItem Value="P">個人</asp:ListItem>
                                <asp:ListItem Value="B">業務</asp:ListItem>
                                <asp:ListItem Value="M">主管</asp:ListItem>
                            </asp:DropDownList>&nbsp;
                            <asp:DropDownList ID="DDL_case" runat="server">
                                <asp:ListItem Value="00">全部</asp:ListItem>
                                <asp:ListItem Value="N">洽案</asp:ListItem>
                                <asp:ListItem Value="R">標案</asp:ListItem>
                            </asp:DropDownList>&nbsp;
                            <asp:DropDownList ID="DDL_status" runat="server">
                            <asp:ListItem>全部</asp:ListItem>
                            <asp:ListItem>進行中</asp:ListItem>
                            <asp:ListItem>已完成</asp:ListItem>
                            <asp:ListItem>結案</asp:ListItem>
                            <asp:ListItem>中止</asp:ListItem>
                            </asp:DropDownList>
                        </td>
                      </tr>
		                <tr><td align="right"><div class="font-title titlebackicon">案件編號/名稱</div></td> <td><asp:textbox id="TB_contnoName" runat="server" Width="150px"></asp:textbox></td>
                            <td align="right"><div class="font-title titlebackicon">客戶名稱</div></td> <td ><asp:textbox id="TB_CompName" runat="server" Width="150px"></asp:textbox></td>
		                </tr>
                    </table>
                    <div class="twocol margin5TB">
	                    <div class="right">
                           <button id="advancesearchclear" class="genbtn">清除</button>
                           <button id="advancesearchclose" class="genbtn">取消</button>
                           <asp:Button ID="btnQuery1" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery1_Click"/>
                        </div>
                    </div>
                </div>
            </div><!-- fixwidth -->  
            <div class="fixwidth">
                <div class="twocol margin5TB">
	                <div class="left">
                    <span class="font-title font-bold font-size3">案件列表</span>&nbsp;&nbsp;
                    條件：  <asp:DropDownList ID="DDL_rule1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DDL_rule1_SelectedIndexChanged">
                                <asp:ListItem Value="P">個人</asp:ListItem>
                                <asp:ListItem Value="B">業務</asp:ListItem>
                                <asp:ListItem Value="M">主管</asp:ListItem>
                            </asp:DropDownList>&nbsp;
                            <asp:DropDownList ID="DDL_case1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DDL_case1_SelectedIndexChanged">
                                <asp:ListItem Value="00">全部</asp:ListItem>
                                <asp:ListItem Value="N">洽案</asp:ListItem>
                                <asp:ListItem Value="R">標案</asp:ListItem>
                            </asp:DropDownList>&nbsp;
                            <asp:DropDownList ID="DDL_status1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DDL_status1_SelectedIndexChanged">
                            <asp:ListItem>全部</asp:ListItem>
                            <asp:ListItem>進行中</asp:ListItem>
                            <asp:ListItem>已完成</asp:ListItem>
                            <asp:ListItem>結案</asp:ListItem>
                            <asp:ListItem>中止</asp:ListItem>
                            </asp:DropDownList>
	                </div>
                </div>
                <span class="stripeMe font-normal">
                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False"  CellPadding="4"     GridLines="None"   DataSourceID="SDS_search" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated"  >
                       <FooterStyle BackColor="White" />
                       <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                       <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                       <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                       <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
					   <Columns>
					     <asp:BoundField DataField="apms_name" HeaderText="案件/計畫名稱" SortExpression="apms_name" ><ItemStyle Width="300px" /></asp:BoundField>
					     <asp:BoundField DataField="comp_name" HeaderText="客戶" SortExpression="comp_name"  ><ItemStyle Width="200px" /></asp:BoundField>
                           <asp:TemplateField HeaderText="洽案">
                               <ItemTemplate>  
                                   <asp:Literal ID="LT_N_View" runat="server"></asp:Literal>                                                 
                               </ItemTemplate>
                               <ItemStyle Width="80px" HorizontalAlign="Center" />
                           </asp:TemplateField>
                           <asp:TemplateField HeaderText="標案">
                               <ItemTemplate>                                                   
                                   <asp:Literal ID="LT_R_View" runat="server"></asp:Literal>                                                 
                               </ItemTemplate>
                               <ItemStyle Width="80px"  HorizontalAlign="Center" />
                           </asp:TemplateField>
                           <asp:TemplateField HeaderText="議約">
                               <ItemTemplate>                                                   
                                   <asp:Literal ID="LT_T_View" runat="server"></asp:Literal>                                                 
                               </ItemTemplate>
                               <ItemStyle Width="80px"  HorizontalAlign="Center" />
                           </asp:TemplateField>
					     <asp:BoundField DataField="" HeaderText="簽約"><ItemStyle Width="80px" HorizontalAlign="Center" /></asp:BoundField>
					     <asp:BoundField DataField="" HeaderText="計畫執行"><ItemStyle Width="110px" HorizontalAlign="Center"  /></asp:BoundField>
					     <asp:BoundField DataField="" HeaderText="結案"><ItemStyle Width="80px"  HorizontalAlign="Center" /></asp:BoundField>
                           <asp:TemplateField HeaderText="滿意度">
                               <ItemTemplate>                                                   
                                   <asp:HyperLink runat="server" ID="HL_satisfaction_URL"  NavigateUrl='https://contsurvey.itri.org.tw/SurveyNew/ShowSurvey/SurveyFillOutView.aspx?id=b59b3ef1-45c2-4cb5-8a3c-06f97ed3e058&contact=Y'  Target="_blank">滿意度<br>調查</asp:HyperLink>
                               </ItemTemplate>
                               <ItemStyle Width="80px"  HorizontalAlign="Center" />
                           </asp:TemplateField>
                       </Columns>
                       <EmptyDataTemplate><!--當找不到資料時則顯示「無資料」-->
                           <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                       </EmptyDataTemplate>
                       <FooterStyle BackColor="White" />
					   <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                    </cc1:SmartGridView><br />
                    <asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_APMS %>" /> 


                </span>
            </div><!-- 表格DIV -->          
        </div><!-- WrapperMain -->
  </div><!-- WrapperBody -->
  </div>
  <div class="WrapperFooter">
	    <div class="footerblock fixwidth font-size2 font-white lineheight02">
        版權所有©2015 工業技術研究院｜ 建議瀏覽解析度1024x768以上<br />
        業務窗口：｜網站製作：資訊處｜<a href="https://itriap6.itri.org.tw/faq/FQA_A010a.aspx?opn_syscd=AMPS">意見反應</a>｜<a href="help.pptx" download="help.pptx">Help</a>｜網站地圖｜
	    </div><!--{* footerblock *}-->
  </div><!-- WrapperFooter -->   

    <!-- 雙色表單 -->
    <script type="text/javascript">
    $(document).ready(function(){
    $(".stripeMe tr").mouseover(function() {$(this).addClass("over");}).mouseout(function() {$(this).removeClass("over");});
    $(".stripeMe tr:even").addClass("alt");
    $(".stripeMe tr:last-child td").css("border-width","0");
    });
    </script>
    <!-- 按鈕動作 -->
    <script type="text/javascript">
    $(document).ready(function(){
    $(".genbtn").hover(
           function(){ $(this).addClass('genbtnhover') },
           function(){ $(this).removeClass('genbtnhover') }
    )
    $(".keybtn").hover(
           function(){ $(this).addClass('keybtnhover') },
           function(){ $(this).removeClass('keybtnhover') }
    )
    $(".infobtn").hover(
           function(){ $(this).addClass('infobtnhover') },
           function(){ $(this).removeClass('infobtnhover') }
    )
    });
    </script>
    <script type="text/javascript">
    $(document).ready(function(){
	    $('.headernews').scrollbox({
      delay: 8,
    });
    });
    </script> 

    <script type="text/javascript">
    $(document).ready(function(){
	    $(".itemhint").tooltip({
	       track: true, 
	    position: { my: "left+15 center", at: "right center" },
	    //讓tooltips內可以放置HTML CODE
	    content: function () {
                  return $(this).prop('title');
              }
	    });
	    $(".inputhint").tooltip({
	    position: { my: "left+10 bottom+40", at: "left bottom " },
	    tooltipClass: "custom-tooltip-styling",
	    //讓tooltips內可以放置HTML CODE
	    content: function () {
                  return $(this).prop('title');
              }
	    });
	    //說明dialog	
	    $( "#advancesearch" ).dialog({
	      open: function (type, data) { $(this).parent().appendTo("form").css({ "z-index": "101" }); },
	      position:["center", 100],
	      width:800,
	      height:310,
          autoOpen: false,
          show: {
            duration: 300
          },
          hide: {
            duration: 300
          }
        });
	    $("#advancesearchopen").click(function () {
	        if ($("#TB_kw").val() != "") {
	            $("#TB_contnoName").val($("#TB_kw").val());
	            $("#TB_CompName").val($("#TB_kw").val());
	            $("#DDL_rule").val($("#DDL_rule1").val());
	            $("#DDL_degree").val($("#DDL_degree1").val());
	            $("#DDL_case").val($("#DDL_case1").val());
	        }
            $("#advancesearch").dialog("open");
        });
	    $( "#advancesearchclose" ).click(function() {
          $( "#advancesearch" ).dialog( "close");
	    });
	    $("#advancesearchclear").click(function () {
	        $("#ddlOrgcd").val("00");
	        $("#TB_contnoName").val("");
	        $("#TB_CompName").val("");
	        $("#DDL_rule").val("P");
	        $("#DDL_degree").val("P");
	        $("#DDL_case").val("00");
	        return false;
	    });
    });
    </script>

<script type='text/javascript' src='js/autoheight.js'></script>

    </form>
</body>
</html>
