﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Comper 的摘要描述
    /// </summary>
    public class Common
    {
        public Common()
        {
            //
            // TODO: 在此加入建構函式的程式碼
            //
        }


        #region 查詢用借印申請
        /// <summary>
        /// 用借印送簽申請回傳申請事項的資料
        /// </summary>
        /// <param name="main_seqsn">標案流水號</param>
        /// <returns></returns>

        public static DataSet GetSealCode(string main_seqsn)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_seal_code";
            oCmd.Parameters.AddWithValue("@main_seqsn", main_seqsn);

            DataSet ds = dalObject.getDataSet(oCmd, CommandType.StoredProcedure);
            return ds;
        }
        #endregion


        #region 人員相關
        /// <summary>
        /// 依工號取得人員資料
        /// </summary>
        /// <param name="empno">工號</param>
        /// <returns></returns>
        public static DataTable GetEmployee(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT * FROM common..comper
WHERE com_empno = @com_empno
";
            oCmd.Parameters.AddWithValue("@com_empno", empno);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static DataTable GetEmployee_bydept(string orgcd,string dept)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT * FROM common..comper
WHERE com_deptid = @com_deptid
";
            oCmd.Parameters.AddWithValue("@com_deptid", orgcd + dept);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion

        #region 單位相關
        /// <summary>
        /// 取得單位資料
        /// </summary>
        /// <returns></returns>



        public static DataTable GetOrgcd()
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select org_orgcd as code_value,org_orgcd+'-'+org_abbr_chnm2 as code_desc 
from common..orgcod 
where org_status='A' and org_orgcd<>'00'
";
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static DataTable GetOrgcd2(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_orgcd_query";

            oCmd.Parameters.AddWithValue("@sso_empno", empno);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.StoredProcedure );
            return dt;
        }
        /// <summary>
        /// 取得幕僚權限之單位資料 Orgcd
        /// 20200713 IRENE [統計與查詢]-->[歷年標案查詢]-->進階查詢-->鎖定管理單位權限 
        /// </summary>
        /// <returns></returns>
        public static DataTable GetOrgcd_Staff(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
IF EXISTS(select bt1_dept_name from engage_buztbl1 where bt1_empno=@empno and SUBSTRING(bt1_org_name,1,2)<>'00')
    BEGIN 
		--當權限只有一個單位
        SELECT distinct org_orgcd as code_value,org_orgcd+'-'+org_abbr_chnm2 as code_desc 
		FROM common..orgcod 
		LEFT JOIN engage_buztbl1 on SUBSTRING(bt1_dept_name,1,2)=org_orgcd or SUBSTRING(bt1_org_name,1,2)=org_orgcd
		LEFT JOIN engage_buztbl2 on bt2_empno=bt1_empno
		WHERE org_status='A' and org_orgcd<>'00' and bt1_empno=@empno
	END
ELSE
        --當權限<>'00'
	    SELECT org_orgcd as code_value,org_orgcd+'-'+org_abbr_chnm2 as code_desc 
	    FROM common..orgcod 
	    WHERE org_status='A' --and org_orgcd<>'00' 
";
            oCmd.Parameters.AddWithValue("@empno", empno);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 取得部門資料
        /// </summary>
        /// <returns></returns>
        public static DataTable GetGroup(string orgcd)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT dep_deptid as code_value, dep_deptid + '-' + dep_abbrnm as code_desc 
FROM common..depcod 
WHERE SUBSTRING(dep_deptid,1,2) = @orgcd
    AND SUBSTRING(dep_deptid,5,3) ='000'
";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 取得幕僚權限之部門資料
        /// 20200710 IRENE [統計與查詢]-->[歷年標案查詢]-->進階查詢-->鎖定管理組別權限 
        /// </summary>
        /// <returns></returns>
        public static DataTable GetGroup_Staff(string orgcd,string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
IF EXISTS(select bt1_dept_name from engage_buztbl1 where bt1_empno=@empno and bt1_dept_name<>'' )
    BEGIN 
        --當權限是單位的組別時，EX:053K
        SELECT dep_deptid as code_value, dep_deptid + '-' + dep_abbrnm as code_desc 
        FROM common..depcod 
        INNER JOIN engage_buztbl1 on SUBSTRING(bt1_dept_name,1,2)=dep_orgcd
        INNER JOIN engage_buztbl2 on bt2_empno=bt1_empno
        WHERE SUBSTRING(dep_deptid,1,2) = @orgcd
        AND SUBSTRING(dep_deptid,5,3) ='000' and bt1_empno=@empno and bt2_orgcd+'000'=dep_deptid
    END
ELSE    
        --當權限是單位時
        SELECT dep_deptid as code_value, dep_deptid + '-' + dep_abbrnm as code_desc 
        FROM common..depcod 
        WHERE SUBSTRING(dep_deptid,1,2) = @orgcd
        AND SUBSTRING(dep_deptid,5,3) ='000'
";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@empno", empno);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static DataTable GetDept(string keyword, string orgcd)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select top 500 org_orgcd,org_abbr_chnm2,dep_deptid,dep_deptcd,dep_deptname,org_abbr_egnm  
from common..orgcod
inner join common..depcod on org_orgcd=dep_orgcd
where 1 = 1";

            if (keyword != string.Empty)
            {
                oCmd.CommandText += @"
 and (upper(dep_deptid) like '%'+ @keyword +'%' or upper(dep_deptname) like '%'+ @keyword +'%')
";
                oCmd.Parameters.AddWithValue("@keyword", keyword.ToUpper());
            }
            if (orgcd != string.Empty)
            {
                oCmd.CommandText += @"
 and dep_orgcd = @orgcd
";
                oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            }

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static DataTable GetDept2(string orgcd, string group)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT dep_deptcd as code_value, dep_deptcd + dep_abbrnm AS code_desc 
FROM common..depcod 
WHERE SUBSTRING(dep_deptid,1,2) = @orgcd 
    AND SUBSTRING(dep_deptid,3,2) = @group
";
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@group", group);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static string GetDeptId(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select com_deptid from common..comper where com_empno = @empno
";
            oCmd.Parameters.AddWithValue("@empno", empno);

            return dalObject.getTopOne(oCmd, CommandType.Text);
        }
        #endregion

        #region CodeTable相關
        /// <summary>
        /// 依代碼類別取得代碼表資料
        /// </summary>
        /// <param name="type">類別</param>
        /// <returns></returns>
        public static DataTable GetCode(string type)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select code_value,code_desc from gpi_code where code_type=@code_type order by code_order
";
            oCmd.Parameters.AddWithValue("@code_type", type);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 依代碼類別取得代碼表資料
        /// </summary>
        /// <param name="type">類別</param>
        /// <param name="str_notin">過濾掉的項目</param>
        /// <returns></returns>
        public static DataTable GetCode(string type,string str_notin)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = string.Format (@"
select code_value,code_desc from gpi_code 
where code_type=@code_type and code_value not in ({0})
order by code_order
", str_notin);
            oCmd.Parameters.AddWithValue("@code_type", type);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 從contract依代碼類別取得代碼表資料
        /// </summary>
        /// <param name="type">類別</param>
        /// <returns></returns>
        public static DataTable GetContractCode(string type)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
--select rtrim(code_value) as code_value,code_valuedesc as code_desc from contract.dbo.cont_codetbl where code_type=@code_type and code_enabled = 1
--20200708 IRENE 更新新契約系統連線DB ([成本定價]-->技轉成本及訂價估算-->挑選*成果來源)
select rtrim(code_value) as code_value,code_valuedesc as code_desc from contractDB.dbo.c_codetbl where code_type=@code_type and code_enabled = 1
order by code_order
";
            oCmd.Parameters.AddWithValue("@code_type", type);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion

        #region UI相關
        /// <summary>
        /// 取得業務人員
        /// </summary>
        /// <returns></returns>
        public static DataTable GetSales()
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select (select com_cname from common..comper where com_empno=bt_empno) as com_cname,
(select com_telext from common..comper where com_empno=bt_empno) as com_telext,* 
from gpi_buztbl
where bt_orgcd='00'
";
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        /// T1條件項目
        /// </summary>
        /// <param name="empno">工號</param>
        /// <returns></returns>
        public static DataTable GetT1(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"pr_gpi_t1";

            oCmd.Parameters.AddWithValue("@empno", empno);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// T2條件項目
        /// </summary>
        /// <param name="empno">工號</param>
        /// <returns></returns>
        public static DataTable GetT2()
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"pr_gpi_t2";

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        #endregion
        /// <summary>
        /// 取得權限，院部幕僚，單位幕僚 
        /// 20200709 IRENE add
        /// </summary>
        /// <param name="empno">工號</param>
        /// <returns></returns>
        public static DataTable GetRight_Staff(string empno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select *,bt1_dept_name as code_desc
from engage_buztbl1
where bt1_empno=@empno and bt1_priority in ('0','3','6','9') --系統負責人/單位系統管理人、院部/單位幕僚
";
            oCmd.Parameters.AddWithValue("@empno", empno);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        #region 20200831 IRENE ADD 決標方式"最低標"，且屬於"議價"，立案日大於20200911，[投標檢核]只需填寫4、6、10題
        /// <summary>
        /// 決標方式"最低標"，且屬於"議價" ，立案日>20200911
        /// </summary>
        public static DataTable lowest_bargain(int main_seqsn)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
                                select * from gpi_main where main_seqsn=@main_seqsn 
                                and main_award = '1'
                                and main_award_lowest = '2'
                                and main_keyindate >='20200911' 
                                ";
            oCmd.Parameters.AddWithValue("@main_seqsn", main_seqsn);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        #endregion
        #region 20200831 IRENE ADD 決標方式"最低標"，立案日大於20200911，才出現議價/比價
        /// <summary>
        /// 判斷-->立案日大於20200911，決標方式"最低標"，才出現議價/比價
        /// </summary>
        public static DataTable lowest_bargain_judgment(int main_seqsn)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
                               DECLARE  @rtn_code  varchar(1)
                               SET @rtn_code=''
                               IF(convert(char(8), getdate(), 112)>='20200911' and @main_seqsn is null) --還沒有儲存的seqsn=0
	                                BEGIN
                                            SET @rtn_code ='1'                                             
	                                END
                               ELSE IF(@main_seqsn <> '' )
                                    BEGIN
		                                IF EXISTS(select * from gpi_main where main_seqsn=@main_seqsn 
                                        and main_keyindate >='20200911')  --立案日
			                                BEGIN
				                                SET @rtn_code ='1' 
			                                END
		                                IF EXISTS(select * from gpi_main where main_seqsn=@main_seqsn 
                                        and main_keyindate <'20200911') --立案日
			                                BEGIN
				                                SET @rtn_code = '0'
			                                END
                                    END
                                         select @rtn_code 
	                                     RETURN 
                                ";
            oCmd.Parameters.AddWithValue("@main_seqsn", main_seqsn);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        #endregion
        #region 20200904 IRENE ADD 立案日大於20200911，[投標檢核]只需填寫4、6、10題，清掉已有寫的欄位
        /// <summary>
        /// 決標方式"最低標"，且屬於"議價" ，立案日>20200911，[投標檢核]只需填寫4、6、10題，清掉已有寫的欄位
        /// </summary>
        public static DataTable Delete_Not_lowest_bargain_item(int main_seqsn)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
                                UPDATE gpi_chkdata
                                SET chk_no1='',chk_no1_reason='',chk_no1_out='',                --第1題
                                    chk_no2='',chk_no2_reason='',                               --第2題
                                    chk_no31='',chk_no31_reason='',                             --第3題
                                    chk_no5='',chk_no5_reason='',chk_no5_org='',chk_no5_amt='', --第5題
                                    chk_no6='',                                                 --第7題
                                    chk_no7='',chk_no7_org='',                                  --第8題
                                    chk_no53='',chk_src_confirm='',chk_src_modify_memo=''       --第9題
                                WHERE chk_seqsn=@main_seqsn

                                DELETE gpi_sply --第7題的競爭單位(一次刪除多筆)
                                WHERE sply_seqsn=@main_seqsn and sply_quno='6'  
                                
                                DELETE gpi_signcont_sRC  --第9題報院條件打勾的(一次刪除多筆)
                                WHERE ess_seqsn=@main_seqsn and ess_ver=(SELECT chk_ver FROM gpi_chkdata WHERE chk_seqsn=@main_seqsn)
                                
                                SELECT * from gpi_main where 1=1 --無意義 為了塞 Datatable
                                ";
            oCmd.Parameters.AddWithValue("@main_seqsn", main_seqsn);
            DataTable dt_DELETE = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt_DELETE;
        }
        #endregion

        #region 客戶相關
        /// <summary>
        /// 取得招標單位資料
        /// </summary>
        /// <param name="keyword">工號</param>
        /// <returns></returns>
        public static DataTable GetCustomer(string keyword)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select top 500 
comp_idno,          --客戶編號
comp_cname,         --客戶名稱
comp_type,          --客戶屬性
comp_chairman,      --招標聯絡人
comp_phone,         --招標聯絡人電話
comp_chairman,      --公司負責人
comp_incometype     --收入來源
from visitdb..cust 
where comp_delmark='0' 
and comp_befreeze='0'
";
            if (keyword != string.Empty)
            {
                oCmd.CommandText += @"
 and (comp_cname like '%'+ @keyword +'%' or comp_idno like '%'+ @keyword +'%')
";
                oCmd.Parameters.AddWithValue("@keyword", keyword);
            }

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public static DataTable GetCustomerDetail(string idno)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select top 500 
comp_idno,          --客戶編號
comp_cname,         --客戶名稱
comp_type,          --客戶屬性
comp_chairman,      --招標聯絡人
comp_phone,         --招標聯絡人電話
comp_chairman,      --公司負責人
comp_incometype     --收入來源
from visitdb..cust 
where comp_delmark='0' 
and comp_befreeze='0'
and comp_idno = @comp_idno
";
            oCmd.Parameters.AddWithValue("@comp_idno", idno);

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion

        #region 契約相關
        /// <summary>
        /// 取得契約資料
        /// </summary>
        /// <param name="year">年度</param>
        /// <param name="contclass">類別</param>
        /// <param name="orgcd">單位</param>
        /// <param name="keyword">關鍵字</param>
        /// <returns></returns>
        public static DataTable GetContract(string year, string contclass, string orgcd, string keyword)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
select top 500 
actcontno,          --契約編號
techtfr_contname,   --契約名稱
org_abbr_chnm2      --單位
--from contract..v_contract_cp --20200708 IRENE 更新契約系統連接DB & VIEW
from contractDB..V_標案_契約_工服_baseData
where 1=1
";
            //年度
            if (year != string.Empty)
            {
                oCmd.CommandText += @"
 and convert(int,contyear) = @year
";
                int intYear = 0;
                int.TryParse(year, out intYear);
                oCmd.Parameters.AddWithValue("@year", intYear);
            }

            //契約類別
            if (contclass != string.Empty)
            {
                oCmd.CommandText += @"
 and contclass = @contclass
";
                oCmd.Parameters.AddWithValue("@contclass", contclass);
            }

            //單位
            if (orgcd != string.Empty)
            {
                oCmd.CommandText += @"
 and contorgcd = @orgcd
";
                oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            }

            //關鍵字
            if (keyword != string.Empty)
            {
                oCmd.CommandText += @"
 and (techtfr_contname like '%'+ @keyword +'%' or actcontno like '%'+ @keyword +'%')
";
                oCmd.Parameters.AddWithValue("@keyword", keyword);
            }

            DataTable dt = dalObject.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        #endregion

        #region 標案相關
        /// <summary>
        /// 取得標案資料
        /// </summary>
        /// <param name="orgcd">單位</param>
        /// <param name="keyword">關鍵字</param>
        /// <returns></returns>
        public static DataTable GetCase(string orgcd, string keyword)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_case_list";
            
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);
            oCmd.Parameters.AddWithValue("@keyword", keyword);
            DataTable dt = dalObject.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        #endregion

        #region 工程會標
        public static DataSet GetEngineering(string keyword)
        {
            mySQLHelper dalObject = new mySQLHelper();
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_engineering_query";
            oCmd.Parameters.AddWithValue("@keyword", keyword);

            DataSet ds = dalObject.getDataSet(oCmd, CommandType.StoredProcedure);
            return ds;
        }
        #endregion
        #region 取得IP程式
        //202009 IRENE
        public static string GetIP4Address()
        {
            System.Web.HttpContext context = System.Web.HttpContext.Current;
            string sIPAddress = context.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (string.IsNullOrEmpty(sIPAddress))
            {
                string[] ipstr = context.Request.ServerVariables["REMOTE_ADDR"].Split(':');
                if (ipstr[0].Trim() != "")
                    return context.Request.ServerVariables["REMOTE_ADDR"];
                else
                    return "LOCAL-Name：" + Environment.MachineName;
            }
            else
            {
                string[] ipArray = sIPAddress.Split(new Char[] { ',' });
                return ipArray[0];
            }
        }
        #endregion
    }
}