(function(a){a.fn.readMore=function(d){var f={readMoreLinkClass:"read-more__link",readMoreText:"Read more",readLessText:"Read less",readMoreHeight:150};d=a.extend(f,d);var e=a(this);function c(g){if(typeof g.data("options")!=="undefined"){this.collapsedHeight=g.data("options")}else{this.collapsedHeight=d.readMoreHeight}}function b(g){g.each(function(){var h=a(this);var i=new c(h);a(this).after("<span>"+d.readMoreText+"</span>").next().addClass(d.readMoreLinkClass);a(this).css({height:i.collapsedHeight,overflow:"hidden"})})}b(e);a("."+d.readMoreLinkClass).click(function(){var g=a(this).prev();var h=new c(g);if(g.css("overflow")==="hidden"){g.css({height:"auto",overflow:"auto"});g.addClass("expanded")}else{g.css({height:h.collapsedHeight,overflow:"hidden"});g.removeClass("expanded")}if(a(this).text()===d.readMoreText){a(this).text(d.readLessText)}else{a(this).text(d.readMoreText)}})}})(jQuery);