﻿<?xml version="1.0"?>
<configuration>
	<connectionStrings>
		<!--系統DB, 測試區:140.96.1.105, 上線區:ITRIDPS.ITRI.DS-->
		<!--add name="ConnString"	connectionString="Data Source=140.96.1.105,5555;Initial Catalog=engagedb;User ID=pubengage;Password=**********" providerName="System.Data.SqlClient"/-->
 		
		<!--add name="ConnString.技資"  connectionString="Data Source=ITRIDPS.ITRI.DS,2830;Initial Catalog=tech;User ID=pubengage;pwd=**********" providerName="System.Data.SqlClient"/-->
	    <!--add name="pubbs"	    connectionString="Data Source=ITRIDPS.ITRI.DS,2830;Initial Catalog=visitdb;User ID=pubbs;Password=********" providerName="System.Data.SqlClient"/-->
		<!--add name="ConnString.專利"  connectionString="Data Source=ITRIDPS.ITRI.DS,2830;Initial Catalog=patent;User ID=pubengage;pwd=**********" providerName="System.Data.SqlClient"/-->

    <!-- 上線區區存放LOG用 -->
    <add name="ConnLogDB" connectionString="Data Source=ITRIDPV.ITRI.DS,8787;Initial Catalog=engage;User ID=pubengage;Password=**********************" providerName="System.Data.SqlClient" />
	
	<add name="ConnString.ECP" connectionString="Data Source=ITRIDPU.ITRI.DS,1600;Initial Catalog=WebFormPT;User ID=ezflSF200;Password=************************" providerName="System.Data.SqlClient"/>

	<add name="common"	   connectionString="Data Source=ITRIDPS.ITRI.DS,2830;Initial Catalog=engagedb;User ID=pubengage;Password=**********************" providerName="System.Data.SqlClient"/>
        <!--add name="ConnString" connectionString="Data Source=ITRIDPS.ITRI.DS,2830;Initial Catalog=engagedb;User ID=pubengage;Password=Sf@********@**********" providerName="System.Data.SqlClient" /-->

	</connectionStrings>

	<appSettings>
		<!-- 避免 URL 編碼型式資料無效的問題 -->
		<!-- <add key="aspnet:MaxHttpCollectionKeys" value="2500"/>-->

	<add key="https_server" value="true"/>  <!-- ******** 新增 -->

		<!--
		DEBUG:	是否進入系統偵錯模式 (true | false)
		DEBUG.USER.ADMIN	此系統的管理者名單
		DEBUG.USER.ACCOUNT	若為個人開發階段，Used by SSOLoginUser as a test account.
		-->
		<add key="DEBUG" value="false" />
		<add key="DEBUG.USER.ADMIN" value="999999" />
		<add key="DEBUG.USER.ACCOUNT" value="" />
		<!--Used by SSOLoginUser as a test account-->
		<add key="TestEmpNo" value="880583"/>



		<!--頁面標題-->
		<add key="PageTitle" value="洽案管理系統"/>

		<!--==============================
		common設定值
		//繼承使用說明(請於web.config依據下列項目名稱定義參數)：
		//1.配合系統位於測試區或上線區，需設定參數 "test" OR "online"，以識別FAQ環境位置。
		//2.Error Handing功能，須設定系統代碼參數sysCode。
		//3.資料庫連線名稱，須設定參數ConnString；ConnString為SSO的資料庫連線(各系統應該一致)
		//4.安全掃描時，須關閉信件發信功能，須設定參數CloseSendMail。
		//5.錯誤時導向的頁面，須設定參數ErrorPage。
		//6.設定是否提供多國語系功能，須設定參數Language(預設false)。注意：系統需有自己的使用者語言紀錄表(工號、語系)，才可使用為true。
		===============================-->
        <!--FAQ系統環境專用：Token-->
        <add key="sysToken" value="723D6F66-21A2-4DDC-B3F1-27A7435A7C54"/>

		<!--1.FAQ系統環境專用：test or online-->
		<add key="ServerSite" value="online"/>
		<!--2.FAQ系統環境專用:向FAQ系統負責人(淑娟)申請取得的系統代碼，且必須為有效系統代碼才會記錄到FAQ。(若暫不寫入FAQ時，請給空字串。)-->
		<add key="sysCode" value="F2-28"/>
		<!--4.安全掃描時，須關閉信件通知功能(open or close)-->
		<add key="CloseSendMail" value="open"/>
		<!--5.錯誤時導向的頁面-->
		<add key="ErrorPage" value="~/Engage/Shared/Error.aspx"/>
		<!--6.是否有多國語言設定。注意：系統需有自己的使用者語言紀錄表(工號、語系)，才可使用為true。-->
		<add key="Language" value="false"/>


		<!--==============================
		URL.XXXX.XXXX: 相關連結路徑.
		URL.EZ.XXX: EasyFlow 相關連結路徑.
		===============================-->
		<add key="customer_url"		  value="https://itriap7.itri.org.tw/comnwebap/web_page" />
		<add key="URL.FAQ意見反應"	  value="https://itriap6.itri.org.tw/faq/FQA_A010a.aspx?opn_syscd=28"/>
		<!--add key="URL.客戶往來分析"	  value="http://Repsprod/ReportServer/Pages/ReportViewer.aspx?/arCredit/rptCredit2&amp;rs:Command=Render&amp;cusno="/-->
        <add key="URL.客戶往來分析"	  value="https://custinv.itri.org.tw/Reports/Report_Analyze.aspx?_do="/>
		<add key="URL.查詢客戶退換票紀錄" value="https://itriap7.itri.org.tw/cust/mgrcust_custdata.aspx?comp_idno={0}&amp;tab=digi0" />
		<add key="URL.議約"		  value="https://amps.itri.org.tw/Treaty/WebPage/TreatyApply.aspx"/>
		<add key="URL.議約草稿區"	  value="https://amps.itri.org.tw/Treaty/WebPage/TreatyApply_View.aspx"/>
		<add key="URL.查詢議約平台資訊"	  value="https://amps.itri.org.tw/treaty/WebPage/TreatyCase_view.aspx?seno={0}" />
		<add key="URL.風險評估"		  value="https://itriap8.itri.org.tw/rams/WebPage/riskInitAss.aspx?sysId={0}&amp;sysType=1" />
		<add key="URL.契約系統"		  value="https://itriap3.itri.org.tw" />
		<!--add key="URL.電子簽核"	  value="https://itriweb.itri.org.tw/system/statist/siteCount.aspx?id=w00386" /-->
		<add key="URL.電子簽核"		  value="https://flow.itri.org.tw/ecpWeb" />
		<add key="URL.FAQ"		  value="https://itriap6.itri.org.tw/faq/AngageFaq/dvf_list.aspx"/>
		<add key="URL.社群平台討論區"	  value="https://itriap7.itri.org.tw/forum/Discussion/TopicList.aspx?GroupID=Grp_Sharing"/>



		<!-- 洽案系統-測試區：檔案上傳路徑, 開發人員="D:\upload\amps_upd\engage" -->
		<!--add key="FilePathString"	value="\\itri.ds\intratest\webdev5_amps-upload\engage" /-->
        <add key="FilePathString"     value="\\nas-2\Intraweb\up_ap9-amps\engage" /> <!-- 設定議約上傳檔案路徑  -->

		<!-- SMTP Server was modify by Paladin -->
		<add key="SMTP_Server" value="mail.itri.org.tw" />
		<add key="Mail_From" value="<EMAIL>" />

		<!-- Active Report License Key was modify by Paladin -->
		<add key="DataDynamicsARLic" value="itri,itri,DD-APN-30-C000226,F4S74SWF4479MI9KK4SJ" />

		<!-- Component Art Client Script Path -->
		<!--add key="ComponentArt.Web.UI.ClientScriptLocation" value="~/Js/componentart_webui_client" /-->
		<add key="ComponentArt.Web.UI.ClientScriptLocation" value="~/Engage/Js/componentart_webui_client" />

		<!-- Reporting Service Path -->
		<!--add key="ReportServicePath" value="http://Repsprod/ReportServer?/Engage" /-->
		<add key="ReportServicePath" value="https://Repsprod.itri.org.tw/ReportServer?/Engage" />

		<!-- EZ2Paper Reporting Service Path -->
		<add key="EZ2PaperRSPath" value="https://itriap5.itri.org.tw/eztopaper/EFService.asmx" />


		<!-- 電子表單查詢-->
		<add key="EZConnString" value="server=ITRIDPS.ITRI.DS,2830;uid=pubengage;pwd=**********************;database=engagedb;Pooling=true;Min Pool Size=0;Max Pool Size=1;" />


		<!--==========================
		  訊息處理
		===========================-->
		<add key="Msg.ez.送簽成功"    value="送簽創單成功！請按「電子表單」按鈕設定簽核流程！"/>
		<add key="Msg.ez.送簽失敗"    value="送簽失敗!!"/>
		<add key="Msg.規劃成本.審查"  value="執行送審後，本版本資料即不能再編輯，確定送審嗎？"/>
		<add key="Msg.新增版次"       value="是否要新增新的版次?"/>
		<add key="Msg.業務人員"       value="如有問題，請洽產服 趙淑鈴(17666). "/>
		<add key="Msg.存取權限"       value="您無使用權限！"/>
		<add key="Msg.無對應規劃案號" value="無對應規劃案號!"/>
		
		<!--==========================
		  電子表單-創單的 Web Service Path
		===========================-->
		<add key="ECP.EG01"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG01.asmx"/>
		<add key="ECP.EG03"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG03.asmx"/>
		<add key="ECP.EG04"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG04.asmx"/>
        <add key="ECP.EG06"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG06.asmx"/>
		<add key="ECP.EG07"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG07.asmx"/>
		<add key="ECP.EG08"			value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG08.asmx"/>
		<add key="ECP.ITRIPublicWebService"	value="https://flow.itri.org.tw/ECPWeb/WebServiceCust/ITRIPublicWebService.asmx"/>
		<add key="ECP.sysid"			value="3103"/>
		<add key="ECP.secureid"			value="47853D3E-1D8D-49AE-BC0A-4FBF78859136"/>
	</appSettings>

 	<location path="Bulletin/BoardAdd.aspx" />
  <system.web>
    
    <compilation debug="false" targetFramework="4.5"/> 
     <!-- 設定檔案上傳size 100M -->
     <!--httpRuntime maxRequestLength="100000" executionTimeout="18000" requestValidationMode="2.0" /-->

  </system.web>



</configuration> 
