﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class webpage_DialogMultiCustomerSelect2 : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private void Page_Load(object sender, System.EventArgs e)
    {
        // Put user code to initialize the page here
        if (!IsPostBack)
        {
            ViewState["Customers"] = "";
            btnAdd.Attributes.Add("onclick", "return chkNeedField();");
            this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>$('#h_compno').val(parent.$.colorbox.settings.data);reflash_topic('company_renew', 0);</script>");
        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            ViewState["Customers"] = this.h_compno.Value;
            BindData();
        }
    }
    private void BindData()
    {
        //this.SDS_company.SelectParameters.Clear();
        //this.SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_company.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //this.SDS_company.SelectParameters.Add("customers", TypeCode.String, ViewState["Customers"].ToString());
        //this.SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(ViewState["Customers"].ToString()));

            try
            {

                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_company.DataSource = dt;
                SGV_company.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void btnExit_Click(object sender, System.EventArgs e)
    {
        //this.RegisterStartupScript( "exit", string.Format("<script>returnValue='{0}';window.close();</script>",Convert.ToString(ViewState["Customers"])));
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            ViewState["Customers"] = Convert.ToString(ViewState["Customers"]).Trim().Replace("," + e.CommandArgument, "");
            BindData();
        }
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //ImageButton ib = (ImageButton)e.Row.FindControl("btnDelete");
            LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
        }
    }
    protected void SGV_company_PreRender(object sender, EventArgs e)
    {
        //BindData();
    }
    protected void btnAdd_Click(object sender, EventArgs e)
    {
        //判斷要新增的客戶是否重複
        if (Convert.ToString(ViewState["Customers"]).Trim().LastIndexOf(h_compno.Value.Trim()) >= 0)
        {
            this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>alert('輸入的廠商已經存在!');</script>");
        }
        else
        { //未重複
            ViewState["Customers"] = Convert.ToString(ViewState["Customers"]).Trim() + "," + Server.HtmlEncode(h_compno.Value);
            txt_compname.Text = "";
            h_compno.Value = "";
            BindData();
            this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>parent.$.colorbox.settings.data = '" + Server.HtmlEncode(ViewState["Customers"].ToString()) + "';</script>");
        }
    }
    protected void btnExit_Click1(object sender, EventArgs e)
    {
        this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>parent.$.colorbox.settings.data = '" + ViewState["Customers"].ToString() + "'; parent.$.colorbox.close();</script>");
    }
}