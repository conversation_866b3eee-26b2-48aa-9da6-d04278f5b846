﻿
using Aspose.Cells;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web.UI.WebControls;
using Treaty_report;

public partial class Search_inner_statistic : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        if (str == "") return true;
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();

            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_auth.SelectCommand = " select count(m_sno) from treaty_buztbl where  emp_no=@empno and year_report_flag='1' ";
            //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
            //if (dv_auth.Count ==0 )
            //{
            //    Response.Redirect("../NoAuthRight.aspx");
            //}


            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = "select count(m_sno) from treaty_buztbl where  emp_no=@empno and year_report_flag='1' ";
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            oCmd.CommandType = CommandType.Text;
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "myTable");
            if (ds != null && ds.Tables[0].Rows.Count == 0)
            {
                Response.Redirect("../NoAuthRight.aspx");
            }

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;

            BindCaseStyle();
            BindContType();
            BindOrg();
            BindHandelList();
            Bindyear();
            DDL_Year.Items.Insert(0, new ListItem(" ", ""));
        }
        //ClientScript.GetPostBackEventReference(new PostBackOptions(this.lbHtml));
    }

    private void Bindyear()
    {

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select distinct tc_year from treaty_case  order by tc_year  desc";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_Year.DataSource = dt;
                DDL_Year.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    private void BindCaseStyle()
    {
        //SDS_CaseStyle.SelectCommand = "exec esp_treatyCase_codetable   '' ,'16' ";
        //SDS_CaseStyle.DataBind();
        //ddlCaseStyle.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_treatyCase_codetable   '' ,'16' ,'case_class' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlCaseStyle.DataSource = dt;
                ddlCaseStyle.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        ddlCaseStyle.Items.Insert(0, new ListItem(" ", ""));

    }
    private void BindContType()
    {
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_treatyCase_codetable   '' ,'10' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        ddlContType.Items.Insert(0, new ListItem(" ", "00"));
    }
    private void BindOrg()
    {
        //SDS_Orgcd.SelectCommand = "exec esp_Search_inner_statistic_orglist '" + SQLInjectionReplaceAll(ViewState["empno"].ToString()) + "' ";
        //SDS_Orgcd.DataBind();
        //CBL_org.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_Search_inner_statistic_orglist";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                CBL_org.DataSource = dt;
                CBL_org.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindHandelList()
    {
        //SDS_handelList.SelectCommand = "exec esp_Search_inner_statistic_handle_list '" + SQLInjectionReplaceAll(ViewState["empno"].ToString())+ "' ";
        //SDS_handelList.DataBind();
        //CBL_handelList.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_Search_inner_statistic_handle_list ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                CBL_handelList.DataSource = dt;
                CBL_handelList.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_search.DataBind();


    }

    private string GetSelectOrgcd()
    {
        string strResult = string.Empty;
        foreach (ListItem li in CBL_org.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("'{0}',", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetSelectStatus()
    {
        string strResult = string.Empty;
        foreach (ListItem it in CBL_Status.Items)
        {
            if (it.Selected)
                strResult += string.Format("'{0}',", it.Value);
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetSelectHandle()
    {
        string strResult = string.Empty;
        foreach (ListItem li in CBL_handelList.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("{0},", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }
    private string GetCaseClass()
    {
        string strResult = string.Empty;
        foreach (ListItem li in cbxCaseClass.Items)
        {
            if (li.Selected == true)
            {
                strResult += string.Format("{0},", li.Value);
            }
        }
        if (strResult.Length > 0)
            strResult = strResult.Substring(0, strResult.Length - 1);
        return strResult;
    }

    private DataTable SetExportDataTable(DataSet ds)
    {
        List<string> columns = new List<string>() { "orgname", "type_1A", "type_1A2", "type_1B", "type_1B2", "type_1C", "type_1C2", "type_2A", "type_3A", "type_4A", "type_5A", "type_6A", "type_7A", "type_8A", "type_AA", "type_9A", "type_S1", "type_S2" };

        DataTable dt = new DataTable();
        columns.ForEach(x => dt.Columns.Add(x));

        foreach (DataRow row in ds.Tables[0].Rows)
        {
            DataRow dr = dt.NewRow();
            columns.ForEach(x => dr[x] = row[x]);            
            dt.Rows.Add(dr);
        }

        return dt;
    }

    private void DoSearch()
    {
        if (!IsNumber((GetSelectOrgcd().Replace(",", "").Replace("'", "")))) Response.Redirect("../danger.aspx");
        if (!IsNatural_Number(GetSelectHandle().Replace(",", "").Replace("'", ""))) Response.Redirect("../danger.aspx");
        if (!IsNumber(DDL_Year.SelectedValue)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlContType.SelectedValue) || (ddlContType.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlCaseStyle.SelectedValue) || (ddlCaseStyle.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (!IsNumber(ddl_amend.SelectedValue)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxPromoterName.Text) || (ddlCaseStyle.SelectedValue.Length > 10)) Response.Redirect("../danger.aspx");
        if (!IsNumber((ddlImportant.SelectedValue.Replace("-", "")))) Response.Redirect("../danger.aspx");
        if (!IsNumber((GetSelectStatus().Replace(",", "").Replace("'", "")))) Response.Redirect("../danger.aspx");
        if (!IsNatural_Number(GetCaseClass().Replace(",", ""))) Response.Redirect("../danger.aspx");
        if (!IsNumber(ddlShowCase.SelectedValue)) Response.Redirect("../danger.aspx");
        if ((txtReceiveSDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtReceiveEDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveEDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseSDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseEDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtKeyWord.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (txtCompname.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");

        SqlCommand oCmd_M = new SqlCommand();
        oCmd_M.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd_M.CommandText = "esp_Search_inner_statistic";
        oCmd_M.CommandType = CommandType.StoredProcedure;
        oCmd_M.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd_M.Parameters.AddWithValue("QYear", oRCM.SQLInjectionReplaceAll(DDL_Year.SelectedValue)); //將舊案的流水號存入
        oCmd_M.Parameters.AddWithValue("QSendSDate", oRCM.SQLInjectionReplaceAll(txtReceiveSDate.Text.Trim()));//洽案(契約)名稱
        oCmd_M.Parameters.AddWithValue("QSendEDate", oRCM.SQLInjectionReplaceAll(txtReceiveEDate.Text.Trim()));
        oCmd_M.Parameters.AddWithValue("QStatus", oRCM.SQLInjectionReplaceAll(GetSelectStatus()));
        oCmd_M.Parameters.AddWithValue("QOrgcd", oRCM.SQLInjectionReplaceAll(GetSelectOrgcd()));
        oCmd_M.Parameters.AddWithValue("QHandle", oRCM.SQLInjectionReplaceAll(GetSelectHandle()));
        oCmd_M.Parameters.AddWithValue("CompName", oRCM.SQLInjectionReplaceAll(txtCompname.Text));
        oCmd_M.Parameters.AddWithValue("Promoter", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
        oCmd_M.Parameters.AddWithValue("MostImportant", oRCM.SQLInjectionReplaceAll(ddlImportant.SelectedValue));
        oCmd_M.Parameters.AddWithValue("Class", oRCM.SQLInjectionReplaceAll(GetCaseClass()));
        oCmd_M.Parameters.AddWithValue("case_flag", oRCM.SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
        oCmd_M.Parameters.AddWithValue("TopOne", oRCM.SQLInjectionReplaceAll(ddlShowCase.SelectedValue));
        oCmd_M.Parameters.AddWithValue("KeyWords", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
        oCmd_M.Parameters.AddWithValue("is_amend", oRCM.SQLInjectionReplaceAll(ddl_amend.SelectedValue));
        oCmd_M.Parameters.AddWithValue("QCase_style", oRCM.SQLInjectionReplaceAll(ddlCaseStyle.SelectedValue));
        oCmd_M.Parameters.AddWithValue("QCloseSDate", oRCM.SQLInjectionReplaceAll(txtCloseSDate.Text.Trim()));
        oCmd_M.Parameters.AddWithValue("QCloseEDate", oRCM.SQLInjectionReplaceAll(txtCloseEDate.Text.Trim()));
        oCmd_M.Parameters.AddWithValue("QContType", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));
        SqlDataAdapter oda_M = new SqlDataAdapter(oCmd_M);
        DataSet ds_M = new DataSet();
        oda_M.Fill(ds_M, "myTable");
        oCmd_M.Dispose();
        oda_M.Dispose();

        //SDS_search.InsertParameters.Clear();
        //SDS_search.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_search.InsertCommand = "esp_Search_inner_statistic";
        //SDS_search.InsertParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //SDS_search.InsertParameters.Add("QYear", SQLInjectionReplaceAll(DDL_Year.SelectedValue)); //將舊案的流水號存入
        //SDS_search.InsertParameters.Add("QSendSDate", SQLInjectionReplaceAll(txtReceiveSDate.Text.Trim()));//洽案(契約)名稱
        //SDS_search.InsertParameters.Add("QSendEDate", SQLInjectionReplaceAll(txtReceiveEDate.Text.Trim()));
        //SDS_search.InsertParameters.Add("QStatus", SQLInjectionReplaceAll(GetSelectStatus()));
        //SDS_search.InsertParameters.Add("QOrgcd", SQLInjectionReplaceAll(GetSelectOrgcd()));
        //SDS_search.InsertParameters.Add("QHandle", SQLInjectionReplaceAll(GetSelectHandle()));
        //SDS_search.InsertParameters.Add("CompName", SQLInjectionReplaceAll(txtCompname.Text));
        //SDS_search.InsertParameters.Add("Promoter", SQLInjectionReplaceAll(tbxPromoterName.Text));
        //SDS_search.InsertParameters.Add("MostImportant", SQLInjectionReplaceAll(ddlImportant.SelectedValue));
        //SDS_search.InsertParameters.Add("Class", SQLInjectionReplaceAll(GetCaseClass()));
        //SDS_search.InsertParameters.Add("case_flag", SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
        //SDS_search.InsertParameters.Add("TopOne", SQLInjectionReplaceAll(ddlShowCase.SelectedValue));
        //SDS_search.InsertParameters.Add("KeyWords", SQLInjectionReplaceAll(txtKeyWord.Text));
        //SDS_search.InsertParameters.Add("is_amend", SQLInjectionReplaceAll(ddl_amend.SelectedValue));
        //SDS_search.InsertParameters.Add("QCase_style", SQLInjectionReplaceAll(ddlCaseStyle.SelectedValue));
        //SDS_search.InsertParameters.Add("QCloseSDate", SQLInjectionReplaceAll(txtCloseSDate.Text.Trim()));
        //SDS_search.InsertParameters.Add("QCloseEDate", SQLInjectionReplaceAll(txtCloseEDate.Text.Trim()));
        //SDS_search.InsertParameters.Add("QContType", SQLInjectionReplaceAll(ddlContType.SelectedValue));
        //SDS_search.Insert();

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_statistic_show_2023";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        DataView dv = ds.Tables[0].DefaultView;
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
        {
            rpt.DataSource = ds;
            rpt.DataBind();
            //StringBuilder sbHtml = new StringBuilder();
            //sbHtml.Append("<table  class='TbStatistic' >");
            //sbHtml.Append("<tr align='center'><td rowspan='3'>單位</td><td colspan='6'>契約</td><td rowspan='3'>法律問題研究</td><td rowspan='3'>院內規章制度</td><td rowspan='3'>法案</td><td rowspan='3'>申訴案件</td><td rowspan='3'>糾紛</td><td rowspan='3'>訴訟</td><td rowspan='3'>鑑定</td><td rowspan='3'>教育訓練</td><td rowspan='3'>其他</td><td rowspan='3'>件</td><td rowspan='3'>案</td></tr>");
            //sbHtml.Append("<tr align='center'><td colspan='2'>中文</td><td colspan='2'>英文</td><td colspan='2'>其他</td></tr>");
            //sbHtml.Append("<tr align='center'><td>件</td><td>案</td><td>件</td><td>案</td><td>件</td><td>案</td></tr>");
            //foreach (DataRowView rowView in dv)
            //{
            //    DataRow row = rowView.Row;
            //    sbHtml.Append("<tr  align='right'>");
            //    for (int i = 1; i < dv.Table.Columns.Count; i++)
            //    {
            //        sbHtml.Append(string.Format("<td>{0}</td>", Server.HtmlEncode(oRCM.RemoveXss(row[i].ToString()))));
            //    }
            //    sbHtml.Append("</tr>");
            //}
            //sbHtml.Append("</table>");
            //lbHtml.Text = sbHtml.ToString();
            tb_Show.Visible = true;
            LB_Excel.Visible = true;
            LB_Mounth.Visible = true;
            LB_btnConttype.Visible = true;
            LB_People.Visible = true;
        }
        else
        {
            tb_Show.Visible = false;
            LB_Excel.Visible = false;
            LB_Mounth.Visible = false;
            LB_btnConttype.Visible = false;
            LB_People.Visible = false;
        }
        ds.Dispose();
        oCmd.Dispose();
        oda.Dispose();
        /*
                SDS_search.SelectParameters.Clear();
                SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                SDS_search.SelectCommand= "esp_Search_inner_statistic_show";// "esp_Search_inner_statistic";
                SDS_search.SelectParameters.Add("empno", ViewState["empno"].ToString());
                for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                {
                    SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
                }
                SDS_search.DataBind();
                System.Data.DataView dv = (DataView)SDS_search.Select(new DataSourceSelectArguments());
                if (dv  != null)
                {
                    StringBuilder sbHtml = new StringBuilder();
                    sbHtml.Append("<table  class='TbStatistic' >");
                    sbHtml.Append("<tr align='center'><td rowspan='3'>單位</td><td colspan='6'>契約</td><td rowspan='3'>法律問題研究</td><td rowspan='3'>院內規章制度</td><td rowspan='3'>法案</td><td rowspan='3'>智權糾紛</td><td rowspan='3'>其他</td><td rowspan='3'>件</td><td rowspan='3'>案</td></tr>");
                    sbHtml.Append("<tr align='center'><td colspan='2'>中文</td><td colspan='2'>英文</td><td colspan='2'>其他</td></tr>");
                    sbHtml.Append("<tr align='center'><td>件</td><td>案</td><td>件</td><td>案</td><td>件</td><td>案</td></tr>");
                    foreach (DataRowView rowView in dv)
                    {
                        DataRow row = rowView.Row;
                        sbHtml.Append("<tr  align='right'>");
                        for (int i = 1; i < dv.Table.Columns.Count; i++)
                        {
                            sbHtml.Append(string.Format("<td>{0}</td>", row[i]));
                        }
                        sbHtml.Append("</tr>");
                    }
                    sbHtml.Append("</table>");
                    lbHtml.Text = sbHtml.ToString();
                    LB_Excel.Visible = true;
                    LB_Mounth.Visible = true;
                    LB_btnConttype.Visible = true;
                    LB_People.Visible = true;
                }
                else
                {
                     LB_Excel.Visible=false ;
                     LB_Mounth.Visible=false ;
                     LB_btnConttype.Visible=false ;
                     LB_People.Visible = false;
                }

          */
    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        DoSearch();
    }
    protected void SDS_search_Selected(object sender, SqlDataSourceStatusEventArgs e)
    {
        ViewState["RowCount"] = e.AffectedRows;
    }
    protected void LB_Excel_Click(object sender, EventArgs e)
    {
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_statistic_show_2023";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");

        asposeExcel excel = new asposeExcel();
        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;
        objStyle.HorizontalAlignment = TextAlignmentType.Center;
        objStyle.VerticalAlignment = TextAlignmentType.Center;
        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        
        objStyleFlag.All = true;

        Aspose.Cells.Worksheet sheet = excel.getWorksheet();

        string[] title1 = { "單位", "契約", "", "", "", "", "", "法律問題研究", "院內規章制度", "法案", "申訴案件", "糾紛", "訴訟", "鑑定", "教育訓練", "其他", "件", "案" };//自訂表頭1
        string[] title2 = { "", "中文", "", "英文", "", "其他", "" };//自訂表頭2
        string[] title3 = { "", "件", "案", "件", "案", "件", "案" };//自訂表頭2
        for (int i = 0; i < title1.Length; i++)
        {
            sheet.Cells[0, i].PutValue(title1[i]); //自訂表頭1
            sheet.Cells[0, i].SetStyle(objStyle);
        }
        sheet.Cells.Merge(0, 0, 3, 1);
        sheet.Cells.Merge(0, 1, 1, 6);
        sheet.Cells.Merge(0, 7, 3, 1);
        sheet.Cells.Merge(0, 8, 3, 1);
        sheet.Cells.Merge(0, 9, 3, 1);
        sheet.Cells.Merge(0, 10, 3, 1);
        sheet.Cells.Merge(0, 11, 3, 1);
        sheet.Cells.Merge(0, 12, 3, 1);
        sheet.Cells.Merge(0, 13, 3, 1);
        sheet.Cells.Merge(0, 14, 3, 1);
        sheet.Cells.Merge(0, 15, 3, 1);
        sheet.Cells.Merge(0, 16, 3, 1);
        sheet.Cells.Merge(0, 17, 3, 1);
        for (int i = 0; i < title2.Length; i++)
        {
            sheet.Cells[1, i].PutValue(title2[i]);
            sheet.Cells[1, i].SetStyle(objStyle);
        }
        sheet.Cells.Merge(1, 1, 1, 2);
        sheet.Cells.Merge(1, 3, 1, 2);
        sheet.Cells.Merge(1, 5, 1, 2);
        for (int i = 0; i < title3.Length; i++)
        {
            sheet.Cells[2, i].PutValue(title3[i]);
            sheet.Cells[2, i].SetStyle(objStyle);
        }

        DataTable dataSource = SetExportDataTable(ds);

        sheet.Cells.ImportDataTable(dataSource, false, "A4");

        ExportExcel("個人案件.xlsx", excel.exportExcel());

        //Response.Clear();
        //Response.Charset = "big5";
        //Response.AddHeader(
        //    "Content-Disposition",
        //    "attachment;filename=" + Server.UrlEncode("個人案件.xls")
        //    );
        //Response.ContentType = "application/vnd.ms-excel";
        //Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");
        //Response.Write("<html><body>");
        //Response.Write("<meta http-equiv=Content-Type content=text/html; charset=big5>"); //避免亂碼
        //Response.Write("<style type=text/css>");
        //Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
        //Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
        //Response.Write("</style>");
        //Response.Write(lbHtml.Text);
        //Response.Write("</body></html>");
        //Response.End();
    }
    protected void LB_Mounth_Click(object sender, EventArgs e)
    {
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = "esp_Search_inner_statistic_month";// "esp_Search_inner_statistic";
        //SDS_NR.SelectParameters.Add("empno", ViewState["empno"].ToString());
        //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
        //{
        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
        //if (dv != null)
        //{

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_statistic_month";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd.CommandTimeout = 300;
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        DataView dv = ds.Tables[0].DefaultView;
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
        {
            asposeExcel excel = new asposeExcel();
            Aspose.Cells.Worksheet sheet = excel.getWorksheet();

            DataTable dataSource = ds.Tables[0];

            sheet.Cells.ImportDataTable(dataSource, false, "A1");

            Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
            Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
            objStyle.Font.IsBold = true;

            //設定表頭用
            Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
            objStyleFlag.All = true;

            ExportExcel("各單位/月份分佈.xlsx", excel.exportExcel());

            //Response.Clear();
            //Response.Charset = "big5";
            //Response.AddHeader(
            //    "Content-Disposition",
            //    "attachment;filename=" + Server.UrlEncode("各單位/月份分佈.xls")
            //    );
            //Response.ContentType = "application/vnd.ms-excel";
            //Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");
            //Response.Write("<html><body>");
            //Response.Write("<meta http-equiv=Content-Type content=text/html; charset=big5>"); //避免亂碼
            //Response.Write("<style type=text/css>");
            //Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
            //Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
            //Response.Write("</style>");
            //#region 產生匯出的表格
            //StringBuilder sbHtml = new StringBuilder();
            //sbHtml.Append("<table border=1>");
            //foreach (DataRowView myDataRowView in dv)
            //{
            //    sbHtml.Append("<tr>");
            //    for (int count = 0; count < dv.Table.Columns.Count; count++)
            //    {
            //        sbHtml.Append(string.Format("<td>{0}</td>", myDataRowView[count]));
            //    }
            //    sbHtml.Append("</tr>");
            //}
            //sbHtml.Append("</table>");
            //#endregion
            //Response.Write(sbHtml.ToString());
            //Response.Write("</body></html>");
            //Response.End();
        }
        oCmd.Dispose();
        oda.Dispose();
        //dv.Dispose();
    }
    protected void LB_btnConttype_Click(object sender, EventArgs e)
    {
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = "esp_Search_inner_statistic_conttype";// "esp_Search_inner_statistic";
        //SDS_NR.SelectParameters.Add("empno", ViewState["empno"].ToString());
        //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
        //{
        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
        //if (dv != null)
        //{

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_statistic_conttype";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");

        asposeExcel excel = new asposeExcel();
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();

        DataTable dataSource = ds.Tables[0];

        sheet.Cells.ImportDataTable(dataSource, false, "A1");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        ExportExcel("契約性質.xlsx", excel.exportExcel());
        //DataView dv = ds.Tables[0].DefaultView;
        //if (ds != null && ds.Tables[0].Rows.Count >= 1)
        //{
        //    Response.Clear();
        //    Response.Charset = "big5";
        //    Response.AddHeader(
        //        "Content-Disposition",
        //        "attachment;filename=" + Server.UrlEncode("契約性質.xls")
        //        );
        //    Response.ContentType = "application/vnd.ms-excel";
        //    Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");
        //    Response.Write("<html><body>");
        //    Response.Write("<meta http-equiv=Content-Type content=text/html; charset=big5>"); //避免亂碼
        //    Response.Write("<style type=text/css>");
        //    Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
        //    Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
        //    Response.Write("</style>");
        //    #region 產生匯出的表格
        //    StringBuilder sbHtml = new StringBuilder();
        //    sbHtml.Append("<table border=1>");
        //    foreach (DataRowView myDataRowView in dv)
        //    {
        //        sbHtml.Append("<tr>");
        //        for (int count = 0; count < dv.Table.Columns.Count; count++)
        //        {
        //            sbHtml.Append(string.Format("<td>{0}</td>", myDataRowView[count]));
        //        }
        //        sbHtml.Append("</tr>");
        //    }
        //    sbHtml.Append("</table>");
        //    #endregion
        //    Response.Write(sbHtml.ToString());
        //    Response.Write("</body></html>");
        //    Response.End();
        //}
        oCmd.Dispose();
        oda.Dispose();
        //dv.Dispose();
    }
    protected void LB_People_Click(object sender, EventArgs e)
    {
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = "esp_Search_inner_statistic_people";// "esp_Search_inner_statistic";
        //SDS_NR.SelectParameters.Add("empno", ViewState["empno"].ToString());
        //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
        //{
        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
        //if (dv != null)
        //{

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_Search_inner_statistic_people_2023";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");

        asposeExcel excel = new asposeExcel();
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();

        DataTable dataSource = ds.Tables[0];

        sheet.Cells.ImportDataTable(dataSource, false, "A1");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        ExportExcel("案件類型.xlsx", excel.exportExcel());
        //DataView dv = ds.Tables[0].DefaultView;
        //if (ds != null && ds.Tables[0].Rows.Count >= 1)
        //{
        //    Response.Clear();
        //    Response.Charset = "big5";
        //    Response.AddHeader(
        //        "Content-Disposition",
        //        "attachment;filename=" + Server.UrlEncode("案件類型.xls")
        //        );
        //    Response.ContentType = "application/vnd.ms-excel";
        //    Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");
        //    Response.Write("<html><body>");
        //    Response.Write("<meta http-equiv=Content-Type content=text/html; charset=big5>"); //避免亂碼
        //    Response.Write("<style type=text/css>");
        //    Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
        //    Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
        //    Response.Write("</style>");
        //    #region 產生匯出的表格
        //    StringBuilder sbHtml = new StringBuilder();
        //    sbHtml.Append("<table border=1>");
        //    foreach (DataRowView myDataRowView in dv)
        //    {
        //        sbHtml.Append("<tr>");
        //        for (int count = 0; count < dv.Table.Columns.Count; count++)
        //        {
        //            sbHtml.Append(string.Format("<td>{0}</td>", myDataRowView[count]));
        //        }
        //        sbHtml.Append("</tr>");
        //    }
        //    sbHtml.Append("</table>");
        //    #endregion
        //    Response.Write(sbHtml.ToString());
        //    Response.Write("</body></html>");
        //    Response.End();
        //}
        oCmd.Dispose();
        oda.Dispose();
        //dv.Dispose();
    }

    private void ExportExcel(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);

        Response.Clear();
        Response.ContentType = "Application/vnd.ms-excel";
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }
}