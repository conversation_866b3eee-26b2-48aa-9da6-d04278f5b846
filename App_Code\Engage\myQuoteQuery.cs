﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myQuoteQuery
	/// </summary>
	public class myQuoteQuery : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private string _empno;
		private string _empname;

		//string _empno = string.Empty;		// 員工工號
		string _eb_compname = string.Empty;	// 客戶名稱
		string _keyword = string.Empty;		// 關鍵字
		string _deplevel = string.Empty;	// 執行部門層級
		string _deplist = string.Empty;		// 執行部門清單
		string _eb_planer = string.Empty;		// 規劃人
		string _eq_date1 = string.Empty;	// 報價單日期1
		string _eq_date2 = string.Empty;	// 報價單日期2
		string _quotamt1 = string.Empty;	// 總金額1
		string _quotamt2 = string.Empty;	// 總金額2
		string _eq_mkemp = string.Empty;	// 報價單製表人
		string _eq_lastmaildate = string.Empty;		// 最近一次傳送日期
		string _eq_lastdownloaddate = string.Empty;	// 最近一次下載日期
		string _eq_valid = string.Empty;	// 版別 (有效單: 1, 無效單: 0)
		string _eb_promo = string.Empty;	// 推廣人
		string _eb_execstatus = string.Empty;		// 進度
		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}


		public string Eb_compname
		{
			get { return _eb_compname; }
			set { _eb_compname = value; }
		}

		public string Keyword
		{
			get { return _keyword; }
			set { _keyword = value; }
		}

		public string Deplevel
		{
			get { return _deplevel; }
			set { _deplevel = value; }
		}

		public string Deplist
		{
			get { return _deplist; }
			set { _deplist = value; }
		}

		public string Eb_planer
		{
			get { return _eb_planer; }
			set { _eb_planer = value; }
		}

		public string Eq_date1
		{
			get { return _eq_date1; }
			set { _eq_date1 = value; }
		}

		public string Eq_date2
		{
			get { return _eq_date2; }
			set { _eq_date2 = value; }
		}

		public string Quotamt1
		{
			get { return _quotamt1; }
			set { _quotamt1 = value; }
		}

		public string Quotamt2
		{
			get { return _quotamt2; }
			set { _quotamt2 = value; }
		}

		public string Eq_mkemp
		{
			get { return _eq_mkemp; }
			set { _eq_mkemp = value; }
		}

		public string Eq_lastmaildate
		{
			get { return _eq_lastmaildate; }
			set { _eq_lastmaildate = value; }
		}

		public string Eq_lastdownloaddate
		{
			get { return _eq_lastdownloaddate; }
			set { _eq_lastdownloaddate = value; }
		}

		public string Eq_valid
		{
			get { return _eq_valid; }
			set { _eq_valid = value; }
		}

		public string Eb_promo
		{
			get { return _eb_promo; }
			set { _eb_promo = value; }
		}

		public string Eb_execstatus
		{
			get { return _eb_execstatus; }
			set { _eb_execstatus = value; }
		}

		#endregion


		public myQuoteQuery()
		{
			
		}

		#region 取得「報價資訊查詢」的列表
		/// <summary>
		/// 取得「報價資訊查詢」的列表
		/// </summary>
		/// <returns></returns>
		public DataSet GetEngageQuoteQuery(string ip)
		{
            SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_quot_query";

			SqlParameter[] parms = {
									    new SqlParameter("@empno",				this._empno),
										new SqlParameter("@deplevel",			this.Deplevel),
										new SqlParameter("@deplist",			this.Deplist),
										new SqlParameter("@keyword",			this.Keyword),
										new SqlParameter("@compname",			this.Eb_compname),
										new SqlParameter("@date1",				this.Eq_date1),
										new SqlParameter("@date2",				this.Eq_date2),
										new SqlParameter("@execstatus",			this.Eb_execstatus),
										new SqlParameter("@quotamt1",			this.Quotamt1),
										new SqlParameter("@quotamt2",			this.Quotamt2),
										new SqlParameter("@lastmaildate",		this.Eq_lastmaildate),
										new SqlParameter("@lastdownloaddate",	this.Eq_lastdownloaddate),
										new SqlParameter("@valid",				this.Eq_valid),
										new SqlParameter("@planer",				this.Eb_planer),
										new SqlParameter("@promo",				this.Eb_promo),
										new SqlParameter("@mkemp",				this.Eq_mkemp),
                                        new SqlParameter("@IP",              ip)
                                   };

			oCmd.Parameters.AddRange(parms);
			return this.getDataSet(oCmd, CommandType.StoredProcedure);
		}
		#endregion

	

	}
}