﻿using Aspose.Pdf;
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_Inspect_his : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }

            BindData();
        }
    }

    private void BindData()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "inspect_his");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_Inspect.DataSource = dt;
                gv_Inspect.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    protected void gv_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("lbl_Istatus");
        if (LB != null)
            switch (LB.Text.Trim())
            {
                case "0":
                    LB.Text = "";
                    break;
                case "1":
                    LB.Text = "同意";
                    break;
                case "2":
                    LB.Text = "退回";
                    break;
            }
        LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");
        if (lb_del != null)
        {
            if (ViewState["Role"].ToString().IndexOf("ADM") >= 1)
            {
                if ((ViewState["tt_degree"].ToString().Trim() == "Z") || (ViewState["tt_degree"].ToString().Trim() == "C"))
                    lb_del.Visible = false;
                else
                    lb_del.Visible = true;
            }
            if (ViewState["Role"].ToString() == "HGroup_ADM")
                lb_del.Visible = false;
        }

        GridView GV_File = (GridView)e.Row.FindControl("GV_File");
        if (GV_File != null)
        {
            string tt_seno = DataBinder.Eval(e.Row.DataItem, "tt_seno").ToString(); ;
            string tti_no = DataBinder.Eval(e.Row.DataItem, "tti_no").ToString(); ;
            GV_File.DataSource = Get_Inspect_His_File(tt_seno, tti_no);
            GV_File.DataBind();
        }
    }

    protected void GV_File_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tcdf_doc"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }

    private DataTable File_View(string tcdf_no)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");
            sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    private DataTable Get_Inspect_His_File(string tt_seno, string tti_no)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "inspect_his_file");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(tt_seno));
            sqlCmd.Parameters.AddWithValue("@tti_no", oRCM.SQLInjectionReplaceAll(tti_no));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }
}