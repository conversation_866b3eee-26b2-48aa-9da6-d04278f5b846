﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="tip_view.aspx.cs" Inherits="Treaty_manager_tip_view" ValidateRequest="false" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link type="text/css" rel="stylesheet" href="../css/myITRIproject/jquery-ui.css" />
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
    <link href="../css/colorbox.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../scripts/jquery-3.2.1.js"></script>
    <script src="../scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
    </script>
</head>
<body style="background: none;">

    <form id="form1" runat="server">
        <span class="stripeMe font-normal">
            <asp:Literal ID="TB_content" runat="server" Text=''></asp:Literal>
            <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" Width="98%">
                <Columns>
                    <asp:TemplateField HeaderText="附件名稱">
                        <ItemTemplate>
                            <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl='<%# "/Treaty/manager/tip_view.aspx?tdf_id="+ Server.HtmlEncode(Eval("tdf_id").ToString()) %>'><%# Server.HtmlEncode(Eval("tdf_filename").ToString()) %></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle Width="550px"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Left" />
                    </asp:TemplateField>
                </Columns>
                <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                <PagerSettings Position="Bottom" />
                <PagerStyle HorizontalAlign="Left" />
            </asp:GridView>
        </span>

    </form>
</body>
</html>
