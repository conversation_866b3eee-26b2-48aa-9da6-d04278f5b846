﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Configuration;
using System.Text.RegularExpressions;
public partial class chooseEmployee : System.Web.UI.Page
{
    //程式作者 : 陳瓊如
    //撰寫日期 : 2014/06/04
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            ViewState["tbxid"] = Server.UrlDecode((Request["tbxid"] != null) ? Request["tbxid"].Trim() : "");//傳入輸入欄位的ID(姓名)
            ViewState["hfid"] = Server.UrlDecode((Request["hfid"] != null) ? Request["hfid"].Trim() : "");//傳入隱藏欄位的ID(工號)
            ViewState["hfValue"] = Server.UrlDecode((Request["hfValue"] != null) ? Request["hfValue"].Trim() : "");//傳入隱藏欄位HiddenField的值
            string empno = GetAttribute(this, "SM_USER");
            if (string.IsNullOrEmpty(empno))
            {
                empno = GetNTLogin(this);
            }
            ViewState["loginEmpno"] = empno; //登入者工號

            SetSelectMember();//呼叫從傳入的姓名與工號清單設定挑選人員清單

            ViewState["SelectType"] = "dept"; //挑選成員方式 dept:部門 / name:姓名
            lbtn_bydept.Enabled = false;
            lbtn_bydept.ForeColor = Color.Black; //黑色
            lbtn_bydept.Attributes.Add("onmouseover", "this.style.color = '#000000'"); //黑色
            lbtn_bydept.Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色

            table1.Style["Display"] = "block";
            gvList.Visible = false;

            //取得登入者的單位代碼與部門代碼
            string orgcd = "";
            string deptcd = "";

            SqlCommand sqlCmd = new SqlCommand(@"select com_orgcd, com_deptcd from common..comper where com_empno=@empno");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["loginEmpno"].ToString().Trim()));

            DataView dv = ExecSqlCmd(sqlCmd);
            if (dv.Count > 0)
            {
                orgcd = dv.Table.Rows[0]["com_orgcd"].ToString();
                deptcd = dv.Table.Rows[0]["com_deptcd"].ToString();
            }
            dv.Dispose();

            ViewState["orgcd"] = orgcd;
            ViewState["deptcd"] = deptcd;

            BindOrgListBox();
        }
    }
    //抓取Single Sign ON帳號
    public string GetAttribute(System.Web.UI.Page sender, string AttrName)
    {
        string AllHttpAttrs, FullAttrName, Result;
        int AttrLocation;
        AllHttpAttrs = Request.ServerVariables["ALL_HTTP"];
        FullAttrName = "HTTP_" + AttrName.ToUpper();
        AttrLocation = AllHttpAttrs.IndexOf(FullAttrName + ":");
        if (AttrLocation > 0)
        {
            Result = AllHttpAttrs.Substring(AttrLocation + FullAttrName.Length + 1);
            AttrLocation = Result.IndexOf("\n");
            if (AttrLocation <= 0) AttrLocation = Result.Length + 1;
            return sender.Request.ServerVariables["HTTP_SM_USER"];
        }
        return "";
    }
    //抓取NT登入帳號
    private static string GetNTLogin(System.Web.UI.Page sender)
    {
        string empno = sender.User.Identity.Name.Substring(sender.User.Identity.Name.IndexOf("\\", 0) + 1);
        return empno;
    }
    //從傳入的姓名與工號清單設定挑選人員清單
    private void SetSelectMember()
    {
        DataTable dtEmp; //宣告一個DataTable物件全域變數,用來存放已挑選人員清單, 共有(工號、姓名、工號;姓名)共3個欄位

        //建立暫存DataTable, 用來存放已挑選人員清單
        dtEmp = new DataTable();
        dtEmp.Columns.Add(new DataColumn("empno", typeof(string))); //工號
        dtEmp.Columns.Add(new DataColumn("cname", typeof(string))); //姓名
        dtEmp.Columns.Add(new DataColumn("empno_name", typeof(string))); //工號;姓名
        try
        {
            string empno_string = ""; //工號字串
            string empno = ""; //工號
            string cname = ""; //姓名
            if (ViewState["hfValue"].ToString().TrimEnd() != "")
            {
                empno_string = ViewState["hfValue"].ToString();

                String[] empnolist = empno_string.Split(';');
                int rows = empnolist.GetLength(0);
                for (int i = 0; i < rows - 1; i++)
                {
                    empno = empnolist[i];
                    cname = GetEmpName(empno);

                    DataRow dr = dtEmp.NewRow();
                    dr["empno"] = empno;
                    dr["cname"] = cname.TrimEnd();
                    dr["empno_name"] = empno + ";" + cname.TrimEnd();
                    dtEmp.Rows.Add(dr);
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 從傳入的姓名與工號清單設定挑選人員清單時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
        Session["dataTable"] = dtEmp; //用Session["dataTable"]來暫存已挑選人員清單
        SetSelectedMemberRepeater();//呼叫設定已挑選成員Repeater 
    }
    //用工號查姓名
    private string GetEmpName(string empno)
    {
        string cname = "";
        SqlCommand sqlCmd = new SqlCommand(@"select rtrim(com_cname) as com_cname from common..comper where com_empno=@empno");
        sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(empno));

        DataView dvEmp = ExecSqlCmd(sqlCmd);
        if (dvEmp.Count > 0)
        {
            cname = dvEmp.Table.Rows[0]["com_cname"].ToString();
        }
        dvEmp.Dispose();
        return cname;
    }
    //設定單位ListBox清單
    private void BindOrgListBox()
    {
        string strSQL = ""; //SQL字串
        strSQL = @"select org_orgcd, org_abbr_chnm2, org_abbr_egnm from common..orgcod where org_status='A' and org_orgcd !='00' order by org_orgcd asc";
        try
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.CommandText = strSQL;
            DataView dvOrgList = ExecSqlCmd(sqlCmd);
            if (dvOrgList.Count > 0)
            {
                ListItem lstOrg = new ListItem();
                for (int i = 0; i < dvOrgList.Table.Rows.Count; i++)
                {
                    lstOrg = new ListItem(dvOrgList.Table.Rows[i]["org_abbr_chnm2"].ToString().Trim() + "(" + dvOrgList.Table.Rows[i]["org_abbr_egnm"].ToString().Trim() + ")", dvOrgList.Table.Rows[i]["org_orgcd"].ToString().Trim());
                    listbox_org.Items.Add(lstOrg);
                }
            }
            dvOrgList.Dispose();
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 設定單位ListBox清單發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

        //依據登入者所屬單位預設單位選項
        if (listbox_org.Items.FindByValue(ViewState["orgcd"].ToString()) != null)
        {
            listbox_org.Items.FindByValue(ViewState["orgcd"].ToString()).Selected = true;
        }

        BindDeptListBox(ViewState["orgcd"].ToString(), ViewState["deptcd"].ToString()); //呼叫設定部門ListBox清單
    }

    //設定部門ListBox清單
    private void BindDeptListBox(string orgcd, string deptcd)
    {
        //依據登入者所屬單位設定部門ListBox
        try
        {
            SqlCommand sqlCmd = new SqlCommand(@"select dep_deptcd, dep_deptname from common..depcod where dep_orgcd=@orgcd");
            sqlCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(orgcd));
            DataView dvDept = ExecSqlCmd(sqlCmd);

            if (dvDept.Count > 0)
            {
                ListItem lstOrg = new ListItem();
                for (int i = 0; i < dvDept.Table.Rows.Count; i++)
                {
                    lstOrg = new ListItem(dvDept.Table.Rows[i]["dep_deptcd"].ToString().Trim() + dvDept.Table.Rows[i]["dep_deptname"].ToString().Trim(), dvDept.Table.Rows[i]["dep_deptcd"].ToString().Trim());
                    listbox_dept.Items.Add(lstOrg);
                }
            }
            dvDept.Dispose();
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 設定部門ListBox清單發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

        //依據登入者所屬部門預設部門選項
        if (deptcd != "")
        {
            if (listbox_dept.Items.FindByValue(deptcd) != null)
            {
                listbox_dept.Items.FindByValue(deptcd).Selected = true;
            }
        }

        BindNameRepeater(orgcd, deptcd); //呼叫設定姓名Repeater清單
    }
    //設定姓名Repeater清單
    private void BindNameRepeater(string orgcd, string deptcd)
    {
        //依據登入者所屬單位部門設定姓名ListBox
        try
        {
            SqlCommand sqlCmd = new SqlCommand(@"select com_empno, rtrim(com_cname) as com_cname, com_empno+';'+rtrim(com_cname) as empno_name from common..comper where com_orgcd=@orgcd and com_deptcd=@deptcd and com_depcd='N'");
            sqlCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(orgcd));
            sqlCmd.Parameters.AddWithValue("@deptcd", oRCM.SQLInjectionReplaceAll(deptcd));
            DataView dvName = ExecSqlCmd(sqlCmd);
            if (dvName.Count >= 0)
            {
                rpDeptMember.DataSource = dvName;
                rpDeptMember.DataBind();
            }
            dvName.Dispose();
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 設定姓名Repeater清單發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

        //呼叫比對姓名清單與已挑選成員清單
        CompareRepeaterNameList();
    }
    //比對姓名清單與已挑選成員清單 , 如姓名清單中的人員已經在已挑選成員清單中, 將加入小圖與姓名連結click Disable, 並將空白小圖Disable
    private void CompareRepeaterNameList()
    {
        try
        {
            string com_empno = ""; //部門成員工號
            string empno = ""; //已挑選成員工號
            foreach (RepeaterItem rowSM in rpSelectedMember.Items)
            {
                empno = (rowSM.FindControl("hf_empno") as HiddenField).Value;
                foreach (RepeaterItem rowDM in rpDeptMember.Items)
                {
                    com_empno = (rowDM.FindControl("hf_com_empno") as HiddenField).Value;
                    if (empno == com_empno)
                    {
                        (rowDM.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_checked.png"; //更換小圖為空白小圖
                        //(rowDM.FindControl("imgbtn_add1") as ImageButton).Enabled = false; //小圖不提供click
                        //(rowDM.FindControl("linbtn_add1") as LinkButton).Enabled = false; //姓名連結不提供click
                        (rowDM.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.Black; //姓名連結改成黑色字
                        (rowDM.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                        (rowDM.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 比對姓名清單與已挑選成員清單時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
    }
    //單位改變
    protected void listbox_org_SelectedIndexChanged(object sender, EventArgs e)
    {
        listbox_dept.Items.Clear(); //清空部門ListBox
        string orgcd = listbox_org.SelectedValue;
        BindDeptListBox(orgcd, ""); //呼叫設定部門ListBox清單
    }
    //部門改變
    protected void listbox_dept_SelectedIndexChanged(object sender, EventArgs e)
    {
        string orgcd = listbox_org.SelectedValue; //單位代碼
        string deptcd = listbox_dept.SelectedValue; //部門代碼
        BindNameRepeater(orgcd, deptcd); //呼叫設定姓名Repeater清單
    }
    //設定員工GridView清單
    private void dataBind(bool reload)
    {
        DataView dvList;
        bool isParameters = false;
        if (reload)
        {
            string strSQL = "";
            if (lbl_flag.Text == "0") //第一次進來以姓名查詢的畫面
            {
                if (ViewState["loginEmpno"].ToString().Trim() != "")
                {
                    strSQL = "select top 200 com_empno, rtrim(com_cname) as com_cname, dep_orgcd+'-'+dep_deptcd as deptid, dep_deptname, com_empno+';'+rtrim(com_cname) as empno_name from common..comper join common..depcod on com_orgcd=dep_orgcd and com_deptcd=dep_deptcd where com_depcd='N' and com_deptid in (select com_deptid from common..comper where com_empno=@empno)";
                    isParameters = true;
                }
                else
                {
                    strSQL = "select top 200 com_empno, rtrim(com_cname) as com_cname, dep_orgcd+'-'+dep_deptcd as deptid, dep_deptname, com_empno+';'+rtrim(com_cname) as empno_name from common..comper join common..depcod on com_orgcd=dep_orgcd and com_deptcd=dep_deptcd where com_depcd='N' ";
                }

            }
            else
            {
                strSQL = "select top 200 com_empno, rtrim(com_cname) as com_cname, dep_orgcd+'-'+dep_deptcd as deptid, dep_deptname, com_empno+';'+rtrim(com_cname) as empno_name from common..comper join common..depcod on com_orgcd=dep_orgcd and com_deptcd=dep_deptcd where com_depcd='N' ";
            }

            try
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.CommandText = strSQL;

                if (isParameters)
                {
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["loginEmpno"].ToString().Trim()));
                }

                dvList = ExecSqlCmd(sqlCmd);
                if (dvList.Count > 0)
                {
                    ViewState["totalCount"] = dvList.Count;
                    gvList.DataSource = dvList;
                    gvList.DataBind();
                }
                Session["dataView"] = dvList;
            }
            catch (System.Exception ex)
            {
                lbl_Msg.Text = "系統訊息: 設定人員GridView清單發生錯誤, 錯誤原因:" + ex.Message.ToString();
                lbl_Msg.ForeColor = Color.Red;
            }
        }
        else
        {
            dvList = Session["dataView"] as DataView;
            gvList.DataSource = dvList;
            gvList.DataBind();
        }

        lbl_flag.Text = "1";
    }
    //點選姓名Repeater rpDeptMember的加入小圖及姓名時, 自動加入已挑選成員清單並將加入小圖換成空白小圖
    protected void rpDeptMember_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "imgbtn_add1_cmd")
        {
            if (CheckExistDataTable(e.CommandArgument.ToString()) == "Y")
            {
                RemovedFromPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable          
                (e.Item.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_unchecked.png";  //更換小圖為空白小圖
                                                                                                                                       //(e.Item.FindControl("imgbtn_add1") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                       //(e.Item.FindControl("linbtn_add1") as LinkButton).Enabled = false; //姓名連結不提供click
                (e.Item.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.FromArgb(97, 146, 09); //姓名復原成綠色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
            }
            else
            {
                AddToPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable          
                (e.Item.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_checked.png"; //更換小圖為空白小圖
                                                                                                                                    //(e.Item.FindControl("imgbtn_add1") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                    //(e.Item.FindControl("linbtn_add1") as LinkButton).Enabled = false; //姓名連結不提供click
                (e.Item.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.Black; //姓名連結改成黑色字
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
            }

        }
        if (e.CommandName == "linbtn_add1_cmd")
        {
            if (CheckExistDataTable(e.CommandArgument.ToString()) == "Y")
            {
                RemovedFromPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable          
                (e.Item.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_unchecked.png";  //更換小圖為空白小圖
                                                                                                                                       //(e.Item.FindControl("imgbtn_add1") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                       //(e.Item.FindControl("linbtn_add1") as LinkButton).Enabled = false; //姓名連結不提供click
                (e.Item.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.FromArgb(97, 146, 09); //姓名復原成綠色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
            }
            else
            {
                AddToPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable 
                (e.Item.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_checked.png"; //更換小圖為空白小圖
                                                                                                                                    //(e.Item.FindControl("imgbtn_add1") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                    //(e.Item.FindControl("linbtn_add1") as LinkButton).Enabled = false; //姓名連結不提供click
                (e.Item.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.Black; //姓名連結改成黑色字
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                (e.Item.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
            }
        }

    }
    //點選姓名Repeater rpSelectedMember的移除小圖及姓名時, 自動移除已挑選成員清單
    protected void rpSelectedMember_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "imgbtn_remove_cmd")
        {
            RemovedFromPickList(e.CommandArgument.ToString()); //呼叫移除已挑選人員清單暫存DataTable  
        }

        if (e.CommandName == "linbtn_remove_cmd")
        {
            RemovedFromPickList(e.CommandArgument.ToString()); //呼叫移除已挑選人員清單暫存DataTable  
        }

        //if (e.CommandName == "linbtn_remove2_cmd")
        //{
        //    RemovedFromPickList(e.CommandArgument.ToString()); //呼叫移除已挑選人員清單暫存DataTable  
        //}
    }
    private string CheckExistDataTable(string empno_name)
    {
        //分解(工號;姓名)字串成(工號)(姓名)
        int location = empno_name.IndexOf(";");
        int lenth = empno_name.Length;
        string empno = empno_name.Substring(0, location);

        string exist = "N";
        DataTable dtEmp = Session["dataTable"] as DataTable;
        //先檢查選取的人員是否已經在暫存DataTable, 如果已經存在則刪除該人員
        for (int i = 0; i < dtEmp.DefaultView.Count; i++)
        {
            if (dtEmp.Rows[i]["empno"].ToString() == empno)
            {
                exist = "Y";
                break; //跳出迴圈
            }
        }
        return exist;
    }
    //加入已挑選人員清單暫存DataTable 
    private void AddToPickList(string empno_name)
    {
        try
        {
            //分解(工號;姓名)字串成(工號)(姓名)
            int location = empno_name.IndexOf(";");
            int lenth = empno_name.Length;
            string empno = empno_name.Substring(0, location);
            string cname = empno_name.Substring(location + 1, lenth - location - 1);

            //在DataTable中新增一筆資料
            DataTable dtEmp = Session["dataTable"] as DataTable;
            DataRow dr = dtEmp.NewRow();
            dr["empno"] = empno;
            dr["cname"] = cname;
            dr["empno_name"] = empno_name;
            dtEmp.Rows.Add(dr);
            Session["dataTable"] = dtEmp;
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 加入已挑選人員清單暫存DataTable時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
        SetSelectedMemberRepeater();//呼叫設定已挑選成員Repeater
    }
    //移除已挑選人員清單暫存DataTable
    private void RemovedFromPickList(string empno_name)
    {
        string empno = "";
        DataTable dtEmp = Session["dataTable"] as DataTable;
        try
        {
            //分解(工號;姓名)字串成(工號)(姓名)
            int location = empno_name.IndexOf(";");
            empno = empno_name.Substring(0, location);
            //在DataTable中移除此筆資料
            for (int i = 0; i < dtEmp.DefaultView.Count; i++)
            {
                if (dtEmp.Rows[i]["empno"].ToString() == empno)
                {
                    dtEmp.Rows[i].Delete();
                    break; //跳出迴圈
                }
            }
            Session["dataTable"] = dtEmp;
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 移除已挑選人員清單暫存DataTable時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

        if (ViewState["SelectType"].ToString() == "dept") //以部門挑選
        {
            //呼叫復原姓名小圖與連結click
            ReturnRepeaterPickable(empno);
        }
        else //以姓名挑選
        {
            //呼叫復原GridView姓名小圖與連結click
            ReturnGridViewPickable(empno);
        }
        SetSelectedMemberRepeater();//呼叫設定已挑選成員Repeater
    }
    //復原姓名Repeater姓名小圖與連結click
    private void ReturnRepeaterPickable(string empno)
    {
        try
        {
            foreach (RepeaterItem row in rpDeptMember.Items)
            {
                string com_empno = (row.FindControl("hf_com_empno") as HiddenField).Value;
                if (com_empno == empno)
                {
                    (row.FindControl("imgbtn_add1") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_unchecked.png";
                    (row.FindControl("imgbtn_add1") as ImageButton).Enabled = true;
                    (row.FindControl("linbtn_add1") as LinkButton).Enabled = true;
                    (row.FindControl("linbtn_add1") as LinkButton).ForeColor = Color.FromArgb(97, 146, 09); //姓名連結復原綠色
                    (row.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                    (row.FindControl("linbtn_add1") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 復原姓名Repeater姓名小圖與連結click時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

    }
    //復原GridView姓名小圖與連結click
    private void ReturnGridViewPickable(string empno)
    {
        try
        {
            string com_empno = "";
            foreach (GridViewRow row in gvList.Rows)
            {
                com_empno = (row.FindControl("com_empno") as Label).Text;
                if (empno == com_empno)
                {
                    (row.FindControl("imgbtn_add2") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_unchecked.png"; //更換小圖為空白小圖
                    (row.FindControl("imgbtn_add2") as ImageButton).Enabled = true; //小圖不提供click
                    (row.FindControl("linbtn_add2") as LinkButton).Enabled = true; //姓名連結不提供click
                    (row.FindControl("linbtn_add2") as LinkButton).ForeColor = Color.FromArgb(97, 146, 09); //姓名連結復原綠色
                    (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                    (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 復原GridView姓名小圖與連結click時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
    }
    //點選人員GridView的加入小圖與姓名時, 自動加入已挑選成員清單並將加入小圖隱藏
    protected void gvList_RowCommand(object sender, GridViewCommandEventArgs e)
    {

        if (e.CommandName == "imgbtn_add2_cmd")
        {
            if (CheckExistDataTable(e.CommandArgument.ToString()) == "Y")
            {
                RemovedFromPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable 
                DisableGridViewName(e.CommandArgument.ToString(), true); //呼叫讀取GridView清單, 將該姓名的小圖與姓名連結Disable
            }
            else
            {
                AddToPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable 
                DisableGridViewName(e.CommandArgument.ToString(), false); //呼叫讀取GridView清單, 將該姓名的小圖與姓名連結Disable
            }
        }
        if (e.CommandName == "linbtn_add2_cmd")
        {
            if (CheckExistDataTable(e.CommandArgument.ToString()) == "Y")
            {
                RemovedFromPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable  
                DisableGridViewName(e.CommandArgument.ToString(), true); //呼叫讀取GridView清單, 將該姓名的小圖與姓名連結Disable
            }
            else
            {
                AddToPickList(e.CommandArgument.ToString()); //呼叫加入已挑選人員清單暫存DataTable 
                DisableGridViewName(e.CommandArgument.ToString(), false); //呼叫讀取GridView清單, 將該姓名的小圖與姓名連結Disable
            }
        }
    }
    //讀取GridView清單, 將該姓名的小圖與姓名連結Disable
    private void DisableGridViewName(string empno_name, bool recovery)
    {
        try
        {
            //分解(工號;姓名)字串成(工號)
            int location = empno_name.IndexOf(";");
            string empno = empno_name.Substring(0, location);
            string com_empno = "";
            foreach (GridViewRow row in gvList.Rows)
            {
                com_empno = (row.FindControl("com_empno") as Label).Text;
                if (empno == com_empno)
                {
                    if (recovery == true) //復原成uncheck box 及綠色
                    {
                        (row.FindControl("imgbtn_add2") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_unchecked.png"; //更換小圖為空白小圖
                                                                                                                                           //(row.FindControl("imgbtn_add2") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                           //(row.FindControl("linbtn_add2") as LinkButton).Enabled = false; //姓名連結不提供click
                        (row.FindControl("linbtn_add2") as LinkButton).ForeColor = Color.FromArgb(97, 146, 09); //姓名連結復原成綠色
                        (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                        (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
                    }
                    else //變成checkbox 及黑色
                    {
                        (row.FindControl("imgbtn_add2") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_checked.png"; //更換小圖為空白小圖
                                                                                                                                         //(row.FindControl("imgbtn_add2") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                         //(row.FindControl("linbtn_add2") as LinkButton).Enabled = false; //姓名連結不提供click
                        (row.FindControl("linbtn_add2") as LinkButton).ForeColor = Color.Black; //姓名連結改成黑色字
                        (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                        (row.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 讀取GridView清單, 將該姓名的小圖與姓名連結Disable時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

    }
    //比對GridView姓名清單與已挑選成員清單
    private void CompareGridViewList()
    {
        try
        {
            string gv_empno = ""; //GridView工號
            string rp_empno = ""; //已挑選人員工號

            foreach (GridViewRow rowGV in gvList.Rows)
            {
                gv_empno = (rowGV.FindControl("com_empno") as Label).Text;
                foreach (RepeaterItem rowSM in rpSelectedMember.Items)
                {
                    rp_empno = (rowSM.FindControl("hf_empno") as HiddenField).Value;
                    if (rp_empno == gv_empno)
                    {
                        (rowGV.FindControl("imgbtn_add2") as ImageButton).ImageUrl = "~/Comp/EmployeeMultiSelect/images/icon_checked.png"; //更換小圖為空白小圖
                                                                                                                                           //(rowGV.FindControl("imgbtn_add2") as ImageButton).Enabled = false; //小圖不提供click
                                                                                                                                           //(rowGV.FindControl("linbtn_add2") as LinkButton).Enabled = false; //姓名連結不提供click
                        (rowGV.FindControl("linbtn_add2") as LinkButton).ForeColor = Color.Black; //姓名連結改成黑色字
                        (rowGV.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
                        (rowGV.FindControl("linbtn_add2") as LinkButton).Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 比對GridView姓名清單與已挑選成員清單時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

    }
    //設定已挑選成員Repeater
    private void SetSelectedMemberRepeater()
    {
        try
        {
            rpSelectedMember.DataSource = Session["dataTable"] as DataTable;
            rpSelectedMember.DataBind();
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 設定已挑選成員Repeater時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }

    }
    //切換用姓名挑選
    protected void lbtn_byname_Click(object sender, EventArgs e)
    {
        lbtn_bydept.Enabled = true;
        lbtn_bydept.ForeColor = Color.FromArgb(97, 146, 09); //姓名復原成綠色
        lbtn_bydept.Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
        lbtn_bydept.Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
        lbtn_byname.Enabled = false;
        lbtn_byname.ForeColor = Color.Black; //黑色
        lbtn_byname.Attributes.Add("onmouseover", "this.style.color = '#000000'"); //黑色
        lbtn_byname.Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色

        lbl_flag.Text = "0";
        lbl_info.Visible = false;
        ViewState["SelectType"] = "name";
        table1.Style["display"] = "none";
        p_search.Style["display"] = "block"; //顯示關鍵字搜尋
        txt_keyword.Text = "請輸入搜尋關鍵字";
        gvList.Visible = true;
        dataBind(true); //呼叫設定GridView清單
        CompareGridViewList(); //比對GridView姓名清單與已挑選成員清單
    }
    //切換用部門挑選
    protected void lbtn_bydept_Click(object sender, EventArgs e)
    {
        lbtn_bydept.Enabled = false;
        lbtn_bydept.ForeColor = Color.Black; //黑色
        lbtn_bydept.Attributes.Add("onmouseover", "this.style.color = '#000000'"); //黑色
        lbtn_bydept.Attributes.Add("onmouseout", "this.style.color = '#000000'"); //黑色
        lbtn_byname.Enabled = true;
        lbtn_byname.ForeColor = Color.FromArgb(97, 146, 09); //姓名復原成綠色
        lbtn_byname.Attributes.Add("onmouseover", "this.style.color = '#FF7000'"); //橘色
        lbtn_byname.Attributes.Add("onmouseout", "this.style.color = '#619209'"); //綠色
        lbl_flag.Text = "0";

        lbl_info.Visible = false;
        listbox_org.Items.Clear();
        listbox_dept.Items.Clear();
        rpDeptMember.Controls.Clear();
        BindOrgListBox();
        ViewState["SelectType"] = "dept";
        table1.Style["display"] = "block";
        p_search.Style["display"] = "none"; //隱藏關鍵字搜尋
        gvList.Visible = false;
    }
    #region GridView分頁控制
    protected void gvList_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Pager)
        {
            try
            {
                // 取得控制項
                GridView gv = sender as GridView;
                PlaceHolder phdPageNumber = e.Row.FindControl("phdPageNumber") as PlaceHolder;
                //顯示共XX筆
                ((Label)e.Row.FindControl("lblTotalRecord")).Text = ((int)ViewState["totalCount"]).ToString("N0");
                ((Label)e.Row.FindControl("lblTotalPage")).Text = gv.PageCount.ToString("N0");
                //顯示共XX頁
                LinkButton lbtnPage;
                // 設定每頁顯示筆數
                ((DropDownList)e.Row.FindControl("ddlstDispCount")).SelectedValue = gv.PageSize.ToString();
                ((DropDownList)e.Row.FindControl("ddlstDispCount")).SelectedIndexChanged += delegate (object obj, EventArgs args)
                {
                    gvList.PageSize = Convert.ToInt16(((DropDownList)e.Row.FindControl("ddlstDispCount")).SelectedValue);
                    dataBind(false);
                };
                // 產生頁數
                int showRange = 10;
                int pageCount = gv.PageCount;
                int pageIndex = gv.PageIndex;
                int startIndex = (pageIndex + 1 < showRange) ?
                    0 : (pageIndex + 1 + showRange / 2 >= pageCount) ? pageCount - showRange : pageIndex - showRange / 2;
                int endIndex = (startIndex >= pageCount - showRange) ? pageCount : startIndex + showRange;

                phdPageNumber.Controls.Add(new LiteralControl("&nbsp;&nbsp;"));
                for (int i = startIndex; i < endIndex; i++)
                {
                    lbtnPage = new LinkButton();
                    lbtnPage.Text = (i + 1).ToString();
                    lbtnPage.CommandName = "Page";
                    lbtnPage.CommandArgument = (i + 1).ToString();
                    lbtnPage.Font.Overline = false;
                    if (i == pageIndex)
                        lbtnPage.Font.Bold = true;
                    else
                        lbtnPage.Font.Bold = false;
                    phdPageNumber.Controls.Add(lbtnPage);
                    phdPageNumber.Controls.Add(new LiteralControl("&nbsp;"));
                }
            }
            catch (System.Exception ex)
            {
                lbl_Msg.Text = "GridView分頁控制發生錯誤, 錯誤原因:" + ex.Message.ToString();
                lbl_Msg.ForeColor = Color.Red;
            }
            // 動態加入控制項
            //phdPageNumber.Controls.Add(
            //    new LiteralControl(string.Format("&nbsp;&nbsp;{0} / {1}", pageIndex + 1, pageCount)));
            //phdPageNumber.Controls.Add(
            //    new LiteralControl("&nbsp;&nbsp;"));
        }
    }

    //變更頁碼
    protected void gvList_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gvList.PageIndex = e.NewPageIndex;
        dataBind(false);

        CompareGridViewList(); //呼叫比對GridView姓名清單與已挑選成員清單

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }

    }

    //變更每頁XX筆
    protected void ddlstDispCount_SelectedIndexChanged(object sender, EventArgs e)
    {
        DropDownList ddlist = sender as DropDownList;
        gvList.PageSize = int.Parse(ddlist.SelectedItem.Value);
        dataBind(false);

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }
    }
    protected void ibStart_Click(object sender, ImageClickEventArgs e)
    {
        gvList.PageIndex = 0;
        dataBind(false);

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }
    }

    //前一頁
    protected void imLastOne_Click(object sender, ImageClickEventArgs e)
    {
        int pageindex = 0;
        pageindex = gvList.PageIndex;
        if (pageindex - 1 > 0)
        {
            gvList.PageIndex = pageindex - 1;
        }
        dataBind(false);

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }
    }

    //下一頁
    protected void imNextOne_Click(object sender, ImageClickEventArgs e)
    {
        int pageindex = 0;
        pageindex = gvList.PageIndex;
        if (pageindex + 1 < gvList.PageCount)
        {
            gvList.PageIndex = pageindex + 1;
        }
        else
        {
            gvList.PageIndex = gvList.PageCount;
        }
        dataBind(false);

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }
    }

    //最後頁
    protected void imEnd_Click(object sender, ImageClickEventArgs e)
    {
        gvList.PageIndex = gvList.PageCount;
        dataBind(false);

        if (ViewState["SortExpression"] != null && ViewState["SortDirection"] != null)
        {
            showSortDirectionImg(ViewState["SortExpression"].ToString(), ViewState["SortDirection"].ToString());
        }
    }
    #endregion
    #region sorting(排序)
    protected void gvList_Sorting(object sender, GridViewSortEventArgs e)
    {
        //Retrieve the table from the session object.
        DataTable dt = ((DataView)Session["dataView"]).Table;
        string vSortDirection = "";

        if (dt != null)
        {
            //Sort the data.
            vSortDirection = GetSortDirection(e.SortExpression);
            dt.DefaultView.Sort = e.SortExpression + " " + vSortDirection;
            gvList.DataSource = dt;
            gvList.DataBind();
            showSortDirectionImg(e.SortExpression, vSortDirection);

            CompareGridViewList(); //比對GridView姓名清單與已挑選成員清單
        }
    }

    protected string GetSortDirection(string column)
    {
        // By default, set the sort direction to ascending.
        string sortDirection = "ASC";

        // Retrieve the last column that was sorted.
        string sortExpression = ViewState["SortExpression"] as string;

        if (sortExpression != null)
        {
            // Check if the same column is being sorted.
            // Otherwise, the default value can be returned.
            if (sortExpression == column)
            {
                string lastDirection = ViewState["SortDirection"] as string;
                if ((lastDirection != null) && (lastDirection == "ASC"))
                {
                    sortDirection = "DESC";
                }
            }
        }

        // Save new values in ViewState.
        ViewState["SortDirection"] = sortDirection;
        ViewState["SortExpression"] = column;

        return sortDirection;
    }

    protected void showSortDirectionImg(string sortExpression, string sortDirection)
    {
        for (int i = 0; i < gvList.Columns.Count; i++)
        {
            if (gvList.Columns[i].SortExpression == sortExpression)
            {
                switch (sortDirection)
                {
                    case "ASC":
                        ((ImageButton)gvList.HeaderRow.Cells[i].Controls[3]).ImageUrl = "~/Comp/EmployeeMultiSelect/images/arrow_desc.png";
                        break;
                    case "DESC":
                        ((ImageButton)gvList.HeaderRow.Cells[i].Controls[3]).ImageUrl = "~/Comp/EmployeeMultiSelect/images/arrow_asc.png";
                        break;
                }
                ((ImageButton)gvList.HeaderRow.Cells[i].Controls[3]).Visible = true;
            }
            else
                if (gvList.HeaderRow.Cells[i].Controls.Count >= 3)
            {
                ((ImageButton)gvList.HeaderRow.Cells[i].Controls[3]).Visible = false;
            }
        }
    }
    #endregion
    //確定
    protected void btn_submit_Click(object sender, EventArgs e)
    {
        try
        {
            StringBuilder sb_empno = new StringBuilder();
            StringBuilder sb_cname = new StringBuilder();
            DataTable dtEmp = Session["DataTable"] as DataTable;
            for (int i = 0; i < dtEmp.DefaultView.Count; i++)
            {
                sb_empno.Append(dtEmp.Rows[i]["empno"].ToString() + ";");
                sb_cname.Append(dtEmp.Rows[i]["cname"].ToString() + ";");
            }
            dtEmp.Dispose(); //釋放資源
            Session["empno"] = sb_empno.ToString();
            Session["empname"] = sb_cname.ToString();
            ClientScript.RegisterStartupScript(this.GetType(), "n1", "<script>parent.$.fn.colorbox.close();</script>");

            //string script = "<script language='javascript'>setValue('" + sb_cname.ToString() + "','" + ViewState["tbxid"].ToString() + "','" + sb_empno.ToString() + "','" + ViewState["hfid"].ToString() + "');window.close();</script>";
            //ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "callfunc", script, false);  
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 按下確定時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
    }
    //關鍵字搜尋
    protected void btn_search_Click(object sender, EventArgs e)
    {
        lbl_Msg.Text = "";

        string keyword = txt_keyword.Text.Trim();//輸入之關鍵字
        if (keyword == "請輸入搜尋關鍵字")
        {
            keyword = "";
        }

        if (keyword.Trim() != "")
        {
            if (IsContainSymbol(keyword) == true) //檢查輸入文字是否包含特殊符號
            {
                ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "openwin", "<script>alert('輸入的關鍵字不允許使用特殊符號「;-- 或 --」!');</script>", false);
                return;
            }
        }

        try
        {
            SqlCommand sqlCmd = new SqlCommand(@"select top 200 com_empno, rtrim(com_cname) as com_cname, dep_orgcd+'-'+dep_deptcd as deptid, dep_deptname,  com_empno+';'+rtrim(com_cname) as empno_name from common..comper join common..depcod on com_orgcd=dep_orgcd and com_deptcd=dep_deptcd where com_depcd='N' and (com_empno like @keyword or com_cname like @keyword or dep_deptname like @keyword or dep_orgcd+'-'+dep_deptcd like @keyword)");
            sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll('%' + keyword + '%'));

            DataView dvList;
            dvList = ExecSqlCmd(sqlCmd);
            if (dvList.Count <= 0)
            {
                lbl_info.Text = "查無資料";
                lbl_info.Visible = true;
            }
            else
            {
                lbl_info.Text = "";
                lbl_info.Visible = false;
            }
            ViewState["totalCount"] = dvList.Count;
            gvList.DataSource = dvList;
            gvList.DataBind();

            Session["dataView"] = dvList;
        }
        catch (System.Exception ex)
        {
            lbl_Msg.Text = "系統訊息: 關鍵字搜尋時發生錯誤, 錯誤原因:" + ex.Message.ToString();
            lbl_Msg.ForeColor = Color.Red;
        }
        CompareGridViewList(); //比對GridView姓名清單與已挑選成員清單
    }
    //檢查輸入文字是否包含特殊字元
    public static bool IsContainSymbol(string str)
    {
        return Regex.IsMatch(str, @"(;--|--)");
    }

    //執行SQLcmd
    public DataView ExecSqlCmd(SqlCommand sqlCmd)
    {
        //SqlConnection usrcn = new SqlConnection();
        //usrcn.ConnectionString = ConfigurationManager.ConnectionStrings["CS_Contract"].ConnectionString;//資料庫連線, 從web.config取得
        //sqlCmd.Connection = usrcn;

        //SqlDataAdapter cmdSQL = new SqlDataAdapter(sqlCmd);
        //DataSet ds = new DataSet();
        //cmdSQL.Fill(ds, "myTable");

        ////Release Resource
        //usrcn = null;
        //cmdSQL = null;

        #region --- query ---
        DataSet ds = new DataSet();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
        {

            sqlCmd.Connection = sqlConn;
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(ds, "myTable");
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return ds.Tables["myTable"].DefaultView;

    }
}