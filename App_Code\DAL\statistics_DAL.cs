﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for statistics_DAL
/// </summary>
public class statistics_DAL : AMPS.common
{
	public statistics_DAL()
	{
		//
		// TODO: Add constructor logic here
		//
		ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString_omcs"]);
	}


    public DataView GetStatisticsList(string year, string orgcd, string deptcd)
    {
        try
        {
            SqlCommand sqlcmd = new SqlCommand("esp_IM_person_fee_sum_report");
            sqlcmd.CommandType = CommandType.StoredProcedure;
            sqlcmd.Parameters.AddWithValue("@orgcd", orgcd);
            sqlcmd.Parameters.AddWithValue("@deptcd", deptcd);
            sqlcmd.Parameters.AddWithValue("@yyyymm", year);

            return runParaCmd(sqlcmd);
        }
        catch (Exception ex)
        {
            throw new Exception(string.Format("取得統計資料清單，錯誤訊息：{0}", ex.ToString()));
        }
		finally
		{
			ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString"]);
		}
		
		
    }


    public DataView GetStatisticsDetailListx()
    {
        try
        {
            string sqlstr = @"
    SELECT * FROM informgt_statistics_detail INNER JOIN common..comper ON isd_host=com_empno
    ";

            SqlCommand sqlcmd = new SqlCommand(sqlstr);
            sqlcmd.CommandType = CommandType.Text;

            return runParaCmd(sqlcmd);
        }
        catch (Exception ex)
        {
            throw new Exception(string.Format("取得統計詳細清單，錯誤訊息：{0}", ex.ToString()));
        }
    }

    /// <summary>
    /// 取得單位下的所有組
    /// </summary>
    /// <param name="orgcd">單位代號</param>
    /// <returns>回傳清單</returns>
    public DataView GetGroupListWithOrgcd(string orgcd)
    {
        try
        {
            string sqlstr = @"
SELECT DISTINCT LEFT(a.dep_deptid, 4) AS dep_deptcd, LEFT(b.dep_deptid, 4) + b.dep_deptname AS dep_deptname FROM common..depcod AS a
INNER JOIN common..depcod AS b ON LEFT(a.dep_deptid, 4) + '000' = b.dep_deptid
WHERE a.dep_orgcd=@orgcd";

            SqlCommand sqlcmd = new SqlCommand(sqlstr);
            sqlcmd.CommandType = CommandType.Text;
            sqlcmd.Parameters.AddWithValue("@orgcd", orgcd);

            return runParaCmd(sqlcmd);
        }
        catch (Exception ex)
        {
            throw new Exception(string.Format("取得單位下的所有組，錯誤訊息：{0}", ex.ToString()));
        }
		finally
		{
			ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString"]);
		}
    }


 

    #region 取得單位清單
    /// <summary>
    /// 取得單位清單
    /// </summary>
    /// <returns>回傳單位清單</returns>
    public DataView GetOrgList()
    {
        try
        {
            string sqlstr = "SELECT org_orgcd, org_abbr_chnm1 FROM common..orgcod WHERE org_status='A' and org_orgcd='53'";

            SqlCommand sqlcmd = new SqlCommand(sqlstr);
            sqlcmd.CommandType = CommandType.Text;

            return runParaCmd(sqlcmd);
        }
        catch (Exception ex)
        {
            throw new Exception(string.Format("取得單位清單，錯誤訊息：{0}", ex.ToString()));
        }
		finally
		{
			ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString"]);
		}
    }
    #endregion

    #region 取得單位部門清單
    /// <summary>
    /// 取得單位部門清單
    /// </summary>
    /// <param name="orgcd">單位代碼</param>
    /// <returns>單位部門清單回傳</returns>
    public DataView GetDeptListWithOrgcd(string orgcd)
    {
        try
        {
            string sqlstr = "SELECT dep_deptcd, dep_deptname FROM common..depcod WHERE dep_orgcd=@orgcd";

            SqlCommand sqlcmd = new SqlCommand(sqlstr);
            sqlcmd.CommandType = CommandType.Text;
            sqlcmd.Parameters.AddWithValue("@orgcd", orgcd);

            return runParaCmd(sqlcmd);
        }
        catch (Exception ex)
        {
            throw new Exception(string.Format("取得單位部門清單，錯誤訊息：{0}", ex.ToString()));
        }
		finally
		{
			ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString"]);
		}
    }
    #endregion
}