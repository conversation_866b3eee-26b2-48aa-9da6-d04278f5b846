{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/dv.js"], "names": ["module", "exports", "_", "t", "default", "e", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "+EAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,yDAAyDC,MAAM,KAAKC,OAAO,4GAA4GD,MAAM,KAAKE,UAAU,EAAEC,cAAc,yDAAyDH,MAAM,KAAKI,YAAY,4GAA4GJ,MAAM,KAAKK,YAAY,qCAAqCL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,WAAWC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,cAAcC,KAAK,YAAYC,EAAE,iBAAiBC,EAAE,WAAWC,GAAG,YAAYC,EAAE,aAAaC,GAAG,cAAcxB,EAAE,WAAWyB,GAAG,YAAYC,EAAE,SAASC,GAAG,UAAUC,EAAE,WAAWC,GAAG,cAAc,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAArpCD,CAAE,EAAQ", "file": "chunks/31.chunk.js", "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_dv=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"dv\",weekdays:\"އާދިއްތަ_ހޯމަ_އަންގާރަ_ބުދަ_ބުރާސްފަތި_ހުކުރު_ހޮނިހިރު\".split(\"_\"),months:\"ޖެނުއަރީ_ފެބްރުއަރީ_މާރިޗު_އޭޕްރީލު_މޭ_ޖޫން_ޖުލައި_އޯގަސްޓު_ސެޕްޓެމްބަރު_އޮކްޓޯބަރު_ނޮވެމްބަރު_ޑިސެމްބަރު\".split(\"_\"),weekStart:7,weekdaysShort:\"އާދިއްތަ_ހޯމަ_އަންގާރަ_ބުދަ_ބުރާސްފަތި_ހުކުރު_ހޮނިހިރު\".split(\"_\"),monthsShort:\"ޖެނުއަރީ_ފެބްރުއަރީ_މާރިޗު_އޭޕްރީލު_މޭ_ޖޫން_ޖުލައި_އޯގަސްޓު_ސެޕްޓެމްބަރު_އޮކްޓޯބަރު_ނޮވެމްބަރު_ޑިސެމްބަރު\".split(\"_\"),weekdaysMin:\"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"D/M/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"ތެރޭގައި %s\",past:\"ކުރިން %s\",s:\"ސިކުންތުކޮޅެއް\",m:\"މިނިޓެއް\",mm:\"މިނިޓު %d\",h:\"ގަޑިއިރެއް\",hh:\"ގަޑިއިރު %d\",d:\"ދުވަހެއް\",dd:\"ދުވަސް %d\",M:\"މަހެއް\",MM:\"މަސް %d\",y:\"އަހަރެއް\",yy:\"އަހަރު %d\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}