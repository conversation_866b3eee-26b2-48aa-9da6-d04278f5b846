﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_XFile.aspx.cs" Inherits="TreatyCase2_XFile" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>新增檔案</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function file_modify(fid, FType) {
            $(".ajax_mod").colorbox({
                href: './TreatyCase2_XFileModify.aspx?fid=' + fid + "&FType=" + FType,
                title: '檔案維護'
                , iframe: true, width: "600px", height: "300px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function fileup(seno, sub_seno, FType) {
            $(".ajax_up").colorbox({
                href: './TreatyCase2_XFileUp.aspx?seno=' + seno + '&sub_seno=' + sub_seno + "&FType=" + FType,
                title: '檔案上傳'
                , iframe: true, width: "660px", height: "280px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }


        function close_win() {
            alert("指定成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 15px">
                <tr>
                    <td>
                        <div class="td_left">&nbsp;<asp:Button ID="BT_FileUp" runat="server" Text="新增" class="genbtnS ajax_up" /></div>
                        <span class="stripeMe">
                            <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" Width="100%" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                                <Columns>
                                    <asp:TemplateField HeaderText="功能">
                                        <ItemTemplate>
                                            <asp:Label ID="LB_tcdf_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                            <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>刪除</asp:LinkButton>
                                            <asp:LinkButton ID="LB_edit" runat="server" class="ajax_mod" CommandName="xEdit" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>維護</asp:LinkButton>
                                        </ItemTemplate>
                                        <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="附件名稱">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                        </ItemTemplate>
                                        <HeaderStyle Width="250px"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Left" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="說明">
                                        <ItemTemplate>
                                            <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle Width="300px"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Left" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="上傳者">
                                        <ItemTemplate>
                                            <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle Width="50px"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="上傳日期">
                                        <ItemTemplate>
                                            <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle Width="100px"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                </Columns>
                                <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                <PagerSettings Position="Bottom" />
                                <PagerStyle HorizontalAlign="Left" />
                            </asp:GridView>
                            <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                            <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

                        </span>
                    </td>
                </tr>
            </table>

        </span>
    </form>
</body>
</html>
