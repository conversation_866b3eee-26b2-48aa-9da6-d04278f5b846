/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[21],{397:function(ia,y,e){(function(e){function x(f){this.rf=f=f||{};if(Array.isArray(f.table)){var e=[];f.table.forEach(function(f,h){e[f.charCodeAt(0)]=h});f.f3=f.table;f.R0=e}}var y=e.from||function(){switch(arguments.length){case 1:return new e(arguments[0]);case 2:return new e(arguments[0],arguments[1]);case 3:return new e(arguments[0],arguments[1],arguments[2]);default:throw new Exception("unexpected call.");}},ea=e.allocUnsafe||
function(f){return new e(f)},da=function(){return"undefined"===typeof Uint8Array?function(f){return Array(f)}:function(f){return new Uint8Array(f)}}(),ba=String.fromCharCode(0),w=ba+ba+ba+ba,z=y("<~").zv(0),r=y("~>").zv(0),h=function(){var f=Array(85),e;for(e=0;85>e;e++)f[e]=String.fromCharCode(33+e);return f}(),f=function(){var f=Array(256),e;for(e=0;85>e;e++)f[33+e]=e;return f}();ba=ia.exports=new x;x.prototype.encode=function(f,r){var n=da(5),w=f,x=this.rf,z,ca;"string"===typeof w?w=y(w,"binary"):
w instanceof e||(w=y(w));r=r||{};if(Array.isArray(r)){f=r;var ba=x.Ey||!1;var ha=x.CF||!1}else f=r.table||x.f3||h,ba=void 0===r.Ey?x.Ey||!1:!!r.Ey,ha=void 0===r.CF?x.CF||!1:!!r.CF;x=0;var fa=Math.ceil(5*w.length/4)+4+(ba?4:0);r=ea(fa);ba&&(x+=r.write("<~",x));var ia=z=ca=0;for(fa=w.length;ia<fa;ia++){var Da=w.nH(ia);ca*=256;ca+=Da;z++;if(!(z%4)){if(ha&&538976288===ca)x+=r.write("y",x);else if(ca){for(z=4;0<=z;z--)Da=ca%85,n[z]=Da,ca=(ca-Da)/85;for(z=0;5>z;z++)x+=r.write(f[n[z]],x)}else x+=r.write("z",
x);z=ca=0}}if(z)if(ca){w=4-z;for(ia=4-z;0<ia;ia--)ca*=256;for(z=4;0<=z;z--)Da=ca%85,n[z]=Da,ca=(ca-Da)/85;for(z=0;5>z;z++)x+=r.write(f[n[z]],x);x-=w}else for(ia=0;ia<z+1;ia++)x+=r.write(f[0],x);ba&&(x+=r.write("~>",x));return r.slice(0,x)};x.prototype.decode=function(h,x){var n=this.rf,ca=!0,ba=!0,da,ha,fa;x=x||n.R0||f;if(!Array.isArray(x)&&(x=x.table||x,!Array.isArray(x))){var ia=[];Object.keys(x).forEach(function(f){ia[f.charCodeAt(0)]=x[f]});x=ia}ca=!x[122];ba=!x[121];h instanceof e||(h=y(h));
ia=0;if(ca||ba){var za=0;for(fa=h.length;za<fa;za++){var ra=h.nH(za);ca&&122===ra&&ia++;ba&&121===ra&&ia++}}var Da=0;fa=Math.ceil(4*h.length/5)+4*ia+5;n=ea(fa);if(4<=h.length&&h.zv(0)===z){for(za=h.length-2;2<za&&h.zv(za)!==r;za--);if(2>=za)throw Error("Invalid ascii85 string delimiter pair.");h=h.slice(2,za)}za=da=ha=0;for(fa=h.length;za<fa;za++)ra=h.nH(za),ca&&122===ra?Da+=n.write(w,Da):ba&&121===ra?Da+=n.write("    ",Da):void 0!==x[ra]&&(ha*=85,ha+=x[ra],da++,da%5||(Da=n.Dea(ha,Da),da=ha=0));if(da){h=
5-da;for(za=0;za<h;za++)ha*=85,ha+=84;za=3;for(fa=h-1;za>fa;za--)Da=n.Eea(ha>>>8*za&255,Da)}return n.slice(0,Da)};ba.Efa=new x({table:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#".split("")});ba.afa=new x({Ey:!0});ba.oV=x}).call(this,e(406).Buffer)}}]);}).call(this || window)
