﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_todo_blacklist : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    List<long> _dtBlackList
    {
        get { return (List<long>)ViewState["_BlackList"]; }
        set { ViewState["_BlackList"] = value; }
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["t_id"] = "";
            if (Request.QueryString["t_id"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["t_id"].ToString()) || (Request.QueryString["t_id"].Length > 5))
                    Response.Redirect("../danger.aspx");
                ViewState["t_id"] = Request.QueryString["t_id"].ToString();
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            Bind_Auth();
            Bind_Data();
        }
    }

    private void Bind_Data()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "view_blacklist");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                sgvList.DataSource = dt;
                sgvList.DataBind();

                _dtBlackList = dt.AsEnumerable().Select(x => x.Field<long>("tt_seno")).ToList();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Bind_Auth()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Auth");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            string str_auth = dv_auth[0]["RW"].ToString();
            if (str_auth == "X")
                Response.Redirect("../NoAuthRight.aspx");

            //if (str_auth != "W")
            //{
            //    string script = "<script language='javascript'>alert('無編輯權限！');location.href='./TechCase_todo_search.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
            //    ClientScript.RegisterStartupScript(this.GetType(), "NoAuthRight", script);
            //}
        }
    }

    protected void sgvList_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.sgvList.PageIndex = e.NewPageIndex;
        Bind_Data();
    }

    protected void IB_del_Click(object sender, ImageClickEventArgs e)
    {
        ImageButton IB_del = (ImageButton)sender;
        long tt_seno = long.Parse(IB_del.CommandArgument);

        _dtBlackList = _dtBlackList.Where(x => x != tt_seno).ToList();

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "modify_blacklist");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_blacklist", oRCM.SQLInjectionReplaceAll(string.Join(",", _dtBlackList)));
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();

                ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('刪除成功');
        </script>
        ", false);

                Bind_Data();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }

    }
}