﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Search_draft.aspx.cs" Inherits="Search_draft" EnableEventValidation="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function doNewCase(seno, contno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_NewVer.aspx?contno=" + contno
                , iframe: true, width: "440px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    if ($.colorbox.data != "undefined")
                        alert($.colorbox.data);
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }

        .Mask {
            display: none;
            position: fixed;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 2;
            top: 0;
            left: 0;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">議約管理 / 議約草稿(含簽核中)查詢</div>
                            <div class="right font-light">
                                <table border="0" cellspacing="5" cellpadding="0">
                                    <tr>
                                        <td width="80" align="right">
                                            <div class="font-title titlebackicon">關鍵字</div>
                                        </td>
                                        <td width="200">
                                            <asp:TextBox ID="txtKeyWord" runat="server" Width="265px" title="ex：單位、議約編號、議約名稱、推廣人、客戶名稱" class="inputex width100 inputhint" />
                                        </td>
                                        <td>
                                            <asp:Button ID="btnQuery" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery_Click"></asp:Button>
                                            <span class="font-normal font-size3 font-bold">
                                                <img src="../images/icon-1301.gif" alt="icon" /><a href="#" id="advancesearchopen">進階查詢</a></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="twocol margin5TB">
                            <div class="left">
                                <span class="font-title font-bold font-size3">案件列表</span>&nbsp;&nbsp;
           條件：<select><option>個人</option>
           </select>&nbsp;
              <asp:DropDownList ID="DDL_CaseStatus" runat="server" OnSelectedIndexChanged="DDL_CaseStatus_SelectedIndexChanged" AutoPostBack="True">
                  <asp:ListItem Value="0">全部(不含草稿&簽核中)</asp:ListItem>
                  <asp:ListItem Value="2" Selected="True">草稿</asp:ListItem>
                  <asp:ListItem Value="E">簽核中</asp:ListItem>
                  <asp:ListItem Value="P">法務內稽中，暫停轉派法務承辦</asp:ListItem>
              </asp:DropDownList>
                            </div>
                        </div>
                        <div id="advancesearch" class="gentablenoline font-normal margin5TB">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align="right">單位別：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="orgcd_name" DataValueField="orgcd" AppendDataBoundItems="True">
                                            <asp:ListItem Value="00">   --請選擇--  </asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td align="right">契約性質：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True">
                                            <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="right">案件編號/名稱：</td>
                                    <td>
                                        <asp:TextBox ID="TB_contnoName" runat="server" Width="150px"></asp:TextBox></td>
                                    <td align="right">客戶名稱：</td>
                                    <td>
                                        <asp:TextBox ID="tbxCompName" runat="server" Width="150px"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td align="right">單位承辦人員：</td>
                                    <td>
                                        <asp:TextBox ID="tbxPromoterName" runat="server" Width="150px"></asp:TextBox></td>
                                    <td align="right">單位執行部門：</td>
                                    <td>
                                        <asp:TextBox ID="tbxReqDept" runat="server" Width="150px"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td align="right">案源：</td>
                                    <td colspan="3">
                                        <asp:CheckBoxList ID="cbxCaseClass" runat="server" RepeatDirection="Horizontal" BorderWidth="0px" Width="650px">
                                            <asp:ListItem Value="A">國外無收入</asp:ListItem>
                                            <asp:ListItem Value="F">國內無收入</asp:ListItem>
                                            <asp:ListItem Value="N">洽案系統</asp:ListItem>
                                            <asp:ListItem Value="R">標案系統</asp:ListItem>
                                            <asp:ListItem Value="M">NDA</asp:ListItem>
                                            <asp:ListItem Value="U">國外契約</asp:ListItem>
                                            <asp:ListItem Value="C">工服</asp:ListItem>
                                            <asp:ListItem Value="S">新創事業</asp:ListItem>
                                            <asp:ListItem Value="T">其它</asp:ListItem>
                                        </asp:CheckBoxList></td>


                                </tr>
                            </table>
                            <div class="twocol margin5TB">

                                <div class="right">
                                    <button id="advancesearchclear" class="genbtn">清除</button>
                                    <button id="advancesearchclose" class="genbtn">取消</button>
                                    <asp:Button ID="btnQuery1" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery1_Click" />
                                </div>
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <table width="100%" border="0" cellspacing="5" cellpadding="0" class="font-size3 font-bold">
                                <tr>
                                    <td>
                                        <div class="bgstepblue">議約需求</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblueS">分案</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">法務承辦</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">案件審核</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblueS">結束</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">契約簽辦</div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <!-- fixwidth -->
                    <div class="fixwidth">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td>
                                    <span class="stripeMe">
                                        <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                            <FooterStyle BackColor="White" />
                                            <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                            <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                            <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                            <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                            <Columns>
                                                <asp:TemplateField HeaderText="單位" SortExpression="org_name">
                                                    <ItemTemplate>
                                                        <asp:Label ID="LB_org_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("org_name").ToString())) %>'></asp:Label>
                                                    </ItemTemplate>
                                                    <ItemStyle Width="40px" HorizontalAlign="Center" />
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="案件編號" SortExpression="tmp_case_actno">
                                                    <ItemTemplate>
                                                        <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tr_seno")+";"+Eval("tmp_case_actno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_actno").ToString())) %>'></asp:LinkButton><br />
                                                    </ItemTemplate>
                                                    <ItemStyle Width="140px" HorizontalAlign="Left" />
                                                </asp:TemplateField>
                                                <asp:BoundField DataField="tmp_case_name" SortExpression="tmp_case_name" HeaderText="洽案／契約名稱">
                                                    <ItemStyle Width="260px" />
                                                </asp:BoundField>
                                                <asp:BoundField DataField="tr_compname" SortExpression="tr_compname" HeaderText="客戶名稱">
                                                    <ItemStyle Width="260px" />
                                                </asp:BoundField>
                                                <asp:TemplateField HeaderText="單位&lt;br&gt;承辦人" SortExpression="tr_promoter_name">
                                                    <ItemTemplate>
                                                        <asp:Label ID="LB_promoter_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tr_promoter_name").ToString())) %>'></asp:Label>
                                                    </ItemTemplate>
                                                    <HeaderStyle HorizontalAlign="Center" />
                                                    <ItemStyle HorizontalAlign="Center" Width="70px" />
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="狀態" SortExpression="tr_promoter_name">
                                                    <ItemTemplate>
                                                        <asp:Literal ID="LB_status" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_status_flag").ToString())) %>'></asp:Literal>
                                                    </ItemTemplate>
                                                    <HeaderStyle HorizontalAlign="Center" />
                                                    <ItemStyle HorizontalAlign="Center" Width="70px" />
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="機密等級">
                                                    <ItemTemplate>
                                                        <asp:Image ID="Image1" runat="server" Height="31px" ImageUrl="../images/CONFIDENTIAL.png" Width="80px" />
                                                    </ItemTemplate>
                                                    <ItemStyle Width="80px" HorizontalAlign="Center" />
                                                </asp:TemplateField>

                                            </Columns>
                                            <EmptyDataTemplate>
                                                <!--當找不到資料時則顯示「無資料」-->
                                                <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                            </EmptyDataTemplate>
                                            <FooterStyle BackColor="White" />
                                            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                        </cc1:SmartGridView>
                                      <%--  <asp:SqlDataSource ID="SDS_search" runat="server" />--%>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc1:Foot runat="server" ID="Foot" />

     <%--   <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            $(document).ready(function () {
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                $(".inputhint").tooltip({
                    position: { my: "left+10 bottom+40", at: "left bottom " },
                    tooltipClass: "custom-tooltip-styling",
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });

            });
            $("#advancesearch").dialog({
                open: function (type, data) { $(this).parent().appendTo("form").css({ "z-index": "101" }); },
                position: ["center", 100],
                width: 800,
                height: 310,
                autoOpen: false,
                show: { duration: 300 },
                hide: { duration: 300 }
            });
            $("#advancesearchopen").click(function () {
                if ($("#txtKeyWord").val() != "") {
                    $("#TB_contnoName").val($("#txtKeyWord").val());
                    $("#tbxCompName").val($("#txtKeyWord").val());
                    $("#tbxPromoterName").val($("#txtKeyWord").val());
                    $("#tbxReqDept").val($("#txtKeyWord").val());
                    $("#tbxHandleName").val($("#txtKeyWord").val());
                }
                $("#advancesearch").dialog("open");
            });
            $("#advancesearchclose").click(function () {
                $("#advancesearch").dialog("close");
            });
            $("#advancesearchclear").click(function () {
                $("#ddlOrgcd").val("00");
                $("#ddlContType").val("");
                $("#TB_contnoName").val("");
                $("#tbxCompName").val("");
                $("#tbxPromoterName").val("");
                $("#tbxReqDept").val("");
                $('input[type=checkbox]').attr('checked', false);
                $("#DDL_CaseStatus1").val("0");
                $("#tbxHandleName").val("");
                $("#ddlShowCase").val("0");
                $("#ddl_amend").val("0");
                return false;
            });
            $("#advancesearching").click(function () {
                $("#txtKeyWord").val("");
                $('#advancesearch').dialog('close');
                $("#btnQuery").click();
            });
        </script>

    </form>
</body>
</html>
