﻿using System;
using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase_modify : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {
            case "N":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視NDA資訊";
                break;
            case "F":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
                break;
            case "A":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國外無收入資訊";
                break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視標案資訊";
                break;
            case "C":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
                break;
            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion

    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;
        btnEngage.Text = GetEngageNDAText(strCaseNo);
        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            //case "U":
            //    bResult = true;
            //    break;
            case "R":
                bResult = true;
                break;
            case "F":
                bResult = true;
                break;
            case "A":
                bResult = true;
                break;
            case "C":
                bResult = true;
                break;
            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion

    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    protected void btnEngage_Click(object sender, EventArgs e)
    {
        string strCaseNo = txtComplexNo.Text.Trim();
        string strCaseNo_C = txtOldContno.Text.Trim();
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
        string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();
        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案/Engage/Base/caseBase.aspx?contno=xxxxx
                strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
                break;

            case "A": // 國外契約   
                strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                break;

            case "F": // 國內契約  
                strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
                break;
            case "C": // 國外契約
                strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                break;
        }

        script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);


    }
    #endregion
    private string 匯率
    {
        get
        {
            string str_字串 = TB_money_rate.Text.Trim().Replace(".", "");
            if (str_字串 == "") return "";
            if (Regex.IsMatch(str_字串, "^[0-9]*$") == false)
                Response.Redirect("../danger.aspx");
            return TB_money_rate.Text.Trim();
        }
    }

    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //txt_px_name.Attributes.Add("onChange", "Find_empno_kw('txt_px_name',2);");
        //txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
        BT_Customer.Attributes.Add("onclick", "find_customer2();");

        ClientScript.GetPostBackEventReference(new PostBackOptions(SGV_company));

        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            if (Request["seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
            }
            if (ViewState["seno"] == null)
                Response.Redirect("../NoAuthRight.aspx");


            ViewState["empNo"] = ssoUser.empNo;
            //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_auth.SelectParameters.Clear();
            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_auth.SelectCommand = "esp_TreatyCase_Auth";
            //SDS_auth.SelectParameters.Add("seno",  SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_auth.SelectParameters.Add("empno", SQLInjectionReplace(ssoUser.empNo));
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_Auth";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_auth = dt.DefaultView;
            if (dv_auth.Count >= 1)
            {
                string str_auth = Server.HtmlEncode(dv_auth[0][0].ToString());
                if (str_auth == "X")
                    Response.Redirect("../NoAuthRight.aspx");
                ViewState["auth"] = Server.HtmlEncode(dv_auth[0][0].ToString());
                ViewState["SYS"] = Server.HtmlEncode(dv_auth[0][1].ToString());
                ViewState["Module"] = Server.HtmlEncode(dv_auth[0][2].ToString());
            }

            Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), Server.HtmlEncode("維護承辦單"), "", Server.HtmlEncode(ViewState["seno"].ToString()), "treaty\\TreatyCase_modify.aspx");
            BT_AddInspect.Attributes.Add("onclick", "Add_Inspect(" + Server.HtmlEncode(ViewState["seno"].ToString()) + ");");
            BindData();
            BindData_file();
            BindInspect();
            BindDefer();
            LT_tratycase_info.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_info.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >案件紀錄</a>";
            LT_infoHandel.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_infoHandel.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次承辦人資訊</a>";
            LT_historyRecord.Text = "<a class='iterm_dymanic_historyRecord' rel='./TreatyCase_historyRecord.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次修改紀錄</a>";
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            txt_req_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            BT_SendInspect.Attributes.Add("onclick", "return  confirm('確定要審查 ?');");
            BT_End.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
            bt_cancle.Attributes.Add("onclick", "return  confirm('確定要取消需求?\\n <<取消前請知會法務人員>> ');");
            BT_FileUp.Attributes.Add("onclick", "treaty_fileup('" + txtComplexNo.Text.Replace("-", "") + "'," + ViewState["seno"].ToString() + ");");
            BT_defert.Attributes.Add("onclick", "treaty_defert(" + ViewState["seno"].ToString() + ");");
            BT_cop.Attributes.Add("onclick", "treaty_cop(" + ViewState["seno"].ToString() + ");");
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
        }
        #region 從洽案、領投標進來的狀況
        DateTime dTime = DateTime.Now;


        #endregion
        ViewState["IsNewType"] = false;
        #region 舊案新件  狀況

        #endregion
        #region 契約修訂狀況


        #endregion

        //lb_keyin_date.Text = DateTime.Now.ToString("yyyyMMdd"); //建檔日期
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Inspect_renew")
        {
            BindInspect();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Defer_renew")
        {
            BindDefer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "cop_renew")
        {
            Bind_cop(ViewState["seno"].ToString());
        }
        if (IsPostBack)
        {
            Bind_sRC_init(ViewState["tr_class"].ToString());
        }

        setParamFileOpen();
    }

    void setParamFileOpen()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        BT_OpenFileCompare.Attributes.Add("key", HttpUtility.UrlEncode("empno=" + sso.empNo + "&議約流水號=" + ViewState["seno"].ToString() + "&議約編號=" + txtComplexNo.Text));
    }
    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = "esp_TreatyCase_MultiCustomer_List_by_NOs";
        //SDS_company.SelectParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_company.SelectParameters.Add("customers", SQLInjectionReplace(this.h_compno.Value.ToString()));
        //SDS_company.SelectParameters.Add("簽約金額", SQLInjectionReplace(ViewState["簽約金額"].ToString()));
        //SDS_company.SelectParameters.Add("mode", SQLInjectionReplace("edit"));
        //SDS_company.DataBind();
        //SGV_company.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_MultiCustomer_List_by_NOs";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(this.h_compno.Value.ToString()));
            sqlCmd.Parameters.AddWithValue("@簽約金額", oRCM.SQLInjectionReplaceAll(ViewState["簽約金額"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "edit");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private static void DisableControl(WebControl control)
    {
        Type controlType = control.GetType();
        if (controlType == typeof(CheckBox))
        {
            ((CheckBox)control).InputAttributes.Add("disabled", "disabled");
        }
        else if (controlType == typeof(RadioButton))
        {
            ((RadioButton)control).InputAttributes.Add("disabled", "true");
        }
        else if (controlType == typeof(ImageButton))
        {
            ((ImageButton)control).Enabled = false;
        }
        else
        {
            control.Attributes.Add("readonly", "readonly");
        }
    }

    public void BindData()
    {
        ViewState["簽約金額"] = "0";

        //SDS_NR.SelectCommand = " select *,convert(decimal(18,0),tc_money) x_money,( select top 1 tr_seno from treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+tr_ver+tr_seqsn=tc_year+tc_orgcd+tc_class+tc_sn+tc_ver+tc_seqsn )tr_seno from treaty_case where tc_seno = @sn ";
        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandText = "esp_TreatyCase";
        //oCmd_1.CommandType = CommandType.StoredProcedure;
        //oCmd_1.Parameters.AddWithValue("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
        //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("view"));
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        //DataSet ds_1 = new DataSet();
        //oda_1.Fill(ds_1, "myTable");
        //DataView dv = ds_1.Tables[0].DefaultView;

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        ViewState["簽約金額"] = "0";
        DataView dv = dt.DefaultView;
        if (dv.Count > 0)
        {
            string str_tr_year = Server.HtmlEncode(dv[0]["tc_year"].ToString().Trim());
            string str_tr_orgcd = Server.HtmlEncode(dv[0]["tc_orgcd"].ToString().Trim());
            string str_tr_class = Server.HtmlEncode(dv[0]["tc_class"].ToString().Trim());
            ViewState["tr_class"] = str_tr_class;
            if ((str_tr_class == "T") && (ViewState["SYS"].ToString().IndexOf("ADM") >= 1))
                btnDelete.Visible = true;

            string str_tr_sn = Server.HtmlEncode(dv[0]["tc_sn"].ToString().Trim());
            string str_tr_ver = Server.HtmlEncode(dv[0]["tc_ver"].ToString().Trim());
            string str_tr_seqsn = Server.HtmlEncode(dv[0]["tc_seqsn"].ToString().Trim());
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;
            txtOldContno.Text = Server.HtmlEncode(dv[0]["tc_old_contno"].ToString().Trim());
            btnEngage.Visible = GetEngageNDAVisible(str_actcontno);
            switch (dv[0]["tc_language"].ToString().Trim())
            {
                //case "0": rb_language_other.Checked = true; break;
                case "1": rb_language_chiness.Checked = true; break;
                case "2": rb_language_english.Checked = true; break;
            }
            #region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + oRCM.SQLInjectionReplaceAll(dv[0]["tc_promoter_no"].ToString().Trim()) + "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tc_promoter_no"].ToString().Trim()));
                try
                {
                    DataTable dt_emp = new DataTable();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt_emp);
                    DataView dv_emp = dt_emp.DefaultView;
                    if (dv_emp.Count >= 1)
                    {
                        txt_promoter_name.Text = Server.HtmlEncode(dv_emp[0]["com_cname"].ToString().Trim());
                        txt_promoter_empno.Value = Server.HtmlEncode(dv_emp[0]["com_empno"].ToString().Trim());
                        txtTel.Text = Server.HtmlEncode(dv_emp[0]["com_telext"].ToString().Trim());
                        txtOrgAbbrName.Text = Server.HtmlEncode(dv_emp[0]["orgName"].ToString().Trim());
                        ViewState["com_orgcd"] = dv_emp[0]["com_orgcd"].ToString().Trim();
                    }

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            txt_req_dept.Text = Server.HtmlEncode(dv[0]["tc_req_dept"].ToString().Trim()); //抓當時留存資料(以免異動對改掉)
            txt_name.Text = Server.HtmlEncode(dv[0]["tc_name"].ToString().Trim());//Server.HtmlEncode(dv[0]["tc_name"].ToString().Trim());//洽案（契約名稱）
            #endregion
            #region 契約預估金額
            BindContMoneyType();
            ddlContMoneyType.SelectedValue = IIf(dv[0]["tc_money_type"].ToString().Trim() == "", "TWD", dv[0]["tc_money_type"].ToString().Trim());

            txtContMoney.Text = Server.HtmlEncode(IIf(dv[0]["x_money"].ToString() == "", "0", dv[0]["x_money"].ToString()));
            TB_money_rate.Text = Server.HtmlEncode(dv[0]["tc_money_rate"].ToString().Trim());
            ViewState["簽約金額"] = (dv[0]["tc_money"].ToString().Trim() == "0" ? 0 : decimal.Parse(dv[0]["tc_money"].ToString().Trim()))
                                  * (dv[0]["tc_money_rate"].ToString().Trim() == "" ? 1 : decimal.Parse(dv[0]["tc_money_rate"].ToString().Trim()));
            #endregion
            #region 客戶
            h_compno.Value = dv[0]["tc_compidno_all"].ToString().Trim().Replace("㊣", ",");//簽約對象(多)
            BindData_Customer();
            #endregion
            #region 案件性質
            cb_conttype_b0.Enabled = false;
            cb_conttype_b1.Enabled = false;
            cb_conttype_d4.Enabled = false;
            cb_conttype_d5.Enabled = false;
            cb_conttype_d7.Enabled = false;
            CB_技術讓與.Enabled = false;
            cb_conttype_ns.Enabled = false;
            cb_conttype_rb.Enabled = false;
            cb_conttype_m.Enabled = false;
            cb_conttype_c.Enabled = false;
            rb_conttype_uo.Enabled = false;
            rb_conttype_ui.Enabled = false;
            rb_conttype_bd.Enabled = false;
            rb_conttype_other.Enabled = false;

            switch (dv[0]["tc_class"].ToString().Trim())
            {
                case "A":
                    #region A
                    rb_conttype_uo.Checked = true;
                    #endregion
                    break;
                case "C":
                    #region C
                    cb_conttype_c.Checked = true;
                    #endregion
                    break;

                case "M":
                    #region M
                    cb_conttype_m.Checked = true;
                    #endregion
                    break;
                case "N":
                    #region N
                    if (dv[0]["tc_conttype_b0"].ToString().Trim() == "1")
                        cb_conttype_b0.Checked = true;

                    if (dv[0]["tc_conttype_b1"].ToString().Trim() == "1")
                        cb_conttype_b1.Checked = true;
                    else
                        cb_conttype_b1.Checked = false;
                    if (dv[0]["tc_conttype_d4"].ToString().Trim() == "1")
                        cb_conttype_d4.Checked = true;

                    if (dv[0]["tc_conttype_d5"].ToString().Trim() == "1")
                        cb_conttype_d5.Checked = true;
                    else
                        cb_conttype_d5.Checked = false;
                    if (dv[0]["tc_conttype_d7"].ToString().Trim() == "1")
                        cb_conttype_d7.Checked = true;

                    if (dv[0]["tc_conttype_ns"].ToString().Trim() == "1")
                        cb_conttype_ns.Checked = true;

                    if (dv[0]["tc_amend"].ToString().Trim().Trim() != "0")
                    {
                        lb_Amend_Show.Visible = true;
                        spanContractEdit.Visible = true;
                        rbl_amend.SelectedValue = Server.HtmlEncode(dv[0]["tc_amend"].ToString().Trim().Trim());
                        LB_amend.Text = Server.HtmlEncode(rbl_amend.SelectedItem.Text);
                        txtamend_other_desc.Text = Server.HtmlEncode(dv[0]["tc_amend_other_desc"].ToString().Trim().Trim());
                    }
                    cb_conttype_b0.Enabled = true;
                    cb_conttype_b1.Enabled = true;
                    cb_conttype_d4.Enabled = true;
                    cb_conttype_d5.Enabled = true;
                    cb_conttype_d7.Enabled = true;
                    CB_技術讓與.Enabled = true;
                    cb_conttype_ns.Enabled = true;
                    #endregion
                    break;
                case "R":
                    #region R
                    cb_conttype_rb.Checked = true;
                    #endregion
                    break;
                case "S":
                    #region S
                    rb_conttype_bd.Checked = true;
                    #endregion
                    break;
                case "T":
                    #region T
                    rb_conttype_other.Checked = true;
                    PL_CoPromoter.Visible = true;
                    //SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  tr_promoter_no_other   FROM treaty_requisition  where tr_seno  =@seno '" + oRCM.SQLInjectionReplaceAll(dv[0]["tr_seno"].ToString()) + "' )";
                    //SDS_emp.DataBind();
                    //System.Data.DataView dv_pno = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

                    #region --- query ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @"select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  tr_promoter_no_other   FROM treaty_requisition  where tr_seno  = @seno)";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(dv[0]["tr_seno"].ToString()));
                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                            sqlDA.Fill(dt);


                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv_pno = dt.DefaultView;
                    if (dv_pno.Count >= 1)
                    {
                        LB_adm.Text = Server.HtmlEncode(dv_pno[0]["cname"].ToString().Trim());
                    }

                    if (dv[0]["tc_org_adm"].ToString() == "1")
                    {
                        //SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = '" + oRCM.SQLInjectionReplaceAll(str_tr_orgcd) + "' )";
                        //SDS_emp.DataBind();
                        //System.Data.DataView dv_px = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

                        #region --- query ---
                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.Text;

                            sqlCmd.CommandText = @"select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =@org)";

                            // --- 避免匯出查詢過久而當掉 --- //
                            sqlCmd.CommandTimeout = 0;

                            sqlCmd.Parameters.Clear();
                            sqlCmd.Parameters.AddWithValue("@org", oRCM.SQLInjectionReplaceAll(str_tr_orgcd));
                            try
                            {
                                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                                sqlDA.Fill(dt);


                            }
                            catch (Exception ex)
                            {
                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );

                                oRCM.ErrorExceptionDataToDB(logMail);

                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }

                        #endregion
                        DataView dv_px = dt.DefaultView;
                        if (dv_px.Count >= 1)
                        {
                            if (dv[0]["tc_org_adm"].ToString().Trim() != "")
                            {
                                PH_rb_adm.Visible = true;
                                LB_adm_text.Text = Server.HtmlEncode(dv_px[0]["cname"].ToString().Trim());
                            }
                        }
                    }
                    #endregion
                    break;
                case "F":
                    #region F
                    rb_conttype_ui.Checked = true;
                    #endregion
                    break;
            }

            if (dv[0]["tc_技術授權"].ToString().Trim() == "1")
            {
                CB_技術授權.Checked = true;
            }
            if (dv[0]["tc_專利授權"].ToString().Trim() == "1")
            {
                CB_專利授權.Checked = true;
            }
            if (dv[0]["tc_技術與專利授權"].ToString().Trim() == "1")
            {
                CB_技術與專利授權.Checked = true;
            }
            if (dv[0]["tc_全球"].ToString().Trim() == "1")
            {
                CB_全球.Checked = true;
            }
            if (dv[0]["tc_陸港澳"].ToString().Trim() == "1")
            {
                CB_陸港澳.Checked = true;
            }
            if (dv[0]["tc_特定區域"].ToString().Trim() == "1")
            {
                CB_特定區域.Checked = true;
            }
            if (dv[0]["tc_韓國"].ToString().Trim() == "1")
            {
                CB_韓國.Checked = true;
            }
            if (dv[0]["tc_技術讓與"].ToString().Trim() == "1")
            {
                CB_技術讓與.Checked = true;
            }

            if (dv[0]["tc_case_flag"].ToString().Trim() == "1")
                CB_常用版本.Checked = true;

            BindContType(dv[0]["tc_conttype"].ToString().Trim());
            #endregion


            #region 契約期間
            txt_contsdate.Text = dv[0]["tc_contsdate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_contsdate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            txt_contedate.Text = dv[0]["tc_contedate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_contedate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            #endregion
            Bind_sRC(str_tr_class, dv[0]["tc_sRC_ver"].ToString().Trim());
            ViewState["ver"] = dv[0]["tc_sRC_ver"].ToString().Trim();

            #region 其他需求
            ViewState["tc_oRC_ver"] = 2;// dv[0]["tr_otherrequire_ver"].ToString().Trim();
            Bind_oRC(ViewState["seno"].ToString(), ViewState["tc_oRC_ver"].ToString().Trim());
            #endregion
            txt_betsum.Text = dv[0]["tc_betsum"].ToString().Trim();//    Server.HtmlEncode(dv[0]["tc_betsum"].ToString().Trim());
            lb_assign_name.Text = Server.HtmlEncode(dv[0]["tc_assign_name"].ToString());//分案主管
            lb_assign_date.Text = Server.HtmlEncode(dv[0]["tc_assign_date"].ToString().Length > 0 ? DateTime.ParseExact(dv[0]["tc_assign_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");  //分案日期
            lb_handle_name.Text = Server.HtmlEncode(dv[0]["tc_handle_name"].ToString());//法務承辦人姓名
            lb_handle_empno.Text = Server.HtmlEncode(dv[0]["tc_handle_empno"].ToString());//法務承辦人工號
            lb_handle_ext.Text = Server.HtmlEncode(dv[0]["tc_handle_ext"].ToString());//法務承辦人分機
            lb_expect_close_date.Text = Server.HtmlEncode(dv[0]["tc_prefinish_date"].ToString().Length > 0 ? DateTime.ParseExact(dv[0]["tc_prefinish_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");//預估完成日
            lb_process_date.Text = Server.HtmlEncode(dv[0]["tc_process_date"].ToString());//處理天數
            if (dv[0]["tc_degree"].ToString().Trim() != "Z")//如果狀態不是結件，不秀
            {
                if (lb_process_date.Text.Trim() == "0")
                    lb_process_date.Text = "";
                if (lb_contract_count.Text.Trim() == "0")
                    lb_contract_count.Text = "";
            }
            lb_contract_count.Text = Server.HtmlEncode(dv[0]["tc_contract_count"].ToString()); //產出契約數
            if (!((dv[0]["tc_degree"].ToString().Trim() == "Z") || (dv[0]["tc_degree"].ToString().Trim() == "C")))
            {
                if (str_tr_ver + str_tr_seqsn == "A01")
                    bt_reject.Visible = true;

            }
            if ((ViewState["auth"].ToString() == "W" || ViewState["auth"].ToString() == "A") && (dv[0]["tc_degree"].ToString().Trim() == "Z"))
                BT_FileUp.Visible = true;

            if (dv[0]["tc_inspect"].ToString() == "1")
            { //不須審查
                CB_NotInspect.Checked = true;
            }
            else
            {
                CB_NotInspect.Checked = false;
                BT_SendInspect.Visible = true;
            }
            if ((dv[0]["tc_degree"].ToString().Trim() == "0")) //案件承辦中
            {

                BT_AddInspect.Visible = true;
            }
            Bind_cop(ViewState["seno"].ToString());
            txtManageNote.Text = Server.HtmlEncode(dv[0]["tc_manage_note"].ToString().Trim());
            lb_case_closedate.Text = Server.HtmlEncode(dv[0]["tc_case_closedate"].ToString().Trim());//需求結件日期
            //SDS_DDL_degree.SelectCommandType = SqlDataSourceCommandType.Text;
            //SDS_DDL_degree.SelectCommand = "exec esp_treatyCase_codetable '','08'  ";
            //SDS_DDL_degree.DataBind();
            //DDL_Degree.DataBind();
            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"exec esp_treatyCase_codetable '','08'  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                    DDL_Degree.DataSource = dt;
                    DDL_Degree.DataBind();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            DDL_Degree.SelectedValue = Server.HtmlEncode(dv[0]["tc_degree"].ToString().Trim());//進度
            LT_L_Degree.Text = Server.HtmlEncode(DDL_Degree.SelectedItem.Text);
            if (dv[0]["tc_send_datetime"].ToString().Trim().Length > 0)//送件日期
            {
                DateTime dTime = DateTime.Parse(dv[0]["tc_send_datetime"].ToString().Trim());
                lb_send_date.Text = dTime.ToString("yyyy/MM/dd");
            }
            lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_name"].ToString());//修改人
            lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_no"].ToString());//修改工號
            lb_modify_date.Text = Server.HtmlEncode(dv[0]["tc_modify_date"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_modify_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : ""); //修改日期
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            if (dv[0]["tc_擬約幫手"].ToString() == "1")
            {
                LT_擬約幫手.Text = "<font color='red'><b>【擬約幫手】</b></font>";
            }
            if (dv[0]["tc_公文編號"].ToString() != "")
            {
                LK_公文文號.Attributes.Add("onclick", "doc_attach('" + Server.HtmlEncode(dv[0]["tc_公文編號"].ToString() + "');"));
                LK_公文文號.Visible = true;
            }
            else
            {
                LK_公文文號.Visible = false;
            }
            if (dv[0]["tc_急件"].ToString().Trim() == "1")
            {
                CB_急件.Checked = true;
                TB_急件原因.Text = dv[0]["tc_急件原因"].ToString();
            }
            if (dv[0]["tc_成果有特殊限制者"].ToString().Trim() == "1")
            {
                CB_成果有特殊限制者.Checked = true;
                TB_成果有特殊限制者_說明.Text = Server.HtmlEncode(dv[0]["tc_成果有特殊限制者_說明"].ToString().Trim());
                TB_成果有特殊限制者_說明.Visible = true;
            }
            else
                TB_成果有特殊限制者_說明.Visible = false;


            //20240624
            if (dv[0]["tc_核心關鍵技術"].ToString().Trim() == "1")
            {
                PL_關鍵技術項目.Visible = true;
                LB_核心關鍵技術_列管迄日.Text = Server.HtmlEncode(dv[0]["tc_核心關鍵技術_列管迄日"].ToString().Trim());
                TB_核心關鍵技術_說明.Text = Server.HtmlEncode(dv[0]["tc_核心關鍵技術_說明"].ToString().Trim());
            }
            else
                PL_關鍵技術項目.Visible = false;

            if (dv[0]["tc_特殊費用負擔"].ToString().Trim() == "1")
            {
                CB_特殊費用負擔.Checked = true;
                TB_特殊費用負擔原因.Text = Server.HtmlEncode(dv[0]["tc_特殊費用負擔原因"].ToString());
                TB_特殊費用負擔原因.Visible = true;
            }
            else
                TB_特殊費用負擔原因.Visible = false;

            LT_計價.Text = Server.HtmlEncode(dv[0]["計價"].ToString().Trim());
            if (dv[0]["計價版件次"].ToString().Trim() != "")
                LT_計價版件次.Text = "計價版件次:" + Server.HtmlEncode(dv[0]["計價版件次"].ToString().Trim());
            DDL_計價.SelectedValue = Server.HtmlEncode(dv[0]["tc_計價"].ToString().Trim());
            ViewState["計價"] = Server.HtmlEncode(dv[0]["tc_計價"].ToString().Trim());
            if (ViewState["計價"].ToString() == "Y" || ViewState["計價"].ToString() == "C")
            {
                DDL_計價.Items.Remove(new ListItem("無", ""));
            }
            if ((cb_conttype_d7.Checked || CB_技術讓與.Checked || CB_技術授權.Checked || CB_專利授權.Checked || CB_技術與專利授權.Checked || CB_特定區域.Checked))
            {
                if (DDL_計價.SelectedValue == "" || DDL_計價.SelectedValue == "C")
                {
                    LB_計價提示.Visible = true;
                }
            }

            TB_計價說明.Text = dv[0]["tc_計價說明"].ToString();
            if (ViewState["Module"].ToString().IndexOf("I") >= 0 && dv[0]["tc_計價"].ToString().Trim() == "Y")
                Bind計價();

        }

    }

    private void BindContMoneyType()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    private void BindData_file()
    {
        //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyCase_files";
        //SDS_gv_file.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_gv_file.SelectParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString().Trim()));
        //SDS_gv_file.SelectParameters.Add("mode", SQLInjectionReplace("Edit"));
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_files";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString().Trim()));
            sqlCmd.Parameters.AddWithValue("@mode", "Edit");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        if (cb_conttype_b0.Checked)
            strCondition += "B0,";

        if (cb_conttype_b1.Checked)
            strCondition += "B1,";

        if (cb_conttype_d4.Checked)
            strCondition += "D4,";

        if (cb_conttype_d5.Checked)
            strCondition += "D5,";

        if (cb_conttype_d7.Checked)
            strCondition += "D7,";
        if (cb_conttype_ns.Checked)
            strCondition += "NS,";
        if (cb_conttype_rb.Checked)
            strCondition += "RB,";
        if (cb_conttype_m.Checked)
            strCondition += "ND,";
        if (rb_conttype_uo.Checked || rb_conttype_ui.Checked)
            strCondition += "UN,";
        if (rb_conttype_bd.Checked)
            strCondition += "BD,";
        if (rb_conttype_other.Checked)
            strCondition += "OT,";

        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);
        if (ViewState["tr_class"].ToString() == "C")
            strCondition = "C12";

        if (strCondition == "")
            strCondition = "ZZ";
        #endregion
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '" + strCondition + "' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_codetable";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "10");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
        }
        #endregion
    }
    private void BindInspect()
    {
        //SDS_Inspect.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Inspect.SelectParameters.Clear();
        //SDS_Inspect.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect.SelectCommand = "esp_treatyCase_Inspect";
        //SDS_Inspect.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_Inspect.DataBind();
        //System.Data.DataView dv = (DataView)SDS_Inspect.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treatyCase_Inspect";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
                GV_Inspect.DataSource = dt;
                GV_Inspect.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count == 0)
        {
            BT_End.Visible = true;
            BT_SendInspect.Visible = false;
        }
        GV_Inspect.DataBind();
    }
    private void BindDefer()
    {

        //SDS_Defer.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Defer.SelectParameters.Clear();
        //SDS_Defer.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Defer.SelectCommand = "esp_TreatyCase_Defer";
        //SDS_Defer.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_Defer.DataBind();
        //GV_Defer.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_Defer";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                GV_Defer.DataSource = dt;
                GV_Defer.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind_sRC_init(string str_class)
    {
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_traetyApplyCase_sRC";
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplace(str_class));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplace(ViewState["ver"].ToString()));
        //for (int i = 0; i < SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_traetyApplyCase_sRC";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["ver"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        DataView dv = dt.DefaultView;

        if (dv.Count >= 1)
        {
            Plh_Dynax_sRC.Visible = true;
            ArrayList my報院條件 = new ArrayList();
            ArrayList my報院條件說明 = new ArrayList();
            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif' class='itemhint' title='法務意見勾選「其他」，系統辨識若有「院長核定」字眼時，<br>報院簽核流程最後一關將自動設定陳院長簽核。 <br>（原，系統設定之「其他」是由系統依組織架構分送單位所屬之院長室督導主管核定，若要送院長核定須特別註明。<br>故請法務協助於內部宣導，若須院長核定，請於議約系統上註明「院長核定」，以便系統能夠正確辨識陳送）'  ></td><td colspan='3' class='lineheight03'>";
            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_x.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        my報院條件.Add(CBL_x.ID);
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_y.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        my報院條件.Add(CBL_y.ID);
                        TextBox tb = new TextBox();
                        tb.ID = Server.HtmlEncode("TB_" + dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 40;
                        tb.Width = 555;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        my報院條件說明.Add(tb.ID);
                        break;
                }
                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }          

            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
    }
    private void Bind_sRC(string str_class, string str_ver)
    {
        Plh_Dynax_sRC.Controls.Clear();
        ArrayList my報院條件 = new ArrayList();
        ArrayList my報院條件說明 = new ArrayList();
        ArrayList my報院條件s = new ArrayList();
        ArrayList my報院條件說明s = new ArrayList();
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_treatyCase_sRc_modify";
        //SDS_sRC.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplace((ViewState["tr_class"].ToString())));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplace("0"));
        //SDS_sRC.SelectParameters.Add("svalue", SQLInjectionReplace("0"));
        //SDS_sRC.SelectParameters.Add("sdoc", SQLInjectionReplace("0"));
        //SDS_sRC.SelectParameters.Add("stype", SQLInjectionReplace("List"));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_sRc_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll((ViewState["tr_class"].ToString())));
            sqlCmd.Parameters.AddWithValue("@ver", "0");
            sqlCmd.Parameters.AddWithValue("@svalue", "0");
            sqlCmd.Parameters.AddWithValue("@sdoc", "0");
            sqlCmd.Parameters.AddWithValue("@stype", "List");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR != null && dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                my報院條件s.Add(dvR[i]["tcsRC_val"].ToString());
                if (dvR[i]["tcsRC_val"].ToString().IndexOf("T") != -1)
                    my報院條件說明s.Add(dvR[i]["tcsRC_val"].ToString() + "©" + dvR[i]["tcsRC_desc"].ToString());
            }
        }
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_traetyApplyCase_sRC";
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplace(str_class));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplace(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_traetyApplyCase_sRC";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {

            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif'  class='itemhint'></td><td colspan='3' class='lineheight03'>";

            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_x.Attributes["value"] = Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        foreach (string obj in my報院條件s)
                        {
                            //if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                            //    CBL_x.Checked = true;

                            if (obj == dv[sRC_count]["tcs_code"].ToString())
                                CBL_x.Checked = true;
                        }
                        my報院條件.Add(CBL_x.ID);
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_y.Attributes["value"] = Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        my報院條件.Add(CBL_y.ID);
                        foreach (string obj in my報院條件s)
                        {
                            if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                                CBL_y.Checked = true;
                        }
                        TextBox tb = new TextBox();
                        tb.ID = Server.HtmlEncode("TB_" + dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 40;
                        tb.Width = 555;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        my報院條件說明.Add(tb.ID);
                        foreach (string str_obj in my報院條件說明s)
                        {
                            if (str_obj.Split('©')[0].ToString() == dv[sRC_count]["tcs_code"].ToString())
                                tb.Text = str_obj.Split('©')[1].ToString();
                        }
                        Literal LB_hint = new Literal();
                        LB_hint.Text = "<br/>" + Server.HtmlDecode(Server.HtmlEncode(Bind報院提示()));// "<br>註:💥法務意見勾選<font color='blue'><b>「其他」</b></font>，系統辨識若有<font color='red'><b>「院長核定」</b></font>字眼時，<u>報院簽核流程最後一關將自動設定陳院長簽核</u>。 <br>（原，系統設定之「其他」是由系統依組織架構分送單位所屬之院長室督導主管核定，若要送院長核定須特別註明。<br>　故請法務協助於內部宣導，<b>若須院長核定，請於議約系統上註明👉「院長核定」👈</b>，以便系統能夠正確辨識陳送）";

                        Plh_Dynax_sRC.Controls.Add(LB_hint);
                        break;
                }

                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }

            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
    }
    private void Bind_oRC(string str_seno, string str_ver)
    {
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.SelectParameters.Clear();
        //SDS_oRC.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.SelectCommand = " select * from treaty_case_oRC where tc_seno=@seno and tcoRC_ver=@ver";
        //SDS_oRC.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_oRC.SelectParameters.Add("ver", SQLInjectionReplace(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_oRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_oRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_oRC.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * from treaty_case_oRC where tc_seno = @seno and tcoRC_ver = @ver";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                switch (dvR[i]["tcoRC_val"].ToString())
                {
                    case "1":
                        rb_other_1.Checked = true;
                        txt_otherrequire_contno.Text = dvR[i]["tcoRC_desc1"].ToString();
                        TB_otherrequire_handle_name.Text = dvR[i]["tcoRC_desc2"].ToString();
                        break;
                    case "2":
                        rb_other_2.Checked = true;
                        txt_otherrequire_asked_name.Text = dvR[i]["tcoRC_desc1"].ToString();
                        break;
                    case "3":
                        rb_other_3.Checked = true;
                        break;
                    case "4":
                        rb_other_4.Checked = true;
                        break;
                    case "T":
                        rb_other_T.Checked = true;
                        txt_otherrequire_desc.Text = dvR[i]["tcoRC_desc1"].ToString();
                        break;
                }
            }
        }
    }
    private void Bind_cop(string str_seno)
    {
        //SDS_cop.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_cop.SelectParameters.Clear();
        //SDS_cop.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_cop.SelectCommand = "esp_TreatyCase_cop";
        //SDS_cop.SelectParameters.Add("seno", ViewState["seno"].ToString());
        //for (int i = 0; i < this.SDS_cop.SelectParameters.Count; i++)
        //{
        //    SDS_cop.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_cop.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_cop.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_cop";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            lb_cop.Text = Server.HtmlEncode(dvR[0][0].ToString());
        }
        else
        {
            lb_cop.Text = "";
        }

    }

    private void Bind計價()
    {
        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandText = "esp_TreatyCase_valuation";
        //oCmd_1.CommandType = CommandType.StoredProcedure;
        //oCmd_1.Parameters.AddWithValue("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
        //oCmd_1.Parameters.AddWithValue("版本", SQLInjectionReplaceAll("0"));
        //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("view"));
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        //DataSet ds_1 = new DataSet();
        //oda_1.Fill(ds_1, "myTable");
        //DataView dv = ds_1.Tables[0].DefaultView;

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            sqlCmd.Parameters.AddWithValue("@版本", "0");
            sqlCmd.Parameters.AddWithValue("@mode", "view");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            ViewState["計價進度"] = Server.HtmlEncode(dv[0]["進度"].ToString().Trim());
            LB_承辦人.Text = Server.HtmlEncode(dv[0]["承辦人"].ToString().Trim());
            DDL_計價進度.SelectedValue = Server.HtmlEncode(dv[0]["進度"].ToString().Trim());
            TB_參考價.Text = Server.HtmlEncode(dv[0]["參考價"].ToString().Trim());
            DDL_盡職調查結果.SelectedValue = Server.HtmlEncode(dv[0]["盡職調查結果"].ToString().Trim());
            TB_底價.Text = Server.HtmlEncode(dv[0]["底價"].ToString().Trim());
            if (dv[0]["底價_無"].ToString().Trim() == "1")
            {
                CB_底價_無.Checked = true;
                TB_底價_無_說明.Text = Server.HtmlEncode(dv[0]["底價_無_說明"].ToString().Trim());
                TB_底價_無_說明.Visible = true;
            }
            else
            {
                CB_底價_無.Checked = false;
                TB_底價_無_說明.Text = "";
                TB_底價_無_說明.Visible = false;
            }

            TB_第三方鑑價.Text = Server.HtmlEncode(dv[0]["第三方鑑價"].ToString().Trim());
            if (dv[0]["第三方鑑價_無"].ToString().Trim() == "1")
            {
                CB_第三方鑑價_無.Checked = true;
                TB_第三方鑑價_無_說明.Text = Server.HtmlEncode(dv[0]["第三方鑑價_無_說明"].ToString().Trim());
                TB_第三方鑑價_無_說明.Visible = true;
            }
            else
            {
                CB_第三方鑑價_無.Checked = false;
                TB_第三方鑑價_無_說明.Text = "";
                TB_第三方鑑價_無_說明.Visible = false;
            }
            TB_其他說明.Text = Server.HtmlEncode(dv[0]["其他說明"].ToString().Trim());
            //LT_審查.Text = dv[0]["審查資訊"].ToString().Trim();
            if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0 || ViewState["SYS"].ToString().IndexOf("HGroup") >= 0)
            {
                if (ViewState["Module"].ToString().IndexOf("I") >= 0 && (dv[0]["進度"].ToString().Trim() == "Z"))
                {
                    PL_Inspect_計價.Visible = true;
                    if (ViewState["Module"].ToString() == "I") //法務人員不能維護計價資料 & 計價完畢才檢視
                    {
                    }
                }

            }
            if ((dv[0]["進度"].ToString().Trim() == "Z") || (dv[0]["進度"].ToString().Trim() == "C"))
            {
            }
            if (dv[0]["版本"].ToString().Trim() == "1")
            {
                PH_計價歷程.Visible = false;
            }
            else
            {
                LT_計價歷程.Text = "<a class='ajax_vmesg'  href='javascript:Valuation_history(" + Server.HtmlEncode(ViewState["seno"].ToString()) + ")'>計價歷次紀錄</a>";
                PH_計價歷程.Visible = true;
            }
        }
        BindInspect_value();
        BindData_vfile();
    }
    private void BindInspect_value()
    {
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  SQLInjectionReplaceAll(
        //SDS_Inspect_value.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Inspect_value.SelectParameters.Clear();
        //SDS_Inspect_value.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect_value.SelectCommand = "esp_TreatyCase_valuation";
        //SDS_Inspect_value.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_Inspect_value.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
        //SDS_Inspect_value.SelectParameters.Add("mode", SQLInjectionReplaceAll("list_inspect"));
        //ConvertSqlParametersEmptyStringToNull(SDS_Inspect_value, false, "Select");
        //SDS_Inspect_value.DataBind();
        GV_Inspect_value.DataBind();

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "list_inspect");



            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                GV_Inspect_value.DataSource = dt;
                GV_Inspect_value.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void BindData_vfile()
    {
        //SDS_vgv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_vgv_file.SelectParameters.Clear();
        //SDS_vgv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_vgv_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_valuation");
        //SDS_vgv_file.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_vgv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString().Trim()));
        //SDS_vgv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("file_view"));
        //ConvertSqlParametersEmptyStringToNull(SDS_vgv_file, false, "Select");
        //SDS_vgv_file.DataBind();
        //gv_vdoc_file.DataBind();

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_valuation";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");



            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                gv_vdoc_file.DataSource = dt;
                gv_vdoc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull, string mode)
    {
        if (mode == "Select")
        {
            foreach (Parameter parameter in dataSource.SelectParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Insert")
        {
            foreach (Parameter parameter in dataSource.InsertParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Updat")
        {
            foreach (Parameter parameter in dataSource.UpdateParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Delete")
        {
            foreach (Parameter parameter in dataSource.DeleteParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
    }
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace(e.CommandArgument.ToString(), "");
            BindData_Customer();
        }

    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + LB.Text.ToString() + "\");' >" + LB.Text.ToString() + "</a>";

    }
    protected void IBx_廠商編號_Click(object sender, EventArgs e)
    {
       ImageButton CompInfo = sender as ImageButton;
        string Compno = CompInfo.Attributes["Compno"].Trim();
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);

    }
    protected void LB_廠商編號_Click(object sender, EventArgs e)
    {
        LinkButton CompInfo = sender as LinkButton;
        string Compno = CompInfo.Attributes["Compno"].Trim();
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfo('{0}'); </script> ", Compno), false);

    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //ViewState["Customers"] = Convert.ToString(ViewState["Customers"]).Trim().Replace("," + e.CommandArgument, "");
            //BindData();
            string str_file_url = "";
            string str_filename = "";
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyCase_file_modify";
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplace("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplace(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            //FileInfo fi = new FileInfo(str_file_url);
            //if (fi.Exists)
            //{
            //    fi.Delete();
            Treaty_log(ViewState["seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase_modify.aspx");
            //SDS_gv_file.DeleteParameters.Clear();
            //SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.DeleteCommand = "esp_TreatyCase_file_modify";
            //SDS_gv_file.DeleteParameters.Add("req_id", SQLInjectionReplace(""));
            //SDS_gv_file.DeleteParameters.Add("fd_name", SQLInjectionReplace(""));
            //SDS_gv_file.DeleteParameters.Add("filetxt", SQLInjectionReplace(""));
            //SDS_gv_file.DeleteParameters.Add("file_url", SQLInjectionReplace(""));
            //SDS_gv_file.DeleteParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
            //SDS_gv_file.DeleteParameters.Add("mode", SQLInjectionReplace("Del"));
            //SDS_gv_file.DeleteParameters.Add("fid", SQLInjectionReplace(e.CommandArgument.ToString()));
            //SDS_gv_file.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "Del");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            //}
            BindData_file();
        }
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyCase_file_modify";
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplace(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplace("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplace(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());
            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_filename, ViewState["xIP"].ToString(), "treaty\\TreatyCase_modify.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            Label lb_tcdf_type = (Label)e.Row.FindControl("LB_tcdf_type");
            Label lb_inspect = (Label)e.Row.FindControl("LB_inspect");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");
                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
                if (lb_tcdf_type.Text == "RE")
                {
                    lb_del.Visible = false;
                    lb_edit.Visible = false;
                }
                if (lb_inspect.Text == "0")
                    lb_inspect.Text = "";
                else
                    lb_inspect.Text = "V";

            }
        }
    }
    protected void GV_Inspect_value_RowDataBound(object sender, GridViewRowEventArgs e)
    {


    }

    protected void GV_Inspect_value_RowCommand(object sender, GridViewCommandEventArgs e)
    {

    }

    protected void gv_vdoc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";

            //SqlCommand oCmd_1 = new SqlCommand();
            //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            //oCmd_1.CommandText = "esp_TreatyCase_valuation";
            //oCmd_1.CommandType = CommandType.StoredProcedure;
            //oCmd_1.Parameters.AddWithValue("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            //oCmd_1.Parameters.AddWithValue("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("file_Download"));
            //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            //DataSet ds_1 = new DataSet();
            //oda_1.Fill(ds_1, "myTable");
            //DataView dv = ds_1.Tables[0].DefaultView;

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_valuation";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "file_Download");


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
                if (str_file_url != "")
                {
                    Response.Clear();
                    Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                    Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                    Response.Flush();
                    Response.End();
                }
            }
        }
    }

    protected void gv_vdoc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void btnDelete_Click(object sender, EventArgs e)
    {
        //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_auth.DeleteParameters.Clear();
        //SDS_auth.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_auth.DeleteCommand = "esp_treaty_TreatyEmpView_Delete";
        //SDS_auth.DeleteParameters.Add("caseno", SQLInjectionReplace(txtComplexNo.Text.Replace("-", "")));
        //SDS_auth.Delete();
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TreatyEmpView_Delete";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@caseno", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Replace("-", "")));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> DeleteCase();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

    }
    protected void GV_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_Istatus");
        if (LB != null)
            switch (LB.Text.Trim())
            {
                case "0":
                    LB.Text = "";
                    BT_SendInspect.Visible = true;
                    BT_End.Visible = false;
                    break;
                case "1":
                    LB.Text = "同意";
                    break;
                case "2":
                    LB.Text = "退回";
                    break;
            }
        LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
        if (lb_del != null)
        {
            //if(CB_NotInspect.Checked ==false)
            //{
            //    Label lb_order = (Label)e.Row.FindControl("LB_order");
            //    if ((lb_order.Text == "1") || (lb_order.Text == "2"))
            //   lb_del.Visible = false;
            //if(LB.Text!="")
            //    lb_del.Visible = false;
            //}
        }
    }
    protected void bt_reject_Click(object sender, EventArgs e)
    {
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommand = "exec esp_TreatyCase_reject_mail @seno ";
        //SDS_NR.UpdateParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_reject_mail";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        Treaty_log(ViewState["seno"].ToString(), "退件", "", ViewState["seno"].ToString(), ViewState["empNo"].ToString());


        Response.Redirect("./TreatyApply_view.aspx?contno=" + txtComplexNo.Text.Replace("-", ""));
    }
    protected void bt_cancle_Click(object sender, EventArgs e)
    {
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
        //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplace("C"));
        //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplace("Z"));
        //SDS_NR.UpdateParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
        //SDS_NR.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_status_update";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tc_degree", "C");
            sqlCmd.Parameters.AddWithValue("@tc_status", "Z");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        Treaty_log(ViewState["seno"].ToString(), "需求取消", "", ViewState["seno"].ToString(), ViewState["empNo"].ToString());
        Response.Redirect("./TreatyCase_view.aspx?seno=" + ViewState["seno"].ToString());

    }
    protected void GV_Inspect_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //this.SDS_Inspect.DeleteParameters.Clear();
            //this.SDS_Inspect.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //this.SDS_Inspect.DeleteCommand = " delete treaty_case_inspect where tci_no=@tci_no";
            //this.SDS_Inspect.DeleteParameters.Add("tci_no", SQLInjectionReplace(e.CommandArgument.ToString()));
            //this.SDS_Inspect.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case_inspect where tci_no=@tci_no";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tci_no", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindInspect();
        }
    }
    protected void BT_SendInspect_Click(object sender, EventArgs e)
    {

        //BT_save_Click(sender, e);
        string str_error = "";
        string str_danger = "0";
        if (txt_promoter_empno.Value == "")

        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))

        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (ViewState["tr_class"].ToString() == "N")
        {
            if (!(cb_conttype_b0.Checked || cb_conttype_b1.Checked || cb_conttype_d4.Checked || cb_conttype_d5.Checked || cb_conttype_d7.Checked || cb_conttype_ns.Checked))
            {
                string script_alert = "<script language='javascript'> $('#cb_conttype_b0').validationEngine('showPrompt', '★案件性質 必須挑選','','',true); $('#cb_conttype_b0').click(function () { $('#cb_conttype_b0').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "cb_conttype_b0", script_alert);

            }
        }
        if (txt_name.Text == "請輸入契約名稱")
        {

            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (h_compno.Value == "")
        {

            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#h_compno').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#h_compno').click(function () { $('#h_compno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);
        }
        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if ((txt_contsdate.Text != "") && (txt_contedate.Text != ""))
        {
            if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                {
                    str_error += "★契約期間異常 (起日 > 訖日)  ";
                    string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 起日 < 訖日 ','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                    txt_contedate.Text = "";
                }
            }
            else
            {
                string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 格式錯誤','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                txt_contedate.Text = "";
                txt_contsdate.Text = "";
            }
        }
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
        {
            str_danger = "1";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '請挑選契約性質!','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";
        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "" || 匯率 == "1.00000"))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        else
        {
            if (txtContMoney.Text.Trim() == "")
                txtContMoney.Text = "0";
        }
        //if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        //if (txtManageNote.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (CB_成果有特殊限制者.Checked && (TB_成果有特殊限制者_說明.Text == ""))
        {
            str_danger = "1";
            string script_alert = "<script language='javascript'> $('#TB_成果有特殊限制者_說明').validationEngine('showPrompt', '請填寫說明!','','',true); $('#TB_成果有特殊限制者_說明').click(function () { $('#TB_成果有特殊限制者_說明').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_成果有特殊限制者_說明", script_alert);
        }
        if (str_error != "")
        {
            string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");
            DoSaveDraft();
            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
            //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplace("4"));
            //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplace("C"));
            //SDS_NR.UpdateParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
            //SDS_NR.Update();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_status_update";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tc_degree", "4");
                sqlCmd.Parameters.AddWithValue("@tc_status", "C");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> SendInspect(" + ViewState["seno"].ToString() + ");</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }
    protected void BT_End_Click(object sender, EventArgs e)
    {

        string str_error = "";
        string str_danger = "0";
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (ViewState["tr_class"].ToString() == "N")
        {
            if (!(cb_conttype_b0.Checked || cb_conttype_b1.Checked || cb_conttype_d4.Checked || cb_conttype_d5.Checked || cb_conttype_d7.Checked || cb_conttype_ns.Checked))
            {
                string script_alert = "<script language='javascript'> $('#cb_conttype_b0').validationEngine('showPrompt', '★案件性質 必須挑選','','',true); $('#cb_conttype_b0').click(function () { $('#cb_conttype_b0').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "cb_conttype_b0", script_alert);

            }
        }
        if (txt_name.Text == "請輸入契約名稱")
        {
            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#h_compno').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#h_compno').click(function () { $('#h_compno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);
        }
        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if ((txt_contsdate.Text != "") && (txt_contedate.Text != ""))
        {
            if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                {
                    str_error += "★契約期間異常 (起日 > 訖日)  ";
                    string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 起日 < 訖日 ','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                    txt_contedate.Text = "";
                }
            }
            else
            {
                string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 格式錯誤','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                txt_contedate.Text = "";
                txt_contsdate.Text = "";
            }
        }
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
        {
            str_danger = "1";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '請挑選契約性質!','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";

        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if ((txtContMoney.Text.Trim().Length > 12) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        else
        {
            if (txtContMoney.Text.Trim() == "")
                txtContMoney.Text = "0";
        }
        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }
        if (CB_成果有特殊限制者.Checked && (TB_成果有特殊限制者_說明.Text == ""))
        {
            str_error += "★成果有特殊限制者_說明必填! \\n ";
            string script_alert = "<script language='javascript'> $('#TB_成果有特殊限制者_說明').validationEngine('showPrompt', '請填寫說明!','','',true); $('#TB_成果有特殊限制者_說明').click(function () { $('#TB_成果有特殊限制者_說明').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_成果有特殊限制者_說明", script_alert);
        }

        if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if (txtManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");

            DoSaveDraft();

            //SDS_Inspect_count.SelectParameters.Clear();
            //SDS_Inspect_count.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_Inspect_count.SelectCommand = "esp_treatyCase_Inspect_getUnInspect";
            //SDS_Inspect_count.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //for (int i = 0; i < this.SDS_Inspect_count.SelectParameters.Count; i++)
            //{
            //    SDS_Inspect_count.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_Inspect_count.DataBind();
            //System.Data.DataView dv_count = (DataView)SDS_Inspect_count.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_count = dt.DefaultView;
            if (dv_count.Count >= 1)
            {
                if (dv_count[0][0].ToString() != "0")
                {
                    StringBuilder script = new StringBuilder("<script type='text/javascript'> 還有審查人未審查!;</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                }
                else
                {
                    //SDS_NR.UpdateParameters.Clear();
                    //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                    //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
                    //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                    //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplace("Z"));
                    //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplace("Z"));
                    //SDS_NR.UpdateParameters.Add("empno", SQLInjectionReplace(ViewState["empNo"].ToString()));
                    //SDS_NR.Update();

                    #region --- modify ---

                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;

                        sqlCmd.CommandText = @"esp_TreatyCase_status_update";

                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        sqlCmd.Parameters.AddWithValue("@tc_degree", "Z");
                        sqlCmd.Parameters.AddWithValue("@tc_status", "Z");
                        sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));

                        try
                        {
                            sqlConn.Open();
                            sqlCmd.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {

                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion

                    Treaty_log(ViewState["seno"].ToString(), "結案", "", "", "treaty\\TreatyCase_modify.aspx");
                    StringBuilder script = new StringBuilder("<script type='text/javascript'> EndCase(" + ViewState["seno"].ToString() + ");</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                }
            }
        }
    }
    protected void BT_save_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";

        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (ViewState["tr_class"].ToString() == "N")
        {
            if (!(cb_conttype_b0.Checked || cb_conttype_b1.Checked || cb_conttype_d4.Checked || cb_conttype_d5.Checked || cb_conttype_d7.Checked || cb_conttype_ns.Checked))
            {
                string script_alert = "<script language='javascript'> $('#cb_conttype_b0').validationEngine('showPrompt', '★案件性質 必須挑選','','',true); $('#cb_conttype_b0').click(function () { $('#cb_conttype_b0').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "cb_conttype_b0", script_alert);
            }
        }
        if (txt_name.Text == "請輸入契約名稱")
        {
            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);

        }
        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if ((txt_contsdate.Text != "") && (txt_contedate.Text != ""))
        {
            if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                {
                    str_error += "★契約期間異常 (起日 > 訖日)  ";
                    string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 起日 < 訖日 ','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                    txt_contedate.Text = "";
                }
            }
            else
            {
                str_error += "★契約期間 格式錯誤  ";
                string script_alert = "<script language='javascript'> $('#txt_contedate').validationEngine('showPrompt', '★契約期間 格式錯誤','','',true); $('#txt_contedate').click(function () { $('#txt_contedate').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "txt_contedate", script_alert);
                txt_contedate.Text = "";
                txt_contsdate.Text = "";
            }
        }
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
        {
            str_danger = "1";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '請挑選契約性質!','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }

        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";

        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }
        if (CB_成果有特殊限制者.Checked && (TB_成果有特殊限制者_說明.Text == ""))
        {
            str_error += "★成果有特殊限制者_說明必填! \\n ";
            string script_alert = "<script language='javascript'> $('#TB_成果有特殊限制者_說明').validationEngine('showPrompt', '請填寫說明!','','',true); $('#TB_成果有特殊限制者_說明').click(function () { $('#TB_成果有特殊限制者_說明').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_成果有特殊限制者_說明", script_alert);
        }
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        else
        {
            if (txtContMoney.Text.Trim() == "")
                txtContMoney.Text = "0";
        }

        // if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if (txtManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if (DDL_計價.SelectedValue == "C" && TB_計價說明.Text.Trim() == "")
        {
            str_error = "1";
            string script_alert = "<script language='javascript'> $('#TB_計價說明').validationEngine('showPrompt', '★請填上取消說明)!','','',true); $('#TB_計價說明').click(function () { $('#TB_計價說明').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_計價說明", script_alert);
        }
        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");
            DoSaveDraft();
            string script = "<script language='javascript'>alert('存檔成功！');location.href='./TreatyCase_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    public static string RemoveHTMLTag(string htmlSource)
    {
        //移除  javascript code.
        htmlSource = Regex.Replace(htmlSource, @"<script[\d\D]*?>[\d\D]*?</script>", String.Empty);

        //移除html tag.
        htmlSource = Regex.Replace(htmlSource, @"<[^>]*>", String.Empty);
        htmlSource = htmlSource.Replace("&nbsp;", " ");
        return htmlSource;
    }
    private void DoSaveDraft()
    {
        string str_error = "";

        if (DDL_計價.SelectedValue == "C" && TB_計價說明.Text.Trim() == "")
        {
            str_error = "1";
            string script = "<script language='javascript'>alert('計價需求取消，需填寫原因!');</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
        if (str_error == "")
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_NR.UpdateCommand = "esp_TreatyCase_update";
            //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_NR.UpdateParameters.Add("tc_old_contno", SQLInjectionReplace(txtOldContno.Text.Trim())); //將舊案的流水號存入
            //SDS_NR.UpdateParameters.Add("tc_name", SQLInjectionReplace(txt_name.Text.Trim()));//洽案(契約)名稱
            //SDS_NR.UpdateParameters.Add("tc_compidno", SQLInjectionReplace(""));
            //SDS_NR.UpdateParameters.Add("tc_compname", SQLInjectionReplace(""));
            //SDS_NR.UpdateParameters.Add("tc_compidno_all", SQLInjectionReplace(h_compno.Value));
            //SDS_NR.UpdateParameters.Add("tc_compname_all", SQLInjectionReplace(""));
            ////if (rb_language_other.Checked)
            ////    SDS_NR.UpdateParameters.Add("tc_language", "0");//契約語文-其他
            //if (rb_language_chiness.Checked)
            //    SDS_NR.UpdateParameters.Add("tc_language", SQLInjectionReplace("1"));//契約語文-中文
            //if (rb_language_english.Checked)
            //    SDS_NR.UpdateParameters.Add("tc_language", SQLInjectionReplace("2"));//契約語文-英文
            //#region 案件性質
            //SDS_NR.UpdateParameters.Add("tc_conttype_b0", SQLInjectionReplace(cb_conttype_b0.Checked ? "1" : "0"));//技術服務
            //SDS_NR.UpdateParameters.Add("tc_conttype_b1", SQLInjectionReplace(cb_conttype_b1.Checked ? "1" : "0"));//合作開發
            //SDS_NR.UpdateParameters.Add("tc_conttype_d4", SQLInjectionReplace(cb_conttype_d4.Checked ? "1" : "0"));//技術授權
            //SDS_NR.UpdateParameters.Add("tc_conttype_d5", SQLInjectionReplace(cb_conttype_d5.Checked ? "1" : "0"));//專利授權
            //SDS_NR.UpdateParameters.Add("tc_conttype_d7", SQLInjectionReplace(cb_conttype_d7.Checked ? "1" : "0"));//專利讓與
            //SDS_NR.UpdateParameters.Add("tc_conttype_ns", SQLInjectionReplace(cb_conttype_ns.Checked ? "1" : "0"));//新創事業(洽案)
            //SDS_NR.UpdateParameters.Add("tc_conttype_rb", SQLInjectionReplace(cb_conttype_rb.Checked ? "1" : "0"));//標案 
            //SDS_NR.UpdateParameters.Add("tc_conttype_uo", SQLInjectionReplace(rb_conttype_uo.Checked ? "1" : "0"));//國外支出(無收入) 
            //SDS_NR.UpdateParameters.Add("tc_conttype_ui", SQLInjectionReplace(rb_conttype_ui.Checked ? "1" : "0"));//國外支出(無收入) 
            //SDS_NR.UpdateParameters.Add("tc_class_other_desc", SQLInjectionReplace("")); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
            //#endregion
            //#region 契約修訂
            //if (spanContractEdit.Visible)
            //{//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
            //    SDS_NR.UpdateParameters.Add("tc_amend", SQLInjectionReplace(rbl_amend.SelectedValue));
            //    SDS_NR.UpdateParameters.Add("tc_amend_other_desc", SQLInjectionReplace(txtamend_other_desc.Text));
            //}
            //else
            //{
            //    SDS_NR.UpdateParameters.Add("tc_amend", SQLInjectionReplace("0"));
            //    SDS_NR.UpdateParameters.Add("tc_amend_other_desc", SQLInjectionReplace(""));
            //}
            //#endregion
            //SDS_NR.UpdateParameters.Add("tc_contsdate", SQLInjectionReplace(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
            //SDS_NR.UpdateParameters.Add("tc_contedate", SQLInjectionReplace(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
            //SDS_NR.UpdateParameters.Add("tc_sign_reason", SQLInjectionReplace(""));//簽約緣由與目的
            //SDS_NR.UpdateParameters.Add("tc_otherrequire_ver", SQLInjectionReplace("2")); //其他需求
            //SDS_NR.UpdateParameters.Add("tc_modify_emp_no", SQLInjectionReplace(ssoUser.empNo));  // 修改工號
            //SDS_NR.UpdateParameters.Add("tc_modify_emp_name", SQLInjectionReplace(ssoUser.empName.Trim()));// 修改人
            //SDS_NR.UpdateParameters.Add("tc_modify_date", SQLInjectionReplace(""));      // 修改日期
            //SDS_NR.UpdateParameters.Add("tc_conttype", SQLInjectionReplace(ddlContType.SelectedValue.ToString().Trim())); //契約性質
            //SDS_NR.UpdateParameters.Add("tc_money_type", SQLInjectionReplace(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
            //if (txtContMoney.Text.Trim() == "")
            //    SDS_NR.UpdateParameters.Add("tc_money", SQLInjectionReplace("0"));
            //else
            //    SDS_NR.UpdateParameters.Add("tc_money", SQLInjectionReplace(txtContMoney.Text.Trim()));
            //SDS_NR.UpdateParameters.Add("tc_money_rate", SQLInjectionReplace(匯率));

            //SDS_NR.UpdateParameters.Add("tc_betsum", SQLInjectionReplace(txt_betsum.Text)); //法務承辦人意見彙整
            ////SDS_NR.UpdateParameters.Add("tc_betsumNoTag", RemoveHTMLTag(txt_betsum.Text));//法務承辦人意見彙整
            //SDS_NR.UpdateParameters.Add("tc_manage_note", SQLInjectionReplace(txtManageNote.Text));//管理者備註欄位
            //SDS_NR.UpdateParameters.Add("tc_case_style", SQLInjectionReplace("1"));//契約類型
            //SDS_NR.UpdateParameters.Add("急件", SQLInjectionReplace(CB_急件.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("急件原因", SQLInjectionReplace(TB_急件原因.Text));
            //SDS_NR.UpdateParameters.Add("成果有特殊限制者", SQLInjectionReplace(CB_成果有特殊限制者.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("成果有特殊限制者_說明", SQLInjectionReplace(TB_成果有特殊限制者_說明.Text));
            //SDS_NR.UpdateParameters.Add("特殊費用負擔", SQLInjectionReplace(CB_特殊費用負擔.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("特殊費用負擔原因", SQLInjectionReplace(TB_特殊費用負擔原因.Text));
            //SDS_NR.UpdateParameters.Add("計價", SQLInjectionReplace(DDL_計價.SelectedValue));
            //SDS_NR.UpdateParameters.Add("計價說明", SQLInjectionReplace(TB_計價說明.Text));

            //for (int i = 0; i < SDS_NR.UpdateParameters.Count; i++)
            //{
            //    SDS_NR.UpdateParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_NR.Update();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_update";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tc_old_contno", oRCM.SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
                sqlCmd.Parameters.AddWithValue("@tc_name", oRCM.SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
                sqlCmd.Parameters.AddWithValue("@tc_compidno", "");
                sqlCmd.Parameters.AddWithValue("@tc_compname", "");
                sqlCmd.Parameters.AddWithValue("@tc_compidno_all", oRCM.SQLInjectionReplaceAll(h_compno.Value));
                sqlCmd.Parameters.AddWithValue("@tc_compname_all", "");
                //if (rb_language_other.Checked)
                //    sqlCmd.Parameters.AddWithValue("tc_language", "0");//契約語文-其他
                if (rb_language_chiness.Checked)
                    sqlCmd.Parameters.AddWithValue("@tc_language", "1");//契約語文-中文
                if (rb_language_english.Checked)
                    sqlCmd.Parameters.AddWithValue("@tc_language", "2");//契約語文-英文
                #region 案件性質
                sqlCmd.Parameters.AddWithValue("@tc_conttype_b0", oRCM.SQLInjectionReplaceAll(cb_conttype_b0.Checked ? "1" : "0"));//技術服務
                sqlCmd.Parameters.AddWithValue("@tc_conttype_b1", oRCM.SQLInjectionReplaceAll(cb_conttype_b1.Checked ? "1" : "0"));//合作開發
                sqlCmd.Parameters.AddWithValue("@tc_conttype_d4", oRCM.SQLInjectionReplaceAll(cb_conttype_d4.Checked ? "1" : "0"));//技術授權
                sqlCmd.Parameters.AddWithValue("@tc_conttype_d5", oRCM.SQLInjectionReplaceAll(cb_conttype_d5.Checked ? "1" : "0"));//專利授權
                sqlCmd.Parameters.AddWithValue("@tc_conttype_d7", oRCM.SQLInjectionReplaceAll(cb_conttype_d7.Checked ? "1" : "0"));//專利讓與
                sqlCmd.Parameters.AddWithValue("@tc_conttype_ns", oRCM.SQLInjectionReplaceAll(cb_conttype_ns.Checked ? "1" : "0"));//新創事業(洽案)
                sqlCmd.Parameters.AddWithValue("@tc_conttype_rb", oRCM.SQLInjectionReplaceAll(cb_conttype_rb.Checked ? "1" : "0"));//標案 
                sqlCmd.Parameters.AddWithValue("@tc_conttype_uo", oRCM.SQLInjectionReplaceAll(rb_conttype_uo.Checked ? "1" : "0"));//國外支出(無收入) 
                sqlCmd.Parameters.AddWithValue("@tc_conttype_ui", oRCM.SQLInjectionReplaceAll(rb_conttype_ui.Checked ? "1" : "0"));//國外支出(無收入) 
                sqlCmd.Parameters.AddWithValue("@tc_class_other_desc", ""); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
                #endregion
                #region 契約修訂
                if (spanContractEdit.Visible)
                {//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
                    sqlCmd.Parameters.AddWithValue("@tc_amend", oRCM.SQLInjectionReplaceAll(rbl_amend.SelectedValue));
                    sqlCmd.Parameters.AddWithValue("@tc_amend_other_desc", oRCM.SQLInjectionReplaceAll(txtamend_other_desc.Text));
                }
                else
                {
                    sqlCmd.Parameters.AddWithValue("@tc_amend", "0");
                    sqlCmd.Parameters.AddWithValue("@tc_amend_other_desc", "");
                }
                #endregion
                sqlCmd.Parameters.AddWithValue("@tc_contsdate", oRCM.SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
                sqlCmd.Parameters.AddWithValue("@tc_contedate", oRCM.SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
                sqlCmd.Parameters.AddWithValue("@tc_sign_reason", "");//簽約緣由與目的
                sqlCmd.Parameters.AddWithValue("@tc_otherrequire_ver", "2"); //其他需求
                sqlCmd.Parameters.AddWithValue("@tc_modify_emp_no", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));  // 修改工號
                sqlCmd.Parameters.AddWithValue("@tc_modify_emp_name", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));// 修改人
                sqlCmd.Parameters.AddWithValue("@tc_modify_date", "");      // 修改日期
                sqlCmd.Parameters.AddWithValue("@tc_conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
                sqlCmd.Parameters.AddWithValue("@tc_money_type", oRCM.SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
                if (txtContMoney.Text.Trim() == "")
                    sqlCmd.Parameters.AddWithValue("@tc_money", "0");
                else
                    sqlCmd.Parameters.AddWithValue("@tc_money", oRCM.SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@tc_money_rate", oRCM.SQLInjectionReplaceAll(匯率));

                sqlCmd.Parameters.AddWithValue("@tc_betsum", txt_betsum.Text); //法務承辦人意見彙整
                                                                               //sqlCmd.Parameters.AddWithValue("tc_betsumNoTag", RemoveHTMLTag(txt_betsum.Text));//法務承辦人意見彙整
                sqlCmd.Parameters.AddWithValue("@tc_manage_note", oRCM.SQLInjectionReplaceAll(txtManageNote.Text));//管理者備註欄位
                sqlCmd.Parameters.AddWithValue("@tc_case_style", "1");//契約類型
                sqlCmd.Parameters.AddWithValue("@急件", oRCM.SQLInjectionReplaceAll(CB_急件.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@急件原因", oRCM.SQLInjectionReplaceAll(TB_急件原因.Text));
                sqlCmd.Parameters.AddWithValue("@成果有特殊限制者", oRCM.SQLInjectionReplaceAll(CB_成果有特殊限制者.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@成果有特殊限制者_說明", oRCM.SQLInjectionReplaceAll(TB_成果有特殊限制者_說明.Text));
                sqlCmd.Parameters.AddWithValue("@特殊費用負擔", oRCM.SQLInjectionReplaceAll(CB_特殊費用負擔.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@特殊費用負擔原因", oRCM.SQLInjectionReplaceAll(TB_特殊費用負擔原因.Text));
                sqlCmd.Parameters.AddWithValue("@計價", oRCM.SQLInjectionReplaceAll(DDL_計價.SelectedValue));
                sqlCmd.Parameters.AddWithValue("@計價說明", oRCM.SQLInjectionReplaceAll(TB_計價說明.Text));


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion


            //常用版本更新
            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_NR.UpdateCommand = "esp_TreatyCase_other_update";
            //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_NR.UpdateParameters.Add("case_flag", SQLInjectionReplace(IIf(CB_常用版本.Checked, "1", "")));
            //SDS_NR.UpdateParameters.Add("技術授權", SQLInjectionReplace(CB_技術授權.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("專利授權", SQLInjectionReplace(CB_專利授權.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("技術與專利授權", SQLInjectionReplace(CB_技術與專利授權.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("全球", SQLInjectionReplace(CB_全球.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("陸港澳", SQLInjectionReplace(CB_陸港澳.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("特定區域", SQLInjectionReplace(CB_特定區域.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("韓國", SQLInjectionReplace(CB_韓國.Checked == true ? "1" : "0"));
            //SDS_NR.UpdateParameters.Add("技術讓與", SQLInjectionReplace(CB_技術讓與.Checked == true ? "1" : "0"));
            //for (int i = 0; i < SDS_NR.UpdateParameters.Count; i++)
            //{
            //    SDS_NR.UpdateParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_NR.Update();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_other_update";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@case_flag", oRCM.SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
                sqlCmd.Parameters.AddWithValue("@技術授權", oRCM.SQLInjectionReplaceAll(CB_技術授權.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@專利授權", oRCM.SQLInjectionReplaceAll(CB_專利授權.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@技術與專利授權", oRCM.SQLInjectionReplaceAll(CB_技術與專利授權.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@全球", oRCM.SQLInjectionReplaceAll(CB_全球.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@陸港澳", oRCM.SQLInjectionReplaceAll(CB_陸港澳.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@特定區域", oRCM.SQLInjectionReplaceAll(CB_特定區域.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@韓國", oRCM.SQLInjectionReplaceAll(CB_韓國.Checked == true ? "1" : "0"));
                sqlCmd.Parameters.AddWithValue("@技術讓與", oRCM.SQLInjectionReplaceAll(CB_技術讓與.Checked == true ? "1" : "0"));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            #region 其它需求
            //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_oRC.DeleteParameters.Clear();
            //SDS_oRC.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_oRC.DeleteCommand = " delete treaty_case_oRC where tc_seno =@seno";
            //SDS_oRC.DeleteParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_oRC.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case_oRC where tc_seno =@seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            if (rb_other_1.Checked == true)
            {
                doInsert("1");
            }
            if (rb_other_2.Checked == true)
            {
                //SDS_oRC.InsertParameters.Clear();
                //SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                //SDS_oRC.InsertCommand = " insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                //SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                //SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplace(ViewState["tc_oRC_ver"].ToString()));
                //SDS_oRC.InsertParameters.Add("val", SQLInjectionReplace("2"));
                //SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplace(txt_otherrequire_asked_name.Text));
                //SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplace(""));
                //for (int i = 0; i < SDS_oRC.InsertParameters.Count; i++)
                //{
                //    SDS_oRC.InsertParameters[i].ConvertEmptyStringToNull = false;
                //}
                //this.SDS_oRC.Insert();
                #region --- modify ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tc_oRC_ver"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@val", "2");
                    sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_asked_name.Text));
                    sqlCmd.Parameters.AddWithValue("@desc2", "");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
            }
            if (rb_other_3.Checked == true)
            {
                //this.SDS_oRC.InsertParameters.Clear();
                //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                //this.SDS_oRC.InsertCommand = " insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplace(ViewState["tc_oRC_ver"].ToString()));
                //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplace("3"));
                //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplace(""));
                //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplace(""));
                //for (int i = 0; i < SDS_oRC.InsertParameters.Count; i++)
                //{
                //    SDS_oRC.InsertParameters[i].ConvertEmptyStringToNull = false;
                //}
                //this.SDS_oRC.Insert();
                #region --- modify ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)  ";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tc_oRC_ver"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@val", "3");
                    sqlCmd.Parameters.AddWithValue("@desc1", "");
                    sqlCmd.Parameters.AddWithValue("@desc2", "");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
            }
            if (rb_other_4.Checked == true)
            {
                //SDS_oRC.InsertParameters.Clear();
                //SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                //SDS_oRC.InsertCommand = " insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                //SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                //SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplace(ViewState["tc_oRC_ver"].ToString()));
                //SDS_oRC.InsertParameters.Add("val", SQLInjectionReplace("4"));
                //SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplace(""));
                //SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplace(""));
                //for (int i = 0; i < SDS_oRC.InsertParameters.Count; i++)
                //{
                //    SDS_oRC.InsertParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_oRC.Insert();
                #region --- modify ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)  ";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tc_oRC_ver"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@val", "4");
                    sqlCmd.Parameters.AddWithValue("@desc1", "");
                    sqlCmd.Parameters.AddWithValue("@desc2", "");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
            }
            if (rb_other_T.Checked == true)
            {
                //SDS_oRC.InsertParameters.Clear();
                //SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                //SDS_oRC.InsertCommand = " insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                //SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                //SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplace(ViewState["tc_oRC_ver"].ToString()));
                //SDS_oRC.InsertParameters.Add("val", SQLInjectionReplace("T"));
                //SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplace(txt_otherrequire_desc.Text));
                //SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplace(""));
                //for (int i = 0; i < SDS_oRC.InsertParameters.Count; i++)
                //{
                //    SDS_oRC.InsertParameters[i].ConvertEmptyStringToNull = false;
                //}
                //this.SDS_oRC.Insert();
                #region --- modify ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)  ";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tc_oRC_ver"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@val", "T");
                    sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_desc.Text));
                    sqlCmd.Parameters.AddWithValue("@desc2", "");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
            }
            #endregion
            #region 報院條件
            //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_sRC.DeleteParameters.Clear();
            //SDS_sRC.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_sRC.DeleteCommand = "esp_treatyCase_sRc_modify";
            //SDS_sRC.DeleteParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
            //SDS_sRC.DeleteParameters.Add("class", SQLInjectionReplace(ViewState["tr_class"].ToString()));
            //SDS_sRC.DeleteParameters.Add("ver", SQLInjectionReplace(""));
            //SDS_sRC.DeleteParameters.Add("svalue", SQLInjectionReplace(""));
            //SDS_sRC.DeleteParameters.Add("sdoc", SQLInjectionReplace(""));
            //SDS_sRC.DeleteParameters.Add("stype", SQLInjectionReplace("Del"));
            //for (int i = 0; i < SDS_sRC.DeleteParameters.Count; i++)
            //{
            //    SDS_sRC.DeleteParameters[i].ConvertEmptyStringToNull = false;
            //}

            //SDS_sRC.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treatyCase_sRc_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                sqlCmd.Parameters.AddWithValue("@ver", "");
                sqlCmd.Parameters.AddWithValue("@svalue", "");
                sqlCmd.Parameters.AddWithValue("@sdoc", "");
                sqlCmd.Parameters.AddWithValue("@stype", "Del");

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            string s_value = "";
            ArrayList my報院條件 = new ArrayList();
            ArrayList my報院條件說明 = new ArrayList();
            my報院條件 = (ArrayList)ViewState["my報院條件"];
            my報院條件說明 = (ArrayList)ViewState["my報院條件說明"];
            if (my報院條件 != null)
            {
                foreach (string str_obj in my報院條件)
                {
                    if (str_obj.IndexOf("CBL_") >= 0)
                    {
                        CheckBox cb = Plh_Dynax_sRC.FindControl(str_obj) as CheckBox;
                        if (cb.Checked == true)
                        {
                            s_value = cb.Attributes["Value"];
                        }
                    }
                    if (s_value != "")
                    {
                        //SDS_sRC.InsertParameters.Clear();
                        //SDS_sRC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
                        //SDS_sRC.InsertCommand = "esp_treatyCase_sRc_modify";
                        //SDS_sRC.InsertParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                        //SDS_sRC.InsertParameters.Add("class", SQLInjectionReplace(ViewState["tr_class"].ToString()));
                        //SDS_sRC.InsertParameters.Add("ver", SQLInjectionReplace("0"));
                        //SDS_sRC.InsertParameters.Add("svalue", SQLInjectionReplace(s_value));
                        //SDS_sRC.InsertParameters.Add("sdoc", SQLInjectionReplace(""));
                        //SDS_sRC.InsertParameters.Add("stype", SQLInjectionReplace("Ins"));
                        //for (int i = 0; i < SDS_sRC.InsertParameters.Count; i++)
                        //{
                        //    SDS_sRC.InsertParameters[i].ConvertEmptyStringToNull = false;
                        //}
                        //SDS_sRC.Insert();
                        #region --- insert ---

                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.StoredProcedure;

                            sqlCmd.CommandText = @"esp_treatyCase_sRc_modify";

                            sqlCmd.CommandTimeout = 0;

                            sqlCmd.Parameters.Clear();
                            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                            sqlCmd.Parameters.AddWithValue("@ver", "0");
                            sqlCmd.Parameters.AddWithValue("@svalue", oRCM.SQLInjectionReplaceAll(s_value));
                            sqlCmd.Parameters.AddWithValue("@sdoc", "");
                            sqlCmd.Parameters.AddWithValue("@stype", "Ins");


                            try
                            {
                                sqlConn.Open();

                                sqlCmd.ExecuteNonQuery();
                            }
                            catch (Exception ex)
                            {

                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );

                                oRCM.ErrorExceptionDataToDB(logMail);

                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }

                        #endregion
                        s_value = "";
                    }
                }
                foreach (string str_obj in my報院條件說明)
                {
                    if (str_obj.IndexOf("TB_") >= 0)
                    {
                        TextBox tb = Plh_Dynax_sRC.FindControl(str_obj) as TextBox;
                        if (tb != null)
                        {
                            if (tb.Text.ToUpper().IndexOf("SCRIPT ") >= 0) Response.Redirect("../danger.aspx");
                            s_value = tb.ID.Replace("TB_", "");
                            //SDS_sRC.UpdateParameters.Clear();
                            //SDS_sRC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                            //SDS_sRC.UpdateCommand = "esp_treatyCase_sRc_modify";
                            //SDS_sRC.UpdateParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
                            //SDS_sRC.UpdateParameters.Add("class", SQLInjectionReplace(ViewState["tr_class"].ToString()));
                            //SDS_sRC.UpdateParameters.Add("ver", SQLInjectionReplace("0"));
                            //SDS_sRC.UpdateParameters.Add("svalue", SQLInjectionReplace(s_value));
                            //SDS_sRC.UpdateParameters.Add("sdoc", SQLInjectionReplace(tb.Text.Trim()));
                            //SDS_sRC.UpdateParameters.Add("stype", SQLInjectionReplace("Modify"));
                            //for (int i = 0; i < SDS_sRC.UpdateParameters.Count; i++)
                            //{
                            //    SDS_sRC.UpdateParameters[i].ConvertEmptyStringToNull = false;
                            //}
                            //SDS_sRC.Update();
                            #region --- modify ---

                            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                            {
                                SqlCommand sqlCmd = new SqlCommand();
                                sqlCmd.Connection = sqlConn;
                                sqlCmd.CommandType = CommandType.StoredProcedure;

                                sqlCmd.CommandText = @"esp_treatyCase_sRc_modify";

                                sqlCmd.CommandTimeout = 0;

                                sqlCmd.Parameters.Clear();
                                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                                sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                                sqlCmd.Parameters.AddWithValue("@ver", "0");
                                sqlCmd.Parameters.AddWithValue("@svalue", oRCM.SQLInjectionReplaceAll(s_value));
                                sqlCmd.Parameters.AddWithValue("@sdoc", oRCM.SQLInjectionReplaceAll(tb.Text.Trim()));
                                sqlCmd.Parameters.AddWithValue("@stype", "Modify");


                                try
                                {
                                    sqlConn.Open();
                                    sqlCmd.ExecuteNonQuery();
                                }
                                catch (Exception ex)
                                {

                                    // --- 執行異常通報 --- //
                                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                        Request,
                                        Response,
                                        ex
                                        );

                                    oRCM.ErrorExceptionDataToDB(logMail);

                                }
                                finally
                                {
                                    sqlConn.Close();
                                }
                            }

                            #endregion
                        }
                    }
                }
            }

            #endregion
            Treaty_log(ViewState["seno"].ToString(), "案件存檔", "", "", "treaty\\TreatyCase_modify.aspx");

        }

    }


    public void doInsert(string type)
    {
        string stype = type;
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"insert treaty_case_oRC (tc_seno,tcoRC_ver,tcoRC_val,tcoRC_desc1,tcoRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tc_oRC_ver"].ToString()));
            sqlCmd.Parameters.AddWithValue("@val", oRCM.SQLInjectionReplaceAll(stype));
            sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_contno.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(TB_otherrequire_handle_name.Text.Trim()));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplace(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplace(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplace(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplace(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplace(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplace(GetUserIP()));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplace(xApp));
        //for (int i = 0; i < SDS_log.InsertParameters.Count; i++)
        //{
        //    SDS_log.InsertParameters[i].ConvertEmptyStringToNull = false;
        //}

        //SDS_log.Insert();
        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void BT_view_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyCase_view.aspx?seno=" + ViewState["seno"].ToString());
    }
    protected void LB_request_Click(object sender, EventArgs e)
    {
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["TreatyURL"].ToString();
        string strWinOpen = string.Format("{0}/treaty/webpage/TreatyApply_view.aspx?contno={1}", strEngage_Path, txtComplexNo.Text);
        string script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
    }

    protected void cb_conttype_CheckedChanged(object sender, EventArgs e)
    {

        if (ViewState["tr_class"].ToString() == "B")
        {
            ddlContType.Items.Clear();
            string strCondition = "";
            if (cb_conttype_b0.Checked)
                strCondition += "B0,";

            if (cb_conttype_b1.Checked)
                strCondition += "B1,";

            if (cb_conttype_d4.Checked)
                strCondition += "D4,";

            if (cb_conttype_d5.Checked)
                strCondition += "D5,";

            if (cb_conttype_d7.Checked)
                strCondition += "D7,";
            if (cb_conttype_ns.Checked)
                strCondition += "NS,";
            if (rb_conttype_ui.Checked)
                strCondition += "F7,FA,F9,F3,FC";

            if (strCondition.Length > 0)
                strCondition = strCondition.Substring(0, strCondition.Length - 1);
            if (strCondition == "")
                strCondition = "ZZ";

            //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '" + strCondition + "' ,'10' ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treatyCase_codetable";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
                sqlCmd.Parameters.AddWithValue("@code_type", "10");

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            ddlContType.Items.Insert(0, " --請選擇-- ");
        }

        if ((cb_conttype_d7.Checked || CB_技術讓與.Checked || CB_技術授權.Checked || CB_專利授權.Checked || CB_技術與專利授權.Checked || CB_特定區域.Checked))
        {
            if (DDL_計價.SelectedValue == "" || DDL_計價.SelectedValue == "C")
            {

                LB_計價提示.Visible = true;
                //DDL_計價.SelectedValue= "Y";
                //string script = "<script language='javascript'>alert('注意！\\n此設定會啟動計價需求!');</script>";
                //ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }

    protected void CB_成果有特殊限制者_CheckedChanged(object sender, EventArgs e)
    {
        if (CB_成果有特殊限制者.Checked)
        {
            TB_成果有特殊限制者_說明.Visible = true;

        }
        else
        {
            TB_成果有特殊限制者_說明.Visible = false;
            TB_成果有特殊限制者_說明.Text = "";
        }
    }

    protected void CB_特殊費用負擔_CheckedChanged(object sender, EventArgs e)
    {
        if (CB_特殊費用負擔.Checked)
        {
            TB_特殊費用負擔原因.Visible = true;
        }
        else
        {
            TB_特殊費用負擔原因.Visible = false;
            TB_特殊費用負擔原因.Text = "";
        }
    }



    protected void DDL_計價_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (DDL_計價.SelectedValue == "Y" && ViewState["計價"].ToString() == "")
        {
            //string script_alert = "<script language='javascript'> $('#TB_計價說明').validationEngine('showPrompt', '★此設定會啟動計價需求(請填上計價說明)!','','',true); $('#TB_計價說明').click(function () { $('#TB_計價說明').validationEngine('hide'); })</script>";

            string script_alert = "<script language='javascript'>alert('此設定會啟動計價程序(請填上計價說明) !!') </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_計價說明", script_alert);
        }
    }

    private string Bind報院提示()
    {
        DataTable dt = new DataTable();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='90' and enable='1' order by display_order  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        if (dt.Rows.Count > 0)
            return Server.HtmlDecode(Server.HtmlEncode(dt.Rows[0]["subtype_desc"].ToString()));

        return "";
    }
}