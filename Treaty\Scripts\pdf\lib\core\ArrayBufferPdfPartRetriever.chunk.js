/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[5],{389:function(ia,y,e){e.r(y);ia=e(48);e=e(316);var fa=function(){function e(e){this.buffer=e;this.fileSize=null===e||void 0===e?void 0:e.byteLength}e.prototype.getFileData=function(e){e(new Uint8Array(this.buffer))};e.prototype.getFile=function(){return Promise.resolve(null)};return e}();Object(ia.a)(fa);Object(e.a)(fa);Object(e.b)(fa);y["default"]=fa}}]);}).call(this || window)
