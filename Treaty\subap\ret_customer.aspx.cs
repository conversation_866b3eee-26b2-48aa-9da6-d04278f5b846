﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;


public partial class web_ret_customer : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class company
    {
        private string Commonkey;
        private string compidno;
        private string compcname;
        private string comppostno;
        private string compfax;
        private string addr;
        private string compchairman;
        private string compcmtitle;
        private string compcapital;
        private string compemployee;
        private string compturnover;
        private string compsetupdate;
        private string compurl;
        private string compmainprd;
        private string compphone;
        private string compcity;
        public string c_Commonkey
        {
            get { return Commonkey; }
            set { Commonkey = value; }
        }
        public string c_compidno
        {
            get { return compidno; }
            set { compidno = value; }
        }
        public string c_compcname
        {
            get { return compcname; }
            set { compcname = value; }
        }
        public string c_comppostno
        {
            get { return comppostno; }
            set { comppostno = value; }
        }
        public string c_compfax
        {
            get { return compfax; }
            set { compfax = value; }
        }
        public string c_addr
        {
            get { return addr; }
            set { addr = value; }
        }
        public string c_compchairman
        {
            get { return compchairman; }
            set { compchairman = value; }
        }
        public string c_compcmtitle
        {
            get { return compcmtitle; }
            set { compcmtitle = value; }
        }
        public string c_compcapital
        {
            get { return compcapital; }
            set { compcapital = value; }
        }
        public string c_compemployee
        {
            get { return compemployee; }
            set { compemployee = value; }
        }
        public string c_compturnover
        {
            get { return compturnover; }
            set { compturnover = value; }
        }
        public string c_compsetupdate
        {
            get { return compsetupdate; }
            set { compsetupdate = value; }
        }
        public string c_compurl
        {
            get { return compurl; }
            set { compurl = value; }
        }
        public string c_compmainprd
        {
            get { return compmainprd; }
            set { compmainprd = value; }
        }
        public string c_compphone
        {
            get { return compphone; }
            set { compphone = value; }
        }
        public string c_compcity
        {
            get { return compcity; }
            set { compcity = value; }
        }

    }

    public bool IsDangerWord(string str)
    {
        if (str.ToUpper().IndexOf("-") >= 0) return true;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        return false;
    }
    protected void Page_Load(object sender, System.EventArgs e)
    {
        company u = new company();
        string str_kw = "";
        int int_danger = 0;

        if (Request.QueryString["keyword"] != null)
        {
            str_kw = Server.UrlDecode(Request.QueryString["keyword"]);
            if (Base64.danger_word_all(str_kw) == "1")
                int_danger++;  //有危險字眼
        }
        else
            int_danger++;  //有危險字眼

        if (Request.QueryString["Commonkey"] != null)
        {
            u.c_Commonkey = Server.HtmlEncode(Request.QueryString["Commonkey"]);
            if (Base64.danger_word_guid(u.c_Commonkey) == "1")
                int_danger++;  //有危險字眼
        }
        else
            int_danger++;  //有危險字眼
        //int_danger = 0;

        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            if (IsDangerWord(Request.QueryString["callback"].ToString())) Response.Redirect("../danger.aspx");
        }

        if (int_danger > 0)
        {   //有危險字眼
            u.c_compidno = "";
            u.c_compcname = "danger";
            u.c_comppostno = "";
            u.c_compfax = "";
            u.c_addr = "";
            u.c_compchairman = "";
            u.c_compcmtitle = "";
            u.c_compcapital = "";
            u.c_compemployee = "";
            u.c_compturnover = "";
            u.c_compsetupdate = "";
            u.c_compurl = "";
            u.c_compmainprd = "";
            u.c_compphone = "";
        }
        else
        {
            /*
                        SqlCommand oCmd = new SqlCommand();

                        string strSQL = @" SELECT  distinct  comp_idno,comp_cname,comp_postno,comp_fax,
                        case when RTRIM(M.code_valuedesc) is NULL then '' else RTRIM(M.code_valuedesc) end +case when rtrim(comp_localarea)is null then '' else rtrim(comp_localarea) end +case when rtrim(comp_addr) is null then '' else rtrim(comp_addr) end  as addr ,comp_chairman,
                        comp_cmtitle,comp_capital,comp_employee,comp_turnover,comp_setupdate,comp_url,comp_mainprd,comp_phone,comp_city
                        FROM  cust  left join dbo.visit_codetbl M on comp_city=M.code_value and M.code_type='008'
                        INNER JOIN tbl_getcust ON dbo.cust.comp_idno = dbo.tbl_getcust.gc_colreturnvalue 
                        WHERE (cust.comp_delmark <> '1')and  gc_colkey=@gcv_colkey ";
                        oCmd.Parameters.AddWithValue("@gcv_colkey", u.c_Commonkey);
                        oCmd.CommandText = strSQL;
            */

            //mark by nico 20120109
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["contract"].ConnectionString);
            if (Session["compno"] != null)
            {
                //SqlCommand oCmd = new SqlCommand();
                //string strSQL = @" SELECT  distinct  comp_idno,comp_cname,comp_postno,comp_fax,
                //case when RTRIM(M.code_valuedesc) is NULL then '' else RTRIM(M.code_valuedesc) end +case when rtrim(comp_localarea)is null then '' else rtrim(comp_localarea) end +case when rtrim(comp_addr) is null then '' else rtrim(comp_addr) end  as addr ,comp_chairman,
                //comp_cmtitle,comp_capital,comp_employee,comp_turnover,comp_setupdate,comp_url,comp_mainprd,comp_phone,comp_city
                //FROM  visitdb.dbo.v_cust  
                //left join visitdb.dbo.v_visit_codetbl M on comp_city=M.code_value and M.code_type='008'
                //left join visitdb.dbo.tbl_getcust ON  comp_idno = visitdb.dbo.tbl_getcust.gc_colreturnvalue 
                //WHERE ( comp_delmark <> '1') and  comp_idno=@comp_idno ";

                //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                DataSet ds = new DataSet();
                //oCmd.Parameters.AddWithValue("@comp_idno", Session["compno"].ToString());
                //oCmd.CommandText = strSQL;
                //oda.Fill(ds);

                #region --- query ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;
                    sqlCmd.CommandText = @"SELECT  distinct  comp_idno,comp_cname,comp_postno,comp_fax,
                --case when RTRIM(M.code_valuedesc) is NULL then '' else RTRIM(M.code_valuedesc) end +
                case when rtrim(comp_localarea) is null then '' else rtrim(comp_localarea) end +case when rtrim(comp_addr) is null then '' else rtrim(comp_addr) end as addr, comp_chairman,
                comp_cmtitle, comp_capital, comp_employee, comp_turnover, comp_setupdate, comp_url, comp_mainprd, comp_phone, comp_city
                FROM  visitdb.dbo.v_cust
                --left join visitdb.dbo.v_visit_codetbl M on comp_city = M.code_value and M.code_type = '008'
                --left join visitdb.dbo.tbl_getcust ON  comp_idno = visitdb.dbo.tbl_getcust.gc_colreturnvalue
                WHERE(comp_delmark <> '1') and  comp_idno = @comp_idno";


                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;
                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@comp_idno", oRCM.SQLInjectionReplaceAll(Session["compno"].ToString()));


                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                        sqlDA.Fill(ds);


                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion

                if (ds.Tables[0].DefaultView.Count == 1)
                {
                    u.c_compidno = Server.HtmlEncode(ds.Tables[0].Rows[0][0].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compcname = Server.HtmlEncode(ds.Tables[0].Rows[0][1].ToString().Trim().Replace("\"", "\\\""));
                    u.c_comppostno = Server.HtmlEncode(ds.Tables[0].Rows[0][2].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compfax = Server.HtmlEncode(ds.Tables[0].Rows[0][3].ToString().Trim().Replace("\"", "\\\""));
                    u.c_addr = Server.HtmlEncode(ds.Tables[0].Rows[0][4].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compchairman = Server.HtmlEncode(ds.Tables[0].Rows[0][5].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compcmtitle = Server.HtmlEncode(ds.Tables[0].Rows[0][6].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compcapital = Server.HtmlEncode(ds.Tables[0].Rows[0][7].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compemployee = Server.HtmlEncode(ds.Tables[0].Rows[0][8].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compturnover = Server.HtmlEncode(ds.Tables[0].Rows[0][9].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compsetupdate = Server.HtmlEncode(ds.Tables[0].Rows[0][10].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compurl = Server.HtmlEncode(ds.Tables[0].Rows[0][11].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compmainprd = Server.HtmlEncode(ds.Tables[0].Rows[0][12].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compphone = Server.HtmlEncode(ds.Tables[0].Rows[0][13].ToString().Trim().Replace("\"", "\\\""));
                    u.c_compcity = Server.HtmlEncode(ds.Tables[0].Rows[0][14].ToString().Trim().Replace("\"", "\\\""));
                }
                if (ds.Tables[0].DefaultView.Count == 0)
                {
                    u.c_compidno = "";
                    u.c_compcname = "error0";
                    u.c_comppostno = "";
                    u.c_compfax = "";
                    u.c_addr = "";
                    u.c_compchairman = "";
                    u.c_compcmtitle = "";
                    u.c_compcapital = "";
                    u.c_compemployee = "";
                    u.c_compturnover = "";
                    u.c_compsetupdate = "";
                    u.c_compurl = "";
                    u.c_compmainprd = "";
                    u.c_compphone = "";
                    u.c_compcity = "";
                }
                if (ds.Tables[0].DefaultView.Count > 1)
                {
                    u.c_compidno = "";
                    u.c_compcname = "error2";
                    u.c_comppostno = "";
                    u.c_compfax = "";
                    u.c_addr = "";
                    u.c_compchairman = "";
                    u.c_compcmtitle = "";
                    u.c_compcapital = "";
                    u.c_compemployee = "";
                    u.c_compturnover = "";
                    u.c_compsetupdate = "";
                    u.c_compurl = "";
                    u.c_compmainprd = "";
                    u.c_compphone = "";
                    u.c_compcity = "";
                }
                Session["compno"] = null;
            }
            else
            {
                u.c_compidno = "";
                u.c_compcname = "error0";
                u.c_comppostno = "";
                u.c_compfax = "";
                u.c_addr = "";
                u.c_compchairman = "";
                u.c_compcmtitle = "";
                u.c_compcapital = "";
                u.c_compemployee = "";
                u.c_compturnover = "";
                u.c_compsetupdate = "";
                u.c_compurl = "";
                u.c_compmainprd = "";
                u.c_compphone = "";
                u.c_compcity = "";

            }
        }
        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            if (CallBackFunction.Length > 50)
                Response.Redirect("../danger.aspx");
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        //20120118 測試輸出為BIG5 
        // Response.ContentType = "text/html";
        // Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");

        Response.Write(j);  //輸出JSONP的內容

    }
}