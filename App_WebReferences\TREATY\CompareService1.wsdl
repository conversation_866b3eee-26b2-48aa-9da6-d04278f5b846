<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="getkey">
        <s:complexType />
      </s:element>
      <s:element name="getkeyResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getkeyResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getText">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="fileName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="data" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getTextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getTextResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="get_diff_data">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="fileName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="data" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="fileName2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="data2" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="get_diff_dataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="get_diff_dataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="get_diff_text">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="txt" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="txt2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="get_diff_textResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="get_diff_textResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="get_match_text">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="txt" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="txt2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="get_match_textResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="get_match_textResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="getkeySoapIn">
    <wsdl:part name="parameters" element="tns:getkey" />
  </wsdl:message>
  <wsdl:message name="getkeySoapOut">
    <wsdl:part name="parameters" element="tns:getkeyResponse" />
  </wsdl:message>
  <wsdl:message name="getTextSoapIn">
    <wsdl:part name="parameters" element="tns:getText" />
  </wsdl:message>
  <wsdl:message name="getTextSoapOut">
    <wsdl:part name="parameters" element="tns:getTextResponse" />
  </wsdl:message>
  <wsdl:message name="get_diff_dataSoapIn">
    <wsdl:part name="parameters" element="tns:get_diff_data" />
  </wsdl:message>
  <wsdl:message name="get_diff_dataSoapOut">
    <wsdl:part name="parameters" element="tns:get_diff_dataResponse" />
  </wsdl:message>
  <wsdl:message name="get_diff_textSoapIn">
    <wsdl:part name="parameters" element="tns:get_diff_text" />
  </wsdl:message>
  <wsdl:message name="get_diff_textSoapOut">
    <wsdl:part name="parameters" element="tns:get_diff_textResponse" />
  </wsdl:message>
  <wsdl:message name="get_match_textSoapIn">
    <wsdl:part name="parameters" element="tns:get_match_text" />
  </wsdl:message>
  <wsdl:message name="get_match_textSoapOut">
    <wsdl:part name="parameters" element="tns:get_match_textResponse" />
  </wsdl:message>
  <wsdl:portType name="CompareService1Soap">
    <wsdl:operation name="getkey">
      <wsdl:input message="tns:getkeySoapIn" />
      <wsdl:output message="tns:getkeySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getText">
      <wsdl:input message="tns:getTextSoapIn" />
      <wsdl:output message="tns:getTextSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="get_diff_data">
      <wsdl:input message="tns:get_diff_dataSoapIn" />
      <wsdl:output message="tns:get_diff_dataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="get_diff_text">
      <wsdl:input message="tns:get_diff_textSoapIn" />
      <wsdl:output message="tns:get_diff_textSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="get_match_text">
      <wsdl:input message="tns:get_match_textSoapIn" />
      <wsdl:output message="tns:get_match_textSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CompareService1Soap" type="tns:CompareService1Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getkey">
      <soap:operation soapAction="http://tempuri.org/getkey" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getText">
      <soap:operation soapAction="http://tempuri.org/getText" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_diff_data">
      <soap:operation soapAction="http://tempuri.org/get_diff_data" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_diff_text">
      <soap:operation soapAction="http://tempuri.org/get_diff_text" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_match_text">
      <soap:operation soapAction="http://tempuri.org/get_match_text" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="CompareService1Soap12" type="tns:CompareService1Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getkey">
      <soap12:operation soapAction="http://tempuri.org/getkey" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getText">
      <soap12:operation soapAction="http://tempuri.org/getText" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_diff_data">
      <soap12:operation soapAction="http://tempuri.org/get_diff_data" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_diff_text">
      <soap12:operation soapAction="http://tempuri.org/get_diff_text" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="get_match_text">
      <soap12:operation soapAction="http://tempuri.org/get_match_text" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CompareService1">
    <wsdl:port name="CompareService1Soap" binding="tns:CompareService1Soap">
      <soap:address location="http://localhost:32722/CompareService1.asmx" />
    </wsdl:port>
    <wsdl:port name="CompareService1Soap12" binding="tns:CompareService1Soap12">
      <soap12:address location="http://localhost:32722/CompareService1.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>