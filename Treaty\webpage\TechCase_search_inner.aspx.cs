﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using Treaty_report;

public partial class TechCase_search_inner : System.Web.UI.Page  //Treaty.common   //
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();

            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = "select count(m_sno) from treaty_buztbl where emp_group='0' and emp_no=@empno";
            oCmd.CommandType = CommandType.Text;
            oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "DataSource");
            if (ds != null && ds.Tables[0].Rows.Count >= 1)
            {
                ViewState["SYS"] = ssoUser.empNo;
            }
            oCmd.Dispose();
            oda.Dispose();

            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;

            ViewState["sortorder"] = "DESC";
            ViewState["sortField"] = "tc_petition_day";

            Bind_Light();
            Bind_Org();
            Bind_ContType();
            Bind_Degree();
            Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_search));

        bool isPC = oRCM.IsPC(Request);
        btnExcel.Visible = isPC;
    }

    private void Bind_Org()
    {
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_search_inner";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "orglist");
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlOrgcd.DataSource = dt;
                ddlOrgcd.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void Bind_ContType()
    {
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_search_inner";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "conttype");
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();
                ddlContType.Items.Insert(0, new ListItem(" ", "00"));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void Bind_Degree()
    {
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_search_inner";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "degree_ist");
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddl_degree.DataSource = dt;
                ddl_degree.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void Bind_Data(string str_sortField, string str_sort)
    {
        if (IsDangerWord(ddlOrgcd.SelectedValue) || (ddlOrgcd.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlContType.SelectedValue) || (ddlContType.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");

        //if (IsDangerWord(tbxPromoterName.Text.ToUpper())) Response.Redirect("../danger.aspx");

        if (IsDangerWord(txtCompname.Text.ToUpper())) Response.Redirect("../danger.aspx");
        //if (IsDangerWord(tbxPromoterName.Text.ToUpper())) Response.Redirect("../danger.aspx");

        if (txtCompname.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        //if (IsDangerWord(ddl_amend.SelectedValue) || (ddl_amend.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");

        DataTable dt = GetSearchData();
        SGV_search.DataSource = dt;
        SGV_search.DataBind();
    }

    private DataTable GetSearchData()
    {
        DataTable dt = new DataTable();

        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_search_inner";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(ddlOrgcd.SelectedValue));//單位別
            sqlCmd.Parameters.AddWithValue("@compName", oRCM.SQLInjectionReplaceAll(txtCompname.Text.Trim()));//客戶名稱
            sqlCmd.Parameters.AddWithValue("@conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));//契約性質
            sqlCmd.Parameters.AddWithValue("@req_dept", oRCM.SQLInjectionReplaceAll(tbx_req_dept.Text));//單位執行部門
            sqlCmd.Parameters.AddWithValue("@degree", oRCM.SQLInjectionReplaceAll(ddl_degree.SelectedValue));//狀態
            //sqlCmd.Parameters.AddWithValue("@promoter_name", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));//單位承辦人員
            //sqlCmd.Parameters.AddWithValue("@handle_name", oRCM.SQLInjectionReplaceAll(tbx_handle_name.Text));//技轉法律中心承辦人
            //sqlCmd.Parameters.AddWithValue("@amend", oRCM.SQLInjectionReplaceAll(ddl_amend.SelectedValue));//是否為修約
            sqlCmd.Parameters.AddWithValue("@kw", oRCM.SQLInjectionReplaceAll(tbx_name.Text));//案件編號/名稱
            sqlCmd.Parameters.AddWithValue("@mode", "search_V3");
            if (ddl_Light.SelectedValue != "")
                sqlCmd.Parameters.AddWithValue("@案件燈號", oRCM.SQLInjectionReplaceAll(ddl_Light.SelectedItem.Text));//案件燈號

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                //oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        return dt;
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {
        SGV_search.PageIndex = 0;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void btnClear_Click(object sender, EventArgs e)
    {
        ddlOrgcd.SelectedIndex = 0;
        txtCompname.Text = "";
        ddlContType.SelectedIndex = 0;
        tbx_req_dept.Text = "";
        ddl_degree.SelectedIndex = 0;
        tbx_name.Text = "";
        ddl_Light.SelectedIndex = 0;
    }

    protected void btnExcel_Click(object sender, EventArgs e)
    {
        DataTable dt = GetSearchData();

        if (dt.Rows.Count == 0)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('查無資料');</script>", false);
            return;
        }

        DataTable dtExport = new DataTable();

        dtExport.Columns.Add("案件編號");
        dtExport.Columns.Add("議約編號");
        dtExport.Columns.Add("單位名稱");
        dtExport.Columns.Add("洽案／契約名稱");
        dtExport.Columns.Add("客戶名稱");
        dtExport.Columns.Add("分案日");
        dtExport.Columns.Add("主管簽核同意日");

        dtExport.Columns.Add("法務承辦人");
        dtExport.Columns.Add("案件類型");
        dtExport.Columns.Add("案件狀態");
        dtExport.Columns.Add("燈號");
        dtExport.Columns.Add("燈號_說明");
        dtExport.Columns.Add("公益目的");
        dtExport.Columns.Add("促進整體產業發展");
        dtExport.Columns.Add("提升研發成果運用效益");
        dtExport.Columns.Add("第三方鑑價報告");
        dtExport.Columns.Add("廠商背景資料");
        dtExport.Columns.Add("承接能力");
        dtExport.Columns.Add("取得授權／讓與之緣由");
        dtExport.Columns.Add("廠商運用規劃");
        dtExport.Columns.Add("產業效益");
        dtExport.Columns.Add("符合國內產業優先原則");
        dtExport.Columns.Add("價金合理性說明");
        dtExport.Columns.Add("廠商申請修約之主因");
        dtExport.Columns.Add("單位同意廠商所請之原因及考量");
        dtExport.Columns.Add("延後付款是否有擔保品及說明");
        dtExport.Columns.Add("備註");

        foreach (DataRow dr in dt.Rows)
        {
            DataRow drExport = dtExport.NewRow();

            drExport["案件編號"] = dr["tt_seno"].ToString();
            drExport["議約編號"] = dr["議約編號"].ToString();
            drExport["單位名稱"] = dr["單位名稱"].ToString();
            drExport["洽案／契約名稱"] = dr["tt_name"].ToString();
            drExport["客戶名稱"] = dr["客戶名稱"].ToString();
            drExport["分案日"] = dr["分案日"].ToString();
            drExport["主管簽核同意日"] = dr["主管簽核同意日"].ToString();
            drExport["法務承辦人"] = dr["法務承辦人"].ToString();
            drExport["案件類型"] = dr["案件類型"].ToString();
            drExport["案件狀態"] = dr["技轉狀態"].ToString();
            drExport["燈號"] = dr["案件燈號"].ToString();
            drExport["燈號_說明"] = dr["燈號_說明"].ToString();
            drExport["公益目的"] = dr["公益目的"].ToString();
            drExport["促進整體產業發展"] = dr["促進產業發展"].ToString();
            drExport["提升研發成果運用效益"] = dr["提升成果運用效益"].ToString();
            drExport["第三方鑑價報告"] = dr["第三方鑑價報告"].ToString();
            drExport["備註"] = dr["tt_remark"].ToString();

            int tt_seno = Convert.ToInt32(dr["tt_seno"].ToString());

            DataTable dt_sRC = sRC_view(tt_seno, "X");

            var o = (from a in dt_sRC.AsEnumerable()
                     select new
                     {
                         tcs_codeName = a.Field<string>("tcs_codeName"),
                         tt_sRC_desc = a.Field<string>("tt_sRC_desc"),
                     }).ToList();

            drExport["廠商背景資料"] = o.Where(x => x.tcs_codeName == "廠商背景資料").Select(x=>x.tt_sRC_desc).FirstOrDefault();
            drExport["承接能力"] = o.Where(x => x.tcs_codeName == "承接能力").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["取得授權／讓與之緣由"] = o.Where(x => x.tcs_codeName == "取得授權/讓與之緣由").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["廠商運用規劃"] = o.Where(x => x.tcs_codeName == "廠商運用規劃").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["產業效益"] = o.Where(x => x.tcs_codeName == "產業效益").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["符合國內產業優先原則"] = o.Where(x => x.tcs_codeName == "符合國內產業優先原則").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["價金合理性說明"] = o.Where(x => x.tcs_codeName == "價金合理性說明").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["廠商申請修約之主因"] = o.Where(x => x.tcs_codeName == "廠商申請修約之主因").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["單位同意廠商所請之原因及考量"] = o.Where(x => x.tcs_codeName == "單位同意廠商所請之原因及考量").Select(x => x.tt_sRC_desc).FirstOrDefault();
            drExport["延後付款是否有擔保品及說明"] = o.Where(x => x.tcs_codeName == "延後付款是否有擔保品及說明").Select(x => x.tt_sRC_desc).FirstOrDefault();

            dtExport.Rows.Add(drExport);
        }

        this.ExportCustomerAnalysisExcelFile(dtExport);
    }

    #region SGV
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }

    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.PageIndex = e.NewPageIndex;

        // SGV_search.DataBind();
    }

    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            Response.Redirect("./TechCase_view.aspx?tt_seno=" + e.CommandArgument.ToString());
        }
    }

    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {


    }

    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    #endregion

    //匯出EXCEL START
    #region 匯出EXCEL	

    //2.套用設定好的EXCEL樣板
    private void ExportCustomerAnalysisExcelFile(DataTable dataSource)
    {
        string filePath = Server.MapPath("../../Template/內部查詢明細_TechCase.xlsx");
        if (File.Exists(filePath) == false)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('找不到樣板檔，無法匯出檔案');
        </script>
        ", false);
            return;
        }

        asposeExcel excel = new asposeExcel("內部查詢明細_TechCase.xlsx");
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();
        sheet.Cells.ImportDataTable(dataSource, true, "B7");
        //sheet.Cells.Merge(dataSource.Rows.Count + 9, 0, 2, 17); //第幾行開始 , 起始位置 , 合併幾列 ,合併幾欄
        excel.PutValue(sheet, dataSource.Rows.Count + 9, 0, "工業技術研究院機密資料 禁止複製、轉載、外流 │ITRI CONFIDENTIAL DOCUMENT DO NOT COPY OR DISTRIBUTE");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        //針對表頭style控制
        objStyle.HorizontalAlignment = Aspose.Cells.TextAlignmentType.Center;
        objStyle.Pattern = Aspose.Cells.BackgroundType.Solid;
        objStyle.ForegroundColor = Color.LightGreen;

        sheet.Cells.ApplyRowStyle(6, objStyle, objStyleFlag);

        ExportExcel("報表.xlsx", excel.exportExcel());
    }

    //匯出EXCEL
    private void ExportExcel(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);

        Response.Clear();
        Response.ContentType = this.RetrieveFileContentType(fileExtension);
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }

    /// <summary>
    /// 取得檔案類型
    /// </summary>
    /// <param name="fileExtension">副檔名</param>
    private string RetrieveFileContentType(string fileExtension)
    {
        if (fileExtension.Equals(".docx"))
            return "Application/msword";
        else if (fileExtension.Equals(".xlsx"))
            return "Application/vnd.ms-excel";
        else if (fileExtension.Equals(".pdf"))
            return "Application/pdf";
        return string.Empty;
    }
    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull)
    {
        foreach (Parameter parameter in dataSource.SelectParameters)
            parameter.ConvertEmptyStringToNull = isNull;
    }
    #endregion
    //匯出EXCEL END 

    protected void LBx_客戶名稱_Click(object sender, EventArgs e)
    {
        LinkButton CompInfo = sender as LinkButton;
        string Compno = CompInfo.Attributes["Compno"];
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);

    }

    private void Bind_Light()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "init_Light");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddl_Light.DataSource = dt;
                ddl_Light.DataTextField = "text";
                ddl_Light.DataValueField = "value";
                ddl_Light.DataBind();
                ddl_Light.Items.Insert(0, new ListItem() { Value = "", Text = "--請選擇--" });
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

    }

    private DataTable sRC_view(int tt_seno, string str_class)
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_search_inner";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "sRC_view");
            sqlCmd.Parameters.AddWithValue("@tt_seno", tt_seno);
            sqlCmd.Parameters.AddWithValue("@tt_class", oRCM.SQLInjectionReplaceAll(str_class));
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        return dt;
        #endregion
    }

}
