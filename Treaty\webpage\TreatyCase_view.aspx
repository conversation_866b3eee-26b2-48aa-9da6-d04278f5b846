﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_view.aspx.cs" Inherits="TreatyCase_view" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot.ascx" TagName="Foot" TagPrefix="uc2" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/jquery-migrate-1.2.1.js"></script>
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />

    <script type="text/javascript" src="../Scripts/autosize.min.js"></script>
    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        var p = navigator.platform;
        //function showDialog() {
        //    jQuery('#popup').dialog({
        //        modal: true,
        //        title: 'Meaasge',
        //        resizable: false,
        //        width: 'auto',
        //        autoOpen: false,
        //        open: function () {
        //            jQuery("button, input[type=submit]").button();
        //            secondarySiteDisplay();
        //            if (jQuery('#ptsiid').length != 0) {
        //                jQuery('#ptsiid').focus();
        //                jQuery('#popup').dialog().width(jQuery('#popup').width());
        //            } else {
        //                document.forms.editform.firstname.focus();
        //            }
        //        }
        //    });
        //}
        //function ViewEnLarge(obj, newPageUrl) {
        //    jQuery('#popup').load(newPageUrl, showDialog);
        //    alert($('#' + obj).val());
        //}
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "90%", height: "80%", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }
        function treaty_Inspect(seno, tci_no) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Inspect.aspx?seno=" + seno + "&tci_no=" + tci_no
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }
        function treaty_fileup() {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileUpZ.aspx?contno=" + ($("#DDL_SeqSn option:selected").text()).replace("-", "") + "&seno=" + $("#DDL_SeqSn").val()
                , title: '結案後檔案上傳'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function DeleteCase() {
            alert("案件已刪除!");
            location.replace("./TreatyApply.aspx");
        }
        function treatyCancle(seno) {
            if (confirm('確定要取消需求?\t <<取消前請知會法務人員>> ')) {
                $(".ajax_mesg").colorbox({
                    href: "./TreatyCase_Cancle.aspx?seno=" + $("#DDL_SeqSn").val()
                    , title: '議約需求取消'
                    , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                        reflash_topic("case_renew", 0);
                    }
                });
            }
        }
        function treatyCaseAssign(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assign.aspx?seno=" + seno
                , title: '分案/指派 法務人員承辦'
                , iframe: true, width: "95%", height: "90%px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: true, ajaxCache: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("case_renew", 0);
                }
            });
        }
        function SatisfView(seno) {
            $(".ajax_Satisf").colorbox({
                href: "./SatisfactionView.aspx?seno=" + seno
                , title: '滿意度'
                , iframe: true, width: "900px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("case_renew", 0);
                }
            });
        }
        function EndCase(seno) {
            alert('案件已發結案通知!\n');
            location.replace('./TreatyCase_view.aspx?seno=' + seno);
        }
        function doNewCase(seno, contno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_NewVer.aspx?contno=" + contno
                , iframe: true, width: "440px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '議約需求'
                , onClosed: function () {
                    if ($.colorbox.data == "1")
                        location.replace('./TreatyApply.aspx?seno=' + seno + '&newver=1');
                    if ($.colorbox.data == "2")
                        location.replace('./TreatyApply.aspx?seno=' + seno + '&newver=2');
                }
            });
        }
        function treaty_trfileup(contno, seno) {
            $(".ajax_trFile").colorbox({
                href: "./TreatyApply_FileUp.aspx?contno=" + contno + "&seno=" + seno
                , title: '上傳檔案'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assignInspect.aspx?seno=" + seno
                , title: '新增審查人'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }

        function openFileCompare(sender) {
            //window.open('../FileCompare/doccompare/index2.aspx?' + sender.getAttribute('key'));
            window.open('../FileCompare/doccompare/index2.aspx?seno=<%=ViewState["seno"].ToString()%>');
        }

        function doc_attach(doc_no) {
            //alert("https://itriap7.itri.org.tw/docfile/src/Query/Query_all_contract_TVDRM.aspx?formid=" + doc_no);
            window.open("https://docfile.itri.org.tw/contract/contractdetail?formid=" + doc_no, "dx", "fullscreen=no,resizable=no,scrollbars, channelmode=no,directories=no, status=no,toolbar=no,menubar=no,location=no,height=1000,width=900", null);
        }

        function WordCheck() {
            window.open('https://itriap9.itri.org.tw/projassist/WebPage/Index.aspx');
        }
        function showCompInfoDialog(Compno) {
            var newopen = window.open('https://arpt.itri.org.tw/CustomerRiskDetails.aspx?CustNo=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }
        function showCompInfo(Compno) {
            var newopen = window.open('https://cust.itri.org.tw/mgrcust_custdata.aspx?comp_idno=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }
        function Valuation_inspect(seno) {
            $(".ajax_vmesg").colorbox({
                href: "./TreatyCase_valuation_inspact.aspx?seno=" + seno
                , title: '計價審查'
                , iframe: true, width: "850px", height: "350px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("valuation_renew", 0);
                }
            });
        }
        function Valuation_modify(seno) {
            $(".ajax_vmesg").colorbox({
                href: "./TreatyCase_valuation_modify.aspx?seno=" + seno
                , title: '計價維護'
                , iframe: true, width: "850px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("valuation_renew", 0);
                }
            });
        }
        function Valuation_history(seno) {
            $(".ajax_vmesg").colorbox({
                href: "./TreatyCase_valuation_view.aspx?seno=" + seno
                , title: '計價歷程'
                , iframe: true, width: "850px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }

        .ui-cluetip-header, .ui-cluetip-content {
            overflow: auto;
            max-heisht: 4em;
        }

        .mce-content-body body {
            font-size: 16px;
        }

        .auto-style1 {
            width: 180px;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">
        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left">
                                <span class="font-red">*表示為必填欄位</span>
                                <asp:ImageButton ID="IB_newVer" runat="server" ImageUrl="../images/text.gif" Class="ajax_mesg" />
                            </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="LB_request" runat="server" OnClick="LB_request_Click"><img src="../images/icon-1301.gif" border="0"/>檢視申請單資訊</asp:LinkButton>
                                    <asp:LinkButton ID="btnEngage" runat="server" OnClick="btnEngage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊&nbsp;&nbsp;</asp:LinkButton>
                                    <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_tratycase_info" runat="server">案件紀錄</asp:Literal>
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_source" Text="列印" Visible="False" OnClick="BT_Print_source_Click" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print1" Text="列印" OnClick="BT_Print_Click" Visible="False" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Tag1" Text="列印檔案標籤" OnClick="BT_Print_Tag_Click" Visible="False" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Excel1" Text="列印檔案標籤_Excel" OnClick="BT_Print_Excel_Click" Visible="False" />
                                    <asp:Button class="ajax_mesg genbtnS" ID="BT_Inspect2" runat="server" Text="案件審查" Visible="False" />
                                    <asp:Button ID="BT_caseAssign0" runat="server" class="ajax_mesg genbtnS" Text="分案指派" Visible="False" />
                                    <asp:Button runat="server" class="ajax_Satisf genbtnS" ID="BT_Satisf2" Text="滿意度調查" Visible="False" />
                                    <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit" Text="編輯" OnClick="btEdit_Click" Visible="False" />
                                    <asp:Button runat="server" class="genbtnS" ID="BT_End1" Text="結案通知" OnClick="BT_End_Click" Visible="false"></asp:Button>
                                    <asp:Button runat="server" class="genbtnS" ID="BT_計價取消" Text="計價取消" OnClick="BT_計價取消_Click" Visible="false"></asp:Button>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:DropDownList ID="DDL_SeqSn" runat="server" Width="160px" DataTextField="contno" DataValueField="seno" AutoPostBack="True" OnSelectedIndexChanged="DDL_SeqSn_SelectedIndexChanged"></asp:DropDownList><asp:Literal ID="LT_擬約幫手" runat="server" Text=''></asp:Literal><br />
                                            <asp:Label ID="txtComplexNo" runat="server" Text="" Visible="false"></asp:Label>
                                            (舊案號:
                                            <asp:Label ID="txtOldContno" runat="server"></asp:Label>)
                        <%--<asp:SqlDataSource ID="SDS_DDL_SeqSn" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align="right" width="60">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                        </td>
                                        <td class="width40">
                                            <asp:Label ID="LB_language" runat="server" Text=""></asp:Label>
                                            <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" Visible="false" />
                                            <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" Visible="false" />
                                            <asp:RadioButton ID="rb_language_other" runat="server" Text="其他" GroupName="ContractLang" Visible="false" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label>&nbsp;
                                            <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server" Width="95px"></asp:Label>
                                            &nbsp;
                       分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server" Width="110px"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                <span class="font-red">*</span>契約名稱
                                            </div>
                                            <br />
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:Label>
                                            <div style="float: right; vertical-align: top">
                                                <asp:Literal ID="LT_計價" runat="server"></asp:Literal>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">公文資訊</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:LinkButton ID="LK_公文文號" runat="server">檢視公文資訊</asp:LinkButton>
                                            <div style="float: right; vertical-align: top">
                                                <asp:Literal ID="LT_計價版件次" runat="server"></asp:Literal>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">是否為急件需求</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="CB_急件" runat="server" Enabled="false" />
                                            <asp:TextBox ID="TB_急件原因" runat="server" Width="608px" TextMode="MultiLine" Height="20px" ReadOnly="true" Enabled="false"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="97%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound" htmlencode="false">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="跳票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_退換票" runat="server" Text='<%#  Eval("tmp_退換票").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="逾資本額½">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資本額" runat="server" Text='<%#  Eval("tmp_資本額").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資產遭查封">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_資產遭查封" runat="server" Text='<%#  Eval("tmp_資產遭查封").ToString()%>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="抽換票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_抽換票" runat="server" Text='<%#  Eval("tmp_抽換票").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="其他風險">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_其他風險" runat="server" Text='<%#  Eval("tmp_其他風險").ToString()%>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="含陸資">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_含陸資" runat="server" Text='<%# Eval("陸資").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商編號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_company" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                    <%--<asp:LinkButton ID="LBx_廠商編號" runat="server"   Compno='<%# Eval("comp_idno")%>' Text='<%# Eval("comp_idno") %>' OnClick="LBx_廠商編號_Click"></asp:LinkButton>--%>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                                <HeaderStyle Width="65px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商中文名稱<hr>廠商英文名稱">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_廠商中文名稱" runat="server" Compno='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString())%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="LB_廠商編號_Click"></asp:LinkButton>
                                                                    <asp:Label ID="LB_廠商英文名稱" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label><hr>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="450px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="50px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="利益迴避" HeaderText="利益迴避">
                                                                <HeaderStyle Width="10px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" ForeColor="Red" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField   HeaderText="風險客戶">
                                                                <HeaderStyle Width="10px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center"  />
                                                                 <ItemTemplate>
                                                                    <asp:ImageButton ID="IB_廠商中文名稱" runat="server" Compno='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString())%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="IBx_廠商編號_Click" ImageUrl="../images/icon-lookdetail.png"></asp:ImageButton>
                                                                 </ItemTemplate>                                                      
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                案件性質<br>
                                                <asp:Label ID="lb_standar_flag" runat="server" Font-Bold="True" ForeColor="blue" Visible="False">(常用版本)</asp:Label>
                                        </td>
                                        <td colspan="3" class="lineheight03">
                                            <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" Enabled="false" />
                                            <b>
                                                <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" Enabled="false" Font-Bold="True" ForeColor="#0060A4" />
                                                <asp:CheckBox ID="CB_技術讓與" runat="server" Text="技術讓與" class="font-title titlebackicon" Enabled="false" Font-Bold="True" ForeColor="#0060A4" />
                                            </b>
                                            <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽案)" Enabled="false" /><br />
                                            <asp:PlaceHolder ID="PH_法務內部資訊" runat="server" Visible="false">
                                                <div class="font-title titlebackicon">
                                                    <b>
                                                        <asp:CheckBox ID="CB_技術授權" runat="server" Text="專屬技術授權" Enabled="false" />
                                                        <asp:CheckBox ID="CB_專利授權" runat="server" Text="專屬專利授權" Enabled="false" />
                                                        <asp:CheckBox ID="CB_技術與專利授權" runat="server" Text="專屬技術與專利授權" Enabled="false" />
                                                        <asp:CheckBox ID="CB_特定區域" runat="server" Text="境外實施" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                        <asp:CheckBox ID="CB_全球" runat="server" Text="全球" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                        <asp:CheckBox ID="CB_陸港澳" runat="server" Text="陸港澳" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                        <asp:CheckBox ID="CB_韓國" runat="server" Text="韓國" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" /></b>
                                                </div>
                                            </asp:PlaceHolder>
                                            <asp:CheckBox ID="cb_conttype_rb" runat="server" Text="標案" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_m" runat="server" Text="保密契約" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_c" runat="server" Text="工服" />
                                            <asp:RadioButton ID="rb_conttype_uo" runat="server" Text="國外無收入" Enabled="false" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_ui" runat="server" Text="國內無收入" Enabled="false" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_bd" runat="server" Text="新創事業" GroupName="ConttypeT" Enabled="false" />
                                            <asp:RadioButton ID="rb_conttype_other" runat="server" Text="其他" GroupName="ConttypeT" Enabled="false" />
                                            <asp:Label ID="txt_class_other_desc" runat="server" Enabled="false"></asp:Label>
                                            <span id="spanContractEdit" runat="server" visible="false">
                                                <br />
                                                <hr />
                                                <font color="#ff0000">契約修訂:
						    <asp:radiobuttonlist id="rbl_amend" runat="server" repeatlayout="Flow" repeatdirection="Horizontal" Visible="false">
							    <asp:listitem value="1" selected="True">展延</asp:listitem>
							    <asp:listitem value="2">中止</asp:listitem>
							    <asp:listitem value="3">其他</asp:listitem>
						    </asp:radiobuttonlist><asp:Label id="LB_amend" runat="server" Text="" ></asp:Label>
                            <asp:Label id="txtamend_other_desc" runat="server" width="400px"></asp:Label></font>
                                            </span>
                                            <span style="color: red">
                                                <br />
                                                (若為收入性質契約，請至洽案管理進行相關案件登錄及申請契約需求！)</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約性質</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="LB_ContType" runat="server" Text=""></asp:Label>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True" Visible="false">
                                                <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:Label ID="lb_Amend_Show" runat="server" ForeColor="Red" Visible="false">(修約)</asp:Label><br />
                                            <%--<asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"  />--%>
                                            <asp:PlaceHolder ID="PH_是否計價" runat="server" Visible="false">是否計價:<asp:DropDownList ID="DDL_計價" runat="server" Enabled="false">
                                                <asp:ListItem Value="">無</asp:ListItem>
                                                <asp:ListItem Value="Y">是</asp:ListItem>
                                                <asp:ListItem Value="C">取消</asp:ListItem>
                                            </asp:DropDownList>
                                                ，計價說明:<asp:Label runat="server" Text="" ID="LB_計價說明"></asp:Label>
                                            </asp:PlaceHolder>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">契約預估金額</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="LB_ContMoneyType" runat="server" Text=""></asp:Label>
                                            <asp:DropDownList ID="ddlContMoneyType" runat="server" Width="144px" DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false" />
                                            <%--<asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"  />--%>
                                            <asp:Label ID="txtContMoney" runat="server" />
                                            &nbsp;元/匯率:<asp:Label ID="TB_money_rate" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                契約期間<br />
                                                (預定)
                                            </div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_contsdate" runat="server" />&nbsp;至&nbsp;
                                            <asp:Label ID="txt_contedate" runat="server"></asp:Label>
                                        </td>
                                        <asp:PlaceHolder ID="PL_olderVer2" runat="server" Visible="false">
                                            <td align="right">
                                                <div class="font-title titlebackicon">契約條件確定日期</div>
                                            </td>
                                            <td>
                                                <asp:Label ID="txt_confirm_date" runat="server" />
                                                <img src="../images/icon_tips.png" class="itemhint" title="契約條件確定日期<hr>1.承辦法務同仁於接案後，會儘快與您討論並確定契約需求條件內容，並據此來研擬適宜契約。<br>2.若於「契約條件確定日期」後再變更契約需求條件內容時，會影響完成時間。" />
                                            </td>
                                        </asp:PlaceHolder>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                契約內對於成果<br>
                                            有特殊限制者</td>
                                        <td>
                                            <div class="font-title titlebackicon">
                                                <asp:CheckBox ID="CB_成果有特殊限制者" runat="server" Text="" Enabled="false" />
                                            </div>
                                            <asp:TextBox ID="TB_成果有特殊限制者_說明" runat="server" Height="38px" TextMode="MultiLine" Width="300px" Enabled="false"></asp:TextBox>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">專利維護費用<img src="../images/icon_tips.png" class="itemhint" title="契約中有約定，廠商須分攤專利維護費者。" /></div>
                                        </td>
                                        <td>
                                            <div class="font-title titlebackicon">
                                                <asp:CheckBox ID="CB_特殊費用負擔" runat="server" Text="" Enabled="false" />
                                            </div>
                                            <asp:TextBox ID="TB_特殊費用負擔原因" runat="server" Height="38px" TextMode="MultiLine" Width="300px" Enabled="false"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PL_關鍵技術項目" runat="server">
                                    <tr>
                                        <td align="right" class="auto-style1"> <div class="font-title titlebackicon">是否涉及<br />國家核心關鍵技術項目</div></td>
                                        <td colspan="3">
                                            <div class="font-title titlebackicon">
                                                <font color='red'><b>是</b></font>／列管迄日:<b><asp:Label ID="LB_核心關鍵技術_列管迄日" runat="server" Text=""></asp:Label></b><br />
                                                列管之關鍵技術項目(請說明)：<br />
                                                <asp:TextBox ID="TB_核心關鍵技術_說明" runat="server" Height="50px" TextMode="MultiLine" Width="600px" Enabled="false"></asp:TextBox>
                                            </div>
                                        </td>
                                    </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_CoPromoter" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">協同承辦人</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:Label ID="txt_px_name" runat="server" Text="" />
                                                <asp:HiddenField ID="h_px_empno" runat="server" />
                                                <asp:Label ID="LB_adm" runat="server" Text=""></asp:Label>
                                                君 
                         <asp:PlaceHolder ID="PH_rb_adm" runat="server" Visible="false">&
                             <asp:RadioButton ID="rb_adm_yes" runat="server" Text="同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                             <asp:RadioButton ID="rb_adm_no" runat="server" Text="不同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                             <font style="color: red; font-weight: bold;">『同意貴單位所指定之業務窗口: <span style="color: red"><asp:Label id="LB_adm_text" runat="server" ForeColor="Red"></asp:Label></span> 君，與您有相同之權限』</font>
                         </asp:PlaceHolder>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_olderVer" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">簽約緣由與目的</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txtSignReason" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_txtSignReason" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_txtSignReason" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    智權歸屬<br />
                                                    (技術服務/合作開發)<br />
                                                </div>
                                                <td colspan="3">
                                                    <asp:RadioButton ID="rb_ipb_itri" runat="server" Text="本院" GroupName="ipb" Enabled="false"></asp:RadioButton><br />
                                                    <asp:RadioButton ID="rb_ipb_coparcenary" runat="server" Text="共有" GroupName="ipb" Enabled="false"></asp:RadioButton>(本院/客戶：
				    <asp:Label ID="txt_ipbi_percent" runat="server"></asp:Label>%&nbsp;/&nbsp;
				    <asp:Label ID="txt_ipbc_percent" runat="server"></asp:Label>%)<br />
                                                    <asp:RadioButton ID="rb_ipb_customer" runat="server" Text="客戶" GroupName="ipb" Enabled="false"></asp:RadioButton><br />
                                                    <asp:RadioButton ID="rb_ipb_other" runat="server" Text="其他" GroupName="ipb" Enabled="false"></asp:RadioButton>
                                                    <asp:Label ID="txt_ipb_other_desc" runat="server"></asp:Label>
                                                </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    智權運用<br />
                                                    <img src="../images/icon_tips.png" class="itemhint" title="智權運用<hr>當智權歸屬非本院所有時，應於契約中規範未來雙方之運用方式。<br>請填入客戶及貴單位之主要意見，以作為契約條文研擬參考。" />
                                                </div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txt_ip_apply" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_ip_apply" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_ip_apply" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    收益分享(共有)<br />
                                                    <img src="../images/icon_tips.png" class="itemhint" title="收益分享(共有)<hr>當智權歸屬非本院所有時，應於契約中規範未來當雙方再運用且有收益發生時，雙方之分享方式。<br>請填入客戶及貴單位之主要意見，以作為契約條文研擬參考。" />
                                                </div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txt_income_divvy" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_income_divvy" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_income_divvy" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">責任範圍 </div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:RadioButton ID="rb_duty_plain" runat="server" Text="計畫經費之" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_plain_budget" runat="server"></asp:Label>
                                                %
                                                <br />
                                                <asp:RadioButton ID="rb_duty_capital" runat="server" Text="最高賠償金額(TWD)" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_capitalsum" runat="server"></asp:Label>
                                                元
                                                <br />
                                                <asp:RadioButton ID="rb_duty_assumpsit" runat="server" Text="賠償客戶之一切損失（無上限賠償）" GroupName="res" Enabled="false"></asp:RadioButton><br />
                                                <asp:RadioButton ID="rb_duty_other" runat="server" Text="其他" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_other_desc" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC" runat="server"></asp:PlaceHolder>
                                    <%--<asp:SqlDataSource ID="SDS_sRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">其他需求</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:PlaceHolder ID="PH_oRC_new" runat="server" Visible="false">
                                                <asp:CheckBox ID="rb_other_1" runat="server" Text="本案契約與前案號　" Enabled="false" />&nbsp;<asp:TextBox ID="txt_otherrequire_contno" class="inputsizeS" runat="server" Enabled="false" />&nbsp;之契約相同,承辦法務同仁為&nbsp;<asp:TextBox ID="TB_otherrequire_handle_name" class="inputsizeS" runat="server" Enabled="false" Width="110px" /><br />
                                                <asp:CheckBox ID="rb_other_2" runat="server" Text="本案前已與法務同仁" Enabled="false" />&nbsp;<asp:TextBox ID="txt_otherrequire_asked_name" class="inputsizeS" runat="server" Enabled="false" />&nbsp;討論,請分案予前述法務同仁<br />
                                                <asp:CheckBox ID="rb_other_3" runat="server" Text="本案請法務同仁僅提供法律原則意見,毌庸修改契約文字" Enabled="false" /><br />
                                                <asp:CheckBox ID="rb_other_4" runat="server" Text="請法務同仁僅提供本院常用契約(草稿)供參考" Enabled="false" /><br />
                                            </asp:PlaceHolder>
                                            <asp:CheckBox ID="rb_other_T" runat="server" Text="其他。" Enabled="false" />
                                            <asp:TextBox ID="txt_otherrequire_desc" runat="server" Width="530px" TextMode="MultiLine" Height="60px" Enabled="false"></asp:TextBox>
                                            <%--<asp:SqlDataSource ID="SDS_oRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Button ID="BT_trfileup" class="ajax_trFile genbtnS" runat="server" Text="新增" Visible="false" />
                                            <asp:Button ID="BT_FileUp" class="ajax_mesg genbtnS" runat="server" Text="結件後補掛附件" Visible="false" />
                                            <input id="BT_OpenFileCompare" class="ajax_mesg genbtnS" onclick="openFileCompare(this)" runat="server" type="button" value="文件版本比對" />
                                            <input id="BT_OpenWordCheck" class="genbtnS" onclick="WordCheck()" runat="server" type="button" value="文件錯別字檢查" />
                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode( Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_no").ToString() )  %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="修改概要">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetxt").ToString())%>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="280px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="審查">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_inspect" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_inspect").ToString()) %>'></asp:Label>
                                                                <asp:Label ID="LB_tcdf_type" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_type").ToString()) %>' Visible="false"></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="30px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="常用版本">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_3" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="50px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--<asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PH_degree_Z" runat="server">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">法務承辦人意見彙整</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txt_betsum" runat="server" Width="608px" TextMode="MultiLine" Height="30px" Font-Size="Medium"></asp:TextBox>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PH_degree_C" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">需求取消原因</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txtRequestCancel" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="Image4" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" />
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_Inspect" runat="server" Visible="false">
                                        <tr valign="top">
                                            <td align="right">
                                                <div class="font-title titlebackicon">審查資訊</div>
                                            </td>
                                            <asp:PlaceHolder ID="PL_Inspect_法務" runat="server">
                                                <td>
                                                    <asp:Button class="ajax_mesg genbtnS" ID="BT_Inspect" runat="server" Text="案件審查" Visible="False" />
                                                    <asp:Button ID="BT_AddInspect" runat="server" Text="新增審查人" class="ajax_mesg genbtnS" />
                                                    <span class="stripeMe">
                                                        <asp:GridView ID="GV_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="GV_Inspect_RowDataBound" OnRowCommand="GV_Inspect_RowCommand" Width="350px">
                                                            <Columns>
                                                                <asp:TemplateField HeaderText="功能">
                                                                    <ItemTemplate>
                                                                        <asp:Label ID="LB_tci_no" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode( Eval("tci_no").ToString())  %>' Visible="false"></asp:Label>
                                                                        <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%#System.Web.HttpUtility.HtmlEncode(Eval("tci_no").ToString() )  %>' Visible="false">刪除</asp:LinkButton>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                                                </asp:TemplateField>
                                                                <asp:BoundField DataField="tci_order" HeaderText="順序">
                                                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                                </asp:BoundField>
                                                                <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                    <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                </asp:BoundField>
                                                                <asp:BoundField DataField="tci_inspect_desc" HeaderText="簽核意見">
                                                                    <ItemStyle Width="170px" />
                                                                </asp:BoundField>
                                                                <asp:TemplateField HeaderText="簽核<br>狀態">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_Istatus" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                </asp:TemplateField>
                                                                <asp:BoundField DataField="short_inspect_time" HeaderText="簽核日期">
                                                                    <ItemStyle HorizontalAlign="center" Width="140px" />
                                                                </asp:BoundField>
                                                            </Columns>
                                                            <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                            <PagerSettings Position="Bottom" />
                                                            <PagerStyle HorizontalAlign="Left" />
                                                        </asp:GridView>
                                                        <%--<asp:SqlDataSource ID="SDS_Inspect" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                        <%--<asp:SqlDataSource ID="SDS_Inspect_count" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                    </span>
                                                </td>
                                            </asp:PlaceHolder>
                                            <asp:PlaceHolder ID="PL_Inspect_計價" runat="server" Visible="false">
                                                <td align="right">
                                                    <div class="font-title titlebackicon">計價資訊</div>
                                                </td>
                                                <td align="left">
                                                    <table border="0" cellspacing="0" cellpadding="0" width="450px">
                                                        <tr>
                                                            <td colspan="2" with="100%" hight="20">承辦人：
                                                                <asp:Label ID="LB_承辦人" runat="server"></asp:Label>
                                                                <asp:Panel ID="PH_指派" runat="server" Visible="false" Style="margin-left: 80px; margin-top: -20px">
                                                                    <%--DataSourceID="SDS_計價承辦人"--%>
                                                                    <asp:DropDownList ID="DDL_計價承辦人" runat="server" Width="100px" DataTextField="Text" DataValueField="Value" AppendDataBoundItems="True" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate_計價">
                                                                        <asp:ListItem Value="">--請指派--</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                    <%--<asp:SqlDataSource ID="SDS_計價承辦人" runat="server"   />--%>
                                                                </asp:Panel>
                                                                <div style="float: right">
                                                                    <asp:Button ID="BT_計價" runat="server" Text="計價維護" class="ajax_vmesg genbtnS" Visible="false" />
                                                                </div>
                                                                <div style="float: right; margin-top: -10px">
                                                                    計價進度:
                                        <asp:DropDownList ID="DDL_計價進度" runat="server" Enabled="false">
                                            <asp:ListItem Value="0">計價承辦 </asp:ListItem>
                                            <asp:ListItem Value="1">草稿  </asp:ListItem>
                                            <asp:ListItem Value="3">審查  </asp:ListItem>
                                            <asp:ListItem Value="Z">完成  </asp:ListItem>
                                            <asp:ListItem Value="C">取消 </asp:ListItem>
                                            <asp:ListItem Value="U">不同意 </asp:ListItem>
                                        </asp:DropDownList>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="40%">參考價：
                                                                <asp:Label ID="TB_參考價" runat="server"></asp:Label>
                                                                元</td>
                                                            <td width="60%">，盡職調查結果: 
                                                <asp:DropDownList ID="DDL_盡職調查結果" runat="server" Enabled="false">
                                                    <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                                    <asp:ListItem Value="1">綠</asp:ListItem>
                                                    <asp:ListItem Value="5">黃</asp:ListItem>
                                                    <asp:ListItem Value="9">紅</asp:ListItem>
                                                </asp:DropDownList>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="40%">底價：
                                                                <asp:Label ID="TB_底價" runat="server"></asp:Label>
                                                                元</td>
                                                            <td width="60%">，<asp:CheckBox ID="CB_底價_無" runat="server" Text="無　說明：" Enabled="false" />
                                                                <asp:TextBox ID="TB_底價_無_說明" runat="server" Width="120px" Height="30px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td width="40%">第三方鑑價：
                                                                <asp:Label ID="TB_第三方鑑價" runat="server"></asp:Label>
                                                                元</td>
                                                            <td width="60%">，<asp:CheckBox ID="CB_第三方鑑價_無" runat="server" Text="無　說明：" Enabled="false" />
                                                                <asp:TextBox ID="TB_第三方鑑價_無_說明" runat="server" Width="120px" Height="30px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2" with="100%">其他說明：
                               <div style="float: right">
                                   <asp:PlaceHolder ID="PH_計價歷程" runat="server" Visible="false">
                                       <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_計價歷程" runat="server">計價歷程</asp:Literal>
                                   </asp:PlaceHolder>
                               </div>
                                                                <br />
                                                                <asp:TextBox ID="TB_其他說明" runat="server" Width="400px" Height="40px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="td_right" colspan="2">
                                                                <span class="stripeMe">
                                                                    <asp:GridView ID="gv_vdoc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_vdoc_file_RowCommand" OnRowDataBound="gv_vdoc_file_RowDataBound">
                                                                        <Columns>
                                                                            <asp:TemplateField HeaderText="附件名稱">
                                                                                <ItemTemplate>
                                                                                    <asp:LinkButton ID="LinkButton1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_no").ToString())  %>'> </asp:LinkButton>
                                                                                </ItemTemplate>
                                                                                <HeaderStyle Width="300px"></HeaderStyle>
                                                                                <ItemStyle HorizontalAlign="Left" />
                                                                            </asp:TemplateField>
                                                                            <asp:TemplateField HeaderText="說明">
                                                                                <ItemTemplate>
                                                                                    <asp:Label ID="LB_2" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_filetxt").ToString())%>'></asp:Label>
                                                                                </ItemTemplate>
                                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                                                <ItemStyle HorizontalAlign="Left" />
                                                                            </asp:TemplateField>
                                                                            <asp:TemplateField HeaderText="上傳資訊">
                                                                                <ItemTemplate>
                                                                                    <asp:Label ID="LB_4" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label><br>
                                                                                    <asp:Label ID="LB_1" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("時間").ToString()) %>' DataFormatString="{0:yyyy/MM/dd}" HtmlEncode="false"></asp:Label>
                                                                                </ItemTemplate>
                                                                                <HeaderStyle Width="70px"></HeaderStyle>
                                                                                <ItemStyle HorizontalAlign="Center" />
                                                                            </asp:TemplateField>
                                                                        </Columns>
                                                                        <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                                        <PagerSettings Position="Bottom" />
                                                                        <PagerStyle HorizontalAlign="Left" />
                                                                    </asp:GridView>
                                                                    <%--<asp:SqlDataSource ID="SDS_vgv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="td_right" colspan="2">
                                                                <div style="float: left">
                                                                    <asp:Button ID="BT_計價審查" runat="server" Text="計價審查" class="ajax_vmesg genbtnS" Visible="false" />
                                                                </div>

                                                                <span class="stripeMe">
                                                                    <asp:GridView ID="GV_Inspect_value" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="GV_Inspect_value_RowDataBound" OnRowCommand="GV_Inspect_value_RowCommand" Width="450px">
                                                                        <Columns>
                                                                            <asp:TemplateField HeaderText="功能">
                                                                                <ItemTemplate>
                                                                                    <asp:Label ID="LB_tci_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tcii_seno").ToString()) %>' Visible="false"></asp:Label>
                                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%#System.Web.HttpUtility.HtmlEncode((Int64)Eval("tcii_seno")  ) %>' Visible="false">刪除</asp:LinkButton>
                                                                                </ItemTemplate>
                                                                                <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                                                            </asp:TemplateField>
                                                                            <asp:BoundField DataField="tci_order" HeaderText="順序">
                                                                                <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                                            </asp:BoundField>
                                                                            <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                                <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                            </asp:BoundField>
                                                                            <asp:BoundField DataField="tci_審查意見" HeaderText="簽核意見">
                                                                                <ItemStyle HorizontalAlign="Left" Width="170px" />
                                                                            </asp:BoundField>
                                                                            <asp:TemplateField HeaderText="簽核<br>狀態">
                                                                                <ItemTemplate>
                                                                                    <asp:Literal ID="LB_Istatus" runat="server" Text='<%#  Server.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                                </ItemTemplate>
                                                                                <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                            </asp:TemplateField>
                                                                            <asp:BoundField DataField="short_inspect_time" HeaderText="簽核日期">
                                                                                <ItemStyle HorizontalAlign="center" Width="140px" />
                                                                            </asp:BoundField>
                                                                        </Columns>
                                                                        <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                                        <PagerSettings Position="Bottom" />
                                                                        <PagerStyle HorizontalAlign="Left" />
                                                                    </asp:GridView>
                                                                    <%--<asp:SqlDataSource ID="SDS_Inspect_value" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </asp:PlaceHolder>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_tc_manage_note" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">預估完成日展延</div>
                                            </td>
                                            <td>
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Defer" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False">
                                                        <Columns>
                                                            <asp:BoundField DataField="tcd_defer_date" HeaderText="展延後預估完成日">
                                                                <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_keyin_date" HeaderText="提出展延日">
                                                                <ItemStyle HorizontalAlign="Center" Width="120px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_desc" HeaderText="展延原因">
                                                                <ItemStyle Width="530px" HorizontalAlign="Left" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無展延! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Defer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">法務備註</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txtManageNote" runat="server" Width="608px" TextMode="MultiLine" Height="60px" onchange="updateTitle(this)" ReadOnly="true"></asp:TextBox>
                                                <%--                    <asp:Image ID="Image3" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" title=""/>--%>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">退件</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txt_reject" runat="server" Width="608px" TextMode="MultiLine" Height="60px" Visible="false"></asp:TextBox>
                                                <asp:Button runat="server" class="ajax_mesg genbtnS" ID="bt_reject" Text="退件" OnClick="bt_reject_Click" Visible="false" />
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td class="td_right" colspan="5">
                                        <div style="float: left">
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="bt_cancle" Text="需求取消" OnClick="bt_cancle_Click" Visible="false" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btnDelete" Text="刪除" OnClick="btnDelete_Click" Visible="False" />
                                        </div>
                                        <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_infoHandel" runat="server">歷次承辦人資訊</asp:Literal>
                                        &nbsp;&nbsp;
               <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_historyRecord" runat="server">歷次修改紀錄</asp:Literal><a href="#dialog06" class="inlineS"></a>
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_caseAssign" Text="分案指派" Visible="False" />
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print" Text="列印" OnClick="BT_Print_Click" Visible="False" />
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Tag" Text="列印檔案標籤" OnClick="BT_Print_Tag_Click" Visible="False" />
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_Print_Excel" Text="列印檔案標籤_Excel" OnClick="BT_Print_Excel_Click" Visible="False" />
                                        <asp:Button runat="server" class="ajax_Satisf genbtnS" ID="BT_Satisf" Text="滿意度調查" Visible="False" />
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit2" Text="編輯" OnClick="btEdit_Click" Visible="False" />
                                        <asp:Button runat="server" class="genbtnS" ID="BT_End" Text="結案通知" OnClick="BT_End_Click" Visible="false"></asp:Button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">分案主管</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件/分案日期</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>&nbsp;&nbsp;/&nbsp;&nbsp;<asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">進度</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal><asp:DropDownList ID="DDL_Degree" runat="server" Visible="false" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_DDL_degree" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">協同法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_cop" runat="server"></asp:Literal><%--<asp:SqlDataSource ID="SDS_cop" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">處理天數</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_process_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">產出文件數</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_contract_count" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">預估/需求結件日</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_expect_close_date" runat="server" />&nbsp;&nbsp;/&nbsp;&nbsp;<asp:Literal ID="lb_case_closedate" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改日期</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc2:Foot runat="server" ID="Foot" />
        <%--    <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_ver" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_report" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_report_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
    <asp:SqlDataSource ID="SDS_公文文號" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            tinymce.init({
                selector: '#txt_betsum',
                width: "800",
                height: "500",
                menubar: false,
                statusbar: false,
                toolbar: false,
                menubar: false,
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
                readonly: 1
            });
            //tinyMCE.init({
            //    // General options
            //    mode: "exact",
            //    elements: "txt_betsum",
            //    theme: "advanced",
            //    width: "700",
            //    height: "500",
            //    readonly: true
            //});

            $(document).ready(function () {

                $(document).bind('cbox_open', function () { $('html').css({ overflow: 'hidden' }); })
                    .bind('cbox_closed', function () { $('html').css({ overflow: 'auto' }); });
                $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_otherrequire_desc").attr("title", $("#txt_otherrequire_desc").val());
                $(".help_otherrequire_desc").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txt_manage_note").val());
                $(".help_manage_note").attr("class", "help_manage_note itemhint");
                $('a.iterm_dymanic').cluetip({ width: '830px', showTitle: false, ajaxCache: false });
                $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_company').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });
                $('a.iterm_dymanic_Valuation').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });

                $(".itemhint").tooltip(
                    {
                        track: true, effect: "slideDown", delay: 250,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () { return $(this).prop('title'); }
                    }
                );
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });

            });
            function updateTitle(me) { me.title = me.value; }
            $('#txtManageNote').mouseover(function () {
                $(this).attr('title', $(this).val())
            })
            $("input[type='checkbox']").click(
                function () {
                    this.checked = !this.checked;
                }
            );

            $(document).ready(function () {
                jQuery('#Form1').validationEngine({});

            });
       // autosize(document.querySelectorAll('textarea'));
        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
