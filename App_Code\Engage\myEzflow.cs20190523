﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using System.Xml;

namespace Engage
{
	/// <summary>
	/// Summary description for myEzflow
	/// </summary>
	public class myEzflow : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private long _seqsn;
		private int _ver = 0;
		private string _empno;
		private string _empname;

		private string _formtype;
		private string _formno;
		private string _ecp_guid;

		private string _signstatus;

		#endregion

		#region 公有屬性

		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 洽案流水號
		/// </summary>
		public long Seqsn
		{
			get { return _seqsn; }
			set { _seqsn = value; }
		}

		/// <summary>
		/// 版次
		/// </summary>
		public int Ver
		{
			get { return _ver; }
			set { _ver = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		/// <summary>
		/// 表單類別, EG01:立案送簽, EG04:停洽送簽, EG03:成本訂價送簽, EG06:契約簽辦送簽, EG08:契約簽辦審查送簽
		/// </summary>
		public string FormType
		{
			get { return _formtype; }
			set { _formtype = value; }
		}

		/// <summary>
		/// 表單編號
		/// </summary>
		public string FormNo
		{
			get { return _formno; }
			set { _formno = value; }
		}

		/// <summary>
		/// 表單流水號
		/// </summary>
		public string Ecp_guid
		{
			get { return _ecp_guid; }
			set { _ecp_guid = value; }
		}

		/// <summary>
		/// 2:同意 3:不同意 4:抽單 P:轉紙本
		/// </summary>
		public string SignStatus
		{
			get { return _signstatus; }
			set { _signstatus = value; }
		}

		//簽核
		public string GUID = "";
		public int Seq = 0;
		public string ActRoleName = "";
		public string RecUserID = "";
		public string RecUserName = "";
		public string SignType = "1";
		public string SignSigle = "Y";
		public string SignClass = "";
		public string IS_LOCK = "";

		public string NoticeEmpno = "";
		public string EcpMemo = "";


		#endregion

		public myEzflow()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		#region 取得簽核流程的 ECP_guid
		/// <summary>
		/// 取得簽核流程的 ECP_guid
		/// </summary>
		/// <returns></returns>
		public string Get_ECP_guid()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @formtype='EG01'
	select eb_ecp_eg01_guid from engage_base where eb_seqsn=@seqsn
else if @formtype='EG04'
	select eb_ecp_eg04_guid from engage_base where eb_seqsn=@seqsn
else if @formtype='EG03'
	select cost_ecp_eg03_guid from engage_cost where cost_seqsn=@seqsn
else if @formtype='EG06'
	select esc_ecp_eg06_guid from engage_signcont where esc_seqsn=@seqsn
else if @formtype='EG08'
	select esc_ecp_eg08_guid from engage_signcont where esc_seqsn=@seqsn
else if @formtype='EG07'
	select esc_ecp_eg07_guid from engage_signcont where esc_seqsn=@seqsn

";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@formtype", _formtype);

			try
			{
				string data = this.getTopOne(oCmd, CommandType.Text);
				return data;
			}
			catch
			{
				return "";
			}
		}
		#endregion

		#region 取得簽核意見, GetECP_command()

		/// <summary>
		/// 取得簽核意見
		/// </summary>
		/// <returns></returns>
		public DataTable GetECP_command()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @formno varchar(10)

if @formtype='EG01'
	select @formno=eb_formno from engage_base where eb_seqsn=@seqsn
else if @formtype='EG04'
	select @formno=eb_back_formno from engage_base where eb_seqsn=@seqsn
else if @formtype='EG03'
begin
	if exists(select * from engage_cost where cost_seqsn=@seqsn and (cost_ver=@ver or @ver=0))
		select @formno=cost_formno from engage_cost where cost_seqsn=@seqsn
	else
		select @formno=cost_formno from engage_his.dbo.engage_cost where cost_seqsn=@seqsn and cost_ver=@ver
end
else if @formtype='EG06'
begin
	if exists(select * from engage_signcont where esc_seqsn=@seqsn and (esc_ver=@ver or @ver=0))
		select @formno=esc_formno from engage_signcont where esc_seqsn=@seqsn
	else
		select @formno=esc_formno from engage_his.dbo.engage_signcont where esc_seqsn=@seqsn and esc_ver=@ver
end
else if @formtype='EG08'
begin
	if exists(select * from engage_signcont where esc_seqsn=@seqsn and (esc_ver=@ver or @ver=0))
		select @formno=esc_formno_eg08 from engage_signcont where esc_seqsn=@seqsn
	else
		select @formno=esc_formno_eg08 from engage_his.dbo.engage_signcont where esc_seqsn=@seqsn and esc_ver=@ver
end
else if @formtype='EG07'
begin
	if exists(select * from engage_signcont where esc_seqsn=@seqsn and (esc_ver=@ver or @ver=0))
		select @formno=esc_formno_eg07 from engage_signcont where esc_seqsn=@seqsn
	else
		select @formno=esc_formno_eg07 from engage_his.dbo.engage_signcont where esc_seqsn=@seqsn and esc_ver=@ver
end

exec pr_engage_ecp_command @formtype, @formno
";

			oCmd.Parameters.AddWithValue("@formtype", _formtype);
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetECP_command2()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
exec pr_engage_ecp_command @formtype, @formno
";

			oCmd.Parameters.AddWithValue("@formtype", _formtype);
			oCmd.Parameters.AddWithValue("@formno", _formno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetECP_main()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select * from engage_ecp_main where ecp_guid = @PARENTGUID
";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		#endregion


		#region 取得簽核預覽流程
		/// <summary>
		/// 取得簽核預覽流程
		/// </summary>
		/// <returns></returns>
		public DataTable Get_preflow()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	select 
		[GUID]
		,[PARENTGUID]
		,[Seq]
		,[ActRoleName]
		,[RecUserID]
		,[RecUserName]
		,[RecTime]
		,[SignType]
		,[SignSigle]
		,[SignStatus]
		,[SetUserID]
		,[SetUserName]
		,[NoticeEmpno]
		--,RTRIM(com_cname) as [NoticeName]
		,(select RTRIM(com_cname)+' ' from dbo.fn_split_no_repeat(NoticeEmpno,',') left join common.dbo.comper ON com_empno =[Value] for xml path('')) as [NoticeName]
		,[SignClass]
		,[IS_LOCK]
	  from [engage_ecp_preflow] 
	  where PARENTGUID=@PARENTGUID 
	  order by [Seq] asc
		";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 產生簽核預覽流程
		/// <summary>
		/// 產生簽核預覽流程
		/// </summary>
		/// <returns>表單Guid</returns>
		public string Gen_preflow()
		{
			string guid = string.Empty;

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_gen_preflow";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@formtype", _formtype);
			oCmd.Parameters.AddWithValue("@keyinemp", _empno);
			SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
			rtn_flag.Direction = ParameterDirection.Output;

			this.Execute(oCmd, CommandType.StoredProcedure);
			if (rtn_flag.Value.ToString() == "0")
			{
				//成功則回傳guid
				guid = Get_ECP_guid();
			}
			return guid;
		}
		#endregion

		#region 刪除簽核預覽流程(重新產生簽核流程前的刪除)
		/// <summary>
		/// 刪除簽核預覽流程(重新產生簽核流程前的刪除)
		/// </summary>
		/// <returns></returns>
		public bool Delete_preflow()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_del_preflow";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);
			SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
			rtn_flag.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = (rtn_flag.Value.ToString() == "0");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 刪除簽核流程(送簽前的刪除)
		/// <summary>
		/// 刪除簽核流程(送簽前的刪除)
		/// </summary>
		/// <returns></returns>
		public bool Delete_signflow2()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete from engage_ecp_preflow where [GUID]=@GUID
";

			oCmd.Parameters.AddWithValue("@GUID", GUID);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 新增簽核預覽流程
		/// <summary>
		/// 新增簽核預覽流程
		/// </summary>
		/// <returns></returns>
		public bool Insert_preflow()
		{
			bool success = false;

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists (select * from engage_ecp_preflow where [GUID]=@GUID and PARENTGUID=@PARENTGUID)
begin
	insert into engage_ecp_preflow
		(PARENTGUID, Seq, ActRoleName, RecUserID, RecUserName,
		SignType, SignSigle, SignClass, IS_LOCK,
		SetUserID, SetUserName)
	select
		@PARENTGUID, @Seq, @ActRoleName, @RecUserID, @RecUserName,
		@SignType, @SignSigle, @SignClass, @IS_LOCK,
		@modempno, @modempname
end
else
begin
	update engage_ecp_preflow set 
			Seq=@Seq, ActRoleName=@ActRoleName, RecUserID=@RecUserID, RecUserName=@RecUserName
			,SignType=@SignType, SignSigle=@SignSigle, SignClass=@SignClass, IS_LOCK=@IS_LOCK
			,SetUserID=@modempno, SetUserName=@modempname
	where [GUID]=@GUID and PARENTGUID=@PARENTGUID
end
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@modempno", _empno);
			oCmd.Parameters.AddWithValue("@modempname", _empname);
			oCmd.Parameters.AddWithValue("@GUID", GUID);
			oCmd.Parameters.AddWithValue("@PARENTGUID", Ecp_guid);
			oCmd.Parameters.AddWithValue("@Seq", Seq);
			oCmd.Parameters.AddWithValue("@ActRoleName", ActRoleName);
			oCmd.Parameters.AddWithValue("@RecUserID", RecUserID);
			oCmd.Parameters.AddWithValue("@RecUserName", RecUserName);
			oCmd.Parameters.AddWithValue("@SignType", SignType);
			oCmd.Parameters.AddWithValue("@SignSigle", SignSigle);
			oCmd.Parameters.AddWithValue("@SignClass", SignClass);
			oCmd.Parameters.AddWithValue("@IS_LOCK", IS_LOCK);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新-送簽填表人
		/// <summary>
		/// 更新-送簽填表人
		/// </summary>
		/// <returns></returns>
		public bool Update_SendEmpno()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_ecp_main set ecp_sendEmpno=@empno, ecp_sendEmpname=@empname, ecp_sendDate=getdate() where ecp_guid=@PARENTGUID
";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新-通知人員
		/// <summary>
		/// 更新-通知人員
		/// </summary>
		/// <returns></returns>
		public bool Update_NoticeEmpno()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @maxSeq int 
select @maxSeq=MAX(Seq) from engage_ecp_preflow where PARENTGUID=@PARENTGUID
update engage_ecp_preflow set NoticeEmpno=null where PARENTGUID=@PARENTGUID and Seq<>@maxSeq
update engage_ecp_preflow set NoticeEmpno=@NoticeEmpno where PARENTGUID=@PARENTGUID and Seq=@maxSeq
update engage_ecp_main set ecp_NoticeEmpno=@NoticeEmpno, ecp_memo=@EcpMemo where ecp_guid=@PARENTGUID
";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);
			oCmd.Parameters.AddWithValue("@NoticeEmpno", NoticeEmpno);
			oCmd.Parameters.AddWithValue("@EcpMemo", EcpMemo);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 送簽失敗，執行刪除-該 ECP 所有關卡
		/// <summary>
		/// call ECP 失敗時,執行刪除-該 ECP 所有關卡
		/// </summary>
		/// <returns></returns>
		public void EcpFail_ToDelEGFlow()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_fail_del_EG_FlowD";
			oCmd.Parameters.AddWithValue("@GUID", _ecp_guid);
			this.Execute(oCmd, CommandType.StoredProcedure);
		}
		#endregion

		#region 確認是否可以送簽-電子表單
		/// <summary>
		/// 確認是否可以送簽-電子表單
		/// </summary>
		/// <returns>True:可以送簽; False:不可以送簽</returns>
		public bool Check_EcpCanSend()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_can_send";

			oCmd.Parameters.AddWithValue("@GUID", _ecp_guid);
			SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
			rtn_flag.Direction = ParameterDirection.Output;

			this.Execute(oCmd, CommandType.StoredProcedure);
			success = (rtn_flag.Value.ToString() == "Y");
			return success;
		}
		#endregion


		#region 送簽，呼叫創單 Web Service.
		/// <summary>
		/// 送簽，呼叫創單 Web Service.
		/// </summary>
		/// <returns></returns>
		public bool Send_CreateSheet()
		{
			bool success = false;

			//先更新engage_ecp_main.ecp_sendEmpno, ecp_sendEmpname 填表人
			success = Update_SendEmpno();
			if (success)
			{
				string pGUID = this.Ecp_guid;
				string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.sysid"];
				string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.secureid"];
				string strSignMsg = "";

				if (FormType == "EG01")
				{
					//呼叫創單 Web Service.
					ECP.EG01 EG = new ECP.EG01();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG01"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else if (FormType == "EG03")
				{
					//呼叫創單 Web Service.
					ECP.EG03 EG = new ECP.EG03();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG03"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else if (FormType == "EG04")
				{
					//呼叫創單 Web Service.
					ECP.EG04 EG = new ECP.EG04();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG04"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else if (FormType == "EG06")
				{
					//呼叫創單 Web Service.
					ECP.EG06 EG = new ECP.EG06();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG06"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else if (FormType == "EG07")
				{
					//呼叫創單 Web Service.
					ECP.EG07 EG = new ECP.EG07();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG07"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else if (FormType == "EG08")
				{
					//呼叫創單 Web Service.
					ECP.EG08 EG = new ECP.EG08();
					EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.EG08"];

					// 呼叫web service創單
					strSignMsg = EG.EGCreate(pGUID, sysid, secureid);
				}
				else
				{
					_returnMessage = "錯誤的表單類別!";
					return false;
				}

				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(strSignMsg);

				strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText;//取得回傳xml字串中的result

				if ("Y".Equals(strSignMsg))
				{
					string SheetNo = xmlDoc.SelectSingleNode("//SheetNo").InnerText; //表單單號
					this.FormNo = SheetNo;
					success = Send_Ezflow_After_Update();
				}
				else
				{
					// 當 call ECP 失敗時,執行以下SP, EXEC [dbo].[pr_engage_fail_del_EG_FlowD] @GUID=N'123'
					// 2015/04/22:Hugo(Mark),先不呼叫此SP.
					//this.EcpFail_ToDelEGFlow();

					_returnMessage = "WS創單失敗!";
					success = false;
				}
			}
			return success;
		}
		#endregion

		#region 呼叫創單的 Web Service 成功後, 更新相關資料欄位.
		/// <summary>
		/// 呼叫創單的 Web Service 成功後, 更新相關資料欄位. Call SP:[pr_engage_ecp_Send_After_Update]
		/// </summary>
		/// <returns></returns>
		public bool Send_Ezflow_After_Update()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_Send_After_Update";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);
			oCmd.Parameters.AddWithValue("@formno", _formno);
			oCmd.Parameters.AddWithValue("@sendempno", _empno);
			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			msg.Direction = ParameterDirection.Output;
			SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
			rtn_flag.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = (rtn_flag.Value.ToString() == "0");
				_returnMessage = msg.Value.ToString();
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion


		#region 取得轉紙本ECP
		/// <summary>
		/// 取得轉紙本ECP
		/// </summary>
		/// <returns></returns>
		public DataTable GetECP_TopPaper_EG()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_topaper_EG";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		#endregion

		#region 轉紙本簽核
		/// <summary>
		/// 轉紙本簽核(EG06)
		/// </summary>
		/// <returns></returns>
		public bool Send_ToPaper()
		{
			bool success = false;

			this._formtype = "EG06";
			this._ecp_guid = this.Get_ECP_guid();
			DataTable dt = this.GetECP_TopPaper_EG();
			if (dt.Rows.Count > 0)
			{
				DataRow row = dt.Rows[0];

				string userID = this._empno;							//使用者ID, 必填
				string workItemOID = row["workItemOID"].ToString();		//工作項目OID, 必填 
				string executiveResult = "Y";							//意見表達結果Y=同意,N=不同意,必填
				string executiveComment = "轉紙本簽核";					//簽核意見, 可空白
				string serialNumber = row["serialNumber"].ToString();	//流程實例序號, 必填

				string addUserID = "";									//加簽人員ID，可加入多人，以#字號隔開	可空白
				string addActName = "";									//加簽人員角色名稱	可空白，但addUserID不為空值時必填
				string isFIREST_GET_FIRST_WIN = "Y";					// 是否單一簽核:Y:是;N:否， 預設值為N	必填
				string FormID = row["main001"].ToString();				//傳入表單代號	必填
				string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.sysid"];
				string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.secureid"];

				ECP.ITRIPublicWebService EG = new ECP.ITRIPublicWebService();
				EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.ITRIPublicWebService"];

				String strSignMsg = EG.completeWorkItem(userID, workItemOID, executiveResult, executiveComment, serialNumber, addUserID, addActName, isFIREST_GET_FIRST_WIN, FormID, sysid, secureid);
				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(strSignMsg);

				strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText;//取得回傳xml字串中的result

				if ("Y".Equals(strSignMsg))
				{
					//this.SignStatus = "P";	//P:轉紙本
					//success = UpdateData_EG();
					//if (success)
					//{
						//GP流程終止掉 ,更新flowD狀態
						DateTime dtTime = DateTime.Now;
						string SignTime = dtTime.ToString("yyyy/MM/dd HH:mm:ss");
						string Result = "";
						if (executiveResult.Equals("Y"))
						{
							Result = "2";
						}
						else
						{
							Result = "3";
						}

						string msg1 = updateECPflowD(this._ecp_guid, userID, Result, executiveComment, SignTime);
						if ("0".Equals(msg1) != true)
						{
							success = false;
							_errorMessage = "updateECPflowD 更新失敗";
						}
					//}
						this.SignStatus = "P";	//P:轉紙本
						success = UpdateData_EG();
				}
				else
				{
					//_errorMessage = "呼叫 WS 中止.";
					_errorMessage = xmlDoc.SelectSingleNode("//Message").InnerText.Replace("\n", "");
				}
			}
			else
			{
				_errorMessage = "呼叫 [pr_engage_topaper_EG] 失敗.";
			}

			return success;
		}

		#region UpdateData_EG()
		/// <summary>
		/// 洽案系統--簽核/結案/抽單時－需更新外部系統資料的API
		/// </summary>
		/// <returns></returns>
		public bool UpdateData_EG()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_UpdateData_EG";

			oCmd.Parameters.AddWithValue("@PARENTGUID", _ecp_guid);
			oCmd.Parameters.AddWithValue("@GUID", _empno);
			oCmd.Parameters.AddWithValue("@SignStatus", _signstatus);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#endregion

		#region  旭紳提供的 updateECPflowD()
		public string updateECPflowD(string pGUID, string userID, string Result, string executiveComment, string SignTime)
		{
			//SqlConnection strConn = new SqlConnection("server=140.96.1.27,5555;uid=ezflSF200;pwd=**********;database=WebFormPT;Pooling=true;Max Pool Size=999;");
			SqlConnection strConn = new SqlConnection(System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnString.ECP"].ConnectionString);

			DataSet ds2 = new DataSet();

			string sql2 = "";
			if (Result.Equals("2"))  //同意
			{
				sql2 = @"update EGMain
                        set Status='1' ,EndResult ='0'
                        where GUID=@guid

                        update EG_flowD
                        set  SignOpinionTxt = @executiveComment ,SignTime = @SignTime , SignStatus ='2'                        
                        where PARENTGUID=@guid and SignStatus ='0'

                        update EG_flowD
                        set SignUserID=@userID ,SignUserName=C.com_cname,SignOpinionTxt = @executiveComment ,SignTime = @SignTime, SignStatus ='2'
                        from EG_flowD E ,common..comper  C 
                        where C.com_empno=@userID and  PARENTGUID=@guid and SignStatus ='1'";

			}
			else
			{   //不同意

				sql2 = @"update EGMain
                        set Status='1' ,EndResult ='1'
                        where GUID=@guid

                        update EG_flowD
                        set SignOpinionTxt = @executiveComment ,SignTime = @SignTime ,SignStatus ='3'
                        where PARENTGUID=@guid and SignStatus ='0'

                        update EG_flowD
                        set SignUserID=@userID ,SignUserName=C.com_cname,SignOpinionTxt = @executiveComment ,SignTime = @SignTime, SignStatus ='3'
                        from EG_flowD E ,common..comper  C 
                        where C.com_empno=@userID and  PARENTGUID=@guid and SignStatus ='1'";
			}

			SqlCommand sqlCom2 = new SqlCommand(sql2, strConn);

			sqlCom2.CommandType = CommandType.Text;

			sqlCom2.Parameters.Add("@guid", SqlDbType.VarChar);
			sqlCom2.Parameters["@guid"].Value = pGUID;

			sqlCom2.Parameters.Add("@userID", SqlDbType.VarChar);
			sqlCom2.Parameters["@userID"].Value = userID;

			sqlCom2.Parameters.Add("@SignTime", SqlDbType.VarChar);
			sqlCom2.Parameters["@SignTime"].Value = SignTime;

			sqlCom2.Parameters.Add("@executiveComment", SqlDbType.VarChar);
			sqlCom2.Parameters["@executiveComment"].Value = executiveComment;

			sqlCom2.Parameters.Add("@Result", SqlDbType.VarChar);
			sqlCom2.Parameters["@Result"].Value = Result;

			sqlCom2.Connection.Open();
			SqlDataAdapter da2 = new SqlDataAdapter(sqlCom2);
			da2.Fill(ds2);
			sqlCom2.Connection.Close();

			if (ds2.Tables.Count == 0)
			{
				return "0";
			}
			else
			{
				return ds2.Tables[0].Rows[0][0].ToString();
			}
		}
		#endregion


		#region 契約簽辦修改
		/// <summary>
		/// 契約簽辦修改(EG06)
		/// </summary>
		/// <returns></returns>
		public bool Send_EG06_ModReason()
		{
			bool success = false;

			this._formtype = "EG06";
			this._ecp_guid = this.Get_ECP_guid();
			DataTable dt = this.GetECP_TopPaper_EG();
			if (dt.Rows.Count > 0)
			{
				DataRow row = dt.Rows[0];

				string userID = this._empno;							//使用者ID, 必填
				string workItemOID = row["workItemOID"].ToString();		//工作項目OID, 必填 
				string executiveResult = "N";							//意見表達結果Y=同意,N=不同意,必填
				string executiveComment = "重新簽辦";					//簽核意見, 可空白
				string serialNumber = row["serialNumber"].ToString();	//流程實例序號, 必填

				string addUserID = "";									//加簽人員ID，可加入多人，以#字號隔開	可空白
				string addActName = "";									//加簽人員角色名稱	可空白，但addUserID不為空值時必填
				string isFIREST_GET_FIRST_WIN = "Y";					// 是否單一簽核:Y:是;N:否， 預設值為N	必填
				string FormID = row["main001"].ToString();				//傳入表單代號	必填
				string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.sysid"];
				string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.secureid"];

				ECP.ITRIPublicWebService EG = new ECP.ITRIPublicWebService();
				EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.ITRIPublicWebService"];

				String strSignMsg = EG.completeWorkItem(userID, workItemOID, executiveResult, executiveComment, serialNumber, addUserID, addActName, isFIREST_GET_FIRST_WIN, FormID, sysid, secureid);
				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(strSignMsg);

				strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText;//取得回傳xml字串中的result

				if ("Y".Equals(strSignMsg))
				{
					this.SignStatus = "P";	//P:轉紙本
					success = UpdateData_EG06_preclose();
					if (success)
					{
						//GP流程終止掉 ,更新flowD狀態
						DateTime dtTime = DateTime.Now;
						string SignTime = dtTime.ToString("yyyy/MM/dd HH:mm:ss");
						string Result = "";
						if (executiveResult.Equals("Y"))
						{
							Result = "2";
						}
						else
						{
							Result = "3";
						}

						string msg1 = updateECPflowD(this._ecp_guid, userID, Result, executiveComment, SignTime);
						if ("0".Equals(msg1) != true)
						{
							success = false;
							_errorMessage = "updateECPflowD 更新失敗";
						}
					}
				}
				else
				{
					//_errorMessage = "呼叫 WS 中止.";
					_errorMessage = xmlDoc.SelectSingleNode("//Message").InnerText.Replace("\n", "");
				}
			}
			else
			{
				_errorMessage = "呼叫 [pr_engage_topaper_EG] 失敗.";
			}

			return success;
		}

		#region UpdateData_EG06_preclose()
		/// <summary>
		/// 洽案系統--EG06 簽約中,可以執行[簽辦修改], 將EG06狀態設為未簽核;未簽關卡設為不同意 
		/// </summary>
		/// <returns></returns>
		public bool UpdateData_EG06_preclose()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_EG06_preclose";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#endregion

		#region 觸發「產服中心計價服務」，本表單予以不同意結案。
		/// <summary>
		/// 觸發「產服中心計價服務」，本表單予以不同意結案。
		/// </summary>
		/// <returns></returns>
		public bool Send_CaseValuation()
		{
			bool success = false;

			//this._formtype = "EG06";
			this._ecp_guid = this.Get_ECP_guid();
			DataTable dt = this.GetECP_TopPaper_EG();
			if (dt.Rows.Count > 0)
			{
				DataRow row = dt.Rows[0];

				string userID = this._empno;							//使用者ID, 必填
				string workItemOID = row["workItemOID"].ToString();		//工作項目OID, 必填 
				string executiveResult = "N";							//意見表達結果Y=同意,N=不同意,必填
				string executiveComment = "不同意結案";					//簽核意見, 可空白
				string serialNumber = row["serialNumber"].ToString();	//流程實例序號, 必填

				string addUserID = "";									//加簽人員ID，可加入多人，以#字號隔開	可空白
				string addActName = "";									//加簽人員角色名稱	可空白，但addUserID不為空值時必填
				string isFIREST_GET_FIRST_WIN = "Y";					// 是否單一簽核:Y:是;N:否， 預設值為N	必填
				string FormID = row["main001"].ToString();				//傳入表單代號	必填
				string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.sysid"];
				string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.secureid"];

				ECP.ITRIPublicWebService EG = new ECP.ITRIPublicWebService();
				EG.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["ECP.ITRIPublicWebService"];

				String strSignMsg = EG.completeWorkItem(userID, workItemOID, executiveResult, executiveComment, serialNumber, addUserID, addActName, isFIREST_GET_FIRST_WIN, FormID, sysid, secureid);
				XmlDocument xmlDoc = new XmlDocument();
				xmlDoc.LoadXml(strSignMsg);

				strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText;//取得回傳xml字串中的result

				if ("Y".Equals(strSignMsg))
				{
					//GP流程終止掉 ,更新flowD狀態
					DateTime dtTime = DateTime.Now;
					string SignTime = dtTime.ToString("yyyy/MM/dd HH:mm:ss");
					string Result = "";
					if (executiveResult.Equals("Y"))
					{
						Result = "2";
					}
					else
					{
						Result = "3";
					}

					string msg1 = updateECPflowD(this._ecp_guid, userID, Result, executiveComment, SignTime);
					if ("0".Equals(msg1) != true)
					{
						success = false;
						_errorMessage = "updateECPflowD 更新失敗";
					}
					else
					{
						this.SignStatus = "3";
						success = UpdateData_EG();
					}
				}
				else
				{
					//_errorMessage = "呼叫 WS 中止.";
					_errorMessage = xmlDoc.SelectSingleNode("//Message").InnerText.Replace("\n", "");
				}
			}
			else
			{
				_errorMessage = "呼叫 [pr_engage_topaper_EG] 失敗.";
			}

			return success;
		}

		#endregion

	}

}