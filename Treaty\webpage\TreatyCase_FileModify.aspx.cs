﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;


public partial class webpage_TreatyCase_FileModify : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["fid"] = "";
            if (Request.QueryString["fid"] != null)
            {
                ViewState["fid"] = Request.QueryString["fid"].ToString();
                int j = 0;
                if (!(int.TryParse(ViewState["fid"].ToString(), out j)))
                    Response.Redirect("..\\danger.aspx");
                //SDS_fileType.SelectParameters.Clear();
                //SDS_fileType.SelectCommandType = SqlDataSourceCommandType.Text;
                //SDS_fileType.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'11' ";
                //SDS_fileType.DataBind();
                //DDL_FileType.DataBind();


                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@code_group", "");
                    sqlCmd.Parameters.AddWithValue("@code_type", "11");
                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);
                        DDL_FileType.DataSource = dt;
                        DDL_FileType.DataBind();
                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion


                //SDS_file.SelectParameters.Clear();
                //SDS_file.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_file.SelectCommand = "esp_TreatyCase_file_modify";
                //SDS_file.SelectParameters.Add("req_id", "");
                //SDS_file.SelectParameters.Add("fd_name", "");
                //SDS_file.SelectParameters.Add("filetxt", "");
                //SDS_file.SelectParameters.Add("file_url", "");
                //SDS_file.SelectParameters.Add("empno", "");
                //SDS_file.SelectParameters.Add("mode", "view");
                //SDS_file.SelectParameters.Add("fid", ViewState["fid"].ToString());
                //for (int i = 0; i < this.SDS_file.SelectParameters.Count; i++)
                //{
                //    SDS_file.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_file.DataBind();
                //System.Data.DataView dv = (DataView)SDS_file.Select(new DataSourceSelectArguments());

                #region --- query ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@req_id", "");
                    sqlCmd.Parameters.AddWithValue("@fd_name", "");
                    sqlCmd.Parameters.AddWithValue("@filetxt", "");
                    sqlCmd.Parameters.AddWithValue("@file_url", "");
                    sqlCmd.Parameters.AddWithValue("@empno", "");
                    sqlCmd.Parameters.AddWithValue("@mode", "view");
                    sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(ViewState["fid"].ToString()));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                        dt = new DataTable();
                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv = dt.DefaultView;
                if (dv.Count >= 1)
                {
                    txt_doc.Text = Server.HtmlEncode(dv[0]["tcdf_doc"].ToString().Trim());
                    txt_filetxt.Text = Server.HtmlEncode(dv[0]["tcdf_filetxt"].ToString().Trim());
                    ViewState["seno"] = Server.HtmlEncode(dv[0]["tcdf_seno"].ToString().Trim());
                    DDL_FileType.SelectedValue = Server.HtmlEncode(dv[0]["tcdf_filetype"].ToString().Trim());
                    if (dv[0]["tcdf_inspect"].ToString() == "1")
                        CB_inspect.Checked = true;
                }
                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
            }

        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {


        //SDS_file.UpdateCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_file.UpdateCommand = "esp_TreatyCase_file_modify";
        //SDS_file.UpdateParameters.Add("req_id", "");
        //SDS_file.UpdateParameters.Add("fd_name", "");
        //SDS_file.UpdateParameters.Add("filetxt", txt_filetxt.Text.Trim());
        //SDS_file.UpdateParameters.Add("file_url", "");
        //SDS_file.UpdateParameters.Add("empno", ViewState["empno"].ToString());
        //SDS_file.UpdateParameters.Add("mode", "modify");
        //SDS_file.UpdateParameters.Add("fid", ViewState["fid"].ToString());
        //SDS_file.UpdateParameters.Add("inspect", IIf(CB_inspect.Checked,"1","0"));
        //SDS_file.UpdateParameters.Add("filetype", DDL_FileType.SelectedValue);
        //SDS_file.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@req_id", "");
            sqlCmd.Parameters.AddWithValue("@fd_name", "");
            sqlCmd.Parameters.AddWithValue("@filetxt", "");
            sqlCmd.Parameters.AddWithValue("@file_url", "");
            sqlCmd.Parameters.AddWithValue("@empno", "");
            sqlCmd.Parameters.AddWithValue("@mode", "view");
            sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(ViewState["fid"].ToString()));


            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        Treaty_log(ViewState["fid"].ToString(), "檔案更新", txt_filetxt.Text.Trim(), ViewState["xIP"].ToString(), "treaty\\TreatyApplyFileModify.aspx");
        string script_alert = "<script language='javascript'>parent.$.fn.colorbox.close();</script>";
        ClientScript.RegisterStartupScript(this.GetType(), "cc", script_alert);
    }

    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", ViewState["seno"].ToString());
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
}