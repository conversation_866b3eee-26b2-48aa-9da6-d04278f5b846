﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_todo_search : System.Web.UI.Page
{
    RemoveCheckMax oRCM = new RemoveCheckMax();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["sortField"] = "";
            ViewState["sortorder"] = "";
            SGV_search.PageIndex = 0;
            Bind_Auth();
            Bind_Data("", "");
        }
    }

    private void Bind_Data(string str_sortField, string str_sort)
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "search_todo");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                if (str_sortField.Trim() != "")
                {
                    dt.DefaultView.Sort = str_sortField + " " + str_sort;
                }

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Bind_Auth()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Auth");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            string str_auth = dv_auth[0]["RW"].ToString();
            if (str_auth == "X")
                Response.Redirect("../NoAuthRight.aspx");

            //if (str_auth != "W")
            //{
            //    string script = "<script language='javascript'>alert('無編輯權限！');location.href='./TechCase_todo_search.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
            //    ClientScript.RegisterStartupScript(this.GetType(), "NoAuthRight", script);
            //}
        }
    }

    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }


    protected void btnAdd_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TechCase_todo_modify.aspx");
    }

    protected void TB_SendMail_Click(object sender, EventArgs e)
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "notify_mail_content");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_sdate", oRCM.SQLInjectionReplaceAll(TB_sdate.Text));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                foreach (DataRow dr in dt.Rows)
                {
                    Send_Email(dr["nm_title"].ToString(), dr["nm_message"].ToString(), dr["nm_mail_to"].ToString(), dr["nm_cc"].ToString());

                }

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Send_Email(string title, string message, string mail_to, string cc)
    {
        mail_to = "<EMAIL>";
        cc = "<EMAIL>";
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "send_notify_mail");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_name", oRCM.SQLInjectionReplaceAll(title));
            sqlCmd.Parameters.AddWithValue("@t_說明", oRCM.SQLInjectionReplaceAll(message));
            sqlCmd.Parameters.AddWithValue("@notify_收件人", oRCM.SQLInjectionReplaceAll(mail_to));
            sqlCmd.Parameters.AddWithValue("@notify_副本", oRCM.SQLInjectionReplaceAll(cc));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
    }
}