﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Default : System.Web.UI.Page
{
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    } 
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;
            LT_loginName.Text = ssoUser.empName;
            ViewState["user_id_string"] = ssoUser.empNo + "_" + ssoUser.empOrgcd + ssoUser.empDeptcd + "_" + ssoUser.empName;

            ViewState["sortorder"] = "";
            ViewState["sortField"] = "";
            ddlOrgcd.DataBind();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
            SGV_search.DataBind();
            TreeNode1.SysId = "All";
        }

    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;
 
        SDS_search.SelectParameters.Clear();
        SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        SDS_search.SelectCommand = "esp_APMS_search";
        SDS_search.SelectParameters.Add("org", ddlOrgcd.SelectedValue);
        SDS_search.SelectParameters.Add("rule", DDL_rule1.SelectedValue); // 角色
        SDS_search.SelectParameters.Add("status", DDL_status1.SelectedValue); 
        SDS_search.SelectParameters.Add("case", DDL_case1.SelectedValue); //案源
        SDS_search.SelectParameters.Add("caseName", TB_contnoName.Text);
        SDS_search.SelectParameters.Add("campanyName", TB_CompName.Text);
        SDS_search.SelectParameters.Add("kw", TB_kw.Text);
        SDS_search.SelectParameters.Add("empno", ssoUser.empNo);
        for (int i = 0; i < this.SDS_search.SelectParameters.Count; i++)
        {
            SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
        }

        SDS_search.DataBind();
        
    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal lt_n = (Literal)e.Row.FindControl("LT_N_View");
        Literal lt_r = (Literal)e.Row.FindControl("LT_R_View");
        Literal lt_T = (Literal)e.Row.FindControl("LT_T_View");
        HyperLink HL_satisfaction_URL = (HyperLink)e.Row.FindControl("HL_satisfaction_URL");
        if (lt_n != null) 
        {
            string sN_seno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "engage_id"));
            string sN_status = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "engage_status"));
            switch(sN_status)
            {
                case "進行中": lt_n.Text = "<a href='#' onclick='doNCase(" + sN_seno + ");'><span class='font-subtitle'>進行中</span></a>"; break;
                case "中止": lt_n.Text = "<a href='#' onclick='doNCase(" + sN_seno + ");'><span class='font-red'>中止</span></a>"; break;
                case "已完成": lt_n.Text = "<a href='#' onclick='doNCase(" + sN_seno + ");'><span class='font-light'>已完成</span></a>"; break;
            }
            string sR_seno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "gpi_id"));
            string sR_status = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "gpi_status"));
            switch (sR_status)
            {
                case "進行中": lt_r.Text = "<a href='#' onclick='doRCase(" + sR_seno + ");'><span class='font-subtitle'>進行中</span></a>"; break;
                case "中止": lt_r.Text = "<a href='#' onclick='doRCase(" + sR_seno + ");'><span class='font-red'>中止</span></a>"; break;
                case "已完成": lt_r.Text = "<a href='#' onclick='doRCase(" + sR_seno + ");'><span class='font-light'>已完成</span></a>"; break;
            }
            string sT_seno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "treaty_id"));
            string sT_status = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "treaty_status"));
            switch (sT_status)
            {
                case "進行中": lt_T.Text = "<a href='#' onclick='doTCase(" + sT_seno + ");'><span class='font-subtitle'>進行中</span></a>"; break;
                case "中止": lt_T.Text = "<a href='#' onclick='doTCase(" + sT_seno + ");'><span class='font-red'>中止</span></a>"; break;
                case "已完成": lt_T.Text = "<a href='#' onclick='doTCase(" + sT_seno + ");'><span class='font-light'>已完成</span></a>"; break;
            }
            if(  Convert.ToString(DataBinder.Eval(e.Row.DataItem, "satisfaction_URL")) !="")
            {
                HL_satisfaction_URL.NavigateUrl = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "satisfaction_URL"));
            }
            else
            {
                HL_satisfaction_URL.Visible = false;
            }
        }

    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {

    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "./images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "./images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        TB_contnoName.Text = "";
        TB_CompName.Text = "";
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
    protected void btnQuery1_Click(object sender, EventArgs e)
    {
        TB_kw.Text = "";
        DDL_case1.SelectedValue = DDL_case.SelectedValue;
        DDL_status1.SelectedValue = DDL_status.SelectedValue;
        DDL_rule1.SelectedValue = DDL_rule.SelectedValue;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
    protected void DDL_case1_SelectedIndexChanged(object sender, EventArgs e)
    {
        DDL_case.SelectedValue = DDL_case1.SelectedValue;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
    protected void DDL_status1_SelectedIndexChanged(object sender, EventArgs e)
    {
        DDL_status.SelectedValue = DDL_status1.SelectedValue;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
    protected void DDL_rule1_SelectedIndexChanged(object sender, EventArgs e)
    {
        DDL_rule.SelectedValue = DDL_rule1.SelectedValue;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.DataBind();
    }
}