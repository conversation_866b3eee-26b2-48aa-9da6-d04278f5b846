﻿using Aspose.Pdf;
using Engage;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.ServiceModel.Activities;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TechCase_view : System.Web.UI.Page
{
	internal RemoveCheckMax oRCM = new RemoveCheckMax();
	public List<SqlParameter> sqlParamList = new List<SqlParameter>();
	private string IIf(bool Expression, string TruePart, string FalsePart)
	{
		string ReturnValue = Expression == true ? TruePart : FalsePart;
		return ReturnValue;
	}
	private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull, string mode)
	{
		if (mode == "Select")
		{
			foreach (Parameter parameter in dataSource.SelectParameters)
				parameter.ConvertEmptyStringToNull = isNull;
		}
		if (mode == "Insert")
		{
			foreach (Parameter parameter in dataSource.InsertParameters)
				parameter.ConvertEmptyStringToNull = isNull;
		}
		if (mode == "Updat")
		{
			foreach (Parameter parameter in dataSource.UpdateParameters)
				parameter.ConvertEmptyStringToNull = isNull;
		}
		if (mode == "Delete")
		{
			foreach (Parameter parameter in dataSource.DeleteParameters)
				parameter.ConvertEmptyStringToNull = isNull;
		}
	}

	public static bool IsNumber(string strNumber)
	{
		System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
		return r.IsMatch(strNumber);
	}

	public bool IsNatural_Number(string str)
	{
		System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
		return reg1.IsMatch(str);
	}

	public bool Isfloat(string str)
	{
		System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
		return reg1.IsMatch(str);
	}

	public bool CheckDateTimeType(string txtDateStart)
	{
		if (String.IsNullOrEmpty(txtDateStart))
		{
			return false;
		}
		else
		{
			try
			{
				DateTime t1 = DateTime.Parse(txtDateStart);
				return true;  //返回真
			}
			catch
			{
				return false;
			}
		}
	}

	#region 根據案件編號，取得要顯示的按鈕文字
	public string GetEngageNDAText(string strCaseNo)
	{
		string strResult = string.Empty;
		if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
			return "";

		switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
		{

			case "A":
				strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國外無收入資訊";
				break;
			case "N":
				strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視洽案資訊";
				break;
			case "M":
				strResult = "<img src='../images/icon-1301.gif' border='0' />檢視NDA資訊";
				break;
			case "F":
				strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
				break;
			case "R":
				strResult = "<img src='../images/icon-1301.gif' border='0' />檢視標案資訊";
				break;
			case "C":
				strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
				break;

			default:
				strResult = "";
				break;
		}
		return strResult;
	}
	#endregion

	#region 根據案件編號，取得是否要顯示按鈕
	public bool GetEngageNDAVisible(string strCaseNo)
	{
		bool bResult = false;
		if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
			return false;
		lnkbtn_Engage.Text = GetEngageNDAText(strCaseNo);
		switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
		{
			case "A":
				bResult = true;
				break;
			case "N":
				bResult = true;
				break;
			case "M":
				bResult = true;
				break;
			//case "U":
			//    bResult = true;
			//    break;
			case "R":
				bResult = true;
				break;
			case "C":
				bResult = true;
				break;
			case "F":
				bResult = true;
				break;


			default:
				bResult = false;
				break;
		}
		return bResult;
	}
	#endregion

	#region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
	protected void lnkbtn_Engage_Click(object sender, EventArgs e)
	{
		string strCaseNo = txt_ComplexNo.Text.Trim();
		//抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
		string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
		string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
		string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
		string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();
		string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
		string strWinOpen = string.Empty; //宣告開窗的URL字串
		string script = "";
		switch (ViewState["tr_class"].ToString())
		{
			case "N": //洽案/Engage/Base/caseBase.aspx?contno=xxxxx
				strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
				break;

			case "R": //標案
				strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
				break;

			case "M": // NDA
					  // strWinOpen = string.Format("{0}/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-",""));
				strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
				break;

			case "A": // 國外契約   norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af <https://webdev5.itri.org.tw/norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af> 
				strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
				break;
			case "F": // 國內契約  
				strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
				break;
				//case "C": // 工服
				//    strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
				//    break;

		}
		script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
		//script = @" <script> alert('" + strWinOpen + "'); </script>";
		Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
		BindData();
	}
	#endregion

	public string GetUserIP()
	{
		string strIP = String.Empty;
		HttpRequest httpReq = HttpContext.Current.Request;
		//test for non-standard proxy server designations of client's IP
		if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
		{
			strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
		}
		else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
		{
			strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
		}
		//test for host address reported by the server
		else if
		(
		//if exists
		(httpReq.UserHostAddress.Length != 0)
		&&
		//and if not localhost IPV6 or localhost name
		((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
		)
		{
			strIP = httpReq.UserHostAddress;
		}
		//finally, if all else fails, get the IP from a web scrape of another server
		else
		{
			WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
			using (WebResponse response = request.GetResponse())
			using (StreamReader sr = new StreamReader(response.GetResponseStream()))
			{
				strIP = sr.ReadToEnd();
			}
			//scrape ip from the html
			int i1 = strIP.IndexOf("Address:") + 9;
			int i2 = strIP.LastIndexOf("</body>");
			strIP = strIP.Substring(i1, i2 - i1);
		}
		return strIP;
	}

	/// <summary>
	/// 給檔案比對值
	/// </summary>
	void setParamFileOpen()
	{
		SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
		sso.GetEmpInfo();

		//BT_OpenFileCompare.Attributes.Add("key", HttpUtility.UrlEncode("empno=" + sso.empNo + "&seno=" + ViewState["tt_seno"].ToString() + "&actcontno=" + ddl_SeqSn.SelectedValue));
	}
	private bool _sAuth;
	
	public bool sAuth
	{
		get { return _sAuth; }
		set { _sAuth = value; }
	}


	private void doAuth()
	{
		sAuth = getAuth();
	}
	protected void Page_Load(object sender, EventArgs e)
	{
		SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
		ssoUser.GetEmpInfo();
		ViewState["empno"] = ssoUser.empNo;
		doAuth();
		txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
		ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));

		cb_conttype_b0.Attributes.Add("disabled", "disabled");
		cb_conttype_b1.Attributes.Add("disabled", "disabled");
		cb_conttype_d4.Attributes.Add("disabled", "disabled");
		cb_conttype_d5.Attributes.Add("disabled", "disabled");
		cb_conttype_d7.Attributes.Add("disabled", "disabled");
		chk_技術授權.Attributes.Add("disabled", "disabled");
		chk_專利授權.Attributes.Add("disabled", "disabled");
		chk_技術與專利授權.Attributes.Add("disabled", "disabled");
		chk_全球.Attributes.Add("disabled", "disabled");
		chk_陸港澳.Attributes.Add("disabled", "disabled");
		chk_特定區域.Attributes.Add("disabled", "disabled");
		chk_韓國.Attributes.Add("disabled", "disabled");
		chk_技術讓與.Attributes.Add("disabled", "disabled");
		chk_修約.Attributes.Add("disabled", "disabled");

		ClientScript.GetPostBackEventReference(new PostBackOptions(this.txt_name));

		if (!IsPostBack)
		{
			if (lb_Subtitle.Text == String.Empty)
			{
				Breadcrumb myBreadcrumb = new Breadcrumb();
				lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
			}

			if (Request["tt_seno"] != null)//設定為編輯狀態
			{
				int j = 0;
				if (!(int.TryParse(Request["tt_seno"], out j)))
					Response.Redirect("../danger.aspx");
				ViewState["tt_seno"] = Request["tt_seno"];
			}

			if (ViewState["tt_seno"] == null)
				Response.Redirect("../danger.aspx");

			//ViewState["tt_degree"] = "";

			Bind_Auth();

			Tech_log(ViewState["tt_seno"].ToString(), "檢視承辦單", "", ViewState["tt_seno"].ToString(), "treaty\\TechCase_view.aspx");
			Bind_Light();
			BindData();
			Bindddl_SeqSn();
			Bind_Doc_File();
			BindInspect();

			lnkbtn_Dispatch.Attributes.Add("onclick", "Dispatch_Case('" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "','X');");
			lnkbtn_DispatchC.Attributes.Add("onclick", "Dispatch_Case('" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "','C');");

			lbtn_outside.Attributes.Add("onclick", "OpenOutside('" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "');");
			lbtn_outside.Visible = sAuth;


			ddl_SeqSn.SelectedValue = ViewState["tt_seno"].ToString();

			txt_ComplexNo.Attributes.Add("readOnly", "readonly");
			txt_OrgAbbrName.Attributes.Add("readOnly", "readonly");
			txt_req_dept.Attributes.Add("readOnly", "readonly");
			btn_End.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
			btn_End2.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
			bt_cancle.Attributes.Add("onclick", "tech_Cancel(" + System.Web.HttpUtility.HtmlEncode(ViewState["tt_seno"].ToString()) + ");");

			//檔案上傳
			btn_FileUp.Attributes.Add("onclick", "tech_fileup('" + txt_ComplexNo.Text.Replace("-", "") + "'," + ViewState["tt_seno"].ToString() + ");");

			if (Request.ServerVariables["HTTP_VIA"] != null)
			{
				ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
			}
			else
			{
				ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
			}
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			ViewState["isPC"] = "false";
			Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", " <script>reflash_topic('Client', p);</script> ");

		}

		if (Request.Params.Get("__EVENTTARGET") == "Client")
		{
			string p = Request.Params.Get("__EVENTARGUMENT").ToString();
			bool isWin, isMac, isLinux, isUnix = false;
			isWin = p.IndexOf("Win") > -1;  //Windows : Win32、Win16
			isMac = p.IndexOf("Mac") > -1;  //MacOS: MacIntel、Macintosh、MacPPC、Mac68K
			isUnix = p.IndexOf("X11") > -1; //Unix
			isLinux = p.IndexOf("Linux") > -1; //Linux: Linux x86_64、Linux x86_64 X11、Linux ppc64

			//Linux 要多加判斷排除，因為行動裝置Android 系統的Platform參數會是 
			//Linux armv7l、Linux armv8l、Linux aarch64、Linux i686(both Chrome on ChromeOS or Linux x86-64)
			if (p.IndexOf("Linux a") > -1 || p.IndexOf("Linux i") > -1)
			{
				isLinux = false;
			}
			if (isWin || isMac || isLinux || isUnix)
			{
				ViewState["isPC"] = "true";
			}
			else
			{
				ViewState["isPC"] = "false";
			}
		}

		#region postback
		if (IsPostBack)
		{

			Bind_sRC_init(Plh_Dynax_sRC, "X");
			Bind_sRC_init(Plh_Dynax_sRC_x, "");

		}

		//lb_keyin_date.Text = DateTime.Now.ToString("yyyyMMdd"); //建檔日期
		if (Request.Params.Get("__EVENTTARGET") == "company_renew")
		{
			BindData_Customer();
		}
		if (Request.Params.Get("__EVENTTARGET") == "file_renew")
		{
			Bind_Doc_File();
		}
		if (Request.Params.Get("__EVENTTARGET") == "Inspect_renew")
		{
			BindInspect();
			BindData();
			//Response.Redirect("./Search_inspect.aspx");
		}
		if (Request.Params.Get("__EVENTTARGET") == "case_renew")
		{
			BindData();
			BindInspect();
		}
		if (Request.Params.Get("__EVENTTARGET") == "Dispatch_Case")
		{
			BindData();
		}
        if (Request.Params.Get("__EVENTTARGET") == "seno_ECP")
        {
            if (h_ECP_success.Value == "Y")
            {
                //string script = "<script language='javascript'>alert('申請單送出簽核成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                string script = "<script language='javascript'>alert('申請單送出簽核成功！');</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }


        #endregion

        setParamFileOpen();

    }






	/// <summary>
	/// 簽約對象 清單
	/// </summary>
	private void BindData_Customer()
	{
		#region --- query ---

		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@mode", "req_company");
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				DataTable dt = new DataTable();
				sqlDA.Fill(dt);
				SGV_company.DataSource = dt;
				SGV_company.DataBind();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion
	}

	public void BindData()
	{
		DataTable dt = Case_View();
		DataView dv = dt.DefaultView;

		if (dv.Count == 0)
		{
			Response.Redirect("../danger.aspx");
		}
		string str_tr_year = Server.HtmlEncode(dv[0]["tt_year"].ToString().Trim());
		string str_tr_orgcd = Server.HtmlEncode(dv[0]["tt_orgcd"].ToString().Trim());
		string str_tr_class = Server.HtmlEncode(dv[0]["tt_class"].ToString().Trim());
		ViewState["tr_class"] = str_tr_class;
		ViewState["tt_degree"] = Server.HtmlEncode(dv[0]["tt_degree"].ToString().Trim());
		ViewState["tt_status"] = Server.HtmlEncode(dv[0]["tt_status"].ToString().Trim());
		ViewState["tt_handle_empno"] = Server.HtmlEncode(dv[0]["tt_handle_empno"].ToString().Trim());
		LT_法務承辦人.Text = Server.HtmlEncode(dv[0]["法務人員"].ToString().Trim());

		//Bind_sRC(Plh_Dynax_sRC, "X");
		//Bind_sRC(Plh_Dynax_sRC_x, "");

		#region btn_Edit 編輯
		btn_Edit.Visible = false;
		btn_Edit2.Visible = false;

		if (ViewState["tt_degree"].ToString() == "0" && ViewState["Role"].ToString() == "ADM")
		{
			btn_Edit.Text = "立案設定";
			btn_Edit2.Text = "立案設定";
			btn_Edit.Visible = true;
			btn_Edit2.Visible = true;
		}

		if (ViewState["RW"].ToString() == "W")
		{
			//if (ViewState["tt_degree"].ToString() == "3" && ViewState["Role"].ToString() == "ADM")
			//{
			//    btn_Edit.Visible = true;
			//    btn_Edit2.Visible = true;
			//}

			if (ViewState["tt_degree"].ToString() == "5" || ViewState["tt_status"].ToString() == "5")
			{
				if (
					((ViewState["Role2"].ToString() == "X" && ViewState["empno"].ToString() == dv[0]["tt_promoter_no"].ToString().Trim()) || ViewState["Role"].ToString() == "ADM") ||
					((ViewState["Role2"].ToString() == "x" && ViewState["empno"].ToString() == dv[0]["tt_handle_c_empno"].ToString().Trim()) || ViewState["Role"].ToString() == "ADM")
					)
				{
					btn_Edit.Visible = true;
					btn_Edit2.Visible = true;
				}
			}

			if (ViewState["tt_degree"].ToString() == "6" &&
				ViewState["tt_status"].ToString() == "6" &&
				(ViewState["tt_handle_empno"].ToString() == ViewState["empno"].ToString() || ViewState["Role"].ToString() == "ADM"))
			{
				btn_Edit.Visible = true;
				btn_Edit2.Visible = true;
				//PL_Light.Visible = true;
			}

			if (ViewState["tt_degree"].ToString() == "9" || ViewState["tt_status"].ToString() == "9")
			{
				if (
					((ViewState["Role2"].ToString() == "X" && ViewState["empno"].ToString() == dv[0]["tt_promoter_no"].ToString().Trim()) || ViewState["Role"].ToString() == "ADM") ||
					((ViewState["Role2"].ToString() == "x" && ViewState["empno"].ToString() == dv[0]["tt_handle_c_empno"].ToString().Trim()) || ViewState["Role"].ToString() == "ADM")
					)
				{
					btn_FileUp.Visible = true;
				}
			}
		}
		#endregion

		#region 案件合理性評估(單位)
		Plh_Dynax_sRC.Visible = false;
		Plh_Dynax_sRC_x.Visible = false;
		if (ViewState["Role"].ToString() == "ADM")
		{
			Plh_Dynax_sRC.Visible = true;
			Plh_Dynax_sRC_x.Visible = true;
			Bind_sRC(Plh_Dynax_sRC, "X");
			Bind_sRC(Plh_Dynax_sRC_x, "");
		}
		else if (ViewState["Role2"].ToString() == "X")
		{
			Plh_Dynax_sRC.Visible = true;
			Plh_Dynax_sRC_x.Visible = true;
			Bind_sRC(Plh_Dynax_sRC, "X");
			Bind_sRC(Plh_Dynax_sRC_x, "");
		}
		else if (ViewState["Role2"].ToString() == "x")
		{
			Plh_Dynax_sRC_x.Visible = true;
			Bind_sRC(Plh_Dynax_sRC_x, "");
		}
		else if (dv[0]["tt_handle_empno"].ToString().Trim() == ViewState["empno"].ToString())
		{
			Plh_Dynax_sRC.Visible = true;
			Plh_Dynax_sRC_x.Visible = true;
			Bind_sRC(Plh_Dynax_sRC, "X");
			Bind_sRC(Plh_Dynax_sRC_x, "");
		}

		#endregion

		#region 技轉分案        
		lnkbtn_Dispatch.Visible = false;
		if (ViewState["tt_degree"].ToString() == "3" &&
		   ((ViewState["Role"].ToString() == "ADM") || (ViewState["Role2"].ToString() == "X" && ViewState["分案權限"].ToString() == "1")))
		{
			lnkbtn_Dispatch.Visible = true;
		}

		#endregion

		#region 技轉分案(C組)        
		lnkbtn_DispatchC.Visible = false;
		if (ViewState["tt_status"].ToString() == "3" &&
		   ((ViewState["Role"].ToString() == "ADM") || (ViewState["Role2"].ToString() == "x" && ViewState["分案權限"].ToString() == "1")))
		{
			lnkbtn_DispatchC.Visible = true;
			Plh_Dynax_sRC_x.Visible = true;
		}

		#endregion

		#region 審查
		if ((ViewState["Role"].ToString() == "ADM" ||
			(ViewState["Role"].ToString() == "Audit" && ViewState["tt_handle_empno"].ToString() == ViewState["empno"].ToString())) &&
			(ViewState["tt_status"].ToString() == "7" && ViewState["tt_degree"].ToString() == "7")
)
		{
			btn_Inspect.Visible = true;
			btn_Inspect2.Visible = true;
		}
		#endregion

		if (ViewState["Role2"].ToString() == "X")
		{
			PL_tt_manage_note.Visible = true;
			//int tt_degree = 0;
			//int.TryParse(ViewState["tt_degree"].ToString(), out tt_degree);
			PL_Light.Visible = true;// ViewState["tt_degree"].ToString() == "Z" || tt_degree >= 6;
		}

		string str_tr_sn = Server.HtmlEncode(dv[0]["tt_sn"].ToString().Trim());
		string str_tr_ver = Server.HtmlEncode(dv[0]["tt_ver"].ToString().Trim());
		string str_tr_seqsn = Server.HtmlEncode(dv[0]["tt_seqsn"].ToString().Trim());
		ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
		txt_ComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
		string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;

		txt_betsum.Text = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tt_betsum"].ToString().Trim()));

		lnkbtn_Engage.Visible = GetEngageNDAVisible(str_actcontno);

		//分案主管
		lb_assign_name.Text = Server.HtmlEncode(dv[0]["tt_assign_name"].ToString());
		//分案日期        
		lb_assign_date.Text = dv[0]["tt_assign_datetime"].ToString().Trim().Length > 0 ? DateTime.Parse(dv[0]["tt_assign_datetime"].ToString().Trim()).ToString("yyyy/MM/dd") : "";
		//技轉承辦人姓名
		lb_handle_name.Text = Server.HtmlEncode(dv[0]["tt_handle_name"].ToString());
		//技轉承辦人工號
		lb_handle_empno.Text = Server.HtmlEncode(dv[0]["tt_handle_empno"].ToString());
		//技轉承辦人分機
		lb_handle_ext.Text = Server.HtmlEncode(dv[0]["tt_handle_ext"].ToString());

		//承辦人姓名c
		lb_handle_c_name.Text = Server.HtmlEncode(dv[0]["tt_handle_c_name"].ToString());
		//承辦人工號c
		lb_handle_c_empno.Text = Server.HtmlEncode(dv[0]["tt_handle_c_empno"].ToString());
		//承辦人分機c
		lb_handle_c_ext.Text = Server.HtmlEncode(dv[0]["tt_handle_c_ext"].ToString());

		txt_ManageNote.Text = Server.HtmlEncode(dv[0]["tt_remark"].ToString().Trim());

		#region 案件性質
		if (dv[0]["tt_conttype_b0"].ToString().Trim() == "1")
			cb_conttype_b0.Checked = true;

		if (dv[0]["tt_conttype_b1"].ToString().Trim() == "1")
			cb_conttype_b1.Checked = true;

		if (dv[0]["tt_conttype_d4"].ToString().Trim() == "1")
			cb_conttype_d4.Checked = true;

		if (dv[0]["tt_conttype_d5"].ToString().Trim() == "1")
			cb_conttype_d5.Checked = true;

		if (dv[0]["tt_conttype_d7"].ToString().Trim() == "1")
			cb_conttype_d7.Checked = true;

		if (dv[0]["tt_conttype_ns"].ToString().Trim() == "1")
			cb_conttype_ns.Checked = true;

		if (dv[0]["tt_技術授權"].ToString().Trim() == "1")
			chk_技術授權.Checked = true;

		if (dv[0]["tt_專利授權"].ToString().Trim() == "1")
			chk_專利授權.Checked = true;

		if (dv[0]["tt_技術與專利授權"].ToString().Trim() == "1")
			chk_技術與專利授權.Checked = true;

		if (dv[0]["tt_特定區域"].ToString().Trim() == "1")
			chk_特定區域.Checked = true;

		if (dv[0]["tt_技術讓與"].ToString().Trim() == "1")
			chk_技術讓與.Checked = true;

		if (dv[0]["tt_修約"].ToString().Trim() == "1")
			chk_修約.Checked = true;

		#endregion

		if (dv[0]["第三方鑑價報告"].ToString().Trim() == "1")
		{
			chk_第三方鑑價報告.Checked = true;
		}

		//案件燈號
		if (dv[0]["案件燈號"].ToString() != "")
			rbl_Light.SelectedValue = Server.HtmlEncode(dv[0]["案件燈號"].ToString());

		if (dv[0]["tt_sRC_relation"].ToString() != "")
			rbl_relation.SelectedValue = Server.HtmlEncode(dv[0]["tt_sRC_relation"].ToString());
		else
			rbl_relation.SelectedValue = "";

		txt_燈號_說明.Text = Server.HtmlEncode(dv[0]["tt_燈號_說明"].ToString());
		txt_燈號_說明.Visible = rbl_Light.SelectedValue == "9";

		#region 經濟部成果歸屬運用辦法第18條
		if (dv[0]["成果歸屬運用辦法"].ToString().Trim() == "1")
		{
			chk_成果歸屬運用辦法.Checked = true;
		}
		chk_公益目的.Checked = Server.HtmlEncode(dv[0]["公益目的"].ToString()) == "1";
		chk_促進整體產業發展.Checked = Server.HtmlEncode(dv[0]["促進整體產業發展"].ToString()) == "1";
		chk_提升研發成果運用效益.Checked = Server.HtmlEncode(dv[0]["提升研發成果運用效益"].ToString()) == "1";
		#endregion

		lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tt_modify_empname"].ToString());//修改人
		lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tt_modify_enpmo"].ToString());//修改工號
		lb_modify_date.Text = Server.HtmlEncode(dv[0]["tt_modify_date"].ToString().Trim().Length > 0 ? DateTime.Parse(dv[0]["tt_modify_date"].ToString().Trim()).ToString("yyyy/MM/dd") : ""); //修改日期

		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			DataTable dt_ddl = new DataTable();
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "init_degree");
			try
			{
				sqlConn.Open();
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt_ddl);
				DDL_Degree.DataSource = dt_ddl;
				DDL_Degree.DataBind();
				DDL_Degree.SelectedValue = ViewState["tt_degree"].ToString();//進度
				LT_L_Degree.Text = System.Web.HttpUtility.HtmlEncode(DDL_Degree.SelectedItem.Text);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);
				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion

		#region 基本資料 &需求單位及部門 --- query --- 
		DataTable dt_emp = new DataTable();

		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tt_promoter_no"].ToString().Trim()));
			sqlCmd.Parameters.AddWithValue("@mode", "req_case");
			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt_emp);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion
		dv = dt_emp.DefaultView;

		if (dv.Count == 0)
		{
			return;
		}

		txt_req_dept.Text = Server.HtmlEncode(dv[0]["com_deptid"].ToString().Trim());
		txt_promoter_name.Text = Server.HtmlEncode(dv[0]["com_cname"].ToString().Trim());
		txt_promoter_empno.Value = Server.HtmlEncode(dv[0]["com_empno"].ToString().Trim());
		txt_Tel.Text = Server.HtmlEncode(dv[0]["com_telext"].ToString().Trim());
		txt_OrgAbbrName.Text = Server.HtmlEncode(dv[0]["orgName"].ToString().Trim());
		ViewState["com_orgcd"] = Server.HtmlEncode(dv[0]["com_orgcd"].ToString().Trim());
		ViewState["tc_degree"] = Server.HtmlEncode(dv[0]["tc_degree"].ToString().Trim());

		txt_name.Text = Server.HtmlEncode(dv[0]["tt_name"].ToString().Trim());//洽案（契約名稱）
		LB_議約狀態.Text = Server.HtmlEncode(dv[0]["議約狀態"].ToString().Trim());
		洽案簽辦人.Text = Server.HtmlEncode(dv[0]["洽案簽辦人"].ToString().Trim());


		#region 客戶
		h_compno.Value = dv[0]["tc_compidno_all"].ToString().Trim().Replace("㊣", ",");//簽約對象(多)
		BindData_Customer();
		#endregion



		#region 需求取消原因
		if (ViewState["tt_degree"].ToString() == "C")
		{
			PH_degree_C.Visible = true;
			txtRequestCancel.Text = Server.HtmlEncode(dv[0]["tc_request_cancel"].ToString().Trim());
		}
		#endregion



		//結件才秀
		//if (ViewState["tt_degree"].ToString() == "Z")
		//{
		//    //技轉承辦人意見彙整
		//    txt_betsum.Text = Server.HtmlEncode(dv[0]["tt_betsum"].ToString().Trim());
		//}



		if ((ViewState["RW"].ToString() == "W" || ViewState["Role"].ToString() == "ADM"))
		{
			if ((ViewState["tt_status"].ToString() == "9") && (ViewState["tt_degree"].ToString() == "9"))
			{
				#region --- query ---
				using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
				{
					DataTable dtSource = new DataTable();
					SqlCommand sqlCmd = new SqlCommand();
					sqlCmd.Connection = sqlConn;
					sqlCmd.CommandType = CommandType.StoredProcedure;
					sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
					// --- 避免匯出查詢過久而當掉 --- //
					sqlCmd.CommandTimeout = 0;

					sqlCmd.Parameters.Clear();

					sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
					sqlCmd.Parameters.AddWithValue("@mode", "un_inspect");
					try
					{
						sqlConn.Open();
						SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
						dtSource = new DataTable();
						sqlDA.Fill(dtSource);
						DataView dv_auth = dtSource.DefaultView;
						if (dv_auth.Count >= 1)
						{
							if (dv_auth[0][0].ToString() == "0")
							{
								btn_End.Visible = true; //沒有審查人 || 審查人都審查完
								btn_End2.Visible = true; //沒有審查人 || 審查人都審查完
								txt_ManageNote.ReadOnly = false;
								PL_tt_manage_note.Visible = true;
							}
						}
					}
					catch (Exception ex)
					{
						// --- 執行異常通報 --- //
						RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
							ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
							Request,
							Response,
							ex
							);
						oRCM.ErrorExceptionDataToDB(logMail);
					}
					finally
					{
						sqlConn.Close();
					}
				}
				#endregion
				//btn_Edit.Visible = true;
				//btn_Edit2.Visible = true;

				//bt_cancle.Visible = true;
			}
		}

		//if ((ViewState["RW"].ToString() == "R") && ((ViewState["tt_status"].ToString() == "7") && (ViewState["tt_status"].ToString() == "7")))
		//{
		//    bt_cancle.Visible = true;
		//}

		//if ((ViewState["Role"].ToString() == "ADM") && ((ViewState["tt_status"].ToString() == "7") && (ViewState["tt_status"].ToString() == "7")))
		//{
		//    bt_cancle.Visible = true;
		//}

		if (ViewState["Role"].ToString() == "ADM" && (ViewState["tt_degree"].ToString() != "Z"))
		{
			bt_cancle.Visible = true;
		}

		PL_Inspect.Visible = true;
		PH_法務內部資訊.Visible = true;
		if ((ViewState["tt_degree"].ToString() == "7")) //審查中
		{
			//btn_Edit.Visible = false;
			#region --- query ---
			DataTable dtSource = new DataTable();
			using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
			{
				SqlCommand sqlCmd = new SqlCommand();
				sqlCmd.Connection = sqlConn;
				sqlCmd.CommandType = CommandType.StoredProcedure;
				sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
				// --- 避免匯出查詢過久而當掉 --- //
				sqlCmd.CommandTimeout = 0;
				sqlCmd.Parameters.Clear();
				sqlCmd.Parameters.AddWithValue("@mode", "last_inspect");
				sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
				try
				{
					sqlConn.Open();
					SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
					dtSource = new DataTable();
					sqlDA.Fill(dtSource);


				}
				catch (Exception ex)
				{
					// --- 執行異常通報 --- //
					RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
						ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
						Request,
						Response,
						ex
						);
					oRCM.ErrorExceptionDataToDB(logMail);
				}
				finally
				{
					sqlConn.Close();
				}
			}
			#endregion
			DataView dv_auth = dtSource.DefaultView;
			if (dv_auth.Count > 0)
			{
				if ((ViewState["empno"].ToString().Trim() == dv_auth[0][0].ToString().Trim()))
				{
					btn_Inspect.Visible = true;
					btn_Inspect2.Visible = true;
					string script_alert = "<script language='javascript'> $('#btn_Inspect').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#btn_Inspect').click(function () { $('#btn_Inspect').validationEngine('hide'); })</script>";
					ClientScript.RegisterStartupScript(this.GetType(), "btn_Inspect", script_alert);
					string script_alert2 = "<script language='javascript'> $('#btn_Inspect2').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#btn_Inspect2').click(function () { $('#btn_Inspect2').validationEngine('hide'); })</script>";
					ClientScript.RegisterStartupScript(this.GetType(), "btn_Inspect2", script_alert2);

					btn_Inspect.Attributes.Add("onclick", "tech_Inspect(" + ViewState["tt_seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
					btn_Inspect2.Attributes.Add("onclick", "tech_Inspect(" + ViewState["tt_seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");

				}
				else
				{
					btn_Inspect.Visible = false;
					btn_Inspect2.Visible = false;
				}

				if (ViewState["Role"].ToString() == "ADM")
				{
					btn_Inspect.Visible = true;
					btn_Inspect2.Visible = true;
					string script_alert = "<script language='javascript'> $('#btn_Inspect').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#btn_Inspect').click(function () { $('#btn_Inspect').validationEngine('hide'); })</script>";
					ClientScript.RegisterStartupScript(this.GetType(), "btn_Inspect", script_alert);
					string script_alert2 = "<script language='javascript'> $('#btn_Inspect2').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#btn_Inspect2').click(function () { $('#btn_Inspect2').validationEngine('hide'); })</script>";
					ClientScript.RegisterStartupScript(this.GetType(), "btn_Inspect2", script_alert2);

					btn_Inspect.Attributes.Add("onclick", "tech_Inspect(" + ViewState["tt_seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
					btn_Inspect2.Attributes.Add("onclick", "tech_Inspect(" + ViewState["tt_seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
				}
			}
		}
		else
		{
			btn_Inspect.Visible = false;
			btn_Inspect2.Visible = false;
		}
		btn_Inspect_His.Attributes.Add("onclick", "tech_Inspect_his(" + ViewState["tt_seno"].ToString() + " )");
		if (dv[0]["tc_send_datetime"].ToString().Trim().Length > 0)//送件日期
		{
			DateTime dTime = DateTime.Parse(dv[0]["tc_send_datetime"].ToString().Trim());
			lb_send_date.Text = dTime.ToString("yyyy/MM/dd");
		}

		if (Request.ServerVariables["HTTP_VIA"] != null)
		{
			ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
		}
		else
		{
			ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
		}
		Tech_log(ViewState["tt_seno"].ToString(), "檢視承辦單", "", "", "treaty\\TechCase_view.aspx");
		if (dv[0]["tc_擬約幫手"].ToString() == "1")
		{
			LT_擬約幫手.Text = "<font color='red'><b>【擬約幫手】</b></font>";
		}




	}

	/// <summary>
	/// 附件資料
	/// </summary>
	private void Bind_Doc_File()
	{
		DataTable dt = new DataTable();

		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));

			if (ViewState["Role"].ToString() == "ADM")
				sqlCmd.Parameters.AddWithValue("@mode", "file_list_adm");
			else
				sqlCmd.Parameters.AddWithValue("@mode", "file_list");
			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
				gv_doc_file.DataSource = dt;
				gv_doc_file.DataBind();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}


		#endregion
	}

	/// <summary>
	/// 審查資訊
	/// </summary>
	private void BindInspect()
	{
		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@mode", "inspect_list");
			try
			{
				sqlConn.Open();
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				DataTable dt = new DataTable();
				sqlDA.Fill(dt);
				gv_Inspect.DataSource = dt;
				gv_Inspect.DataBind();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	/// <summary>
	/// 取得案號
	/// </summary>
	private void Bindddl_SeqSn()
	{
		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "view_senoTree");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			try
			{
				sqlConn.Open();
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				DataTable dt = new DataTable();
				sqlDA.Fill(dt);
				ddl_SeqSn.DataSource = dt;
				ddl_SeqSn.DataBind();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);
				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	private void Bind_Light()
	{
		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "init_Light");
			try
			{
				sqlConn.Open();
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				DataTable dt = new DataTable();
				sqlDA.Fill(dt);
				rbl_Light.DataSource = dt;
				rbl_Light.DataTextField = "text";
				rbl_Light.DataValueField = "value";
				rbl_Light.DataBind();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	protected void ddl_SeqSn_SelectedIndexChanged(object sender, EventArgs e)
	{
		Response.Redirect("./TechCase_view.aspx?tt_seno=" + ddl_SeqSn.SelectedValue);
	}

	private void Bind_sRC_init(PlaceHolder Plh, string tt_class)
	{
		if (Plh.Visible == false)
			return;

		ArrayList my報院條件說明s = (ArrayList)ViewState["my報院條件說明s" + tt_class];

		DataTable dt = sRC_ver_list(tt_class);

		DataView dv = dt.DefaultView;
		if (dv.Count == 0)
		{
			return;
		}
		ArrayList my報院條件 = new ArrayList();
		ArrayList my報院條件說明 = new ArrayList();
		Literal lbl_trs = new Literal();
		lbl_trs.Text = string.Format(@"<tr><td align='right' valign='top' nowrap='nowrap'>
<div class='font-title titlebackicon'>{0}</div><br/>
", tt_class == "X" ? "案件合理性評估<br/>(單位)" : "公告及計價事項");
		Plh.Controls.Add(lbl_trs);

		if (ViewState["Role"].ToString() == "ADM")
		{
			Plh.Controls.Add(new Literal() { Text = "<div><span class='font-title titlebackicon'>進度</span>" });
			DropDownList ddl = new DropDownList();
			ddl.ID = Server.HtmlEncode("ddlProgress" + tt_class);
			ddl.DataSource = GetProgress(tt_class);
			ddl.DataTextField = "text";
			ddl.DataValueField = "value";
			ddl.DataBind();
			ddl.Items.Insert(0, new ListItem(" ", ""));
			ddl.Enabled = false;
			ddl.Attributes.Add("readOnly", "readonly");
			Plh.Controls.Add(ddl);
		}

		Plh.Controls.Add(new Literal() { Text = "</div></td><td colspan='3' class='lineheight03' valign='top'>" });

		for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
		{
			string tcs_code = dv[sRC_count]["tcs_code"].ToString();
			string tt_sRC_val = "";
			string tt_sRC_desc = "";
			string tt_sRC_id = "";
			foreach (string str_obj in my報院條件說明s)
			{
				string[] arr = str_obj.Split('©');
				if (arr[0] == tcs_code)
				{
					tt_sRC_val = arr[0];
					tt_sRC_desc = arr[1];
					tt_sRC_id = arr[2];
				}
			}
			switch (dv[sRC_count]["tcs_codeCheck"].ToString())
			{
				case "0":
					Literal lbl_title = new Literal();
					lbl_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
					Plh.Controls.Add(lbl_title);
					break;
				case "1":
					CheckBox CBL_x = new CheckBox();
					CBL_x.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
					CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
					CBL_x.Attributes["value"] = tcs_code;
					CBL_x.Enabled = false;
					CBL_x.Attributes.Add("readOnly", "readonly");
					CBL_x.Attributes.Add("style", "color: #0060a4;font-size: 15pt;");
					Plh.Controls.Add(CBL_x);
					my報院條件.Add(CBL_x.ID);

					Plh.Controls.Add(Bind_sRC_tip(tt_class, tcs_code));

					Plh.Controls.Add(new Literal() { Text = string.Format(@"
<div class='timelinebox' >
            <div class='giveboxH givebox lh-lg'>
              {0}
            </div>
        </div>
", Server.HtmlEncode(tt_sRC_desc)) });
					//my報院條件說明.Add(tb_x.ID);
					if (CBL_x.Text == "價金合理性說明")
					{
						Literal lbl_x = new Literal();
						lbl_x.Text = "<div>" + BindContMoneyType() + "</div>";
						Plh.Controls.Add(lbl_x);
					}
					break;
				case "Z":
					CheckBox CBL_y = new CheckBox();
					CBL_y.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
					CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
					CBL_y.Attributes["value"] = tcs_code;
					CBL_y.Enabled = false;
					CBL_y.Attributes.Add("readOnly", "readonly");
					CBL_y.Attributes.Add("style", "color: #0060a4;font-size: 15pt;");
					Plh.Controls.Add(CBL_y);
					my報院條件.Add(CBL_y.ID);
					TextBox tb_y = new TextBox();
					tb_y.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
					tb_y.TextMode = TextBoxMode.MultiLine;
					tb_y.Height = 40;
					tb_y.Width = 450;
					tb_y.Enabled = false;
					tb_y.Attributes.Add("readOnly", "readonly");
					foreach (string str_obj in my報院條件說明s)
					{
						if (str_obj.Split('©')[0].ToString() == tcs_code)
							tb_y.Text = Server.HtmlEncode(str_obj.Split('©')[1].ToString());
					}
					//Plh.Controls.Add(tb_y);
					my報院條件說明.Add(tb_y.ID);

					Plh.Controls.Add(new Literal() { Text = string.Format(@"
<div class='timelinebox' >
            <div class='giveboxH givebox lh-lg'>
              {0}
            </div>
        </div>
", Server.HtmlEncode(tb_y.Text)) });

					break;
			}
			Literal lbl_br1 = new Literal();
			lbl_br1.Text = "<br />";
			Plh.Controls.Add(lbl_br1);
		}
		Literal lbl_tre = new Literal();
		lbl_tre.Text = "<br /><br /></td></tr>";
		Plh.Controls.Add(lbl_tre);
		ViewState["my報院條件"] = my報院條件;
		//ViewState["my報院條件說明"] = my報院條件說明;

	}

	private void Bind_sRC(PlaceHolder Plh, string tt_class)
	{
		Plh.Controls.Clear();
		ArrayList my報院條件 = new ArrayList();
		ArrayList my報院條件說明 = new ArrayList();
		ArrayList my報院條件s = new ArrayList();
		ArrayList my報院條件說明s = new ArrayList();

		DataTable dt = sRC_view(tt_class);

		DataView dvR = dt.DefaultView;
		if (dvR.Count >= 1)
		{
			for (int i = 0; i < dvR.Count; i++)
			{
				my報院條件s.Add(dvR[i]["tt_sRC_val"].ToString());
				my報院條件說明s.Add(dvR[i]["tt_sRC_val"].ToString() + "©" + dvR[i]["tt_sRC_desc"].ToString() + "©" + dvR[i]["tt_sRC_id"].ToString());
			}
		}

		DataTable dt_src = sRC_ver_list(tt_class);
		DataView dv = dt_src.DefaultView;
		if (dv.Count == 0)
		{
			return;
		}
		Literal lbl_trs = new Literal();
		lbl_trs.Text = string.Format(@"<tr><td align='right' valign='top' nowrap='nowrap'>
<div class='font-title titlebackicon'>{0}</div><br/>
", tt_class == "X" ? "案件合理性評估<br/>(單位)" : "公告及計價事項");
		Plh.Controls.Add(lbl_trs);

		if (ViewState["Role"].ToString() == "ADM")
		{
			Plh.Controls.Add(new Literal() { Text = "<div><span class='font-title titlebackicon'>進度</span>" });

			DropDownList ddl = new DropDownList();
			ddl.ID = Server.HtmlEncode("ddlProgress" + tt_class);
			ddl.DataSource = GetProgress(tt_class);
			ddl.DataTextField = "text";
			ddl.DataValueField = "value";
			ddl.DataBind();
			ddl.Enabled = false;
			ddl.Attributes.Add("readOnly", "readonly");
			ddl.Items.Insert(0, new ListItem(" ", ""));
			ddl.SelectedValue = tt_class == "X" ? ViewState["tt_degree"].ToString() : ViewState["tt_status"].ToString();
			Plh.Controls.Add(ddl);
		}
		Plh.Controls.Add(new Literal() { Text = "</div></td><td colspan='3' class='lineheight03' valign='top'>" });


		for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
		{
			string tcs_code = dv[sRC_count]["tcs_code"].ToString();
			string tt_sRC_val = "";
			string tt_sRC_desc = "";
			string tt_sRC_id = "";
			foreach (string str_obj in my報院條件說明s)
			{
				string[] arr = str_obj.Split('©');
				if (arr[0] == tcs_code)
				{
					tt_sRC_val = arr[0];
					tt_sRC_desc = arr[1];
					tt_sRC_id = arr[2];
				}
			}
			switch (dv[sRC_count]["tcs_codeCheck"].ToString())
			{
				case "0":
					Literal lbl_title = new Literal();
					lbl_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
					Plh.Controls.Add(lbl_title);
					break;
				case "1":
					CheckBox CBL_x = new CheckBox();
					CBL_x.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
					CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
					CBL_x.Attributes["value"] = Server.HtmlEncode(tcs_code);
					CBL_x.Attributes["tt_sRC_id"] = tt_sRC_id;
					CBL_x.Attributes.Add("readOnly", "readonly");
					CBL_x.Attributes.Add("style", "color: #0060a4;font-size: 15pt;");
					if (tt_sRC_val == tcs_code)
					{
						CBL_x.Checked = true;
					}

					my報院條件.Add(CBL_x.ID);

					Plh.Controls.Add(CBL_x);

					Plh.Controls.Add(Bind_sRC_tip(tt_class, tcs_code));

					Plh.Controls.Add(new Literal() { Text = string.Format(@"
<div class='timelinebox' >
            <div class='giveboxH givebox lh-lg'>
              {0}
            </div>
        </div>
", Server.HtmlEncode(tt_sRC_desc)) });
					if (CBL_x.Text == "價金合理性說明")
					{
						Literal lbl_x = new Literal();
						lbl_x.Text = "<div>" + BindContMoneyType() + "</div>";
						Plh.Controls.Add(lbl_x);
					}
					break;
				case "Z":
					CheckBox CBL_y = new CheckBox();
					CBL_y.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
					CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
					CBL_y.Attributes["value"] = Server.HtmlEncode(tcs_code);
					CBL_y.Attributes.Add("readOnly", "readonly");
					CBL_y.Attributes.Add("style", "color: #0060a4;font-size: 15pt;");
					Plh.Controls.Add(CBL_y);
					my報院條件.Add(CBL_y.ID);
					foreach (string obj in my報院條件s)
					{
						if (obj.IndexOf(tcs_code) >= 0)
							CBL_y.Checked = true;
					}
					TextBox tb_y = new TextBox();
					tb_y.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
					tb_y.TextMode = TextBoxMode.MultiLine;
					tb_y.Height = 40;
					tb_y.Width = 450;
					tb_y.Attributes.Add("readOnly", "readonly");
					foreach (string str_obj in my報院條件說明s)
					{
						if (str_obj.Split('©')[0].ToString() == tcs_code)
							tb_y.Text = Server.HtmlEncode(str_obj.Split('©')[1].ToString());
					}
					//Plh.Controls.Add(tb_y);
					my報院條件說明.Add(tb_y.ID);

					Plh.Controls.Add(new Literal() { Text = string.Format(@"
<div class='timelinebox' >
            <div class='giveboxH givebox lh-lg'>
              {0}
            </div>
        </div>
", Server.HtmlEncode(tb_y.Text)) });
					break;
			}

			Literal lbl_br1 = new Literal();
			lbl_br1.Text = "<br />";
			Plh.Controls.Add(lbl_br1);
		}

		Literal lbl_tre = new Literal();
		lbl_tre.Text = "<br /><br /></td></tr>";
		Plh.Controls.Add(lbl_tre);
		ViewState["my報院條件"] = my報院條件;
		//ViewState["my報院條件說明"] = my報院條件說明;
		ViewState["my報院條件說明s" + tt_class] = my報院條件說明s;
	}

	protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
	{

	}

	protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
	{
		Literal LB = (Literal)e.Row.FindControl("lbl_company");
		if (LB != null)
			LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + LB.Text.ToString() + "\");' >" + LB.Text.ToString() + "</a>";

	}

	protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
	{

		if (e.CommandName == "xDownload")
		{
			if (oRCM.IsPC(Request) == false)
			{
				Response.Redirect("../DownloadFail.aspx");
			}
			string str_file_url = "";
			string str_filename = "";


			DataTable dt = File_View(e.CommandArgument.ToString());

			DataView dv = dt.DefaultView;
			if (dv.Count >= 1)
			{
				str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
				str_filename = dv[0]["tcdf_doc"].ToString().Trim();
			}
			if (str_file_url != "")
			{
				Tech_log(ViewState["tt_seno"].ToString(), "檔案下載", str_filename, ViewState["xIP"].ToString(), "treaty\\TechCase_modify.aspx");
				Response.Clear();
				Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
				Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
				Response.Flush();
				Response.End();
			}
		}
	}

	protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
	{
		if (e.Row.RowType == DataControlRowType.DataRow)
		{
			Label lb_inspect = (Label)e.Row.FindControl("lbl_inspect");
			if (lb_inspect != null)
			{
				if (lb_inspect.Text == "0")
					lb_inspect.Text = "";
				else
					lb_inspect.Text = "V";
			}

			LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");

			if (lb_del != null)
			{
				lb_del.Visible = btn_FileUp.Visible;
				lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
			}
		}
	}

	public void Tech_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
	{
		return;
		SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
		ssoUser.GetEmpInfo();
		#region --- insert ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_log";
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
			sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
			sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
			sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
			sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
			sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));
			try
			{
				sqlConn.Open();
				sqlCmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);
				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	protected void btn_Edit_Click(object sender, EventArgs e)
	{
		Response.Redirect("./TechCase_modify.aspx?tt_seno=" + ViewState["tt_seno"].ToString());
	}

	protected void gv_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
	{
		Literal LB = (Literal)e.Row.FindControl("lbl_Istatus");
		if (LB != null)
			switch (LB.Text.Trim())
			{
				case "0":
					LB.Text = "";
					break;
				case "1":
					LB.Text = "同意";
					break;
				case "2":
					LB.Text = "退回";
					break;
			}
		LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");
		if (lb_del != null)
		{
			if (ViewState["Role"].ToString().IndexOf("ADM") >= 1)
			{
				if ((ViewState["tt_degree"].ToString().Trim() == "Z") || (ViewState["tt_degree"].ToString().Trim() == "C"))
					lb_del.Visible = false;
				else
					lb_del.Visible = true;
			}
			if (ViewState["Role"].ToString() == "HGroup_ADM")
				lb_del.Visible = false;
		}
	}

	protected void gv_Inspect_RowCommand(object sender, GridViewCommandEventArgs e)
	{
		if (e.CommandName == "xDelete")
		{
			#region --- delete ---
			using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
			{
				SqlCommand sqlCmd = new SqlCommand();
				sqlCmd.Connection = sqlConn;
				sqlCmd.CommandType = CommandType.Text;
				sqlCmd.CommandText = @"delete treaty_case_inspect where tci_no=@tci_no";
				sqlCmd.CommandTimeout = 0;
				sqlCmd.Parameters.Clear();
				sqlCmd.Parameters.AddWithValue("@tci_no", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
				try
				{
					sqlConn.Open();
					sqlCmd.ExecuteNonQuery();
				}
				catch (Exception ex)
				{
					// --- 執行異常通報 --- //
					RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
						ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
						Request,
						Response,
						ex
						);

					oRCM.ErrorExceptionDataToDB(logMail);
				}
				finally
				{
					sqlConn.Close();
				}
			}
			#endregion
			StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
			ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
			BindInspect();
		}
	}

	protected void bt_cancle_Click(object sender, EventArgs e)
	{
		Set_Degree("C");
		Set_Status("Z");

		Tech_log(ViewState["tt_seno"].ToString(), "需求取消", "", ViewState["tt_seno"].ToString(), ViewState["empno"].ToString());
		BindData();
	}

	private void saveFile(string fileName, MemoryStream ms)
	{
		string contenttype = "";
		string filetype = fileName.Split('.')[1].ToLower();

		if (filetype == "docx")
			contenttype = "Application/msword";
		else if (filetype == "pdf")
			contenttype = "Application/pdf";

		Response.Clear();
		Response.ContentType = contenttype;
		Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

		fileName = Server.UrlPathEncode(fileName).Replace("/", "").Replace("..", "");
		string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
		fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
		Response.AddHeader("Content-Disposition", strContentDisposition);

		Response.Buffer = true;
		ms.WriteTo(Response.OutputStream);
		ms.Close();
		Response.End();
	}

	protected void BT_Print_Tag_Click(object sender, EventArgs e)
	{
		string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
		string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=image&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["tt_seno"]);
		Response.Redirect(url, true);
	}

	protected void BT_Print_Excel_Click(object sender, EventArgs e)
	{
		string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
		string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=excel&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["tt_seno"]);
		Response.Redirect(url, true);
	}

	protected void btn_End_Click(object sender, EventArgs e)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();

			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@mode", "un_inspect");

			try
			{
				sqlConn.Open();
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion
		DataView dv_count = dt.DefaultView;

		if (dv_count.Count >= 1)
		{
			if (dv_count[0][0].ToString() != "0")
			{
				StringBuilder script = new StringBuilder("<script type='text/javascript'>alert('還有審查人未審查,不能結案!');</script>");
				ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
			}
			else
			{
				DoEnd();

				Tech_log(ViewState["tt_seno"].ToString(), "結案", "", "", "treaty\\TechCase_view.aspx");
				StringBuilder script = new StringBuilder("<script type='text/javascript'>EndCase(" + ViewState["tt_seno"].ToString() + ")</script>");
				ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
			}
		}
	}

	protected void lnkbtn_廠商中文名稱_Click(object sender, EventArgs e)
	{
		LinkButton CompInfo = sender as LinkButton;
		string Compno = CompInfo.Attributes["Compno"].Trim();
		ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);

	}

	private DataTable sRC_ver_list(string tt_class)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			if (tt_class == "X")
				sqlCmd.Parameters.AddWithValue("@mode", "sRC_ver_list_X_v2");
			else
				sqlCmd.Parameters.AddWithValue("@mode", "sRC_ver_list_x");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@tt_sRC_relation", oRCM.SQLInjectionReplaceAll(rbl_relation.SelectedValue));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}
		return dt;
		#endregion
	}

	private DataTable sRC_view(string str_class)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			if (str_class == "X")
				sqlCmd.Parameters.AddWithValue("@mode", "sRC_view_X");
			else
				sqlCmd.Parameters.AddWithValue("@mode", "sRC_view_x");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}
		return dt;
		#endregion
	}

	private DataTable GetProgress(string str_class)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			if (str_class == "X")
				sqlCmd.Parameters.AddWithValue("@mode", "init_degree");
			else
				sqlCmd.Parameters.AddWithValue("@mode", "init_status");

			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}
		return dt;
		#endregion
	}

	private void Bind_Auth()
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@mode", "Auth");
			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion
		DataView dv_auth = dt.DefaultView;
		if (dv_auth.Count >= 1)
		{
			string str_auth = dv_auth[0]["RW"].ToString();
			if (str_auth == "X")
				Response.Redirect("../NoAuthRight.aspx");

			ViewState["RW"] = Server.HtmlEncode(dv_auth[0]["RW"].ToString());
			ViewState["Role"] = Server.HtmlEncode(dv_auth[0]["Role"].ToString());
			ViewState["Role2"] = Server.HtmlEncode(dv_auth[0]["Role2"].ToString());
			ViewState["分案權限"] = Server.HtmlEncode(dv_auth[0]["分案權限"].ToString());
			ViewState["檢視意見彙整"] = Server.HtmlEncode(dv_auth[0]["檢視意見彙整"].ToString());

			if (ViewState["檢視意見彙整"].ToString() == "0")
			{
				PL_Inspect.Visible = false;
				PL_tt_manage_note.Visible = false;
			}
		}
		else
		{
			Response.Redirect("../NoAuthRight.aspx");
		}
	}

	private void Set_Degree(string degree)
	{
		#region --- modify ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "degree_modify");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@tt_degree", oRCM.SQLInjectionReplaceAll(degree));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
			try
			{
				sqlConn.Open();
				sqlCmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);
				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	private void Set_Status(string status)
	{
		#region --- modify ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "status_modify");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@tt_status", oRCM.SQLInjectionReplaceAll(status));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
			try
			{
				sqlConn.Open();
				sqlCmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);
				oRCM.ErrorExceptionDataToDB(logMail);
			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	private DataTable GetHandle_Empno(string str_class)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			if (str_class == "X")
				sqlCmd.Parameters.AddWithValue("@mode", "handle_X_init");
			else
				sqlCmd.Parameters.AddWithValue("@mode", "handle_x_init");

			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}
		return dt;
		#endregion
	}

	private DataTable File_View(string tcdf_no)
	{
		DataTable dt = new DataTable();

		#region --- query ---
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "file_view");
			sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);

			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion

		return dt;
	}

	private DataTable Case_View()
	{
		DataTable dt = new DataTable();
		#region --- query --- 
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;
			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;
			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@mode", "view_case");
			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion
		return dt;
	}

	private void DoEnd()
	{
		#region 更新主檔
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mode", "end_case");
			sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
			sqlCmd.Parameters.AddWithValue("@tt_remark", oRCM.SQLInjectionReplaceAll(txt_ManageNote.Text));
			sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

			try
			{
				sqlConn.Open();
				sqlCmd.ExecuteNonQuery();
			}
			catch (Exception ex)
			{

				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}
		#endregion
	}

	private string BindContMoneyType()
	{
		DataTable dt = new DataTable();

		#region --- query ---

		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.Text;

			sqlCmd.CommandText = @"
SELECT          TOP (200) code_type, type_desc, code_subtype, subtype_desc, display_order, enable, code_group
FROM              treaty_TechCase_code_table
WHERE          (code_type = '90') AND (code_subtype = '7')
";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);


			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion

		if (dt.Rows.Count > 0)
			return Server.HtmlDecode(Server.HtmlEncode(dt.Rows[0]["subtype_desc"].ToString()));

		return "";
	}

	private Literal Bind_sRC_tip(string tcs_class, string tcs_code)
	{
		#region --- query ---
		DataTable dt = new DataTable();
		using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
		{
			SqlCommand sqlCmd = new SqlCommand();
			sqlCmd.Connection = sqlConn;
			sqlCmd.CommandType = CommandType.StoredProcedure;

			sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

			// --- 避免匯出查詢過久而當掉 --- //
			sqlCmd.CommandTimeout = 0;

			sqlCmd.Parameters.Clear();
			sqlCmd.Parameters.AddWithValue("@mod", "sRC_tip");
			sqlCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(tcs_class));
			sqlCmd.Parameters.AddWithValue("@tip_xid", oRCM.SQLInjectionReplaceAll(tcs_code));

			try
			{
				SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
				sqlDA.Fill(dt);
			}
			catch (Exception ex)
			{
				// --- 執行異常通報 --- //
				RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
					ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
					Request,
					Response,
					ex
					);

				oRCM.ErrorExceptionDataToDB(logMail);

			}
			finally
			{
				sqlConn.Close();
			}
		}

		#endregion

		Literal lt_view = new Literal();

		if (dt.Rows.Count > 0)
		{
			int tip_id = int.Parse(dt.Rows[0]["tip_id"].ToString());

			lt_view.Text = "<a class='iterm_dymanic' rel='../manager/tip_View.aspx?tid=" + tip_id + "' ><img src='../../images/icon_tips.png'></img> </a>";
		}

		return lt_view;
	}


	public bool getAuth()
	{
		string sAuth = string.Empty;
		DataTable dt = new DataTable();
		bool bAuth = false;

		sqlParamList.Clear();
		sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("Auth_ECP")));
		dt = get_SP();

		if (dt.Rows.Count > 0)
		{
			bAuth = true;
		}
		else
		{
			bAuth = false;
		}
		return bAuth;
	}
	public DataTable get_SP()
	{
		common com = new common();
		SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
		sso.GetEmpInfo();
		DataTable dt = new DataTable();
		#region --- query ---
		SqlCommand oCmd = new SqlCommand();
		oCmd.CommandType = CommandType.StoredProcedure;
		oCmd.CommandText = @"esp_TechCase_ECP";
		sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
		sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
		sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
		oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

		dt = com.runParaCmdDS(oCmd).Tables[0];
		#endregion
		return dt;
	}

}