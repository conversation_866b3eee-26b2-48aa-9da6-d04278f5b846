﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="sample.aspx.cs" Inherits="Comp_sample" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script src="../js/jquery-3.6.0.min.js"></script>
    <script src="../js/jquery.colorbox.js"></script>
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript">

        function Find_Empno1(obj, arg_sw) {
            $(".ajax_mesg").colorbox({
                href: "./EmployeeSingleSelect/EmployeeSingleWindow.aspx"
               , iframe: true, width: "700px", height: "630px", transition: "none", opacity: "0.5", overlayClose: false
               , title: '單人挑選'
               , onClosed: function () {
                   $('html, body').css('overflow', '');
                   var strURL = '../Comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                   if (arg_sw == "1") {
                       $.getJSON(strURL + '?callback=?', jsonp_callback1);
                   }
 
               }
            });
        }

        function Find_Empno(obj, arg_sw) {
            $(".ajax_mesg").colorbox({
                href: "./EmployeeMultiSelect/EmployeeWindow.aspx?hfValue=" + $('#' + obj).val()
               , iframe: true, width: "780px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false
               , title: '多人挑選'
               , onClosed: function () {
                   $('html, body').css('overflow', '');
                   var strURL = '../Comp/EmployeeMultiSelect/ret_employee_kw.aspx';
                   if (arg_sw == "2") {
                       $.getJSON(strURL + '?callback=?', jsonp_callback2);
                   }
               }
            });
        }

        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#TB_promoter_empno').val(data.c_com_empno);
                    $('#txt_promoter_name').val(data.c_com_cname);
                    $('#txtTel').val(data.c_com_telext);
                    //$('#txtOrgAbbrName').val(data.c_com_orgName);
                    //$('#x_dept').val(data.c_com_deptid.substr(3, 5));
                    //$('#txt_req_dept').html(data.c_com_deptid);
                    //$('#txtOrgAbbrName').val(data.c_com_orgName);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#TB_empno_string').val(data.c_com_empno);
                    $('#TB_MtName').val(data.c_com_cname);
            }
        }

        function Find_Prjno() {
            $(".ajax_proj").colorbox({
                href:  './Qry_Projno.aspx',
                title: '計畫代號查詢'
                , iframe: true, width: "650px", height: "540px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL =   './ret_Projno.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callbackProj);
                }
            });
        }
        function jsonp_callbackProj(data) {
            switch (data.c_pojno) {
                case "error0":
                    //alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    $("#txt_projno").val(data.c_pojno);
                      reflash_topic("proj_renew", data.c_conttype2);
                    break;
            }
        }
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
    </script>
    <style type="text/css">
        .td_right { text-align: right}
        .td_left{ text-align: left;}
        .TB_ReadOnly{background-color: rgb(236, 233, 216);}
        #colorbox #cboxClose { top: 2px;   right: 0;background:url('../../images/colorbox/colorboxcancel.gif') no-repeat;z-index:10;}
        #cboxTitle{position:absolute;padding-top:2px; top:0px; left:0; text-align:center; width:100%;height:22px; color:#FFF;font-weight:bold;background:url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;}​
        .empty { color: #aaa; }
    </style>


</head>
<body>
    <form id="form1" runat="server">


        多選挑人<asp:textbox id="TB_MtName" runat="server"  class="inputex text-input"></asp:textbox>
        <a onclick="javascript:Find_Empno('TB_empno_string','2');"  >
        <img id="img_promoter_name" src="../images/icon_search.gif" border="0"  class="ajax_mesg btn_mouseout" /></a>&nbsp;
        <div style="display:none"><asp:TextBox ID="TB_empno_string" runat="server"></asp:TextBox></div>


        <hr />
	   單選挑人:<asp:textbox id="txt_promoter_name" runat="server" width="95px"  class="inputex text-input"></asp:textbox>
       <a onclick="javascript:Find_Empno1('txt_promoter_empno','1');" title="挑選客戶">
           <img   src="../images/icon_search.gif" border="0"  class="ajax_mesg btn_mouseout" /></a>&nbsp;
        工號<asp:TextBox ID="TB_promoter_empno" runat="server"  ></asp:TextBox> 

        分機 &nbsp; <asp:textbox id="txtTel" runat="server" width="110px"  class="TB_ReadOnly" readonly="True"></asp:textbox>&nbsp;


        <hr />

	   計畫代號:<asp:textbox id="txt_projno" runat="server" width="95px"  class="inputex text-input"></asp:textbox>
       <a onclick="javascript:Find_Prjno();" title="挑選計畫">  <img   src="../images/icon_search.gif" border="0"  class="ajax_proj btn_mouseout" /></a>&nbsp;


    </form>
</body>
</html>
