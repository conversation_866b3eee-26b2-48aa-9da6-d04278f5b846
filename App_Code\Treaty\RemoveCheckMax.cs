﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Web;

/// <summary>
/// RemoveCheckMax 的摘要描述
/// </summary>
public class RemoveCheckMax
{
    public RemoveCheckMax()
    {
        //
        // TODO: 在這裡新增建構函式邏輯
        //
    }

    public bool IsPC(System.Web.HttpRequest request)
    {
        string p = request.UserAgent;

        //iPad Pro升級到了iPadOS 13，Apple 聲稱在 iPadOS 上使用 Safari 進行桌面級瀏覽
        //因此行動 Safari 似乎也模仿了 macOS 行為和用戶代理程式
        //故iPad Pro 跟 iPad Air無法判斷
        String[] Agents = { "X11", "Linux", "Android", "iPhone", "webOS", "BlackBerry", "SymbianOS", "Windows Phone", "iPad", "iPod" };    //常用的手機系統版本

        bool flag = true;    //建立標誌
        for (int v = 0; v < Agents.Length; v++)
        {
            if (p.IndexOf(Agents[v]) >= 0)
            {
                return false;    //如果是手機版本返回false                
            }
        }

        bool isWin, isMac, isLinux, isUnix = false;
        isWin = p.IndexOf("Win") > -1;  //Windows : Win32、Win16
        isMac = p.IndexOf("Mac") > -1;  //MacOS: MacIntel、Macintosh、MacPPC、Mac68K
        isUnix = p.IndexOf("X11") > -1; //Unix
        isLinux = p.IndexOf("Linux") > -1; //Linux: Linux x86_64、Linux x86_64 X11、Linux ppc64

        //Linux 要多加判斷排除，因為行動裝置Android 系統的Platform參數會是 
        //Linux armv7l、Linux armv8l、Linux aarch64、Linux i686(both Chrome on ChromeOS or Linux x86-64)
        if (p.IndexOf("Linux a") > -1 || p.IndexOf("Linux i") > -1)
        {
            isLinux = false;
        }
        if (isWin || isMac || isLinux || isUnix || flag)
        {
            return true;
        }
        else
        {
            return false;
        }


    }

    public string SQLInjectionReplaceAll(string inputString)
    {
        //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
        //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
        //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
        return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    }

    public string RemoveXss(string str)
    {
        string strOri = str;
        if (!string.IsNullOrEmpty(strOri))
        {
            strOri = Regex.Replace(strOri, "script", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "alert", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "onmouseove", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "onload", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, @"\+last", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, @"\+not", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, @"\+and", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, @"\+or", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, @"\+position", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "%27", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "%28", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "%29", "", RegexOptions.IgnoreCase);
            //strOri = Regex.Replace(strOri, "%3D", "", RegexOptions.IgnoreCase);
            strOri = Regex.Replace(strOri, "%2C", "", RegexOptions.IgnoreCase);
            //防止盲目的SQL
            strOri = Regex.Replace(strOri, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
        }
        return strOri;
    }

    public string HtmlRead(string str)
    {
        return HttpUtility.HtmlEncode(HttpUtility.HtmlDecode(str));
    }

    public string HtmlWrite(string str)
    {
        return HttpUtility.HtmlDecode(HttpUtility.HtmlEncode(str));
    }

    #region Path Traversal
    public string GetValidPathPart(string rootPath, string path)
    {
        rootPath = rootPath.Replace("/", "").Replace("..", "");
        path = path.Replace("/", "").Replace("..", "");
        var p = Path.Combine(rootPath, path);
        DirectoryInfo dir = new DirectoryInfo(p);
        if (!dir.Exists)
            Directory.CreateDirectory(p);

        //string[] dirs = Directory.GetDirectories(rootPath);

        //string result = dirs.Where(s => s.Equals(p, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();

        //if (result == null)
        //{
        //    System.IO.Directory.CreateDirectory(p);//建立資料夾
        //}
        return p;
    }

    private bool IsFileExists(String sFilename)
    {
        //App_Data目錄
        //string dirPath = Server.MapPath(dirPath);
        //DirectoryInfo dir = new DirectoryInfo(@dirPath);
        //列舉全部檔案再比對檔名

        FileInfo file = new FileInfo(sFilename);

        if (file != null && file.Exists)//檔案存在的話
        {
            //原本預期下載Excel檔
            return true;
        }
        else
        {
            return false;
        }
    }

    public bool IsFileExists(string dirPath, string fileName)
    {
        //App_Data目錄
        //string dirPath = Server.MapPath("~/App_Data");
        DirectoryInfo dir = new DirectoryInfo(dirPath);

        //列舉全部檔案再比對檔名
        FileInfo file = dir.EnumerateFiles()
                            .FirstOrDefault(m => m.Name == fileName);

        if (file != null && file.Exists)//檔案存在的話
        {
            //原本預期下載Excel檔
            return true;
        }
        else
        {
            return false;
        }
    }
    #endregion

    #region 取得用戶端IP
    /// <summary>
    /// 取得用戶端IP
    /// </summary>
    /// <returns></returns>
    public string GetClientIP(System.Web.HttpRequest request)
    {
        if (request.ServerVariables["HTTP_X_FORWARDED_FOR"] == null)
        {
            return request.ServerVariables["REMOTE_HOST"].ToString();
        }
        else
        {
            return request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
    }

    /// <summary>
    /// 取得用戶端IP
    /// </summary>
    /// <param name="request">來源Request</param>
    /// <returns>IP Address</returns>
    public string GetIP(System.Web.HttpRequest request)
    {
        string ip = string.Empty;
        string trueIP = string.Empty;

        //先取得是否有經過代理伺服器
        if (request.ServerVariables["HTTP_VIA"] != null)
            ip = request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        else
            ip = request.ServerVariables["REMOTE_ADDR"].ToString();

        if (!string.IsNullOrEmpty(ip))
        {
            //將取得的 IP 字串存入陣列
            string[] ipRange = ip.Split(',');

            //比對陣列中的每個 IP
            if (ip != "::1")
            {
                for (int i = 0; i < ipRange.Length; i++)
                {
                    //剔除內部 IP 及不合法的 IP 後，取出第一個合法 IP
                    if (ipRange[i].Trim().Substring(0, 3) != "10." &&
                        ipRange[i].Trim().Substring(0, 7) != "192.168" &&
                        ipRange[i].Trim().Substring(0, 7) != "172.16." &&
                        CheckIP(ipRange[i].Trim()))
                    {
                        trueIP = ipRange[i].Trim();
                        break;
                    }
                }
            }
            else
            {
                trueIP = "127.0.0.1";
            }

        }
        else
        {
            //沒經過代理伺服器，直接使用 ServerVariables["REMOTE_ADDR"]
            //並經過 CheckIP( ) 的驗證
            trueIP = CheckIP(request.ServerVariables["REMOTE_ADDR"]) ?
                request.ServerVariables["REMOTE_ADDR"] : "";
        }
        //trueIP = "fe80::3928:5e4c:2e54:dee5%13";
        return trueIP;
    }

    public string GetIP()
    {
        string ip = string.Empty;
        string trueIP = string.Empty;

        //先取得是否有經過代理伺服器
        if (HttpContext.Current.Request.ServerVariables["HTTP_VIA"] != null)
            ip = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        else
            ip = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();

        if (!string.IsNullOrEmpty(ip))
        {
            //將取得的 IP 字串存入陣列
            string[] ipRange = ip.Split(',');

            //比對陣列中的每個 IP
            if (ip != "::1")
            {
                for (int i = 0; i < ipRange.Length; i++)
                {
                    //剔除內部 IP 及不合法的 IP 後，取出第一個合法 IP
                    if (ipRange[i].Trim().Substring(0, 3) != "10." &&
                        ipRange[i].Trim().Substring(0, 7) != "192.168" &&
                        ipRange[i].Trim().Substring(0, 7) != "172.16." &&
                        CheckIP(ipRange[i].Trim()))
                    {
                        trueIP = ipRange[i].Trim();
                        break;
                    }
                }
            }
            else
            {
                trueIP = "127.0.0.1";
            }

        }
        else
        {
            //沒經過代理伺服器，直接使用 ServerVariables["REMOTE_ADDR"]
            //並經過 CheckIP( ) 的驗證
            trueIP = CheckIP(HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"]) ?
                HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"] : "";
        }
        //trueIP = "fe80::3928:5e4c:2e54:dee5%13";
        return trueIP;
    }

    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }


    private bool CheckIP(string strPattern)
    {
        // 繼承自：System.Text.RegularExpressions
        // regular: ^\d{1,3}[\.]\d{1,3}[\.]\d{1,3}[\.]\d{1,3}$
        Regex regex = new Regex("^\\d{1,3}[\\.]\\d{1,3}[\\.]\\d{1,3}[\\.]\\d{1,3}$");
        Match m = regex.Match(strPattern);

        return m.Success;
    }
    #endregion

    /// <summary>
    /// ErrorLogMail 的摘要描述
    /// </summary>
    public class ErrorLogMail
    {
        //private HttpRequest request;
        //private HttpResponse response;
        //private Exception ex;

        public string ConnStr { get; private set; }
        public HttpRequest Request { get; private set; }
        public HttpResponse Response { get; private set; }
        public Exception Excep { get; private set; }

        /// <summary>
        /// ErrorLogMail
        /// </summary>
        /// <param name="connStr">資料庫連結字串</param>
        /// <param name="request">request</param>
        /// <param name="response">response</param>
        /// <param name="ex">exception</param>
        public ErrorLogMail(string connStr, HttpRequest request, HttpResponse response, Exception ex)
        {
            //
            // TODO: 在這裡新增建構函式邏輯
            //            
            ConnStr = connStr;
            Request = request;
            Response = response;
            Excep = ex;
        }
    }

    /// <summary>
    /// 異常通報
    /// </summary>
    /// <param name="errorLogMail">異常通報物件</param>
    public void ErrorExceptionDataToDB(ErrorLogMail errorLogMail)
    {
        return;
        using (SqlConnection sqlConn = new SqlConnection(errorLogMail.ConnStr))
        {
            try
            {
                sqlConn.Open();

                // --- 主機IP --- //
                //string serverIP = SQLInjectionReplaceAll(GetClientIP(errorLogMail.Request));
                //string serverIP = SQLInjectionReplaceAll(GetIP());
                string serverIP = SQLInjectionReplaceAll(GetIP(errorLogMail.Request));

                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();

                string empNo = SQLInjectionReplaceAll(ssoUser.empNo);

                string empName = SQLInjectionReplaceAll(ssoUser.empName);

                string mailBody = "發生時間：" + DateTime.Now + "<BR>" +
                                  "主機IP：" + serverIP + "<BR>" +
                                  "發生網頁：" + errorLogMail.Request.Url + "<BR>" +
                                  "使用者：" + empName + "(" + empNo + ")" + "<BR>" +
                                  "錯誤內容：" + errorLogMail.Excep.ToString() + "<BR>";

                SqlCommand exceptionCmd = new SqlCommand();
                exceptionCmd.Connection = sqlConn;

                exceptionCmd.CommandText = @"esp_except_mail";
                exceptionCmd.CommandType = CommandType.StoredProcedure;

                exceptionCmd.Parameters.Clear();
                exceptionCmd.Parameters.AddWithValue("@mailBody", SQLInjectionReplaceAll(mailBody));

                exceptionCmd.ExecuteNonQuery();
            }
            catch
            {
                errorLogMail.Response.Redirect("No_Auth.aspx");
            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

}