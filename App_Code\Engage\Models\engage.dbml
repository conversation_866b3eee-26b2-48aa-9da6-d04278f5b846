﻿<?xml version="1.0" encoding="utf-8"?><Database Name="engagedb" EntityNamespace="Engage" ContextNamespace="Engage" Class="engageDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Connection Mode="WebSettings" ConnectionString="Data Source=140.96.1.105,5555;Initial Catalog=engagedb;User ID=pubengage" SettingsObjectName="System.Configuration.ConfigurationManager.ConnectionStrings" SettingsPropertyName="ConnString" Provider="System.Data.SqlClient" />
  <Table Name="dbo.engage_ibc_empno" Member="ContactInfos">
    <Type Name="ContactInfo">
      <Column Name="empno" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="empname" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="dept" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="memo" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.v_engage_base" Member="v_engage_bases">
    <Type Name="v_engage_base" Id="ID2">
      <Column Name="seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="eb_class" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="eb_orgcd" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="org_abbr_chnm2" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="planno" Type="System.String" DbType="Char(11) NOT NULL" CanBeNull="false" />
      <Column Name="remark1" Type="System.String" DbType="VarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="remark2" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="remark3" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="remark4" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="remark5" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="eb_compname" Type="System.String" DbType="NVarChar(120) NOT NULL" CanBeNull="false" />
      <Column Name="key_compname" Type="System.String" DbType="NVarChar(265)" CanBeNull="true" />
      <Column Name="eb_planname" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="eb_registerdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="execstatus_catalog" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="execstatus" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="execstatus_name" Type="System.String" DbType="VarChar(56)" CanBeNull="true" />
      <Column Name="eb_execdept" Type="System.String" DbType="Char(7) NOT NULL" CanBeNull="false" />
      <Column Name="eb_planer" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_planerempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promoname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promoempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promodept" Type="System.String" DbType="Char(7) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyinname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyindate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyword" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="securelevel" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="eb_registerdate_slash" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_codetbl" Member="engage_codetbls">
    <Type Name="engage_codetbl">
      <Column Name="code_type" Type="System.String" DbType="VarChar(20) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="code_typedesc" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_value" Type="System.String" DbType="VarChar(3) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="code_valuedesc" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="code_relationtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_relationvalue" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="code_order" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code_enabled" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="visitdb.dbo.visit_codetbl" Member="visit_codetbls">
    <Type Name="visit_codetbl">
      <Column Name="code_type" Type="System.String" DbType="Char(20) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="code_typedesc" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_value" Type="System.String" DbType="Char(3) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="code_valuedesc" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="code_relationtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_relationvalue" Type="System.String" DbType="Char(3)" CanBeNull="true" />
      <Column Name="code_order" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code_enabled" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="common.dbo.comper" Member="compers">
    <Type Name="comper">
      <Column Name="com_orgcd" Type="System.String" DbType="NVarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="com_empno" Type="System.String" DbType="NVarChar(11) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="com_badge_v" Type="System.String" DbType="NVarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="com_type" Type="System.String" DbType="NVarChar(3) NOT NULL" CanBeNull="false" />
      <Column Name="com_cname" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="com_ename" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="com_trannm" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="com_deptid" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="com_deptcd" Type="System.String" DbType="NVarChar(5) NOT NULL" CanBeNull="false" />
      <Column Name="com_poscd" Type="System.String" DbType="NVarChar(3) NOT NULL" CanBeNull="false" />
      <Column Name="com_position_nbr" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="com_jobcd" Type="System.String" DbType="NVarChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="com_supervisor_lvl" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="com_arr_date" Type="System.String" DbType="NVarChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="com_arr_date_yyyy" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="com_sex" Type="System.String" DbType="NVarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="com_depcd" Type="System.String" DbType="NVarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="com_dep_date" Type="System.String" DbType="NVarChar(6)" CanBeNull="true" />
      <Column Name="com_dep_date_yyyy" Type="System.String" DbType="NVarChar(8)" CanBeNull="true" />
      <Column Name="com_telext" Type="System.String" DbType="NVarChar(24) NOT NULL" CanBeNull="false" />
      <Column Name="com_mailadd" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="com_cmpnm" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="com_mdate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="com_full_part_time" Type="System.String" DbType="NVarChar(1)" CanBeNull="true" />
      <Column Name="com_telephone" Type="System.String" DbType="NVarChar(25)" CanBeNull="true" />
      <Column Name="com_ipphone" Type="System.String" DbType="NVarChar(25)" CanBeNull="true" />
      <Column Name="com_location" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="com_nhi_status" Type="System.String" DbType="NVarChar(1)" CanBeNull="true" />
      <Column Name="com_recordbook" Type="System.String" DbType="NVarChar(1)" CanBeNull="true" />
      <Column Name="com_report_empno" Type="System.String" DbType="NVarChar(11)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="common.dbo.orgcod" Member="orgcods">
    <Type Name="orgcod">
      <Column Name="org_orgcd" Type="System.String" DbType="NVarChar(2) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="org_full_nm" Type="System.String" DbType="NVarChar(80)" CanBeNull="true" />
      <Column Name="org_orgchnm" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="org_abbr_chnm1" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="org_abbr_chnm2" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="org_abbr_chnm3" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="org_abbr_egnm" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="org_orgegnm" Type="System.String" DbType="NVarChar(60) NOT NULL" CanBeNull="false" />
      <Column Name="org_address" Type="System.String" DbType="NVarChar(140)" CanBeNull="true" />
      <Column Name="zip_code" Type="System.String" DbType="NVarChar(12)" CanBeNull="true" />
      <Column Name="org_tele" Type="System.String" DbType="NVarChar(24)" CanBeNull="true" />
      <Column Name="org_eaddress" Type="System.String" DbType="NVarChar(140)" CanBeNull="true" />
      <Column Name="org_status" Type="System.String" DbType="NVarChar(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="common.dbo.depcod" Member="depcods">
    <Type Name="depcod">
      <Column Name="dep_orgcd" Type="System.String" DbType="NVarChar(2) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="dep_deptid" Type="System.String" DbType="NVarChar(10) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="dep_deptcd" Type="System.String" DbType="NVarChar(5) NOT NULL" CanBeNull="false" />
      <Column Name="dep_deptname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="dep_abbrnm" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="dep_updeptid" Type="System.String" DbType="NVarChar(7)" CanBeNull="true" />
      <Column Name="dep_uporgcd" Type="System.String" DbType="NVarChar(2)" CanBeNull="true" />
      <Column Name="dep_updeptnm" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="dep_manager_id" Type="System.String" DbType="NVarChar(11)" CanBeNull="true" />
      <Column Name="dep_deptename" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="dep_deptenamefull" Type="System.String" DbType="NVarChar(80)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="common.dbo.pro020" Member="pro020s">
    <Type Name="pro020">
      <Column Name="p20_orgcd" Type="System.String" DbType="NChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pojno" Type="System.String" DbType="NChar(10) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="p20_pojcname" Type="System.String" DbType="NChar(40) NOT NULL" CanBeNull="false" />
      <Column Name="p20_profitctr" Type="System.String" DbType="NChar(7) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pjdept" Type="System.String" DbType="NChar(7) NOT NULL" CanBeNull="false" />
      <Column Name="p20_noinchrg" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_nminchrg" Type="System.String" DbType="NChar(25) NOT NULL" CanBeNull="false" />
      <Column Name="p20_nocochrg" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_nmcochrg" Type="System.String" DbType="NChar(25) NOT NULL" CanBeNull="false" />
      <Column Name="p20_nocontact" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_nmcontact" Type="System.String" DbType="NChar(25) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pojamt" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="p20_taxcd" Type="System.Char" DbType="NChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="p20_begdate" Type="System.String" DbType="NChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="p20_enddate" Type="System.String" DbType="NChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pcldate" Type="System.String" DbType="NChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="p20_begdate_8" Type="System.String" DbType="NChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_enddate_8" Type="System.String" DbType="NChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pcldate_8" Type="System.String" DbType="NChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_status" Type="System.String" DbType="NChar(4) NOT NULL" CanBeNull="false" />
      <Column Name="p20_asorgpojno" Type="System.String" DbType="NChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="p20_asorgnm" Type="System.String" DbType="NChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="p20_uppojno" Type="System.String" DbType="NChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_lowest" Type="System.Char" DbType="NChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="p20_degree" Type="System.String" DbType="NChar(3) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pjtype" Type="System.Char" DbType="NChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="p20_revcd" Type="System.Char" DbType="NChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="p20_expcd" Type="System.Char" DbType="NChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="p20_moduid" Type="System.String" DbType="NChar(12) NOT NULL" CanBeNull="false" />
      <Column Name="p20_moddt" Type="System.String" DbType="NChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_keyuid" Type="System.String" DbType="NChar(12) NOT NULL" CanBeNull="false" />
      <Column Name="p20_keydt" Type="System.String" DbType="NChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_techdomain" Type="System.String" DbType="NChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_ipright" Type="System.String" DbType="NChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_corebiz" Type="System.String" DbType="NChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_control" Type="System.String" DbType="NChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_focus" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="p20_fiuser" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_bgyear" Type="System.String" DbType="NChar(4) NOT NULL" CanBeNull="false" />
      <Column Name="p20_plsdate_8" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="p20_pledate_8" Type="System.String" DbType="NVarChar(8) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.engage_base" Member="engage_bases">
    <Type Name="engage_base">
      <Column Name="eb_seqsn" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="eb_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="eb_year" Type="System.String" DbType="Char(4) NOT NULL" CanBeNull="false" />
      <Column Name="eb_orgcd" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="eb_class" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="eb_sn" Type="System.String" DbType="Char(4) NOT NULL" CanBeNull="false" />
      <Column Name="eb_registerdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_compidno" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="eb_compname" Type="System.String" DbType="NVarChar(120) NOT NULL" CanBeNull="false" />
      <Column Name="eb_compbranch" Type="System.String" DbType="NVarChar(120)" CanBeNull="true" />
      <Column Name="eb_srvarea" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="eb_planname" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="eb_tcehtype1" Type="System.String" DbType="Char(3)" CanBeNull="true" />
      <Column Name="eb_tcehtype2" Type="System.String" DbType="Char(3)" CanBeNull="true" />
      <Column Name="eb_industype" Type="System.String" DbType="Char(3) NOT NULL" CanBeNull="false" />
      <Column Name="eb_execstatus" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="eb_execdept" Type="System.String" DbType="Char(7) NOT NULL" CanBeNull="false" />
      <Column Name="eb_execdeptnm" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="eb_planer" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_planerempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_planerext" Type="System.String" DbType="VarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_planermail" Type="System.String" DbType="VarChar(30)" CanBeNull="true" />
      <Column Name="eb_promoname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promoempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promoext" Type="System.String" DbType="VarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promomail" Type="System.String" DbType="VarChar(30)" CanBeNull="true" />
      <Column Name="eb_promodept" Type="System.String" DbType="Char(7) NOT NULL" CanBeNull="false" />
      <Column Name="eb_promodeptnm" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="eb_demand" Type="System.String" DbType="NText NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="eb_success_rate" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="eb_estimfee_code" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyword" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="eb_precontfdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_conttype_b0" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_conttype_b1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_conttype_d4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_conttype_d5" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_conttype_d7" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_gov_assist" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_moea_cont" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_srib_cont" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_master_cont" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_pat_agree" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_gov_plan" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_tenderidno" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="eb_tendername" Type="System.String" DbType="NVarChar(120)" CanBeNull="true" />
      <Column Name="eb_pretechauth" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_projno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="eb_projname" Type="System.String" DbType="NVarChar(40)" CanBeNull="true" />
      <Column Name="eb_repeat" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_actcontno" Type="System.String" DbType="VarChar(15)" CanBeNull="true" />
      <Column Name="eb_cont_contname" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="eb_cont_compname" Type="System.String" DbType="NVarChar(120)" CanBeNull="true" />
      <Column Name="eb_formtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="eb_formno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="eb_regidate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_regiemp" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eb_regiempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eb_eg01_empno2" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="eb_status_regi" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_regi_result" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eb_back_formtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="eb_back_formno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="eb_backdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_backemp" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eb_backempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eb_status_back" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_back_result" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eb_stopdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_stopcause1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause5" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause6" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopcause7" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_stopmemo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="eb_join" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_planhopedate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_signhopedate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_pc_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_treaty_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_halt_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyinname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_keyindate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="eb_modempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eb_modname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="eb_moddate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="eb_ez_batch_time" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="eb_bonu_notice_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_base3_notice_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_umbrella_notice_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_bonus_newver_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_bonus_contract_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_bonus_notice_date" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eb_old_planno" Type="System.String" DbType="VarChar(11)" CanBeNull="true" />
      <Column Name="eb_securelevel" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="eb_ipb" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_ipbi_percent" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eb_ipbc_percent" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eb_ipb_other_desc" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="eb_secure_memo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="eb_memo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="eb_purchase" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_tech_key" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="eb_tech_domain1" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="eb_tech_domain2" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="eb_trl" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="eb_new_venture" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_tech_overlap" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eb_estimfee_type" Type="System.String" CanBeNull="false" />
      <Column Name="eb_estimfee" Type="System.Decimal" CanBeNull="false" />
      <Association Name="engage_base_engage_coplaner" Member="engage_coplaners" ThisKey="eb_seqsn" OtherKey="ecp_seqsn" Type="engage_coplaner" />
      <Association Name="engage_base_engage_visitor" Member="engage_visitors" ThisKey="eb_seqsn" OtherKey="visit_seqsn" Type="engage_visitor" />
    </Type>
  </Table>
  <Table Name="dbo.engage_halt" Member="engage_halts">
    <Type Name="engage_halt">
      <Column Name="eh_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="eh_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eh_serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="eh_startdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eh_starter" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eh_starterempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eh_stopdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="eh_stoper" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eh_stoperempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eh_haltcause1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eh_haltcause2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eh_haltcause3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eh_haltcause4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eh_haltmemo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
    </Type>
  </Table>
  <Table Name="dbo.engage_coplaner" Member="engage_coplaners">
    <Type Name="engage_coplaner">
      <Column Name="ecp_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ecp_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ecp_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ecp_coplaner" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ecp_coplanerempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Association Name="engage_base_engage_coplaner" Member="engage_base" ThisKey="ecp_seqsn" OtherKey="eb_seqsn" Type="engage_base" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_visitor" Member="engage_visitors">
    <Type Name="engage_visitor">
      <Column Name="visit_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="visit_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="visit_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="visit_visitorname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="visit_visitortitle" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="visit_visitorbranch" Type="System.String" DbType="VarChar(30)" CanBeNull="true" />
      <Column Name="visit_visitordept" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="visit_visitorphone" Type="System.String" DbType="VarChar(40)" CanBeNull="true" />
      <Column Name="visit_visitorfax" Type="System.String" DbType="VarChar(15)" CanBeNull="true" />
      <Column Name="visit_visitoremail" Type="System.String" DbType="VarChar(45)" CanBeNull="true" />
      <Column Name="visit_visitorcompidno" Type="System.String" DbType="Char(10) NOT NULL" CanBeNull="false" />
      <Column Name="visit_country" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="visit_city" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="visit_localarea" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="visit_postno" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="visit_addr" Type="System.String" DbType="NVarChar(300) NOT NULL" CanBeNull="false" />
      <Column Name="visit_desc" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Association Name="engage_base_engage_visitor" Member="engage_base" ThisKey="visit_seqsn" OtherKey="eb_seqsn" Type="engage_base" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_reviewer" Member="engage_reviewers">
    <Type Name="engage_reviewer">
      <Column Name="err_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="err_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="err_examempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="err_examname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.engage_plan" Member="engage_plans">
    <Type Name="engage_plan">
      <Column Name="ep_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ep_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ep_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ep_status" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="ep_content" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="ep_benefit_type" Type="System.String" DbType="VarChar(60)" CanBeNull="true" />
      <Column Name="ep_benefit" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="ep_formtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="ep_formno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="ep_sendemp" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="ep_sendempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ep_senddate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="ep_result" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ep_signempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="ep_signname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ep_signdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="ep_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ep_keyinname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ep_keyindate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="ep_modempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ep_modname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ep_moddate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="ep_ez_batch_time" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_plan_review1" Member="engage_plan_review1s">
    <Type Name="engage_plan_review1">
      <Column Name="epv1_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="epv1_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="epv1_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="epv1_status" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="epv1_plan_ver" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="epv1_send_examemp" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="epv1_send_examname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="epv1_send_examdate" Type="System.String" DbType="VarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="epv1_comadjmemo" Type="System.String" DbType="NVarChar(1000)" CanBeNull="true" />
      <Column Name="epv1_comadjemp" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="epv1_comadjname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="epv1_comadjdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="epv1_sendedmail_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_plan_review2" Member="engage_plan_review2s">
    <Type Name="engage_plan_review2">
      <Column Name="epv2_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="epv2_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="epv2_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="epv2_examempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="epv2_examname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="epv2_memo" Type="System.String" DbType="NVarChar(1000)" CanBeNull="true" />
      <Column Name="epv2_signdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="epv2_result" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="epv2_sendedmail_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_join" Member="engage_joins">
    <Type Name="engage_join">
      <Column Name="ej_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ej_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ej_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ej_orgcd" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="ej_orgcdnm" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ej_empno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ej_empname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ej_memo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
    </Type>
  </Table>
  <Table Name="dbo.engage_workitem" Member="engage_workitems">
    <Type Name="engage_workitem">
      <Column Name="ew_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ew_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ew_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ew_serial" Type="System.Int16" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ew_memo" Type="System.String" DbType="NText NOT NULL" CanBeNull="false" UpdateCheck="Never" />
    </Type>
  </Table>
  <Table Name="dbo.engage_attfile1" Member="engage_attfile1s">
    <Type Name="engage_attfile1">
      <Column Name="ea_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ea_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ea_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ea_master_id" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ea_filetype" Type="System.String" DbType="VarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="ea_doc" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ea_uploaddate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ea_filename" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="ea_filetxt" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="ea_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ea_keyinempname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ea_file_url" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_attfile2" Member="engage_attfile2s">
    <Type Name="engage_attfile2">
      <Column Name="ea_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ea_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ea_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ea_filetype" Type="System.String" DbType="VarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="ea_valid" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="ea_doc" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="ea_uploaddate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ea_filename" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="ea_filetxt" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="ea_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ea_keyinempname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ea_file_url" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_techauth" Member="engage_techauths">
    <Type Name="engage_techauth">
      <Column Name="eta_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="eta_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eta_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="eta_techname" Type="System.String" DbType="NChar(130) NOT NULL" CanBeNull="false" />
      <Column Name="eta_resultfrom" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="eta_otherorg" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eta_mature" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eta_authmethod" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eta_area1" Type="System.String" DbType="Char(10)" CanBeNull="true" />
      <Column Name="eta_authitem11" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem12" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem13" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem14" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem15" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem16" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_area2" Type="System.String" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem21" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem22" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem23" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem24" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem25" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_authitem26" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eta_otherarea" Type="System.String" DbType="NVarChar(3)" CanBeNull="true" />
      <Column Name="eta_contribute" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Association Name="engage_techauth_engage_authtechitem" Member="engage_authtechitems" ThisKey="eta_id" OtherKey="eati_eta_id" Type="engage_authtechitem" />
    </Type>
  </Table>
  <Table Name="dbo.engage_authtechitem" Member="engage_authtechitems">
    <Type Name="engage_authtechitem">
      <Column Name="eati_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="eati_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eati_eta_id" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eati_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="eati_techno" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="eati_org" Type="System.String" DbType="Char(2)" CanBeNull="true" />
      <Column Name="eati_techtype" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eati_itemname" Type="System.String" DbType="NVarChar(120)" CanBeNull="true" />
      <Column Name="eati_itemdate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Association Name="engage_techauth_engage_authtechitem" Member="engage_techauth" ThisKey="eati_eta_id" OtherKey="eta_id" Type="engage_techauth" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_authpat" Member="engage_authpats">
    <Type Name="engage_authpat">
      <Column Name="eap_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="eap_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eap_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="eap_patentno" Type="System.String" DbType="VarChar(15) NOT NULL" CanBeNull="false" />
      <Column Name="eap_authmethod" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="eap_authmethod1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authmethod2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authmethod3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authitem1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authitem2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authitem3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_authitem4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_use" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eap_contribute" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="eap_tech_domain" Type="System.String" DbType="NVARCHAR(max)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_cost" Member="engage_costs">
    <Type Name="engage_cost">
      <Column Name="cost_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost_status" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost_valuation" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="cost_evalempno1" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="cost_evalempname1" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_evalempno2" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="cost_evalempname2" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_formtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="cost_formno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="cost_senddate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="cost_sendemp" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="cost_sendempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_result" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_signempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="cost_signname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_signdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="cost_reviewtype" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_viewdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="cost_viewempno" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="cost_viewempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="cost_moneytype" Type="System.String" DbType="VarChar(3)" CanBeNull="true" />
      <Column Name="cost_moneyrate" Type="System.Decimal" DbType="Decimal(18,2)" CanBeNull="true" />
      <Column Name="cost_share" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_sdate" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="cost_edate" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="cost_overhead_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_promo_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_orgmng_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_headmng_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_profit_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_srv_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_other_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_os_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_equip_rate" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost_suggestfee1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_basefee1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_promincome" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_rschfee1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_rschfee2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_patentfee1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_patentfee2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_transcnt1" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="cost_transfee1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_transcnt2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="cost_transfee2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_yearfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_suggestfee2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_basefee2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_techincome" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_techsrvfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost_stock_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk4" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact5" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk5" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact6" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk6" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact71" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact72" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact73" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact74" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk7" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact81" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact82" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact83" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact84" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact85" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk8" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact9" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk9" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact10" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk10" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact11" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk11" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_fact12" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk12" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk21" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk22" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_eval_risk23" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="cost_stock_dsp" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="cost_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="cost_keyinname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="cost_keyindate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="cost_modempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="cost_modname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="cost_moddate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="cost_memo1" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo2" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo3" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo4" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo6" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo7" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo8" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo9" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_memo10" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_iomemo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_transmemo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_techsrvmemo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_evaluememo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_risk_memo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo1" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo2" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo3" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo4" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo5" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo6" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo7" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo8" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo9" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo10" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo11" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo12" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost_eval_memo20" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="cost_ez_batch_time" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_cost_review1" Member="engage_cost_review1s">
    <Type Name="engage_cost_review1">
      <Column Name="ecr1_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ecr1_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ecr1_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ecr1_status" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="ecr1_cost_ver" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="ecr1_send_examemp" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ecr1_send_examname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ecr1_send_examdate" Type="System.String" DbType="VarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="ecr1_comadjmemo" Type="System.String" DbType="NVarChar(1000)" CanBeNull="true" />
      <Column Name="ecr1_comadjemp" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="ecr1_comadjname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ecr1_comadjdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="ecr1_sendedmail_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_cost_review2" Member="engage_cost_review2s">
    <Type Name="engage_cost_review2">
      <Column Name="ecr2_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ecr2_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="ecr2_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ecr2_examempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="ecr2_examname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="ecr2_memo" Type="System.String" DbType="NVarChar(1000)" CanBeNull="true" />
      <Column Name="ecr2_signdate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="ecr2_result" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="ecr2_sendedmail_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costdept1" Member="engage_costdept1s">
    <Type Name="engage_costdept1">
      <Column Name="cost1_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost1_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost1_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost1_dept" Type="System.String" DbType="VarChar(7) NOT NULL" CanBeNull="false" />
      <Column Name="cost1_deptnm" Type="System.String" DbType="NVarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="cost1_per" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_travel" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_material" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_maintain" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_business" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_equipuse" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_other" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_land" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_improve" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_house" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_instrument" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_infoequip" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_transport" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_otherequip" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_rent" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_dircost" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_rate1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man1" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_rate2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man2" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_rate3" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man3" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_rate4" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man4" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_rate5" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man5" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_rate6" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_man6" Type="System.Decimal" DbType="Decimal(18,1)" CanBeNull="true" />
      <Column Name="cost1_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost1_liftfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costdept2" Member="engage_costdept2s">
    <Type Name="engage_costdept2">
      <Column Name="cost2_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost2_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost2_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost2_dept" Type="System.String" DbType="VarChar(7) NOT NULL" CanBeNull="false" />
      <Column Name="cost2_deptnm" Type="System.String" DbType="NVarChar(40) NOT NULL" CanBeNull="false" />
      <Column Name="cost2_enginfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost2_laborfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost2_otherfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost2_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costdept3" Member="engage_costdept3s">
    <Type Name="engage_costdept3">
      <Column Name="cost3_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost3_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost3_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost3_dept" Type="System.String" DbType="VarChar(7) NOT NULL" CanBeNull="false" />
      <Column Name="cost3_deptnm" Type="System.String" DbType="NVarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="cost3_equipfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost3_otherfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost3_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costdept4" Member="engage_costdept4s">
    <Type Name="engage_costdept4">
      <Column Name="cost4_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost4_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost4_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost4_resultfrom" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost4_tafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0) NOT NULL" CanBeNull="false" />
      <Column Name="cost4_tpfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_pafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_ppfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_cash_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_tafbyfixfe_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_tpfbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_pafbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_ppfbyfixfe_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_stock_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_stock_value_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costdept6" Member="engage_costdept6s">
    <Type Name="engage_costdept6">
      <Column Name="cost6_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost6_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost6_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost6_type" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_resultfrom" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_gatherway" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_year" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="cost6_gather" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost6_estimfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost6_memo1" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost6_memo2" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
    </Type>
  </Table>
  <Table Name="dbo.engage_costrate" Member="engage_costrates">
    <Type Name="engage_costrate">
      <Column Name="ecr_org" Type="System.String" DbType="Char(2) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="ecr_rate1" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate2" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate3" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate4" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate5" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate6" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate7" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate8" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_rate9" Type="System.Decimal" DbType="Decimal(5,3)" CanBeNull="true" />
      <Column Name="ecr_onestep_sign" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="ecr_plan_reviewtype" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="ecr_cost_reviewtype" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="ecr_keyinempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="ecr_keyinname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ecr_keyindate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="ecr_modempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="ecr_modname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="ecr_moddate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_empno_default" Member="engage_empno_defaults">
    <Type Name="engage_empno_default">
      <Column Name="eed_org" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="eed_type" Type="System.String" DbType="VarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="eed_empno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="eed_empname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eed_serial" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="eed_keyinempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eed_keyinname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eed_keyindate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="eed_modempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eed_modname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eed_moddate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_emprate" Member="engage_emprates">
    <Type Name="engage_emprate">
      <Column Name="eer_org" Type="System.String" DbType="Char(2) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="eer_rate1" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_rate2" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_rate3" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_rate4" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_rate5" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_rate6" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="eer_keyinempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eer_keyinname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eer_keyindate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="eer_modempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="eer_modname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="eer_moddate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="contract.dbo.cont_codetbl" Member="cont_codetbls">
    <Type Name="cont_codetbl">
      <Column Name="code_type" Type="System.String" DbType="Char(20) NOT NULL" CanBeNull="false" />
      <Column Name="code_typedesc" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_value" Type="System.String" DbType="Char(3) NOT NULL" CanBeNull="false" />
      <Column Name="code_valuedesc" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="code_relationtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="code_relationvalue" Type="System.String" DbType="Char(3)" CanBeNull="true" />
      <Column Name="code_order" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code_enabled" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signcont" Member="engage_signconts">
    <Type Name="engage_signcont">
      <Column Name="esc_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="esc_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esc_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="esc_securelevel" Type="System.String" DbType="Char(2)" CanBeNull="true" />
      <Column Name="esc_lastprintdate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esc_moneytype" Type="System.String" DbType="Char(3)" CanBeNull="true" />
      <Column Name="esc_moneyrate" Type="System.Decimal" DbType="Decimal(18,2)" CanBeNull="true" />
      <Column Name="esc_signcontempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_signcontname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_signcontreason" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="esc_signdate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esc_signresult" Type="System.String" DbType="Char(2)" CanBeNull="true" />
      <Column Name="esc_signtype" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="esc_formtype" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="esc_formno" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="esc_senddate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esc_sendemp" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_sendempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_status" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esc_result" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_signempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_signname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_ez_topaper" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esc_ez_topaper_empno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_formtype_eg08" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="esc_formno_eg08" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="esc_senddate_eg08" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esc_sendemp_eg08" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_sendempname_eg08" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_status_eg08" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="esc_result_eg08" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_signempno_eg08" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esc_signname_eg08" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_comefrom" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="esc_conttype" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="esc_sendedmail_flag" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="esc_signopinion" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="esc_review_empno1" Type="System.String" DbType="VarChar(7)" CanBeNull="true" />
      <Column Name="esc_review_name1" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_review_result1" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="esc_review_memo1" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="esc_review_date1" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="esc_review_empno2" Type="System.String" DbType="VarChar(7)" CanBeNull="true" />
      <Column Name="esc_review_name2" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esc_review_result2" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="esc_review_memo2" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="esc_review_date2" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="esc_mail_president" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esc_keyinempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="esc_keyinname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="esc_keyindate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="esc_modempno" Type="System.String" DbType="Char(6) NOT NULL" CanBeNull="false" />
      <Column Name="esc_modname" Type="System.String" DbType="NVarChar(30) NOT NULL" CanBeNull="false" />
      <Column Name="esc_moddate" Type="System.String" DbType="Char(8) NOT NULL" CanBeNull="false" />
      <Column Name="esc_org_president" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="esc_mail_body" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signitem1" Member="engage_signitem1s">
    <Type Name="engage_signitem1">
      <Column Name="esi1_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="esi1_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esi1_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="esi1_tc_seno" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="esi1_contno_zz" Type="System.String" DbType="VarChar(15)" CanBeNull="true" />
      <Column Name="esi1_contname" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="esil_conttype" Type="System.String" DbType="Char(2)" CanBeNull="true" />
      <Column Name="esi1_contsdate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esi1_contedate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esi1_presigndate" Type="System.String" DbType="Char(8)" CanBeNull="true" />
      <Column Name="esi1_actualsigndate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="esi1_docno" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="esi1_signstatus" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="esi1_tsfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tspay" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tafbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tpfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tpfbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_pafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_pafbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ppfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ppfbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tpfbynonfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_tpfbynonfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ppfbynonfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ppfbynonfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_cooperfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_agencyfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_checked" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="esi1_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_process" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="esi1_process_memo" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="esi1_trandate" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="esi1_tranempno" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="esi1_tranempname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esi1_trancontno" Type="System.String" DbType="VarChar(11)" CanBeNull="true" />
      <Column Name="esi1_actcontno" Type="System.String" DbType="VarChar(15)" CanBeNull="true" />
      <Column Name="esi1_memo7" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="esi1_memo8" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="esi1_web_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esi1_web_date" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="esi1_ipb" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esi1_ipbi_percent" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ipbc_percent" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="esi1_ipb_other_desc" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="esi1_treaty_flag" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esi1_treaty_result" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="esi1_risk" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="esi1_exec_reason" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="esi1_tc_seqno_orignal" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="esi1_cm_id" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signitem1_proc" Member="engage_signitem1_procs">
    <Type Name="engage_signitem1_proc">
      <Column Name="proc_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="proc_esi1_id" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="proc_esi1_seqsn" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="proc_ver" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="proc_date" Type="System.String" DbType="VarChar(8)" CanBeNull="true" />
      <Column Name="proc_itri_empno" Type="System.String" DbType="VarChar(6)" CanBeNull="true" />
      <Column Name="proc_itri_empname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="proc_cust" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="proc_cust_tel" Type="System.String" DbType="VarChar(30)" CanBeNull="true" />
      <Column Name="proc_memo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signitem2" Member="engage_signitem2s">
    <Type Name="engage_signitem2">
      <Column Name="esi2_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="esi2_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esi2_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="esi2_doctype" Type="System.String" DbType="VarChar(2) NOT NULL" CanBeNull="false" />
      <Column Name="esi2_docver" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="esi2_attdocid" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esi2_checked" Type="System.Char" DbType="Char(1) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signitem4" Member="engage_signitem4s">
    <Type Name="engage_signitem4">
      <Column Name="esi4_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="esi4_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esi4_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="esi4_signtype" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
      <Column Name="esi4_signempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esi4_signname" Type="System.String" DbType="NVarChar(30)" CanBeNull="true" />
      <Column Name="esi4_signdept" Type="System.String" DbType="Char(7)" CanBeNull="true" />
      <Column Name="esi4_result" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="esi4_comment" Type="System.String" DbType="NVarChar(1000)" CanBeNull="true" />
      <Column Name="esi4_signdate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="esi4_eztype" Type="System.String" DbType="VarChar(4)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_signitem5" Member="engage_signitem5s">
    <Type Name="engage_signitem5">
      <Column Name="esi5_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="esi5_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="esi5_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="esi5_sendtoempno" Type="System.String" DbType="Char(6)" CanBeNull="true" />
      <Column Name="esi5_sendtoname" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="esi5_sendtodept" Type="System.String" DbType="Char(7)" CanBeNull="true" />
      <Column Name="esi5_nodelete" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_sign_costdept4" Member="engage_sign_costdept4s">
    <Type Name="engage_sign_costdept4">
      <Column Name="cost4_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost4_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost4_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost4_tc_seno" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="cost4_resultfrom" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost4_tafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0) NOT NULL" CanBeNull="false" />
      <Column Name="cost4_tpfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_pafbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_ppfbyfixfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_cash_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_tafbyfixfe_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_tpfbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_pafbyfixfee_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_ppfbyfixfe_stock" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_stock_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost4_stock_value_total" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.engage_sign_costdept6" Member="engage_sign_costdept6s">
    <Type Name="engage_sign_costdept6">
      <Column Name="cost6_id" Type="System.Int64" DbType="BigInt NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="cost6_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="cost6_ver" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="cost6_tc_seno" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="cost6_type" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_resultfrom" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_gatherway" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="cost6_year" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="cost6_gather" Type="System.Decimal" DbType="Decimal(5,2)" CanBeNull="true" />
      <Column Name="cost6_estimfee" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="cost6_memo1" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="cost6_memo2" Type="System.String" DbType="NText" CanBeNull="true" UpdateCheck="Never" />
    </Type>
  </Table>
  <Table Name="" Member="FlowDsps">
    <Type Name="FlowDsp" Id="ID1">
      <Column Name="seqsn" Type="System.Int64" DbType="BigInt" CanBeNull="false" />
      <Column Name="D" Member="刪除" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="A" Member="原核定之基本資料" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="F" Member="正式立案送簽" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="FF" Member="直接立案" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="F_EZ" Member="立案送簽" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="S" Member="停洽送簽" Type="System.String" DbType="Char(1) NOT NULL" CanBeNull="false" />
      <Column Name="S_EZ" Member="停洽送簽EZ" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="R" Member="契約需求申請" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="R1" Member="契約需求單尚在草稿區" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="HB" Member="案件暫停_開始" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="HS" Member="案件暫停_結束" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="PA" Member="規劃構想送審" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="P1" Member="規劃構想送簽" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="PA_SUG" Member="規劃構想_顯示綜整意見" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="P1_EZ" Member="規劃構想_顯示電子表單" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="CA" Member="成本訂價送審" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="C1" Member="成本訂價送簽" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="CA_SUG" Member="成本訂價_顯示綜整意見" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="C1_EZ" Member="成本訂價_顯示電子表單" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="cost_wts" Member="國際工服計價" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="BE" Member="基本資料_編輯" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="SLevel_E" Member="機密等級_編輯" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="PE" Member="規劃構想_編輯" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="CE" Member="成本訂價_編輯" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="CL" Member="成本訂價_是否有權限看此頁面" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="BN" Member="基本資料_新增版次" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="PN" Member="規劃構想_新增版次" Type="System.String" DbType="char(1)" CanBeNull="false" />
      <Column Name="CN" Member="成本訂價_新增版次" Type="System.String" DbType="char(1)" CanBeNull="false" />
    </Type>
  </Table>
  <Function Name="dbo.pr_engage_right_sql" Method="pr_engage_right_sql">
    <Parameter Name="empno" Type="System.String" DbType="VarChar(6)" />
    <Parameter Name="rtn_code1" Type="System.String" DbType="VarChar(1)" Direction="InOut" />
    <Parameter Name="rtn_code2" Type="System.String" DbType="VarChar(1)" Direction="InOut" />
    <Parameter Name="msg1" Type="System.String" DbType="VarChar(1000)" Direction="InOut" />
    <Parameter Name="msg2" Type="System.String" DbType="VarChar(1000)" Direction="InOut" />
    <Parameter Name="msg3" Type="System.String" DbType="VarChar(1000)" Direction="InOut" />
    <Return Type="System.Int32" />
  </Function>
  <Function Name="dbo.pr_engage_right_rw" Method="pr_engage_right_rw">
    <Parameter Name="empno" Type="System.String" DbType="VarChar(6)" />
    <Parameter Name="seqno" Type="System.Int64" DbType="BigInt" />
    <Parameter Name="rtn_code" Type="System.String" DbType="VarChar(5)" Direction="InOut" />
    <Return Type="System.Int32" />
  </Function>
  <Function Name="dbo.pr_engage_flow_dsp" Method="pr_engage_flow_dsp">
    <Parameter Name="seqsn" Type="System.Int64" DbType="BigInt" />
    <Parameter Name="empno" Type="System.String" DbType="VarChar(7)" />
    <ElementType IdRef="ID1" />
  </Function>
  <Function Name="dbo.pr_addnew_engage" Method="pr_addnew_engage">
    <Parameter Name="eb_orgcd" Type="System.String" DbType="Char(2)" />
    <Parameter Name="eb_compidno" Type="System.String" DbType="Char(10)" />
    <Parameter Name="eb_compname" Type="System.String" DbType="NVarChar(120)" />
    <Parameter Name="eb_compbranch" Type="System.String" DbType="NVarChar(120)" />
    <Parameter Name="eb_srvarea" Type="System.String" DbType="VarChar(3)" />
    <Parameter Name="eb_planname" Type="System.String" DbType="NVarChar(200)" />
    <Parameter Name="eb_tcehtype1" Type="System.String" DbType="Char(3)" />
    <Parameter Name="eb_tcehtype2" Type="System.String" DbType="Char(3)" />
    <Parameter Name="eb_industype" Type="System.String" DbType="Char(3)" />
    <Parameter Name="eb_execdept" Type="System.String" DbType="Char(7)" />
    <Parameter Name="eb_execdeptnm" Type="System.String" DbType="NVarChar(20)" />
    <Parameter Name="eb_planer" Type="System.String" DbType="NVarChar(30)" />
    <Parameter Name="eb_planerempno" Type="System.String" DbType="Char(6)" />
    <Parameter Name="eb_planerext" Type="System.String" DbType="VarChar(30)" />
    <Parameter Name="eb_planermail" Type="System.String" DbType="VarChar(30)" />
    <Parameter Name="eb_promoname" Type="System.String" DbType="NVarChar(30)" />
    <Parameter Name="eb_promoempno" Type="System.String" DbType="Char(6)" />
    <Parameter Name="eb_promoext" Type="System.String" DbType="VarChar(30)" />
    <Parameter Name="eb_promomail" Type="System.String" DbType="VarChar(30)" />
    <Parameter Name="eb_promodept" Type="System.String" DbType="Char(7)" />
    <Parameter Name="eb_promodeptnm" Type="System.String" DbType="NVarChar(20)" />
    <Parameter Name="eb_demand" Type="System.String" DbType="NText" />
    <Parameter Name="eb_success_rate" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_estimfee_code" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_keyword" Type="System.String" DbType="NVarChar(500)" />
    <Parameter Name="eb_keyinempno" Type="System.String" DbType="Char(6)" />
    <Parameter Name="eb_keyinname" Type="System.String" DbType="NVarChar(30)" />
    <Parameter Name="eb_conttype_b0" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_conttype_b1" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_conttype_d4" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_conttype_d5" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_conttype_d7" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_gov_assist" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_gov_plan" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_tenderidno" Type="System.String" DbType="VarChar(10)" />
    <Parameter Name="eb_tendername" Type="System.String" DbType="NVarChar(120)" />
    <Parameter Name="eb_pretechauth" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_repeat" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_actcontno" Type="System.String" DbType="VarChar(15)" />
    <Parameter Name="eb_cont_contname" Type="System.String" DbType="NVarChar(200)" />
    <Parameter Name="eb_cont_compname" Type="System.String" DbType="NVarChar(120)" />
    <Parameter Name="eb_precontfdate" Type="System.String" DbType="VarChar(8)" />
    <Parameter Name="eb_moea_cont" Type="System.String" DbType="VarChar(1)" />
    <Parameter Name="eb_srib_cont" Type="System.String" DbType="VarChar(1)" />
    <Parameter Name="eb_master_cont" Type="System.String" DbType="VarChar(1)" />
    <Parameter Name="eb_projno" Type="System.String" DbType="VarChar(10)" />
    <Parameter Name="eb_projname" Type="System.String" DbType="NVarChar(40)" />
    <Parameter Name="eb_signhopedate" Type="System.String" DbType="Char(8)" />
    <Parameter Name="eb_pat_agree" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_securelevel" Type="System.String" DbType="VarChar(2)" />
    <Parameter Name="eb_ipb" Type="System.Char" DbType="Char(1)" />
    <Parameter Name="eb_ipbi_percent" Type="System.Decimal" DbType="Decimal(18,0)" />
    <Parameter Name="eb_ipbc_percent" Type="System.Decimal" DbType="Decimal(18,0)" />
    <Parameter Name="eb_ipb_other_desc" Type="System.String" DbType="Text" />
    <Parameter Name="eb_secure_memo" Type="System.String" DbType="Text" />
    <Parameter Name="eb_purchase" Type="System.String" DbType="VarChar(1)" />
    <Parameter Name="eb_tech_key" Type="System.String" DbType="NVarChar(MAX)" />
    <Parameter Name="eb_tech_domain1" Type="System.String" DbType="VarChar(3)" />
    <Parameter Name="eb_tech_domain2" Type="System.String" DbType="VarChar(3)" />
    <Parameter Name="eb_trl" Type="System.String" DbType="VarChar(3)" />
    <Parameter Name="eb_new_venture" Type="System.String" DbType="VarChar(1)" />
    <Return Type="System.Int32" />
  </Function>
  <Function Name="dbo.pr_del_engage" Method="pr_del_engage">
    <Parameter Name="seqsn" Type="System.Int64" DbType="BigInt" />
    <ElementType Name="pr_del_engageResult">
      <Column Name="eb_status_regi" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="eb_seqsn" Type="System.Int64" DbType="BigInt NOT NULL" CanBeNull="false" />
      <Column Name="eb_execstatus" Type="System.String" DbType="Char(2) NOT NULL" CanBeNull="false" />
    </ElementType>
  </Function>
  <Function Name="dbo.pr_engage_caseentry_query" Method="pr_engage_caseentry_query">
    <Parameter Name="srh_type" Type="System.String" DbType="VarChar(1)" />
    <Parameter Name="srh_execstatus_catalog" Type="System.String" DbType="VarChar(30)" />
    <Parameter Name="srh_keyword" Type="System.String" DbType="NVarChar(100)" />
    <Parameter Name="srh_keyword2" Type="System.String" DbType="NVarChar(100)" />
    <Parameter Name="srh_planname" Type="System.String" DbType="NVarChar(100)" />
    <Parameter Name="srh_planno" Type="System.String" DbType="NVarChar(11)" />
    <Parameter Name="srh_compname" Type="System.String" DbType="NVarChar(120)" />
    <Parameter Name="srh_regdate1" Type="System.String" DbType="VarChar(8)" />
    <Parameter Name="srh_regdate2" Type="System.String" DbType="VarChar(8)" />
    <Parameter Name="srh_remark1" Type="System.String" DbType="NVarChar(1)" />
    <Parameter Name="srh_execdept" Type="System.String" DbType="VarChar(7)" />
    <Parameter Name="srh_planer" Type="System.String" DbType="NVarChar(30)" />
    <Parameter Name="srh_promo" Type="System.String" DbType="NVarChar(30)" />
    <Parameter Name="empno" Type="System.String" DbType="VarChar(6)" />
    <Parameter Name="sortExpression" Type="System.String" DbType="NVarChar(100)" />
    <Parameter Name="pageIndex" Type="System.Int32" DbType="Int" />
    <Parameter Name="pageSize" Type="System.Int32" DbType="Int" />
    <Parameter Name="rowCount" Type="System.Int32" DbType="Int" Direction="InOut" />
    <ElementType IdRef="ID2" />
  </Function>
</Database>