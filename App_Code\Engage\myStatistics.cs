﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myStatistics
	/// </summary>
	public class myStatistics : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _empno;
		private string _empname;
		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}
		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}
		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		#endregion

		public string qry_year1 = string.Empty;
		public string qry_year2 = string.Empty;
		public string qry_source_class = string.Empty;
		public string qry_orglist = string.Empty;


		public myStatistics()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		#region 「提供能查詢單位」資料
		/// <summary>
		/// GetOrgRightQueryByEmpno,依據 empno 員工工號，取得「提供能查詢單位」給 DropDownList_Org 使用。
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public DataTable GetOrgRightQueryByEmpno(string empno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @sql_orgcd_out varchar(1000), @sql_where_out varchar(1000)
exec pr_engage_right_query @empno,@sql_orgcd_out output,@sql_where_out output

select @sql_orgcd_out = replace(@sql_orgcd_out,'org_abbr_chnm2','org_orgcd+''-''+org_abbr_chnm2 AS org_abbr_chnm2')

exec (@sql_orgcd_out)
";

			oCmd.Parameters.AddWithValue("@empno", empno);

			return this.getDataTable(oCmd, CommandType.Text);
		}
		#endregion

		public DataSet GetEngageStatisticsExecstatus()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_statistics_execstatus";

			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@selectedyear1", qry_year1);
			oCmd.Parameters.AddWithValue("@selectedyear2", qry_year2);
			oCmd.Parameters.AddWithValue("@source_class", qry_source_class);
			oCmd.Parameters.AddWithValue("@orglist", qry_orglist);

			DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
			return ds;
		}

		/// <summary>
		/// 取得「洽案統計值明細」的列表
		/// </summary>
		/// <param name="execdept"></param>
		/// <param name="type"></param>
		/// <param name="empno"></param>
		/// <returns></returns>
		public DataSet GetEngageStatExecstatusDetail(string execdept, string type, string empno)
		{

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_stat_execstatus_detail";

			oCmd.Parameters.AddWithValue("@execdept", execdept);
			oCmd.Parameters.AddWithValue("@type", type);
			oCmd.Parameters.AddWithValue("@empno", empno);

			DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
			return ds;
		}


	}
}