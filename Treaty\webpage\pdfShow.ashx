﻿<%@ WebHandler Language="C#" Class="pdfShow" %>

using System;
using System.Web;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using Aspose.Words;
public class pdfShow : IHttpHandler
{

    private SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
    private RemoveCheckMax oRCM = new RemoveCheckMax();
    List<SqlParameter> sqlParamList = new List<SqlParameter>();
    //string UpLoadPath = System.Configuration.ConfigurationManager.AppSettings["FilePathString"];

    private string m_filename;
    private string n_filename;

    /// <summary>
    /// 檔案路徑
    /// </summary>
    private string Filename
    {
        get
        {
            return m_filename;
        }
    }

    /// <summary>
    /// 檔案名稱
    /// </summary>
    private string NFilename
    {
        get
        {
            return n_filename;
        }
    }

    public void ProcessRequest(HttpContext context)
    {
        if (context.Request["seno"] != null || context.Request["guid"] != null)
        {
            string sPath = string.Empty;
            string Seno = context.Request["seno"] != null ? oRCM.RemoveXss(context.Request["seno"].ToString().Trim()) : "";
            string guid = context.Request["guid"] != null ? oRCM.RemoveXss(context.Request["guid"].ToString().Trim()) : "";
            sqlParamList.Clear();
            sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
            sqlParamList.Add(new SqlParameter("signGUID", oRCM.SQLInjectionReplaceAll(guid)));
            sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("View_sign_pdf")));

            DataTable dt = new DataTable();
            dt = get_SP();
            if (dt.Rows.Count > 0 && !string.IsNullOrEmpty(dt.Rows[0]["tcdf_url"].ToString()))
            {
                // 檔案路徑
                sPath = dt.Rows[0]["tcdf_url"].ToString();

                //新路徑含檔名
                string newFilePath = Path.ChangeExtension(sPath, ".pdf");

                // 設定授權路徑
                string licensePath = context.Server.MapPath("~/Aspose.Total.lic");
                // 設定 Aspose.Words 的授權
                Aspose.Words.License license = new Aspose.Words.License();
                license.SetLicense(licensePath);

                // 設定 Aspose.Cells 的授權
                Aspose.Cells.License cellsLicense = new Aspose.Cells.License();
                cellsLicense.SetLicense(licensePath);

                //不是pdf 才需要轉
                if (!sPath.EndsWith(".pdf"))
                {
                    #region 讓Words跟Excel都可以轉PDF
                    // 判斷檔案類型並進行轉換
                    if (sPath.EndsWith(".docx"))
                    {
                        // 轉換 .docx 檔案為 PDF
                        Document doc = new Document(sPath);
                        // 保存為 PDF 文件     
                        doc.Save(newFilePath, SaveFormat.Pdf);
                    }
                    else if (sPath.EndsWith(".xlsx"))
                    {
                        // 轉換 .xlsx 檔案為 PDF
                        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook(sPath);
                        workbook.Save(newFilePath, Aspose.Cells.SaveFormat.Pdf);
                    }
                    #endregion
                }
                m_filename = newFilePath;
                //從路徑中提取文件名
                string fileName = Path.GetFileName(newFilePath);
                if (IsValid())
                {
                    //檔案是否存在
                    if (System.IO.File.Exists(newFilePath))
                    {
                        context.Response.ContentType = "application/pdf";
                        context.Response.AppendHeader(string.Format("Content-Disposition", "inline; filename={0}"), fileName);
                        context.Response.WriteFile(newFilePath);
                        context.Response.End();
                    }
                    else
                    {
                        context.Response.End();
                    }
                }
            }
            else
            {
                context.Response.End();
            }
        }
    }

    public bool IsValid()
    {
        if (IsDangerousPath())
        {
            // 存在危險路徑
            return false;
        }
        else if (!IsFileExist())
        {
            // 檔案不存在
            return false;
        }
        else
        {
            return true;
        }
    }

    public bool IsFileExist()
    {
        bool result = true;

        string filename = Path.GetFileName(this.Filename),
            path = string.Format(@"{0}", this.Filename);

        result = File.Exists(path);
        if (!result)
        {
            //修正原先已經有上傳檔案，檔名內有含有%28, %29字元的檔名。
            m_filename = m_filename.Replace("(", "%28").Replace(")", "%29");
            path = string.Format(@"{0}", this.Filename);
            result = File.Exists(path);
        }

        return result;
    }

    #region 判斷是否存在危險路徑與符號
    /// <summary>
    /// 判斷是否存在危險路徑與符號
    /// </summary>
    public bool IsDangerousPath()
    {
        bool result = false;

        if (this.Filename.Contains("../") || this.Filename.Contains("..\\") || this.Filename.Contains("?"))
            result = true;

        return result;
    }
    #endregion
    public DataTable get_SP()
    {
        Treaty.common com = new Treaty.common();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";

        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));

        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());
        dt = com.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }
    public bool IsReusable
    {
        get
        {
            return false;
        }
    }

}