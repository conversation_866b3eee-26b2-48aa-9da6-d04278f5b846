{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ar.js"], "names": ["module", "exports", "e", "n", "default", "t", "r", "split", "_", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "d", "o", "name", "weekdays", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "preparse", "replace", "postformat", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "+EAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,6EAA6EC,MAAM,KAAKC,EAAE,CAACC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,KAAKC,EAAE,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKC,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDf,MAAM,KAAKgB,cAAc,wCAAwChB,MAAM,KAAKiB,YAAY,gBAAgBjB,MAAM,KAAKkB,OAAOnB,EAAEoB,YAAYpB,EAAEqB,UAAU,EAAEC,aAAa,CAACC,OAAO,SAASC,KAAK,SAASC,EAAE,cAAcC,EAAE,cAAcC,GAAG,WAAWC,EAAE,aAAaC,GAAG,WAAWhB,EAAE,WAAWiB,GAAG,UAAUC,EAAE,WAAWC,GAAG,UAAUC,EAAE,WAAWC,GAAG,YAAYC,SAAS,SAASvC,GAAG,OAAOA,EAAEwC,QAAQ,iBAAgB,SAAUxC,GAAG,OAAOiB,EAAEjB,MAAMwC,QAAQ,KAAK,MAAMC,WAAW,SAASzC,GAAG,OAAOA,EAAEwC,QAAQ,OAAM,SAAUxC,GAAG,OAAOM,EAAEN,MAAMwC,QAAQ,KAAK,MAAME,QAAQ,SAAS1C,GAAG,OAAOA,GAAG2C,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2B,OAAOhD,EAAEC,QAAQgD,OAAOhC,EAAE,MAAK,GAAIA,EAAvyCf,CAAE,EAAQ", "file": "chunks/12.chunk.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ar=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=t(e),r=\"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر\".split(\"_\"),_={1:\"١\",2:\"٢\",3:\"٣\",4:\"٤\",5:\"٥\",6:\"٦\",7:\"٧\",8:\"٨\",9:\"٩\",0:\"٠\"},d={\"١\":\"1\",\"٢\":\"2\",\"٣\":\"3\",\"٤\":\"4\",\"٥\":\"5\",\"٦\":\"6\",\"٧\":\"7\",\"٨\":\"8\",\"٩\":\"9\",\"٠\":\"0\"},o={name:\"ar\",weekdays:\"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت\".split(\"_\"),weekdaysShort:\"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت\".split(\"_\"),weekdaysMin:\"ح_ن_ث_ر_خ_ج_س\".split(\"_\"),months:r,monthsShort:r,weekStart:6,relativeTime:{future:\"بعد %s\",past:\"منذ %s\",s:\"ثانية واحدة\",m:\"دقيقة واحدة\",mm:\"%d دقائق\",h:\"ساعة واحدة\",hh:\"%d ساعات\",d:\"يوم واحد\",dd:\"%d أيام\",M:\"شهر واحد\",MM:\"%d أشهر\",y:\"عام واحد\",yy:\"%d أعوام\"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return d[e]})).replace(/،/g,\",\")},postformat:function(e){return e.replace(/\\d/g,(function(e){return _[e]})).replace(/,/g,\"،\")},ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"D/‏M/‏YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"}};return n.default.locale(o,null,!0),o}));"], "sourceRoot": ""}