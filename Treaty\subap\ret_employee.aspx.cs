﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Newtonsoft.Json;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;


public partial class web_ret_employee : System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class employee
    {
        private string Commonkey;
        private string com_empno;
        private string com_cname;
        private string com_telext;
        private string com_orgcd;
        private string com_deptcd;
        private string com_deptid;
        private string com_dept_name;
        private string com_mailadd;
        private string com_orgName;
        public string c_Commonkey
        {
            get { return Commonkey; }
            set { Commonkey = value; }
        }
        public string c_com_empno
        {
            get { return com_empno; }
            set { com_empno = value; }
        }
        public string c_com_cname
        {
            get { return com_cname; }
            set { com_cname = value; }
        }
        public string c_com_telext
        {
            get { return com_telext; }
            set { com_telext = value; }
        }
        public string c_com_orgcd
        {
            get { return com_orgcd; }
            set { com_orgcd = value; }
        }
        public string c_com_deptcd
        {
            get { return com_deptcd; }
            set { com_deptcd = value; }
        }
        public string c_com_deptid
        {
            get { return com_deptid; }
            set { com_deptid = value; }
        }
        public string c_com_dept_name
        {
            get { return com_dept_name; }
            set { com_dept_name = value; }
        }
        public string c_com_mailadd
        {
            get { return com_mailadd; }
            set { com_mailadd = value; }
        }
        public string c_com_orgName
        {
            get { return com_orgName; }
            set { com_orgName = value; }
        }

    }

    protected void Page_Load(object sender, System.EventArgs e)
    {
        employee u = new employee();
        int int_danger = 0;

        if (Request.QueryString["Commonkey"] != null)
        {
            u.c_Commonkey =Server.HtmlEncode(Request.QueryString["Commonkey"]);
            if (Base64.danger_word_guid(u.c_Commonkey) == "1")
                int_danger++;  //有危險字眼
        }
        else
            int_danger++;  //有危險字眼


        if (int_danger > 0)
        {   //有危險字眼
            u.c_com_empno = "";
            u.c_com_cname = "danger";
            u.c_com_telext = "";
            u.c_com_orgcd = "";
            u.c_com_deptcd = "";
            u.c_com_deptid = "";
            u.c_com_dept_name = "";
            u.c_com_mailadd = "";
        }
        else
        {

            //u.c_Commonkey =  Request.QueryString["Commonkey"];
            //SqlCommand oCmd = new SqlCommand();
            //string strSQL = string.Format(@" SELECT com_empno , com_cname , com_telext, com_orgcd, com_deptcd , com_deptid ,(select dep_deptname  from common..depcod where dep_deptid=com_deptid ) com_dept_name , com_mailadd ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from   common..comper , swapdb..tbl_getemp  where  com_empno = gc_colreturnvalue and gc_colkey =@gc_colkey ");
            //oCmd.Parameters.AddWithValue("@gc_colkey", u.c_Commonkey);
            //oCmd.CommandText = strSQL;
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["common"].ConnectionString);
            //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            //oda.Fill(ds);

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"SELECT com_empno , com_cname , com_telext, com_orgcd, com_deptcd , com_deptid ,(select dep_deptname  from common..depcod where dep_deptid=com_deptid ) com_dept_name , com_mailadd ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from   common..comper , swapdb..tbl_getemp  where  com_empno = gc_colreturnvalue and gc_colkey =@gc_colkey ";


                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@gc_colkey", oRCM.SQLInjectionReplaceAll(u.c_Commonkey));


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(ds);


                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            if (ds.Tables[0].DefaultView.Count > 0)
            {
                u.c_com_empno = ds.Tables[0].Rows[0]["com_empno"].ToString().Replace("\"", "\\\"");
                u.c_com_cname = ds.Tables[0].Rows[0]["com_cname"].ToString().Replace("\"", "\\\"");
                u.c_com_telext = ds.Tables[0].Rows[0]["com_telext"].ToString().Replace("\"", "\\\"");
                u.c_com_orgcd = ds.Tables[0].Rows[0]["com_orgcd"].ToString().Replace("\"", "\\\"");
                u.c_com_deptcd = ds.Tables[0].Rows[0]["com_deptcd"].ToString().Replace("\"", "\\\"");
                u.c_com_deptid = ds.Tables[0].Rows[0]["com_deptid"].ToString().Replace("\"", "\\\"");
                u.c_com_dept_name = ds.Tables[0].Rows[0]["com_dept_name"].ToString().Replace("\"", "\\\"");
                u.c_com_mailadd = ds.Tables[0].Rows[0]["com_mailadd"].ToString().Replace("\"", "\\\"");
                u.c_com_orgName = ds.Tables[0].Rows[0]["orgName"].ToString().Replace("\"", "\\\"");
            }
            else
            {
                u.c_com_empno = "";
                u.c_com_cname = "error0";
                u.c_com_telext = "";
                u.c_com_orgcd = "";
                u.c_com_deptcd = "";
                u.c_com_deptid = "";
                u.c_com_dept_name = "";
                u.c_com_mailadd = "";
                u.c_com_orgName = "";

            }
            string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
            if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
            {
                string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
                j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
            }
            Response.Write(j);  //輸出JSONP的內容
        }

    }
}