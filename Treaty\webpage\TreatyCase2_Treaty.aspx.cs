﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class TreatyCase2_Treaty : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {


            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyCase2_Auth";
                //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Auth";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_auth = dt.DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if ((str_auth == "X") || (str_auth == "R"))
                        Response.Redirect("../NoAuthRight.aspx");
                }
                ViewState["sub_seno"] = "0";
                if (Request["sub_seno"] != null)
                {
                    int sj = 0;
                    if (!(int.TryParse(Request["sub_seno"], out sj)))
                        Response.Redirect("../danger.aspx");
                    ViewState["sub_seno"] = Request["sub_seno"];
                    //SDS_SC.SelectParameters.Clear();
                    //SDS_SC.SelectCommand = " SELECT tc2u_seno, tc2u_treaty_no+'<br>'+ tc2u_treaty_name treaty, tc2u_abstract  FROM  treaty_case2_followup  where tc2u_sub_seno = @subseno ";
                    //SDS_SC.SelectParameters.Add("subseno", ViewState["sub_seno"].ToString());
                    //System.Data.DataView dv = (DataView)SDS_SC.Select(new DataSourceSelectArguments());
                    #region --- query ---

                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @" SELECT tc2u_seno, tc2u_treaty_no+'<br>'+ tc2u_treaty_name treaty, tc2u_abstract  FROM  treaty_case2_followup  where tc2u_sub_seno = @subseno ";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@subseno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));

                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dt = new DataTable();
                            sqlDA.Fill(dt);

                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv = dt.DefaultView;
                    if (dv.Count >= 1)
                    {
                        TB_Treaty.Text = dv[0]["treaty"].ToString();
                        TB_Docu.Text = dv[0]["tc2u_abstract"].ToString();
                    }
                }
            }
            else
                Response.Redirect("../NoAuthRight.aspx");
        }


    }


    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (TB_Docu.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        //this.SDS_SC.UpdateParameters.Clear();
        //this.SDS_SC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.UpdateCommand = "esp_TreatyCase2_followup";
        //this.SDS_SC.UpdateParameters.Add("seno", ViewState["seno"].ToString());
        //this.SDS_SC.UpdateParameters.Add("sub_seno", ViewState["sub_seno"].ToString());
        //this.SDS_SC.UpdateParameters.Add("treaty_seno", "0");
        //this.SDS_SC.UpdateParameters.Add("abstract", TB_Docu.Text);
        //this.SDS_SC.UpdateParameters.Add("mode", "update");
        //this.SDS_SC.DataBind();
        //this.SDS_SC.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_followup";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@treaty_seno", "0");
            sqlCmd.Parameters.AddWithValue("@abstract", oRCM.SQLInjectionReplaceAll(TB_Docu.Text));
            sqlCmd.Parameters.AddWithValue("@mode", "update");


            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);



    }
}