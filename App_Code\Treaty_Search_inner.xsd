﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Treaty_Search_inner" targetNamespace="http://tempuri.org/Treaty_Search_inner.xsd" xmlns:mstns="http://tempuri.org/Treaty_Search_inner.xsd" xmlns="http://tempuri.org/Treaty_Search_inner.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="ConnString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.ConnString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="esp_treaty_search_case_inner_rptTableAdapter" GeneratorDataComponentClassName="esp_treaty_search_case_inner_rptTableAdapter" Name="esp_treaty_search_case_inner_rpt" UserDataComponentName="esp_treaty_search_case_inner_rptTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnString (Web.config)" DbObjectName="engagedb.dbo.esp_treaty_search_case_inner_rpt" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.esp_treaty_search_case_inner_rpt</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@empno" Precision="0" ProviderType="NVarChar" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@SortExpression" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="nvarchar" DbType="String" Direction="Input" ParameterName="@SortDirection" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="tmp_uid" DataSetColumn="tmp_uid" />
              <Mapping SourceColumn="tc_seno" DataSetColumn="tc_seno" />
              <Mapping SourceColumn="tmp_show_flag" DataSetColumn="tmp_show_flag" />
              <Mapping SourceColumn="tmp_author_flag" DataSetColumn="tmp_author_flag" />
              <Mapping SourceColumn="tmp_new_version" DataSetColumn="tmp_new_version" />
              <Mapping SourceColumn="tmp_case_actno" DataSetColumn="tmp_case_actno" />
              <Mapping SourceColumn="tmp_case_name" DataSetColumn="tmp_case_name" />
              <Mapping SourceColumn="tmp_status_flag" DataSetColumn="tmp_status_flag" />
              <Mapping SourceColumn="tmp_status_name" DataSetColumn="tmp_status_name" />
              <Mapping SourceColumn="tmp_defer" DataSetColumn="tmp_defer" />
              <Mapping SourceColumn="tmp_other_status" DataSetColumn="tmp_other_status" />
              <Mapping SourceColumn="tmp_other_status_name" DataSetColumn="tmp_other_status_name" />
              <Mapping SourceColumn="org_name" DataSetColumn="org_name" />
              <Mapping SourceColumn="tc_compname" DataSetColumn="tc_compname" />
              <Mapping SourceColumn="tc_case_closedate" DataSetColumn="tc_case_closedate" />
              <Mapping SourceColumn="tc_petition_day" DataSetColumn="tc_petition_day" />
              <Mapping SourceColumn="tc_prefinish_date" DataSetColumn="tc_prefinish_date" />
              <Mapping SourceColumn="tc_promoter_name" DataSetColumn="tc_promoter_name" />
              <Mapping SourceColumn="tc_handle_name" DataSetColumn="tc_handle_name" />
              <Mapping SourceColumn="tc_status" DataSetColumn="tc_status" />
              <Mapping SourceColumn="tc_reqdate" DataSetColumn="tc_reqdate" />
              <Mapping SourceColumn="tmp_conttype" DataSetColumn="tmp_conttype" />
              <Mapping SourceColumn="tmp_conttype_name" DataSetColumn="tmp_conttype_name" />
              <Mapping SourceColumn="tmp_case_style" DataSetColumn="tmp_case_style" />
              <Mapping SourceColumn="tc_hec_flag" DataSetColumn="tc_hec_flag" />
              <Mapping SourceColumn="tmp_language" DataSetColumn="tmp_language" />
              <Mapping SourceColumn="tmp_process_date" DataSetColumn="tmp_process_date" />
              <Mapping SourceColumn="tmp_case_closedate" DataSetColumn="tmp_case_closedate" />
              <Mapping SourceColumn="tmp_case_flag" DataSetColumn="tmp_case_flag" />
              <Mapping SourceColumn="tc_money" DataSetColumn="tc_money" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Treaty_Search_inner" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Treaty_Search_inner" msprop:Generator_DataSetName="Treaty_Search_inner">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="esp_treaty_search_case_inner_rpt" msprop:Generator_TableClassName="esp_treaty_search_case_inner_rptDataTable" msprop:Generator_TableVarName="tableesp_treaty_search_case_inner_rpt" msprop:Generator_TablePropName="esp_treaty_search_case_inner_rpt" msprop:Generator_RowDeletingName="esp_treaty_search_case_inner_rptRowDeleting" msprop:Generator_RowChangingName="esp_treaty_search_case_inner_rptRowChanging" msprop:Generator_RowEvHandlerName="esp_treaty_search_case_inner_rptRowChangeEventHandler" msprop:Generator_RowDeletedName="esp_treaty_search_case_inner_rptRowDeleted" msprop:Generator_UserTableName="esp_treaty_search_case_inner_rpt" msprop:Generator_RowChangedName="esp_treaty_search_case_inner_rptRowChanged" msprop:Generator_RowEvArgName="esp_treaty_search_case_inner_rptRowChangeEvent" msprop:Generator_RowClassName="esp_treaty_search_case_inner_rptRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="tmp_uid" msprop:Generator_ColumnVarNameInTable="columntmp_uid" msprop:Generator_ColumnPropNameInRow="tmp_uid" msprop:Generator_ColumnPropNameInTable="tmp_uidColumn" msprop:Generator_UserColumnName="tmp_uid" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_seno" msprop:Generator_ColumnVarNameInTable="columntc_seno" msprop:Generator_ColumnPropNameInRow="tc_seno" msprop:Generator_ColumnPropNameInTable="tc_senoColumn" msprop:Generator_UserColumnName="tc_seno" type="xs:long" minOccurs="0" />
              <xs:element name="tmp_show_flag" msprop:Generator_ColumnVarNameInTable="columntmp_show_flag" msprop:Generator_ColumnPropNameInRow="tmp_show_flag" msprop:Generator_ColumnPropNameInTable="tmp_show_flagColumn" msprop:Generator_UserColumnName="tmp_show_flag" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_author_flag" msprop:Generator_ColumnVarNameInTable="columntmp_author_flag" msprop:Generator_ColumnPropNameInRow="tmp_author_flag" msprop:Generator_ColumnPropNameInTable="tmp_author_flagColumn" msprop:Generator_UserColumnName="tmp_author_flag" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_new_version" msprop:Generator_ColumnVarNameInTable="columntmp_new_version" msprop:Generator_ColumnPropNameInRow="tmp_new_version" msprop:Generator_ColumnPropNameInTable="tmp_new_versionColumn" msprop:Generator_UserColumnName="tmp_new_version" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_case_actno" msprop:Generator_ColumnVarNameInTable="columntmp_case_actno" msprop:Generator_ColumnPropNameInRow="tmp_case_actno" msprop:Generator_ColumnPropNameInTable="tmp_case_actnoColumn" msprop:Generator_UserColumnName="tmp_case_actno" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="25" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_case_name" msprop:Generator_ColumnVarNameInTable="columntmp_case_name" msprop:Generator_ColumnPropNameInRow="tmp_case_name" msprop:Generator_ColumnPropNameInTable="tmp_case_nameColumn" msprop:Generator_UserColumnName="tmp_case_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_status_flag" msprop:Generator_ColumnVarNameInTable="columntmp_status_flag" msprop:Generator_ColumnPropNameInRow="tmp_status_flag" msprop:Generator_ColumnPropNameInTable="tmp_status_flagColumn" msprop:Generator_UserColumnName="tmp_status_flag" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_status_name" msprop:Generator_ColumnVarNameInTable="columntmp_status_name" msprop:Generator_ColumnPropNameInRow="tmp_status_name" msprop:Generator_ColumnPropNameInTable="tmp_status_nameColumn" msprop:Generator_UserColumnName="tmp_status_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_defer" msprop:Generator_ColumnVarNameInTable="columntmp_defer" msprop:Generator_ColumnPropNameInRow="tmp_defer" msprop:Generator_ColumnPropNameInTable="tmp_deferColumn" msprop:Generator_UserColumnName="tmp_defer" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_other_status" msprop:Generator_ColumnVarNameInTable="columntmp_other_status" msprop:Generator_ColumnPropNameInRow="tmp_other_status" msprop:Generator_ColumnPropNameInTable="tmp_other_statusColumn" msprop:Generator_UserColumnName="tmp_other_status" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_other_status_name" msprop:Generator_ColumnVarNameInTable="columntmp_other_status_name" msprop:Generator_ColumnPropNameInRow="tmp_other_status_name" msprop:Generator_ColumnPropNameInTable="tmp_other_status_nameColumn" msprop:Generator_UserColumnName="tmp_other_status_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="org_name" msprop:Generator_ColumnVarNameInTable="columnorg_name" msprop:Generator_ColumnPropNameInRow="org_name" msprop:Generator_ColumnPropNameInTable="org_nameColumn" msprop:Generator_UserColumnName="org_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_compname" msprop:Generator_ColumnVarNameInTable="columntc_compname" msprop:Generator_ColumnPropNameInRow="tc_compname" msprop:Generator_ColumnPropNameInTable="tc_compnameColumn" msprop:Generator_UserColumnName="tc_compname" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_case_closedate" msprop:Generator_ColumnVarNameInTable="columntc_case_closedate" msprop:Generator_ColumnPropNameInRow="tc_case_closedate" msprop:Generator_ColumnPropNameInTable="tc_case_closedateColumn" msprop:Generator_UserColumnName="tc_case_closedate" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_petition_day" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columntc_petition_day" msprop:Generator_ColumnPropNameInRow="tc_petition_day" msprop:Generator_ColumnPropNameInTable="tc_petition_dayColumn" msprop:Generator_UserColumnName="tc_petition_day" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_prefinish_date" msprop:Generator_ColumnVarNameInTable="columntc_prefinish_date" msprop:Generator_ColumnPropNameInRow="tc_prefinish_date" msprop:Generator_ColumnPropNameInTable="tc_prefinish_dateColumn" msprop:Generator_UserColumnName="tc_prefinish_date" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_promoter_name" msprop:Generator_ColumnVarNameInTable="columntc_promoter_name" msprop:Generator_ColumnPropNameInRow="tc_promoter_name" msprop:Generator_ColumnPropNameInTable="tc_promoter_nameColumn" msprop:Generator_UserColumnName="tc_promoter_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_handle_name" msprop:Generator_ColumnVarNameInTable="columntc_handle_name" msprop:Generator_ColumnPropNameInRow="tc_handle_name" msprop:Generator_ColumnPropNameInTable="tc_handle_nameColumn" msprop:Generator_UserColumnName="tc_handle_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_status" msprop:Generator_ColumnVarNameInTable="columntc_status" msprop:Generator_ColumnPropNameInRow="tc_status" msprop:Generator_ColumnPropNameInTable="tc_statusColumn" msprop:Generator_UserColumnName="tc_status" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_reqdate" msprop:Generator_ColumnVarNameInTable="columntc_reqdate" msprop:Generator_ColumnPropNameInRow="tc_reqdate" msprop:Generator_ColumnPropNameInTable="tc_reqdateColumn" msprop:Generator_UserColumnName="tc_reqdate" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_conttype" msprop:Generator_ColumnVarNameInTable="columntmp_conttype" msprop:Generator_ColumnPropNameInRow="tmp_conttype" msprop:Generator_ColumnPropNameInTable="tmp_conttypeColumn" msprop:Generator_UserColumnName="tmp_conttype" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_conttype_name" msprop:Generator_ColumnVarNameInTable="columntmp_conttype_name" msprop:Generator_ColumnPropNameInRow="tmp_conttype_name" msprop:Generator_ColumnPropNameInTable="tmp_conttype_nameColumn" msprop:Generator_UserColumnName="tmp_conttype_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_case_style" msprop:Generator_ColumnVarNameInTable="columntmp_case_style" msprop:Generator_ColumnPropNameInRow="tmp_case_style" msprop:Generator_ColumnPropNameInTable="tmp_case_styleColumn" msprop:Generator_UserColumnName="tmp_case_style" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_hec_flag" msprop:Generator_ColumnVarNameInTable="columntc_hec_flag" msprop:Generator_ColumnPropNameInRow="tc_hec_flag" msprop:Generator_ColumnPropNameInTable="tc_hec_flagColumn" msprop:Generator_UserColumnName="tc_hec_flag" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_language" msprop:Generator_ColumnVarNameInTable="columntmp_language" msprop:Generator_ColumnPropNameInRow="tmp_language" msprop:Generator_ColumnPropNameInTable="tmp_languageColumn" msprop:Generator_UserColumnName="tmp_language" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_process_date" msprop:Generator_ColumnVarNameInTable="columntmp_process_date" msprop:Generator_ColumnPropNameInRow="tmp_process_date" msprop:Generator_ColumnPropNameInTable="tmp_process_dateColumn" msprop:Generator_UserColumnName="tmp_process_date" type="xs:int" minOccurs="0" />
              <xs:element name="tmp_case_closedate" msprop:Generator_ColumnVarNameInTable="columntmp_case_closedate" msprop:Generator_ColumnPropNameInRow="tmp_case_closedate" msprop:Generator_ColumnPropNameInTable="tmp_case_closedateColumn" msprop:Generator_UserColumnName="tmp_case_closedate" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_case_flag" msprop:Generator_ColumnVarNameInTable="columntmp_case_flag" msprop:Generator_ColumnPropNameInRow="tmp_case_flag" msprop:Generator_ColumnPropNameInTable="tmp_case_flagColumn" msprop:Generator_UserColumnName="tmp_case_flag" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_money" msprop:Generator_ColumnVarNameInTable="columntc_money" msprop:Generator_ColumnPropNameInRow="tc_money" msprop:Generator_ColumnPropNameInTable="tc_moneyColumn" msprop:Generator_UserColumnName="tc_money" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>