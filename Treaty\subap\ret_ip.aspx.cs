﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;


public partial class web_ret_ip : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class IP
    {
        private string docno;
        private string patentno;
        private string pntcnn;
        private string applynation;
        private string cerpntno;
        public string c_docno
        {
            get { return docno; }
            set { docno = value; }
        }
        public string c_patentno
        {
            get { return patentno; }
            set { patentno = value; }
        }
        public string c_pntcnn
        {
            get { return pntcnn; }
            set { pntcnn = value; }
        }
        public string c_applynation
        {
            get { return applynation; }
            set { applynation = value; }
        }
        public string c_cerpntno
        {
            get { return cerpntno; }
            set { cerpntno = value; }
        }
    }

    protected void Page_Load(object sender, System.EventArgs e)
    {
        IP u = new IP();
        SqlCommand oCmd = new SqlCommand();
        if (Session["Find_IP"] != null)
        {
            //string strSQL = @"  select  p40_docno ,p40_patentno,p40_pntcnn , p40_applynation,p40_cerpntno   from  OpenDatasource('SQLOLEDB','Data Source=ITRIDPS.ITRI.ds,2830;User ID=pubptadm;Password=********').patent.dbo.v_pat040_contract where p40_docno = @docno ";
            //oCmd.Parameters.AddWithValue("@docno", Session["Find_IP"].ToString());
            //oCmd.CommandText = strSQL;
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString);
            //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            //oda.Fill(ds);

            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"select  p40_docno ,p40_patentno,p40_pntcnn , p40_applynation,p40_cerpntno   from   patent.dbo.v_pat040_contract where p40_docno = @docno ";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@docno", oRCM.SQLInjectionReplaceAll(Session["Find_IP"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(ds);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            if (ds.Tables[0].DefaultView.Count == 1)
            {
                u.c_docno = ds.Tables[0].Rows[0][0].ToString().Trim().Replace("\"", "\\\"");
                u.c_patentno = ds.Tables[0].Rows[0][1].ToString().Trim().Replace("\"", "\\\"");
                u.c_pntcnn = ds.Tables[0].Rows[0][2].ToString().Trim().Replace("\"", "\\\"");
                u.c_applynation = ds.Tables[0].Rows[0][3].ToString().Trim().Replace("\"", "\\\"");
                u.c_cerpntno = ds.Tables[0].Rows[0][4].ToString().Trim().Replace("\"", "\\\"");
            }
            if (ds.Tables[0].DefaultView.Count == 0)
            {
                u.c_docno = "";
                u.c_patentno = "error0";
                u.c_pntcnn = "";
                u.c_applynation = "";
                u.c_cerpntno = "";
            }
            if (ds.Tables[0].DefaultView.Count > 1)
            {
                u.c_docno = "";
                u.c_patentno = "error2";
                u.c_pntcnn = "";
                u.c_applynation = "";
                u.c_cerpntno = "";
            }
        }
        else
        {
            u.c_docno = "";
            u.c_patentno = "error0";
            u.c_pntcnn = "";
            u.c_applynation = "";
            u.c_cerpntno = "";
        }


        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        //20120118 測試輸出為BIG5 
        // Response.ContentType = "text/html";
        // Response.ContentEncoding = System.Text.Encoding.GetEncoding("big5");

        Response.Write(j);  //輸出JSONP的內容
    } 
    
}