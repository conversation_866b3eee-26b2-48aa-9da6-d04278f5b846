﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyApplyQ : Treaty.common
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    private string h_COMPno  //案件編號/名稱
    {
        get
        {
            string custName = h_compno.Value.Trim().Replace("-", "").Replace(" ", "");
            if (Regex.IsMatch(custName, "^[,0-9A-Za-z]*$") == false)
                Response.Redirect("../danger.aspx");
            return h_compno.Value.Trim();
        }
    }
    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));
        if (Request["seno"] != null)
        {
            if ((Request["seno"].Length == 0) || (Request["seno"].Length > 8))
                Response.Redirect("../danger.aspx");
            if (!IsNumber(Request["seno"].ToString()))
                Response.Redirect("../danger.aspx");
            if (!IsNumber(Request["newver"].ToString()))
                Response.Redirect("../danger.aspx");
        }
        if (!IsPostBack)
        {
            BindContMoneyType();
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("../../sys_BreadcrumbsFile.xml", "Y");
            }
            ViewState["NewType"] = "1";
            BT_Customer.Attributes.Add("onclick", "find_customer2();");
            txt_promoter_name.Attributes.Add("onChange", string.Format("Find_empno_kw('{0}',1);", txt_promoter_empno.ClientID));
            string strCaseNo = "";
            if (Request["contno"] != null)
            {
                if (Request["contno"].Length > 15)
                    Response.Redirect("../danger.aspx");
                if (!IsNatural_Number(Request["contno"].ToString()))
                    Response.Redirect("../danger.aspx");
                strCaseNo = Request["contno"].ToString();
            }
            else
            {
                if (Request["seno"] != null)
                {
                    if (Request["newver"] != null)
                    {
                        if (Request["newver"].Length > 1)
                            Response.Redirect("../danger.aspx");
                        //this.SDS_NR.SelectParameters.Clear();
                        //this.SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                        //this.SDS_NR.SelectCommand = "esp_TreatyApply_NewContno";
                        //this.SDS_NR.SelectParameters.Add("seno", TypeCode.String, Request["seno"].ToString());
                        //if (Request["newver"].ToString() == "1") //新件
                        //{
                        //    this.SDS_NR.SelectParameters.Add("ver", TypeCode.String, "1");
                        //    BT_Customer.Visible = false;
                        //}
                        //if (Request["newver"].ToString() == "2")//新版
                        //    this.SDS_NR.SelectParameters.Add("ver", TypeCode.String, "2");
                        //this.SDS_NR.SelectParameters.Add("contno", "");
                        //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                        //{
                        //    this.SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                        //}
                        //System.Data.DataView dv_contno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());


                        #region --- query ---
                        DataTable dt = new DataTable();
                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.StoredProcedure;

                            sqlCmd.CommandText = @"esp_TreatyApply_NewContno";

                            // --- 避免匯出查詢過久而當掉 --- //
                            sqlCmd.CommandTimeout = 0;

                            sqlCmd.Parameters.Clear();

                            sqlCmd.Parameters.AddWithValue("seno", Request["seno"].ToString());
                            if (Request["newver"].ToString() == "1") //新件
                            {
                                sqlCmd.Parameters.AddWithValue("ver", "1");
                                BT_Customer.Visible = false;
                            }
                            if (Request["newver"].ToString() == "2")//新版
                                sqlCmd.Parameters.AddWithValue("ver", "2");
                            sqlCmd.Parameters.AddWithValue("contno", "");

                            try
                            {
                                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                                sqlDA.Fill(dt);

                            }
                            catch (Exception ex)
                            {
                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );

                                oRCM.ErrorExceptionDataToDB(logMail);

                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }

                        #endregion
                        DataView dv_contno = dt.DefaultView;
                        if (dv_contno.Count >= 1)
                        {
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][0].ToString()));
                            h_compno.Value = dv_contno[0][1].ToString();
                            string str_casetype = dv_contno[0][2].ToString();
                            txt_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][3].ToString()));

                            txt_req_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][6].ToString()));
                            txt_promoter_empno.Value = dv_contno[0][7].ToString();
                            txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][8].ToString()));
                            txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][9].ToString()));
                            txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][10].ToString()));
                            x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][6].ToString().Substring(2, 5)));
                            ViewState["tr_class"] = "Q";
                            BindContType("");
                            BindData_Customer();
                            Bindcasetype("1");
                        }
                        txtComplexNo.Visible = true;
                    }
                    else
                    {
                        Response.Redirect("../danger.aspx");
                    }
                }
                else
                {
                    if ((Request["contno"] == null) && (Request["seno"] == null)) //其他議約需求
                    {
                        BindContType("");
                        Bindcasetype("1");
                        txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empOrgname));
                        x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empDeptcd));
                        txt_req_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empOrgcd + ssoUser.empDeptcd));
                        txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empName));
                        txt_promoter_empno.Value = ssoUser.empNo;
                        txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empTelext));
                        ViewState["tr_class"] = "Q";

                    }

                }
                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
            }
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            x_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            lb_keyin_emp_no.Text = Server.HtmlEncode(ssoUser.empNo);       //建檔人工號
            lb_keyin_emp_name.Text = Server.HtmlEncode(ssoUser.empName);   //建檔人名稱
            lb_keyin_tel.Text = Server.HtmlEncode(ssoUser.empTelext);      //建檔人分機
            lb_keyin_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期
            lb_modify_emp_no.Text = Server.HtmlEncode(ssoUser.empNo);       //建檔人工號
            lb_modify_emp_name.Text = Server.HtmlEncode(ssoUser.empName);   //建檔人名稱
            lb_modify_tel.Text = Server.HtmlEncode(ssoUser.empTelext);      //建檔人分機
            lb_modify_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期

            //txtOrgAbbrName.Text = ssoUser.empOrgname;
            //x_dept.Text = ssoUser.empDeptcd;
            //txt_req_dept.Text = ssoUser.empOrgcd+ssoUser.empDeptcd;
            //txt_promoter_name.Text = ssoUser.empName;
            //txt_promoter_empno.Value = ssoUser.empNo;
            //txtTel.Text = ssoUser.empTelext;
            //ViewState["tr_class"] = "Q";
            if (Request["CaseStyle"] != null)
            {
                DDL_case_style.SelectedValue = Request["CaseStyle"].ToString();

                ddlContType.Items.Clear();
                txtContMoney.Text = "0";
                txt_contsdate.Text = "";
                txt_contedate.Text = "";
                DDL_case_style_SelectedIndexChanged(this, EventArgs.Empty);
            }

        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        //if (IsPostBack)
        //    Bind_sRC_init(ViewState["tr_class"].ToString());
    }

    private void BindContMoneyType()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    private void BindData_Customer()
    {
        //this.SDS_company.SelectParameters.Clear();
        //this.SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_company.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //this.SDS_company.SelectParameters.Add("customers", TypeCode.String, h_COMPno);
        //this.SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_COMPno));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        strCondition += "";

        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);

        #endregion
        //SDS_ContType.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_ContType.SelectCommand = "exec esp_treaty_codetable_query_by_group  '" + SQLInjectionReplaceAll(strCondition) + "' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "10");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
        }
        #endregion
    }
    private void Bindcasetype(string strContType)
    {
        string strCondition = "";
        //SDS_case_style.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_case_style.SelectCommand = "exec esp_treatyCase_codetable  '" + SQLInjectionReplaceAll(strCondition) + "' ,'16' ";
        //SDS_case_style.DataBind();
        //DDL_case_style.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_codetable";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "16");
            sqlCmd.Parameters.AddWithValue("@mode", "case_class");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_case_style.DataSource = dt;
                DDL_case_style.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


        DDL_case_style.Items.Insert(0, new ListItem(" ", ""));
        #region 如果有指定 ContType,則將指定的 ContType 選取
        //DDL_case_style.Items.FindByValue(strContType).Selected = true; 
        #endregion
    }
    protected void rb_conttype_bd_CheckedChanged(object sender, EventArgs e)
    {
        BindContType("");
    }
    protected void rb_conttype_other_CheckedChanged(object sender, EventArgs e)
    {
        BindContType("");
    }
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "")
            strNumber = "0";
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    private void DoSaveDraft(string tr_status)
    {
        //SDS_NR.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = "esp_TreatyApply_modify";
        ViewState["eb_orgcd"] = txt_req_dept.Text.Substring(0, 2);//取得組織單位
        //if (txtComplexNo.Text == "")
        //{
        //    SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(""));
        //    SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll((DateTime.Now).Year.ToString()));
        //    SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txt_req_dept.Text.Substring(0, 2)));
        //    SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
        //    SDS_NR.SelectParameters.Add("tr_sn", SQLInjectionReplaceAll(""));
        //    SDS_NR.SelectParameters.Add("tr_ver", SQLInjectionReplaceAll("A"));  //案件編號(版次)   
        //    SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll("01"));//案件編號(件次)
        //}
        //else
        //{
        //    SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(""));
        //    SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
        //    SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
        //    SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
        //    SDS_NR.SelectParameters.Add("tr_sn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
        //    SDS_NR.SelectParameters.Add("tr_ver", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
        //    SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
        //}
        //SDS_NR.SelectParameters.Add("tr_status", SQLInjectionReplaceAll(tr_status)); //草稿
        //SDS_NR.SelectParameters.Add("tr_old_contno", SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
        //SDS_NR.SelectParameters.Add("tr_req_dept", SQLInjectionReplaceAll(txt_req_dept.Text.Trim()));
        //SDS_NR.SelectParameters.Add("tr_promoter_no", SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
        //SDS_NR.SelectParameters.Add("tr_promoter_name", SQLInjectionReplace(txt_promoter_name.Text.Trim()));
        //SDS_NR.SelectParameters.Add("tr_name", SQLInjectionReplace(txt_name.Text.Trim()));//洽案(契約)名稱
        //SDS_NR.SelectParameters.Add("tr_compidno", SQLInjectionReplaceAll(""));
        //SDS_NR.SelectParameters.Add("tr_compidno_all", SQLInjectionReplaceAll(h_COMPno));
        //SDS_NR.SelectParameters.Add("tr_compname", SQLInjectionReplaceAll(""));
        //SDS_NR.SelectParameters.Add("tr_compname_all", SQLInjectionReplaceAll(""));

        //if (rb_language_chiness.Checked)
        //    SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("1"));//契約語文-中文
        //if (rb_language_english.Checked)
        //    SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("2"));//契約語文-英文
        //#region 案件性質
        //SDS_NR.SelectParameters.Add("tr_conttype_b0", SQLInjectionReplaceAll("0"));//技術服務
        //SDS_NR.SelectParameters.Add("tr_conttype_b1", SQLInjectionReplaceAll("0"));//合作開發
        //SDS_NR.SelectParameters.Add("tr_conttype_d4", SQLInjectionReplaceAll("0"));//技術授權
        //SDS_NR.SelectParameters.Add("tr_conttype_d5", SQLInjectionReplaceAll("0"));//專利授權
        //SDS_NR.SelectParameters.Add("tr_conttype_d7", SQLInjectionReplaceAll("0"));//專利讓與
        //SDS_NR.SelectParameters.Add("tr_conttype_ns", SQLInjectionReplaceAll("0"));//新創事業(洽案)
        //SDS_NR.SelectParameters.Add("tr_conttype_rb", SQLInjectionReplaceAll("0"));//標案 
        //SDS_NR.SelectParameters.Add("tr_conttype_uo", SQLInjectionReplaceAll("0"));//國外支出(無收入) 
        //SDS_NR.SelectParameters.Add("tr_conttype_ui", SQLInjectionReplaceAll("0"));//國外支出(無收入) 
        //SDS_NR.SelectParameters.Add("tr_class_other_desc", SQLInjectionReplaceAll("")); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
        //#endregion
        //#region 契約修訂
        //SDS_NR.SelectParameters.Add("tr_amend", SQLInjectionReplaceAll("0"));
        //SDS_NR.SelectParameters.Add("tr_amend_other_desc", SQLInjectionReplaceAll(""));
        //#endregion
        //SDS_NR.SelectParameters.Add("tr_contsdate", SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
        //SDS_NR.SelectParameters.Add("tr_contedate", SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
        //#region 簽約緣由與目的
        //SDS_NR.SelectParameters.Add("tr_promoter_no_other", SQLInjectionReplaceAll(""));
        //SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll(""));
        //#endregion

        //SDS_NR.SelectParameters.Add("tr_otherrequire_ver", SQLInjectionReplaceAll("2"));
        //SDS_NR.SelectParameters.Add("tr_keyin_emp_no", SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
        //SDS_NR.SelectParameters.Add("tr_keyin_emp_name", SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
        //SDS_NR.SelectParameters.Add("tr_keyin_date", SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
        //SDS_NR.SelectParameters.Add("tr_modify_emp_no", SQLInjectionReplaceAll(lb_modify_emp_no.Text.Trim()));  // 修改工號
        //SDS_NR.SelectParameters.Add("tr_modify_emp_name", SQLInjectionReplaceAll(lb_modify_emp_name.Text.Trim()));// 修改人
        //SDS_NR.SelectParameters.Add("tr_modify_date", SQLInjectionReplaceAll(lb_modify_date.Text.Trim().Replace("/", "")));      // 修改日期
        //SDS_NR.SelectParameters.Add("tr_file_flag", SQLInjectionReplaceAll("0"));//附件狀態
        //SDS_NR.SelectParameters.Add("tr_conttype", SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
        //SDS_NR.SelectParameters.Add("tr_money_type", SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
        //if (txtContMoney.Text.Trim() == "")
        //    SDS_NR.SelectParameters.Add("tr_money", SQLInjectionReplaceAll("0"));
        //else
        //    SDS_NR.SelectParameters.Add("tr_money", SQLInjectionReplaceAll(txtContMoney.Text.Trim()));

        //SDS_NR.SelectParameters.Add("NewType", SQLInjectionReplaceAll(ViewState["NewType"].ToString())); // 草稿
        //SDS_NR.SelectParameters.Add("tr_case_style", SQLInjectionReplaceAll(DDL_case_style.SelectedValue.ToString()));

        //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
        //{
        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}

        //SDS_NR.DataBind();
        //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyApply_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            if (txtComplexNo.Text == "")
            {
                sqlCmd.Parameters.AddWithValue("@tr_seno", "");
                sqlCmd.Parameters.AddWithValue("@tr_year", oRCM.SQLInjectionReplaceAll((DateTime.Now).Year.ToString()));
                sqlCmd.Parameters.AddWithValue("@tr_orgcd", oRCM.SQLInjectionReplaceAll(txt_req_dept.Text.Substring(0, 2)));
                sqlCmd.Parameters.AddWithValue("@tr_class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tr_sn", "");
                sqlCmd.Parameters.AddWithValue("@tr_ver", "A");  //案件編號(版次)   
                sqlCmd.Parameters.AddWithValue("@tr_seqsn", "01");//案件編號(件次)
            }
            else
            {
                sqlCmd.Parameters.AddWithValue("@tr_seno", "");
                sqlCmd.Parameters.AddWithValue("@tr_year", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
                sqlCmd.Parameters.AddWithValue("@tr_orgcd", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
                sqlCmd.Parameters.AddWithValue("@tr_class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tr_sn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
                sqlCmd.Parameters.AddWithValue("@tr_ver", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
                sqlCmd.Parameters.AddWithValue("@tr_seqsn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
            }
            sqlCmd.Parameters.AddWithValue("@tr_status", oRCM.SQLInjectionReplaceAll(tr_status)); //草稿
            sqlCmd.Parameters.AddWithValue("@tr_old_contno", oRCM.SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
            sqlCmd.Parameters.AddWithValue("@tr_req_dept", oRCM.SQLInjectionReplaceAll(txt_req_dept.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@tr_promoter_no", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
            sqlCmd.Parameters.AddWithValue("@tr_promoter_name", oRCM.SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@tr_name", oRCM.SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
            sqlCmd.Parameters.AddWithValue("@tr_compidno", "");
            sqlCmd.Parameters.AddWithValue("@tr_compidno_all", oRCM.SQLInjectionReplaceAll(h_COMPno));
            sqlCmd.Parameters.AddWithValue("@tr_compname", "");
            sqlCmd.Parameters.AddWithValue("@tr_compname_all", "");

            if (rb_language_chiness.Checked)
                sqlCmd.Parameters.AddWithValue("@tr_language", "1");//契約語文-中文
            if (rb_language_english.Checked)
                sqlCmd.Parameters.AddWithValue("@tr_language", "2");//契約語文-英文
            #region 案件性質
            sqlCmd.Parameters.AddWithValue("@tr_conttype_b0", "0");//技術服務
            sqlCmd.Parameters.AddWithValue("@tr_conttype_b1", "0");//合作開發
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d4", "0");//技術授權
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d5", "0");//專利授權
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d7", "0");//專利讓與
            sqlCmd.Parameters.AddWithValue("@tr_conttype_ns", "0");//新創事業(洽案)
            sqlCmd.Parameters.AddWithValue("@tr_conttype_rb", "0");//標案 
            sqlCmd.Parameters.AddWithValue("@tr_conttype_uo", "0");//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tr_conttype_ui", "0");//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tr_class_other_desc", ""); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
            #endregion
            #region 契約修訂
            sqlCmd.Parameters.AddWithValue("@tr_amend", "0");
            sqlCmd.Parameters.AddWithValue("@tr_amend_other_desc", "");
            #endregion
            sqlCmd.Parameters.AddWithValue("@tr_contsdate", oRCM.SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
            sqlCmd.Parameters.AddWithValue("@tr_contedate", oRCM.SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
            #region 簽約緣由與目的
            sqlCmd.Parameters.AddWithValue("@tr_promoter_no_other", "");
            sqlCmd.Parameters.AddWithValue("@tr_org_adm", "");
            #endregion

            sqlCmd.Parameters.AddWithValue("@tr_otherrequire_ver", "2");
            sqlCmd.Parameters.AddWithValue("@tr_keyin_emp_no", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
            sqlCmd.Parameters.AddWithValue("@tr_keyin_emp_name", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
            sqlCmd.Parameters.AddWithValue("@tr_keyin_date", oRCM.SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
            sqlCmd.Parameters.AddWithValue("@tr_modify_emp_no", oRCM.SQLInjectionReplaceAll(lb_modify_emp_no.Text.Trim()));  // 修改工號
            sqlCmd.Parameters.AddWithValue("@tr_modify_emp_name", oRCM.SQLInjectionReplaceAll(lb_modify_emp_name.Text.Trim()));// 修改人
            sqlCmd.Parameters.AddWithValue("@tr_modify_date", oRCM.SQLInjectionReplaceAll(lb_modify_date.Text.Trim().Replace("/", "")));      // 修改日期
            sqlCmd.Parameters.AddWithValue("@tr_file_flag", "0");//附件狀態
            sqlCmd.Parameters.AddWithValue("@tr_conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
            sqlCmd.Parameters.AddWithValue("@tr_money_type", oRCM.SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
            if (txtContMoney.Text.Trim() == "")
                sqlCmd.Parameters.AddWithValue("@tr_money", "0");
            else
                sqlCmd.Parameters.AddWithValue("@tr_money", oRCM.SQLInjectionReplaceAll(txtContMoney.Text.Trim()));

            sqlCmd.Parameters.AddWithValue("@NewType", oRCM.SQLInjectionReplaceAll(ViewState["NewType"].ToString())); // 草稿
            sqlCmd.Parameters.AddWithValue("@tr_case_style", oRCM.SQLInjectionReplaceAll(DDL_case_style.SelectedValue.ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);


            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_actno = dt.DefaultView;
        if (dv_actno.Count >= 1)
        {
            ViewState["seno"] = dv_actno[0][0].ToString();
        }

    }

    protected void btnSendApply_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入案件名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★案件名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_empno').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_empno').click(function () { $('#txt_promoter_empno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (txt_name.Text == "")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name.Text').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name.Text", script_alert);
        }
        if (h_COMPno == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須輸入','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "BT_Customer", script_alert);
        }
        if (ddlContType.SelectedValue.Length > 2)
        {
            str_danger = "1";
        }
        if ((DDL_case_style.SelectedValue == "1") && (ddlContType.SelectedValue == ""))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }


        if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
        {

            int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
            int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
            if (dt1 > dt2)
                str_error += "★契約期間異常 (起日 > 訖日) \\n ";
        }
        if ((txt_req_dept.Text.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Text.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((DDL_case_style.SelectedValue == "1") || (DDL_case_style.SelectedValue == "2") || (DDL_case_style.SelectedValue == "9"))
        {
            if (ddlContMoneyType.SelectedValue == "")
                str_error += "★幣別不能挑選空白 \\n ";
            else
            {
                if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                    str_danger = "1";
            }
        }

        if ((txtContMoney.Text.Trim().Length > 10) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        else
        {
            if (txtContMoney.Text.Trim() == "")
                txtContMoney.Text = "0";
        }

        if (str_danger == "1")
            Response.Redirect("../danger.aspx");

        if (str_error == "")
        {
            DoSaveDraft("3");
            //SDS_NR.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_NR.SelectParameters.Clear();
            //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_NR.SelectCommand = SQLInjectionReplaceAll("esp_TreatyApplyToTreatyCase");//送出承辦
            //SDS_NR.SelectParameters.Add("requisition_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
            //{
            //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_NR.DataBind();
            //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApplyToTreatyCase";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                sqlCmd.Parameters.AddWithValue("@requisition_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_actno = dt.DefaultView;
            if (dv_actno.Count >= 1)
            {
                ViewState["seno"] = dv_actno[0][0].ToString();
            }
            Treaty_log(ViewState["seno"].ToString(), "送出申請單", "", "", "treaty\\TreatyApplyQ.aspx");
            string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyCaseQ_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_COMPno.Replace(e.CommandArgument.ToString(), "");
            BindData_Customer();
        }
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
        if (LB != null)
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = SQLInjectionReplaceAll("esp_treaty_log");
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplaceAll(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplaceAll(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplaceAll(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplaceAll(xIP));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplaceAll(xApp));
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void DDL_case_style_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (DDL_case_style.SelectedValue == "1")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10') order by display_order ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();


            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10') order by display_order ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "2")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "3")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "4")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "5")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "6")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
            txtContMoney.Text = "";
            ddlContMoneyType.SelectedValue = "TWD";
        }
        if (DDL_case_style.SelectedValue == "7")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
            txtContMoney.Text = "";
            ddlContMoneyType.SelectedValue = "TWD";
        }
        if (DDL_case_style.SelectedValue == "8")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '30') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '30') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "9")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);


                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "A")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = false;
            ddlContType.Items.Clear();
        }

    }
}