﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using Treaty_report;

public partial class Search_inner : System.Web.UI.Page  //Treaty.common   //
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }

    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            //SqlDataSource SDS_auth = new SqlDataSource();
            //SDS_auth.ID = "SDS_auth";
            //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;

            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_auth.SelectCommand = " select count(m_sno) from treaty_buztbl where emp_group='0' and emp_no=@empno";
            //SDS_auth.SelectParameters.Add("empno", SQLInjectionReplaceAll( ssoUser.empNo)  );
            //for (int i = 0; i < SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
            //if (dv_auth.Count >= 1)
            //{
            //    ViewState["SYS"] = ssoUser.empNo;
            //}
            //SDS_auth.Dispose();

            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = "select count(m_sno) from treaty_buztbl where emp_group='0' and emp_no=@empno";
            oCmd.CommandType = CommandType.Text;
            oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "DataSource");
            if (ds != null && ds.Tables[0].Rows.Count >= 1)
            {
                ViewState["SYS"] = ssoUser.empNo;
            }
            oCmd.Dispose();
            oda.Dispose();

            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;
            tbxHandleName.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empName));
            ViewState["sortorder"] = "DESC";
            ViewState["sortField"] = "tc_petition_day";
            BindCaseStyle();
            BindContType();
            BindOrg();
            DoSearch();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_search));

        bool isPC = oRCM.IsPC(Request);
        btnExcel.Visible = isPC;
        btnPDF.Visible = isPC;
    }

    private void BindCaseStyle()
    {
        SqlDataSource SDS_CaseStyle = new SqlDataSource();
        SDS_CaseStyle.ID = "SDS_CaseStyle";
        SDS_CaseStyle.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;

        SDS_CaseStyle.SelectCommand = "exec esp_treatyCase_codetable   '' ,'16', 'case_class' ";
        SDS_CaseStyle.DataBind();
        ddlCaseStyle.DataSource = SDS_CaseStyle;
        ddlCaseStyle.DataBind();
        ddlCaseStyle.Items.Insert(0, new ListItem(" ", ""));
        SDS_CaseStyle.Dispose();
    }
    private void BindContType()
    {
        SqlDataSource SDS_ContType = new SqlDataSource();
        SDS_ContType.ID = "SDS_ContType";
        SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        SDS_ContType.DataBind();
        ddlContType.DataSource = SDS_ContType;
        ddlContType.DataBind();
        ddlContType.Items.Insert(0, new ListItem(" ", "00"));
        SDS_ContType.Dispose();
    }
    private void BindOrg()
    {
        using (SqlConnection sqlConn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_case_inner_orglist";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlOrgcd.DataSource = dt;
                ddlOrgcd.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }
    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;

        SqlDataSource SDS_search = new SqlDataSource();
        SDS_search.ID = "SDS_auth";
        SDS_search.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;

        SDS_search.SelectParameters.Clear();
        SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        if (str_sortField == "")
            SDS_search.SelectCommand = " SELECT  * from v_treaty_search_case_inner where tmp_uid =@tmp_uid order by tmp_case_actno ";
        else
            SDS_search.SelectCommand = " SELECT  * from v_treaty_search_case_inner where tmp_uid =@tmp_uid order by  " + str_sortField + " " + str_sort;
        SDS_search.SelectParameters.Add("tmp_uid", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        SDS_search.DataBind();
        SGV_search.DataSource = SDS_search;
        SGV_search.DataBind();
        SDS_search.Dispose();
    }
    private void DoSearch()
    {
        if (IsDangerWord(ddlOrgcd.SelectedValue) || (ddlOrgcd.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlContType.SelectedValue) || (ddlContType.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlCaseStyle.SelectedValue) || (ddlCaseStyle.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(DDL_money.SelectedValue) || (DDL_money.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxHandleName.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxPromoterName.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtKeyWord.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if ((txtReceiveSDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseSDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseSDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtReceiveEDate.Text.Trim().Length > 8) || (!IsNumber(txtReceiveEDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((txtCloseEDate.Text.Trim().Length > 8) || (!IsNumber(txtCloseEDate.Text.Trim()))) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtCompname.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxPromoterName.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxHandleName.Text.ToUpper())) Response.Redirect("../danger.aspx");
        if (ddlShowCase.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (txtCompname.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddl_amend.SelectedValue) || (ddl_amend.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");


        if ((TB_列管迄日_s.Text.Trim().Length > 8) || (!IsNumber(TB_列管迄日_s.Text.Trim()))) Response.Redirect("../danger.aspx");
        if ((TB_列管迄日_e.Text.Trim().Length > 8) || (!IsNumber(TB_列管迄日_e.Text.Trim()))) Response.Redirect("../danger.aspx");

        string str_核心關鍵技術 = "";
        switch (DDL_核心關鍵技術.SelectedItem.Value)
        {
            case "1":
                str_核心關鍵技術 = "1";
                break;

            case "0":
                str_核心關鍵技術 = "0";
                break;
        }


        string str_class = "";
        foreach (ListItem li in cbxCaseClass.Items)
        {
            if (li.Selected == true)
            {
                str_class += li.Value + ",";
            }
        }
        string str_Important = "";
        switch (ddlImportant.SelectedItem.Value)
        {
            case "1":
                str_Important = "1";
                break;

            case "0":
                str_Important = "0";
                break;
        }

        string str_CaseStatus = string.Empty;
        str_CaseStatus = IIf(cbxCaseStatus1.Checked, "@", "");
        str_CaseStatus += IIf(cbxCaseStatus2.Checked, ",Z", "");
        str_CaseStatus += IIf(cbxCaseStatus3.Checked, ",C", "");

        //      SqlDataSource SDS_NR = new SqlDataSource();
        //      SDS_NR.ID = "SDS_auth";
        //      SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //      SDS_NR.UpdateParameters.Clear();
        //      SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //      SDS_NR.UpdateCommand = "esp_treaty_search_case_inner";
        //      SDS_NR.UpdateParameters.Add("login", SQLInjectionReplaceAll(ViewState["empno"].ToString().Replace("'", "").Replace("--", "－－")));
        //      SDS_NR.UpdateParameters.Add("orgcd", SQLInjectionReplaceAll(ddlOrgcd.SelectedValue)); //將舊案的流水號存入
        //      SDS_NR.UpdateParameters.Add("conttype", SQLInjectionReplaceAll(ddlContType.SelectedValue));//洽案(契約)名稱
        //      SDS_NR.UpdateParameters.Add("handle_name", SQLInjectionReplaceAll(tbxHandleName.Text));
        //      SDS_NR.UpdateParameters.Add("promoter_name", SQLInjectionReplaceAll(tbxPromoterName.Text));
        //      SDS_NR.UpdateParameters.Add("class", SQLInjectionReplaceAll(str_class));
        //      SDS_NR.UpdateParameters.Add("case_flag", SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
        //      SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplaceAll(str_CaseStatus));
        //      SDS_NR.UpdateParameters.Add("kw", SQLInjectionReplaceAll(txtKeyWord.Text));
        //      SDS_NR.UpdateParameters.Add("top_one", SQLInjectionReplaceAll(ddlShowCase.SelectedValue));
        //      SDS_NR.UpdateParameters.Add("case_style", SQLInjectionReplaceAll(ddlCaseStyle.SelectedValue));
        //      SDS_NR.UpdateParameters.Add("tc_hec_flag", SQLInjectionReplaceAll(str_Important));
        //      SDS_NR.UpdateParameters.Add("ReceiveDocStart", SQLInjectionReplaceAll(txtReceiveSDate.Text.Trim()));
        //      SDS_NR.UpdateParameters.Add("ReceiveDocEnd", SQLInjectionReplaceAll(txtReceiveEDate.Text.Trim()));
        //      SDS_NR.UpdateParameters.Add("OverDate", SQLInjectionReplaceAll(IIf( cbxOverDate.Checked,"1","0")));
        //      SDS_NR.UpdateParameters.Add("Compname", SQLInjectionReplaceAll(txtCompname.Text.Trim()));
        //      SDS_NR.UpdateParameters.Add("is_amend", SQLInjectionReplaceAll(ddl_amend.SelectedValue));
        //      SDS_NR.UpdateParameters.Add("QCloseSDate", SQLInjectionReplaceAll(txtCloseSDate.Text.Trim()));
        //      SDS_NR.UpdateParameters.Add("QCloseEDate", SQLInjectionReplaceAll(txtCloseEDate.Text.Trim()));
        //      SDS_NR.UpdateParameters.Add("momey_range", SQLInjectionReplaceAll(DDL_money.SelectedValue));
        //SDS_NR.UpdateParameters.Add("急件", SQLInjectionReplaceAll(IIf(CB_急件.Checked, "1", "0")));
        //      SDS_NR.UpdateParameters.Add("成果有特殊限制者", SQLInjectionReplaceAll(IIf(CB_成果有特殊限制者.Checked, "1", "0")));
        //      SDS_NR.UpdateParameters.Add("陸資", SQLInjectionReplaceAll(IIf(CB_陸資.Checked, "1", "0")));
        //      for (int i = 0; i < SDS_NR.UpdateParameters.Count; i++)
        //      {
        //          SDS_NR.UpdateParameters[i].ConvertEmptyStringToNull = false;
        //      }
        //      SDS_NR.Update();
        //      SDS_NR.Dispose();


        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_case_inner";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Replace("'", "").Replace("--", "－－")));
        oCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(ddlOrgcd.SelectedValue)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("@conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));//洽案(契約)名稱
        oCmd.Parameters.AddWithValue("@handle_name", oRCM.SQLInjectionReplaceAll(tbxHandleName.Text));
        oCmd.Parameters.AddWithValue("@promoter_name", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
        oCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
        oCmd.Parameters.AddWithValue("@case_flag", oRCM.SQLInjectionReplaceAll(IIf(CB_常用版本.Checked, "1", "")));
        oCmd.Parameters.AddWithValue("@tc_degree", oRCM.SQLInjectionReplaceAll(str_CaseStatus));
        oCmd.Parameters.AddWithValue("@kw", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
        oCmd.Parameters.AddWithValue("@top_one", oRCM.SQLInjectionReplaceAll(ddlShowCase.SelectedValue));
        oCmd.Parameters.AddWithValue("@case_style", oRCM.SQLInjectionReplaceAll(ddlCaseStyle.SelectedValue));
        oCmd.Parameters.AddWithValue("@tc_hec_flag", oRCM.SQLInjectionReplaceAll(str_Important));
        oCmd.Parameters.AddWithValue("@ReceiveDocStart", oRCM.SQLInjectionReplaceAll(txtReceiveSDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("@ReceiveDocEnd", oRCM.SQLInjectionReplaceAll(txtReceiveEDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("@OverDate", oRCM.SQLInjectionReplaceAll(IIf(cbxOverDate.Checked, "1", "0")));
        oCmd.Parameters.AddWithValue("@Compname", oRCM.SQLInjectionReplaceAll(txtCompname.Text.Trim()));
        oCmd.Parameters.AddWithValue("@is_amend", oRCM.SQLInjectionReplaceAll(ddl_amend.SelectedValue));
        oCmd.Parameters.AddWithValue("@QCloseSDate", oRCM.SQLInjectionReplaceAll(txtCloseSDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("@QCloseEDate", oRCM.SQLInjectionReplaceAll(txtCloseEDate.Text.Trim()));
        oCmd.Parameters.AddWithValue("@momey_range", oRCM.SQLInjectionReplaceAll(DDL_money.SelectedValue));
        oCmd.Parameters.AddWithValue("@急件", oRCM.SQLInjectionReplaceAll(IIf(CB_急件.Checked, "1", "0")));
        oCmd.Parameters.AddWithValue("@成果有特殊限制者", oRCM.SQLInjectionReplaceAll(IIf(CB_成果有特殊限制者.Checked, "1", "0")));
        oCmd.Parameters.AddWithValue("@陸資", oRCM.SQLInjectionReplaceAll(IIf(CB_陸資.Checked, "1", "0")));
        oCmd.Parameters.AddWithValue("@計價", oRCM.SQLInjectionReplaceAll(IIf(CB_計價.Checked, "1", "0")));
        oCmd.Parameters.AddWithValue("@核心關鍵技術", oRCM.SQLInjectionReplaceAll(str_核心關鍵技術));
        oCmd.Parameters.AddWithValue("@列管迄日_s", oRCM.SQLInjectionReplaceAll(TB_列管迄日_s.Text.Trim()));
        oCmd.Parameters.AddWithValue("@列管迄日_e", oRCM.SQLInjectionReplaceAll(TB_列管迄日_e.Text.Trim()));
        oCmd.Parameters.AddWithValue("@URL", Request.Url.AbsoluteUri.ToString());
        oCmd.Parameters.AddWithValue("@VIP", oRCM.GetUserIP());


        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        SGV_search.PageIndex = 0;
        DoSearch();
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {

        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.PageIndex = e.NewPageIndex;

        // SGV_search.DataBind();
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            StringBuilder script;
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            switch (arg[1].Substring(6, 1))
            {
                case "A":
                case "F":
                case "N":
                case "M":
                case "R":
                case "S":
                case "T":
                case "C":
                    Response.Redirect("./TreatyCase_view.aspx?seno=" + arg[0]);
                    break;
                case "Q":
                    Response.Redirect("./TreatyCaseQ_view.aspx?seno=" + arg[0]);
                    break;
                case "L":
                    Response.Redirect("./TreatyCase2_view.aspx?seno=" + arg[0]);
                    break;

                default:
                    script = new StringBuilder("<script type='text/javascript'> alert('請暫時由舊系統內部查詢進入');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
            }
        }
    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        Literal lt_常用版本 = (Literal)e.Row.FindControl("LT_常用版本");
        if (lt_常用版本 != null)
            if (lt_常用版本.Text == "1")
                lt_常用版本.Text = "<font color='Blue'>【常用版本】</font>";
            else
                lt_常用版本.Text = "";

        Literal lt_擬約幫手 = (Literal)e.Row.FindControl("LT_擬約幫手");
        if (lt_擬約幫手 != null)
            if (lt_擬約幫手.Text == "1")
            {
                lt_擬約幫手.Text = "<font color='red'><b>【擬約幫手】</b></font>";
                lt_常用版本.Text = "";
            }
            else
                lt_擬約幫手.Text = "";

        Literal lt_標記 = (Literal)e.Row.FindControl("LT_標記");
        Label LB_case_name = (Label)e.Row.FindControl("LB_case_name");
        if (lt_標記 != null)
        {
            string tmp_status_name = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_status_name"));
            switch (tmp_status_name)
            {
                case "待分": lt_標記.Text = "<font color='red'>待分</font>"; break;
                case "BOMB": lt_標記.Text = "<img border=0 src='../images/bomb.gif'>"; break;
                case "red": lt_標記.Text = "<img border=0 src='../images/redlight.gif'>"; break;
                case "逾期": lt_標記.Text = "<font color='red'>逾期</font>"; break;
                case "處理": lt_標記.Text = "處理"; break;
                case "取消": lt_標記.Text = "取消"; break;
                case "簽約": lt_標記.Text = "結件"; break;
                case "結件": lt_標記.Text = "結件"; break;
                case "簽辦": lt_標記.Text = "結件<br>(簽辦)"; break;
                case "送審": lt_標記.Text = "送審"; break;
            }
            if (Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_defer")) != "")
                lt_標記.Text = oRCM.RemoveXss(Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_defer"))) + "<br>" + oRCM.RemoveXss(lt_標記.Text);
            LB_case_name.Text = oRCM.RemoveXss(LB_case_name.Text).Replace("【急件】", "<font color='red'>【急件】</font>");
        }
        Literal lt_新件次 = (Literal)e.Row.FindControl("LT_新件次");
        LinkButton lb_View = (LinkButton)e.Row.FindControl("LB_View");
        if ((lb_View != null) && (Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_new_version")) != "0"))
        {
            string sz_actno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_case_actno"));
            string sz_seno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tc_seno"));

            switch (lb_View.Text.Substring(6, 1))
            {
                case "M":
                    lt_新件次.Text = "<img border=0 src='../images/text.gif' >"; break;
                case "U":
                    lt_新件次.Text = "<img border=0 src='../images/text.gif' >"; break;
                case "L":
                    lt_新件次.Text = "<img border=0 src='../images/text.gif' >"; break;
                default:
                    lt_新件次.Text = "<input type='image'  Class='ajax_mesg' src='../images/text.gif' onclick='doNewCase(\"" + Server.HtmlEncode(oRCM.RemoveXss(sz_seno)) + "\",\"" + Server.HtmlEncode(oRCM.RemoveXss(sz_actno)) + "\");' />"; break;
            }
        }
        Literal lt_客戶名稱 = (Literal)e.Row.FindControl("LB_客戶名稱");
        if (lt_客戶名稱 != null)
            lt_客戶名稱.Text = lt_客戶名稱.Text.Replace("㊣", "<br>");

        DropDownList ddlHec = (DropDownList)e.Row.FindControl("DDL_Hec");
        if ((ddlHec != null) && (ViewState["SYS"] != null))
        {
            ddlHec.Visible = true;
            ddlHec.Attributes["caseno"] = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_case_actno")).Trim();
            ddlHec.SelectedValue = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tc_hec_flag"));
        }

    }
    protected void HecUpdate(object sender, System.EventArgs e)
    {
        string strCaseno = ((DropDownList)sender).Attributes["caseno"];
        string strHecflag = ((DropDownList)sender).SelectedValue;

        if (IsDangerWord(strHecflag)) Response.Redirect("../danger.aspx");
        //SqlDataSource SDS_auth = new SqlDataSource();
        //SDS_auth.ID = "SDS_auth";
        //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_auth.SelectParameters.Clear();
        //SDS_auth.UpdateCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_auth.UpdateCommand = "esp_treaty_hec_update";
        //SDS_auth.UpdateParameters.Add("CaseNo", SQLInjectionReplaceAll(strCaseno));
        //SDS_auth.UpdateParameters.Add("hec", SQLInjectionReplaceAll(strHecflag));
        //SDS_auth.Update();
        //SDS_auth.Dispose();

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_hec_update";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@CaseNo", oRCM.SQLInjectionReplaceAll(strCaseno));
        oCmd.Parameters.AddWithValue("@hec", oRCM.SQLInjectionReplaceAll(strHecflag)); //將舊案的流水號存入
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    public void ExportReport(string FileType, string fileName, string rpName, string spNane1, string dsNane1, string spNane2, string dsNane2)
    {
        //ReportViewer1.LocalReport.DataSources.Clear();
        //SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        //ssoUser.GetEmpInfo();
        //SDS_Detail.SelectParameters.Clear();
        //SDS_Detail.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Detail.SelectCommand = spNane1;
        //SDS_Detail.SelectParameters.Add("empno", ssoUser.empNo);
        //SDS_Detail.SelectParameters.Add("SortExpression", "tmp_case_actno");
        //SDS_Detail.SelectParameters.Add("SortDirection", "asc");
        //for (int i = 0; i < this.SDS_Detail.SelectParameters.Count; i++)
        //{
        //    SDS_Detail.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_Detail.DataBind();
        //System.Data.DataView dv = (DataView)SDS_Detail.Select(new DataSourceSelectArguments());
        //DataTable dt = dv.ToTable();
        //if (spNane2 != "")
        //{
        //    SDS_Condition.SelectParameters.Clear();
        //    SDS_Condition.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //    SDS_Condition.SelectCommand = spNane2;
        //    SDS_Condition.SelectParameters.Add("search_form", "esp_treaty_search_case_inner");
        //    SDS_Condition.SelectParameters.Add("empno", ssoUser.empNo);
        //    for (int i = 0; i < this.SDS_Condition.SelectParameters.Count; i++)
        //    {
        //        SDS_Condition.SelectParameters[i].ConvertEmptyStringToNull = false;
        //    }
        //    SDS_Condition.DataBind();
        //    System.Data.DataView dvd = (DataView)SDS_Condition.Select(new DataSourceSelectArguments());
        //    DataTable dtd = dvd.ToTable();
        //    ReportViewer1.LocalReport.DataSources.Add(new Microsoft.Reporting.WebForms.ReportDataSource(dsNane2, dtd));
        //}
        //ReportViewer1.LocalReport.ReportPath = rpName;
        //ReportViewer1.LocalReport.DataSources.Add(new Microsoft.Reporting.WebForms.ReportDataSource(dsNane1, dt));
        //ReportViewer1.LocalReport.Refresh();
        //Warning[] warnings;
        //string[] streamids;
        //string mimeType;
        //string encoding;
        //string extension;
        ////支援PDF、Excel、Image等格式
        //byte[] bytes = ReportViewer1.LocalReport.Render(FileType, null, out mimeType, out encoding, out extension, out streamids, out warnings);
        //Response.Clear();
        //Response.HeaderEncoding = Encoding.GetEncoding("big5");
        //if (Request.Browser.Browser == "IE")
        //{
        //    fileName = Server.UrlPathEncode(fileName);
        //}
        //Response.AddHeader("Content-Disposition", "attachment; filename=" + fileName);
        //Response.AddHeader("Content-Length", bytes.Length.ToString());
        //Response.ContentType = "application/octet-stream";
        //Response.OutputStream.Write(bytes, 0, bytes.Length);
    }

    //匯出EXCEL START
    #region 匯出EXCEL					   
    protected void btnExcel_Click(object sender, EventArgs e)
    {
        this.ExportCustomerAnalysisData();
    }

    //1.撈出要匯出EXCEL的資料
    private void ExportCustomerAnalysisData()
    {
        //SqlDataSource SDS_statistics = new SqlDataSource();
        //SDS_statistics.ID = "SDS_auth";
        //SDS_statistics.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_statistics.SelectCommand = "esp_treaty_search_case_inner_匯出Excel";
        //SDS_statistics.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_statistics.SelectParameters.Clear();
        //SDS_statistics.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //SDS_statistics.DataBind();
        //DataView dataSource =  SDS_statistics.Select(new DataSourceSelectArguments()) as DataView;
        //if (dataSource.Count > 0)
        //    this.ExportCustomerAnalysisExcelFile(dataSource.ToTable("DataSource"));
        //else
        //    ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('查無資料');</script>", false);

        //SDS_statistics.Dispose();

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_case_inner_匯出Excel";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "DataSource");
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
            this.ExportCustomerAnalysisExcelFile(ds.Tables[0]);
        else
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('查無資料');</script>", false);

        oCmd.Dispose();
        oda.Dispose();
    }

    //2.套用設定好的EXCEL樣板
    private void ExportCustomerAnalysisExcelFile(DataTable dataSource)
    {
        string filePath = Server.MapPath("../../Template/內部查詢明細_樣版.xlsx");
        if (File.Exists(filePath) == false)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('找不到樣板檔，無法匯出檔案');
        </script>
        ", false);
            return;
        }

        asposeExcel excel = new asposeExcel("內部查詢明細_樣版.xlsx");
        Aspose.Cells.Worksheet sheet = excel.getWorksheet();
        sheet.Cells.ImportDataTable(dataSource, true, "B7");
        //sheet.Cells.Merge(dataSource.Rows.Count + 9, 0, 2, 17); //第幾行開始 , 起始位置 , 合併幾列 ,合併幾欄
        excel.PutValue(sheet, dataSource.Rows.Count + 9, 0, "工業技術研究院機密資料 禁止複製、轉載、外流 │ITRI CONFIDENTIAL DOCUMENT DO NOT COPY OR DISTRIBUTE");

        Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
        Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
        objStyle.Font.IsBold = true;

        //設定表頭用
        Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
        objStyleFlag.All = true;

        //針對表頭style控制
        objStyle.HorizontalAlignment = Aspose.Cells.TextAlignmentType.Center;
        objStyle.Pattern = Aspose.Cells.BackgroundType.Solid;
        objStyle.ForegroundColor = Color.LightGreen;
        //objStyle.SetBorder(Aspose.Cells.BorderType.LeftBorder, Aspose.Cells.CellBorderType.Thin, Color.Black);
        //objStyle.SetBorder(Aspose.Cells.BorderType.RightBorder, Aspose.Cells.CellBorderType.Thin, Color.Black);
        //objStyle.SetBorder(Aspose.Cells.BorderType.TopBorder, Aspose.Cells.CellBorderType.Thin, Color.Black);
        sheet.Cells.ApplyRowStyle(6, objStyle, objStyleFlag);
        //excel.PutValue(sheet, 2, 5, reportName);
        //sheet.AutoFitRow(3);

        ExportExcel("內部查詢明細.xlsx", excel.exportExcel());
    }
    //匯出EXCEL
    private void ExportExcel(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);

        Response.Clear();
        Response.ContentType = this.RetrieveFileContentType(fileExtension);
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }

    /// <summary>
    /// 取得檔案類型
    /// </summary>
    /// <param name="fileExtension">副檔名</param>
    private string RetrieveFileContentType(string fileExtension)
    {
        if (fileExtension.Equals(".docx"))
            return "Application/msword";
        else if (fileExtension.Equals(".xlsx"))
            return "Application/vnd.ms-excel";
        else if (fileExtension.Equals(".pdf"))
            return "Application/pdf";
        return string.Empty;
    }
    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull)
    {
        foreach (Parameter parameter in dataSource.SelectParameters)
            parameter.ConvertEmptyStringToNull = isNull;
    }
    #endregion
    //匯出EXCEL END 

    //匯出PDF START
    #region 匯出PDF
    protected void btnPDF_Click(object sender, EventArgs e)
    {
        this.PrintPDFData();
    }

    //撈出要匯出的資料
    private void PrintPDFData()
    {
        //SqlDataSource SDS_statistics = new SqlDataSource();
        //SDS_statistics.ID = "SDS_auth";
        //SDS_statistics.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_statistics.SelectCommand = "esp_treaty_search_case_inner_匯出";
        //SDS_statistics.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_statistics.SelectParameters.Clear();
        //SDS_statistics.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString().Replace("'", "").Replace("--", "－－")));
        //SDS_statistics.DataBind();
        //DataView dataSource = SDS_statistics.Select(new DataSourceSelectArguments()) as DataView;
        //if (dataSource.Count > 0)
        //    this.ExportPdfFile(dataSource.ToTable("DataSource"));
        //else
        //    ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('查無資料');</script>", false);
        //SDS_statistics.Dispose();

        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_case_inner_匯出";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "DataSource");
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
            this.ExportPdfFile(ds.Tables[0]);
        else
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"<script type='text/javascript'>alert('查無資料');</script>", false);

        oCmd.Dispose();
        oda.Dispose();

    }

    //套用設定好word樣板
    private void ExportPdfFile(DataTable dataSource)
    {
        //Tuple<string, string> outputFileNames = this.RetrievePdfOutputFileNames();
        string sampleFileName = "內部查詢明細_樣版.docx";
        string pdfFileName = "內部查詢明細.pdf";
        Treaty_report.asposeWord word = new Treaty_report.asposeWord(sampleFileName);
        Aspose.Words.DocumentBuilder documentBuilder = word.getDocumentBuilder();
        Aspose.Words.Document document = word.getDocument();
        DateTime dt = DateTime.Now;
        documentBuilder.MoveToMergeField("印表日期");
        documentBuilder.Write(dt.ToShortDateString().ToString());
        documentBuilder.MoveToDocumentStart();

        #region 案源
        StringBuilder sbCaseSource = new StringBuilder();
        string strCaseSource = string.Empty;
        foreach (ListItem it in cbxCaseClass.Items)
        {
            if (it.Selected)
                sbCaseSource.Append(string.Format("{0},", it.Text));
        }
        if (sbCaseSource.Length > 0)
        {
            strCaseSource = sbCaseSource.ToString().Substring(0, sbCaseSource.ToString().Length - 1);
        }
        #endregion

        #region 狀態
        string strStatus = string.Empty;
        StringBuilder sbStatus = new StringBuilder();
        if (cbxCaseStatus1.Checked)
            sbStatus.Append(cbxCaseStatus1.Text + ",");
        if (cbxCaseStatus2.Checked)
            sbStatus.Append(cbxCaseStatus2.Text + ",");
        if (cbxCaseStatus3.Checked)
            sbStatus.Append(cbxCaseStatus3.Text + ",");
        if (cbxOverDate.Checked)
            sbStatus.Append(cbxOverDate.Text + ",");

        if (sbStatus.Length > 0)
            strStatus = sbStatus.ToString().Substring(0, sbStatus.ToString().Length - 1);
        #endregion

        //替換掉word上設置的查詢條件欄位
        document.Range.Replace("@ddlOrgcd", ddlOrgcd.SelectedItem.Text.ToString().Trim(), false, false);//單位別
        document.Range.Replace("@ddlCaseStyle", ddlCaseStyle.SelectedItem.Text.ToString().Trim(), false, false);//案件類型
        document.Range.Replace("@tmp_conttype_name", ddlContType.SelectedItem.Text.ToString().Trim(), false, false);//契約性質
        document.Range.Replace("@tbxHandleName", tbxHandleName.Text.ToString().Trim(), false, false);//承辦法務人員
        document.Range.Replace("@tbxPromoterName", tbxPromoterName.Text.ToString().Trim(), false, false);//單位承辦員
        document.Range.Replace("@Important", ddlImportant.SelectedItem.Text.ToString().Trim(), false, false);//重大效益案件
        document.Range.Replace("@CaseClass", strCaseSource, false, false);//案源
        document.Range.Replace("@CaseStatus", strStatus, false, false);//狀態
        document.Range.Replace("@ShowCase", ddlShowCase.SelectedItem.Text.ToString().Trim(), false, false);//案件顯示
        document.Range.Replace("@txtKeyWord", txtKeyWord.Text.ToString().Trim(), false, false);//關鍵字查詢
        document.Range.Replace("@Compname", txtCompname.Text.ToString().Trim(), false, false);//客戶名稱
        document.Range.Replace("@ReceiveSDate", txtReceiveSDate.Text.ToString().Trim(), false, false);//收文日其 起
        document.Range.Replace("@ReceiveEDat", txtReceiveEDate.Text.ToString().Trim(), false, false);//收文日其 訖
        document.Range.Replace("@amend", ddl_amend.SelectedItem.Text.ToString().Trim(), false, false);//是否維修約類


        document.MailMerge.ExecuteWithRegions(dataSource);

        this.ExportPDF(pdfFileName, word.exportPDF());
    }


    private void ExportPDF(string fileName, MemoryStream ms)
    {
        string contentType = string.Empty;
        string fileExtension = Path.GetExtension(fileName);
        Response.Clear();
        Response.ContentType = this.RetrieveFileContentType(fileExtension);
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");
        fileName = Server.UrlPathEncode(fileName);
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);
        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }
    #endregion


    protected void BT_報院條件_Click(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SqlDataSource SDS_report = new SqlDataSource();
        //SDS_report.ID = "SDS_auth";
        //SDS_report.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_report.SelectParameters.Clear();
        //SDS_report.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_report.SelectCommand = "esp_treaty_search_case_inner_報院條件";
        //SDS_report.SelectParameters.Add("login", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_report.DataBind();
        //System.Data.DataView dv = (DataView)SDS_report.Select(new DataSourceSelectArguments());
        //if (dv.Count >= 1)
        //{
        //    Response.Clear();
        //    Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        //    XSSFWorkbook workbook = new XSSFWorkbook();
        //    ISheet u_sheet = workbook.CreateSheet("報院條件清單");
        //    //*** 每一列的「第一格」才能用這種寫法！！ ******************************************
        //    //u_sheet.CreateRow(0).CreateCell(0).SetCellValue("契約編號");
        //    int rowY = 0;
        //    foreach (DataRowView myDataRowView in dv)
        //    {
        //        IRow u_row = u_sheet.CreateRow(rowY);
        //        for (int colX = 0; colX < dv.Table.Columns.Count; colX++)
        //        {
        //            u_row.CreateCell(colX).SetCellValue(myDataRowView[colX].ToString());
        //        }
        //        rowY++;
        //    }

        //    MemoryStream MS = new MemoryStream();   //==需要 System.IO命名空間
        //    workbook.Write(MS);
        //    string fileName = "報院條件清單.xlsx";
        //    if ((Request.Browser.Browser == "InternetExplorer") || (Request.Browser.Browser == "IE"))
        //    {
        //        fileName = Server.UrlPathEncode(fileName);
        //    }
        //    //== Excel檔名，請寫在最後面 filename的地方
        //    Response.AddHeader("Content-Disposition", "attachment; filename=" + fileName);
        //    Response.BinaryWrite(MS.ToArray());

        //    //== 釋放資源
        //    workbook = null;
        //    MS.Close();
        //    MS.Dispose();

        //    Response.Flush();
        //    Response.End();

        //}

        //SDS_report.Dispose();


        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_case_inner_報院條件";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        if (ds != null && ds.Tables[0].Rows.Count >= 1)
        {
            asposeExcel excel = new asposeExcel();
            Aspose.Cells.Worksheet sheet = excel.getWorksheet();
            sheet.Name = "報院條件清單";
            DataTable dataSource = ds.Tables[0];

            sheet.Cells.ImportDataTable(dataSource, false, "A1");

            Aspose.Cells.Workbook workbook = new Aspose.Cells.Workbook();
            Aspose.Cells.Style objStyle = workbook.Styles[workbook.Styles.Add()];
            objStyle.Font.IsBold = true;

            //設定表頭用
            Aspose.Cells.StyleFlag objStyleFlag = new Aspose.Cells.StyleFlag();
            objStyleFlag.All = true;

            ExportExcel("報院條件清單.xlsx", excel.exportExcel());

            //Response.Clear();
            //Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            //XSSFWorkbook workbook = new XSSFWorkbook();
            //ISheet u_sheet = workbook.CreateSheet("報院條件清單");
            ////*** 每一列的「第一格」才能用這種寫法！！ ******************************************
            ////u_sheet.CreateRow(0).CreateCell(0).SetCellValue("契約編號");

            //for (int rowY = 0; rowY < ds.Tables[0].Rows.Count; rowY++)
            //{
            //    IRow u_row = u_sheet.CreateRow(rowY);
            //    for (int colX = 0; colX < ds.Tables[0].Columns.Count; colX++)
            //    {
            //        u_row.CreateCell(colX).SetCellValue(ds.Tables[0].Rows[rowY][colX].ToString());
            //    }
            //}
            //MemoryStream MS = new MemoryStream();   //==需要 System.IO命名空間
            //workbook.Write(MS);
            //string fileName = "報院條件清單.xlsx";
            //if ((Request.Browser.Browser == "InternetExplorer") || (Request.Browser.Browser == "IE"))
            //{
            //    fileName = Server.UrlPathEncode(fileName);
            //}
            ////== Excel檔名，請寫在最後面 filename的地方
            //Response.AddHeader("Content-Disposition", "attachment; filename=" + fileName);
            //Response.BinaryWrite(MS.ToArray());

            ////== 釋放資源
            //workbook = null;
            //MS.Close();
            //MS.Dispose();
            //Response.Flush();
            //Response.End();
        }
        oCmd.Dispose();
        oda.Dispose();
    }

    protected void LBx_客戶名稱_Click(object sender, EventArgs e)
    {
        LinkButton CompInfo = sender as LinkButton;
        string Compno = CompInfo.Attributes["Compno"];
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);

    }
}
