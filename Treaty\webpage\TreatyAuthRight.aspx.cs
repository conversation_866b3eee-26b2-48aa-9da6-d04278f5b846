﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyAuthRight : System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_auth.SelectCommand = " select count(m_sno) from treaty_buztbl where emp_group='0' and emp_no=@empno";
            //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select count(m_sno) from treaty_buztbl where emp_group='0' and emp_no=@empno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_auth = dt.DefaultView;
            if (dv_auth.Count == 0)
            {
                Response.Redirect("../NoAuthRight.aspx");
            }
            txt_px_name.Attributes.Add("onChange", "Find_empno_kw('txt_px_name',2);");
            BindDDL();
            BindData_Auth_list();
        }
        //ClientScript.GetPostBackEventReference(new PostBackOptions(this.SDS_gv_file));
    }


    private void BindDDL()
    {

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT  emp_no, emp_name FROM   treaty_buztbl";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                DDL_list.DataSource = dt;
                DDL_list.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindData_Auth_list()
    {
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_gv_file.SelectCommand = " SELECT   m_sno, emp_no, emp_name, CASE WHEN emp_group = '0' THEN '管理員' WHEN emp_group = '1' THEN '主管' ELSE '一般' END AS 角色 ,do_nda_job FROM treaty_buztbl";
        //SDS_gv_file.DataBind();
        //gv_list.DataBind();

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            //sqlCmd.CommandText = @"SELECT   m_sno, emp_no, emp_name, CASE WHEN emp_group = '0' THEN '管理員' WHEN emp_group = '1' THEN '主管' ELSE '一般' END AS 角色 ,do_nda_job FROM treaty_buztbl";
            sqlCmd.CommandText = @" EXEC esp_TreatyAuthRight";
           
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
                gv_list.DataSource = dt;
                gv_list.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void gv_role_list_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //this.SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_gv_file.DeleteCommand = "Delete treaty_buztbl  where m_sno=" + e.CommandArgument;
            //this.SDS_gv_file.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"Delete treaty_buztbl  where m_sno=@ssno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@ssno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindData_Auth_list();
        }

    }
    protected void gv_list_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

            LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
            DropDownList ddl_NDA = (DropDownList)e.Row.FindControl("DDL_NDA");
            if (ddl_NDA != null)
            {
                ddl_NDA.SelectedValue = DataBinder.Eval(e.Row.DataItem, "do_nda_job").ToString();
                ddl_NDA.Attributes["m_sno"] = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "m_sno")).Trim();

            }

        }
    }


    protected void BT_add_Click(object sender, EventArgs e)
    {
        if ((h_px_empno.Value != "") && (h_px_empno.Value.Length <= 6))
        {
            //this.SDS_gv_file.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.InsertCommand = "esp_TreatyAuthRight_insert";
            //SDS_gv_file.InsertParameters.Add("new_id", TypeCode.String, h_px_empno.Value.Trim());
            //SDS_gv_file.InsertParameters.Add("old_id", TypeCode.String, DDL_list.SelectedValue);
            //this.SDS_gv_file.Insert();
            #region --- insert ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyAuthRight_insert";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@new_id", oRCM.SQLInjectionReplaceAll(h_px_empno.Value.Trim()));
                sqlCmd.Parameters.AddWithValue("@old_id", oRCM.SQLInjectionReplaceAll(DDL_list.SelectedValue));


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            BindData_Auth_list();
            h_px_empno.Value = "";
            txt_px_name.Text = "";
        }
        else
        {
            string script_alert = "<script language='javascript'>alert('新增時需要挑選人員資料!') ;</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }

    }

    protected void gv_list_Sorting(object sender, GridViewSortEventArgs e)
    {
        gv_list.PageIndex = 0;

        BindData_Auth_list();
    }

    protected void HecUpdate(object sender, System.EventArgs e)
    {
        string m_sno = ((DropDownList)sender).Attributes["m_sno"];
        string do_NDA = ((DropDownList)sender).SelectedValue;
        //SDS_NDA.UpdateParameters.Clear();
        //SDS_NDA.UpdateCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
        //SDS_NDA.UpdateCommand = " update treaty_buztbl set do_nda_job=@do_NDA where m_sno=@m_sno";
        //SDS_NDA.UpdateParameters.Add("m_sno", m_sno);
        //SDS_NDA.UpdateParameters.Add("do_NDA", do_NDA);
        //SDS_NDA.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"update treaty_buztbl set do_nda_job=@do_NDA where m_sno=@m_sno";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@m_sno", oRCM.SQLInjectionReplaceAll(m_sno));
            sqlCmd.Parameters.AddWithValue("@do_NDA", oRCM.SQLInjectionReplaceAll(do_NDA));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
}