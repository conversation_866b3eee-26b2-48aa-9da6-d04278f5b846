﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;


namespace AMPS
{
    /// <summary>
    /// Summary description for NCR
    /// </summary>
    public class Dal
    {
        //資料庫連線。
        public SqlConnection ConnString = new SqlConnection(ConfigurationManager.ConnectionStrings["FileCompareConnString"].ConnectionString);//ConnString為SSO的資料庫連線(各系統應該一致)


        /// <summary>
        /// 清單
        /// </summary>
        /// <param name="empno"></param>
        /// <param name="no"></param>
        /// <param name="no2"></param>
        /// <returns></returns>
        public DataTable getAllFiles(SqlParameter[] pParas)
        {

            DataTable dt = new DataTable();
            try
            {
                dt = runSpDS("esp_TreatyCase_檔案清單", pParas).Tables[0];
            }
            catch (Exception ex) { }

            return dt;
        }


        /// <summary>
        /// 利用此函式來執行stored procedure,回傳DataSet		 
        /// </summary>
        /// <param name="pSPName">Stored Procedure名稱</param>
        /// <param name="pParas">參數</param>
        /// <returns>return DataSet</returns>
        public DataSet runSpDS(string pSPName, SqlParameter[] pParas)
        {
            SqlCommand cmd = new SqlCommand();

            // Add Parameter into SqlCommand.SqlParameter
            for (int i = 0; i < pParas.Length; i++)
            {
                cmd.Parameters.Add(pParas[i]);
            }

            // set properties of SqlCommand
            cmd.Connection = ConnString;
            cmd.CommandText = pSPName;
            cmd.CommandType = CommandType.StoredProcedure;

            //取代可能的攻擊字眼
            ReplaceMaliciousParametersSqlCommand(ref cmd);

            // execute SQL and return a dataview
            SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
            DataSet ds = new DataSet();
            cmdSQL.Fill(ds, "myTable");

            // Release Resource 
            if (pParas != null)
                cmd.Parameters.Clear();

            ConnString.Close();

            return ds;
        }

        #region 處理 SqlCommand 惡意攻擊符號
        /// <summary>
        /// 處理 SqlCommand 惡意攻擊符號
        /// </summary>
        /// <param name="sc"></param>
        private static void ReplaceMaliciousParametersSqlCommand(ref SqlCommand sc)
        {
            foreach (System.Data.SqlClient.SqlParameter p in sc.Parameters)
            {
                if (p.Value != null)
                {
                    p.Value = RemoveHTMLTags(DoReplace(p.Value.ToString()));
                }
            }
        }
        /// <summary>
        /// 目前可通過掃描的項目
        /// </summary>
        /// <param name="strOri">參數</param>
        /// <returns></returns>
        private static string DoReplace(string strOri)
        {
            if (strOri.Length > 0)
            {
                strOri = strOri.Replace("|", "︱");
                strOri = strOri.Replace("&", "＆");
                //strOri = strOri.Replace(";", "；");
                strOri = strOri.Replace("$", "＄");
                strOri = strOri.Replace("%", "％");
                //strOri = strOri.Replace("@", "＠");
                strOri = strOri.Replace("'", "’");
                strOri = strOri.Replace("<", "＜");
                //strOri = strOri.Replace("(", "（");
                //strOri = strOri.Replace("\"", "＂");
                strOri = strOri.Replace(">", "＞");
                //strOri = strOri.Replace(")", "）");
                strOri = strOri.Replace("+", "＋");
                strOri = strOri.Replace("#", "＃");
                //strOri = strOri.Replace(" CR ", "ＣＲ");
                // strOri = strOri.Replace(" LF ", "ＬＦ");
                //strOri = strOri.Replace("\\", "＼");
                //strOri = strOri.Replace("&lt", "＆lt");
                // strOri = strOri.Replace("&gt", "＆gt");

                //如果有連續兩個以上的"-"，則將所有的"-"變成全型"－"
                if (strOri.IndexOf("--") > -1)
                    strOri = strOri.Replace("-", "－");

            }

            return strOri;
        }

        #region 移除HTML Tags
        /// <summary>
        /// 移除HTML Tags
        /// </summary>
        /// <param name="HtmlSource"></param>
        /// <returns></returns>
        public static string RemoveHTMLTags(string HtmlSource)
        {
            string PureText = System.Text.RegularExpressions.Regex.Replace(Regex.Replace(HtmlSource.ToString(), "(?is)<.+?>", "").ToString(), "(?is)&.+?;", "").ToString();
            return PureText;
        }
        #endregion
        #endregion
    }

}