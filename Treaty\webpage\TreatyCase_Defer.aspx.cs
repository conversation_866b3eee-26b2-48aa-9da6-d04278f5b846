﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TreatyCase_Defer : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            TB_deferdate.Attributes.Add("readOnly", "readonly");
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyCase_Auth";
                //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_Auth";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_auth = dt.DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if ((str_auth == "X") || (str_auth == "R"))
                        Response.Redirect("../NoAuthRight.aspx");
                }

                //SDS_SC.SelectParameters.Clear();
                //SDS_SC.SelectCommand = " select * from treaty_case where tc_seno = @sn ";
                //SDS_SC.SelectParameters.Add("sn", TypeCode.String, ViewState["seno"].ToString());
                //SDS_SC.DataBind();
                //System.Data.DataView dv = (DataView)SDS_SC.Select(new DataSourceSelectArguments());

                #region --- query ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"select * from treaty_case where tc_seno = @sn ";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                        dt = new DataTable();
                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv = dt.DefaultView;
                if (dv.Count >= 1)
                {
                    ViewState["tc_prefinish_date"] = dv[0]["tc_prefinish_date"].ToString();
                }
            }
            else
                Response.Redirect("../NoAuthRight.aspx");
        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if ((TB_deferdate.Text == "") || (TB_defertxt.Text == ""))
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('展延後預估完成日 & 原因 必須填寫 !');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

        }
        else
        {
            if (!IsNumber(TB_deferdate.Text.Replace("/", "")) || (TB_deferdate.Text.Replace("/", "").Length > 8))
                Response.Redirect("../danger.aspx");
            if (TB_defertxt.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");

            if (int.Parse(ViewState["tc_prefinish_date"].ToString().Replace("/", "")) > int.Parse(TB_deferdate.Text.Replace("/", "")))
            {
                StringBuilder script = new StringBuilder("<script type='text/javascript'>alert('展延日期必須大於預估完成日!');</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }
            else
            {
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_SC.SelectParameters.Clear();
                //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
                //this.SDS_SC.InsertCommand = "esp_TreatyCase_DeferInsert";
                //this.SDS_SC.InsertParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
                //this.SDS_SC.InsertParameters.Add("empno", TypeCode.String, ssoUser.empNo);
                //this.SDS_SC.InsertParameters.Add("DeferDate", TypeCode.String, TB_deferdate.Text.Replace("/",""));
                //this.SDS_SC.InsertParameters.Add("DeferMeno", TypeCode.String, TB_defertxt.Text);
                //this.SDS_SC.Insert();


                #region --- insert ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_DeferInsert";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    sqlCmd.Parameters.AddWithValue("@DeferDate", oRCM.SQLInjectionReplaceAll(TB_deferdate.Text.Trim().Replace("/", "")));
                    sqlCmd.Parameters.AddWithValue("@DeferMeno", oRCM.SQLInjectionReplaceAll(TB_defertxt.Text.Trim()));


                    try
                    {
                        sqlConn.Open();

                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }

        }

    }
}