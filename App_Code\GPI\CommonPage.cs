﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Text.RegularExpressions;
using System.Data;
using System.Web.UI.WebControls;
using SSOUtil;
using System.Configuration;

namespace GPI
{
    /// <summary>
    /// 底層頁面類別
    /// </summary>
    public class CommonPage : System.Web.UI.Page
    {
        public CommonPage()
        {
            //
            // TODO: Add constructor logic here
            //
        }

        protected override void OnLoad(EventArgs e)
        {
            if (AccountInfo.EmpNo == string.Empty)
            {
                SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
                sso.GetEmpInfo();

                AccountInfo.EmpNo = sso.empNo;
                AccountInfo.EmpName = sso.empName;
                AccountInfo.Email = sso.empEmail;
                AccountInfo.Orgcd = sso.empOrgcd;
                AccountInfo.Deptcd = sso.empDeptcd;
                AccountInfo.Telext = sso.empTelext;

                try
                {
                    loginlog.ws_loginlog LoginLog = new loginlog.ws_loginlog();
                    LoginLog.insert_loginlog(
                        AccountInfo.EmpNo,
                        Request.UserHostAddress.ToString(), DateTime.Now,
                        "登入",
                        ConfigurationManager.AppSettings["SysName"],//系統名稱
                        ConfigurationManager.AppSettings["Asset_ID"], //新服務ID
                        AccountInfo.Orgcd + AccountInfo.Deptcd);
                }
                catch (Exception)
                {
                    this.Alert("記錄登入資訊失敗");
                }
                
            }

            base.OnLoad(e);
        }

        #region OnError 事件

        protected override void OnError(EventArgs e)
        {
            try
            {
                //當錯誤發生的，執行的動作
                if (ConfigurationManager.AppSettings["Enable_MAIL"] == "Y")
                {

                    //可使用「Server.GetLastError().ToString()」來取得錯誤訊息

                    SmtpMail mySmtpMail = new SmtpMail();
                    mySmtpMail.MailFrom = String.Format("{0}<{1}>", ConfigurationManager.AppSettings["SysName"], ConfigurationManager.AppSettings["SysMail"]);
                    mySmtpMail.MailTo = String.Format("{0}<{1}>", ConfigurationManager.AppSettings["MgrName"], ConfigurationManager.AppSettings["MgrMail"]);
                    mySmtpMail.Subject = string.Format("【{0}】系統於【{1}】發生錯誤", ConfigurationManager.AppSettings["SysName"], DateTime.Now.ToString());
                    mySmtpMail.IsBodyHtml = true;
                    mySmtpMail.Body = string.Format(@"
帳號：{0}<br>
錯誤內容：{1}<br>
錯誤堆疊：{2}<br>
", AccountInfo.EmpNo
     , Server.GetLastError().Message
     , Server.GetLastError().StackTrace.Replace("於", "<br>於"));

                    mySmtpMail.SendMail();
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (ConfigurationManager.AppSettings["IsDebug"] != "Y")
                {
                    //導至錯誤畫面
                    Response.Redirect("~/GPI/Shared/Error.aspx");
                }
            }
        }

        #endregion

        #region JAVASCRIPT
        /// <summary>
        /// 顯示訊息
        /// </summary>
        /// <param name="msg"></param>
        public void Alert(string msg)//顯示訊息
        {
            ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");", msg), true);
        }
        /// <summary>
        /// 關閉視窗
        /// </summary>
        public void Close()
        {
            ScriptManager.RegisterStartupScript(this.Page, this.GetType(), "close", "window.close();", true);

        }
        /// <summary>
        /// 顯示訊息完後關閉視窗
        /// </summary>
        /// <param name="msg">輸出的訊息</param>
        public void Alert_Close(string msg)//顯示訊息完後關閉視窗
        {
            ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");window.close();", msg), true);

        }
        /// <summary>
        /// 顯示完訊息後導至網頁
        /// </summary>
        /// <param name="msg">輸出的訊息</param>
        /// <param name="path">導至網頁的URL</param>
        public void AlertGo(string msg, string path)//顯示完訊息後導至網頁
        {
            ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");location.href='{1}';", msg, path), true);
        }
        public void AlertOpen(string msg, string path)//顯示完訊息另開視窗至指定網頁
        {
            ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");window.open('{1}');", msg, path), true);
        }

        #endregion

        #region 驗證

        #region 判斷是否為數字
        /// <summary>
        /// 判斷是否為數字(不使用小數點)(0~9)
        /// </summary>
        /// <param name="str">欲判斷的值</param>
        /// <returns></returns>
        public bool IsNumeric(string str)
        {
            char[] tmp = str.ToCharArray();
            for (int i = 0; i < tmp.Length; i++)
            {
                if ((int)tmp[i] < 48 || (int)tmp[i] > 57)
                {
                    return false;
                }

            }
            return true;
        }

        #endregion

        #region 判斷是否為日期格式
        /// <summary>
        /// 判斷是否為日期格式
        /// </summary>
        /// <param name="str">欲判斷的值</param>
        /// <returns></returns>
        public bool IsDate(string str)
        {
            bool success = false;
            DateTime odate = DateTime.Now;
            if (str.Length > 8)
            {
                success = DateTime.TryParse(str, out odate);
            }
            else if (str.Length == 8)
            {
                string date = string.Format("{0}/{1}/{2}", str.Substring(0, 4), str.Substring(4, 2), str.Substring(6, 2));
                success = DateTime.TryParse(date, out odate);
            }
            else
            {
                success = false;
            }

            return success;
        }

        #endregion

        #region 檢查參數

        /// <summary>
        /// 檢查參數
        /// </summary>
        /// <param name="name">參數名稱</param>
        /// <param name="type">參數資料型態</param>
        /// <param name="isexist">參數是否必須存在</param>
        /// <returns></returns>
        public bool chkRequestData(string name, TypeCode type, bool isexist)
        {

            if (isexist)//檢查是否必須存在
            {
                if (string.IsNullOrEmpty(Request[name]))
                {
                    return false;
                }
            }

            #region 檢查資料型態
            if (!string.IsNullOrEmpty(Request[name]))
            {
                switch (type.ToString())
                {
                    case "Int32":
                        if (!IsNumeric(Request[name]))
                        {
                            return false;
                        }
                        break;

                }
            }
            #endregion

            return true;
        }

        #endregion

        #region 檢查mail正確性
        /// <summary>
        /// 判斷Email的正確性
        /// </summary>
        /// <param name="txt_mail">欲判斷mail</param>
        /// <returns></returns>

        public bool MailCheck(string txt_mail)
        {
            Regex myEmailRegex = new Regex(@"([a-zA-Z_0-9.-]+\@[a-zA-Z_0-9.-]+\.\w+)", RegexOptions.IgnoreCase);

            if (!myEmailRegex.IsMatch(txt_mail, 0))
            {
                return false;
            }

            return true;
        }
        #endregion

        #endregion

    }
}