﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_valuation_modify.aspx.cs" Inherits="TreatyCase_valuation_modify" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>案件審查</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>


    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>

    <script type="text/javascript">
        function close_win() {
            alert("存檔完成 & 送出審查!");
            parent.$.fn.colorbox.close();
        }
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function close_Dwin() {
            alert("存檔完成 !");
            parent.$.fn.colorbox.close();
        }
        function treaty_fileup(seno) {

            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_valuation_FileUp.aspx?seno=" + seno
                , title: '上傳檔案'
                , iframe: true, width: "700px", height: "350px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_valuation_FileUp.aspx?fid=" + fid + "&seno=<%=Server.HtmlEncode(Request.QueryString["seno"]) %>"
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>


            <table style="margin-left: 15px; margin-top: 25px; width: 800px">
                <tr>
                    <td class="td_right">版本：</td>
                    <td>
                        <asp:Label ID="LB_版本" runat="server" Text=""></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">參考價：</td>
                    <td>
                        <table border="0" cellspacing="0" cellpadding="0" with="200px">
                            <tr>
                                <td>
                                    <asp:TextBox ID="TB_參考價" runat="server"></asp:TextBox>
                                </td>
                                <td>盡職調查結果：</td>
                                <td width="200px">
                                    <asp:DropDownList ID="DDL_盡職調查結果" runat="server">
                                        <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                        <asp:ListItem Value="1">綠</asp:ListItem>
                                        <asp:ListItem Value="5">黃</asp:ListItem>
                                        <asp:ListItem Value="9">紅</asp:ListItem>
                                    </asp:DropDownList>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <tr>
                            <td class="td_right">底價：</td>
                            <td>
                                <asp:TextBox ID="TB_底價" runat="server"></asp:TextBox>
                                ，<asp:CheckBox ID="CB_底價_無" runat="server" Text="無　說明：" AutoPostBack="true" OnCheckedChanged="CB_底價_無_CheckedChanged" />
                                <asp:TextBox ID="TB_底價_無_說明" runat="server" Width="120px" Height="30px" TextMode="MultiLine" Visible="false"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td class="td_right">第三方鑑價：</td>
                            <td>
                                <asp:TextBox ID="TB_第三方鑑價" runat="server"></asp:TextBox>
                                ，<asp:CheckBox ID="CB_第三方鑑價_無" runat="server" Text="無　說明：" AutoPostBack="true" OnCheckedChanged="CB_第三方鑑價_無_CheckedChanged" />
                                <asp:TextBox ID="TB_第三方鑑價_無_說明" runat="server" Width="120px" Height="30px" TextMode="MultiLine" Visible="false"></asp:TextBox>
                            </td>
                        </tr>
                    </ContentTemplate>
                </asp:UpdatePanel>
                <tr>
                    <td class="td_right">其他說明：</td>
                    <td>
                        <asp:TextBox ID="TB_其他說明" runat="server" Width="500px" Height="40px" TextMode="MultiLine"></asp:TextBox>
                        <br />
                        <div style="float: right">
                            <asp:Button ID="BT_save_draft" runat="server" Text="草稿存檔" class="genbtnS" OnClick="BT_save_draft_Click" />&nbsp;&nbsp;
							<asp:Button ID="BT_save_send" runat="server" Text="正式存檔" class="genbtnS" OnClick="BT_save_send_Click" />
                        </div>
                    </td>
                </tr>


                <tr>
                    <td class="td_left" colspan="2">&nbsp;<asp:Button class="ajax_mesg genbtnS" ID="btnFilesUpload2" runat="server" Text="新增檔案"></asp:Button>

                        <asp:GridView ID="gv_vdoc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_vdoc_file_RowCommand" OnRowDataBound="gv_vdoc_file_RowDataBound">
                            <Columns>
                                <asp:TemplateField HeaderText="功能">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_tcdf_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                        <asp:LinkButton ID="LB_edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>維護</asp:LinkButton><br />
                                        <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' ForeColor="Red">刪除</asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="30px" HorizontalAlign="Center" ForeColor="Black" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="附件名稱">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="300px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="說明">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="250px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="上傳人">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="60px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="上傳日期">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("時間").ToString()) %>' DataFormatString="{0:yyyy/MM/dd}" HtmlEncode="false"></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="70px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>
                            </Columns>
                            <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                            <PagerSettings Position="Bottom" />
                            <PagerStyle HorizontalAlign="Left" />
                        </asp:GridView>
                        <%-- <asp:SqlDataSource ID="SDS_vgv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>


                    </td>
                </tr>

            </table>


        </span>
        <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
