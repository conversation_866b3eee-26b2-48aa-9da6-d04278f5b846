﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myQuot
	/// </summary>
	public class myQuot : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _empno;
		private string _empname;
		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}
		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}
		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		//工作項目
		public long Seqsn = 0;
		public int eqi_id = 0;
		public int eqi_serial = 0;
		public string eqi_workitem = "";
		public decimal eqi_qty = 0;
		public decimal eqi_unitprice = 0;
		public string eqi_memo = "";

		//子單
		public string qsh_id = string.Empty;			//識別號
		public string qsh_seqsn = string.Empty;			//規劃流水號
		public string qsh_ver = string.Empty;			//報價母單版次
		public string qsh_serial = string.Empty;		//子單序號
		public string qsh_date = string.Empty;			//報價日期
		public string qsh_validdate = string.Empty;		//報價有效期限
		public string qsh_desc = string.Empty;			//報價項目
		public string qsh_empno = string.Empty;			//本院聯絡人
		public string qsh_empname = string.Empty;		//本院聯絡人姓名
		public string qsh_dept = string.Empty;			//本院聯絡人部門
		public string qsh_deptname = string.Empty;		//本院聯絡人部門名稱
		public string qsh_phone = string.Empty;			//本院聯絡人電話
		public string qsh_visitorname = string.Empty;	//客戶聯絡人
		public string qsh_visitordept = string.Empty;	//客戶聯絡人部門
		public string qsh_visitorphone = string.Empty;	//客戶聯絡人電話
		public string qsh_visitorfax = string.Empty;	//客戶聯絡人傳真
		public string qsh_addr = string.Empty;			//客戶聯絡人地址
		public string qsh_visitoremail = string.Empty;	//客戶聯絡人-電子郵件
		public string qsh_modempno = string.Empty;		//修改人工號
		public string qsh_modempname = string.Empty;	//修改人姓名
		public string qsh_moddate = string.Empty;		//修改日期
		
		#endregion

		public myQuot()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		public DataTable GetQuotMainBySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select * from engage_quot_m where eq_seqsn=@seqsn ";

			oCmd.Parameters.AddWithValue("@seqsn", this.Seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}


		#region 報價單,工作項目
		
		/// <summary>
		/// 報價單,工作項目 
		/// </summary>
		/// <returns></returns>
		public DataTable Get_QuotWorkItemByEqiID()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select  IDENTITY(int, 1,1) as ItemSN,isnull(eqi_id,'') eqi_id,eqi_seqsn,eqi_ver,eqi_serial,
		eqi_workitem,eqi_qty,eqi_unitprice,eqi_memo,Convert(nvarchar(24),
		Convert(money,ROUND(eqi_qty*eqi_unitprice,2),2),1) as eqi_total,
		(select eq_moneytype from engage_quot_m where eq_seqsn=eqi_seqsn and eq_ver=eqi_ver) eq_moneytype
  into #tmp
  from engage_quotitem_m where eqi_id=@eqi_id
select * from #tmp
drop table #tmp
";

			oCmd.Parameters.AddWithValue("@eqi_id", eqi_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 報價單,工作項目 儲存
		/// </summary>
		/// <returns></returns>
		public bool Save_QuotWorkItem()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_quotitem_m where eqi_id = @eqi_id)
begin
	declare @ver int
	select @ver=isnull(eq_ver,1) from engage_quot_m where eq_seqsn=@seqsn and eq_valid='1'

	insert into engage_quotitem_m 
		(eqi_seqsn, eqi_ver, eqi_serial, eqi_workitem, 
		 eqi_qty, eqi_unitprice, eqi_memo)
	  select 
		@seqsn, @ver, @eqi_serial, @eqi_workitem, 
		 @eqi_qty, @eqi_unitprice, @eqi_memo
end
else
begin
	update engage_quotitem_m set
		eqi_serial=@eqi_serial, eqi_workitem=@eqi_workitem, 
		 eqi_qty=@eqi_qty, eqi_unitprice=@eqi_unitprice, eqi_memo=@eqi_memo
	  where eqi_id = @eqi_id
end

update engage_quot_m set
	eq_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,eq_modempno=@empno
	,eq_modname=@empname
  where eq_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", Seqsn);
			oCmd.Parameters.AddWithValue("@eqi_id", eqi_id);
			oCmd.Parameters.AddWithValue("@eqi_serial", eqi_serial);
			oCmd.Parameters.AddWithValue("@eqi_workitem", eqi_workitem);
			oCmd.Parameters.AddWithValue("@eqi_qty", eqi_qty);
			oCmd.Parameters.AddWithValue("@eqi_unitprice", eqi_unitprice);
			oCmd.Parameters.AddWithValue("@eqi_memo", eqi_memo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 報價單,工作項目刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_QuotWorkItem()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_quotitem_m WHERE  eqi_id = @eqi_id
";
			oCmd.Parameters.AddWithValue("@eqi_id", eqi_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion


		#region 取得報價子單
		public DataTable Get_QuotSubsheet()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"select * from engage_quotsubsheet where qsh_seqsn=@seqsn";

			oCmd.Parameters.AddWithValue("@seqsn", Seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 新增一筆報價子單
		/// <summary>
		/// 新增一筆報價子單
		/// </summary>
		/// <returns></returns>
		public bool Insert_QuotSubsheet()
		{
			//取出工作項次
			string[] aWorkItem = qsh_desc.Split(',');

			string sql = @"
declare @qsh_id bigint;
insert into engage_quotsubsheet 
	(qsh_seqsn,qsh_ver,qsh_serial,qsh_date,qsh_validdate,
	qsh_empno,qsh_empname,qsh_dept,qsh_deptname,qsh_phone,qsh_visitorname,qsh_visitordept,
	qsh_visitorphone, qsh_visitorfax,qsh_addr,qsh_visitoremail,
	qsh_keyinempno,qsh_keyinname,qsh_keyindate,qsh_modempno,qsh_modname,qsh_moddate) 
values 
	(@qsh_seqsn,@qsh_ver,@qsh_serial,@qsh_date,@qsh_validdate,
	@qsh_empno,@qsh_empname,@qsh_dept,@qsh_deptname,@qsh_phone,@qsh_visitorname,@qsh_visitordept,
	@qsh_visitorphone,@qsh_visitorfax,@qsh_addr,@qsh_visitoremail,
	@qsh_modempno,@qsh_modempname,@qsh_moddate,@qsh_modempno,@qsh_modempname,@qsh_moddate);

set @qsh_id=@@IDENTITY;
";

			string sqlSub = string.Empty;

			for (int i = 0; i < aWorkItem.Length; i++)
			{
 				sqlSub = sqlSub + string.Format(@"
insert into engage_quotsubsheetitem (qshi_qshid,qshi_eqiid) values (@qsh_id,{0});
", aWorkItem[i]);

			}

			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql + sqlSub;

			oCmd.Parameters.AddWithValue("@qsh_seqsn", qsh_seqsn);
			oCmd.Parameters.AddWithValue("@qsh_ver", qsh_ver);
			oCmd.Parameters.AddWithValue("@qsh_serial", qsh_serial);
			oCmd.Parameters.AddWithValue("@qsh_date", qsh_date);
			oCmd.Parameters.AddWithValue("@qsh_validdate", qsh_validdate);
			oCmd.Parameters.AddWithValue("@qsh_empno", qsh_empno);
			oCmd.Parameters.AddWithValue("@qsh_empname", qsh_empname);
			oCmd.Parameters.AddWithValue("@qsh_dept", qsh_dept);
			oCmd.Parameters.AddWithValue("@qsh_deptname", qsh_deptname);
			oCmd.Parameters.AddWithValue("@qsh_phone", qsh_phone);
			oCmd.Parameters.AddWithValue("@qsh_visitorname", qsh_visitorname);
			oCmd.Parameters.AddWithValue("@qsh_visitordept", qsh_visitordept);
			oCmd.Parameters.AddWithValue("@qsh_visitorphone", qsh_visitorphone);
			oCmd.Parameters.AddWithValue("@qsh_visitorfax", qsh_visitorfax);
			oCmd.Parameters.AddWithValue("@qsh_addr", qsh_addr);
			oCmd.Parameters.AddWithValue("@qsh_visitoremail", qsh_visitoremail);
			oCmd.Parameters.AddWithValue("@qsh_modempno", qsh_modempno);
			oCmd.Parameters.AddWithValue("@qsh_modempname", qsh_modempname);
			oCmd.Parameters.AddWithValue("@qsh_moddate", qsh_moddate);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新一筆報價子單
		/// <summary>
		/// 更新一筆報價子單
		/// </summary>
		/// <returns></returns>
		public bool Update_QuotSubsheet()
		{
			//取出工作項次
			string[] aWorkItem = qsh_desc.Split(',');

			string sql = @"
update  engage_quotsubsheet set 
	qsh_seqsn=@qsh_seqsn,
	qsh_ver=@qsh_ver,
	qsh_serial=@qsh_serial,
	qsh_date=@qsh_date,
	qsh_validdate=@qsh_validdate,
	qsh_empno=@qsh_empno,
	qsh_empname=@qsh_empname,
	qsh_dept=@qsh_dept,
	qsh_deptname=@qsh_deptname,
	qsh_phone=@qsh_phone,
	qsh_visitorname=@qsh_visitorname,
	qsh_visitordept=@qsh_visitordept,
	qsh_visitorphone=@qsh_visitorphone,
	qsh_visitorfax=@qsh_visitorfax,
	qsh_addr=@qsh_addr,
	qsh_modempno=@qsh_modempno,
	qsh_modname=@qsh_modempname,
	qsh_moddate=@qsh_moddate 
where qsh_id=@qsh_id;

/* 新清除舊資料 */
delete engage_quotsubsheetitem where qshi_qshid=@qsh_id;

";
			string sqlSub = string.Empty;
			for (int i = 0; i < aWorkItem.Length; i++)
			{
				sqlSub = sqlSub + string.Format(@"
insert into engage_quotsubsheetitem (qshi_qshid,qshi_eqiid) values (@qsh_id,{0});
", aWorkItem[i]);

			}

			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql + sqlSub;

			oCmd.Parameters.AddWithValue("@qsh_seqsn", qsh_seqsn);
			oCmd.Parameters.AddWithValue("@qsh_ver", qsh_ver);
			oCmd.Parameters.AddWithValue("@qsh_serial", qsh_serial);
			oCmd.Parameters.AddWithValue("@qsh_date", qsh_date);
			oCmd.Parameters.AddWithValue("@qsh_validdate", qsh_validdate);
			oCmd.Parameters.AddWithValue("@qsh_empno", qsh_empno);
			oCmd.Parameters.AddWithValue("@qsh_empname", qsh_empname);
			oCmd.Parameters.AddWithValue("@qsh_dept", qsh_dept);
			oCmd.Parameters.AddWithValue("@qsh_deptname", qsh_deptname);
			oCmd.Parameters.AddWithValue("@qsh_phone", qsh_phone);
			oCmd.Parameters.AddWithValue("@qsh_visitorname", qsh_visitorname);
			oCmd.Parameters.AddWithValue("@qsh_visitordept", qsh_visitordept);
			oCmd.Parameters.AddWithValue("@qsh_visitorphone", qsh_visitorphone);
			oCmd.Parameters.AddWithValue("@qsh_visitorfax", qsh_visitorfax);
			oCmd.Parameters.AddWithValue("@qsh_addr", qsh_addr);
			oCmd.Parameters.AddWithValue("@qsh_visitoremail", qsh_visitoremail);
			oCmd.Parameters.AddWithValue("@qsh_modempno", qsh_modempno);
			oCmd.Parameters.AddWithValue("@qsh_modempname", qsh_modempname);
			oCmd.Parameters.AddWithValue("@qsh_moddate", qsh_moddate);
			oCmd.Parameters.AddWithValue("@qsh_id", qsh_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 刪除一筆報價子單
		/// <summary>
		/// 刪除一筆報價子單
		/// </summary>
		/// <returns></returns>
		public bool Delete_QuotSubsheet()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete from engage_quotsubsheetitem where qshi_qshid = @qsh_id;
delete engage_quotsubsheet where qsh_id = @qsh_id;
";
			oCmd.Parameters.AddWithValue("@qsh_id", qsh_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion


	}
}