﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using System.IO;

/// <summary>
/// Summary description for Breadcrumb
/// </summary>
public class Breadcrumb
{
    public Breadcrumb()
    {
        //
        // TODO: Add constructor logic here
        //
    }
    /// <summary>
    /// 建立麵包屑
    /// </summary>
    /// <param name="xml_file_path">提供XML檔案路徑</param>
    /// <param name="isShowSelf">是否顯示本身名稱在麵包屑上：Y-顯示（顯示完整麵包屑）；N-不顯示（自己頁面不顯示，只顯示上層資訊）</param>
    /// <returns></returns>
    public string Breadcrumbs_string(string xml_file_path, string isShowSelf)
    {
        string strFileName = xml_file_path;
        XmlDocument xmlDoc = new XmlDocument();
        if (File.Exists(HttpContext.Current.Server.MapPath(strFileName)))
        {
            xmlDoc.Load(HttpContext.Current.Server.MapPath(strFileName));
            return Breadcrumbs_string(xmlDoc, isShowSelf);
        } return "error....";
    }

	private string GetRooturl()
	{
		if (HttpContext.Current.Request.ApplicationPath.Equals("/"))
			return string.Format("{0}://{1}",HttpContext.Current.Request.Url.Scheme, HttpContext.Current.Request.Url.Authority);
		else
			return string.Format("{0}://{1}{2}", HttpContext.Current.Request.Url.Scheme, HttpContext.Current.Request.Url.Authority, HttpContext.Current.Request.ApplicationPath.ToString());
	}
    /// <summary>
    /// 建立麵包屑
    /// </summary>
    /// <param name="xmlDoc">提供XML內容</param>
    /// <param name="isShowSelf">是否顯示本身名稱在麵包屑上：Y-顯示（顯示完整麵包屑）；N-不顯示（自己頁面不顯示，只顯示上層資訊）</param>
    /// <returns></returns>
    public string Breadcrumbs_string(XmlDocument xmlDoc, string isShowSelf)
    {
        //階層ID
        string progid = "";
		string last_url = "";


        if (HttpContext.Current.Request.Url.Query.ToString() != null)
        {
            if (HttpContext.Current.Request.Url.Query == "?CaseStyle=5" || HttpContext.Current.Request.Url.Query == "?CaseStyle=2")
            {
                if (HttpContext.Current.Request.Url.Query == "?CaseStyle=5")
                {
                    return "<a href='../../Default.aspx'>首頁</a>＞<a href='./default.aspx'>議約清單</a>＞申訴案件";
                }
                if (HttpContext.Current.Request.Url.Query == "?CaseStyle=2")
                {
                    return "<a href='../../Default.aspx'>首頁</a>＞<a href='./default.aspx'>議約清單</a>＞契約及法律問題承辦";
                }
            }
        }


        string full_progname = HttpContext.Current.Request.Url.LocalPath;
        foreach (XmlNode xNode in xmlDoc.SelectNodes("/siteMap/siteMapNode"))
        {
            if (full_progname.ToUpper().Contains(xNode.Attributes["url"].Value.ToUpper()))
            {
				//修正重複的檔名，並取最大的相對路徑的檔名.
				if (progid == "" || (progid != "" && xNode.Attributes["url"].Value.Length > last_url.Length))
				{ 
					progid = xNode.Attributes["progid"].Value;
					last_url = xNode.Attributes["url"].Value;
				}
            }
        }
        if (progid != "")
        {
            char[] delimiterChars = { '.' };
            string[] words = progid.Split(delimiterChars);
            string prog_id_subitem = "";
            string Breadcrumbs_string = "";

            foreach (string s in words)
            {
                if (prog_id_subitem == "")
                {
                    prog_id_subitem = s;
                }
                else
                {
                    prog_id_subitem = prog_id_subitem + "." + s;
                }

				XmlNode xNode = xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']");

                if (isShowSelf == "Y")
                {
					//顯示完整麵包屑
                    //#：表示無頁面
                    //if (xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["url"].Value != "#")
					if (xNode.Attributes["url"].Value != "#")
                    {
                        //本頁不顯示LINK
                        if (progid == prog_id_subitem)
                        {
                            //Breadcrumbs_string = Breadcrumbs_string + "<a>" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["title"].Value + "</a>";
                            //20131209:Hugo(Modify),本頁不顯示LINK, 將 a tag 換成 span tag.
							Breadcrumbs_string += string.Format("<span>{0}</span>", xNode.Attributes["title"].Value);
                        }
                        else
						{
                            //Breadcrumbs_string = Breadcrumbs_string + "<a href='../" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["url"].Value + "'>" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["title"].Value + "</a>＞";
							
                            if(xNode.Attributes["title"].Value=="")
                            {
                                Breadcrumbs_string += string.Format("<a href='{0}'>{1}</a>＞" ,xNode.Attributes["url"].Value, xNode.Attributes["title"].Value);
                            }
                            else
                            {
                                if(xNode.Attributes["title"].Value=="首頁")
                                {
                                    Breadcrumbs_string += string.Format("<a href='{0}'>{1}</a>＞",   xNode.Attributes["url"].Value, xNode.Attributes["title"].Value);
                                }
                                else
                                {
                                    Breadcrumbs_string += string.Format("<a href='{0}{1}'>{2}</a>＞", GetRooturl(), xNode.Attributes["url"].Value, xNode.Attributes["title"].Value);
                                }
                            }
						}
                    }
                    else
					{ 
                        //Breadcrumbs_string = Breadcrumbs_string + "<a>" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["title"].Value + "</a>＞";
						Breadcrumbs_string += string.Format("<a>{0}</a>＞", xNode.Attributes["title"].Value);
					}
                }
                else
                {
                    //自己頁面不顯示，只顯示上層資訊
                    if (progid != prog_id_subitem)
                    {
                        //#：表示無頁面
                        //if (xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["url"].Value != "#")
						if (xNode.Attributes["url"].Value != "#")
                        {
                            //Breadcrumbs_string = Breadcrumbs_string + "<a href='../" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["url"].Value + "'>" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["title"].Value + "</a>＞";
							Breadcrumbs_string += string.Format("<a href='{0}{1}'>{2}</a>＞", GetRooturl(), xNode.Attributes["url"].Value, xNode.Attributes["title"].Value);
						}
                        else
						{ 
                            //Breadcrumbs_string = Breadcrumbs_string + "<a>" + xmlDoc.SelectSingleNode("/siteMap/siteMapNode[@progid='" + prog_id_subitem + "']").Attributes["title"].Value + "</a>＞";
							Breadcrumbs_string += string.Format("<a>{0}</a>＞", xNode.Attributes["title"].Value);
						}
                    }
                }
            }

            return Breadcrumbs_string;
            //2013-10-17 Panda : (update)套用麵包屑樣式
            //return string.Format("<div style='height:3px;'></div>{0}<span class = 'font-blacklink'>{1}</span>", System.Configuration.ConfigurationManager.AppSettings["BreadcrumbTitle"].ToString(), Breadcrumbs_string);
		}
        else
        {
            return "error....";
        }
    }

}