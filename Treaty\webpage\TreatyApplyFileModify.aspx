﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApplyFileModify.aspx.cs" Inherits="webpage_TreatyApplyFileModify" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            $("#txt_doc").val(compare);
        }
        function close_win() {
            parent.$.fn.colorbox.close();
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 10px">
                <tr>
                    <td class="td_right">文件名稱：</td>
                    <td>
                        <asp:Label ID="txt_doc" runat="server" Width="549px"></asp:Label></td>
                </tr>
                <tr>
                    <td class="td_right">修改概要：</td>
                    <td>
                        <asp:TextBox ID="txt_filetxt" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <%--  <asp:SqlDataSource ID="SDS_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="更新" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>

            </table>
        </span>
    </form>
</body>
</html>
