/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[16],{396:function(ia,y,e){e.r(y);var fa=e(1),x=e(0);e.n(x);ia=e(84);e=e(316);ia=function(e){function y(x,y,w){y=e.call(this,x,y,w)||this;y.db=x;return y}Object(fa.c)(y,e);y.prototype.request=function(e){var y=this;Object(x.each)(e,function(e){y.db.get(e,function(w,r,h){return w?y.trigger("partReady",{Ya:e,error:w}):y.trigger("partReady",{Ya:e,data:r,Gh:!1,xf:!1,error:null,Jc:h})})})};y.prototype.hr=function(e){e()};return y}(ia.a);
Object(e.a)(ia);Object(e.b)(ia);y["default"]=ia}}]);}).call(this || window)
