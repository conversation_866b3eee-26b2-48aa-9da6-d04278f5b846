﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web;

namespace SSOUtil
{
    /// <summary>
    /// Summary description for SSOLoginUser.
    /// </summary>
    [Serializable]
    public class SSOLoginUser
    {
        #region Private local variable
        private string _emp_empno = string.Empty;
        private string _emp_name = string.Empty;
        private string _emp_telext = string.Empty;
        private string _emp_orgcd = string.Empty;
        private string _emp_deptcd = string.Empty;
        private string _emp_email = string.Empty;
		private string _emp_orgname = string.Empty;
		private string _emp_deptname = string.Empty;

        #endregion

        #region Property
        public string empNo
        {
            get { return _emp_empno; }
        }

        public string empName
        {
            get { return _emp_name; }
        }

        public string empTelext
        {
            get { return _emp_telext; }
        }

        public string empOrgcd
        {
            get { return _emp_orgcd; }
        }

        public string empDeptcd
        {
            get { return _emp_deptcd; }
        }

        public string empEmail
        {
            get { return _emp_email; }
        }

		public string empOrgname
		{
			get { return _emp_orgname; }
		}

		public string empDeptname
		{
			get { return _emp_deptname; }
		}

        #endregion

        private string m_connString = string.Empty;

        public SSOLoginUser()
        {
            m_connString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnString"].ConnectionString;
        }

        public SSOLoginUser(string connectionString)
        {
            m_connString = connectionString;
        }


        #region //		=======================SSO用=============================
        private string GetSSOAttribute(string AttrName)
        {
            return "" + HttpContext.Current.Request.ServerVariables["HTTP_SM_USER"];
        }
        //		=======================SSO用=============================
        #endregion

        #region //取得使用者工號/網域字串
        private string GetUserId()
        {
            //如果 Session["NewSSOEmpno"] 有資料,則先抓 Session["NewSSOEmpno"]
            string strNewSSOEmpno = string.Empty;
            if (HttpContext.Current.Session != null && HttpContext.Current.Session["NewSSOEmpno"] != null)
                strNewSSOEmpno = "" + HttpContext.Current.Session["NewSSOEmpno"];

            //取得使用者工號/網域字串
            string SSOstruser = string.Empty;
            if (strNewSSOEmpno != "")
                SSOstruser = strNewSSOEmpno;
            else
                SSOstruser = GetSSOAttribute("HTTP_SM_USER");
            
            string empno = "";
            if (SSOstruser.Length == 0)
            {
                //當SSO失效,轉為讀Web.config，測試用員工帳號 
                string TestEmpNo = System.Web.Configuration.WebConfigurationManager.AppSettings["TestEmpNo"] + "";
                if (TestEmpNo.Length > 0)
                {
                    return TestEmpNo;
                }

                //當SSO失效,轉回為ITRIAD認證
                empno = HttpContext.Current.User.Identity.Name.Substring(HttpContext.Current.User.Identity.Name.IndexOf("\\", 0) + 1);
            }
            else
            {
                empno = SSOstruser;
            }

            return empno;
        }
        #endregion

        #region GetEmpInfo

        private const string GET_EmpInfo = @"
SELECT com_empno,com_cname,com_telext,com_orgcd,com_deptcd,com_mailadd
		,org_abbr_chnm2,dep_deptname
  FROM common.dbo.comper 
  JOIN common.dbo.orgcod on com_orgcd = org_orgcd
  JOIN common.dbo.depcod on com_deptid = dep_deptid
  WHERE com_empno=@empno
		";

        public void GetEmpInfo()
        {
            SqlConnection conn = new SqlConnection(m_connString);
            SqlCommand cmd = new SqlCommand(GET_EmpInfo, conn);
            cmd.Parameters.AddWithValue("@empno", GetUserId());
            SqlDataReader dr = null;

            try
            {
                conn.Open();
                dr = cmd.ExecuteReader();

                if (dr.Read())
                {
                    this._emp_empno = Convert.ToString(dr["com_empno"]).Trim();
                    this._emp_name = Convert.ToString(dr["com_cname"]).Trim();
                    this._emp_telext = Convert.ToString(dr["com_telext"]).Trim();
                    this._emp_orgcd = Convert.ToString(dr["com_orgcd"]).Trim();
                    this._emp_deptcd = Convert.ToString(dr["com_deptcd"]).Trim();
                    this._emp_email = Convert.ToString(dr["com_mailadd"]).Trim();
					this._emp_orgname = Convert.ToString(dr["org_abbr_chnm2"]).Trim();
					this._emp_deptname = Convert.ToString(dr["dep_deptname"]).Trim();
                }
                else
                {
                    throw new Exception("No record.");
                }
            }
            catch (Exception ex)
            {
                HttpContext.Current.Response.Write("人事資料庫連結錯誤!&nbsp;");
                HttpContext.Current.Response.Write(ex.Message);
                HttpContext.Current.Response.End();
            }
            finally
            {
                if (dr != null && !dr.IsClosed)
                {
                    dr.Close();
                }
                conn.Close();
            }
        }
        #endregion

    }
}
