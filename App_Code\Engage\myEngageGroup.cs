﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myEngageGroup
	/// </summary>
	public class myEngageGroup : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		string _orgcd = string.Empty;		// 單位
		string _keyword = string.Empty;		// 關鍵字

		string _empno = string.Empty;		// 員工工號
		string _empname = string.Empty;		// 員工姓名
		string _priority = string.Empty;	// 群組權限
		string _keyinempno = string.Empty;	// 輸入員工工號
		string _keyinempname = string.Empty;// 輸入員工姓名
		string _keyindate = string.Empty;	// 輸入日期
		string _modempno = string.Empty;	// 修改員工工號
		string _modempname = string.Empty;	// 修改員工姓名
		string _moddate = string.Empty;		// 修改日期
		string _orgname = string.Empty;		// 管理單位
		string _deptname = string.Empty;	// 管理組別
		#endregion

		#region 公有屬性

		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}


		public string Orgcd
		{
			get { return _orgcd; }
			set { _orgcd = value; }
		}

		public string Keyword
		{
			get { return _keyword; }
			set { _keyword = value; }
		}

		public string Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		public string KeyinEmpno
		{
			get { return _keyinempno; }
			set { _keyinempno = value; }
		}

		public string KeyinEmpName
		{
			get { return _keyinempname; }
			set { _keyinempname = value; }
		}

		public string KeyinDate
		{
			get { return _keyindate; }
			set { _keyindate = value; }
		}

		public string ModEmpno
		{
			get { return _modempno; }
			set { _modempno = value; }
		}

		public string ModEmpName
		{
			get { return _modempname; }
			set { _modempname = value; }
		}

		public string ModDate
		{
			get { return _moddate; }
			set { _moddate = value; }
		}

		public string OrgName
		{
			get { return _orgname; }
			set { _orgname = value; }
		}

		public string DeptName
		{
			get { return _deptname; }
			set { _deptname = value; }
		}

		#endregion

		public myEngageGroup() { }

		#region 「提供能查詢單位」資料
		/// <summary>
		/// 依據 empno 員工工號，取得「提供能查詢單位」給 DropDownList_Org 使用。
		/// </summary>
		/// <returns>DataTable</returns>
		public DataTable GetOrgRightQueryByEmpno()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_buztbl_right_query";


			oCmd.Parameters.AddWithValue("@empno", _empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		#endregion

		/// <summary>
		/// search for all the employees having lower or equal priorities than the employee whose ID is empno
		/// </summary>
		/// <returns></returns>
		public DataSet GetEngageGroup()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_buztbl_query";

			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@keyword", _keyword);
			oCmd.Parameters.AddWithValue("@orgcd_list", _orgcd);

			DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
			return ds;
		}

		/// <summary>
		/// search for all the employees having lower or equal priorities than the employee whose ID is empno
		/// </summary>
		/// <returns></returns>
		public DataTable GetEngageGroupFromTmp()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT bt_empno, bt1_empname AS bt_empname, com_orgcd AS bt_emporgcd, bt1_priority AS bt_priority, code_valuedesc AS bt_valuedesc, bt1_org_name AS bt_org_name, 
		bt1_dept_name AS bt_dept_name, bt_readonly, bt1_keyinname, bt1_keyindate, bt1_modname, bt1_moddate
FROM tmp_engage_buztbl_query 
INNER JOIN v_engage_buztbl_query ON bt_empno = bt1_empno 
INNER JOIN engage_codetbl ON bt1_priority = code_value 
WHERE code_type = '120' and bt_qryempno = @empno;");

			oCmd.Parameters.AddWithValue("@empno", _empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetEngageGroupByEmpno(string empno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT bt1_empno, bt1_empname, com_orgcd AS bt1_emporgcd, bt1_priority, code_valuedesc, bt1_org_name, bt1_dept_name 
FROM v_engage_buztbl_query inner join engage_codetbl on bt1_priority = code_value 
where code_type = '120' and bt1_empno = @empno;");

			oCmd.Parameters.AddWithValue("@empno", empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetEngageBuztblOrgcdByEmpno(string empno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT DISTINCT substring(bt2_orgcd, 1, 2) AS bt2_orgcd FROM engage_buztbl2 WHERE bt2_empno = @empno;");

			oCmd.Parameters.AddWithValue("@empno", empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetEngageBuztblDeptidByEmpno(string empno)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT bt2_orgcd FROM engage_buztbl2 WHERE bt2_empno = @empno;");

			oCmd.Parameters.AddWithValue("@empno", empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetPriorityCodeValues()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT code_value, code_valuedesc FROM engage_codetbl where code_type = '120'");

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetOrgCodeValues()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT org_orgcd, org_abbr_chnm2, (org_orgcd + '-' + org_abbr_chnm2) AS org_orgname FROM common..orgcod where org_status='A'");

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public DataTable GetDeptCodeValues(string orglist)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
SELECT 
	left(dep_deptid, 4) AS dep_deptid, 
	ltrim(rtrim(dep_abbrnm)) AS dep_abbrnm, 
	(left(dep_deptid, 4) + '-' + ltrim(rtrim(dep_abbrnm))) AS dep_deptname 
FROM common..depcod 
WHERE right(dep_deptid, 3)='000' and dep_orgcd in ('{0}')", orglist.Replace("'", "").Replace(",", "','"));

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 新增 - 該工號的群組權限設定
		/// </summary>
		public bool InsertGroupEntry()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_buztbl_insert";

			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@priority", _priority);
			oCmd.Parameters.AddWithValue("@keynempno", _keyinempno);
			oCmd.Parameters.AddWithValue("@keyinname", _keyinempname);
			oCmd.Parameters.AddWithValue("@keyindate", _keyindate);
			oCmd.Parameters.AddWithValue("@org_name", _orgname);
			oCmd.Parameters.AddWithValue("@dept_name", _deptname);

			this.Execute(oCmd, CommandType.StoredProcedure);
			return true;
		}

		/// <summary>
		/// 更新 - 該工號的群組權限設定 
		/// </summary>
		/// <returns></returns>
		public bool UpdateGroupEntry()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_buztbl_update";

			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@priority", _priority);
			oCmd.Parameters.AddWithValue("@modempno", _modempno);
			oCmd.Parameters.AddWithValue("@modname", _modempname);
			oCmd.Parameters.AddWithValue("@moddate", _moddate);
			oCmd.Parameters.AddWithValue("@org_name", _orgname);
			oCmd.Parameters.AddWithValue("@dept_name", _deptname);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 刪除該工號的群組權限設定
		/// </summary>
		/// <param name="empno"></param>
		/// <returns></returns>
		public bool DeleteGroupEntry(string empno)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE FROM engage_buztbl1 WHERE bt1_empno = @empno;
DELETE FROM engage_buztbl2 WHERE bt2_empno = @empno;
";

			oCmd.Parameters.AddWithValue("@empno", empno);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
	}
}