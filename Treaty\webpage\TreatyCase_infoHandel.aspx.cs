﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase_infoHandel : Treaty.common  
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["seno"] = "2604";
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
            }
             databinding("");
        }
            
    }
    protected void SGV_log_DataBound(object sender, EventArgs e)
    {

    }
    protected void SGV_log_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "vc")
        {
 
        }
    }
    protected void databinding( string str_sort)
    { 
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.Text;
        //this.SDS_SC.SelectCommand = "select  tca_case_assign_name,tca_case_assign_date, tca_case_handle_name,tca_case_handle_ext from treaty_case_allocation where tca_seno=@tca_seno order by tca_allocation_seno desc";
        //this.SDS_SC.SelectParameters.Add("tca_seno", TypeCode.String, ViewState["seno"].ToString());
 
        //for (int i = 0; i < this.SDS_SC.SelectParameters.Count; i++)
        //{
        //    SDS_SC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_SC.DataBind();
        //SGV_log.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select  tca_case_assign_name,tca_case_assign_date, tca_case_handle_name,tca_case_handle_ext from treaty_case_allocation where tca_seno=@tca_seno order by tca_allocation_seno desc";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tca_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_log.DataSource = dt;
                SGV_log.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_log_PageIndexChanged(object sender, EventArgs e)
    {
        
        databinding("");
    }
    protected void SGV_log_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_log.PageIndex = e.NewPageIndex;
        SGV_log.DataBind();
    }
    protected void SGV_log_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType != DataControlRowType.Header)
        {
            foreach (TableCell tc in e.Row.Cells)
            {
                tc.Attributes["style"] = "border-color:white";
            }
        }
    }
    protected void SGV_log_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {
               
                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_log.SortExpression)
                    {
                        //System.Web.UI.WebControls.Image objImage = null;
                        //objImage = (System.Web.UI.WebControls.Image)e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].FindControl("btnOrder");
                        //if (objImage == null)
                        //{
                        //    objImage = new System.Web.UI.WebControls.Image();
                        //    objImage.ID = "btnOrder";
                        //    e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].Controls.Add(objImage);
                        //}
                        //否為為排序欄位
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_log.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                    //else
                    //{
                    //    objImage = (System.Web.UI.WebControls.Image)e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].FindControl("btnOrder");
                    //    if (objImage != null)
                    //    {
                    //        e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].Controls.Remove(objImage);
                    //    }                   
                    //}
                }
            }
        }
    }
    protected void SDS_SC_Selecting(object sender, SqlDataSourceSelectingEventArgs e)
    {
        for (int i = 0; i < e.Command.Parameters.Count - 1; i++)
        {
            if (e.Command.Parameters[i].Value == null)
            {
                e.Command.Parameters[i].Value = "";
            }
        }
    }
    protected void SGV_log_Sorted(object sender, EventArgs e)
    {

    }
    protected void SGV_log_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortexpression"] = e.SortExpression;
        if (ViewState["sortdirection"] == null)
            ViewState["sortdirection"] = "order by "+e.SortExpression+ " asc";
        else
        {
            if (ViewState["sortdirection"].ToString().IndexOf("asc")>1)
                ViewState["sortdirection"] = "order by " + e.SortExpression + "desc";
            else
                ViewState["sortdirection"] = "order by " + e.SortExpression + "asc";
        }
        this.SGV_log.PageIndex = 0;
        databinding(ViewState["sortexpression"].ToString());
    }

}