﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_Evidence.aspx.cs" Inherits="TreatyCase2_Evidence" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("新增成功!");
            parent.$.fn.colorbox.close();
        }
        function Find_IP() {
            $(".ajax_ip").colorbox({
                href: './TreatyCase2_IP_Find.aspx',
                title: '挑選專利'
                , iframe: true, width: "850px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false, scrolling: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../subap/ret_ip.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callbackIP);

                }
            });

        }
        function jsonp_callbackIP(data) {
            switch (data.c_compcname) {
                case "error0":
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    $("#TB_IpName").val(data.c_pntcnn);
                    $("#DDL_country").val(data.c_applynation);
                    $("#TB_IpNo").val(data.c_cerpntno);
                    $("#TB_cerpntno").val(data.c_patentno);
                    break;
            }
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 40px; width: 550px;">

                <tr>
                    <td class="td_right">侵權証據型態：</td>
                    <td>
                        <asp:DropDownList ID="DDL_Evidence" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True">
                        </asp:DropDownList>
                        <%--<asp:SqlDataSource ID="SDS_Evidence" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                    </td>
                </tr>
                <tr>
                    <td class="td_right"><font color="#ff0000">*</font>侵權証據名稱：</td>
                    <td>
                        <asp:TextBox ID="TB_EvidenceName" runat="server" class="inputex inputsizeL" Width="400px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right"></td>
                    <td>
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>
            </table>
            <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        </span>

    </form>
</body>
</html>
