using System;
using DAL.Model;
using System.Data.SqlClient;
using System.Data;


namespace DAL.IDAL
{
	/// <summary>
	/// Summary description for IEngagePlan.
	/// </summary>
	public interface IEngageQuotation
	{
		DataSet GetEngageBaseBySeno(string seqsn);
		DataSet GetQuotMainBySeqsn(string seqsn);
		DataSet GetQuotMainBySeqsnVer(string seqsn,string ver);
		DataSet GetQuotMainByEqid(string eqid);
		DataSet GetCodeTableByType(string CodeType);
		DataSet GetAllCostVer(string seqsn);
		DataSet GetVisitorAllBySeqsn(string seqsn);
		DataSet GetVisitById(string visit_id);
		DataSet GetPlanVerBySeqsn(string seqsn);
		DataSet GetWorkItemBySeqsnVer(string seqsn,string ver);
		DataSet GetWorkItemByIsHistoryID(string IsHistory,string id);
		void RefreshQuotitemBySeqsnVer(string seqsn,string ver,string planver);
		DataSet GetQuotWorkItemBySeqsnVer(string seqsn,string ver);
		DataSet GetQuotWorkItemByID(string id);
		void UpdateQuotByID(engage_workitem_M oM);
		void DeleteQuotWorkItemByID(string id);
		engage_quot_M GetQuotMainByEqid(engage_quot_M oM);
		void UpdateQuotMainByEqid(engage_quot_M oM);
		int GetLatestSubOrderSerial(string seqsn,string ver);
		string InsertQuotSub(engage_quotsub_M oM);
		DataSet  GetQuotSubSerialBySeqsnVer(string seqsn,string ver);
		DataSet  GetQuotSubBySeqsnVer(string seqsn,string ver);
		DataSet GetQuotSubById(string id);
		void UpdateQuotSubById(engage_quotsub_M oM);
		void DeleteQuotSubById(string id);
		string GetMaxSerialIDBySeqsnVer(string seqsn,string ver);
		DataSet GetVisitorAll();
		DataSet GetOrgcod(string strOrgcd);
		string NewQuotMainByEqid(string strSeqsn,string strKeyinempno,string strKeyinname,string strOldVer);
		void SetValidQuotById(string id);
		void UpdataPlanVerByID(string id,string PlanVer);
		string GetAddrByVisitorID(string vID);
		void UpdateQuotMainLastMailBySeqsnVer(string seqsn,string ver);
		void UpdateQuotMainLastDownLoadBySeqsnVer(string seqsn,string ver);
		DataSet GetDetailEmpInforByNo(string strEmpno);
		string InsertQuotItem(engage_workitem_M oM);
		DataSet GetSubSheetitemByQshid(string Qshid);
		DataSet GetTotalAmountById(string qsh_id);
		DataSet GetAttfile2BySeqsn(string seqsn,string condition);
		DataSet GetQuotWorkItemBySeqsnVerForPDF(string seqsn,string ver);
		bool IsSignResultFinish(string seqsn);

	}
}
