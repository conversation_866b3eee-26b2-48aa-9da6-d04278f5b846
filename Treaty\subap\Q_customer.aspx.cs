﻿using System;
using System.Collections.Generic;
//using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;

public partial class Q_customer : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    //guid 參數放在pub string
    public string Commonkey
    {
        get { return Server.HtmlEncode(ViewState["_Commonkey"].ToString()); }
        set { ViewState["_Commonkey"] = value; }
    }
    public string oricountry
    {
        get { return Server.HtmlEncode(ViewState["_oricountry"].ToString()); }
        set { ViewState["_oricountry"] = value; }
    }
    public string SystemCode
    {
        get { return Server.HtmlEncode(ViewState["_SystemCode"].ToString()); }
        set { ViewState["_SystemCode"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            //1先判斷參數有值, 2 值給validation 確認過
            this.Commonkey = "ddrrtwegr"; //Request.QueryString["Commonkey"].ToString();
            this.SystemCode = "F2-47"; //Request.QueryString["SystemCode"].ToString();
            this.TB_mp.Text = "";// Request.QueryString["keyword"].ToString();
            BindDLL();
            databinding();
        }
    }

    protected void DDL_code1_SelectedIndexChanged(object sender, EventArgs e)
    {
        //SDL_code_2.SelectCommand = "SELECT  code_value ,  code_valuedesc  FROM  visit_codetbl   WHERE (code_value LIKE '"+DDL_code1.SelectedValue.Substring(0,2)+"[^0]')";
        //SDL_code_2.DataBind();
        //DDL_code2.DataBind();
        //DDL_code2.Items.Insert(0, new ListItem("--請挑選--", ""));
    }
    protected void DDL_code1_DataBound(object sender, EventArgs e)
    {
        //DDL_code1.Items.Insert(0, new ListItem("--請挑選--", ""));
    }
    protected void DDL_code2_DataBound(object sender, EventArgs e)
    {
        //DDL_code2.Items.Insert(0, new ListItem("--請挑選--", ""));
    }
    protected void BT_search_Click(object sender, EventArgs e)
    {
        databinding();
    }

    protected void databinding()
    {
        if ((Base64.danger_word_all(this.DDL_code1.SelectedValue) == "1"))
        {
            Response.Redirect("error.aspx");
        }
        if ((Base64.danger_word_all(this.DDL_code2.SelectedValue) == "1"))
        {
            Response.Redirect("error.aspx");
        }
        if ((Base64.danger_word_all(this.TB_mp.Text) == "1"))
        {
            Response.Redirect("error.aspx");
        }
        //this.SDS_jssg.SelectParameters.Clear();
        //this.SDS_jssg.SelectCommandType = SqlDataSourceCommandType.Text;
        //if( TB_mp.Text != "")
        //    this.SDS_jssg.SelectCommand = " SELECT  comp_idno, comp_cname, (SELECT code_valuedesc  FROM visitdb..visit_codetbl WHERE (code_type = '007') AND ( cust.comp_country = code_value)) AS CN   FROM  visitdb..cust where (cust.comp_delmark <> '1') and ( comp_idno like '%" + TB_mp.Text + "%' or comp_cname  like '%" + TB_mp.Text + "%')";
        //else
        //    this.SDS_jssg.SelectCommand = " SELECT  comp_idno, comp_cname, (SELECT code_valuedesc  FROM visitdb..visit_codetbl WHERE (code_type = '007') AND ( cust.comp_country = code_value)) AS CN   FROM  visitdb..cust where (cust.comp_delmark <> '1') ";
        ////this.SDS_jssg.SelectParameters.Add("keyword ", TB_mp.Text);
        ////this.SDS_jssg.SelectParameters.Add("oricountry", TypeCode.String, "");
        ////這是判斷Null 值也要順利bind 
        //for (int i = 0; i < this.SDS_jssg.SelectParameters.Count; i++)
        //{
        //    SDS_jssg.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_jssg.DataBind();
        //this.SGV_company.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            if (TB_mp.Text != "")
                sqlCmd.CommandText = " SELECT  comp_idno, comp_cname, (SELECT code_valuedesc  FROM visitdb..visit_codetbl WHERE (code_type = '007') AND ( cust.comp_country = code_value)) AS CN   FROM  visitdb..cust where (cust.comp_delmark <> '1') and ( comp_idno like '%'+@keyword+'%' or comp_cname  like '%'+@keyword+'%')";
            else
                sqlCmd.CommandText = " SELECT  comp_idno, comp_cname, (SELECT code_valuedesc  FROM visitdb..visit_codetbl WHERE (code_type = '007') AND ( cust.comp_country = code_value)) AS CN   FROM  visitdb..cust where (cust.comp_delmark <> '1') ";


            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll(TB_mp.Text.ToUpper()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


        this.btn_Ins.Visible = true;
    }

    protected void BindDLL()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = "SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl WHERE (code_value LIKE '__0') and code_type='001' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_code1.DataSource = dt;
                DDL_code1.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = "SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl where 1=0 ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_code2.DataSource = dt;
                DDL_code2.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void INSData(string value)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        //this.SDS_Instblgetcust.InsertParameters.Clear();
        //this.SDS_Instblgetcust.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_Instblgetcust.InsertCommand = "pr_tbl_getcust";
        //this.SDS_Instblgetcust.InsertParameters.Add("gc_colkey", TypeCode.String, this.Commonkey);
        //this.SDS_Instblgetcust.InsertParameters.Add("gc_colreturnvalue", TypeCode.String, value);
        //this.SDS_Instblgetcust.InsertParameters.Add("gc_empno", TypeCode.String, ssoUser.empNo);
        //this.SDS_Instblgetcust.InsertParameters.Add("gc_systemcode", TypeCode.String, SystemCode);
        //this.SDS_Instblgetcust.InsertParameters.Add("Type", TypeCode.String, "I");

        try
        {
            //this.SDS_Instblgetcust.Insert();
            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"pr_tbl_getcust";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@gc_colkey", oRCM.SQLInjectionReplaceAll(Commonkey));
                sqlCmd.Parameters.AddWithValue("@gc_colreturnvalue", oRCM.SQLInjectionReplaceAll(value));
                sqlCmd.Parameters.AddWithValue("@gc_empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@gc_systemcode", oRCM.SQLInjectionReplaceAll(SystemCode));
                sqlCmd.Parameters.AddWithValue("@Type", "I");


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["pubbs"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

        }
        catch (Exception ex)
        {
            //要寫導頁or Error log 
            //目前donthing
            throw ex;
        }
    }
    protected void SGV_company_PageIndexChanged(object sender, EventArgs e)
    {
        databinding();
    }
    protected void SGV_company_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_company.PageIndex = e.NewPageIndex;
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "view_case")
        {
            // ClientScript.RegisterStartupScript(this.GetType(), "error", "alert('" + e.CommandArgument  + "');", true);
            //INSData(e.CommandArgument.ToString());
            Session["compno"] = e.CommandArgument.ToString();
            //ClientScript.RegisterClientScriptBlock(this.GetType(), "close", "close_win();", true);
            //string script = "<script language='javascript'>parent.$.fn.colorbox.close();</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            //Response.Redirect("http:////localhost:56588//2014//subap//colorbox_close.aspx");
            string script = "<script language='javascript'>window.location.replace('http://localhost:56588/2014/subap/colorbox_close.aspx');</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    protected void SGV_company_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {
                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_company.SortExpression)
                    {
                        //否為為排序欄位
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_company.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "./images/icon-arrowasc.gif";
                        else
                            ig_sort.ImageUrl = "./images/icon-arrowdesc.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                }
            }
        }
    }
    protected void SDS_jssg_Selecting(object sender, SqlDataSourceSelectingEventArgs e)
    {

    }
    protected void btn_Ins_Click(object sender, EventArgs e)
    {
        string url = string.Format(@"{0}?SystemCode={1}&Commonkey={2}", ConfigurationManager.AppSettings["CustAdd"], this.SystemCode, this.Commonkey);

        this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>adjustDims();  go('" + url + "');</script>");
        // Response.Redirect(url);

    }
}