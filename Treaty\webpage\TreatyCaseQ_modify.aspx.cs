﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCaseQ_modify : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));

        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            BT_Customer.Attributes.Add("onclick", "find_customer2();");

            if (Request.QueryString["contno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["contno"].Replace("-", "")) || (Request.QueryString["contno"].Length > 15))
                    Response.Redirect("../danger.aspx");
                ViewState["contno"] = Request.QueryString["contno"].ToString();
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyCase_contnoToseno";
                //SDS_NR.SelectParameters.Add("contno", ViewState["contno"].ToString());
                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                //{
                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_NR.DataBind();
                //System.Data.DataView dv_contnoToseno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                #region --- query ---
                DataTable dtNR = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_contnoToseno";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@contno", oRCM.SQLInjectionReplaceAll(ViewState["contno"].ToString()));


                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dtNR);
                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_contnoToseno = dtNR.DefaultView;
                if (dv_contnoToseno.Count >= 1)
                {
                    if (dv_contnoToseno[0][0].ToString() == "")
                        Response.Redirect("../danger.aspx");
                    else
                        ViewState["seno"] = dv_contnoToseno[0][0].ToString();
                }
            }
            if (Request["seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
            }
            if (ViewState["seno"] == null)
                Response.Redirect("../danger.aspx");

            ViewState["empNo"] = ssoUser.empNo;
            //SDS_auth.SelectParameters.Clear();
            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_auth.SelectCommand = "esp_TreatyCase_Auth";
            //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_Auth";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@empno", ssoUser.empNo);

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_auth = dt.DefaultView;
            if (dv_auth.Count >= 1)
            {
                string str_auth = dv_auth[0][0].ToString();
                if (str_auth == "X")
                    Response.Redirect("../NoAuthRight.aspx");
                ViewState["auth"] = dv_auth[0][0].ToString();
                ViewState["SYS"] = dv_auth[0][1].ToString();

            }

            Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "維護法律問題承辦單", "", Server.HtmlEncode(ViewState["seno"].ToString()), "treaty\\TreatyCaseQ_modify.aspx");
            BindData();
            BindData_file();
            BindInspect();
            BindDefer();
            BindDDL_SeqSn();
            DDL_SeqSn.SelectedValue = Server.HtmlEncode(ViewState["seno"].ToString());
            BT_AddInspect.Attributes.Add("onclick", "Add_Inspect(" + Server.HtmlEncode(ViewState["seno"].ToString()) + ");");
            LT_tratycase_info.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_info.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >案件紀錄</a>";
            LT_infoHandel.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_infoHandel.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次承辦人資訊</a>";
            LT_historyRecord.Text = "<a class='iterm_dymanic_historyRecord' rel='./TreatyCase_historyRecord.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次修改紀錄</a>";
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            txt_req_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            BT_SendInspect.Attributes.Add("onclick", "return  confirm('確定要審查 ?');");
            BT_End.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
            BT_FileUp.Attributes.Add("onclick", "treaty_fileup('" + Server.HtmlEncode(txtComplexNo.Text.Replace("-", "")) + "'," + Server.HtmlEncode(ViewState["seno"].ToString()) + ");");
            BT_defert.Attributes.Add("onclick", "treaty_defert(" + Server.HtmlEncode(ViewState["seno"].ToString()) + ");");


            if ((ViewState["SYS"].ToString().IndexOf("ADM") >= 1))
            {
                btnDelete.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                btnDelete.Visible = true;
            }

            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
        }

        #region postback
        //lb_keyin_date.Text = DateTime.Now.ToString("yyyyMMdd"); //建檔日期
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Inspect_renew")
        {
            BindInspect();
        }
        if (Request.Params.Get("__EVENTTARGET") == "case_renew")
        {
            BindData();
        }
        #endregion        
    }
    private void BindData_Customer()
    {
        //this.SDS_company.SelectParameters.Clear();
        //this.SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_company.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //this.SDS_company.SelectParameters.Add("customers", TypeCode.String, this.h_compno.Value.ToString());
        //this.SDS_company.DataBind();
        //this.SDS_company.SelectParameters.Clear();
        //this.SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_company.SelectCommand = "esp_TreatyCase_MultiCustomer_List_by_NOs";
        //this.SDS_company.SelectParameters.Add("tc_seno", TypeCode.String, ViewState["seno"].ToString());
        //this.SDS_company.SelectParameters.Add("customers", TypeCode.String, this.h_compno.Value.ToString());
        //this.SDS_company.SelectParameters.Add("簽約金額", TypeCode.String, ViewState["簽約金額"].ToString());
        //this.SDS_company.SelectParameters.Add("mode", TypeCode.String, "view");
        //this.SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));
            sqlCmd.Parameters.AddWithValue("@簽約金額", oRCM.SQLInjectionReplaceAll(ViewState["簽約金額"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    public void BindData()
    {
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_NR.SelectCommand = " select * from treaty_case where tc_seno = @sn ";
        //SDS_NR.SelectParameters.Add("sn", TypeCode.String, ViewState["seno"].ToString());
        //SDS_NR.DataBind();
        ViewState["簽約金額"] = "0";
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * from treaty_case where tc_seno = @sn ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            if (dv[0]["tc_class"].ToString().Trim() != "Q")
                Response.Redirect("../danger.aspx");
            string str_tr_year = dv[0]["tc_year"].ToString().Trim();
            string str_tr_orgcd = dv[0]["tc_orgcd"].ToString().Trim();
            string str_tr_class = dv[0]["tc_class"].ToString().Trim();
            ViewState["tr_class"] = str_tr_class;
            string str_tr_sn = dv[0]["tc_sn"].ToString().Trim();
            string str_tr_ver = dv[0]["tc_ver"].ToString().Trim();
            string str_tr_seqsn = dv[0]["tc_seqsn"].ToString().Trim();
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;

            switch (dv[0]["tc_language"].ToString().Trim())
            {

                case "1": rb_language_chiness.Checked = true; break;
                case "2": rb_language_english.Checked = true; break;
            }
            #region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + dv[0]["tc_promoter_no"].ToString().Trim() + "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tc_promoter_no"].ToString().Trim()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt = new DataTable();
                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_emp = dt.DefaultView;
            if (dv_emp.Count >= 1)
            {
                txt_req_dept.Text = Server.HtmlEncode(dv_emp[0]["com_deptid"].ToString().Trim());
                txt_promoter_name.Text = Server.HtmlEncode(dv_emp[0]["com_cname"].ToString().Trim());
                txt_promoter_empno.Value = Server.HtmlEncode(dv_emp[0]["com_empno"].ToString().Trim());
                txtTel.Text = Server.HtmlEncode(dv_emp[0]["com_telext"].ToString().Trim());
                txtOrgAbbrName.Text = Server.HtmlEncode(dv_emp[0]["orgName"].ToString().Trim());
                ViewState["com_orgcd"] = dv_emp[0]["com_orgcd"].ToString().Trim();
            }
            if (dv[0]["tc_hec_flag"].ToString().Trim() == "1")
                LB_hec_flag.Visible = true;
            txt_name.Text = Server.HtmlEncode(dv[0]["tc_name"].ToString().Trim());//洽案（契約名稱）
            #endregion
            ViewState["簽約金額"] = (dv[0]["tc_money"].ToString().Trim() == "0" ? 0 : decimal.Parse(dv[0]["tc_money"].ToString().Trim()))
                                  * (dv[0]["tc_money_rate"].ToString().Trim() == "" ? 1 : decimal.Parse(dv[0]["tc_money_rate"].ToString().Trim()));
            #region 客戶
            h_compno.Value = Server.HtmlEncode(dv[0]["tc_compidno_all"].ToString().Trim().Replace("㊣", ","));//簽約對象(多)
            BindData_Customer();
            #endregion
            #region 契約預估金額
            BindContMoneyType();
            ddlContMoneyType.SelectedValue = IIf(dv[0]["tc_money_type"].ToString().Trim() == "", "TWD", dv[0]["tc_money_type"].ToString().Trim());
            txtContMoney.Text = Server.HtmlEncode(dv[0]["tc_money"].ToString().Trim());
            #endregion
            #region 契約期間
            txt_contsdate.Text = Server.HtmlEncode(dv[0]["tc_contsdate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_contsdate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");
            txt_contedate.Text = Server.HtmlEncode(dv[0]["tc_contedate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_contedate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");
            #endregion
            Bindcasetype(dv[0]["tc_case_style"].ToString().Trim());
            BindContType(dv[0]["tc_conttype"].ToString().Trim());
            TB_案號股別.Text = Server.HtmlEncode(dv[0]["tc_案號股別"].ToString().Trim());
            ViewState["ver"] = dv[0]["tc_sRC_ver"].ToString().Trim();
            txtSignReason.Text = Server.HtmlEncode(dv[0]["tc_sign_reason"].ToString().Trim());
            txt_betsum.Text = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tc_betsum"].ToString().Trim()));
            txtManageNote.Text = Server.HtmlEncode(dv[0]["tc_manage_note"].ToString().Trim());
            lb_assign_name.Text = Server.HtmlEncode(dv[0]["tc_assign_name"].ToString());//分案主管
            lb_assign_date.Text = Server.HtmlEncode(dv[0]["tc_assign_date"].ToString().Length > 0 ? DateTime.ParseExact(dv[0]["tc_assign_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");  //分案日期
            lb_handle_name.Text = Server.HtmlEncode(dv[0]["tc_handle_name"].ToString());//法務承辦人姓名
            lb_handle_empno.Text = Server.HtmlEncode(dv[0]["tc_handle_empno"].ToString());//法務承辦人工號
            lb_handle_ext.Text = Server.HtmlEncode(dv[0]["tc_handle_ext"].ToString());//法務承辦人分機
            lb_expect_close_date.Text = Server.HtmlEncode(dv[0]["tc_prefinish_date"].ToString().Length > 0 ? DateTime.ParseExact(dv[0]["tc_prefinish_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");//預估完成日
            lb_process_date.Text = Server.HtmlEncode(dv[0]["tc_process_date"].ToString());//處理天數
            if (dv[0]["tc_degree"].ToString().Trim() != "Z")//如果狀態不是結件，不秀
            {
                if (lb_process_date.Text.Trim() == "0")
                    lb_process_date.Text = "";
                if (lb_contract_count.Text.Trim() == "0")
                    lb_contract_count.Text = "";
            }
            lb_contract_count.Text = Server.HtmlEncode(dv[0]["tc_contract_count"].ToString()); //產出契約數

            if (((dv[0]["tc_degree"].ToString().Trim() == "Z") || (dv[0]["tc_degree"].ToString().Trim() == "C")))
            {
                BT_Print_Excel.Visible = true;
                BT_Print_Tag.Visible = true;
                BT_Print.Visible = true;
            }
            if (dv[0]["tc_inspect"].ToString() == "1")
                CB_NotInspect.Checked = true;
            if ((ViewState["auth"].ToString() == "W" || ViewState["auth"].ToString() == "A") && (dv[0]["tc_degree"].ToString().Trim() == "0" || dv[0]["tc_degree"].ToString().Trim() == "8"))
            {
                if ((dv[0]["tc_status"].ToString() == "5") || (dv[0]["tc_status"].ToString() == "7") || (dv[0]["tc_status"].ToString() == "9"))
                {
                    //SDS_auth.SelectParameters.Clear();
                    //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                    //SDS_auth.SelectCommand = "esp_treatyCase_Inspect_getUnInspect";
                    //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                    //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                    //{
                    //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                    //}
                    //SDS_auth.DataBind();
                    //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

                    #region --- query ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;

                        sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dt = new DataTable();
                            sqlDA.Fill(dt);

                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv_auth = dt.DefaultView;
                    if (dv_auth.Count >= 1)
                    {
                        if (dv_auth[0][0].ToString() == "0")
                            BT_End.Visible = true; //沒有審查人 || 審查人都審查完
                        else
                        {
                            BT_End.Visible = false;
                            BT_SendInspect.Visible = true;
                        }
                    }
                }
            }
            if ((dv[0]["tc_status"].ToString().Trim() == "5") && (dv[0]["tc_handle_empno"].ToString() == ViewState["empNo"].ToString()))
            {
                //當法務承辦人進來檢視案件時,案件狀態由 5:待法務讀取 --> 7:處理中
                //SDS_NR.UpdateParameters.Clear();
                //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
                //SDS_NR.UpdateParameters.Add("tc_seno", TypeCode.String, ViewState["seno"].ToString());
                //SDS_NR.UpdateParameters.Add("tc_degree", TypeCode.String, dv[0]["tc_degree"].ToString().Trim());
                //SDS_NR.UpdateParameters.Add("tc_status", TypeCode.String, "7");
                //SDS_NR.Update();

                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_status_update";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@tc_degree", oRCM.SQLInjectionReplaceAll(dv[0]["tc_degree"].ToString().Trim()));
                    sqlCmd.Parameters.AddWithValue("@tc_status", "7");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
            }

            //lb_case_closedate.Text = dv[0]["tc_case_closedate"].ToString().Trim();//需求結件日期
            //SDS_DDL_degree.SelectCommand = "exec esp_treatyCase_codetable  '' ,'08' ";
            //SDS_DDL_degree.DataBind();
            //DDL_Degree.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treatyCase_codetable";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                sqlCmd.Parameters.AddWithValue("@code_group", "");
                sqlCmd.Parameters.AddWithValue("@code_type", "08");

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt = new DataTable();
                    sqlDA.Fill(dt);

                    DDL_Degree.DataSource = dt;
                    DDL_Degree.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DDL_Degree.SelectedValue = dv[0]["tc_degree"].ToString().Trim();//進度
            LT_L_Degree.Text = Server.HtmlEncode(DDL_Degree.SelectedItem.Text);
            if (dv[0]["tc_send_datetime"].ToString().Trim().Length > 0)//送件日期
            {
                DateTime dTime = DateTime.Parse(dv[0]["tc_send_datetime"].ToString().Trim());
                lb_send_date.Text = Server.HtmlEncode(dTime.ToString("yyyy/MM/dd"));
            }
            lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_name"].ToString());//修改人
            lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_no"].ToString());//修改工號
            lb_modify_date.Text = Server.HtmlEncode(dv[0]["tc_modify_date"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc_modify_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : ""); //修改日期
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            Treaty_log(ViewState["seno"].ToString(), "檢視承辦單", "", "", "treaty\\TreatyCaseQ_view.aspx");
        }
    }

    private void BindContMoneyType()
    {
        ddlContMoneyType.Items.Clear();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    private void BindData_file()
    {
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyCase_files";
        //SDS_gv_file.SelectParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_gv_file.SelectParameters.Add("empno", TypeCode.String, ViewState["empNo"].ToString().Trim());
        //if (ViewState["SYS"].ToString().IndexOf("LAW") > 1)
        //    SDS_gv_file.SelectParameters.Add("mode", TypeCode.String, "Edit");
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_files";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString().Trim()));
            if (ViewState["SYS"].ToString().IndexOf("LAW") > 1)
                sqlCmd.Parameters.AddWithValue("@mode", "Edit");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {

        if (DDL_case_style.SelectedValue == "1")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = false;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10') ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10') ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);

                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }

        if (DDL_case_style.SelectedValue == "2")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = false;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27') ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27') ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);

                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "6")
        {

            rb_language_chiness.Checked = true;
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件語文.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);

                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "7")
        {

            rb_language_chiness.Checked = true;
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件語文.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);

                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "9")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = false;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28') ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28') ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);

                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
        }
        #endregion
    }
    private void Bindcasetype(string strContType)
    {
        string strCondition = "";
        //SDS_case_style.SelectCommand = "exec esp_treatyCase_codetable  '" + strCondition + "' ,'16' ";
        //SDS_case_style.DataBind();
        //DDL_case_style.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_codetable";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "16");
            sqlCmd.Parameters.AddWithValue("@mode", "case_class");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_case_style.DataSource = dt;
                DDL_case_style.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        #region 如果有指定 ContType,則將指定的 ContType 選取
        DDL_case_style.SelectedValue = strContType;
        #endregion
        if (DDL_case_style.SelectedValue == "1")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
        }
        if (DDL_case_style.SelectedValue == "2")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
        }
        if (DDL_case_style.SelectedValue == "3")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
        }
        if (DDL_case_style.SelectedValue == "4")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
        }
        if (DDL_case_style.SelectedValue == "5")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
        }
        if (DDL_case_style.SelectedValue == "6")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件語文.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = true;
        }
        if (DDL_case_style.SelectedValue == "7")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件語文.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = true;
        }
        if (DDL_case_style.SelectedValue == "9")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件分類.Visible = true;
        }

    }

    private void BindInspect()
    {

        //SDS_Inspect.SelectParameters.Clear();
        //SDS_Inspect.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect.SelectCommand = "esp_treatyCase_Inspect";
        //SDS_Inspect.SelectParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_Inspect.DataBind();
        //GV_Inspect.DataBind();
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_Inspect";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

                GV_Inspect.DataSource = dt;
                GV_Inspect.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


        //SDS_auth.SelectParameters.Clear();
        //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_auth.SelectCommand = "esp_treatyCase_Inspect_getUnInspect";
        //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
        //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
        //{
        //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_auth.DataBind();
        //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            if (dv_auth[0][0].ToString() == "0")
                BT_End.Visible = true; //沒有審查人 || 審查人都審查完
            else
            {
                BT_End.Visible = false;
                BT_SendInspect.Visible = true;
            }
        }


    }
    private void BindDDL_SeqSn()
    {
        //SDS_DDL_SeqSn.SelectParameters.Clear();
        //SDS_DDL_SeqSn.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_DDL_SeqSn.SelectCommand = "esp_TreatyCase_senoTree";
        //SDS_DDL_SeqSn.SelectParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_DDL_SeqSn.DataBind();
        //DDL_SeqSn.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_senoTree";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_SeqSn.DataSource = dt;
                DDL_SeqSn.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void DDL_SeqSn_SelectedIndexChanged(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyCaseQ_view.aspx?seno=" + DDL_SeqSn.SelectedValue);
    }
    private void BindDefer()
    {

        //SDS_Defer.SelectParameters.Clear();
        //SDS_Defer.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Defer.SelectCommand = "esp_TreatyCase_Defer";
        //SDS_Defer.SelectParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_Defer.DataBind();
        //GV_Defer.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_Defer";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                GV_Defer.DataSource = dt;
                GV_Defer.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        if (str == "")
            return true;
        else
        {
            System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
            return reg1.IsMatch(str);
        }
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = Server.HtmlEncode(h_compno.Value.Replace("," + e.CommandArgument, ""));
            BindData_Customer();
        }
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + Server.HtmlEncode(LB.Text.ToString()) + "\");' >" + Server.HtmlEncode(LB.Text.ToString()) + "</a>";

    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //ViewState["Customers"] = Convert.ToString(ViewState["Customers"]).Trim().Replace("," + e.CommandArgument, "");
            //BindData();
            string str_file_url = "";
            string str_filename = "";
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyApply_file_modify";
            //SDS_log.SelectParameters.Add("req_id", "");
            //SDS_log.SelectParameters.Add("fd_name", "");
            //SDS_log.SelectParameters.Add("filetxt", "");
            //SDS_log.SelectParameters.Add("file_url", "");
            //SDS_log.SelectParameters.Add("empno", "");
            //SDS_log.SelectParameters.Add("mode", "view");
            //SDS_log.SelectParameters.Add("fid", e.CommandArgument.ToString());
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            FileInfo fi = new FileInfo(str_file_url.Replace("/", "").Replace("..", ""));
            if (fi.Exists)
            {
                fi.Delete();
                Treaty_log(ViewState["seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCaseQ_modify.aspx");
                //SDS_gv_file.DeleteParameters.Clear();
                //SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_gv_file.DeleteCommand = "esp_TreatyApply_file_modify";
                //SDS_gv_file.DeleteParameters.Add("req_id", "");
                //SDS_gv_file.DeleteParameters.Add("fd_name", "");
                //SDS_gv_file.DeleteParameters.Add("filetxt", "");
                //SDS_gv_file.DeleteParameters.Add("file_url", "");
                //SDS_gv_file.DeleteParameters.Add("empno", "");
                //SDS_gv_file.DeleteParameters.Add("mode", "delete");
                //SDS_gv_file.DeleteParameters.Add("fid", e.CommandArgument.ToString());
                //SDS_gv_file.Delete();
                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@req_id", "");
                    sqlCmd.Parameters.AddWithValue("@fd_name", "");
                    sqlCmd.Parameters.AddWithValue("@filetxt", "");
                    sqlCmd.Parameters.AddWithValue("@file_url", "");
                    sqlCmd.Parameters.AddWithValue("@empno", "");
                    sqlCmd.Parameters.AddWithValue("@mode", "delete");
                    sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
            }
            BindData_file();
        }
        if (e.CommandName == "xEdit")
        {
            BindData_file();
        }
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyApply_file_modify";
            //SDS_log.SelectParameters.Add("req_id", "");
            //SDS_log.SelectParameters.Add("fd_name", "");
            //SDS_log.SelectParameters.Add("filetxt", "");
            //SDS_log.SelectParameters.Add("file_url", "");
            //SDS_log.SelectParameters.Add("empno", "");
            //SDS_log.SelectParameters.Add("mode", "view");
            //SDS_log.SelectParameters.Add("fid", e.CommandArgument.ToString());
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCaseQ_View.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url.Replace("/", "").Replace("..", ""))));
                Response.Flush();
                Response.End();
            }
        }
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            Label lb_tcdf_type = (Label)e.Row.FindControl("LB_tcdf_type");
            Label lb_inspect = (Label)e.Row.FindControl("LB_inspect");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");
                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
                if (lb_tcdf_type.Text == "RE")
                {
                    lb_del.Visible = false;
                    lb_edit.Visible = false;
                }
                if (lb_inspect.Text == "0")
                    lb_inspect.Text = "";
                else
                    lb_inspect.Text = "V";

            }
        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", GetUserIP());
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void btEdit_Click(object sender, EventArgs e)
    {

        string str_error = "";
        string str_danger = "0";
        if (txt_name.Text == "")
            str_error += "★請輸入契約名稱 必須輸入\\n ";
        if (txt_promoter_empno.Value == "")
            str_error += "★單位承辦人 必須輸入\\n ";

        if (txt_name.Text == "請輸入契約名稱")
        {
            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#h_compno').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#h_compno').click(function () { $('#h_compno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);
        }
        if ((txt_contsdate.Text != "") && (txt_contedate.Text != ""))
        {
            if ((!CheckDateTimeType(txt_contsdate.Text)) && (!CheckDateTimeType(txt_contedate.Text)))
            {
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }
        if ((DDL_case_style.SelectedValue.Length > 3))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3))
            str_danger = "1";
        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";

        if ((txtContMoney.Text.Trim().Length > 10) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        else
        {
            if (txtContMoney.Text.Trim() == "")
                txtContMoney.Text = "0";
        }
        if (txtManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if (txtSignReason.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if ((DDL_case_style.SelectedValue == "1") || (DDL_case_style.SelectedValue == "2") || (DDL_case_style.SelectedValue == "9"))
        {
            if (ddlContMoneyType.SelectedValue == "")
                str_error += "★幣別不能挑選空白 \\n ";
            else
            {
                if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                    str_danger = "1";
            }
        }
        if (str_error != "")
        {
            string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");
            DoSaveDraft();
            string script = "<script language='javascript'>alert('存檔成功！');location.href='./TreatyCaseQ_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    private void DoSaveDraft()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_TreatyCase_update";
        //SDS_NR.UpdateParameters.Add("tc_seno", ViewState["seno"].ToString());
        //SDS_NR.UpdateParameters.Add("tc_old_contno", txtOldContno.Text.Trim()); //將舊案的流水號存入
        //SDS_NR.UpdateParameters.Add("tc_name", txt_name.Text.Trim());//洽案(契約)名稱
        //SDS_NR.UpdateParameters.Add("tc_compidno", "");
        //SDS_NR.UpdateParameters.Add("tc_compname", "");
        //SDS_NR.UpdateParameters.Add("tc_compidno_all", h_compno.Value);
        //SDS_NR.UpdateParameters.Add("tc_compname_all", "");
        ////if (rb_language_other.Checked)
        ////    SDS_NR.UpdateParameters.Add("tc_language", "0");//契約語文-其他
        //if (rb_language_chiness.Checked)
        //    SDS_NR.UpdateParameters.Add("tc_language", "1");//契約語文-中文
        //if (rb_language_english.Checked)
        //    SDS_NR.UpdateParameters.Add("tc_language", "2");//契約語文-英文
        //#region 案件性質
        //SDS_NR.UpdateParameters.Add("tc_conttype_b0", "0");//技術服務
        //SDS_NR.UpdateParameters.Add("tc_conttype_b1", "0");//合作開發
        //SDS_NR.UpdateParameters.Add("tc_conttype_d4", "0");//技術授權
        //SDS_NR.UpdateParameters.Add("tc_conttype_d5", "0");//專利授權
        //SDS_NR.UpdateParameters.Add("tc_conttype_d7", "0");//專利讓與
        //SDS_NR.UpdateParameters.Add("tc_conttype_ns", "0");//新創事業(洽案)
        //SDS_NR.UpdateParameters.Add("tc_conttype_rb", "0");//標案 
        //SDS_NR.UpdateParameters.Add("tc_conttype_uo", "0");//國外支出(無收入) 
        //SDS_NR.UpdateParameters.Add("tc_conttype_ui", "0");//國外支出(無收入) 
        //SDS_NR.UpdateParameters.Add("tc_class_other_desc", ""); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
        //#endregion
        //#region 契約修訂
        //SDS_NR.UpdateParameters.Add("tc_amend", "0");
        //SDS_NR.UpdateParameters.Add("tc_amend_other_desc", "");
        //#endregion
        //SDS_NR.UpdateParameters.Add("tc_contsdate", txt_contsdate.Text.Trim().Replace("/", ""));  //契約期間(起)
        //SDS_NR.UpdateParameters.Add("tc_contedate", txt_contedate.Text.Trim().Replace("/", ""));  //契約期間(迄)
        //SDS_NR.UpdateParameters.Add("tc_sign_reason", txtSignReason.Text);//簽約緣由與目的
        //SDS_NR.UpdateParameters.Add("tc_otherrequire_ver", "2");
        //SDS_NR.UpdateParameters.Add("tc_modify_emp_no", ssoUser.empNo);  // 修改工號
        //SDS_NR.UpdateParameters.Add("tc_modify_emp_name", ssoUser.empName.Trim());// 修改人
        //SDS_NR.UpdateParameters.Add("tc_modify_date", "");      // 修改日期
        //SDS_NR.UpdateParameters.Add("tc_conttype", ddlContType.SelectedValue.ToString().Trim()); //契約性質
        //SDS_NR.UpdateParameters.Add("tc_money_type", ddlContMoneyType.SelectedValue.ToString().Trim());//契約預估金額
        //if (txtContMoney.Text.Trim() == "")
        //    SDS_NR.UpdateParameters.Add("tc_money", "0");
        //else
        //    SDS_NR.UpdateParameters.Add("tc_money", txtContMoney.Text.Trim());

        //SDS_NR.UpdateParameters.Add("tc_betsum", txt_betsum.Text);//法務承辦人意見彙整
        //SDS_NR.UpdateParameters.Add("tc_manage_note", txtManageNote.Text);//管理者備註欄位
        //SDS_NR.UpdateParameters.Add("tc_case_style", DDL_case_style.SelectedValue.ToString());
        //SDS_NR.Update();

        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_update";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tc_old_contno", oRCM.SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
            sqlCmd.Parameters.AddWithValue("@tc_name", oRCM.SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
            sqlCmd.Parameters.AddWithValue("@tc_compidno", "");
            sqlCmd.Parameters.AddWithValue("@tc_compname", "");
            sqlCmd.Parameters.AddWithValue("@tc_compidno_all", oRCM.SQLInjectionReplaceAll(h_compno.Value));
            sqlCmd.Parameters.AddWithValue("@tc_compname_all", "");
            //if (rb_language_other.Checked)
            //    sqlCmd.Parameters.AddWithValue("tc_language", "0");//契約語文-其他
            if (rb_language_chiness.Checked)
                sqlCmd.Parameters.AddWithValue("@tc_language", "1");//契約語文-中文
            if (rb_language_english.Checked)
                sqlCmd.Parameters.AddWithValue("@tc_language", "2");//契約語文-英文
            #region 案件性質
            sqlCmd.Parameters.AddWithValue("@tc_conttype_b0", "0");//技術服務
            sqlCmd.Parameters.AddWithValue("@tc_conttype_b1", "0");//合作開發
            sqlCmd.Parameters.AddWithValue("@tc_conttype_d4", "0");//技術授權
            sqlCmd.Parameters.AddWithValue("@tc_conttype_d5", "0");//專利授權
            sqlCmd.Parameters.AddWithValue("@tc_conttype_d7", "0");//專利讓與
            sqlCmd.Parameters.AddWithValue("@tc_conttype_ns", "0");//新創事業(洽案)
            sqlCmd.Parameters.AddWithValue("@tc_conttype_rb", "0");//標案 
            sqlCmd.Parameters.AddWithValue("@tc_conttype_uo", "0");//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tc_conttype_ui", "0");//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tc_class_other_desc", ""); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
            #endregion
            #region 契約修訂
            sqlCmd.Parameters.AddWithValue("@tc_amend", "0");
            sqlCmd.Parameters.AddWithValue("@tc_amend_other_desc", "");
            #endregion
            sqlCmd.Parameters.AddWithValue("@tc_contsdate", oRCM.SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
            sqlCmd.Parameters.AddWithValue("@tc_contedate", oRCM.SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
            sqlCmd.Parameters.AddWithValue("@tc_sign_reason", oRCM.SQLInjectionReplaceAll(txtSignReason.Text));//簽約緣由與目的
            sqlCmd.Parameters.AddWithValue("@tc_otherrequire_ver", "2");
            sqlCmd.Parameters.AddWithValue("@tc_modify_emp_no", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));  // 修改工號
            sqlCmd.Parameters.AddWithValue("@tc_modify_emp_name", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));// 修改人
            sqlCmd.Parameters.AddWithValue("@tc_modify_date", "");      // 修改日期
            sqlCmd.Parameters.AddWithValue("@tc_conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
            sqlCmd.Parameters.AddWithValue("@tc_money_type", oRCM.SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
            if (txtContMoney.Text.Trim() == "")
                sqlCmd.Parameters.AddWithValue("@tc_money", "0");
            else
                sqlCmd.Parameters.AddWithValue("@tc_money", oRCM.SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@tc_betsum", txt_betsum.Text);//法務承辦人意見彙整
            sqlCmd.Parameters.AddWithValue("@tc_manage_note", oRCM.SQLInjectionReplaceAll(txtManageNote.Text));//管理者備註欄位
            sqlCmd.Parameters.AddWithValue("@tc_case_style", oRCM.SQLInjectionReplaceAll(DDL_case_style.SelectedValue.ToString()));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        if (DDL_case_style.SelectedValue == "6" || DDL_case_style.SelectedValue == "7")
        {
            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.Text;
            //SDS_NR.UpdateCommand = "update treaty_case set tc_案號股別 = @案號股別   where  tc_seno=@tc_seno";
            //SDS_NR.UpdateParameters.Add("tc_seno", ViewState["seno"].ToString());
            //SDS_NR.UpdateParameters.Add("案號股別", TB_案號股別.Text);
            //SDS_NR.Update();

            #region --- modify ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"update treaty_case set tc_案號股別 = @案號股別   where  tc_seno=@tc_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@案號股別", oRCM.SQLInjectionReplaceAll(TB_案號股別.Text));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "案件存檔", "", "", "treaty\\TreatyCaseQ_modify.aspx");
    }
    protected void BT_End_Click(object sender, EventArgs e)
    {

        //SDS_Inspect_count.SelectParameters.Clear();
        //SDS_Inspect_count.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect_count.SelectCommand = "esp_treatyCase_Inspect_getUnInspect";
        //SDS_Inspect_count.SelectParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
        //for (int i = 0; i < this.SDS_Inspect_count.SelectParameters.Count; i++)
        //{
        //    SDS_Inspect_count.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_Inspect_count.DataBind();
        //System.Data.DataView dv_count = (DataView)SDS_Inspect_count.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_count = dt.DefaultView;
        if (dv_count.Count >= 1)
        {
            if (dv_count[0][0].ToString() != "0")
            {
                StringBuilder script = new StringBuilder("<script type='text/javascript'>alert('還有審查人未審查,不能結案!');</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }
            else
            {
                string str_error = "";
                string str_danger = "0";
                if (txt_name.Text == "")
                    str_error += "★請輸入契約名稱 必須輸入\\n ";
                if (txt_promoter_empno.Value == "")
                    str_error += "★單位承辦人 必須輸入\\n ";

                if (txt_name.Text == "請輸入契約名稱")
                {
                    //str_error += "★契約名稱 必須輸入\\n ";
                    string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
                }
                if (h_compno.Value == "")
                {
                    str_error += "★簽約對象 必須輸入\\n ";
                    string script_alert = "<script language='javascript'> $('#h_compno').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#h_compno').click(function () { $('#h_compno').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);
                }
                if ((txt_contsdate.Text != "") && (txt_contedate.Text != ""))
                {
                    if ((!CheckDateTimeType(txt_contsdate.Text)) && (!CheckDateTimeType(txt_contedate.Text)))
                    {
                        int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                        int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                        if (dt1 > dt2)
                            str_error += "★契約期間異常 (起日 > 訖日) \\n ";
                    }
                }

                if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
                    str_danger = "1";
                if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                {
                    str_danger = "1";
                    string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '請挑選契約性質!','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
                }
                if ((DDL_case_style.SelectedValue.Length > 3))
                    str_danger = "1";
                if ((ddlContType.SelectedValue.Length > 3))
                    str_danger = "1";
                if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                    str_danger = "1";

                if ((txtContMoney.Text.Trim().Length > 10) || (!IsNumber(txtContMoney.Text)))
                {
                    str_danger = "1";
                }
                else
                {
                    if (txtContMoney.Text.Trim() == "")
                        txtContMoney.Text = "0";
                }
                if (txtManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
                if (txtSignReason.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
                if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
                if ((DDL_case_style.SelectedValue == "1") || (DDL_case_style.SelectedValue == "2") || (DDL_case_style.SelectedValue == "9"))
                {
                    if (ddlContMoneyType.SelectedValue == "")
                        str_error += "★幣別不能挑選空白 \\n ";
                    else
                    {
                        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                            str_danger = "1";
                    }
                }
                if (str_error != "")
                {
                    string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
                }
                else
                {
                    if (str_danger == "1")
                        Response.Redirect("../danger.aspx");
                    DoSaveDraft();

                    //SDS_NR.UpdateParameters.Clear();
                    //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                    //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
                    //SDS_NR.UpdateParameters.Add("tc_seno", TypeCode.String, ViewState["seno"].ToString());
                    //SDS_NR.UpdateParameters.Add("tc_degree", TypeCode.String, "Z");
                    //SDS_NR.UpdateParameters.Add("tc_status", TypeCode.String, "Z");
                    //SDS_NR.UpdateParameters.Add("empno", TypeCode.String, ViewState["empNo"].ToString());
                    //SDS_NR.Update();
                    #region --- modify ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;
                        sqlCmd.CommandText = @"esp_TreatyCase_status_update";
                        sqlCmd.CommandTimeout = 0;
                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        sqlCmd.Parameters.AddWithValue("@tc_degree", "Z");
                        sqlCmd.Parameters.AddWithValue("@tc_status", "Z");
                        sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                        try
                        {
                            sqlConn.Open();
                            sqlCmd.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {

                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }
                    #endregion
                    Treaty_log(ViewState["seno"].ToString(), "結案", "", "", "treaty\\TreatyCaseQ_modify.aspx?");
                    string script = "<script language='javascript'>alert('案件結案！');location.href='./TreatyCaseQ_modify.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                }
            }
        }
    }

    protected void BT_SendInspect_Click(object sender, EventArgs e)
    {
        btEdit_Click(sender, e);
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
        //SDS_NR.UpdateParameters.Add("tc_seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_NR.UpdateParameters.Add("tc_degree", TypeCode.String, "4");
        //SDS_NR.UpdateParameters.Add("tc_status", TypeCode.String, "C");
        //SDS_NR.UpdateParameters.Add("empno", TypeCode.String, ViewState["empNo"].ToString());
        //SDS_NR.Update();

        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_status_update";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tc_degree", "4");
            sqlCmd.Parameters.AddWithValue("@tc_status", "C");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));


            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> SendInspect(" + Server.HtmlEncode(ViewState["seno"].ToString()) + ");</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }
    protected void GV_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_Istatus");
        if (LB != null)
            switch (LB.Text.Trim())
            {
                case "0":
                    LB.Text = "";
                    LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
                    if (lb_del != null)
                    {
                        //Label lb_order = (Label)e.Row.FindControl("LB_order");
                        //if ((lb_order.Text == "1") && (CB_NotInspect.Checked==false))
                        lb_del.Visible = true;
                    }
                    break;
                case "1":
                    LB.Text = "同意";
                    LinkButton lb_delx = (LinkButton)e.Row.FindControl("LB_del");
                    lb_delx.Visible = false;
                    break;
                case "2":
                    LB.Text = "退回";
                    break;
            }

    }
    protected void GV_Inspect_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //this.SDS_Inspect.DeleteParameters.Clear();
            //this.SDS_Inspect.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //this.SDS_Inspect.DeleteCommand = " delete treaty_case_inspect where tci_no=@tci_no";
            //this.SDS_Inspect.DeleteParameters.Add("tci_no", TypeCode.String, e.CommandArgument.ToString());
            //this.SDS_Inspect.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case_inspect where tci_no=@tci_no";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tci_no", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindInspect();
        }
    }
    protected void bt_reject_Click(object sender, EventArgs e)
    {
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommand = "exec esp_treaty_reject_mail @seno ";
        //SDS_NR.UpdateParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_reject_mail";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "退件", "", Server.HtmlEncode(ViewState["seno"].ToString()), Server.HtmlEncode(ViewState["empNo"].ToString()));
        //SDS_NR.DataBind();
        Response.Redirect("./TreatyApply_view.aspx?contno=" + Server.HtmlEncode(txtComplexNo.Text.Replace("-", "")));
    }
    protected void bt_cancle_Click(object sender, EventArgs e)
    {
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_TreatyCase_status_update";
        //SDS_NR.UpdateParameters.Add("tc_seno", TypeCode.String, ViewState["seno"].ToString());
        //SDS_NR.UpdateParameters.Add("tc_degree", TypeCode.String, "C");
        //SDS_NR.UpdateParameters.Add("tc_status", TypeCode.String, "Z");
        //SDS_NR.Update();
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_status_update";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tc_degree", "C");
            sqlCmd.Parameters.AddWithValue("@tc_status", "Z");

            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "需求取消", "", Server.HtmlEncode(ViewState["seno"].ToString()), Server.HtmlEncode(ViewState["empNo"].ToString()));
        BindData();
    }
    protected void BT_Print_Click(object sender, EventArgs e)
    {
        string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
        string url = string.Format("{0}/rptTreaty&rc:ToolBar=false&rs:ClearSession=true&rs:Format=pdf&tc_seno={1}", strRSPath, (string)ViewState["seno"]);
        Response.Redirect(url, true);
    }
    protected void BT_Print_Tag_Click(object sender, EventArgs e)
    {
        string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
        string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=image&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["seno"]);
        Response.Redirect(url, true);
    }
    protected void BT_Print_Excel_Click(object sender, EventArgs e)
    {
        string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
        string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=excel&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["seno"]);
        Response.Redirect(url, true);
    }

    protected void DDL_case_style_SelectedIndexChanged(object sender, EventArgs e)
    {

        if (DDL_case_style.SelectedValue == "1")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10')  order by display_order ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '10')  order by display_order ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }

        if (DDL_case_style.SelectedValue == "2")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27')  order by display_order ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '27')  order by display_order ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "3")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "4")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "5")
        {
            案件語文.Visible = false;
            rb_language_chiness.Checked = true;
            預定期間.Visible = false;
            契約預估金額.Visible = false;
            案件分類.Visible = false;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            txtContMoney.Text = "0";
            txt_contsdate.Text = "";
            txt_contedate.Text = "";
        }
        if (DDL_case_style.SelectedValue == "6")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件分類.Visible = true;
            案號股別.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }
        if (DDL_case_style.SelectedValue == "7")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = false;
            案件分類.Visible = true;
            案號股別.Visible = true;
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '29') order by display_order  ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }


        if (DDL_case_style.SelectedValue == "9")
        {
            案件語文.Visible = true;
            預定期間.Visible = true;
            契約預估金額.Visible = true;
            案件分類.Visible = true; ;
            案號股別.Visible = false;
            TB_案號股別.Text = "";
            ddlContType.Items.Clear();
            //SDS_ContType.SelectCommand = "select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28')  order by display_order ";
            //SDS_ContType.DataBind();
            //ddlContType.DataBind();

            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select subtype_desc,code_subtype from  treaty_code_table WHERE (code_type = '28')  order by display_order ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    ddlContType.DataSource = dt;
                    ddlContType.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }


    }
    protected void btnDelete_Click(object sender, EventArgs e)
    {
        //SDS_NR.DeleteParameters.Clear();
        //SDS_NR.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.DeleteCommand = "esp_treaty_TreatyEmpView_Delete";
        //SDS_NR.DeleteParameters.Add("caseno", txtComplexNo.Text.Replace("-", ""));
        //SDS_NR.Delete();
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TreatyEmpView_Delete";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@caseno", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Replace("-", "")));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> DeleteCase();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }
}