﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="tip_modify.aspx.cs" Inherits="Treaty_manager_tip_modify" ValidateRequest="false" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link type="text/css" rel="stylesheet" href="../css/myITRIproject/jquery-ui.css" />
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
    <link href="../css/colorbox.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="../scripts/jquery-3.2.1.js"></script>
    <script src="../scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("更新成功!");
            parent.$.fn.colorbox.close();
        }

        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            $("#txt_doc").val(compare);
        }

        tinymce.init({
            selector: '#TB_content',
            width: "100%",
            height: "500",
            language: 'zh_TW',
            fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 24pt 36pt",
            toolbar: "insertfile undo redo | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor | fontsizeselect",
            statusbar: false,
            plugins: [' code', 'textcolor'],
        });
    </script>
</head>
<body style="background: none;">
    <form id="form1" runat="server">
        <div class="stripeMe font-normal">
            <table style="margin-left: 15px; margin-top: 25px;width:98%">
                <tr style="display: none">
                    <td colspan="4">ID：<asp:Label ID="LB_id" runat="server" Text=''></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: right">Type</td>
                    <td style="width:100px;" nowrap="nowrap">
                        <asp:DropDownList ID="DDL_type" runat="server" OnSelectedIndexChanged="DDL_type_SelectedIndexChanged" AutoPostBack="true">
                            <asp:ListItem Value="X">X</asp:ListItem>
                            <asp:ListItem Value="x">x</asp:ListItem>
                            <asp:ListItem Value="">其他</asp:ListItem>
                        </asp:DropDownList>
                        <asp:TextBox ID="TB_class" runat="server" Width="30" OnTextChanged="TB_class_TextChanged" AutoPostBack="true"></asp:TextBox>
                    </td>
                    <td style="text-align: right;width:50px">代碼</td>
                    <td>
                        <asp:DropDownList ID="DDL_code" runat="server">
                        </asp:DropDownList>
                    </td>
                </tr>

                <tr>
                    <td style="text-align: right">標題</td>
                    <td colspan="3">
                        <asp:TextBox ID="TB_title" runat="server" Text='' Width="600px"></asp:TextBox></td>
                </tr>
                <tr>

                    <td class="td_right">上傳檔案</td>
                    <td colspan="3">
                        <asp:FileUpload ID="FileUpload1" runat="server" onpropertychange="TransferData(this.value);" />
                        &emsp;
                        <asp:Button ID="btnFileUpload" runat="server" Text="上傳" CssClass="btn btn-outline-primary btn-sm" OnClick="btnFileUpload_Click" />
                        <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" Width="98%">
                            <Columns>
                                <asp:TemplateField HeaderText="功能">
                                    <ItemTemplate>
                                        <asp:Label ID="lbl_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tdf_id").ToString()) %>' Visible="false"></asp:Label>
                                        <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tdf_id") %>' ForeColor="Red" OnClick="lnkbtn_Del_Click">刪除</asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="附件名稱">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tdf_filename").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tdf_id") %>'> </asp:LinkButton>
                                    </ItemTemplate>
                                    <HeaderStyle Width="550px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Left" />
                                </asp:TemplateField>

                                <%--<asp:TemplateField HeaderText="上傳者">
                                    <ItemTemplate>
                                        <asp:Label ID="lbl_4" runat="server" Text='<%# Server.HtmlEncode(Eval("tdf_up_empname").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="50px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="上傳日期">
                                    <ItemTemplate>
                                        <asp:Label ID="lbl_1" runat="server" Text='<%# Server.HtmlEncode(Eval("tdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <HeaderStyle Width="100px"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center" />
                                </asp:TemplateField>--%>
                            </Columns>
                            <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                            <PagerSettings Position="Bottom" />
                            <PagerStyle HorizontalAlign="Left" />
                        </asp:GridView>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: right">內文</td>
                    <td colspan="3">
                        <asp:TextBox ID="TB_content" runat="server" Text='' TextMode="MultiLine" Width="650px" Height="280px"></asp:TextBox></td>
                </tr>
                <tr>
                    <td colspan="4" style="text-align: right">
                        <asp:Button ID="BT_save" runat="server" Text="存檔" OnClick="BT_save_Click" />
                    </td>
                </tr>
            </table>

        </div>
    </form>
</body>
</html>
