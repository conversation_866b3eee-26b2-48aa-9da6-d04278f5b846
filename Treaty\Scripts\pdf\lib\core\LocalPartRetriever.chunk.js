/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[11],{386:function(ia,y,e){e.r(y);var fa=e(1),x=e(2),ha=e(132);ia=e(84);var ea=e(216);e=e(316);var da=window;ia=function(e){function w(w,r,h){r=e.call(this,w,r,h)||this;if(w.name&&"xod"!==w.name.toLowerCase().split(".").pop())throw Error("Not an XOD file");if(!da.FileReader||!da.File||!da.Blob)throw Error("File API is not supported in this browser");r.file=w;r.ry=[];r.dE=0;return r}Object(fa.c)(w,e);w.prototype.pG=function(e,r,h){var f=
this,n=new FileReader;n.onloadend=function(e){if(0<f.ry.length){var w=f.ry.shift();w.haa.readAsBinaryString(w.file)}else f.dE--;if(n.error){e=n.error;if(e.code===e.ABORT_ERR){Object(x.j)("Request for chunk "+r.start+"-"+r.stop+" was aborted");return}return h(e)}if(e=n.content||e.target.result)return h(!1,e);Object(x.j)("No data was returned from FileReader.")};r&&(e=(e.slice||e.webkitSlice||e.mozSlice||e.Tha).call(e,r.start,r.stop));0===f.ry.length&&50>f.dE?(n.readAsBinaryString(e),f.dE++):f.ry.push({haa:n,
file:e});return function(){n.abort()}};w.prototype.hr=function(e){var r=this;r.ny=!0;var h=ha.a;r.pG(r.file,{start:-h,stop:r.file.size},function(f,n){if(f)return Object(x.j)("Error loading end header: %s "+f),e(f);if(n.length!==h)throw Error("Zip end header data is wrong size!");r.wd=new ea.a(n);var w=r.wd.oO();r.pG(r.file,w,function(f,h){if(f)return Object(x.j)("Error loading central directory: %s "+f),e(f);if(h.length!==w.stop-w.start)throw Error("Zip central directory data is wrong size!");r.wd.PR(h);
r.QD=!0;r.ny=!1;return e(!1)})})};w.prototype.lH=function(e,r){var h=this,f=h.fh[e];if(h.wd.EM(e)){var n=h.wd.gu(e),w=h.pG(h.file,n,function(f,w){delete h.fh[e];if(f)return Object(x.j)('Error loading part "%s": %s, '+e+", "+f),r(f);if(w.length!==n.stop-n.start)throw Error("Part data is wrong size!");r(!1,e,w,h.wd.FP(e))});f.fU=!0;f.cancel=w}else r(Error('File not found: "'+e+'"'),e)};return w}(ia.a);Object(e.a)(ia);Object(e.b)(ia);y["default"]=ia}}]);}).call(this || window)
