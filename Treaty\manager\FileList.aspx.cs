﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class FileList : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {

    }

    protected void BT_copy_Click(object sender, EventArgs e)
    {

        string str_FileName = TB_file.Text.Replace("/", "").Replace(".....", ".").Replace("....", ".").Replace("...", ".").Replace("..", ".").Replace("&amp;", "＆");
        str_FileName = TB_file.Text.Replace("&", "＆");
        try
        {
            File.Copy(TB_file.Text, str_FileName, true);
            LK_DL.Text = str_FileName;
        }
        catch (IOException iox)
        {
            LK_DL.Text = iox.ToString()+ " error!!";
        }
    }

    protected void LK_DL_Click(object sender, EventArgs e)
    {

        FileInfo fi = new FileInfo(TB_file.Text);
        if (fi.Exists)
        {
            //LK_DL.Text = "有檔案!";
            Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(TB_file.Text, Encoding.UTF8));
            Response.WriteFile(TB_file.Text);
            Response.Flush();
            Response.End();
        }
        if (File.Exists(TB_file.Text.Replace("//", "/")))
        {
            Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(TB_file.Text, Encoding.UTF8));
            Response.WriteFile(LK_DL.Text);
            Response.Flush();
            Response.End();           
        }
        else
        {
            LK_DL.Text = LK_DL.Text+"無法下載!";
        }
    }

    protected void BT_read_Click(object sender, EventArgs e)
    {

        String[] FileCollection;
        String FilePath = "\\\\nas-2\\Intraweb\\up_ap9-amps\\treaty\\2023\\";
        //String FilePath = "D:\\System_develop\\AMPS\\Program\\Treaty\\2014\\Treaty\\manager";
        FileCollection = Directory.GetFiles(FilePath, "*.*");

        List<string> myList = new List<string>();
 

        // 取得資料夾內所有檔案
        foreach (string fname in System.IO.Directory.GetFiles(FilePath))
        {
            string line;

            // 一次讀取一行
            System.IO.StreamReader file = new System.IO.StreamReader(fname);
            if ((line = file.ReadLine()) != null)
            {

                LT_list.Text = LT_list.Text + fname.Replace(FilePath+"\\","") + "<br>";
                myList.Add(line.Trim());
            }

            file.Close();
        }
    }     
    
 
}