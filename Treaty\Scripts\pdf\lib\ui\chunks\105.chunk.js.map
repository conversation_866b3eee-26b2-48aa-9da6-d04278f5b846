{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/pt.js"], "names": ["module", "exports", "e", "o", "default", "a", "t", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "yearStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,iFAAiFC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,2FAA2FH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,EAAE,KAAKa,UAAU,EAAEC,UAAU,EAAEC,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,mCAAmCC,KAAK,0CAA0CC,aAAa,CAACC,OAAO,QAAQC,KAAK,QAAQC,EAAE,kBAAkBC,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,WAAWC,EAAE,SAASC,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,YAAY,OAAOlC,EAAEC,QAAQkC,OAAOhC,EAAE,MAAK,GAAIA,EAAjmCD,CAAE,EAAQ", "file": "chunks/105.chunk.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_pt=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var o=a(e),t={name:\"pt\",weekdays:\"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado\".split(\"_\"),weekdaysShort:\"dom_seg_ter_qua_qui_sex_sab\".split(\"_\"),weekdaysMin:\"Do_2ª_3ª_4ª_5ª_6ª_Sa\".split(\"_\"),months:\"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro\".split(\"_\"),monthsShort:\"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez\".split(\"_\"),ordinal:function(e){return e+\"º\"},weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY [às] HH:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY [às] HH:mm\"},relativeTime:{future:\"em %s\",past:\"há %s\",s:\"alguns segundos\",m:\"um minuto\",mm:\"%d minutos\",h:\"uma hora\",hh:\"%d horas\",d:\"um dia\",dd:\"%d dias\",M:\"um mês\",MM:\"%d meses\",y:\"um ano\",yy:\"%d anos\"}};return o.default.locale(t,null,!0),t}));"], "sourceRoot": ""}