﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase_cop : Treaty.common
{    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r;
        try
        {
            r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        }
        catch
        {
            return false;
        }
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                ViewState["empNo"] = ssoUser.empNo;
                ViewState["empName"] = ssoUser.empName;
                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
                //SDS_SC.SelectParameters.Clear();
                //SDS_SC.SelectCommandType = SqlDataSourceCommandType.Text;
                //SDS_SC.SelectCommand = " select * from treaty_case where tc_seno = @sn ";
                //SDS_SC.SelectParameters.Add("sn", TypeCode.String, ViewState["seno"].ToString());
                //SDS_SC.DataBind();
                //System.Data.DataView dv = (DataView)SDS_SC.Select(new DataSourceSelectArguments());

                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"select * from treaty_case where tc_seno = @sn ";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();

                    sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv = dt.DefaultView;
                if (dv.Count >= 1)
                {
                    TB_empNo.Text = Server.HtmlEncode(dv[0]["tc_cop_empno"].ToString().Trim());
                    TB_empName.Text = Server.HtmlEncode(dv[0]["tc_cop_name"].ToString().Trim());
                }
            }
            else
            {

            }
            databinding();
            BindData_cop();
        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (!IsNatural_Number(DDL_cop.SelectedValue) || (DDL_cop.SelectedValue.Length == 0) || (DDL_cop.SelectedValue.Length > 7))
            Response.Redirect("../danger.aspx");
        
        if (TB_empNo.Text != "")
        {
            TB_empNo.Text = Server.HtmlEncode((TB_empNo.Text + DDL_cop.SelectedValue + ",").Replace(",,", ","));
            TB_empName.Text = Server.HtmlEncode((TB_empName.Text + DDL_cop.SelectedItem.Text + ",").Replace(",,", ","));
        }
        else
        {
            TB_empNo.Text = Server.HtmlEncode(DDL_cop.SelectedValue) + ",";
            TB_empName.Text = Server.HtmlEncode(DDL_cop.SelectedItem.Text) + ",";
        }
        //SDS_cop.UpdateCommandType = SqlDataSourceCommandType.Text;
        //SDS_cop.UpdateCommand = " update  treaty_case  set tc_cop_empno=@emp,tc_cop_name=@eName ,tc_modify_emp_no=@empNo ,tc_modify_emp_name=@empName,tc_modify_date=@modify_date  where tc_seno = @sn ";
        //SDS_cop.UpdateParameters.Add("sn", ViewState["seno"].ToString());
        //SDS_cop.UpdateParameters.Add("emp", TB_empNo.Text.Replace(",,", ","));
        //SDS_cop.UpdateParameters.Add("eName", TB_empName.Text.Replace(",,", ","));
        //SDS_cop.UpdateParameters.Add("empNo", ViewState["empNo"].ToString());
        //SDS_cop.UpdateParameters.Add("empName", ViewState["empName"].ToString());
        //SDS_cop.UpdateParameters.Add("modify_date", DateTime.Now.ToString("yyyyMMdd"));
        //SDS_cop.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"update  treaty_case  set tc_cop_empno=@emp,tc_cop_name=@eName ,tc_modify_emp_no=@empNo ,tc_modify_emp_name=@empName,tc_modify_date=@modify_date  where tc_seno = @sn ";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@emp", oRCM.SQLInjectionReplaceAll(TB_empNo.Text.Trim().Replace(",,", ",")));
            sqlCmd.Parameters.AddWithValue("@eName", oRCM.SQLInjectionReplaceAll(TB_empName.Text.Trim().Replace(",,", ",")));
            sqlCmd.Parameters.AddWithValue("@empNo", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ViewState["empName"].ToString()));
            sqlCmd.Parameters.AddWithValue("@modify_date", oRCM.SQLInjectionReplaceAll(DateTime.Now.ToString("yyyyMMdd"))); ;


            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        Treaty_log(ViewState["seno"].ToString(), "協同承辦維護", "", ViewState["xIP"].ToString(), "treaty\\TreatyCase_cop.aspx");

        if (TB_empNo.Text == ",") TB_empNo.Text = "";
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }
    private void BindData_cop()
    {
        //this.SDS_cop.SelectParameters.Clear();
        //this.SDS_cop.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_cop.SelectCommand = "esp_TreatyCase_cop_List";
        //this.SDS_cop.SelectParameters.Add("seno", ViewState["seno"].ToString());
        //this.SDS_cop.DataBind();
        //SGV_cop.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_cop_List";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_cop.DataSource = dt;
                SGV_cop.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void databinding()
    {
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.Text;
        //this.SDS_SC.SelectCommand = "select rtrim(emp_no) empNo  ,rtrim(emp_name) empName  from  treaty_buztbl order by emp_group";
        //this.SDS_SC.DataBind();
        //DDL_cop.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select rtrim(emp_no) empNo  ,rtrim(emp_name) empName  from  treaty_buztbl order by emp_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_cop.DataSource = dt;
                DDL_cop.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_cop_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void SGV_cop_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            TB_empNo.Text = Server.HtmlEncode(TB_empNo.Text.Replace(arg[0].Trim() + ",", "").Replace(",,", ","));
            if (TB_empNo.Text == ",") TB_empNo.Text = "";

            TB_empName.Text = Server.HtmlEncode(TB_empName.Text.Replace(arg[1].Trim() + ",", "").Replace(",,", ","));
            if (TB_empName.Text == ",") TB_empName.Text = "";

            //SDS_cop.UpdateParameters.Clear();
            //SDS_cop.UpdateCommandType = SqlDataSourceCommandType.Text;
            //SDS_cop.UpdateCommand = " update  treaty_case  set tc_cop_empno=@emp,tc_cop_name=@eName ,tc_modify_emp_no=@empNo ,tc_modify_emp_name=@empName,tc_modify_date=@modify_date  where tc_seno = @sn ";
            //SDS_cop.UpdateParameters.Add("sn", ViewState["seno"].ToString());
            //SDS_cop.UpdateParameters.Add("emp", TB_empNo.Text.Replace(",,", ","));
            //SDS_cop.UpdateParameters.Add("eName", TB_empName.Text.Replace(",,", ","));
            //SDS_cop.UpdateParameters.Add("empNo", ViewState["empNo"].ToString());
            //SDS_cop.UpdateParameters.Add("empName", ViewState["empName"].ToString());
            //SDS_cop.UpdateParameters.Add("modify_date", DateTime.Now.ToString("yyyyMMdd"));
            //SDS_cop.Update();
           

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"update  treaty_case  set tc_cop_empno=@emp,tc_cop_name=@eName ,tc_modify_emp_no=@empNo ,tc_modify_emp_name=@empName,tc_modify_date=@modify_date  where tc_seno = @sn ";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@emp", oRCM.SQLInjectionReplaceAll(TB_empNo.Text.Trim().Replace(",,", ",")));
                sqlCmd.Parameters.AddWithValue("@eName", oRCM.SQLInjectionReplaceAll(TB_empName.Text.Trim().Replace(",,", ",")));
                sqlCmd.Parameters.AddWithValue("@empNo", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ViewState["empName"].ToString()));
                sqlCmd.Parameters.AddWithValue("@modify_date", oRCM.SQLInjectionReplaceAll(DateTime.Now.ToString("yyyyMMdd")));


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindData_cop();
        } 
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

}