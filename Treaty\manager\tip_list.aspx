﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="tip_list.aspx.cs" Inherits="Treaty_manager_tip_list" ValidateRequest="false" %>

<!DOCTYPE html>
<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot_tech.ascx" TagName="Foot" TagPrefix="uc2" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript">

        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function Tip(tid, type) {
            $(".ajax-addMore").colorbox({
                href: "./tip_modify.aspx?tid=" + tid + "&mod=" + type,
                iframe: true, width: "90%", height: "90%", transition: "none", opacity: "0.5", overlayClose: false,
                onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("renew", 0);
                }
            });
        }
        $(document).ready(function () {
            $('a.iterm_dymanic').cluetip({
                width: '800px', cluetipClass: 'jtip',
                ajaxCache: false,
                sticky: true,
                closePosition: 'title',
                closeText: '<img src="../Scripts/cluetip/images/cross.png" alt="close" />'
            });
        });

    </script>
    <style type="text/css">
        .cluetip-inner {
            width: 98%
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="WrapperBody">
            <div class="WrapperContent">

                <div class="WrapperHeader fixwidth Pops">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <%--<asp:Literal ID="lb_Subtitle" runat="server" />--%>
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="fixwidth">
                                <asp:DropDownList ID="DDL_type" runat="server" AutoPostBack="True" OnSelectedIndexChanged="DDL_typw_SelectedIndexChanged">
                                    <asp:ListItem Value="-">全部</asp:ListItem>
                                    <asp:ListItem Value="X">單位</asp:ListItem>
                                    <asp:ListItem Value="x">C組</asp:ListItem>
                                </asp:DropDownList>
                                <asp:Button ID="BT_addMore" runat="server" Class="genbtnS ajax-addMore" Text="Tip新增" OnClientClick="Tip(0,'Add') " />
                                <span class="stripeMe font-normal">
                                    <cc1:SmartGridView ID="sgvList" BorderWidth="0px" CellPadding="0" AllowPaging="True" runat="server" AutoGenerateColumns="False" Width="100%" OnPageIndexChanging="sgvList_PageIndexChanging" OnRowDataBound="sgvList_RowDataBound" OnRowCommand="sgvList_RowCommand" AllowSorting="True" OnSorting="sgvList_Sorting">
                                        <Columns>
                                            <asp:TemplateField HeaderText="功能">
                                                <ItemTemplate>
                                                    <asp:ImageButton ID="IB_del" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-delete.png" CommandName="Del" CommandArgument='<%# Eval("tip_id") %>' />
                                                    <asp:ImageButton ID="IB_mod" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-edit.png" class="ajax-addMore" />
                                                    <asp:Literal ID="LT_view" runat="server" />
                                                </ItemTemplate>
                                                <HeaderStyle Width="80px"></HeaderStyle>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="編碼">
                                                <HeaderStyle Width="30px"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="left" />
                                                <ItemTemplate><%#Eval("tip_id")%></ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="類別">
                                                <HeaderStyle Width="30px"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="center" />
                                                <ItemTemplate><%#Eval("tip_type")%></ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="標題" SortExpression="tip_title">
                                                <HeaderStyle Width="110px"></HeaderStyle>
                                                <ItemStyle HorizontalAlign="Left" />
                                                <ItemTemplate><%#Eval("tip_title")%></ItemTemplate>
                                            </asp:TemplateField>
                                            <asp:TemplateField HeaderText="內文">
                                                <ItemStyle HorizontalAlign="Left" />
                                                <ItemTemplate><%#Eval("tip_content")%></ItemTemplate>
                                            </asp:TemplateField>
                                        </Columns>
                                        <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span style='color:#578c27'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#578c27'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#578c27'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#578c27'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#578c27'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#578c27'>頁</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                                        <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Left" CssClass="PagerStyle" />
                                        <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="~/images/first.gif" FirstPageText="第一頁"
                                            LastPageImageUrl="~/images/last.gif" LastPageText="最後一頁" NextPageImageUrl="~/images/next.gif"
                                            NextPageText="下一頁" PreviousPageImageUrl="~/images/previous.gif" PreviousPageText="上一頁" />
                                        <EmptyDataTemplate>
                                            <div style="text-align: center;">無符合資料</div>
                                        </EmptyDataTemplate>
                                    </cc1:SmartGridView>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <uc2:Foot ID="Foot1" runat="server" />
    </form>
</body>
</html>
