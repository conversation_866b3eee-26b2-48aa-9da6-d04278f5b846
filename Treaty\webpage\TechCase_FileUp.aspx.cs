﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TechCase_FileUp : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }
            Bind_Auth();

            Bind_FileType();
        }
    }

    public void Tech_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        return;
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (txt_filetxt.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];// + "\\TechCase"; 
        string path = oRCM.GetValidPathPart(FilePathString, "TechCase"); // string.Format("{0}\\{1}\\", FilePathString, ViewState["tt_seno"].ToString());
        path = oRCM.GetValidPathPart(path, DateTime.Now.Year.ToString());

        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);
        if (Request.ServerVariables["HTTP_VIA"] != null)
        {
            ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
        }
        if (FU_up.PostedFile.ContentLength > 0)
        {
            string upFileName = FU_up.FileName.Replace("&", "＆").Replace("­", "－");
            string str_FileName = "\\DR_" + ViewState["tt_seno"].ToString() + "_" + strPreRandom + "_" +
                                    Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("--", "－－") +
                                    Path.GetExtension(upFileName);

            FU_up.SaveAs(path.Replace("/", "").Replace("..", "") + oRCM.SQLInjectionReplaceAll(str_FileName));

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@mode", "file_Add");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
                //sqlCmd.Parameters.AddWithValue("@tcdf_type", "");
                sqlCmd.Parameters.AddWithValue("@file_url", oRCM.SQLInjectionReplaceAll(path.Replace("/", "").Replace("..", "") + str_FileName));
                sqlCmd.Parameters.AddWithValue("@inspect", oRCM.SQLInjectionReplaceAll(IIf(CB_inspect.Checked, "1", "0")));
                sqlCmd.Parameters.AddWithValue("@fd_name", oRCM.SQLInjectionReplaceAll(upFileName.Replace("--", "－－")));
                sqlCmd.Parameters.AddWithValue("@filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                sqlCmd.Parameters.AddWithValue("@filetype", oRCM.SQLInjectionReplaceAll(DDL_FileType.SelectedValue));

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            Tech_log(ViewState["tt_seno"].ToString(), "附件上傳", "", "", "FU_up.FileName");

            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

        }

    }

    private void Bind_Auth()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@mode", "Auth");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            string str_auth = dv_auth[0]["RW"].ToString();
            if (str_auth == "X")
                Response.Redirect("../NoAuthRight.aspx");
            ViewState["auth"] = Server.HtmlEncode(dv_auth[0]["RW"].ToString());
            ViewState["SYS"] = Server.HtmlEncode(dv_auth[0]["Role"].ToString());
            ViewState["Role2"] = Server.HtmlEncode(dv_auth[0]["Role2"].ToString());
            ViewState["分案權限"] = Server.HtmlEncode(dv_auth[0]["分案權限"].ToString());
        }
    }

    private void Bind_FileType()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            string Role2 = ViewState["Role2"].ToString();
            if (Role2 == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "file_type");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "file_type_x");

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
                DDL_FileType.DataSource = dt;
                DDL_FileType.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
}