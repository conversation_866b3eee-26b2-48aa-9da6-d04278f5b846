﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Linq;

public partial class TechCase_modify : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }

    public static string RemoveHTMLTag(string htmlSource)
    {
        //移除  javascript code.
        htmlSource = Regex.Replace(htmlSource, @"<script[\d\D]*?>[\d\D]*?</script>", String.Empty);

        //移除html tag.
        htmlSource = Regex.Replace(htmlSource, @"<[^>]*>", String.Empty);
        htmlSource = htmlSource.Replace("&nbsp;", " ");
        return htmlSource;
    }

    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }

    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {
            case "N":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視NDA資訊";
                break;
            case "F":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
                break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視標案資訊";
                break;
            case "C":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
                break;
            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion

    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;
        lnkbtn_Engage.Text = GetEngageNDAText(strCaseNo);
        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            //case "U":
            //    bResult = true;
            //    break;
            case "R":
                bResult = true;
                break;
            case "F":
                bResult = true;
                break;
            case "C":
                bResult = true;
                break;
            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion

    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    protected void lnkbtn_Engage_Click(object sender, EventArgs e)
    {
        string strCaseNo = txt_ComplexNo.Text.Trim();
        //string strCaseNo_C = txtOldContno.Text.Trim();
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
        string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();
        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案/Engage/Base/caseBase.aspx?contno=xxxxx
                strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
                break;

            case "A": // 國外契約   
                strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                break;

            case "F": // 國內契約  
                strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
                break;
                //case "C": // 國外契約
                //    strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                //    break;
        }

        script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);


    }
    #endregion

    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;

        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            if (Request["tt_seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["tt_seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request["tt_seno"];
            }
            if (ViewState["tt_seno"] == null)
                Response.Redirect("../NoAuthRight.aspx");

            Bind_Auth();

            Tech_log(Server.HtmlEncode(ViewState["tt_seno"].ToString()), Server.HtmlEncode("維護承辦單"), "", Server.HtmlEncode(ViewState["tt_seno"].ToString()), "treaty\\TechCase_modify.aspx");

            Bind_Light();

            Bind_Data();

            Bind_Doc_File();

            Bind_Inspect();


            lnkbtn_Reject.Attributes.Add("onclick", "Reject_Case('" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "','X');");
            lnkbtn_RejectC.Attributes.Add("onclick", "Reject_Case('" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "','C');");

            //案號
            txt_ComplexNo.Attributes.Add("readOnly", "readonly");
            //需求單位及部門
            txt_OrgAbbrName.Attributes.Add("readOnly", "readonly");
            txt_req_dept.Attributes.Add("readOnly", "readonly");
            //新增審查人
            btn_AddInspect.Attributes.Add("onclick", "Add_Inspect(" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + ");");
            //送出審查
            btn_SendInspect.Attributes.Add("onclick", "return  confirm('確定要審查 ?');");
            //結案通知
            btn_End.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
            //檔案上傳
            btn_FileUp.Attributes.Add("onclick", "tech_fileup('" + txt_ComplexNo.Text.Replace("-", "") + "'," + ViewState["tt_seno"].ToString() + ");");

            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
        }
        else
        {
            Bind_sRC_init(Plh_Dynax_sRC, "X");
            Bind_sRC_init(Plh_Dynax_sRC_x, "");
        }

        ViewState["IsNewType"] = false;

        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            Bind_Doc_File();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Inspect_renew")
        {
            Bind_Inspect();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Dispatch_Case")
        {
            if (hiReject.Value == "Y")
            {
                //Do_Modify_Temp();

                ////報院條件       
                //Save_sRC(Plh_Dynax_sRC, "X");
                //Save_sRC(Plh_Dynax_sRC_x, "");

                string script = "<script language='javascript'>location.href='./TechCase_View.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }

    private void BindData_Customer()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "req_company");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Bind_Data()
    {
        DataTable dt = Case_View();
        DataView dv = dt.DefaultView;
        if (dv.Count == 0)
        {
            Response.Redirect("../danger.aspx");
        }

        string str_tr_year = Server.HtmlEncode(dv[0]["tt_year"].ToString().Trim());
        string str_tr_orgcd = Server.HtmlEncode(dv[0]["tt_orgcd"].ToString().Trim());
        string str_tr_class = Server.HtmlEncode(dv[0]["tt_class"].ToString().Trim());
        ViewState["tr_class"] = str_tr_class;
        ViewState["tt_ver"] = Server.HtmlEncode(dv[0]["tt_ver"].ToString().Trim());
        ViewState["tt_degree"] = Server.HtmlEncode(dv[0]["tt_degree"].ToString().Trim());
        ViewState["tt_status"] = Server.HtmlEncode(dv[0]["tt_status"].ToString().Trim());
        ViewState["tc_seno"] = Server.HtmlEncode(dv[0]["tc_seno"].ToString().Trim());
        LT_法務承辦人.Text = Server.HtmlEncode(dv[0]["法務人員"].ToString().Trim());

        string str_tr_sn = Server.HtmlEncode(dv[0]["tt_sn"].ToString().Trim());
        string str_tr_ver = Server.HtmlEncode(dv[0]["tt_ver"].ToString().Trim());
        string str_tr_seqsn = Server.HtmlEncode(dv[0]["tt_seqsn"].ToString().Trim());
        ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
        txt_ComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
        string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;
        lnkbtn_Engage.Visible = GetEngageNDAVisible(str_actcontno);
        //string tt_handle_empno = dv[0]["tt_handle_empno"].ToString().Trim();
        txt_betsum.Text = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tt_betsum"].ToString().Trim()));//    Server.HtmlEncode(dv[0]["tc_betsum"].ToString().Trim());

        lb_assign_name.Text = Server.HtmlEncode(dv[0]["tt_assign_name"].ToString());//分案主管
        lb_assign_date.Text = Server.HtmlEncode(dv[0]["tt_assign_datetime"].ToString().Length > 0 ? DateTime.Parse(dv[0]["tt_assign_datetime"].ToString().Trim()).ToString("yyyy/MM/dd") : "");  //分案日期
        lb_handle_name.Text = Server.HtmlEncode(dv[0]["tt_handle_name"].ToString());//法務承辦人姓名
        lb_handle_empno.Text = Server.HtmlEncode(dv[0]["tt_handle_empno"].ToString());//法務承辦人工號
        lb_handle_ext.Text = Server.HtmlEncode(dv[0]["tt_handle_ext"].ToString());//法務承辦人分機
        //承辦人姓名c
        lb_handle_c_name.Text = Server.HtmlEncode(dv[0]["tt_handle_c_name"].ToString());
        //承辦人工號c
        lb_handle_c_empno.Text = Server.HtmlEncode(dv[0]["tt_handle_c_empno"].ToString());
        //承辦人分機c
        lb_handle_c_ext.Text = Server.HtmlEncode(dv[0]["tt_handle_c_ext"].ToString());

        #region 案件性質     

        #region N
        if (dv[0]["tt_conttype_b0"].ToString().Trim() == "1")
            cb_conttype_b0.Checked = true;

        if (dv[0]["tt_conttype_b1"].ToString().Trim() == "1")
            cb_conttype_b1.Checked = true;
        else
            cb_conttype_b1.Checked = false;
        if (dv[0]["tt_conttype_d4"].ToString().Trim() == "1")
            cb_conttype_d4.Checked = true;

        if (dv[0]["tt_conttype_d5"].ToString().Trim() == "1")
            cb_conttype_d5.Checked = true;
        else
            cb_conttype_d5.Checked = false;
        if (dv[0]["tt_conttype_d7"].ToString().Trim() == "1")
            cb_conttype_d7.Checked = true;

        if (dv[0]["tt_conttype_ns"].ToString().Trim() == "1")
            cb_conttype_ns.Checked = true;
        #endregion

        if (dv[0]["tt_技術授權"].ToString().Trim() == "1")
            chk_技術授權.Checked = true;

        if (dv[0]["tt_專利授權"].ToString().Trim() == "1")
            chk_專利授權.Checked = true;

        if (dv[0]["tt_技術與專利授權"].ToString().Trim() == "1")
            chk_技術與專利授權.Checked = true;

        if (dv[0]["tt_特定區域"].ToString().Trim() == "1")
            chk_特定區域.Checked = true;

        if (dv[0]["tt_技術讓與"].ToString().Trim() == "1")
            chk_技術讓與.Checked = true;

        if (dv[0]["tt_修約"].ToString().Trim() == "1")
            chk_修約.Checked = true;

        if (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6")
        {
            cb_conttype_b0.Enabled = true;
            cb_conttype_b1.Enabled = true;
            cb_conttype_d4.Enabled = true;
            cb_conttype_d5.Enabled = true;
            cb_conttype_d7.Enabled = true;
            chk_技術讓與.Enabled = true;
            cb_conttype_ns.Enabled = true;
            chk_技術授權.Enabled = true;
            chk_專利授權.Enabled = true;
            chk_技術與專利授權.Enabled = true;
            chk_特定區域.Enabled = true;
            chk_修約.Enabled = true;
        }
        #endregion

        if (dv[0]["第三方鑑價報告"].ToString().Trim() == "1")
        {
            chk_第三方鑑價報告.Checked = true;
        }

        //案件燈號
        if (dv[0]["案件燈號"].ToString() != "")
            rbl_Light.SelectedValue = Server.HtmlEncode(dv[0]["案件燈號"].ToString());
        //案件合理性評估
        if (dv[0]["tt_sRC_relation"].ToString() != "")
            rbl_relation.SelectedValue = Server.HtmlEncode(dv[0]["tt_sRC_relation"].ToString());
        else
            rbl_relation.SelectedValue = "";

        txt_燈號_說明.Text = Server.HtmlEncode(dv[0]["tt_燈號_說明"].ToString());
        txt_燈號_說明.Visible = rbl_Light.SelectedValue == "9";

        #region 經濟部成果歸屬運用辦法第18條
        if (dv[0]["成果歸屬運用辦法"].ToString().Trim() == "1")
        {
            chk_成果歸屬運用辦法.Checked = true;
        }

        chk_公益目的.Checked = Server.HtmlEncode(dv[0]["公益目的"].ToString()) == "1";
        chk_促進整體產業發展.Checked = Server.HtmlEncode(dv[0]["促進整體產業發展"].ToString()) == "1";
        chk_提升研發成果運用效益.Checked = Server.HtmlEncode(dv[0]["提升研發成果運用效益"].ToString()) == "1";

        chk_成果歸屬運用辦法_CheckedChanged(chk_成果歸屬運用辦法, EventArgs.Empty);
        #endregion

        lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tt_modify_empname"].ToString());//修改人
        lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tt_modify_enpmo"].ToString());//修改工號
        lb_modify_date.Text = Server.HtmlEncode(dv[0]["tt_modify_date"].ToString().Trim().Length > 0 ? DateTime.Parse(dv[0]["tt_modify_date"].ToString().Trim()).ToString("yyyy/MM/dd") : ""); //修改日期
        txt_ManageNote.Text = Server.HtmlEncode(dv[0]["tt_remark"].ToString().Trim());
        if ((ViewState["tt_degree"].ToString() == "5" && ViewState["Role2"].ToString() == "X") ||
            (ViewState["tt_status"].ToString() == "5" && ViewState["Role2"].ToString() == "x"))
        {
            btn_Save.Attributes["onclick"] = @"if (!confirm('一旦正式存檔後,就無法修改( 由承辦人 審核) 確定?')) { return false; }";
        }
        if (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6")
        {
            btn_Save.Attributes["onclick"] = @"if (!confirm('一旦正式存檔後,就無法修改 (送出審核) 確定?')) { return false; }";
            PL_Light.Visible = true;
        }

        #region 判斷編輯
        if ((ViewState["tt_degree"].ToString() == "0" && ViewState["Role"].ToString() == "ADM") ||
            (ViewState["tt_degree"].ToString() == "5" && ViewState["Role2"].ToString() == "X" &&
            ViewState["empno"].ToString() == dv[0]["tt_promoter_no"].ToString().Trim() || ViewState["Role"].ToString() == "ADM") ||
            (ViewState["tt_status"].ToString() == "5" && ViewState["Role2"].ToString() == "x" &&
            ViewState["empno"].ToString() == dv[0]["tt_handle_c_empno"].ToString().Trim() || ViewState["Role"].ToString() == "ADM") ||
            ViewState["tt_degree"].ToString() == "6" &&//計價完成
           (ViewState["Role"].ToString() == "ADM") || (dv[0]["tt_handle_empno"].ToString().Trim() == ViewState["empno"].ToString()))
        {

        }
        else
        {
            string script = "<script language='javascript'>alert('無編輯權限！');location.href='./TechCase_View.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "NoAuthRight", script);
        }
        #endregion

        #region 退洽案承辦
        if (dv[0]["tt_handle_empno"].ToString().Trim() == ViewState["empno"].ToString() || ViewState["Role"].ToString() == "ADM")
        {
            if (ViewState["tt_degree"].ToString() == "6")
            {
                lnkbtn_Reject.Visible = true;
            }
            if (ViewState["tt_status"].ToString() == "6")
            {
                lnkbtn_RejectC.Visible = true;
            }
        }
        #endregion       

        #region 報院說明事項
        Plh_Dynax_sRC.Visible = false;
        Plh_Dynax_sRC_x.Visible = false;
        if (ViewState["tt_degree"].ToString() == "0" && ViewState["Role"].ToString() == "ADM")
        {
            Plh_Dynax_sRC.Visible = true;
            Plh_Dynax_sRC_x.Visible = true;
            Bind_sRC(Plh_Dynax_sRC, "X");
            Bind_sRC(Plh_Dynax_sRC_x, "");
        }
        else if (ViewState["tt_degree"].ToString() == "5" &&
            ViewState["Role2"].ToString() == "X" &&
            ViewState["empno"].ToString() == dv[0]["tt_promoter_no"].ToString().Trim())
        {
            Plh_Dynax_sRC.Visible = true;
            Bind_sRC(Plh_Dynax_sRC, "X");
        }
        else if (ViewState["tt_status"].ToString() == "5" &&
            ViewState["Role2"].ToString() == "x" &&
            ViewState["empno"].ToString() == dv[0]["tt_handle_c_empno"].ToString().Trim())
        {
            Plh_Dynax_sRC_x.Visible = true;
            Bind_sRC(Plh_Dynax_sRC_x, "");
        }
        else if (ViewState["tt_degree"].ToString() == "6" && dv[0]["tt_handle_empno"].ToString().Trim() == ViewState["empno"].ToString())
        {
            Plh_Dynax_sRC.Visible = true;
            Plh_Dynax_sRC_x.Visible = true;
            Bind_sRC(Plh_Dynax_sRC, "X");
            Bind_sRC(Plh_Dynax_sRC_x, "");
        }
        else if (ViewState["Role"].ToString() == "ADM")
        {
            Plh_Dynax_sRC.Visible = true;
            Plh_Dynax_sRC_x.Visible = true;
            Bind_sRC(Plh_Dynax_sRC, "X");
            Bind_sRC(Plh_Dynax_sRC_x, "");
        }

        #endregion      

        #region 需求單位及部門

        #region --- query ---
        DataTable dt_emp = new DataTable();

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tt_promoter_no"].ToString().Trim()));
            sqlCmd.Parameters.AddWithValue("@mode", "req_case");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt_emp);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        dv = dt_emp.DefaultView;

        if (dv.Count == 0)
        {
            return;
        }
        txt_req_dept.Text = Server.HtmlEncode(dv[0]["com_deptid"].ToString().Trim());
        txt_promoter_name.Text = Server.HtmlEncode(dv[0]["com_cname"].ToString().Trim());
        txt_promoter_empno.Value = Server.HtmlEncode(dv[0]["com_empno"].ToString().Trim());
        txt_Tel.Text = Server.HtmlEncode(dv[0]["com_telext"].ToString().Trim());
        txt_OrgAbbrName.Text = Server.HtmlEncode(dv[0]["orgName"].ToString().Trim());
        ViewState["com_orgcd"] = Server.HtmlEncode(dv[0]["com_orgcd"].ToString().Trim());
        ViewState["tc_degree"] = Server.HtmlEncode(dv[0]["tc_degree"].ToString().Trim());
        txt_name.Text = Server.HtmlEncode(dv[0]["tt_name"].ToString().Trim());
        LB_議約狀態.Text = Server.HtmlEncode(dv[0]["議約狀態"].ToString().Trim());
        洽案簽辦人.Text = Server.HtmlEncode(dv[0]["洽案簽辦人"].ToString().Trim());
        #endregion

        #region 客戶
        h_compno.Value = dv[0]["tc_compidno_all"].ToString().Trim().Replace("㊣", ",");//簽約對象(多)
        BindData_Customer();
        #endregion

        //正式存檔
        if (ViewState["tt_degree"].ToString() == "0")
        {
            PL_relation.Visible = true;
            btn_Save.Text = "正式立案";
            btn_Save2.Text = "正式立案";
            btn_Save.Attributes["onclick"] = @"if (!confirm('請確認案件合理性評估是否挑選正確 !?')) { return false; }";
        }
        //結案通知
        if (ViewState["tt_degree"].ToString() == "7")
        {
            btn_End.Visible = true;
            //trManageNote.Visible = true;
        }

        //檔案上傳
        if ((ViewState["RW"].ToString() == "W" || ViewState["RW"].ToString() == "A") && (ViewState["tt_degree"].ToString() == "Z"))
            btn_FileUp.Visible = true;

        //新增審查人
        if (ViewState["Role"].ToString() == "ADM") //案件承辦中
            btn_AddInspect.Visible = true;

        //送出審查
        if (ViewState["Role"].ToString() == "ADM" && ViewState["tt_status"].ToString() == "6" && ViewState["tt_degree"].ToString() == "6")
            btn_SendInspect.Visible = true;

        Bind_tc_degree();

        DDL_Degree.SelectedValue = Server.HtmlEncode(ViewState["tt_degree"].ToString());//進度
        LT_L_Degree.Text = Server.HtmlEncode(DDL_Degree.SelectedItem.Text);
        if (dv[0]["tc_send_datetime"].ToString().Trim().Length > 0)//送件日期
        {
            DateTime dTime = DateTime.Parse(dv[0]["tc_send_datetime"].ToString().Trim());
            lb_send_date.Text = dTime.ToString("yyyy/MM/dd");
        }

        if (Request.ServerVariables["HTTP_VIA"] != null)
        {
            ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
        }
        //if (dv[0]["tc_擬約幫手"].ToString() == "1")
        //{
        //    LT_擬約幫手.Text = "<font color='red'><b>【擬約幫手】</b></font>";
        //}
    }

    private void Bind_Doc_File()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));

            if (ViewState["Role"].ToString() == "ADM")
                sqlCmd.Parameters.AddWithValue("@mode", "file_list_adm");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "file_list");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    private void Bind_Inspect()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "inspect_list");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
                gv_Inspect.DataSource = dt;
                gv_Inspect.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;

        if (dv.Count == 0)
        {
            btn_SendInspect.Visible = false;
        }
        gv_Inspect.DataBind();
    }

    private void Bind_sRC_init(PlaceHolder Plh, string tt_class)
    {
        //if (Plh.Visible == false)
        //{
        //    return;
        //}
        DataTable dt = sRC_ver_list(tt_class);

        DataView dv = dt.DefaultView;

        if (dv.Count == 0)
        {
            return;
        }

        List<string> my報院條件 = new List<string>();
        List<string> my報院條件說明 = new List<string>();
        Literal lbl_trs = new Literal();
        lbl_trs.Text = string.Format(@"<tr><td align='right' valign='top' nowrap='nowrap'>
<div class='font-title titlebackicon'>{0}</div><br/>
<div><span class='font-title titlebackicon'>進度</span>
", tt_class == "X" ? "案件合理性評估<br/>(單位)" : "公告及計價事項");
        Plh.Controls.Add(lbl_trs);

        DropDownList ddl = new DropDownList();
        ddl.ID = Server.HtmlEncode("ddlProgress" + tt_class);
        ddl.DataSource = GetProgress(tt_class);
        ddl.DataTextField = "text";
        ddl.DataValueField = "value";
        ddl.Enabled = false;
        ddl.DataBind();
        ddl.Items.Insert(0, new ListItem(" ", ""));
        Plh.Controls.Add(ddl);

        Plh.Controls.Add(new Literal() { Text = "</div></td><td colspan='3' class='lineheight03' align='right' valign='top'>" });

        for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
        {
            string tcs_code = dv[sRC_count]["tcs_code"].ToString();
            switch (dv[sRC_count]["tcs_codeCheck"].ToString())
            {
                case "0":
                    Literal lbl_title = new Literal();
                    lbl_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                    Plh.Controls.Add(lbl_title);
                    break;
                case "1":
                    Plh.Controls.Add(Bind_sRC_tip(tt_class, tcs_code));

                    CheckBox CBL_x = new CheckBox();
                    CBL_x.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
                    CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                    CBL_x.Attributes["value"] = tcs_code;
                    my報院條件.Add(CBL_x.ID);
                    TextBox tb_x = new TextBox();
                    tb_x.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
                    tb_x.TextMode = TextBoxMode.MultiLine;
                    tb_x.Height = 60;
                    tb_x.Width = 450;
                    my報院條件說明.Add(tb_x.ID);
                    if ((ViewState["tt_degree"].ToString() == "0" || ViewState["tt_degree"].ToString() == "6") == false)
                    {
                        CBL_x.Attributes.Add("onclick", "return false;");
                        CBL_x.CssClass = "TB_ReadOnly";
                    }
                    if (((ViewState["tt_degree"].ToString() == "5" && Plh_Dynax_sRC.Visible == true) ||
                        (ViewState["tt_status"].ToString() == "5" && Plh_Dynax_sRC_x.Visible == true)) == false || CBL_x.Checked == false)
                    {
                        tb_x.Attributes["readonly"] = "readonly";
                        tb_x.CssClass = "TB_ReadOnly";
                    }
                    Plh.Controls.Add(CBL_x);
                    Plh.Controls.Add(tb_x);
                    if (CBL_x.Text == "價金合理性說明")
                    {
                        Literal lbl_x = new Literal();
                        lbl_x.Text = "<div style='width: 500px;'>" + BindContMoneyType() + "</div>";
                        Plh.Controls.Add(lbl_x);
                    }

                    
                    break;
                case "Z":
                    CheckBox CBL_y = new CheckBox();
                    CBL_y.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
                    CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                    CBL_y.Attributes["value"] = tcs_code;
                    my報院條件.Add(CBL_y.ID);
                    TextBox tb_y = new TextBox();
                    tb_y.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
                    tb_y.TextMode = TextBoxMode.MultiLine;
                    tb_y.Height = 60;
                    tb_y.Width = 450;
                    my報院條件說明.Add(tb_y.ID);
                    if ((ViewState["tt_degree"].ToString() == "0" || ViewState["tt_degree"].ToString() == "6") == false)
                    {
                        CBL_y.Attributes.Add("onclick", "return false;");
                        CBL_y.CssClass = "TB_ReadOnly";
                    }
                    if (((ViewState["tt_degree"].ToString() == "5" && Plh_Dynax_sRC.Visible == true) ||
                        (ViewState["tt_status"].ToString() == "5" && Plh_Dynax_sRC_x.Visible == true)) == false || CBL_y.Checked == false)
                    {
                        tb_y.Attributes["readonly"] = "readonly";
                        tb_y.CssClass = "TB_ReadOnly";
                    }
                    Plh.Controls.Add(CBL_y);
                    Plh.Controls.Add(tb_y);
                    break;
            }
            Literal lbl_br1 = new Literal();
            lbl_br1.Text = "<br />";
            Plh.Controls.Add(lbl_br1);
        }

        Literal lbl_tre = new Literal();
        lbl_tre.Text = "<br /></td></tr>";
        Plh.Controls.Add(lbl_tre);
        ViewState["my報院條件"] = my報院條件;
        ViewState["my報院條件說明"] = my報院條件說明;
    }

    private void Bind_sRC(PlaceHolder Plh, string tt_class)
    {
        if (Plh.Visible == false)
        {
            return;
        }
        Plh.Controls.Clear();
        List<string> my報院條件 = new List<string>();
        List<string> my報院條件說明 = new List<string>();
        List<string> my報院條件s = new List<string>();
        List<string> my報院條件說明s = new List<string>();

        DataTable dt = sRC_view(tt_class);

        DataView dvR = dt.DefaultView;
        if (dvR != null && dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                my報院條件s.Add(dvR[i]["tt_sRC_val"].ToString());
                //if (dvR[i]["tt_sRC_val"].ToString().IndexOf("T") != -1)
                my報院條件說明s.Add(dvR[i]["tt_sRC_val"].ToString() + "©" + dvR[i]["tt_sRC_desc"].ToString() + "©" + dvR[i]["tt_sRC_id"].ToString());
            }
        }

        dt = sRC_ver_list(tt_class);
        DataView dv = dt.DefaultView;
        if (dv.Count == 0)
        {
            return;
        }
        Literal lbl_trs = new Literal();
        lbl_trs.Text = string.Format(@"<tr><td align='right' valign='top' nowrap='nowrap'>
<div class='font-title titlebackicon'>{0}</div><br/>
<div><span class='font-title titlebackicon'>進度</span>
", tt_class == "X" ? "案件合理性評估<br/>(單位)" : "公告及計價事項");
        Plh.Controls.Add(lbl_trs);

        DropDownList ddl = new DropDownList();
        ddl.ID = Server.HtmlEncode("ddlProgress" + tt_class);
        ddl.DataSource = GetProgress(tt_class);
        ddl.DataTextField = "text";
        ddl.DataValueField = "value";
        ddl.Enabled = false;
        ddl.DataBind();
        ddl.Items.Insert(0, new ListItem(" ", ""));
        ddl.SelectedValue = tt_class == "X" ? ViewState["tt_degree"].ToString() : ViewState["tt_status"].ToString();
        Plh.Controls.Add(ddl);

        Plh.Controls.Add(new Literal() { Text = "</div></td><td colspan='3' class='lineheight03' align='right' valign='top'>" });

        for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
        {
            string tcs_code = dv[sRC_count]["tcs_code"].ToString();
            string tt_sRC_val = "";
            string tt_sRC_desc = "";
            string tt_sRC_id = "";
            foreach (string str_obj in my報院條件說明s)
            {
                string[] arr = str_obj.Split('©');
                if (arr[0] == tcs_code)
                {
                    tt_sRC_val = arr[0];
                    tt_sRC_desc = arr[1];
                    tt_sRC_id = arr[2];
                }
            }


            switch (dv[sRC_count]["tcs_codeCheck"].ToString())
            {
                case "0":
                    Literal lbl_title = new Literal();
                    lbl_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                    Plh.Controls.Add(lbl_title);
                    break;
                case "1":
                    Plh.Controls.Add(Bind_sRC_tip(tt_class, tcs_code));

                    CheckBox CBL_x = new CheckBox();
                    CBL_x.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
                    CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                    CBL_x.Attributes["value"] = Server.HtmlEncode(tcs_code);
                    CBL_x.Attributes["tt_sRC_id"] = tt_sRC_id;

                    TextBox tb_x = new TextBox();
                    tb_x.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
                    if (tt_sRC_val == tcs_code)
                    {
                        CBL_x.Checked = true;
                    }
                    tb_x.Text = tt_sRC_desc;
                    tb_x.TextMode = TextBoxMode.MultiLine;
                    tb_x.Height = 60;
                    tb_x.Width = 450;
                    tb_x.Attributes["tt_sRC_id"] = tt_sRC_id;
                    my報院條件.Add(CBL_x.ID);
                    my報院條件說明.Add(tb_x.ID);
                    if ((ViewState["tt_degree"].ToString() == "0" || (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6")) == false)
                    {
                        CBL_x.Attributes.Add("onclick", "return false;");
                        CBL_x.CssClass = "CB_ReadOnly";
                    }
                    if (((ViewState["tt_degree"].ToString() == "5" && Plh_Dynax_sRC.Visible == true) ||
                        (ViewState["tt_status"].ToString() == "5" && Plh_Dynax_sRC_x.Visible == true)) == false || CBL_x.Checked == false)
                    {
                        tb_x.Attributes["readonly"] = "readonly";
                        tb_x.CssClass = "TB_ReadOnly";
                    }
                    Plh.Controls.Add(CBL_x);
                    Plh.Controls.Add(tb_x);
                    if (CBL_x.Text == "價金合理性說明")
                    {
                        Literal lbl_x = new Literal();
                        lbl_x.Text = "<div style='width: 500px;'>" + BindContMoneyType() + "</div>";
                        Plh.Controls.Add(lbl_x);
                    }
                    break;
                case "Z":
                    CheckBox CBL_y = new CheckBox();
                    CBL_y.ID = Server.HtmlEncode("CBL_" + tt_class + tcs_code);
                    CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                    CBL_y.Attributes["value"] = Server.HtmlEncode(tcs_code);

                    my報院條件.Add(CBL_y.ID);
                    foreach (string obj in my報院條件s)
                    {
                        if (obj.IndexOf(tcs_code) >= 0)
                            CBL_y.Checked = true;
                    }
                    TextBox tb_y = new TextBox();
                    tb_y.ID = Server.HtmlEncode("TB_" + tt_class + tcs_code);
                    tb_y.TextMode = TextBoxMode.MultiLine;
                    tb_y.Height = 60;
                    tb_y.Width = 450;

                    my報院條件說明.Add(tb_y.ID);
                    foreach (string str_obj in my報院條件說明s)
                    {
                        if (str_obj.Split('©')[0].ToString() == tcs_code)
                            tb_y.Text = Server.HtmlEncode(str_obj.Split('©')[1].ToString());
                    }
                    if ((ViewState["tt_degree"].ToString() == "0" || ViewState["tt_degree"].ToString() == "6") == false)
                    {
                        CBL_y.Attributes.Add("onclick", "return false;");
                        CBL_y.CssClass = "CB_ReadOnly";
                    }
                    if (((ViewState["tt_degree"].ToString() == "5" && Plh_Dynax_sRC.Visible == true) ||
                        (ViewState["tt_status"].ToString() == "5" && Plh_Dynax_sRC_x.Visible == true)) == false || CBL_y.Checked == false)
                    {
                        tb_y.Attributes["readonly"] = "readonly";
                        tb_y.CssClass = "TB_ReadOnly";
                    }
                    Plh.Controls.Add(CBL_y);
                    Plh.Controls.Add(tb_y);
                    break;
            }

            Literal lbl_br1 = new Literal();
            lbl_br1.Text = "<br />";
            Plh.Controls.Add(lbl_br1);
        }

        Literal lbl_tre = new Literal();
        lbl_tre.Text = "<br /></td></tr>";
        Plh.Controls.Add(lbl_tre);
        ViewState["my報院條件"] = my報院條件;
        ViewState["my報院條件說明"] = my報院條件說明;
        ViewState["my報院條件說明save" + Plh.ClientID] = my報院條件說明s;
    }

    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace(e.CommandArgument.ToString(), "");
            BindData_Customer();
        }

    }

    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("lbl_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + Server.HtmlEncode(LB.Text) + "\");' >" + Server.HtmlEncode(LB.Text) + "</a>";

    }

    protected void lnkbtn_廠商中文名稱_Click(object sender, EventArgs e)
    {
        LinkButton CompInfo = sender as LinkButton;
        string Compno = CompInfo.Attributes["Compno"].Trim();
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);
    }

    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_doc"].ToString().Trim();
            }
            //FileInfo fi = new FileInfo(str_file_url);
            //if (fi.Exists)
            //{
            //fi.Delete();
            Tech_log(ViewState["tt_seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TechCase_modify.aspx");

            File_Del(e.CommandArgument.ToString());
            //}

            Bind_Doc_File();
        }

        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tcdf_doc"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Tech_log(ViewState["tt_seno"].ToString(), "檔案下載", str_filename, ViewState["xIP"].ToString(), "treaty\\TechCase_modify.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }

    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");
            Label lb_tcdf_type = (Label)e.Row.FindControl("lbl_tcdf_type");
            Label lb_inspect = (Label)e.Row.FindControl("lbl_inspect");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                Label lb_tcdf_no = (Label)e.Row.FindControl("lbl_tcdf_no");
                LinkButton lb_edit = (LinkButton)e.Row.FindControl("lnkbtn_Edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
                if (lb_tcdf_type.Text == "RE")
                {
                    lb_del.Visible = false;
                    lb_edit.Visible = false;
                }
                //if (lb_inspect.Text == "0")
                //    lb_inspect.Text = "";
                //else
                //    lb_inspect.Text = "V";


                DropDownList ddlHec = (DropDownList)e.Row.FindControl("DDL_Hec");
                DropDownList ddlHec_order = (DropDownList)e.Row.FindControl("DDL_Hec_order");
                if ((ddlHec != null) && (ViewState["Role"].ToString() == "ADM" || lb_handle_empno.Text == ViewState["empno"].ToString()))
                {
                    ddlHec.Visible = true;
                    ddlHec_order.Visible = true;
                    ddlHec.Attributes["caseno"] = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tcdf_no")).Trim();
                    ddlHec.SelectedValue = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tcdf_inspect"));
                    ddlHec_order.Attributes["caseno"] = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tcdf_no")).Trim();
                    ddlHec_order.SelectedValue = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tcdf_up_order"));
                }
            }
        }
    }

    protected void gv_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("lbl_Istatus");
        if (LB != null)
            switch (LB.Text.Trim())
            {
                case "0":
                    LB.Text = "";
                    btn_SendInspect.Visible = true;
                    btn_End.Visible = false;
                    break;
                case "1":
                    LB.Text = "同意";
                    break;
                case "2":
                    LB.Text = "退回";
                    break;
            }
        LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");
        if (lb_del != null)
        {
            lb_del.Visible = ViewState["Role"].ToString() == "ADM";
        }
    }

    protected void gv_Inspect_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "del_inspect");
                sqlCmd.Parameters.AddWithValue("@tti_no", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            Bind_Inspect();
        }
    }

    protected void btn_SendInspect_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if (txt_promoter_empno.Value == "")

        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }

        if (txt_name.Text == "請輸入契約名稱")
        {

            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (h_compno.Value == "")
        {

            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#h_compno').validationEngine('showPrompt', '★簽約對象 必須挑選','','',true); $('#h_compno').click(function () { $('#h_compno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "h_compno", script_alert);
        }


        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";



        if (str_error != "")
        {
            string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");

            Do_Modify();

            StringBuilder script = new StringBuilder("<script type='text/javascript'> SendInspect(" + ViewState["tt_seno"].ToString() + ");</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }

    protected void btn_End_Click(object sender, EventArgs e)
    {
        string str_danger = "0";

        if (txt_name.Text == "請輸入契約名稱")
        {
            //str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }

        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";

        if (txt_betsum.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";
        if (txt_ManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if (str_danger == "1")
            Response.Redirect("../danger.aspx");

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "un_inspect");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_count = dt.DefaultView;
        if (dv_count.Count >= 1)
        {
            if (dv_count[0][0].ToString() != "0")
            {
                StringBuilder script = new StringBuilder("<script type='text/javascript'> 還有審查人未審查!;</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }
            else
            {
                DoEnd();

                Tech_log(ViewState["tt_seno"].ToString(), "結案", "", "", "treaty\\TechCase_modify.aspx");
                StringBuilder script = new StringBuilder("<script type='text/javascript'> EndCase(" + ViewState["tt_seno"].ToString() + ");</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }

        }
    }

    protected void btn_Tmp_Save_Click(object sender, EventArgs e)
    {
        if (CheckSave() == true)
        {
            Do_Modify_Temp();

            //報院條件       
            Save_sRC(Plh_Dynax_sRC, "X");
            Save_sRC(Plh_Dynax_sRC_x, "");

            Tech_log(ViewState["tt_seno"].ToString(), "案件存檔", "", "", "treaty\\TechCase_modify.aspx");

            string script = "<script language='javascript'>alert('暫存成功！');location.href='./TechCase_View.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }

    protected void btn_Save_Click(object sender, EventArgs e)
    {
        if (CheckSave() == true)
        {
            //return;
            Do_Modify_Temp();

            //報院條件       
            Save_sRC(Plh_Dynax_sRC, "X");
            Save_sRC(Plh_Dynax_sRC_x, "");

            #region 更新狀態
            if (ViewState["tt_degree"].ToString() == "0" && ViewState["Role"].ToString() == "ADM")
            {
                Set_Status("3");
                Set_Degree("3");
            }
            else if (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6")
            {
                Do_Modify();
            }
            else if (ViewState["RW"].ToString() == "W")
            {
                if ((ViewState["tt_degree"].ToString() == "5" && ViewState["Role2"].ToString() == "X") ||
                    (ViewState["tt_status"].ToString() == "5" && ViewState["Role2"].ToString() == "x"))
                {
                    Do_Modify_X("6");
                }
            }
            #endregion

            Tech_log(ViewState["tt_seno"].ToString(), "案件存檔", "", "", "treaty\\TechCase_modify.aspx");

            string script = "<script language='javascript'>alert('存檔成功！');location.href='./TechCase_View.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }

    protected void btn_Back_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TechCase_view.aspx?tt_seno=" + ViewState["tt_seno"].ToString());
    }

    private void DoEnd()
    {
        #region 更新主檔
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "end_case");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_remark", oRCM.SQLInjectionReplaceAll(txt_ManageNote.Text));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Save_sRC(PlaceHolder Plh, string tt_class)
    {
        if (Plh.Visible == false)
        {
            return;
        }
        List<string> my報院條件說明s = new List<string>();
        my報院條件說明s = (List<string>)ViewState["my報院條件說明save" + Plh.ClientID];
        if (my報院條件說明s == null) return;
        foreach (Control ctrl in Plh.Controls)
        {
            if (ctrl is CheckBox)
            {
                CheckBox cb = (CheckBox)ctrl;
                string tt_sRC_val = cb.Attributes["value"];
                string tt_sRC_id = "";
                string tt_sRC_desc = "";
                string str_obj = my報院條件說明s.Where(x => x.StartsWith(tt_sRC_val + "©")).Select(x => x).FirstOrDefault<string>();
                if (string.IsNullOrEmpty(str_obj) == false)
                {
                    string[] arr = str_obj.Split('©');
                    tt_sRC_id = arr[2];
                }

                TextBox tb = Plh.FindControl("TB_" + tt_class + tt_sRC_val) as TextBox;
                if (tb != null)
                    tt_sRC_desc = tb.Text;

                if (cb.Checked == true)
                {
                    if (string.IsNullOrEmpty(tt_sRC_id) == true)
                        sRC_Add(tt_class, tt_sRC_val, tt_sRC_desc);
                    else
                        sRC_modify(tt_sRC_id, tt_sRC_val, tt_sRC_desc);
                }
                else
                {
                    if (tt_sRC_id != "")
                        sRC_del(tt_sRC_id);
                }
            }
        }
    }

    private DataTable sRC_ver_list(string tt_class)
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            if (tt_class == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "sRC_ver_list_X_v2");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "sRC_ver_list_x");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_sRC_relation", oRCM.SQLInjectionReplaceAll(rbl_relation.SelectedValue));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        return dt;
        #endregion
    }

    private DataTable sRC_view(string str_class)
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            if (str_class == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "sRC_view_X");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "sRC_view_x");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        return dt;
        #endregion
    }

    private void sRC_Add(string tt_class, string tt_sRC_val, string tt_sRC_desc)
    {
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "sRC_Add");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_class", oRCM.SQLInjectionReplaceAll(tt_class == "X" ? "X" : "x"));
            sqlCmd.Parameters.AddWithValue("@tt_sRC_val", oRCM.SQLInjectionReplaceAll(tt_sRC_val));
            sqlCmd.Parameters.AddWithValue("@tt_sRC_desc", oRCM.SQLInjectionReplaceAll(tt_sRC_desc));

            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void sRC_del(string tt_sRC_id)
    {
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "sRC_del");
            sqlCmd.Parameters.AddWithValue("@tt_sRC_id", oRCM.SQLInjectionReplaceAll(tt_sRC_id));

            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void sRC_modify(string tt_sRC_id, string tt_sRC_val, string tt_sRC_desc)
    {
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "sRC_modify");
            sqlCmd.Parameters.AddWithValue("@tt_sRC_id", oRCM.SQLInjectionReplaceAll(tt_sRC_id));
            sqlCmd.Parameters.AddWithValue("@tt_sRC_val", oRCM.SQLInjectionReplaceAll(tt_sRC_val));
            sqlCmd.Parameters.AddWithValue("@tt_sRC_desc", oRCM.SQLInjectionReplaceAll(tt_sRC_desc));

            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
    }

    private void Tech_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        return;
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private DataTable GetProgress(string str_class)
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            if (str_class == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "init_degree");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "init_status");

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        return dt;
        #endregion
    }

    private void Bind_Auth()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Auth");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_auth = dt.DefaultView;
        if (dv_auth.Count >= 1)
        {
            string str_auth = dv_auth[0]["RW"].ToString();
            if (str_auth == "X")
                Response.Redirect("../NoAuthRight.aspx");

            if (str_auth != "W")
            {
                string script = "<script language='javascript'>alert('無編輯權限！');location.href='./TechCase_View.aspx?tt_seno=" + Server.HtmlEncode(ViewState["tt_seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "NoAuthRight", script);
            }

            ViewState["RW"] = Server.HtmlEncode(dv_auth[0]["RW"].ToString());
            ViewState["Role"] = Server.HtmlEncode(dv_auth[0]["Role"].ToString());
            ViewState["Role2"] = Server.HtmlEncode(dv_auth[0]["Role2"].ToString());
            ViewState["分案權限"] = Server.HtmlEncode(dv_auth[0]["分案權限"].ToString());
            ViewState["檢視意見彙整"] = Server.HtmlEncode(dv_auth[0]["檢視意見彙整"].ToString());

            if (ViewState["檢視意見彙整"].ToString() == "0")
            {
                PL_Inspect.Visible = false;
                PL_tt_manage_note.Visible = false;
            }
        }
    }

    protected void HecUpdate(object sender, System.EventArgs e)
    {
        string strCaseno = ((DropDownList)sender).Attributes["caseno"];
        string strHecflag = ((DropDownList)sender).SelectedValue;

        if (IsDangerWord(strHecflag)) Response.Redirect("../danger.aspx");


        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_TechCase_modify";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(strCaseno));
        oCmd.Parameters.AddWithValue("@inspect", oRCM.SQLInjectionReplaceAll(strHecflag)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("@mode", oRCM.SQLInjectionReplaceAll("file_inspect")); //將舊案的流水號存入

        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    protected void HecUpdate_order(object sender, System.EventArgs e)
    {
        string strCaseno = ((DropDownList)sender).Attributes["caseno"];
        string strHecflag = ((DropDownList)sender).SelectedValue;

        if (IsDangerWord(strHecflag)) Response.Redirect("../danger.aspx");
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_TechCase_modify";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(strCaseno));
        oCmd.Parameters.AddWithValue("@tcdf_up_order", oRCM.SQLInjectionReplaceAll(strHecflag)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("@mode", oRCM.SQLInjectionReplaceAll("file_order")); //將舊案的流水號存入

        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    private void Set_Degree(string degree)
    {
        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "degree_modify");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_degree", oRCM.SQLInjectionReplaceAll(degree));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Set_Status(string status)
    {
        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "status_modify");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_status", oRCM.SQLInjectionReplaceAll(status));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private bool CheckSave()
    {
        string str_error = "";
        string str_danger = "0";

        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }

        if (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6" && chk_成果歸屬運用辦法.Checked == true)
        {
            if (!(chk_公益目的.Checked || chk_促進整體產業發展.Checked || chk_提升研發成果運用效益.Checked))
            {
                str_error += "★成果歸屬運用辦法 必須輸入\\n ";
                string script_alert = "<script language='javascript'> $('#chk_公益目的').validationEngine('showPrompt', '★成果歸屬運用辦法 必須挑選','','',true); $('#chk_公益目的').click(function () { $('#chk_公益目的').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "chk_公益目的", script_alert);
            }
        }

        if (ViewState["tt_degree"].ToString() == "6" && ViewState["tt_status"].ToString() == "6")
        {
            if (rbl_Light.SelectedValue == "")
            {
                str_error += "★案件燈號 必須輸入\\n ";
                string script_alert = "<script language='javascript'> $('#rbl_Light').validationEngine('showPrompt', '★案件燈號 必須挑選','','',true); $('#rbl_Light').click(function () { $('#rbl_Light').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "rbl_Light", script_alert);
            }
        }

        if (ViewState["tt_degree"].ToString() == "0")
        {
            if (rbl_relation.SelectedValue == "")
            {
                str_error += "★案件合理性評估 必須輸入\\n ";
                string script_alert = "<script language='javascript'> $('#rbl_relation').validationEngine('showPrompt', '★案件合理性評估 必須挑選','','',true); $('#rbl_relation').click(function () { $('#rbl_relation').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "rbl_relation", script_alert);
            }
        }

        if (ViewState["tr_class"].ToString() == "N")
        {
            if (!(cb_conttype_b0.Checked || cb_conttype_b1.Checked || cb_conttype_d4.Checked || cb_conttype_d5.Checked || cb_conttype_d7.Checked || cb_conttype_ns.Checked))
            {
                string script_alert = "<script language='javascript'> $('#cb_conttype_b0').validationEngine('showPrompt', '★案件性質 必須挑選','','',true); $('#cb_conttype_b0').click(function () { $('#cb_conttype_b0').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "cb_conttype_b0", script_alert);
            }
        }

        if (txt_name.Text == "請輸入契約名稱")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }

        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";

        if (txt_ManageNote.Text.ToUpper().IndexOf("SCRIPT ") >= 0) str_danger = "1";

        if (str_error == "")
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");

            return true;
        }

        return false;
    }

    private DataTable Case_View()
    {
        DataTable dt = new DataTable();
        #region --- query --- 
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view_case");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        return dt;
    }

    private DataTable File_View(string tcdf_no)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");
            sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    private void File_Del(string tcdf_no)
    {
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_Del");
            sqlCmd.Parameters.AddWithValue("@tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Bind_tc_degree()
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "init_degree");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                DDL_Degree.DataSource = dt;
                DDL_Degree.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Do_Modify_Temp()
    {
        #region 更新主檔
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "modify_Temp_V3");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_betsum", oRCM.SQLInjectionReplaceAll(txt_betsum.Text));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@案件燈號", oRCM.SQLInjectionReplaceAll(rbl_Light.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@tt_燈號_說明", oRCM.SQLInjectionReplaceAll(txt_燈號_說明.Text));
            sqlCmd.Parameters.AddWithValue("@成果歸屬運用辦法", oRCM.SQLInjectionReplaceAll(chk_成果歸屬運用辦法.Checked ? "1" : "0"));
            sqlCmd.Parameters.AddWithValue("@公益目的", chk_公益目的.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@促進整體產業發展", chk_促進整體產業發展.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@提升研發成果運用效益", chk_提升研發成果運用效益.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@第三方鑑價報告", chk_第三方鑑價報告.Checked ? "1" : "0");

            sqlCmd.Parameters.AddWithValue("@tt_conttype_b0", cb_conttype_b0.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_b1", cb_conttype_b1.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d4", cb_conttype_b1.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d5", cb_conttype_d5.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d7", cb_conttype_d7.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術讓與", chk_技術讓與.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_ns", cb_conttype_ns.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術授權", chk_技術授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_專利授權", chk_專利授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術與專利授權", chk_技術與專利授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_特定區域", chk_特定區域.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_修約", chk_修約.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_sRC_relation", oRCM.SQLInjectionReplaceAll(rbl_relation.SelectedValue));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Do_Modify_X(string degree_status)
    {
        #region 更新主檔
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            if (ViewState["Role2"].ToString() == "X")
            {
                sqlCmd.Parameters.AddWithValue("@mode", "modify_X");
                sqlCmd.Parameters.AddWithValue("@tt_degree", oRCM.SQLInjectionReplaceAll(degree_status));
            }
            else
            {
                sqlCmd.Parameters.AddWithValue("@mode", "modify_x");
                sqlCmd.Parameters.AddWithValue("@tt_status", oRCM.SQLInjectionReplaceAll(degree_status));
            }
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Do_Modify()
    {
        #region 更新主檔
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@mode", "modify_V3");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_betsum", oRCM.SQLInjectionReplaceAll(txt_betsum.Text));
            sqlCmd.Parameters.AddWithValue("@tt_degree", "7");
            sqlCmd.Parameters.AddWithValue("@tt_status", "7");
            sqlCmd.Parameters.AddWithValue("@案件燈號", oRCM.SQLInjectionReplaceAll(rbl_Light.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@tt_燈號_說明", oRCM.SQLInjectionReplaceAll(txt_燈號_說明.Text));
            sqlCmd.Parameters.AddWithValue("@成果歸屬運用辦法", oRCM.SQLInjectionReplaceAll(chk_成果歸屬運用辦法.Checked ? "1" : "0"));
            sqlCmd.Parameters.AddWithValue("@公益目的", chk_公益目的.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@促進整體產業發展", chk_促進整體產業發展.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@提升研發成果運用效益", chk_提升研發成果運用效益.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@第三方鑑價報告", chk_第三方鑑價報告.Checked ? "1" : "0");

            sqlCmd.Parameters.AddWithValue("@tt_conttype_b0", cb_conttype_b0.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_b1", cb_conttype_b1.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d4", cb_conttype_b1.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d5", cb_conttype_d5.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_d7", cb_conttype_d7.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術讓與", chk_技術讓與.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_conttype_ns", cb_conttype_ns.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術授權", chk_技術授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_專利授權", chk_專利授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_技術與專利授權", chk_技術與專利授權.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_特定區域", chk_特定區域.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_修約", chk_修約.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@tt_sRC_relation", oRCM.SQLInjectionReplaceAll(rbl_relation.SelectedValue));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void Bind_Light()
    {
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";

            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "init_Light");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                rbl_Light.DataSource = dt;
                rbl_Light.DataTextField = "text";
                rbl_Light.DataValueField = "value";
                rbl_Light.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    protected void chk_成果歸屬運用辦法_CheckedChanged(object sender, EventArgs e)
    {
        if (chk_成果歸屬運用辦法.Checked == false)
        {
            chk_公益目的.Checked = false;
            chk_促進整體產業發展.Checked = false;
            chk_提升研發成果運用效益.Checked = false;
            chk_公益目的.Enabled = false;
            chk_促進整體產業發展.Enabled = false;
            chk_提升研發成果運用效益.Enabled = false;
        }
        else
        {
            chk_公益目的.Enabled = true;
            chk_促進整體產業發展.Enabled = true;
            chk_提升研發成果運用效益.Enabled = true;
        }
    }

    private string BindContMoneyType()
    {
        DataTable dt = new DataTable();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"
SELECT          TOP (200) code_type, type_desc, code_subtype, subtype_desc, display_order, enable, code_group
FROM              treaty_TechCase_code_table
WHERE          (code_type = '90') AND (code_subtype = '7')
";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);


            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        if (dt.Rows.Count > 0)
            return Server.HtmlDecode(Server.HtmlEncode(dt.Rows[0]["subtype_desc"].ToString()));

        return "";
    }

    protected void rbl_Light_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (rbl_Light.SelectedValue == "9")
        {
            txt_燈號_說明.Visible = true;
        }
        else
        {
            txt_燈號_說明.Visible = false;
            txt_燈號_說明.Text = "";
        }
    }

    private Literal Bind_sRC_tip(string tcs_class, string tcs_code)
    {
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mod", "sRC_tip");
            sqlCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(tcs_class));
            sqlCmd.Parameters.AddWithValue("@tip_xid", oRCM.SQLInjectionReplaceAll(tcs_code));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        Literal lt_view = new Literal();

        if (dt.Rows.Count > 0)
        {
            int tip_id = int.Parse(dt.Rows[0]["tip_id"].ToString());

            lt_view.Text = "<a class='iterm_dymanic' rel='../manager/tip_View.aspx?tid=" + tip_id + "' ><img src='../../images/icon_tips.png'></img> </a>";
        }

        return lt_view;
    }

}