﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase2_XFile : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r;
        try
        {
            r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        }
        catch
        {
            return false;
        }
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        if (str == "")
            return true;
        else
        {
            System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
            return reg1.IsMatch(str);
        }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");

            if (Request.QueryString["sub_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["sub_seno"]) || (Request.QueryString["sub_seno"].Length == 0) || (Request.QueryString["sub_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["sub_seno"] = Request.QueryString["sub_seno"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");

            if (Request.QueryString["FType"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["FType"]) || (Request.QueryString["FType"].Length == 0) || (Request.QueryString["FType"].Length > 3))
                    Response.Redirect("../danger.aspx");
                ViewState["FType"] = Request.QueryString["FType"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");

            BT_FileUp.Attributes.Add("onclick", "fileup( " + ViewState["seno"].ToString() + "," + ViewState["sub_seno"].ToString() + ",'" + ViewState["FType"].ToString() + "');");
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empNo"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }

            BindData();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData();
        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {

        Treaty_log(ViewState["seno"].ToString(), "侵權證據資料檔案上傳", "", ViewState["xIP"].ToString(), "treaty\\TreatyCase2_XFile.aspx");
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }
    private void BindData()
    {
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyCase2_XFile_modify";
        //SDS_gv_file.SelectParameters.Add("seno",      ViewState["seno"].ToString());
        //SDS_gv_file.SelectParameters.Add("sub_seno",  ViewState["sub_seno"].ToString());
        //SDS_gv_file.SelectParameters.Add("fd_name",  "");
        //SDS_gv_file.SelectParameters.Add("filetxt",  "");
        //SDS_gv_file.SelectParameters.Add("file_url",  "");
        //SDS_gv_file.SelectParameters.Add("empno",  "");
        //SDS_gv_file.SelectParameters.Add("fid", "");
        //SDS_gv_file.SelectParameters.Add("FType", ViewState["FType"].ToString());
        //SDS_gv_file.SelectParameters.Add("mode",  "Select");
        //for (int i = 0; i < SDS_gv_file.SelectParameters.Count; i++)
        //{
        //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_XFile_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@fd_name", "");
            sqlCmd.Parameters.AddWithValue("@filetxt", "");
            sqlCmd.Parameters.AddWithValue("@file_url", "");
            sqlCmd.Parameters.AddWithValue("@empno", "");
            sqlCmd.Parameters.AddWithValue("@fid", "");
            sqlCmd.Parameters.AddWithValue("@FType", oRCM.SQLInjectionReplaceAll(ViewState["FType"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Select");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";
            //SDS_gv_file.SelectParameters.Clear();
            //SDS_gv_file.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.SelectCommand = "esp_TreatyCase2_XFile_modify";
            //SDS_gv_file.SelectParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_gv_file.SelectParameters.Add("sub_seno", ViewState["sub_seno"].ToString());
            //SDS_gv_file.SelectParameters.Add("fd_name", "");
            //SDS_gv_file.SelectParameters.Add("filetxt", "");
            //SDS_gv_file.SelectParameters.Add("file_url", "");
            //SDS_gv_file.SelectParameters.Add("empno", "");
            //SDS_gv_file.SelectParameters.Add("fid", e.CommandArgument.ToString());
            //SDS_gv_file.SelectParameters.Add("FType", ViewState["FType"].ToString());
            //SDS_gv_file.SelectParameters.Add("mode", "select");
            //for (int i = 0; i < SDS_gv_file.SelectParameters.Count; i++)
            //{
            //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_gv_file.DataBind();
            //System.Data.DataView dv = (DataView)SDS_gv_file.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_XFile_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@FType", oRCM.SQLInjectionReplaceAll(ViewState["FType"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "select");
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            FileInfo fi = new FileInfo(str_file_url.Replace("/", "").Replace("..", ""));
            if (fi.Exists)
            {
                fi.Delete();
                Treaty_log(ViewState["seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase2_XFile.aspx");
                //SDS_gv_file.DeleteParameters.Clear();
                //SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_gv_file.DeleteCommand = "esp_TreatyCase2_XFile_modify";
                //SDS_gv_file.DeleteParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_gv_file.DeleteParameters.Add("sub_seno", ViewState["sub_seno"].ToString());
                //SDS_gv_file.DeleteParameters.Add("fd_name", "");
                //SDS_gv_file.DeleteParameters.Add("filetxt", "");
                //SDS_gv_file.DeleteParameters.Add("file_url", "");
                //SDS_gv_file.DeleteParameters.Add("empno", "");
                //SDS_gv_file.DeleteParameters.Add("fid", e.CommandArgument.ToString());
                //SDS_gv_file.DeleteParameters.Add("FType", ViewState["FType"].ToString());
                //SDS_gv_file.DeleteParameters.Add("mode", "delete");
                //SDS_gv_file.Delete();
                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_XFile_modify";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@fd_name", "");
                    sqlCmd.Parameters.AddWithValue("@filetxt", "");
                    sqlCmd.Parameters.AddWithValue("@file_url", "");
                    sqlCmd.Parameters.AddWithValue("@empno", "");
                    sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                    sqlCmd.Parameters.AddWithValue("@FType", oRCM.SQLInjectionReplaceAll(ViewState["FType"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@mode", "delete");

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                BindData();
            }
        }
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";
            //SDS_gv_file.SelectParameters.Clear();
            //SDS_gv_file.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.SelectCommand = "esp_TreatyCase2_XFile_modify";
            //SDS_gv_file.SelectParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_gv_file.SelectParameters.Add("sub_seno", ViewState["sub_seno"].ToString());
            //SDS_gv_file.SelectParameters.Add("fd_name", "");
            //SDS_gv_file.SelectParameters.Add("filetxt", "");
            //SDS_gv_file.SelectParameters.Add("file_url", "");
            //SDS_gv_file.SelectParameters.Add("empno", "");
            //SDS_gv_file.SelectParameters.Add("fid", e.CommandArgument.ToString());
            //SDS_gv_file.SelectParameters.Add("FType", ViewState["FType"].ToString());          
            //SDS_gv_file.SelectParameters.Add("mode", "view");

            //for (int i = 0; i < this.SDS_gv_file.SelectParameters.Count; i++)
            //{
            //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_gv_file.DataBind();
            //System.Data.DataView dv = (DataView)SDS_gv_file.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_XFile_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@FType", oRCM.SQLInjectionReplaceAll(ViewState["FType"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "view");


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase2_Evidence_File.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                Response.Flush();
                Response.End();
            }
        }
        if (e.CommandName == "xEdit")
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'>file_modify(" + e.CommandArgument.ToString() + ",'" + ViewState["FType"].ToString() + "');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindData();
        }

    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            Label lb_tcdf_type = (Label)e.Row.FindControl("LB_tcdf_type");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");
                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ",'" + ViewState["FType"].ToString() + "');");

            }
        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_TreatyCase2_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();
        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

}