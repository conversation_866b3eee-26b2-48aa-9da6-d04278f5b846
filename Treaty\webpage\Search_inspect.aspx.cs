﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class Search_inspect : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    public bool IsDanger(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;

            ViewState["sortorder"] = "";
            ViewState["sortField"] = "";
            BindContType();
            BindOrg();
            DoSearch();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }
    private void BindContType()
    {
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"exec esp_treatyCase_codetable   '' ,'10' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindOrg()
    {
        //SDS_Orgcd.SelectCommand = "exec esp_treaty_search_inspect_orglist '" + SQLInjectionReplaceAll(ViewState["empno"].ToString()) + "' ";
        //SDS_Orgcd.DataBind();
        //ddlOrgcd.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_inspect_orglist ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlOrgcd.DataSource = dt;
                ddlOrgcd.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        //if (str_sortField == "")
        //    SDS_search.SelectCommand = " SELECT  * from v_treaty_search_inspect where tmp_uid ='" +ssoUser.empNo + "' order by tmp_case_actno ";
        //else
        //    SDS_search.SelectCommand = " SELECT  * from v_treaty_search_inspect where tmp_uid ='" + ssoUser.empNo + "' order by  " + str_sortField + " " + str_sort;

        //SDS_search.DataBind();
        //SGV_search.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.Clear();

            if (str_sortField == "")
                sqlCmd.CommandText = " SELECT * from v_treaty_search_inspect where tmp_uid=@sso order by tmp_case_actno ";
            else
                sqlCmd.CommandText = " SELECT * from v_treaty_search_inspect where tmp_uid =@sso order by  " + oRCM.SQLInjectionReplaceAll(str_sortField) + " " + oRCM.SQLInjectionReplaceAll(str_sort);

            sqlCmd.Parameters.AddWithValue("@sso", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;



            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void DoSearch()
    {
        if (ddlContType.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (ddlOrgcd.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (ddlContType.SelectedValue.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (ddlOrgcd.SelectedValue.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxCompName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxHandleName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxPromoterName.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxReqDept.Text.ToUpper().IndexOf("-") >= 0) Response.Redirect("../danger.aspx");
        if (tbxReqDept.Text.Length >= 8) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxCompName.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxReqDept.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxHandleName.Text)) Response.Redirect("../danger.aspx");
        if (IsDanger(tbxPromoterName.Text)) Response.Redirect("../danger.aspx");

        string str_class = "";
        string A = (cbxCaseClass.Items[0].Selected == true) ? cbxCaseClass.Items[0].Value : "";
        string N = (cbxCaseClass.Items[1].Selected == true) ? cbxCaseClass.Items[1].Value : "";
        string R = (cbxCaseClass.Items[2].Selected == true) ? cbxCaseClass.Items[2].Value : "";
        string M = (cbxCaseClass.Items[3].Selected == true) ? cbxCaseClass.Items[3].Value : "";
        string U = (cbxCaseClass.Items[4].Selected == true) ? cbxCaseClass.Items[4].Value : "";
        string S = (cbxCaseClass.Items[5].Selected == true) ? cbxCaseClass.Items[5].Value : "";
        string T = (cbxCaseClass.Items[6].Selected == true) ? cbxCaseClass.Items[6].Value : "";
        if ((A != "") || (N != "") || (R != "") || (M != "") || (U != "") || (S != "") || (T != ""))
            str_class = A + "," + N + "," + R + "," + M + "," + U + "," + S + "," + T;

        if (IsDanger(str_class)) Response.Redirect("../danger.aspx");

        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_treaty_search_inspect";
        //SDS_NR.UpdateParameters.Add("login", ViewState["empno"].ToString());
        //SDS_NR.UpdateParameters.Add("orgcd", ddlOrgcd.SelectedValue); //將舊案的流水號存入
        //SDS_NR.UpdateParameters.Add("conttype", ddlContType.SelectedValue);//洽案(契約)名稱
        //SDS_NR.UpdateParameters.Add("company", tbxCompName.Text);
        //SDS_NR.UpdateParameters.Add("handle_name", tbxHandleName.Text);
        //SDS_NR.UpdateParameters.Add("promoter_name", tbxPromoterName.Text);
        //SDS_NR.UpdateParameters.Add("class", str_class);
        //SDS_NR.UpdateParameters.Add("tc_degree", "");
        //SDS_NR.UpdateParameters.Add("req_dept", tbxReqDept.Text);
        //SDS_NR.UpdateParameters.Add("kw", "");
        //SDS_NR.Update();


        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_treaty_search_inspect";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd.Parameters.AddWithValue("orgcd", oRCM.SQLInjectionReplaceAll(ddlOrgcd.SelectedValue)); //將舊案的流水號存入
        oCmd.Parameters.AddWithValue("conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));//洽案(契約)名稱
        oCmd.Parameters.AddWithValue("company", oRCM.SQLInjectionReplaceAll(tbxCompName.Text));
        oCmd.Parameters.AddWithValue("handle_name", oRCM.SQLInjectionReplaceAll(tbxHandleName.Text));
        oCmd.Parameters.AddWithValue("promoter_name", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
        oCmd.Parameters.AddWithValue("class", oRCM.SQLInjectionReplaceAll(str_class));
        oCmd.Parameters.AddWithValue("tc_degree", "");
        oCmd.Parameters.AddWithValue("req_dept", oRCM.SQLInjectionReplaceAll(tbxReqDept.Text));
        oCmd.Parameters.AddWithValue("kw", "");
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();

    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        DoSearch();
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            Response.Redirect("./TreatyCase_view.aspx?seno=" + e.CommandArgument.ToString());
        }
    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
}