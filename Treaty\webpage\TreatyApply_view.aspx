﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApply_view.aspx.cs" Inherits="TreatyApply_view" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../style/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />



    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap/colorbox_close.aspx";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        var p = navigator.platform;
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" Height="30px" ImageUrl="../images/CONFIDENTIAL.png" Width="85px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="btnEngage" runat="server" OnClick="btnEngage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊</asp:LinkButton>&nbsp;&nbsp;
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="td_left"><span class="font-red">
                                            <asp:Label ID="LB_status" runat="server" Text=""></asp:Label></span></td>
                                        <td class="td_right" colspan="4">&nbsp;
                                        </td>
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:Label ID="txtComplexNo" runat="server" Text=""></asp:Label>
                                            (舊案號:
                                            <asp:Label ID="txtOldContno" runat="server"></asp:Label>)</td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                        </td>
                                        <td class="width40">
                                            <asp:Label ID="LB_language" runat="server" Text=""></asp:Label>
                                            <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" Visible="false" />
                                            <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" Visible="false" />
                                            <asp:RadioButton ID="rb_language_other" runat="server" Text="其他" GroupName="ContractLang" Visible="false" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label>&nbsp;
                                            <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server" Width="95px"></asp:Label>
                                            &nbsp;
                       分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server" Width="110px"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:Label ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:Label></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">是否為急件需求</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="CB_急件" runat="server" Enabled="false" />
                                            <asp:TextBox ID="TB_急件原因" runat="server" Width="608px" TextMode="MultiLine" Height="20px" Enabled="false"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:BoundField DataField="comp_idno" HeaderText="廠商編號">
                                                                <HeaderStyle Width="90px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_cname" HeaderText="廠商中文名稱">
                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_ename" HeaderText="廠商英文名稱">
                                                                <HeaderStyle Width="350px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--  <asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" /> --%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            案件性質</td>
                                        <td colspan="3" class="lineheight03">
                                            <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽案)" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_rb" runat="server" Text="標案" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_m" runat="server" Text="保密契約" Enabled="false" />
                                            <asp:Label ID="lb_standar_flag" runat="server" Font-Bold="True" ForeColor="red" Visible="False">(常用版本)</asp:Label>
                                            <asp:CheckBox ID="cb_conttype_c" runat="server" Text="工服" Enabled="false" />
                                            <br />
                                            <asp:RadioButton ID="rb_conttype_uo" runat="server" Text="國外支出(無收入)" Enabled="false" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_ui" runat="server" Text="國內支出(無收入)" Enabled="false" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_bd" runat="server" Text="新創事業" GroupName="ConttypeT" Enabled="false" />
                                            <asp:RadioButton ID="rb_conttype_other" runat="server" Text="其他" GroupName="ConttypeT" Enabled="false" />
                                            <asp:Label ID="txt_class_other_desc" runat="server" Enabled="false"></asp:Label>
                                            <span id="spanContractEdit" runat="server" visible="false">
                                                <br />
                                                <font color="#ff0000">契約修訂
						    <asp:radiobuttonlist id="rblContractEdit" runat="server" repeatlayout="Flow" repeatdirection="Horizontal" Visible="false">
							    <asp:listitem value="1" selected="True">展延</asp:listitem>
							    <asp:listitem value="2">中止</asp:listitem>
							    <asp:listitem value="3">其他</asp:listitem>
						    </asp:radiobuttonlist>
                            <asp:Label id="txtContractEdit" runat="server" width="400px"></asp:Label></font>
                                            </span>
                                            <span style="color: red">
                                                <br />
                                                (若為收入性質契約，請至洽案管理進行相關案件登錄及申請契約需求！)</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約性質</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="LB_ContType" runat="server" Text="Label"></asp:Label>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True" Visible="false">
                                                <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:Label ID="lb_Amend_Show" runat="server" ForeColor="Red" Visible="false">(修約)</asp:Label>
                                            <%--  <asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"  />--%>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">契約預估金額</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="LB_ContMoneyType" runat="server" Text="Label"></asp:Label>
                                            <asp:DropDownList ID="ddlContMoneyType" runat="server" Width="144px" DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false" />
                                            <%-- <asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  " />--%>
                                            <asp:Label ID="txtContMoney" runat="server" />
                                            &nbsp;元/匯率:<asp:Label ID="TB_money_rate" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                契約期間<br />
                                                (預定)
                                            </div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_contsdate" runat="server" />&nbsp;至&nbsp;
                                            <asp:Label ID="txt_contedate" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">契約條件確定日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_confirm_date" runat="server" />
                                            <img src="../images/tooltiphint.gif" class="itemhint" title="契約條件確定日期<hr>1.承辦法務同仁於接案後，會儘快與您討論並確定契約需求條件內容，並據此來研擬適宜契約。<br>2.若於「契約條件確定日期」後再變更契約需求條件內容時，會影響完成時間。" />
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PL_CoPromoter" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">協同承辦人</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:Label ID="txt_px_name" runat="server" Text="" />
                                                <asp:HiddenField ID="h_px_empno" runat="server" />
                                                <asp:Label ID="LB_adm" runat="server" Text=""></asp:Label>
                                                <asp:PlaceHolder ID="PH_rb_adm" runat="server" Visible="false">
                                                    <asp:RadioButton ID="rb_adm_yes" runat="server" Text="同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                                                    <asp:RadioButton ID="rb_adm_no" runat="server" Text="不同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                                                    <font style="color: red; font-weight: bold;"> 　『同意貴單位所指定之業務窗口: <span style="color: red"><asp:Label id="LB_adm_text" runat="server" ForeColor="Red"></asp:Label></span> 君，與您有相同之權限』</font>
                                                </asp:PlaceHolder>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_olderVer" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">簽約緣由與目的</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txtSignReason" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_txtSignReason" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_txtSignReason" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    智權歸屬<br />
                                                    (技術服務/合作開發)<br />
                                                </div>
                                            </td>
                                            <td colspan="3">
                                                <asp:RadioButton ID="rb_ipb_itri" runat="server" Text="本院" GroupName="ipb" Enabled="false"></asp:RadioButton><br />
                                                <asp:RadioButton ID="rb_ipb_coparcenary" runat="server" Text="共有" GroupName="ipb" Enabled="false"></asp:RadioButton>(本院/客戶：
				    <asp:Label ID="txt_ipbi_percent" runat="server"></asp:Label>%&nbsp;/&nbsp;
				    <asp:Label ID="txt_ipbc_percent" runat="server"></asp:Label>%)<br />
                                                <asp:RadioButton ID="rb_ipb_customer" runat="server" Text="客戶" GroupName="ipb" Enabled="false"></asp:RadioButton><br />
                                                <asp:RadioButton ID="rb_ipb_other" runat="server" Text="其他" GroupName="ipb" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_ipb_other_desc" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    智權運用<br />
                                                    <img src="../images/tooltiphint.gif" class="itemhint" title="智權運用<hr>當智權歸屬非本院所有時，應於契約中規範未來雙方之運用方式。<br>請填入客戶及貴單位之主要意見，以作為契約條文研擬參考。" />
                                                </div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txt_ip_apply" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_ip_apply" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_ip_apply" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    收益分享(共有)<br />
                                                    <img src="../images/tooltiphint.gif" class="itemhint" title="收益分享(共有)<hr>當智權歸屬非本院所有時，應於契約中規範未來當雙方再運用且有收益發生時，雙方之分享方式。<br>請填入客戶及貴單位之主要意見，以作為契約條文研擬參考。">
                                                </div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txt_income_divvy" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                                <asp:Image ID="img_income_divvy" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_income_divvy" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">責任範圍 </div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:RadioButton ID="rb_duty_plain" runat="server" Text="計畫經費之" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_plain_budget" runat="server"></asp:Label>
                                                %
                                                <br />
                                                <asp:RadioButton ID="rb_duty_capital" runat="server" Text="最高賠償金額(TWD)" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_capitalsum" runat="server"></asp:Label>
                                                元
                                                <br />
                                                <asp:RadioButton ID="rb_duty_assumpsit" runat="server" Text="賠償客戶之一切損失（無上限賠償）" GroupName="res" Enabled="false"></asp:RadioButton><br />
                                                <asp:RadioButton ID="rb_duty_other" runat="server" Text="其他" GroupName="res" Enabled="false"></asp:RadioButton>
                                                <asp:Label ID="txt_duty_other_desc" runat="server"></asp:Label>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC" runat="server"></asp:PlaceHolder>
                                    <%--               <asp:SqlDataSource ID="SDS_sRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">其他需求</div>
                                        </td>
                                        <td class="lineheight03" colspan="3">
                                            <asp:PlaceHolder ID="PH_oRC_new" runat="server" Visible="false">
                                                <asp:CheckBox ID="rb_other_1" runat="server" Text="本案契約與前案號" Enabled="false" />&nbsp;<asp:TextBox ID="txt_otherrequire_contno" class="inputsizeS" runat="server" Enabled="false" />&nbsp;之契約相同,承辦法務同仁為&nbsp;<asp:TextBox ID="TB_otherrequire_handle_name" class="inputsizeS" runat="server" Enabled="false" /><br />
                                                <asp:CheckBox ID="rb_other_2" runat="server" Text="本案前已與法務同仁" Enabled="false" />&nbsp;<asp:TextBox ID="txt_otherrequire_asked_name" class="inputsizeS" runat="server" Enabled="false" />&nbsp;討論,請分案予前述法務同仁<br />
                                                <asp:CheckBox ID="rb_other_3" runat="server" Text="本案請法務同仁僅提供法律原則意見,毌庸修改契約文字" Enabled="false" /><br />
                                                <asp:CheckBox ID="rb_other_4" runat="server" Text="請法務同仁僅提供本院常用契約(草稿)供參考" Enabled="false" /><br />
                                            </asp:PlaceHolder>
                                            <asp:CheckBox ID="rb_other_T" runat="server" Text="其他。" Enabled="false" />
                                            <asp:TextBox ID="txt_otherrequire_desc" runat="server" Width="530px" TextMode="MultiLine" Height="60px" Enabled="false"></asp:TextBox>
                                            <%--      <asp:SqlDataSource ID="SDS_oRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td colspan="3">
                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="LinkButton1" runat="server" Text=' <%# Server.HtmlDecode(Server.HtmlEncode(Eval("tcdf_doc").ToString())) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="修改概要">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="300px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="常用版本">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_3" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="70px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="撰寫人">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="70px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%--  <asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                                <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btEdit" Text="編輯" Visible="False" OnClick="btEdit_Click"></asp:Button>&nbsp;
	           <asp:Button class="ajax_mesg genbtnS" ID="btnDelete2" runat="server" Text="刪除" Visible="False"></asp:Button>
                            </div>
                        </div>
                        <div class="uplineT1">
                            <span class="gentablenoline">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_modify_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_modify_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_send_date" runat="server"></asp:Label></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- uplineT1 -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc1:Foot runat="server" ID="Foot" />
        <%--<asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
<asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
<asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">
            $(document).ready(function () {
                $(".help_txtSignReason").removeAttr("title");
                $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                $(".help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                $(".help_ip_apply").removeAttr("title");
                $(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                $(".help_ip_apply").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                $(".help_income_divvy").removeAttr("title");
                $(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                $(".help_income_divvy").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });

            });
        </script>

    </form>
</body>
</html>
