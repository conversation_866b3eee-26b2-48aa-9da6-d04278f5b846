﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Case
    /// </summary>
    public class Case : GPI.mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;
        private string _keyword;
        private string _keyindate_from;
        private string _keyindate_to;
        private string _enddate_from;
        private string _enddate_to;
        private string _t1;
        private string _t2;
        private string _empno;
        private string _flag;
        private string _status1;
        private string _status2;
        private string _status3;
        private string _status4;
        private string _status5;

        private int _seqsn;
        private string _gpiyear;
        private string _gpiorgcd;
        private string _class;
        private string _gpisn;
        private string _gpino;
        private string _orgcd;
        private string _type;
        private string _custno;
        private string _custname;
        private string _custtype;
        private string _role;
        private string _gpiname;
        private string _no;
        private string _no_old;
        private int _amt;
        private string _enddate;
        private string _curr;
        private string _seqno;
        private string _sdate;
        private string _edate;
        private int _payamt;
        private string _receive;
        private string _bid;
        private string _unlimit_liability;
        private string _contman;
        private string _conttel;
        private string _online;
        private string _deposit;
        private int _depositamt;
        private string _deposittype;
        private string _promodept;
        private string _promoempno;
        private string _promoempname;
        private string _dodept;
        private string _doempno;
        private string _doempname;
        private string _resource;
        private string _plantype;
        private string _planmark;
        private string _planyear;
        private string _plantotyear;
        private string _continue;
        private string _contno;
        private string _lastgpino;
        private string _status;
        private string _transfer;
        private string _memo;
        private string _stampdate;
        private string _treaty_flag;
        private string _old_flag;
        private string _join;
        private string _confidential;
        private string _mail_president;
        private string _secret_reason;
        private DateTime _del_date;
        private string _check;
        private string _award;
        //20200825 IRENE add 最低標 ，比價、議價
        private string _lowestbid;
        private int _ver;
        //added by cary：協同規劃人
        private string _coplaner;
        private string _coplaner_name;


        //add by 870259 解密日期/ 不解密原因

        private string _decrypt_flag;
        private string _decrypt_txt;
        private string _ndecrypt_date;

        //add by 940532 重案與否
        private string _isDup;

        //add by 940532 代表本院投標
        private string _isRep;

        //end added

        //共同投標檔
        private int sub_id;
        private string sub_splyno;
        private string sub_splyname;
        private string sub_mark;
        private string sub_custtype;
        private int sub_amt;
        private string sub_contman;
        private string sub_conttel;


        //分包投標檔
        private int bid_id;
        private string bid_splyno;
        private string bid_splyname;
        private string bid_mark;
        private string bid_custtype;
        private float bid_qurate;

        //規劃構想
        private int plan_id;
        private int plan_ver;
        private string plan_tcehtype1;
        private string plan_tcehtype2;
        private string plan_industype1;
        private string plan_industype2;
        private string plan_keyword;
        private string plan_website;
        private string plan_online;
        private string plan_no;
        private string plan_quname;
        private string plan_mark;
        private string plan_custtype;
        private string plan_memo;

        //與院內其它單位共同執行
        private int join_id;
        private string join_orgcd;
        private string join_orgcdnm;
        private string join_empno;
        private string join_empname;
        private string join_memo;

        //案件管理
        private string _chk_signdate;
        private string _chk_result;
        private string _chk_sign_memo;
        private string _chk_review_empno;
        private string _chk_review_date;
        private string _chk_review_result;
        private string _chk_review_memo;
        private string _chk_signempno;

        private string _keyinempno;
        private string _keyinempname;
        private string _keyindate;
        private string _modempno;
        private string _modempname;
        private string _moddate;
        private string _modify;
        private string _delempno;
        private string _delempname;
        private string _delete;

        private string _save;
        #endregion

        #region 建構子

        public Case()
        {
            _errorMessage = String.Empty;
            _keyword = String.Empty;
            _keyindate_from = String.Empty;
            _keyindate_to = String.Empty;
            _enddate_from = String.Empty;
            _enddate_to = String.Empty;
            _t1 = String.Empty;
            _t2 = String.Empty;
            _empno = String.Empty;
            _flag = String.Empty;
            _status1 = String.Empty;
            _status2 = String.Empty;
            _status3 = String.Empty;
            _status4 = String.Empty;
            _status5 = String.Empty;

            _seqsn = 0;
            _gpiyear = String.Empty;
            _gpiorgcd = String.Empty;
            _class = String.Empty;
            _gpisn = String.Empty;
            _gpino = String.Empty;
            _orgcd = String.Empty;
            _type = String.Empty;
            _custno = String.Empty;
            _custname = String.Empty;
            _custtype = String.Empty;
            _role = String.Empty;
            _gpiname = String.Empty;
            _no = String.Empty;
            _no_old = String.Empty;
            _amt = 0;
            _enddate = String.Empty;
            _curr = String.Empty;
            _seqno = String.Empty;
            _sdate = String.Empty;
            _edate = String.Empty;
            _payamt = 0;
            _receive = String.Empty;
            _bid = String.Empty;
            _unlimit_liability = String.Empty;
            _contman = String.Empty;
            _conttel = String.Empty;
            _online = String.Empty;
            _deposit = "N";
            _depositamt = 0;
            _deposittype = String.Empty;
            _promodept = String.Empty;
            _promoempno = String.Empty;
            _promoempname = String.Empty;
            _dodept = String.Empty;
            _doempno = String.Empty;
            _doempname = String.Empty;
            _resource = String.Empty;
            _plantype = String.Empty;
            _planmark = String.Empty;
            _planyear = String.Empty;
            _plantotyear = String.Empty;
            _continue = String.Empty;
            _contno = String.Empty;
            _lastgpino = String.Empty;
            _status = String.Empty;
            _transfer = String.Empty;
            _memo = String.Empty;
            _stampdate = String.Empty;
            _treaty_flag = String.Empty;
            _old_flag = String.Empty;
            _join = String.Empty;
            _confidential = String.Empty;
            _mail_president = String.Empty;
            _secret_reason = String.Empty;
            _del_date = DateTime.Now;
            _check = String.Empty;
            _award = String.Empty;
            _lowestbid = String.Empty;//20200825 IRENE 最低標 ，比價 、 議價
            _ver = 0;
            _coplaner = String.Empty;
            _coplaner_name = String.Empty;

            //20190115 870259 add
            _decrypt_flag = String.Empty;
            _decrypt_txt = String.Empty;
            _ndecrypt_date = String.Empty;

            //共同投標檔
            sub_id = 0;
            sub_splyno = String.Empty;
            sub_splyname = String.Empty;
            sub_mark = String.Empty;
            sub_custtype = String.Empty;
            sub_amt = 0;
            sub_contman = String.Empty;
            sub_conttel = String.Empty;

            //分包投標檔
            bid_id = 0;
            bid_splyno = String.Empty;
            bid_splyname = String.Empty;
            bid_mark = String.Empty;
            bid_custtype = String.Empty;
            bid_qurate = 0;

            //規劃構想
            plan_id = 0;
            plan_ver = 0;
            plan_tcehtype1 = String.Empty;
            plan_tcehtype2 = String.Empty;
            plan_industype1 = String.Empty;
            plan_industype2 = String.Empty;
            plan_keyword = String.Empty;
            plan_website = String.Empty;
            plan_online = String.Empty;
            plan_no = String.Empty;
            plan_quname = String.Empty;
            plan_mark = String.Empty;
            plan_custtype = String.Empty;
            plan_memo = String.Empty;

            //與院內其它單位共同執行
            plan_id = 0;
            join_orgcd = String.Empty;
            join_orgcdnm = String.Empty;
            join_empno = String.Empty;
            join_empname = String.Empty;
            join_memo = String.Empty;

            //案件管理
            _chk_signdate = String.Empty;
            _chk_result = String.Empty;
            _chk_sign_memo = String.Empty;
            _chk_review_empno = String.Empty;
            _chk_review_date = String.Empty;
            _chk_review_result = String.Empty;
            _chk_review_memo = String.Empty;
            _chk_signempno = String.Empty;

            _keyinempno = String.Empty;
            _keyinempname = String.Empty;
            _keyindate = String.Empty;
            _modempno = String.Empty;
            _modempname = String.Empty;
            _moddate = String.Empty;
            _modify = String.Empty;
            _delempno = String.Empty;
            _delempname = String.Empty;
            _delete = String.Empty;

            _save = String.Empty;
        }

        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 關鍵字
        /// </summary>
        public string Keyword
        {
            get { return _keyword; }
            set { _keyword = value; }
        }

        /// <summary>
        /// 登錄日期(起)
        /// </summary>
        public string KeyinDate_From
        {
            get { return _keyindate_from; }
            set { _keyindate_from = value; }
        }

        /// <summary>
        /// 登錄日期(迄)
        /// </summary>
        public string KeyinDate_To
        {
            get { return _keyindate_to; }
            set { _keyindate_to = value; }
        }

        /// <summary>
        /// 截標日期(起)
        /// </summary>
        public string EndDate_From
        {
            get { return _enddate_from; }
            set { _enddate_from = value; }
        }

        /// <summary>
        /// 截標日期(迄)
        /// </summary>
        public string EndDate_To
        {
            get { return _enddate_to; }
            set { _enddate_to = value; }
        }

        /// <summary>
        /// T1
        /// </summary>
        public string T1
        {
            get { return _t1; }
            set { _t1 = value; }
        }

        /// <summary>
        /// T2
        /// </summary>
        public string T2
        {
            get { return _t2; }
            set { _t2 = value; }
        }

        /// <summary>
        /// 登入人員
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        /// <summary>
        /// Y 產服中心   N單位業管
        /// </summary>
        public string Flag
        {
            get { return _flag; }
            set { _flag = value; }
        }

        /// <summary>
        /// 未完成會簽
        /// </summary>
        public string Status1
        {
            get { return _status1; }
            set { _status1 = value; }
        }
        /// <summary>
        /// 未結件
        /// </summary>
        public string Status2
        {
            get { return _status2; }
            set { _status2 = value; }
        }
        /// <summary>
        /// 未投標
        /// </summary>
        public string Status3
        {
            get { return _status3; }
            set { _status3 = value; }
        }
        /// <summary>
        /// 未填寫追蹤
        /// </summary>
        public string Status4
        {
            get { return _status4; }
            set { _status4 = value; }
        }
        /// <summary>
        /// 策略性退出標案
        /// </summary>
        public string Status5
        {
            get { return _status5; }
            set { _status5 = value; }
        }

        #region 基本資料
        /// <summary>
        /// 流水號
        /// </summary>
        public int Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }

        /// <summary>
        /// 規劃年度
        /// </summary>
        public string GpiYear
        {
            get { return _gpiyear; }
            set { _gpiyear = value; }
        }

        /// <summary>
        /// 規劃單位
        /// </summary>
        public string GpiOrgcd
        {
            get { return _gpiorgcd; }
            set { _gpiorgcd = value; }
        }

        /// <summary>
        /// 規劃案號-系統
        /// </summary>
        public string Class
        {
            get { return _class; }
            set { _class = value; }
        }
        /// <summary>
        /// 規劃案號-序號
        /// </summary>
        public string GpiSn
        {
            get { return _gpisn; }
            set { _gpisn = value; }
        }
        /// <summary>
        /// 規劃案號
        /// </summary>
        public string GpiNo
        {
            get { return _gpino; }
            set { _gpino = value; }
        }
        /// <summary>
        /// 領標單位
        /// </summary>
        public string Orgcd
        {
            get { return _orgcd; }
            set { _orgcd = value; }
        }
        /// <summary>
        /// 標案類別
        /// </summary>
        public string Type
        {
            get { return _type; }
            set { _type = value; }
        }
        /// <summary>
        /// 招標單位
        /// </summary>
        public string CustNo
        {
            get { return _custno; }
            set { _custno = value; }
        }
        /// <summary>
        /// 招標單位名稱
        /// </summary>
        public string CustName
        {
            get { return _custname; }
            set { _custname = value; }
        }
        /// <summary>
        /// 招標單位屬性
        /// </summary>
        public string CustType
        {
            get { return _custtype; }
            set { _custtype = value; }
        }
        /// <summary>
        /// 本院角色
        /// </summary>
        public string Role
        {
            get { return _role; }
            set { _role = value; }
        }
        /// <summary>
        /// 標案名稱
        /// </summary>
        public string GpiName
        {
            get { return _gpiname; }
            set { _gpiname = value; }
        }
        /// <summary>
        /// 標案案號
        /// </summary>
        public string No
        {
            get { return _no; }
            set { _no = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string No_Old
        {
            get { return _no_old; }
            set { _no_old = value; }
        }
        /// <summary>
        /// 招標金額
        /// </summary>
        public int Amt
        {
            get { return _amt; }
            set { _amt = value; }
        }
        /// <summary>
        /// 截標日期
        /// </summary>
        public string EndDate
        {
            get { return _enddate; }
            set { _enddate = value; }
        }
        /// <summary>
        /// 核心業務別
        /// </summary>
        public string Curr
        {
            get { return _curr; }
            set { _curr = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string SeqNo
        {
            get { return _seqno; }
            set { _seqno = value; }
        }
        /// <summary>
        /// 執行期間(起)
        /// </summary>
        public string SDate
        {
            get { return _sdate; }
            set { _sdate = value; }
        }
        /// <summary>
        /// 執行期間(迄)
        /// </summary>
        public string EDate
        {
            get { return _edate; }
            set { _edate = value; }
        }
        /// <summary>
        /// 電子領標繳費金額
        /// </summary>
        public int PayAmt
        {
            get { return _payamt; }
            set { _payamt = value; }
        }
        /// <summary>
        /// 招標方式
        /// </summary>
        public string Receive
        {
            get { return _receive; }
            set { _receive = value; }
        }
        /// <summary>
        /// 投標方式
        /// </summary>
        public string Bid
        {
            get { return _bid; }
            set { _bid = value; }
        }
        /// <summary>
        /// 未約定本院責任上限
        /// </summary>
        public string Unlimit_liability
        {
            get { return _unlimit_liability; }
            set { _unlimit_liability = value; }
        }

        /// <summary>
        /// 聯絡人
        /// </summary>
        public string ContMan
        {
            get { return _contman; }
            set { _contman = value; }
        }
        /// <summary>
        /// 聯絡人電話
        /// </summary>
        public string ContTel
        {
            get { return _conttel; }
            set { _conttel = value; }
        }
        /// <summary>
        /// 線上議比價
        /// </summary>
        public string Online
        {
            get { return _online; }
            set { _online = value; }
        }
        /// <summary>
        /// 押標金申請
        /// </summary>
        public string Deposit
        {
            get { return _deposit; }
            set { _deposit = value; }
        }
        /// <summary>
        /// 押標金額
        /// </summary>
        public int DepositAmt
        {
            get { return _depositamt; }
            set { _depositamt = value; }
        }
        /// <summary>
        /// 押標金方式
        /// </summary>
        public string DepositType
        {
            get { return _deposittype; }
            set { _deposittype = value; }
        }
        /// <summary>
        /// 本院投標業務推廣人部門
        /// </summary>
        public string PromoDept
        {
            get { return _promodept; }
            set { _promodept = value; }
        }
        /// <summary>
        /// 本院投標業務推廣人工號
        /// </summary>
        public string PromoEmpNo
        {
            get { return _promoempno; }
            set { _promoempno = value; }
        }
        /// <summary>
        /// 本院投標業務推廣人姓名
        /// </summary>
        public string PromoEmpName
        {
            get { return _promoempname; }
            set { _promoempname = value; }
        }
        /// <summary>
        /// 標案規劃部門
        /// </summary>
        public string DoDept
        {
            get { return _dodept; }
            set { _dodept = value; }
        }
        /// <summary>
        /// 標案規劃人工號
        /// </summary>
        public string DoEmpNo
        {
            get { return _doempno; }
            set { _doempno = value; }
        }
        /// <summary>
        /// 標案規劃人姓名
        /// </summary>
        public string DoEmpName
        {
            get { return _doempname; }
            set { _doempname = value; }
        }

        /// <summary>
        /// 本院標案分類
        /// </summary>
        public string Resource
        {
            get { return _resource; }
            set { _resource = value; }
        }

        /// <summary>
        /// 工程會標的分類
        /// </summary>
        public string PlanType
        {
            get { return _plantype; }
            set { _plantype = value; }
        }
        /// <summary>
        /// 是否為全程計畫(Y/N)
        /// </summary>
        public string PlanMark
        {
            get { return _planmark; }
            set { _planmark = value; }
        }
        /// <summary>
        /// 全程計畫之第年
        /// </summary>
        public string PlanYear
        {
            get { return _planyear; }
            set { _planyear = value; }
        }
        /// <summary>
        /// 計畫總數年
        /// </summary>
        public string PlanTotYear
        {
            get { return _plantotyear; }
            set { _plantotyear = value; }
        }
        /// <summary>
        /// 是否為延續性計畫
        /// </summary>
        public string Continue
        {
            get { return _continue; }
            set { _continue = value; }
        }
        /// <summary>
        /// 延續的契約編號
        /// </summary>
        public string ContNo
        {
            get { return _contno; }
            set { _contno = value; }
        }

        /// <summary>
        /// 延續的規劃案號
        /// </summary>
        public string LastGpiNo
        {
            get { return _lastgpino; }
            set { _lastgpino = value; }
        }
        /// <summary>
        /// 進度
        /// </summary>
        public string Status
        {
            get { return _status; }
            set { _status = value; }
        }
        /// <summary>
        /// 案件移轉處理
        /// </summary>
        public string Transfer
        {
            get { return _transfer; }
            set { _transfer = value; }
        }
        /// <summary>
        /// 備註
        /// </summary>
        public string Memo
        {
            get { return _memo; }
            set { _memo = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string StampDate
        {
            get { return _stampdate; }
            set { _stampdate = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Treaty_flag
        {
            get { return _treaty_flag; }
            set { _treaty_flag = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Old_flag
        {
            get { return _old_flag; }
            set { _old_flag = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Join
        {
            get { return _join; }
            set { _join = value; }
        }
        /// <summary>
        /// 機密等級
        /// </summary>
        public string Confidential
        {
            get { return _confidential; }
            set { _confidential = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Mail_President
        {
            get { return _mail_president; }
            set { _mail_president = value; }
        }
        /// <summary>
        /// 極機密理由
        /// </summary>
        public string Secret_reason
        {
            get { return _secret_reason; }
            set { _secret_reason = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime Del_Date
        {
            get { return _del_date; }
            set { _del_date = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Check
        {
            get { return _check; }
            set { _check = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Award
        {
            get { return _award; }
            set { _award = value; }
        }
        /// <summary>
        /// 20200825 IRENE ADD 最低標，比價、議價
        /// </summary>
        public string Lowestbid
        {
            get { return _lowestbid; }
            set { _lowestbid = value; }
        }
        /// <summary>
        /// 版本
        /// </summary>
        public int Ver
        {
            get { return _ver; }
            set { _ver = value; }
        }
        /// <summary>
        /// 協同規劃人
        /// </summary>
        public string Coplaner_name
        {
            get { return _coplaner_name; }
            set { _coplaner_name = value; }
        }
        /// <summary>
        /// 協同規劃人工號
        /// </summary>
        public string Coplaner
        { 
            get { return _coplaner; }
            set { _coplaner = value; }
        }


        /// <summary>
        /// 解密Y/N
        /// </summary>
        public string decrypt
        {
            get { return _decrypt_flag; }
            set { _decrypt_flag = value; }
        }


        /// <summary>
        /// 解密日期
        /// </summary>
        public string ndecrypt_date
        {
            get { return _ndecrypt_date; }
            set { _ndecrypt_date = value; }
        }

        /// <summary>
        /// 不解密原因
        /// </summary>
        public string decrypt_txt
        {
            get { return _decrypt_txt; }
            set { _decrypt_txt = value; }
        }

        /// <summary>
        /// 重案與否
        /// </summary>
        public string isDup
        {
            get { return _isDup; }
            set { _isDup = value; }

        }

        public string isRep
        {
            get { return _isRep; }
            set { _isRep = value; }
        }

        #endregion

        #region 共同投標檔
        /// <summary>
        /// 
        /// </summary>
        public int Sub_id
        {
            get { return sub_id; }
            set { sub_id = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_splyno
        {
            get { return sub_splyno; }
            set { sub_splyno = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_splyname
        {
            get { return sub_splyname; }
            set { sub_splyname = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_mark
        {
            get { return sub_mark; }
            set { sub_mark = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_custtype
        {
            get { return sub_custtype; }
            set { sub_custtype = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int Sub_amt
        {
            get { return sub_amt; }
            set { sub_amt = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_contman
        {
            get { return sub_contman; }
            set { sub_contman = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Sub_conttel
        {
            get { return sub_conttel; }
            set { sub_conttel = value; }
        }
        #endregion

        #region 分包投標檔
        /// <summary>
        /// 
        /// </summary>
        public int Bid_id
        {
            get { return bid_id; }
            set { bid_id = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Bid_splyno
        {
            get { return bid_splyno; }
            set { bid_splyno = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Bid_splyname
        {
            get { return bid_splyname; }
            set { bid_splyname = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Bid_mark
        {
            get { return bid_mark; }
            set { bid_mark = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Bid_custtype
        {
            get { return bid_custtype; }
            set { bid_custtype = value; }
        }
        /// <summary>
        /// 
        /// </summary>
        public float Bid_qurate
        {
            get { return bid_qurate; }
            set { bid_qurate = value; }
        }
        #endregion

        #region 規劃構想
        /// <summary>
        /// 識別號
        /// </summary>
        public int Plan_id
        {
            get { return plan_id; }
            set { plan_id = value; }
        }
        /// <summary>
        /// 規劃構想版本
        /// </summary>
        public int Plan_ver
        {
            get { return plan_ver; }
            set { plan_ver = value; }
        }

        /// <summary>
        /// 技術分類1
        /// </summary>
        public string Plan_tcehtype1
        {
            get { return plan_tcehtype1; }
            set { plan_tcehtype1 = value; }
        }

        /// <summary>
        /// 技術分類2
        /// </summary>
        public string Plan_tcehtype2
        {
            get { return plan_tcehtype2; }
            set { plan_tcehtype2 = value; }
        }

        /// <summary>
        /// 產業別1
        /// </summary>
        public string Plan_industype1
        {
            get { return plan_industype1; }
            set { plan_industype1 = value; }
        }

        /// <summary>
        /// 產業別2
        /// </summary>
        public string Plan_industype2
        {
            get { return plan_industype2; }
            set { plan_industype2 = value; }
        }

        /// <summary>
        /// 中英文關鍵字
        /// </summary>
        public string Plan_keyword
        {
            get { return plan_keyword; }
            set { plan_keyword = value; }
        }

        /// <summary>
        /// 本計畫是否須接受業主委託建置網站(Y: 是, N: 否)
        /// </summary>
        public string Plan_website
        {
            get { return plan_website; }
            set { plan_website = value; }
        }

        /// <summary>
        /// 網站預計上線日
        /// </summary>
        public string Plan_online
        {
            get { return plan_online; }
            set { plan_online = value; }
        }

        /// <summary>
        /// 背景說明
        /// </summary>
        public string Plan_no
        {
            get { return plan_no; }
            set { plan_no = value; }
        }

        /// <summary>
        /// 工作說明
        /// </summary>
        public string Plan_quname
        {
            get { return plan_quname; }
            set { plan_quname = value; }
        }

        /// <summary>
        /// 發展效益
        /// </summary>
        public string Plan_mark
        {
            get { return plan_mark; }
            set { plan_mark = value; }
        }

        /// <summary>
        /// 競標企業說明
        /// </summary>
        public string Plan_custtype
        {
            get { return plan_custtype; }
            set { plan_custtype = value; }
        }

        /// <summary>
        /// 規劃構想備註
        /// </summary>
        public string Plan_memo
        {
            get { return plan_memo; }
            set { plan_memo = value; }
        }
        #endregion

        #region 與院內其它單位共同執行
        /// <summary>
        /// 識別號
        /// </summary>
        public int Join_id
        {
            get { return join_id; }
            set { join_id = value; }
        }

        /// <summary>
        /// 單位代碼
        /// </summary>
        public string Join_orgcd
        {
            get { return join_orgcd; }
            set { join_orgcd = value; }
        }

        /// <summary>
        /// 單位名稱(ex:17-資科)
        /// </summary>
        public string Join_orgcdnm
        {
            get { return join_orgcdnm; }
            set { join_orgcdnm = value; }
        }

        /// <summary>
        /// 負責人工號
        /// </summary>
        public string Join_empno
        {
            get { return join_empno; }
            set { join_empno = value; }
        }

        /// <summary>
        /// 負責人姓名
        /// </summary>
        public string Join_empname
        {
            get { return join_empname; }
            set { join_empname = value; }
        }

        /// <summary>
        /// 說明
        /// </summary>
        public string Join_memo
        {
            get { return join_memo; }
            set { join_memo = value; }
        }
        #endregion

        #region 案件管理
        /// <summary>
        /// 簽核日期
        /// </summary>
        public string Chk_signdate
        {
            get { return _chk_signdate; }
            set { _chk_signdate = value; }
        }
        /// <summary>
        /// 簽核結果
        /// </summary>
        public string Chk_result
        {
            get { return _chk_result; }
            set { _chk_result = value; }
        }
        /// <summary>
        /// 簽核備註
        /// </summary>
        public string Chk_sign_memo
        {
            get { return _chk_sign_memo; }
            set { _chk_sign_memo = value; }
        }
        /// <summary>
        /// 核定人員
        /// </summary>
        public string Chk_review_empno
        {
            get { return _chk_review_empno; }
            set { _chk_review_empno = value; }
        }
        /// <summary>
        /// 核定日期
        /// </summary>
        public string Chk_review_date
        {
            get { return _chk_review_date; }
            set { _chk_review_date = value; }
        }
        /// <summary>
        /// 核定結果
        /// </summary>
        public string Chk_review_result
        {
            get { return _chk_review_result; }
            set { _chk_review_result = value; }
        }
        /// <summary>
        /// 核定意見
        /// </summary>
        public string Chk_review_memo
        {
            get { return _chk_review_memo; }
            set { _chk_review_memo = value; }
        }
        /// <summary>
        /// 簽核人員
        /// </summary>
        public string Chk_signempno
        {
            get { return _chk_signempno; }
            set { _chk_signempno = value; }
        }


        #endregion

        /// <summary>
        /// 填寫人工號
        /// </summary>
        public string KeyinEmpNo
        {
            get { return _keyinempno; }
            set { _keyinempno = value; }
        }
        /// <summary>
        /// 填寫人姓名
        /// </summary>
        public string KeyinEmpName
        {
            get { return _keyinempname; }
            set { _keyinempname = value; }
        }
        /// <summary>
        /// 填寫日期
        /// </summary>
        public string KeyinDate
        {
            get { return _keyindate; }
            set { _keyindate = value; }
        }
        /// <summary>
        /// 修改人工號
        /// </summary>
        public string ModEmpNo
        {
            get { return _modempno; }
            set { _modempno = value; }
        }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModEmpName
        {
            get { return _modempname; }
            set { _modempname = value; }
        }
        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModDate
        {
            get { return _moddate; }
            set { _moddate = value; }
        }
        /// <summary>
        /// 是否可修改Y. 是 N. 否, Default:Y
        /// </summary>
        public string Modify
        {
            get { return _modify; }
            set { _modify = value; }
        }
        /// <summary>
        /// 刪除人工號
        /// </summary>
        public string DelEmpNo
        {
            get { return _delempno; }
            set { _delempno = value; }
        }
        /// <summary>
        /// 刪除人姓名
        /// </summary>
        public string DelEmpName
        {
            get { return _delempname; }
            set { _delempname = value; }
        }
        /// <summary>
        /// 刪除註記(Default:N)
        /// </summary>
        public string Main_Delete
        {
            get { return _delete; }
            set { _delete = value; }
        }

        /// <summary>
        ///     草稿N  儲存Y
        /// </summary>
        public string Plan_Save
        {
            get { return _save; }
            set { _save = value; }
        }
        #endregion

        #region 公有函式
        public bool Delete()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_seqsn_del";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@empno", _empno);

            try
            {
                this.Execute2(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public DataTable Get()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_casequery";

            oCmd.Parameters.AddWithValue("@main_gpiorgcd", _gpiorgcd);
            oCmd.Parameters.AddWithValue("@main_status", _status);
            oCmd.Parameters.AddWithValue("@main_custno", _custno);
            oCmd.Parameters.AddWithValue("@main_promoempname", _promoempname);
            oCmd.Parameters.AddWithValue("@main_doempname", _doempname);
            oCmd.Parameters.AddWithValue("@main_type", _type);
            oCmd.Parameters.AddWithValue("@main_gpiname", _gpiname);
            oCmd.Parameters.AddWithValue("@main_keyindate_from", _keyindate_from);
            oCmd.Parameters.AddWithValue("@main_keyindate_to", _keyindate_to);
            oCmd.Parameters.AddWithValue("@main_enddate_from", _enddate_from);
            oCmd.Parameters.AddWithValue("@main_enddate_to", _enddate_to);
            oCmd.Parameters.AddWithValue("@main_gpino", _gpino);
            oCmd.Parameters.AddWithValue("@main_no", _no);
            oCmd.Parameters.AddWithValue("@keyword", _keyword);
            oCmd.Parameters.AddWithValue("@t1", _t1);
            oCmd.Parameters.AddWithValue("@t2", _t2);
            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@PageSize", PageSize);
            oCmd.Parameters.AddWithValue("@CurrentPage", CurrentPage);
            oCmd.Parameters.AddWithValue("@SortFields", SortFields);
            oCmd.Parameters.AddWithValue("@SortDirection", SortDirection);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// 取得標案明細
        /// </summary>
        /// <returns></returns>
        public DataTable GetDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_select_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@main_ver", _ver);

            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得標案權限(00:無權限、10:可讀、11:可讀寫)
        /// </summary>
        /// <returns></returns>
        public string GetRight()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_right_rw";

            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@seqno", _seqsn);
            SqlParameter rtn_code = oCmd.Parameters.Add("@rtn_code", SqlDbType.VarChar, 5);
            rtn_code.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_code.Value.ToString();
        }
        public string GetSeqsn()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select top 1 main_seqsn from gpi_main where main_gpino = @gpino";

            oCmd.Parameters.AddWithValue("@gpino", _gpino);

            return this.getTopOne(oCmd, CommandType.Text);
            
        }
        #region 共同投標
        /// <summary>
        /// 共同投標廠商名單及占契約金額比例
        /// </summary>
        /// <returns></returns>
        public DataTable GetBid()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_bid_select_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@ver", _ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 刪除共同投標檔
        /// </summary>
        /// <returns></returns>
        public bool DeleteBid()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"Delete gpi_bid WHERE bid_seqsn=@main_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            if (bid_splyno != string.Empty)
            {
                oCmd.CommandText += " and bid_splyno = @bid_splyno";
                oCmd.Parameters.AddWithValue("@bid_splyno", bid_splyno);
            }
            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 儲存共同投標檔
        /// </summary>
        /// <returns></returns>
        public bool SaveBid()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_bid_update";
            oCmd.Parameters.AddWithValue("@bid_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@bid_splyno", bid_splyno);
            oCmd.Parameters.AddWithValue("@bid_splyname", bid_splyname);
            oCmd.Parameters.AddWithValue("@bid_mark", bid_mark);
            oCmd.Parameters.AddWithValue("@bid_custtype", bid_custtype);
            oCmd.Parameters.AddWithValue("@bid_qurate", bid_qurate);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        #region 分包投標
        /// <summary>
        /// 分包廠商
        /// </summary>
        /// <returns></returns>
        public DataTable GetSub()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sub_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            //歷史版本
            if (_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@ver", _ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 刪除分包投標檔
        /// </summary>
        /// <returns></returns>
        public bool DeleteSub()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"delete gpi_sub WHERE sub_seqsn=@main_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 儲存分包投標檔
        /// </summary>
        /// <returns></returns>
        public bool SaveSub()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_sub_update";
            oCmd.Parameters.AddWithValue("@sub_id", sub_id);
            oCmd.Parameters.AddWithValue("@sub_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@sub_splyno", sub_splyno);
            oCmd.Parameters.AddWithValue("@sub_splyname", sub_splyname);
            oCmd.Parameters.AddWithValue("@sub_custtype", sub_custtype);
            oCmd.Parameters.AddWithValue("@sub_amt", sub_amt);
            oCmd.Parameters.AddWithValue("@sub_contman", sub_contman);
            oCmd.Parameters.AddWithValue("@sub_conttel", sub_conttel);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        /// <summary>
        /// 重案檢查
        /// </summary>
        /// <returns></returns>
        public bool Dupl_Check()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_dupl_check";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_gpino", _gpino);
            oCmd.Parameters.AddWithValue("@main_orgcd", _orgcd);
            oCmd.Parameters.AddWithValue("@main_custno", _custno);
            oCmd.Parameters.AddWithValue("@main_gpiname", _gpiname);
            oCmd.Parameters.AddWithValue("@main_no", _no);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }



        public bool Save()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_main_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_gpiyear", _gpiyear);
            oCmd.Parameters.AddWithValue("@main_gpiorgcd", _gpiorgcd);
            oCmd.Parameters.AddWithValue("@main_class", _class);
            oCmd.Parameters.AddWithValue("@main_gpisn", _gpisn);
            oCmd.Parameters.AddWithValue("@main_gpino", _gpino);
            oCmd.Parameters.AddWithValue("@main_orgcd", _orgcd);
            oCmd.Parameters.AddWithValue("@main_type", _type);
            oCmd.Parameters.AddWithValue("@main_custno", _custno);
            oCmd.Parameters.AddWithValue("@main_custname", _custname);
            oCmd.Parameters.AddWithValue("@main_custtype", _custtype);
            oCmd.Parameters.AddWithValue("@main_role", _role);
            oCmd.Parameters.AddWithValue("@main_gpiname", _gpiname);
            oCmd.Parameters.AddWithValue("@main_no", _no);
            oCmd.Parameters.AddWithValue("@main_amt", _amt);
            oCmd.Parameters.AddWithValue("@main_enddate", _enddate);
            oCmd.Parameters.AddWithValue("@main_curr", _curr);
            oCmd.Parameters.AddWithValue("@main_seqno", _seqno);
            oCmd.Parameters.AddWithValue("@main_sdate", _sdate);
            oCmd.Parameters.AddWithValue("@main_edate", _edate);
            oCmd.Parameters.AddWithValue("@main_payamt", _payamt);
            oCmd.Parameters.AddWithValue("@main_receive", _receive);
            oCmd.Parameters.AddWithValue("@main_bid", _bid);
            oCmd.Parameters.AddWithValue("@main_unlimit_liability", _unlimit_liability);
            oCmd.Parameters.AddWithValue("@main_contman", _contman);
            oCmd.Parameters.AddWithValue("@main_conttel", _conttel);
            oCmd.Parameters.AddWithValue("@main_online", _online);
            oCmd.Parameters.AddWithValue("@main_deposit", _deposit);
            oCmd.Parameters.AddWithValue("@main_depositamt", _depositamt);
            oCmd.Parameters.AddWithValue("@main_deposittype", _deposittype);
            oCmd.Parameters.AddWithValue("@main_promoempname", _promoempname);
            oCmd.Parameters.AddWithValue("@main_promoempno", _promoempno);
            oCmd.Parameters.AddWithValue("@main_promodept", _promodept);
            oCmd.Parameters.AddWithValue("@main_dodept", _dodept);
            oCmd.Parameters.AddWithValue("@main_doempname", _doempname);
            oCmd.Parameters.AddWithValue("@main_doempno", _doempno);
            oCmd.Parameters.AddWithValue("@main_resource", _resource);
            oCmd.Parameters.AddWithValue("@main_plantype", _plantype);
            oCmd.Parameters.AddWithValue("@main_planmark", _planmark);
            oCmd.Parameters.AddWithValue("@main_planyear", _planyear);
            oCmd.Parameters.AddWithValue("@main_plantotyear", _plantotyear);
            oCmd.Parameters.AddWithValue("@main_continue", _continue);
            oCmd.Parameters.AddWithValue("@main_contno", _contno);
            oCmd.Parameters.AddWithValue("@main_status", _status);
            oCmd.Parameters.AddWithValue("@main_transfer", _transfer);
            oCmd.Parameters.AddWithValue("@main_memo", _memo);
            oCmd.Parameters.AddWithValue("@main_keyinempno", _keyinempno);
            oCmd.Parameters.AddWithValue("@main_keyinempname", _keyinempname);
            oCmd.Parameters.AddWithValue("@main_keyindate", _keyindate);
            oCmd.Parameters.AddWithValue("@main_lastgpino", _lastgpino);
            oCmd.Parameters.AddWithValue("@main_confidential", _confidential);
            oCmd.Parameters.AddWithValue("@main_secret_reason", _secret_reason);
            oCmd.Parameters.AddWithValue("@main_modempno", _modempno);
            oCmd.Parameters.AddWithValue("@main_modempname", _modempname);
            oCmd.Parameters.AddWithValue("@main_moddate", _moddate);
            oCmd.Parameters.AddWithValue("@main_award", _award);
            //20200825 IRENE add 最低標 ， 比價、議價
            oCmd.Parameters.AddWithValue("@main_award_lowest", _lowestbid);
            oCmd.Parameters.AddWithValue("@main_coplaner", _coplaner);
            oCmd.Parameters.AddWithValue("@main_coplaner_name", _coplaner_name);

            //add by 870259 @main_decrypt_flag varchar(1)=null,@main_decrypt_txt nvarchar(300)=null,@main_ndecrypt_date varchar(8)=null

            oCmd.Parameters.AddWithValue("@main_decrypt_flag", _decrypt_flag);
            oCmd.Parameters.AddWithValue("@main_decrypt_txt", _decrypt_txt);
            oCmd.Parameters.AddWithValue("@main_ndecrypt_date", _ndecrypt_date);


            try
            {
                _seqsn = int.Parse(this.getTopOne2(oCmd, CommandType.StoredProcedure));
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 延續的規劃案號檢查
        /// </summary>
        /// <returns></returns>
        public string GpiNo_Check()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_check_gpino";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_gpino", _gpino);

            return this.getTopOne(oCmd, CommandType.StoredProcedure);

        }

        #region 規劃構想

        /// <summary>
        /// 取得規劃構想
        /// </summary>
        /// <returns></returns>
        public DataTable GetPlan()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_plan_select_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            //歷史版本
            if (plan_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@ver", plan_ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 儲存規劃構想
        /// </summary>
        /// <returns></returns>
        public bool SavePlan()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_plan_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@plan_no", plan_no);
            oCmd.Parameters.AddWithValue("@plan_quname", plan_quname);
            oCmd.Parameters.AddWithValue("@plan_mark", plan_mark);
            oCmd.Parameters.AddWithValue("@plan_custtype", plan_custtype);
            oCmd.Parameters.AddWithValue("@plan_memo", plan_memo);
            oCmd.Parameters.AddWithValue("@plan_keyinempno", _keyinempno);
            oCmd.Parameters.AddWithValue("@plan_tcehtype1", plan_tcehtype1);
            oCmd.Parameters.AddWithValue("@plan_tcehtype2", plan_tcehtype2);
            oCmd.Parameters.AddWithValue("@plan_industype1", plan_industype1);
            oCmd.Parameters.AddWithValue("@plan_industype2", plan_industype2);
            oCmd.Parameters.AddWithValue("@plan_keyword", plan_keyword);
            oCmd.Parameters.AddWithValue("@plan_website", plan_website);
            oCmd.Parameters.AddWithValue("@plan_online", plan_online);
            oCmd.Parameters.AddWithValue("@plan_save", _save);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        #region 與院內其它單位共同執行
        public bool InsertJoin()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_join_update";

            oCmd.Parameters.AddWithValue("@join_id", join_id);
            oCmd.Parameters.AddWithValue("@join_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@join_orgcd", join_orgcd);
            oCmd.Parameters.AddWithValue("@join_orgcdnm", join_orgcdnm);
            oCmd.Parameters.AddWithValue("@join_empno", join_empno);
            oCmd.Parameters.AddWithValue("@join_empname", join_empname);
            oCmd.Parameters.AddWithValue("@join_memo", join_memo);
            oCmd.Parameters.AddWithValue("@sso_empno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool UpdateJoin()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"pr_gpi_join_update";

            oCmd.Parameters.AddWithValue("@join_id", join_id);
            oCmd.Parameters.AddWithValue("@join_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@join_orgcd", join_orgcd);
            oCmd.Parameters.AddWithValue("@join_orgcdnm", join_orgcdnm);
            oCmd.Parameters.AddWithValue("@join_empno", join_empno);
            oCmd.Parameters.AddWithValue("@join_empname", join_empname);
            oCmd.Parameters.AddWithValue("@join_memo", join_memo);
            oCmd.Parameters.AddWithValue("@sso_empno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool DeleteJoin()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_join_update";

            oCmd.Parameters.AddWithValue("@join_id", join_id);
            oCmd.Parameters.AddWithValue("@join_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@delete", "Y");
           
            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        /// <summary>
        /// 與院內其他單位共同執行
        /// </summary>
        /// <returns></returns>   
        public DataTable GetJoin()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_join_by_seqsn";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            //歷史版本
            if (plan_ver != 0)//未指定版本，不使用歷史區
            {
                oCmd.Parameters.AddWithValue("@ver", plan_ver);
            }
            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        #endregion

        #region 文件清單
        public DataTable GetDoc()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_doclist_by_seqsn";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 議約文件
        /// </summary>
        /// <returns></returns>
        public DataTable GetTreatyDoc()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"esp_treatyCase_getAllFiles_ANMR";

            oCmd.Parameters.AddWithValue("@contno", _gpino);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        #endregion

        #region 管理資料
        /// <summary>
        /// 取得業務人員權限(00:無權限、Y:產服業務管理人員、N:單位業務管理人員)
        /// </summary>
        /// <returns></returns>
        public string GetRight_bu()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_check_bu";

            oCmd.Parameters.AddWithValue("@empno", _empno);
            SqlParameter rtn_code = oCmd.Parameters.Add("@rtn_code", SqlDbType.VarChar, 5);
            rtn_code.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_code.Value.ToString();
        }
        /// <summary>
        /// 取得業管權限單位(N)
        /// </summary>
        /// <returns></returns>
        public DataTable GetOrgcd()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT org_orgcd as code_value,org_orgcd+'-'+org_abbr_chnm2 as code_desc 
FROM common..orgcod 
WHERE org_status='A' AND org_orgcd<>'00' AND org_orgcd IN
(SELECT SUBSTRING(bt2_orgcd,1,2) AS bt2_orgcd 
    FROM engage_buztbl2 WHERE bt2_empno=@empno)
";

            oCmd.Parameters.AddWithValue("@empno", _empno);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        /// <summary>
        /// 取得管理清單
        /// </summary>
        /// <returns></returns>
        public DataTable Get_tsc()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_tsc_casequery";

            oCmd.Parameters.AddWithValue("@main_gpiorgcd", _gpiorgcd);
            oCmd.Parameters.AddWithValue("@main_status", _status);
            oCmd.Parameters.AddWithValue("@main_custno", _custno);
            oCmd.Parameters.AddWithValue("@main_type", _type);
            oCmd.Parameters.AddWithValue("@main_gpiname", _gpiname);
            oCmd.Parameters.AddWithValue("@main_keyindate_from", _keyindate_from);
            oCmd.Parameters.AddWithValue("@main_keyindate_to", _keyindate_to);
            oCmd.Parameters.AddWithValue("@main_enddate_from", _enddate_from);
            oCmd.Parameters.AddWithValue("@main_enddate_to", _enddate_to);
            oCmd.Parameters.AddWithValue("@status1", _status1);
            oCmd.Parameters.AddWithValue("@status2", _status2);
            oCmd.Parameters.AddWithValue("@status3", _status3);
            oCmd.Parameters.AddWithValue("@status4", _status4);
            oCmd.Parameters.AddWithValue("@status5", _status5);
            oCmd.Parameters.AddWithValue("@keyword", _keyword);
            oCmd.Parameters.AddWithValue("@flag", _flag);
            oCmd.Parameters.AddWithValue("@empno", _empno);
            oCmd.Parameters.AddWithValue("@PageSize", PageSize);
            oCmd.Parameters.AddWithValue("@CurrentPage", CurrentPage);
            oCmd.Parameters.AddWithValue("@SortFields", SortFields);
            oCmd.Parameters.AddWithValue("@SortDirection", SortDirection);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得標案管理明細資料
        /// </summary>
        /// <returns></returns>
        public DataTable Get_tsc_by_seqsn()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_tsc_by_seqsn";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得按鈕顯示
        /// </summary>
        /// <returns></returns>
        public DataTable Get_flow_dsp_n()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_flow_dsp_n";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@empno", _empno);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 簽核結果儲存
        /// </summary>
        /// <returns></returns>
        public bool Update_tsc_sign()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_tsc_sign_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@chk_signdate", _chk_signdate);
            oCmd.Parameters.AddWithValue("@chk_result", _chk_result);
            oCmd.Parameters.AddWithValue("@chk_sign_memo", _chk_sign_memo);
            oCmd.Parameters.AddWithValue("@chk_signempno", _chk_signempno);
            oCmd.Parameters.AddWithValue("@empno", _empno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 簽核結果儲存
        /// </summary>
        /// <returns></returns>
        public bool Update_tsc_review()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_tsc_review_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@chk_review_empno", _chk_review_empno);
            oCmd.Parameters.AddWithValue("@chk_review_date", _chk_review_date);
            oCmd.Parameters.AddWithValue("@chk_review_result", _chk_review_result);
            oCmd.Parameters.AddWithValue("@chk_review_memo", _chk_review_memo);
            oCmd.Parameters.AddWithValue("@empno", _empno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        /// <summary>
        /// 檢核簽核結果、院長室核定是否完成
        /// </summary>
        /// <returns></returns>
        public string Get_tsc_check_data()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_tsc_check_data";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 5);
            rtn_flag.Direction = ParameterDirection.Output;

            this.Execute(oCmd, CommandType.StoredProcedure);

            return rtn_flag.Value.ToString();
        }
        /// <summary>
        /// 結件通知
        /// </summary>
        /// <returns></returns>
        public bool MailEmessage()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetMailEMessage";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        
        #endregion

        /// <summary>
        /// 取得基本資料版本
        /// </summary>
        /// <returns></returns>
        public int GetVer()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT main_ver FROM gpi_main
WHERE main_seqsn = @main_seqsn
";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            string data = this.getTopOne(oCmd, CommandType.Text);
            int ver = 0;
            if (data != string.Empty)
            {
                ver = int.Parse(data);
            }
            return ver;
        }
        /// <summary>
        /// 取得規劃構版本
        /// </summary>
        /// <returns></returns>
        public int GetPlanVer()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT plan_ver FROM gpi_plan
WHERE plan_seqsn = @main_seqsn
";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            string data = this.getTopOne(oCmd, CommandType.Text);
            int ver = 1;//規劃構想預設為「1」
            if (data != string.Empty)
            {
                ver = int.Parse(data);
            }
            return ver;
        }



        #region 歷史版次清單
        public DataTable GetHistory()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT [his_id]
      ,[his_seqsn]
      ,[his_ver]
      ,[his_keyinempno]
      ,[his_keyinempname]
      ,[his_keyindate]
  FROM view_gpi_history	
WHERE his_seqsn = @main_seqsn

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion


        /// <summary>
        /// 確認重案資料，更新產服中心確認人的工號、日期
        /// </summary>
        /// <returns></returns>
        public string SetGPIdupl()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
declare @s varchar(1)
set @s='N'
if exists(select * from gpi_dupl  WHERE dup_seqsn = @main_seqsn and isnull(dup_re_empno,'')='')
begin
	update gpi_dupl set dup_re_empno=@main_empno,dup_re_date=getdate()
	WHERE dup_seqsn = @main_seqsn
	set @s='Y'
end
else
begin
if exists(select * from gpi_dupl  WHERE dup_seqsn = @main_seqsn)
	set @s='Z'
end

select @s as t

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
	    oCmd.Parameters.AddWithValue("@main_empno", _empno);

            string data = this.getTopOne(oCmd, CommandType.Text);
       	    string Success1="N";

            if (data != string.Empty)
            {
                Success1= data;
            }
            return Success1;
        }
        
        /// <summary>
        /// 重案與否
        /// </summary>
        public void setIsDup()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"

update gpi_main 
set main_isDup=(case when @main_isDup='Y' then @main_isDup else null end) 
where main_seqsn=@main_seqsn

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_isDup", _isDup);

            this.Execute(oCmd, CommandType.Text);
            
            
        }
        /// <summary>
        /// 代表本院投標
        /// </summary>
        public void setIsRep()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"

update gpi_main 
set main_isRep=(case when @main_isRep ='Y' then @main_isRep else null end)
where main_seqsn=@main_seqsn

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@main_isRep", _isRep);

            this.Execute(oCmd, CommandType.Text);

        }


        #endregion
    }
}