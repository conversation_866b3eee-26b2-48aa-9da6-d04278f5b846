﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase_AllFile : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ClientScript.GetPostBackEventReference(new PostBackOptions(SGV_log));
            ViewState["contno"] = "";
            if (Request.QueryString["contno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["contno"]) || (Request.QueryString["contno"].Length == 0) || (Request.QueryString["contno"].Length > 15))
                    Response.Redirect("../danger.aspx");
                ViewState["contno"] = Request.QueryString["contno"].ToString();
            }
            ViewState["sortField"] = "tmp_caseno";
            ViewState["sortorder"] = "ASC";
            databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            ViewState["isPC"] = "false";
            Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", " <script>reflash_topic('Client', p);</script> ");
            //ScriptManager.RegisterStartupScript(this.Page, GetType(), "platformCheck", "reflash_topic('Client', p);");
        }

        if (Request.Params.Get("__EVENTTARGET") == "Client")
        {
            string p = Request.Params.Get("__EVENTARGUMENT").ToString();
            bool isWin, isMac, isLinux, isUnix = false;
            isWin = p.IndexOf("Win") > -1;  //Windows : Win32、Win16
            isMac = p.IndexOf("Mac") > -1;  //MacOS: MacIntel、Macintosh、MacPPC、Mac68K
            isUnix = p.IndexOf("X11") > -1; //Unix
            isLinux = p.IndexOf("Linux") > -1; //Linux: Linux x86_64、Linux x86_64 X11、Linux ppc64

            //Linux 要多加判斷排除，因為行動裝置Android 系統的Platform參數會是 
            //Linux armv7l、Linux armv8l、Linux aarch64、Linux i686(both Chrome on ChromeOS or Linux x86-64)
            if (p.IndexOf("Linux a") > -1 || p.IndexOf("Linux i") > -1)
            {
                isLinux = false;
            }
            if (isWin || isMac || isLinux || isUnix)
            {
                ViewState["isPC"] = "true";
            }
            else
            {
                ViewState["isPC"] = "false";
            }
        }
    }
    protected void databinding(string str_sortField, string str_sort)
    {
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.SelectCommand = "esp_treaty_ListAllUpload";
        //this.SDS_SC.SelectParameters.Add("caseno", TypeCode.String, ViewState["contno"].ToString());
        //this.SDS_SC.SelectParameters.Add("sortField", TypeCode.String, str_sortField);
        //this.SDS_SC.SelectParameters.Add("sortType", TypeCode.String, str_sort);
        //for (int i = 0; i < this.SDS_SC.SelectParameters.Count; i++)
        //{
        //    SDS_SC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_SC.DataBind();
        //SGV_log.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_ListAllUpload";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@caseno", oRCM.SQLInjectionReplaceAll(ViewState["contno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sortField", oRCM.SQLInjectionReplaceAll(str_sortField));
            sqlCmd.Parameters.AddWithValue("@sortType", oRCM.SQLInjectionReplaceAll(str_sort));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_log.DataSource = dt;
                SGV_log.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_log_DataBound(object sender, EventArgs e)
    {

    }
    protected void SGV_log_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "dl")
        {
            string str_file_url = "";
            string str_filename = "";
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyCase_file_modify";
            //SDS_log.SelectParameters.Add("req_id", "");
            //SDS_log.SelectParameters.Add("fd_name", "");
            //SDS_log.SelectParameters.Add("filetxt", "");
            //SDS_log.SelectParameters.Add("file_url", "");
            //SDS_log.SelectParameters.Add("empno", "");
            //SDS_log.SelectParameters.Add("mode", "view");
            //SDS_log.SelectParameters.Add("fid", arg[1]);
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());


            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(arg[1]));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = Server.HtmlEncode(dv[0]["tcdf_url"].ToString().Trim());
                str_filename = Server.HtmlEncode(dv[0]["tcdf_filename"].ToString().Trim());
            }
            if (str_file_url != "")
            {
                Treaty_log(arg[0], "檔案下載", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase_View.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(str_file_url);
                Response.Flush();
                Response.End();
            }
        }
    }

    protected void SGV_log_PageIndexChanged(object sender, EventArgs e)
    {

        databinding("", "");
    }
    protected void SGV_log_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_log.PageIndex = e.NewPageIndex;
        SGV_log.DataBind();
    }
    protected void SGV_log_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType != DataControlRowType.Header)
        {
            foreach (TableCell tc in e.Row.Cells)
            {
                tc.Attributes["style"] = "border-color:white";
            }
        }
    }
    protected void SGV_log_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_log.SortExpression)
                    {
                        //System.Web.UI.WebControls.Image objImage = null;
                        //objImage = (System.Web.UI.WebControls.Image)e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].FindControl("btnOrder");
                        //if (objImage == null)
                        //{
                        //    objImage = new System.Web.UI.WebControls.Image();
                        //    objImage.ID = "btnOrder";
                        //    e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].Controls.Add(objImage);
                        //}
                        //否為為排序欄位
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_log.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                    //else
                    //{
                    //    objImage = (System.Web.UI.WebControls.Image)e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].FindControl("btnOrder");
                    //    if (objImage != null)
                    //    {
                    //        e.Row.Cells[e.Row.Cells.GetCellIndex(MyHeader)].Controls.Remove(objImage);
                    //    }                   
                    //}
                }
            }
        }
    }
    protected void SDS_SC_Selecting(object sender, SqlDataSourceSelectingEventArgs e)
    {
        for (int i = 0; i < e.Command.Parameters.Count - 1; i++)
        {
            if (e.Command.Parameters[i].Value == null)
            {
                e.Command.Parameters[i].Value = "";
            }
        }
    }
    protected void SGV_log_Sorted(object sender, EventArgs e)
    {

    }
    protected void SGV_log_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        this.SGV_log.PageIndex = 0;
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

}