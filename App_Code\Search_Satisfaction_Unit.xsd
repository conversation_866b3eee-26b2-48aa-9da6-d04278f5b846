﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Search_Satisfaction_Unit" targetNamespace="http://tempuri.org/Search_Satisfaction_Unit.xsd" xmlns:mstns="http://tempuri.org/Search_Satisfaction_Unit.xsd" xmlns="http://tempuri.org/Search_Satisfaction_Unit.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="ConnString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.ConnString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables />
        <Sources>
          <DbSource ConnectionRef="ConnString (Web.config)" DbObjectName="engagedb.dbo.esp_SatisfactionManager_search_sgr" DbObjectType="StoredProcedure" GenerateShortCommands="true" MethodsParameterType="CLR" Modifier="Public" Name="esp_SatisfactionManager_search_sgr" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true">
            <SelectCommand>
              <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                <CommandText>dbo.esp_SatisfactionManager_search_sgr</CommandText>
                <Parameters>
                  <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                  <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="char" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@empno" Precision="0" ProviderType="Char" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                </Parameters>
              </DbCommand>
            </SelectCommand>
          </DbSource>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Search_Satisfaction_Unit" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="False">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded" />
    </xs:complexType>
  </xs:element>
</xs:schema>