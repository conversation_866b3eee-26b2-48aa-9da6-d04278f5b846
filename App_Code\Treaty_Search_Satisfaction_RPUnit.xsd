﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Treaty_Search_Satisfaction_RPUnit" targetNamespace="http://tempuri.org/Treaty_Search_Satisfaction_RPUnit.xsd" xmlns:mstns="http://tempuri.org/Treaty_Search_Satisfaction_RPUnit.xsd" xmlns="http://tempuri.org/Treaty_Search_Satisfaction_RPUnit.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="ConnString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.ConnString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="esp_SatisfactionManager_search_sgrTableAdapter" GeneratorDataComponentClassName="esp_SatisfactionManager_search_sgrTableAdapter" Name="esp_SatisfactionManager_search_sgr" UserDataComponentName="esp_SatisfactionManager_search_sgrTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnString (Web.config)" DbObjectName="engagedb.dbo.esp_SatisfactionManager_search_sgr" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.esp_SatisfactionManager_search_sgr</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="char" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@empno" Precision="0" ProviderType="Char" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="tmp_seno" DataSetColumn="tmp_seno" />
              <Mapping SourceColumn="tmp_uid" DataSetColumn="tmp_uid" />
              <Mapping SourceColumn="tc_org_name" DataSetColumn="tc_org_name" />
              <Mapping SourceColumn="ts_survery_total" DataSetColumn="ts_survery_total" />
              <Mapping SourceColumn="ts_survery_total_yes" DataSetColumn="ts_survery_total_yes" />
              <Mapping SourceColumn="ts_survery_total_not" DataSetColumn="ts_survery_total_not" />
              <Mapping SourceColumn="ts_survery_total_good" DataSetColumn="ts_survery_total_good" />
              <Mapping SourceColumn="ts_survery_total_proc" DataSetColumn="ts_survery_total_proc" />
              <Mapping SourceColumn="ts_survery_valid" DataSetColumn="ts_survery_valid" />
              <Mapping SourceColumn="ts_friendly_yes" DataSetColumn="ts_friendly_yes" />
              <Mapping SourceColumn="ts_friendly_normal" DataSetColumn="ts_friendly_normal" />
              <Mapping SourceColumn="ts_friendly_no" DataSetColumn="ts_friendly_no" />
              <Mapping SourceColumn="ts_effective_1" DataSetColumn="ts_effective_1" />
              <Mapping SourceColumn="ts_effective_2" DataSetColumn="ts_effective_2" />
              <Mapping SourceColumn="ts_effective_3" DataSetColumn="ts_effective_3" />
              <Mapping SourceColumn="ts_effective_4" DataSetColumn="ts_effective_4" />
              <Mapping SourceColumn="ts_effective_5" DataSetColumn="ts_effective_5" />
              <Mapping SourceColumn="ts_effective_6" DataSetColumn="ts_effective_6" />
              <Mapping SourceColumn="ts_satisfaction_high" DataSetColumn="ts_satisfaction_high" />
              <Mapping SourceColumn="ts_satisfaction_yes" DataSetColumn="ts_satisfaction_yes" />
              <Mapping SourceColumn="ts_satisfaction_normal" DataSetColumn="ts_satisfaction_normal" />
              <Mapping SourceColumn="ts_satisfaction_no" DataSetColumn="ts_satisfaction_no" />
              <Mapping SourceColumn="ts_satisfaction_low" DataSetColumn="ts_satisfaction_low" />
              <Mapping SourceColumn="ts_order" DataSetColumn="ts_order" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Treaty_Search_Satisfaction_RPUnit" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="False" msprop:Generator_DataSetName="Treaty_Search_Satisfaction_RPUnit" msprop:Generator_UserDSName="Treaty_Search_Satisfaction_RPUnit">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="esp_SatisfactionManager_search_sgr" msprop:Generator_TableClassName="esp_SatisfactionManager_search_sgrDataTable" msprop:Generator_TableVarName="tableesp_SatisfactionManager_search_sgr" msprop:Generator_TablePropName="esp_SatisfactionManager_search_sgr" msprop:Generator_RowDeletingName="esp_SatisfactionManager_search_sgrRowDeleting" msprop:Generator_RowChangingName="esp_SatisfactionManager_search_sgrRowChanging" msprop:Generator_RowEvHandlerName="esp_SatisfactionManager_search_sgrRowChangeEventHandler" msprop:Generator_RowDeletedName="esp_SatisfactionManager_search_sgrRowDeleted" msprop:Generator_UserTableName="esp_SatisfactionManager_search_sgr" msprop:Generator_RowChangedName="esp_SatisfactionManager_search_sgrRowChanged" msprop:Generator_RowEvArgName="esp_SatisfactionManager_search_sgrRowChangeEvent" msprop:Generator_RowClassName="esp_SatisfactionManager_search_sgrRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="tmp_seno" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columntmp_seno" msprop:Generator_ColumnPropNameInRow="tmp_seno" msprop:Generator_ColumnPropNameInTable="tmp_senoColumn" msprop:Generator_UserColumnName="tmp_seno" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tmp_uid" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columntmp_uid" msprop:Generator_ColumnPropNameInRow="tmp_uid" msprop:Generator_ColumnPropNameInTable="tmp_uidColumn" msprop:Generator_UserColumnName="tmp_uid" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="tc_org_name" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columntc_org_name" msprop:Generator_ColumnPropNameInRow="tc_org_name" msprop:Generator_ColumnPropNameInTable="tc_org_nameColumn" msprop:Generator_UserColumnName="tc_org_name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_total" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_total" msprop:Generator_ColumnPropNameInRow="ts_survery_total" msprop:Generator_ColumnPropNameInTable="ts_survery_totalColumn" msprop:Generator_UserColumnName="ts_survery_total" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_total_yes" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_total_yes" msprop:Generator_ColumnPropNameInRow="ts_survery_total_yes" msprop:Generator_ColumnPropNameInTable="ts_survery_total_yesColumn" msprop:Generator_UserColumnName="ts_survery_total_yes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_total_not" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_total_not" msprop:Generator_ColumnPropNameInRow="ts_survery_total_not" msprop:Generator_ColumnPropNameInTable="ts_survery_total_notColumn" msprop:Generator_UserColumnName="ts_survery_total_not" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_total_good" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_total_good" msprop:Generator_ColumnPropNameInRow="ts_survery_total_good" msprop:Generator_ColumnPropNameInTable="ts_survery_total_goodColumn" msprop:Generator_UserColumnName="ts_survery_total_good" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_total_proc" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_total_proc" msprop:Generator_ColumnPropNameInRow="ts_survery_total_proc" msprop:Generator_ColumnPropNameInTable="ts_survery_total_procColumn" msprop:Generator_UserColumnName="ts_survery_total_proc" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_survery_valid" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_survery_valid" msprop:Generator_ColumnPropNameInRow="ts_survery_valid" msprop:Generator_ColumnPropNameInTable="ts_survery_validColumn" msprop:Generator_UserColumnName="ts_survery_valid" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_yes" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_friendly_yes" msprop:Generator_ColumnPropNameInRow="ts_friendly_yes" msprop:Generator_ColumnPropNameInTable="ts_friendly_yesColumn" msprop:Generator_UserColumnName="ts_friendly_yes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_normal" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_friendly_normal" msprop:Generator_ColumnPropNameInRow="ts_friendly_normal" msprop:Generator_ColumnPropNameInTable="ts_friendly_normalColumn" msprop:Generator_UserColumnName="ts_friendly_normal" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_friendly_no" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_friendly_no" msprop:Generator_ColumnPropNameInRow="ts_friendly_no" msprop:Generator_ColumnPropNameInTable="ts_friendly_noColumn" msprop:Generator_UserColumnName="ts_friendly_no" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_1" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_1" msprop:Generator_ColumnPropNameInRow="ts_effective_1" msprop:Generator_ColumnPropNameInTable="ts_effective_1Column" msprop:Generator_UserColumnName="ts_effective_1" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_2" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_2" msprop:Generator_ColumnPropNameInRow="ts_effective_2" msprop:Generator_ColumnPropNameInTable="ts_effective_2Column" msprop:Generator_UserColumnName="ts_effective_2" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_3" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_3" msprop:Generator_ColumnPropNameInRow="ts_effective_3" msprop:Generator_ColumnPropNameInTable="ts_effective_3Column" msprop:Generator_UserColumnName="ts_effective_3" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_4" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_4" msprop:Generator_ColumnPropNameInRow="ts_effective_4" msprop:Generator_ColumnPropNameInTable="ts_effective_4Column" msprop:Generator_UserColumnName="ts_effective_4" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_5" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_5" msprop:Generator_ColumnPropNameInRow="ts_effective_5" msprop:Generator_ColumnPropNameInTable="ts_effective_5Column" msprop:Generator_UserColumnName="ts_effective_5" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_effective_6" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_effective_6" msprop:Generator_ColumnPropNameInRow="ts_effective_6" msprop:Generator_ColumnPropNameInTable="ts_effective_6Column" msprop:Generator_UserColumnName="ts_effective_6" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_high" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_satisfaction_high" msprop:Generator_ColumnPropNameInRow="ts_satisfaction_high" msprop:Generator_ColumnPropNameInTable="ts_satisfaction_highColumn" msprop:Generator_UserColumnName="ts_satisfaction_high" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_yes" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_satisfaction_yes" msprop:Generator_ColumnPropNameInRow="ts_satisfaction_yes" msprop:Generator_ColumnPropNameInTable="ts_satisfaction_yesColumn" msprop:Generator_UserColumnName="ts_satisfaction_yes" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_normal" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_satisfaction_normal" msprop:Generator_ColumnPropNameInRow="ts_satisfaction_normal" msprop:Generator_ColumnPropNameInTable="ts_satisfaction_normalColumn" msprop:Generator_UserColumnName="ts_satisfaction_normal" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_no" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_satisfaction_no" msprop:Generator_ColumnPropNameInRow="ts_satisfaction_no" msprop:Generator_ColumnPropNameInTable="ts_satisfaction_noColumn" msprop:Generator_UserColumnName="ts_satisfaction_no" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_satisfaction_low" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_satisfaction_low" msprop:Generator_ColumnPropNameInRow="ts_satisfaction_low" msprop:Generator_ColumnPropNameInTable="ts_satisfaction_lowColumn" msprop:Generator_UserColumnName="ts_satisfaction_low" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ts_order" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnts_order" msprop:Generator_ColumnPropNameInRow="ts_order" msprop:Generator_ColumnPropNameInTable="ts_orderColumn" msprop:Generator_UserColumnName="ts_order" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>