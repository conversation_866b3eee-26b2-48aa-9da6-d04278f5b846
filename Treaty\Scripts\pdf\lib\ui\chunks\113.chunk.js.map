{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/sk.js"], "names": ["module", "exports", "e", "n", "default", "t", "r", "o", "a", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAG,SAASI,EAAEJ,GAAG,OAAOA,EAAE,GAAGA,EAAE,GAAG,MAAMA,EAAE,IAAI,SAASK,EAAEL,EAAEG,EAAEF,EAAEI,GAAG,IAAIC,EAAEN,EAAE,IAAI,OAAOC,GAAG,IAAI,IAAI,OAAOE,GAAGE,EAAE,aAAa,gBAAgB,IAAI,IAAI,OAAOF,EAAE,SAASE,EAAE,SAAS,UAAU,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,SAAS,SAASM,EAAE,WAAW,IAAI,IAAI,OAAOH,EAAE,SAASE,EAAE,SAAS,UAAU,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,SAAS,SAASM,EAAE,WAAW,IAAI,IAAI,OAAOH,GAAGE,EAAE,MAAM,OAAO,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,MAAM,OAAOM,EAAE,QAAQ,IAAI,IAAI,OAAOH,GAAGE,EAAE,SAAS,WAAW,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,UAAU,YAAYM,EAAE,WAAW,IAAI,IAAI,OAAOH,GAAGE,EAAE,MAAM,QAAQ,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,OAAO,SAASM,EAAE,SAAS,IAAIA,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDC,MAAM,KAAKC,cAAc,uBAAuBD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,oFAAoFH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,UAAU,EAAEC,QAAQ,SAAShB,GAAG,OAAOA,EAAE,KAAKiB,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,eAAeC,IAAI,oBAAoBC,KAAK,yBAAyBC,EAAE,cAAcC,aAAa,CAACC,OAAO,QAAQC,KAAK,UAAUC,EAAEvB,EAAEwB,EAAExB,EAAEyB,GAAGzB,EAAE0B,EAAE1B,EAAE2B,GAAG3B,EAAE4B,EAAE5B,EAAE6B,GAAG7B,EAAE8B,EAAE9B,EAAE+B,GAAG/B,EAAEgC,EAAEhC,EAAEiC,GAAGjC,IAAI,OAAOJ,EAAEC,QAAQqC,OAAOjC,EAAE,MAAK,GAAIA,EAAjjDH,CAAE,EAAQ", "file": "chunks/113.chunk.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_sk=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=t(e);function r(e){return e>1&&e<5&&1!=~~(e/10)}function o(e,t,n,o){var a=e+\" \";switch(n){case\"s\":return t||o?\"pár sekúnd\":\"pár sekundami\";case\"m\":return t?\"minúta\":o?\"minútu\":\"minútou\";case\"mm\":return t||o?a+(r(e)?\"minúty\":\"minút\"):a+\"minútami\";case\"h\":return t?\"hodina\":o?\"hodinu\":\"hodinou\";case\"hh\":return t||o?a+(r(e)?\"hodiny\":\"hodín\"):a+\"hodinami\";case\"d\":return t||o?\"deň\":\"dňom\";case\"dd\":return t||o?a+(r(e)?\"dni\":\"dní\"):a+\"dňami\";case\"M\":return t||o?\"mesiac\":\"mesiacom\";case\"MM\":return t||o?a+(r(e)?\"mesiace\":\"mesiacov\"):a+\"mesiacmi\";case\"y\":return t||o?\"rok\":\"rokom\";case\"yy\":return t||o?a+(r(e)?\"roky\":\"rokov\"):a+\"rokmi\"}}var a={name:\"sk\",weekdays:\"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota\".split(\"_\"),weekdaysShort:\"ne_po_ut_st_št_pi_so\".split(\"_\"),weekdaysMin:\"ne_po_ut_st_št_pi_so\".split(\"_\"),months:\"január_február_marec_apríl_máj_jún_júl_august_september_október_november_december\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd D. MMMM YYYY H:mm\",l:\"D. M. YYYY\"},relativeTime:{future:\"za %s\",past:\"pred %s\",s:o,m:o,mm:o,h:o,hh:o,d:o,dd:o,M:o,MM:o,y:o,yy:o}};return n.default.locale(a,null,!0),a}));"], "sourceRoot": ""}