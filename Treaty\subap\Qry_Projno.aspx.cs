﻿using System;
using System.Collections.Generic;
//using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;

public partial class Qry_Projno : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    //guid 參數放在pub string
    public string Commonkey
    {
        get { return ViewState["_Commonkey"].ToString(); }
        set { ViewState["_Commonkey"] = value; }
    }
    public string oricountry
    {
        get { return ViewState["_oricountry"].ToString(); }
        set { ViewState["_oricountry"] = value; }
    }
    public string SystemCode
    {
        get { return ViewState["_SystemCode"].ToString(); }
        set { ViewState["_SystemCode"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["URL"] = "";
            if (Request.QueryString["url"] != null)
                ViewState["URL"] = Uri.UnescapeDataString(Request.QueryString["url"].ToString());


            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @" SELECT  org_orgcd AS orgcd, org_orgcd + '-' + org_abbr_chnm2 AS orgcd_name FROM  common..orgcod WHERE (org_status = 'A') and org_orgcd <>'00' ";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    DDL_code2.DataSource = dt;
                    DDL_code2.DataBind();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }              
            databinding();
        }
    }
 
    protected void BT_search_Click(object sender, EventArgs e)
    {
        databinding();
    }

    protected void BindDLL()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = "SELECT  org_orgcd AS orgcd, org_orgcd + '-' + org_abbr_chnm2 AS orgcd_name FROM  common..orgcod WHERE (org_status = 'A') and org_orgcd <>'00' ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                DDL_code2.DataSource = dt;
                DDL_code2.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void databinding()
    {
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_search.SelectCommand = "esp_treaty_tort_plan_list_keyword";
        //SDS_search.SelectParameters.Add("orgcd", DDL_code2.SelectedValue);
        //SDS_search.SelectParameters.Add("keyword", TB_mp.Text.ToUpper());
        //for (int i = 0; i < SDS_search.SelectParameters.Count; i++)
        //{
        //    SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_search.DataBind();
        //this.SGV_company.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_tort_plan_list_keyword";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@orgcd", oRCM.SQLInjectionReplaceAll(DDL_code2.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll(TB_mp.Text.ToUpper()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }
    protected void SGV_company_PageIndexChanged(object sender, EventArgs e)
    {
        databinding();
    }
    protected void SGV_company_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_company.PageIndex = e.NewPageIndex;
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "view_case")
        {
            Session["projno"] = e.CommandArgument.ToString();
            string script = "<script language='javascript'>close_win();</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    protected void SGV_company_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {
                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_company.SortExpression)
                    {
                        //否為為排序欄位
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_company.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "./images/icon-arrowasc.gif";
                        else
                            ig_sort.ImageUrl = "./images/icon-arrowdesc.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                }
            }
        }
    }
 
 
}