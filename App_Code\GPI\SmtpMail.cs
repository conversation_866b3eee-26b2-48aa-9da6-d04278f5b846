﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Net.Mail;

namespace GPI
{
    /// <summary>
    /// SmtpMail 的摘要描述
    /// </summary>
    public class SmtpMail
    {
        private string MAIL_SERVER = ConfigurationManager.AppSettings["MailServer"];

        private string _MailFrom = "";
        private string _MailTo = "";
        private string _MailCC = "";
        private string _MailBCC = "";
        private string _Subject = "";
        private bool _IsBodyHtml = true;
        private string _Body = "";
        private string _Attachments = "";

        //From
        public string MailFrom
        {
            get { return this._MailFrom; }
            set { this._MailFrom = value; }
        }
        //MailTo
        public string MailTo
        {
            get { return this._MailTo; }
            set { this._MailTo = value; }
        }
        //CC
        public string MailCC
        {
            get { return this._MailCC; }
            set { this._MailCC = value; }
        }
        //BCC
        public string MailBCC
        {
            get { return this._MailBCC; }
            set { this._MailBCC = value; }
        }
        //Subject
        public string Subject
        {
            get { return this._Subject; }
            set { this._Subject = value; }
        }
        //IsBodyHtml
        public bool IsBodyHtml
        {
            get { return this._IsBodyHtml; }
            set { this._IsBodyHtml = value; }
        }
        //Body
        public string Body
        {
            get { return this._Body; }
            set { this._Body = value; }
        }
        //Attachments
        public string Attachments
        {
            get { return this._Attachments; }
            set { this._Attachments = value; }
        }

        public void SendMail()
        {

            try
            {

                //宣告MailMessage
                SmtpClient mySmtpClient = new SmtpClient();
                MailMessage myMailMessage = new MailMessage();

                //設定MailServer
                mySmtpClient.Host = MAIL_SERVER;
                //寄信者
                myMailMessage.From = new MailAddress(this._MailFrom);
                //收件者
                myMailMessage = this.AddTo(this._MailTo, myMailMessage);
                //副本
                myMailMessage = this.AddCC(this._MailCC, myMailMessage);
                //秘密副本
                myMailMessage = this.AddBCC(this._MailBCC, myMailMessage);
                //寄信主題
                myMailMessage.Subject = this._Subject;
                //以文字格式寄信
                myMailMessage.IsBodyHtml = this._IsBodyHtml;
                //寄信內容
                myMailMessage.Body = this._Body;
                //夾帶檔案
                myMailMessage = this.AddAttachments(this._Attachments, myMailMessage);
                //寄信
                if (ConfigurationManager.AppSettings["Enable_MAIL"] == "Y")
                {
                    mySmtpClient.Send(myMailMessage);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        private MailMessage AddTo(string toList, MailMessage myMailMessage)
        {

            if (toList != "")
            {

                string[] toArray = toList.Split(',');

                for (int count1 = 0; count1 < toArray.Length; count1++)
                {
                    myMailMessage.To.Add(new MailAddress(toArray[count1]));
                }

            }

            return myMailMessage;

        }

        private MailMessage AddCC(string ccList, MailMessage myMailMessage)
        {

            if (ccList != "")
            {

                string[] ccArray = ccList.Split(',');

                for (int count1 = 0; count1 < ccArray.Length; count1++)
                {
                    myMailMessage.CC.Add(new MailAddress(ccArray[count1]));
                }

            }

            return myMailMessage;

        }

        private MailMessage AddBCC(string bccList, MailMessage myMailMessage)
        {

            if (bccList != "")
            {

                string[] bccArray = bccList.Split(',');

                for (int count1 = 0; count1 < bccArray.Length; count1++)
                {
                    myMailMessage.Bcc.Add(new MailAddress(bccArray[count1]));
                }
            }

            return myMailMessage;

        }

        private MailMessage AddAttachments(string attachmentsList, MailMessage myMailMessage)
        {

            if (attachmentsList.Trim() != "")
            {

                string[] attachmentsArray = attachmentsList.Split(',');

                for (int count1 = 0; count1 < attachmentsArray.Length; count1++)
                {
                    myMailMessage.Attachments.Add(new Attachment(attachmentsArray[count1]));
                }

            }

            return myMailMessage;
        }


    }
}
