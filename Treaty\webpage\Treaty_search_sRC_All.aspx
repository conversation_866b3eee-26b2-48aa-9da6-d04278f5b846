﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Treaty_search_sRC_All.aspx.cs" Inherits="Treaty_webpage_Treaty_search_sRC_All" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 2px;
            right: 0;
            background: url('../../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            padding-top: 2px;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 22px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock gentable font-normal">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align='right'>案源：</td>
                                    <td colspan="3">
                                        <asp:CheckBoxList ID="cbxCaseClass" runat="server" RepeatDirection="Horizontal" BorderWidth="0px" RepeatLayout="Flow">
                                            <asp:ListItem Value="N">洽案系統</asp:ListItem>
                                            <%--<asp:ListItem Value="R">標案系統</asp:ListItem>--%>
                                            <asp:ListItem Value="M">NDA</asp:ListItem>
                                            <asp:ListItem Value="A">國外無收入</asp:ListItem>
                                            <asp:ListItem Value="F">國內無收入</asp:ListItem>
                                            <asp:ListItem Value="C">工服</asp:ListItem>

                                        </asp:CheckBoxList>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>單位別：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_orgcd" runat="server" Width="150px" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                    </td>
                                    <td align='right'>客戶名稱：</td>
                                    <td>
                                        <asp:TextBox ID="tbx_compname" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td align='right'>契約性質：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_conttype" runat="server" Width="165px" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                    </td>
                                    <td align='right'>單位承辦人：</td>
                                    <td>
                                        <asp:TextBox ID="tbx_promoter_name" runat="server" class="inputex text-input"></asp:TextBox>
<%--                                        <a onclick="javascript:Find_Empno('tbx_promoter_empno','1');"> <img src="../images/icon_search.gif" border="0" class="ajax_mesg btn_mouseout" /></a>&nbsp;--%>
        <div style="display: none">
            <asp:TextBox ID="tbx_promoter_empno" runat="server"></asp:TextBox>
        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>單位處理情形：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_org_status" runat="server" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                    </td>
                                    <td align='right'>法務承辦人：</td>
                                    <td>
                                        <asp:TextBox ID="tbx_handle_name" runat="server" class="inputex text-input"></asp:TextBox>
<%--                                        <a onclick="javascript:Find_Empno('tbx_handle_empno','2');"> <img src="../images/icon_search.gif" border="0" class="ajax_mesg btn_mouseout" /></a>&nbsp;--%>
        <div style="display: none">
            <asp:TextBox ID="tbx_handle_empno" runat="server"></asp:TextBox>
        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>年度：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_year" runat="server" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                    </td>
                                    <td align='right'>關鍵字查詢：</td>
                                    <td>
                                        <asp:TextBox ID="tbx_kw" runat="server"></asp:TextBox>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>修約類：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_amend" runat="server" DataTextField="Text" DataValueField="Value">
                                            <asp:ListItem Value="">全部</asp:ListItem>
                                            <asp:ListItem Value="1">是</asp:ListItem>
                                            <asp:ListItem Value="0">否</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td align='right'>報院條件：</td>
                                    <td>
                                        <asp:CheckBox ID="cb_sRC" runat="server" />
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" class="td_right">
                                        <asp:Button ID="btnExcel" runat="server" Class="genbtnS" Text="匯出" OnClick="btnExcel_Click"></asp:Button>
                                        <asp:Button ID="btnClear" runat="server" Class="genbtnS" Text="清除" OnClick="btnClear_Click"></asp:Button>
                                        <asp:Button ID="btnQuery" TabIndex="1" runat="server" Text="查詢" Class="genbtnS" OnClick="btnQuery_Click"></asp:Button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="6"></td>
                                </tr>
                            </table>

                            <div class="twocol margin5TB stripeMe">
                                <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                    <FooterStyle BackColor="White" />
                                    <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                    <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                    <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                    <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                    <Columns>
                                        <asp:BoundField DataField="case_className" HeaderText="案源"></asp:BoundField>
                                        <asp:BoundField DataField="單位別" HeaderText="單位別"></asp:BoundField>

                                        <asp:TemplateField HeaderText="規劃案號<hr/>草約編號">
                                            <ItemTemplate>
                                                <%# Server.HtmlEncode(Eval("pre_contno").ToString()) %>
                                                <hr />
                                                <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl='<%# "TreatyCase_view.aspx?seno="+ Server.HtmlEncode(Eval("tc_seno").ToString()) %>' Target="_blank"><%# Server.HtmlEncode(Eval("actcontno").ToString()) %></asp:HyperLink>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="契約名稱<hr />客戶名稱">
                                            <ItemTemplate>
                                                <%# Server.HtmlEncode(Eval("casename").ToString()) %>
                                                <hr />
                                                <%# Server.HtmlEncode(Eval("compname").ToString()) %>
                                            </ItemTemplate>
                                            <ItemStyle Width="150px" />
                                        </asp:TemplateField>
                                        <asp:BoundField DataField="契約性質" HeaderText="契約性質"></asp:BoundField>
                                        <asp:TemplateField HeaderText="契約金額">
                                            <ItemTemplate>
                                                <%# string.Format("{0:###,###}",Eval("amount")) %>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="契約起日<hr/>契約迄日">
                                            <ItemTemplate>
                                                <%# Server.HtmlEncode(Eval("contsdate").ToString()) %>
                                                <hr />
                                                <%# Server.HtmlEncode(Eval("contedate").ToString()) %>
                                            </ItemTemplate>
                                        </asp:TemplateField>

                                        <asp:TemplateField HeaderText="單位承辦人<hr/>法務承辦人">
                                            <ItemTemplate>
                                                <%# Server.HtmlEncode(Eval("promoter_name").ToString()) %>
                                                <hr />
                                                <%# Server.HtmlEncode(Eval("handle_name").ToString()) %>
                                            </ItemTemplate>
                                            <ItemStyle Width="70px" />
                                        </asp:TemplateField>
                                        <asp:BoundField DataField="單位處理情形" HeaderText="單位處理情形"></asp:BoundField>
                                        <asp:TemplateField HeaderText="單位說明">
                                            <ItemTemplate>
                                                <asp:Label ID="lbl_remark" runat="server" class="iterm_dymanic_說明"><%#  Server.HtmlEncode(Eval("org_remark_s").ToString())  %>
                                                </asp:Label>
                                            </ItemTemplate>
                                            <ItemStyle Width="50px" />
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="法務意見">
                                            <ItemTemplate>
                                                <asp:Label ID="lbl_betsum" runat="server" class="iterm_dymanic_說明">
                                                            <%# Server.HtmlEncode( Eval("betsum_s").ToString()) %>
                                                </asp:Label>
                                            </ItemTemplate>
                                            <ItemStyle Width="50px" />
                                        </asp:TemplateField>
                                    </Columns>
                                    <EmptyDataTemplate>
                                        <!--當找不到資料時則顯示「無資料」-->
                                        <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                    </EmptyDataTemplate>
                                    <FooterStyle BackColor="White" />
                                    <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                </cc1:SmartGridView>

                            </div>

                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc1:Foot runat="server" ID="Foot" />
        <script type="text/javascript">
            //pickdate
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });


            jQuery(document).ready(function () {
                special();
            });
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(special);
            function special() {

                //alert("iterm_dymanic");
                $('span.iterm_dymanic_說明').cluetip({ width: '850px', showTitle: false, ajaxCache: false, arrows: true });
            }

            function Find_Empno(obj, arg_sw) {

                $(".ajax_mesg").colorbox({
                    href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                    , iframe: true, width: "85%", height: "90%", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                        var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                        if (arg_sw == "1") {
                            $.getJSON(strURL + '?callback=?', jsonp_callback1);
                        }
                        if (arg_sw == "2") {
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    }
                });
            }

            function jsonp_callback1(data) {
                $('#tbx_promoter_empno').val('');
                $('#tbx_promoter_name').val('');
                switch (data.c_com_cname) {
                    case "danger":
                        alert("有危險字眼!");
                        break;
                    case "error0":
                        alert("查無此人 或 空值!");
                        break;
                    case "error2":
                        alert("查到的資料有2筆以上,請填較精確的值!");
                        break;
                    default:
                        $('#tbx_promoter_empno').val(data.c_com_empno);
                        $('#tbx_promoter_name').val(data.c_com_cname);
                }
            }
            function jsonp_callback2(data) {
                $('#tbx_handle_empno').val('');
                $('#tbx_handle_name').val('');
                switch (data.c_com_cname) {
                    case "danger":
                        alert("有危險字眼!");
                        break;
                    case "error0":
                        alert("查無此人 或 空值!");
                        break;
                    case "error2":
                        alert("查到的資料有2筆以上,請填較精確的值!");
                        break;
                    default:
                        $('#tbx_handle_empno').val(data.c_com_empno);
                        $('#tbx_handle_name').val(data.c_com_cname);
                }
            }
        </script>
    </form>
</body>
</html>
