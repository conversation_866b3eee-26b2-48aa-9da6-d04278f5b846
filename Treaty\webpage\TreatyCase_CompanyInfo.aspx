﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_CompanyInfo.aspx.cs" Inherits="Treaty_webpage_TreatyCase_CompanyInfo" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .empty {
            color: #aaa;
        }

        .ui-cluetip-header, .ui-cluetip-content {
            overflow: auto;
            max-heisht: 4em;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <cc1:SmartGridView ID="SGV_log" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" Style="margin-left: 10px"
                 OnDataBound="SGV_log_DataBound" OnRowCommand="SGV_log_RowCommand" OnPageIndexChanged="SGV_log_PageIndexChanged" OnPageIndexChanging="SGV_log_PageIndexChanging" OnRowCreated="SGV_log_RowCreated" OnRowDataBound="SGV_log_RowDataBound" OnSorted="SGV_log_Sorted" OnSorting="SGV_log_Sorting" AllowPaging="True" AllowSorting="True">
                <HeaderStyle CssClass="fixedheadertable" />
                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                <Columns>
                    <asp:BoundField DataField="Orgcd" SortExpression="Orgcd" HeaderText="單位">
                        <HeaderStyle HorizontalAlign="Center" />
                        <ItemStyle Width="30px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="議約編號" SortExpression="treatyno">
                        <ItemTemplate>
                            <asp:Label ID="LB_seno" runat="server" Text='<%# Server.HtmlEncode(Eval("tc_seno").ToString()) %>' Visible="False"></asp:Label>
                            <asp:Literal ID="LT_contno" runat="server" Text='<%# Server.HtmlEncode(Eval("treatyno").ToString()) %>'></asp:Literal>
                            <asp:LinkButton ID="LB_contno" runat="server" CommandName="view_case" CommandArgument='<%# Eval("tc_seno") %>' class='ajax_mesg_comp' Visible="False">  <%#DataBinder.Eval(Container.DataItem, "treatyno")%></asp:LinkButton>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Left" Width="125px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="Treaty_Name" SortExpression="Treaty_Name" HeaderText="洽案(契約)名稱">
                        <ItemStyle HorizontalAlign="Left" Width="400px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="Handle_Name" SortExpression="Handle_Name" HeaderText="法務承辦">
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tc_cop_name" SortExpression="tc_cop_name" HeaderText="協同承辦">
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>

                    <asp:BoundField DataField="Assign_Date" SortExpression="Assign_Date" HeaderText="分案日">
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="Closedate" SortExpression="Closedate" HeaderText="結案日">
                        <ItemStyle HorizontalAlign="Left" Width="60px" />
                    </asp:BoundField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無相關資訊!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
            </cc1:SmartGridView>
        </span>
        <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" OnSelecting="SDS_SC_Selecting" />--%>
        <script type="text/javascript">
            function viewCase(seno) {
                //var url = './TreatyCase_view.aspx?seno=' + seno;
                //      window.open(url, 'companyInfo', config = 'height=600px,width=950px,scrollbars=yes,resizable=yes');
                $(".ajax_mesg_comp").colorbox({
                    href: "./TreatyCase_view.aspx?seno=" + seno
                    , title: '契約資料'
                    , iframe: true, width: "90%", height: "80%", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                    }
                });
            }

            function viewCaseQ(seno) {
                //var url = './TreatyCaseQ_view.aspx?seno=' + seno;
                //window.open(url, 'companyInfo', config = 'height=600px,width=950px,scrollbars=yes,resizable=yes');
                $(".ajax_mesg_comp").colorbox({
                    href: "./TreatyCase_view.aspx?seno=" + seno
                    , title: '契約資料'
                    , iframe: true, width: "90%", height: "80%", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                    }
                });
            }
        </script>


    </form>
</body>
</html>
