﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.SqlClient;

public class mySQLHelper
{
    #region 私有變數

    private string _conn;

    private int _pagesize;
    private int _currentpage;
    private int _rowcount;
    private int _pagecount;
    private DataTable _pagelist;
    private string _sortFields;
    private string _sortDirection;


    #endregion

    #region 建構子

    public mySQLHelper()
    {
        _conn = System.Configuration.ConfigurationManager.AppSettings["ConnString"];

        _pagesize = Int32.MaxValue;
        _currentpage = 1;
        _rowcount = 0;
        _rowcount = 1;
        _pagelist = null;
        _sortFields = String.Empty;
        _sortDirection = String.Empty;
    }

    #endregion

    #region 公有屬性

    /// <summary>
    /// 資料庫連線字串
    /// </summary>
    public string Conn
    {
        get { return _conn; }
        set { _conn = value; }
    }

    /// <summary>
    /// 每頁筆數
    /// </summary>
    public int PageSize
    {
        get { return _pagesize; }
        set { _pagesize = value; }
    }

    /// <summary>
    /// 目前頁碼
    /// </summary>
    public int CurrentPage
    {
        get { return _currentpage; }
        set { _currentpage = value; }
    }

    /// <summary>
    /// 資料總筆數
    /// </summary>
    public int RowCount
    {
        get { return _rowcount; }
        set { _rowcount = value; }
    }
    /// <summary>
    /// 資料總頁數
    /// </summary>
    public int PageCount
    {
        get { return _pagecount; }
        set { _pagecount = value; }
    }
    public DataTable PageList
    {
        get { return _pagelist; }
        set { _pagelist = value; }
    }


    /// <summary>
    /// 排序欄位
    /// (當「每頁筆數(CurrentPage)」不為 Int32.MaxValue 時，才有作用)
    /// </summary>
    public string SortFields
    {
        get { return _sortFields; }
        set { _sortFields = value; }
    }

    /// <summary>
    /// 排序方向
    /// (當「每頁筆數(CurrentPage)」不為 Int32.MaxValue 時，才有作用)
    /// </summary>
    public string SortDirection
    {
        get { return _sortDirection; }
        set { _sortDirection = value; }
    }

    #endregion

    #region 私有函式

    private string pagerHeader()
    {
        return @"
DECLARE @UpperBand INT;
DECLARE @LowerBand INT;

SET @LowerBand  = ( @CurrentPage - 1 ) * @PageSize;
SET @UpperBand  = (@CurrentPage * @PageSize ) + 1;

";

    }

    private string pagerFooter()
    {
        string footer = @"
SELECT IDENTITY(int ,1, 1) AS serialno, *
INTO #Paging_MID
FROM #ForPaging
--
SELECT *
FROM #Paging_MID
WHERE serialno > @LowerBand AND serialno < @UpperBand

SELECT COUNT(*) AS [rowcount]
FROM #Paging_MID
DROP TABLE #ForPaging
DROP TABLE #Paging_MID
";
        if (_sortFields != string.Empty && _sortDirection != string.Empty)
        {
            footer = footer.Replace("--", "ORDER BY " + _sortFields + " " + _sortDirection);
        }

        return footer;

    }

    #endregion

    #region 公有函式
    /// <summary>
    /// 傳回資料表
    /// </summary>
    /// <param name="oCmd">Sql指令物件</param>
    /// <param name="oType">指令種類</param>
    /// <returns></returns>
    public DataTable getDataTable(SqlCommand oCmd, CommandType oType)
    {
        oCmd.Connection = new SqlConnection(_conn);
        oCmd.CommandType = oType;

        //有指定每頁筆數時，則使用分頁功能
        if (_pagesize != Int32.MaxValue)
        {
            if (oType == CommandType.Text)
            {
                oCmd.CommandText = pagerHeader() + oCmd.CommandText + pagerFooter();

                oCmd.Parameters.AddWithValue("@CurrentPage", _currentpage);
                oCmd.Parameters.AddWithValue("@PageSize", _pagesize);
            }
        }
        else
        {
            oCmd.CommandText = oCmd.CommandText.Replace("INTO #ForPaging", "");
        }

        oCmd.Connection.Open();
        oCmd.ExecuteReader();
        oCmd.Connection.Close();

        SqlDataAdapter oda = new SqlDataAdapter(oCmd);

        DataSet ds = new DataSet();
        oda.Fill(ds);

        DataTable dt = ds.Tables[0];
        if (ds.Tables.Count > 1)
        {
            _rowcount = ds.Tables[1].Rows[0][0] == DBNull.Value ? 0 : int.Parse(ds.Tables[1].Rows[0][0].ToString());

            //計算頁數
            if (_rowcount == 0)
            {
                _pagecount = 0;
            }
            else if (_rowcount <= _pagesize)
            {
                _pagecount = 1;
            }
            else
            {
                _pagecount = Convert.ToInt16(Math.Ceiling(Convert.ToDouble(_rowcount) / Convert.ToDouble(_pagesize)));
            }

            #region 頁清單
            //頁清單(只顯示10頁)
            int page_start = _currentpage - 4 <= 0 ? 1 : _currentpage - 4;
            int page_end = 0;
            if (_currentpage < 5)
            {
                if (page_start + (10 - _currentpage) > _pagecount)
                {
                    page_end = _pagecount;
                }
                else
                {
                    page_end = page_start + (10 - _currentpage);
                }
            }
            else
            {
                if (_currentpage + 5 > _pagecount)
                {
                    page_end = _pagecount;
                }
                else
                {
                    page_end = _currentpage + 5;
                }
            }

            _pagelist = new DataTable();
            _pagelist.Columns.Add("page", typeof(int));
            for (int i = page_start; i <= page_end; i++)
            {
                DataRow row = _pagelist.NewRow();
                row["page"] = i.ToString();
                _pagelist.Rows.Add(row);
            }
            #endregion
        }

        return dt;

    }

    /// <summary>
    /// 傳回資料集
    /// </summary>
    /// <param name="oCmd">Sql指令物件</param>
    /// <param name="oType">指令種類</param>
    /// <returns></returns>
    public DataSet getDataSet(SqlCommand oCmd, CommandType oType)
    {
        oCmd.Connection = new SqlConnection(_conn);
        oCmd.CommandType = oType;

        oCmd.Connection.Open();
        oCmd.ExecuteReader();
        oCmd.Connection.Close();

        SqlDataAdapter oda = new SqlDataAdapter(oCmd);

        DataSet ds = new DataSet();
        oda.Fill(ds);

        return ds;

    }

    /// <summary>
    /// 傳回第一個資料列的第一個資料行
    /// </summary>
    /// <param name="oCmd">Sql指令物件</param>
    /// <param name="oType">指令種類</param>
    /// <returns></returns>
    public string getTopOne(SqlCommand oCmd, CommandType oType)
    {
        oCmd.Connection = new SqlConnection(_conn);
        oCmd.CommandType = oType;

        oCmd.Connection.Open();
        SqlTransaction oTrans = oCmd.Connection.BeginTransaction();
        oCmd.Transaction = oTrans;
        object obj = String.Empty;
        string data = String.Empty;
        try
        {
            obj = oCmd.ExecuteScalar();
            if (obj != null)
            {
                data = obj.ToString();
            }
            oTrans.Commit();

        }
        catch (Exception e)
        {
            oTrans.Rollback();
            throw new Exception(e.Message);
        }
        finally
        {
            oCmd.Connection.Close();
        }

        return data;

    }

    /// <summary>
    /// 執行
    /// </summary>
    /// <param name="oCmd">Sql指令物件</param>
    /// <param name="oType">指令種類</param>
    /// <returns></returns>
    public int Execute(SqlCommand oCmd, CommandType oType)
    {
        int rows = 0;
        oCmd.Connection = new SqlConnection(_conn);
        oCmd.Connection.Open();
        oCmd.CommandType = oType;
        SqlTransaction oTrans = oCmd.Connection.BeginTransaction();
        oCmd.Transaction = oTrans;

        try
        {
            rows = oCmd.ExecuteNonQuery();
            oTrans.Commit();

        }
        catch (Exception e)
        {
            oTrans.Rollback();
            throw new Exception(e.Message);
        }
        finally
        {
            oCmd.Connection.Close();
        }

        return rows;


    }
    #endregion
}