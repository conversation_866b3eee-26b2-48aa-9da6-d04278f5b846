﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class TreatyCase2_Lawer : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            TB_recordate.Attributes.Add("readOnly", "readonly");

            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyCase2_Auth";
                //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Auth";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_auth = dt.DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if ((str_auth == "X") || (str_auth == "R"))
                        Response.Redirect("../NoAuthRight.aspx");
                }
                ViewState["sub_seno"] = "0";
                if (Request["sub_seno"] != null)
                {
                    int sj = 0;
                    if (!(int.TryParse(Request["sub_seno"], out sj)))
                        Response.Redirect("../danger.aspx");
                    ViewState["sub_seno"] = Request["sub_seno"];
                    //SDS_SC.SelectParameters.Clear();
                    //SDS_SC.SelectCommand = " select  tc2l_sub_seno , tc2l_lawer, tc2l_date,tc2l_content from treaty_case2_lawer where tc2l_sub_seno = @subseno ";
                    //SDS_SC.SelectParameters.Add("subseno", ViewState["sub_seno"].ToString());
                    //System.Data.DataView dv = (DataView)SDS_SC.Select(new DataSourceSelectArguments());

                    #region --- query ---

                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @" select  tc2l_sub_seno , tc2l_lawer, tc2l_date,tc2l_content from treaty_case2_lawer where tc2l_sub_seno = @subseno ";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@subseno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));

                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                            sqlDA.Fill(dt);

                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv = dt.DefaultView;
                    if (dv.Count >= 1)
                    {
                        TB_recordate.Text = Server.HtmlEncode(dv[0]["tc2l_date"].ToString());
                        TB_Lawer.Text = Server.HtmlEncode(dv[0]["tc2l_lawer"].ToString());
                        TB_Docu.Text = Server.HtmlEncode(dv[0]["tc2l_content"].ToString());
                    }
                }
            }
            else
                Response.Redirect("../NoAuthRight.aspx");
        }


    }


    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if ((TB_recordate.Text == ""))
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('委任日期 必須填寫 !');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            if (!IsNumber(TB_recordate.Text.Replace("/", "")) || (TB_recordate.Text.Length > 10))
                Response.Redirect("../danger.aspx");

            if (TB_Docu.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_Lawer.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_Lawer.Text.Length > 10)
                Response.Redirect("../danger.aspx");

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            if (ViewState["sub_seno"].ToString() != "0")
            {
                //this.SDS_SC.UpdateParameters.Clear();
                //this.SDS_SC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                //this.SDS_SC.UpdateCommand = "esp_TreatyCase2_Lawer";
                //this.SDS_SC.UpdateParameters.Add("seno", ViewState["seno"].ToString());
                //this.SDS_SC.UpdateParameters.Add("subseno", ViewState["sub_seno"].ToString());
                //this.SDS_SC.UpdateParameters.Add("empno", ssoUser.empNo);
                //this.SDS_SC.UpdateParameters.Add("recordate", TB_recordate.Text);
                //this.SDS_SC.UpdateParameters.Add("Lawer", TB_Lawer.Text);
                //this.SDS_SC.UpdateParameters.Add("docu", TB_Docu.Text);
                //this.SDS_SC.UpdateParameters.Add("mode", "Update");
                //this.SDS_SC.Update();
                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Lawer";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@subseno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    sqlCmd.Parameters.AddWithValue("@recordate", oRCM.SQLInjectionReplaceAll(TB_recordate.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@Lawer", oRCM.SQLInjectionReplaceAll(TB_Lawer.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@docu", oRCM.SQLInjectionReplaceAll(TB_Docu.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@mode", "Update");


                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
            }
            else
            {
                //this.SDS_SC.InsertParameters.Clear();
                //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
                //this.SDS_SC.InsertCommand = "esp_TreatyCase2_Lawer";
                //this.SDS_SC.InsertParameters.Add("seno",       ViewState["seno"].ToString());
                //this.SDS_SC.InsertParameters.Add("subseno",   ViewState["sub_seno"].ToString());
                //this.SDS_SC.InsertParameters.Add("empno",      ssoUser.empNo);
                //this.SDS_SC.InsertParameters.Add("recordate",  TB_recordate.Text );
                //this.SDS_SC.InsertParameters.Add("Lawer", TB_Lawer.Text);
                //this.SDS_SC.InsertParameters.Add("docu",   TB_Docu.Text);
                //this.SDS_SC.InsertParameters.Add("mode", "Insert");
                //this.SDS_SC.Insert();

                #region --- insert ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Lawer";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@subseno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    sqlCmd.Parameters.AddWithValue("@recordate", oRCM.SQLInjectionReplaceAll(TB_recordate.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@Lawer", oRCM.SQLInjectionReplaceAll(TB_Lawer.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@docu", oRCM.SQLInjectionReplaceAll(TB_Docu.Text.Trim()));
                    sqlCmd.Parameters.AddWithValue("@mode", "Insert");


                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
            }
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

        }

    }
}