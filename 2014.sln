﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35208.52
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "2014", ".", "{34B6E054-1CC7-483C-A4AF-3497CC3A1FDC}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8"
		Debug.AspNetCompiler.VirtualPath = "/localhost_56259"
		Debug.AspNetCompiler.PhysicalPath = "..\2014\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_56259\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_56259"
		Release.AspNetCompiler.PhysicalPath = "..\2014\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_56259\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "56259"
		SlnRelativePath = "..\2014\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{34B6E054-1CC7-483C-A4AF-3497CC3A1FDC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34B6E054-1CC7-483C-A4AF-3497CC3A1FDC}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {64BBC387-5E66-4435-BC0F-854A035E9BA0}
	EndGlobalSection
EndGlobal
