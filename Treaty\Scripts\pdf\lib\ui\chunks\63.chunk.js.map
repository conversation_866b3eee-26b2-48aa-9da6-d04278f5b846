{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/he.js"], "names": ["module", "exports", "Y", "d", "default", "M", "e", "s", "ss", "m", "mm", "h", "hh", "hh2", "dd", "dd2", "MM", "MM2", "y", "yy", "yy2", "_", "replace", "l", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "relativeTime", "future", "past", "ordinal", "format", "LT", "LTS", "L", "LL", "LLL", "LLLL", "ll", "lll", "llll", "formats", "locale"], "mappings": "+EAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,EAAE,aAAaC,GAAG,WAAWC,EAAE,MAAMC,GAAG,UAAUC,EAAE,MAAMC,GAAG,UAAUC,IAAI,SAASV,EAAE,MAAMW,GAAG,UAAUC,IAAI,SAASV,EAAE,OAAOW,GAAG,YAAYC,IAAI,UAAUC,EAAE,MAAMC,GAAG,UAAUC,IAAI,UAAU,SAASC,EAAEnB,EAAEG,EAAEF,GAAG,OAAOG,EAAEH,GAAG,IAAID,EAAE,IAAI,MAAMI,EAAEH,IAAImB,QAAQ,KAAKpB,GAAG,IAAIqB,EAAE,CAACC,KAAK,KAAKC,SAAS,uCAAuCC,MAAM,KAAKC,cAAc,uBAAuBD,MAAM,KAAKE,YAAY,sBAAsBF,MAAM,KAAKG,OAAO,0EAA0EH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,aAAa,CAACC,OAAO,UAAUC,KAAK,UAAU1B,EAAEc,EAAEZ,EAAEY,EAAEX,GAAGW,EAAEV,EAAEU,EAAET,GAAGS,EAAElB,EAAEkB,EAAEP,GAAGO,EAAEhB,EAAEgB,EAAEL,GAAGK,EAAEH,EAAEG,EAAEF,GAAGE,GAAGa,QAAQ,SAAShC,GAAG,OAAOA,GAAGiC,OAAO,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,iBAAiBC,IAAI,uBAAuBC,KAAK,6BAA6BlB,EAAE,WAAWmB,GAAG,aAAaC,IAAI,mBAAmBC,KAAK,yBAAyBC,QAAQ,CAACT,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,iBAAiBC,IAAI,uBAAuBC,KAAK,6BAA6BlB,EAAE,WAAWmB,GAAG,aAAaC,IAAI,mBAAmBC,KAAK,0BAA0B,OAAOzC,EAAEC,QAAQ0C,OAAOvB,EAAE,MAAK,GAAIA,EAAj7ClB,CAAE,EAAQ", "file": "chunks/63.chunk.js", "sourcesContent": ["!function(Y,M){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=M(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],M):(Y=\"undefined\"!=typeof globalThis?globalThis:Y||self).dayjs_locale_he=M(Y.dayjs)}(this,(function(Y){\"use strict\";function M(Y){return Y&&\"object\"==typeof Y&&\"default\"in Y?Y:{default:Y}}var d=M(Y),e={s:\"מספר שניות\",ss:\"%d שניות\",m:\"דקה\",mm:\"%d דקות\",h:\"שעה\",hh:\"%d שעות\",hh2:\"שעתיים\",d:\"יום\",dd:\"%d ימים\",dd2:\"יומיים\",M:\"חודש\",MM:\"%d חודשים\",MM2:\"חודשיים\",y:\"שנה\",yy:\"%d שנים\",yy2:\"שנתיים\"};function _(Y,M,d){return(e[d+(2===Y?\"2\":\"\")]||e[d]).replace(\"%d\",Y)}var l={name:\"he\",weekdays:\"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת\".split(\"_\"),weekdaysShort:\"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳\".split(\"_\"),weekdaysMin:\"א׳_ב׳_ג׳_ד׳_ה׳_ו_ש׳\".split(\"_\"),months:\"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר\".split(\"_\"),monthsShort:\"ינו_פבר_מרץ_אפר_מאי_יונ_יול_אוג_ספט_אוק_נוב_דצמ\".split(\"_\"),relativeTime:{future:\"בעוד %s\",past:\"לפני %s\",s:_,m:_,mm:_,h:_,hh:_,d:_,dd:_,M:_,MM:_,y:_,yy:_},ordinal:function(Y){return Y},format:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [ב]MMMM YYYY\",LLL:\"D [ב]MMMM YYYY HH:mm\",LLLL:\"dddd, D [ב]MMMM YYYY HH:mm\",l:\"D/M/YYYY\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd, D MMM YYYY HH:mm\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [ב]MMMM YYYY\",LLL:\"D [ב]MMMM YYYY HH:mm\",LLLL:\"dddd, D [ב]MMMM YYYY HH:mm\",l:\"D/M/YYYY\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd, D MMM YYYY HH:mm\"}};return d.default.locale(l,null,!0),l}));"], "sourceRoot": ""}