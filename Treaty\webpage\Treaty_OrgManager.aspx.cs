﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_OrgManager : Treaty.common   //System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        if (strNumber == "") return true;
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    private string 管理人員
    {
        get
        {
            string leader = txt_px_empno.Text.Trim();
            if(leader !="")
            {
                if (Regex.IsMatch(leader, "^[0-9A-Z]*$") == false)
                    Response.Redirect("../danger.aspx");
            }
             return leader;
        }
    }
    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;

    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            /*
                    SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
                    SDS_auth.SelectCommand = " select count(*) from treaty_buztbl where emp_group='0' and emp_no=@empno";
                    SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                    for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                    {
                        SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                    }
                    SDS_auth.DataBind();
                    System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                    if (dv_auth.Count >= 1)
                    {
                        ViewState["SYS"] = ssoUser.empNo;
                    }
                    if (dv_auth.Count == 0)
                    {
                            Response.Redirect("../NoAuthRight.aspx");
                    }
            */
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = " select count(*) from treaty_buztbl where emp_group='0' and emp_no=@empno ";
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            oCmd.CommandType = CommandType.Text;
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "myTable");
            if (ds != null && ds.Tables[0].Rows.Count == 0)
            {
                Response.Redirect("../NoAuthRight.aspx");
            }
            if (ds != null && ds.Tables[0].Rows.Count >= 1)
            {
                ViewState["SYS"] = ssoUser.empNo;
            }

            BindOrg();
            Binddata();
        }
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_search));
    }
    private void BindOrg()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_Orgcd.SelectCommand = "exec esp_treaty_search_case_statistic_orglist '" + ssoUser.empNo + "' ";
        //SDS_Orgcd.DataBind();
        //ddlOrgcd.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_case_statistic_orglist";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlOrgcd.DataSource = dt;
                ddlOrgcd.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Binddata()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_search.SelectCommand = " select adm_org,( select rtrim(org_abbr_chnm2)  from common..orgcod where org_orgcd= adm_org ) org_name ,(select rtrim(com_cname)  from common..comper where com_empno=adm_empno) adm_name ,adm_empno   from treaty_buztbl_adm_other order by adm_org ";
        //SDS_search.DataBind();
        //SGV_search.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select adm_org,( select rtrim(org_abbr_chnm2)  from common..orgcod where org_orgcd= adm_org ) org_name ,(select rtrim(com_cname)  from common..comper where com_empno=adm_empno) adm_name ,adm_empno   from treaty_buztbl_adm_other order by adm_org ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {
        if ((txt_px_empno.Text == ""))
        {
            string script_alert = "<script language='javascript'> alert('人員需要挑選');</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            foreach (ListItem li in ddlOrgcd.Items)
            {
                if (li.Selected == true)
                {
                    //SDS_search.InsertParameters.Clear();
                    //SDS_search.InsertCommandType = SqlDataSourceCommandType.Text;
                    //SDS_search.InsertCommand = "insert treaty_buztbl_adm_other values( '" + li.Value + "', '" + txt_px_empno.Text + "') ";
                    //SDS_search.Insert();
                    #region --- insert ---

                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @"insert treaty_buztbl_adm_other values( @p1, @p2) ";

                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@p1", oRCM.SQLInjectionReplaceAll(li.Value));
                        sqlCmd.Parameters.AddWithValue("@p2", oRCM.SQLInjectionReplaceAll(管理人員));

                        try
                        {
                            sqlConn.Open();

                            sqlCmd.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {

                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                }
            }
            Binddata();
        }
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata();
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "case_del")
        {
            string scriptSendRError;
            string[] s = e.CommandArgument.ToString().Split('@');
            //SDS_search.DeleteParameters.Clear();
            //SDS_search.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_search.DeleteCommand = "delete treaty_buztbl_adm_other  where  adm_empno='" + s[0] + "' and adm_org='" + s[1] + "' ";
            //SDS_search.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_buztbl_adm_other  where  adm_empno=@empno and adm_org=@org";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(s[0]));
                sqlCmd.Parameters.AddWithValue("@org", oRCM.SQLInjectionReplaceAll(s[1]));
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            Binddata();
        }
    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {


    }
    protected void HecUpdate(object sender, System.EventArgs e)
    {
    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata();
    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }


}