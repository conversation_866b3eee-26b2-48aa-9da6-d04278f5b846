﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;

public partial class TreatyApply : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {
            case "N":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視NDA資訊";
                break;
            //case "U":
            //    strResult = "<img src='../images/icon-1301.gif' />檢視國外契約資訊";
            //    break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視標案資訊";
                break;
            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion
    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            case "U":
                bResult = true;
                break;
            case "R":
                bResult = true;
                break;
            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion
    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    public void GetEngageNDAOnClick(string strCaseNo)
    {
        //string strCaseNo = txtComplexNo.Text.Trim();
        string strCaseNo_C = txtOldContno.Text.Trim();
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();

        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["IndusURL"].ToString();

        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案
                strWinOpen = string.Format("{0}/engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo);
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                strWinOpen = string.Format("{0}/nda/NDA_readonly.aspx?kind=NDA&seqsn=&contno={1}&style=2&readstyle=2", strNDA_Path, strCaseNo.Substring(0, 12));
                break;

            case "A": // 國外契約
                strWinOpen = string.Format(@"window.open('{0}');	return false;", strWinOpen);
                break;

            case "F": // 國內契約
                strWinOpen = string.Format("{0}/norcontIN_baseView.aspx?contno=' + c_id + '&type=G&role=role_handler", strNDA_Path, strCaseNo.Replace("=", ""));
                break;

            case "C": // 工服
                strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                break;


        }
        script = @" <script> window.open('" + HttpUtility.HtmlEncode(strWinOpen) + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
    }
    #endregion
    protected void btnEngage_Click(object sender, EventArgs e)
    {
        GetEngageNDAOnClick(txtComplexNo.Text.Trim());
    }

    private string 匯率
    {
        get
        {
            string str_字串 = TB_money_rate.Text.Trim().Replace(".", "");
            if (str_字串 == "") return "";
            if (Regex.IsMatch(str_字串, "^[0-9]*$") == false)
                Response.Redirect("../danger.aspx");
            return TB_money_rate.Text.Trim();
        }
    }
    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }

    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            BT_Customer.Attributes.Add("onclick", "find_customer2();");
            txt_promoter_name.Attributes.Add("onChange", string.Format("Find_empno_kw('{0}',1);", txt_promoter_name.ClientID));
            txt_px_name.Attributes.Add("onChange", string.Format("Find_empno_kw('{0}',2);", txt_px_name.ClientID));
            BindContMoneyType();
            string strCaseNo = "";
            if (Request["contno"] != null)
            {
                if (Request["contno"].Length > 15)
                    Response.Redirect("../danger.aspx");
                if (!IsNatural_Number(Request["contno"].ToString().Replace("-", "")))
                    Response.Redirect("../danger.aspx");
                strCaseNo = Request["contno"].ToString();
                if (!IsNumber(Request["contno"].ToString().Substring(0, 4)))
                    Response.Redirect("../danger.aspx");
                if (!IsNumber(Request["contno"].ToString().Substring(4, 2)))
                    Response.Redirect("../danger.aspx");
                if (!IsNumber(Request["contno"].ToString().Substring(7, 4)))
                    Response.Redirect("../danger.aspx");

                #region 檢查NRMA 權限
                //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyApply_Auth";
                //SDS_auth.SelectParameters.Add("seno", "");
                //SDS_auth.SelectParameters.Add("contno", strCaseNo);
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                DataSet ds = new DataSet();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_TreatyApply_Auth");
                    oCmd.Parameters.AddWithValue("seno", "");
                    oCmd.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                    oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;

                    SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                    oda.Fill(ds);
                }

                //SqlCommand oCmd = new SqlCommand();
                //oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                //oCmd.CommandType = CommandType.StoredProcedure;
                //oCmd.CommandText = "esp_TreatyApply_Auth";
                //oCmd.Parameters.AddWithValue("seno", "");
                //oCmd.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                //oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                //SqlDataAdapter oda = new SqlDataAdapter(oCmd);

                //oda.Fill(ds, "myTable");
                DataView dv_auth = ds.Tables[0].DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if (str_auth == "X")
                        Response.Redirect("../NoAuthRight.aspx");
                }
                //ds.Dispose();
                //oCmd.Dispose();
                //oda.Dispose();

                #endregion
                #region 檢查NRMA 是否還有草稿未送出
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyApply_ANRM_request_check";
                //SDS_NR.SelectParameters.Add("contno", TypeCode.String, strCaseNo);
                //SDS_NR.DataBind();
                //System.Data.DataView dv_ANMR_check = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                DataSet ds_2 = new DataSet();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_TreatyApply_ANRM_request_check");
                    oCmd.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;

                    SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                    oda.Fill(ds_2);
                }
                DataView dv_ANMR_check = ds_2.Tables[0].DefaultView;
                if (dv_ANMR_check.Count >= 1)
                {
                    Response.Redirect("../doubleCase.aspx");
                }
                //SqlCommand oCmd_2 = new SqlCommand();
                //oCmd_2.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                //oCmd_2.CommandType = CommandType.StoredProcedure;
                //oCmd_2.CommandText = "esp_TreatyApply_ANRM_request_check";
                //oCmd_2.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                //SqlDataAdapter oda_2 = new SqlDataAdapter(oCmd_2);
                //oda_2.Fill(ds_2, "myTable");
                //ds_2.Dispose();
                //oCmd_2.Dispose();
                //oda_2.Dispose();
                #endregion

                //this.SDS_NR.SelectParameters.Clear();
                //this.SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                //this.SDS_NR.SelectCommand = "esp_TreatyApplyBaseData_ANMR";
                //this.SDS_NR.SelectParameters.Add("contno", TypeCode.String, strCaseNo);
                //this.SDS_NR.DataBind();
                //System.Data.DataView dv_ANMR = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                //SqlCommand oCmd_3 = new SqlCommand();
                //oCmd_3.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                //oCmd_3.CommandType = CommandType.StoredProcedure;
                //oCmd_3.CommandText = "esp_TreatyApplyBaseData_ANMR";
                //oCmd_3.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                //SqlDataAdapter oda_3 = new SqlDataAdapter(oCmd_3);
                DataSet ds_3 = new DataSet();
                //oda_3.Fill(ds_3, "myTable");
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_TreatyApplyBaseData_ANMR");
                    oCmd.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(strCaseNo));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;

                    SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                    oda.Fill(ds_3);
                }

                DataView dv_ANMR = ds_3.Tables[0].DefaultView;

                #region 案件性質
                if (dv_ANMR.Count >= 1)
                {
                    #region 案件性質
                    switch (Request["contno"].Substring(6, 1))
                    {
                        case "A":
                            #region A
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString()));
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            cb_conttype_b0.Enabled = false;
                            cb_conttype_b1.Enabled = false;
                            cb_conttype_d4.Enabled = false;
                            cb_conttype_d5.Enabled = false;
                            cb_conttype_d7.Enabled = false;
                            cb_conttype_rb.Enabled = false;
                            cb_conttype_m.Enabled = false;
                            cb_conttype_c.Enabled = false;
                            rb_conttype_uo.Enabled = true;
                            rb_conttype_uo.Checked = true;
                            rb_conttype_ui.Enabled = false;
                            rb_conttype_other.Enabled = false;
                            #endregion
                            break;
                        case "M":
                            #region M
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString()));
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            cb_conttype_b0.Enabled = false;
                            cb_conttype_b1.Enabled = false;
                            cb_conttype_d4.Enabled = false;
                            cb_conttype_d5.Enabled = false;
                            cb_conttype_d7.Enabled = false;
                            cb_conttype_rb.Enabled = false;
                            cb_conttype_m.Enabled = true;
                            cb_conttype_m.Checked = true;
                            cb_conttype_c.Enabled = false;
                            rb_conttype_uo.Enabled = false;
                            rb_conttype_ui.Enabled = false;
                            rb_conttype_other.Enabled = false;
                            txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empOrgname));
                            x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(ssoUser.empDeptcd));
                            txt_req_dept.Value = ssoUser.empOrgcd + ssoUser.empDeptcd;
                            AMPS.common c = new AMPS.common();
                            //txtOrgAbbrName.Text =
                            //x_dept.Text =  
                            //txt_promoter_name.Text = c.GetEmpName(dv_ANMR[0][5].ToString());
                            //txt_promoter_empno.Value = dv_ANMR[0][5].ToString();
                            //txtTel.Text = c.GetEmptel(dv_ANMR[0][5].ToString());
                            txt_contsdate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][6].ToString()));
                            txt_contedate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][7].ToString()));
                            if (dv_ANMR[0][4].ToString() == "2")
                                rb_language_english.Checked = true;
                            else
                                rb_language_chiness.Checked = true;

                            #endregion
                            break;
                        case "N":
                            #region N
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString())) + "A-01";
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            cb_conttype_rb.Enabled = false;
                            cb_conttype_m.Enabled = false;
                            cb_conttype_c.Enabled = false;
                            rb_conttype_uo.Enabled = false;
                            rb_conttype_ui.Enabled = false;
                            rb_conttype_other.Enabled = false;
                            #endregion
                            break;
                        case "R":
                            #region R
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString())) + "A-01";
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            cb_conttype_b0.Enabled = false;
                            cb_conttype_b1.Enabled = false;
                            cb_conttype_d4.Enabled = false;
                            cb_conttype_d5.Enabled = false;
                            cb_conttype_d7.Enabled = false;
                            cb_conttype_rb.Checked = true;
                            cb_conttype_m.Enabled = false;
                            cb_conttype_c.Enabled = false;
                            rb_conttype_uo.Enabled = false;
                            rb_conttype_ui.Enabled = false;
                            rb_conttype_other.Enabled = false;
                            rb_other_T.Checked = true;
                            txt_otherrequire_desc.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0]["endtxt"].ToString()));
                            #endregion
                            break;
                        case "F":
                            #region A
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString())) + "A-01";
                            if (dv_ANMR[0][4].ToString() == "1")
                                rb_language_chiness.Checked = true;
                            else
                                rb_language_english.Checked = true;
                            //ddlContType.SelectedValue = dv_ANMR[0][3].ToString();
                            txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][14].ToString()));
                            txt_req_dept.Value = dv_ANMR[0][5].ToString() + dv_ANMR[0][6].ToString();
                            x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][6].ToString()));
                            txt_promoter_empno.Value = dv_ANMR[0][7].ToString();
                            txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][8].ToString()));
                            txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][9].ToString()));

                            txtContMoney.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][10].ToString()));
                            txt_contsdate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][11].ToString()));
                            txt_contedate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][12].ToString()));
                            /*20230630 預設拿掉 統一於 轉承辦處理
                                                        if (dv_ANMR[0][12].ToString() != "")
                                                        {
                                                            txt_otherrequire_desc.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][13].ToString()));
                                                            rb_other_T.Checked = true;
                                                        }
                            */
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            cb_conttype_b0.Enabled = false;
                            cb_conttype_b1.Enabled = false;
                            cb_conttype_d4.Enabled = false;
                            cb_conttype_d5.Enabled = false;
                            cb_conttype_d7.Enabled = false;
                            cb_conttype_rb.Enabled = false;
                            cb_conttype_m.Enabled = false;
                            cb_conttype_c.Enabled = false;
                            rb_conttype_uo.Enabled = false;
                            rb_conttype_ui.Checked = true;
                            rb_conttype_ui.Enabled = true;
                            rb_conttype_other.Enabled = false;
                            #endregion
                            break;
                        case "C":
                            #region C
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][0].ToString())) + "A-01";
                            txtComplexNo.Visible = true;
                            txtComplexNo.Attributes.Add("class", "TB_ReadOnly");
                            txtOldContno.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][4].ToString()));

                            ddlContMoneyType.SelectedValue = dv_ANMR[0][5].ToString();
                            TB_money_rate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][6].ToString()));
                            txtContMoney.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][7].ToString()));
                            cb_conttype_rb.Enabled = false;
                            cb_conttype_m.Enabled = false;
                            cb_conttype_c.Enabled = true;
                            cb_conttype_c.Checked = true;
                            rb_conttype_uo.Enabled = false;
                            rb_conttype_ui.Enabled = false;
                            rb_conttype_other.Enabled = false;
                            ddlContType.SelectedValue = dv_ANMR[0][3].ToString();
                            #endregion
                            break;
                    }
                    txtTel.Attributes.Add("readOnly", "readonly");
                    #endregion
                    GetEngageNDAText(Request["contno"].ToString());
                    btnEngage.Visible = GetEngageNDAVisible(Request["contno"].ToString());
                    txtComplexNo.ReadOnly = true;
                    txt_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_ANMR[0][1].ToString()));
                    h_compno.Value = dv_ANMR[0][2].ToString().Replace("㊣", ",");
                    BindData_Customer();
                    string str_casetype = dv_ANMR[0][3].ToString();
                    if (Request["contno"].Substring(6, 1) != "F")
                    {
                        if (str_casetype.IndexOf("B0") >= 0)
                            cb_conttype_b0.Checked = true;
                        if (str_casetype.IndexOf("B1") >= 0)
                            cb_conttype_b1.Checked = true;
                        if (str_casetype.IndexOf("D4") >= 0)
                            cb_conttype_d4.Checked = true;
                        if (str_casetype.IndexOf("D5") >= 0)
                            cb_conttype_d5.Checked = true;
                        if (str_casetype.IndexOf("D7") >= 0)
                            cb_conttype_d7.Checked = true;
                        if (str_casetype.IndexOf("NS") >= 0)
                            cb_conttype_ns.Checked = true;
                        if (str_casetype.IndexOf("RB") >= 0)
                            cb_conttype_rb.Checked = true;
                        if (str_casetype.IndexOf("UO") >= 0)
                            rb_conttype_uo.Checked = true;
                        if (str_casetype.IndexOf("UI") >= 0)
                            rb_conttype_ui.Checked = true;
                        if (str_casetype.IndexOf("MN") >= 0)
                            cb_conttype_m.Checked = true;
                        if (str_casetype.IndexOf("MS") >= 0)
                            cb_conttype_m.Checked = true;
                        BindContType("");
                    }
                    else
                        BindContType(str_casetype);

                    if (str_casetype.IndexOf("已簽約") >= 0) //洽案已簽約時 , 議約需求需填契約修訂
                        spanContractEdit.Visible = true;

                }
                else
                { //倒到錯誤頁面
                    Response.Redirect("../error.aspx");
                }
                #endregion
                //ds_3.Dispose();
                //oCmd_3.Dispose();
                //oda_3.Dispose();
            }
            else
            {
                if (Request["seno"] != null)
                {
                    if ((Request["seno"].Length == 0) || (Request["seno"].Length > 8))
                        Response.Redirect("../danger.aspx");
                    if (!IsNumber(Request["seno"].ToString()))
                        Response.Redirect("../danger.aspx");
                    if (Request["newver"] != null)
                    {
                        if (Request["newver"].Length > 1)
                            Response.Redirect("../danger.aspx");
                        if (!IsNumber(Request["newver"].ToString()))
                            Response.Redirect("../danger.aspx");

                        if (!(Request["newver"].ToString() == "1" || Request["newver"].ToString() == "2"))
                            Response.Redirect("../danger.aspx");

                        // SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                        // SDS_NR.SelectParameters.Clear();
                        // SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                        // SDS_NR.SelectCommand = "esp_TreatyApply_NewContno";
                        // SDS_NR.SelectParameters.Add("seno", TypeCode.String, Request["seno"].ToString());
                        //if (Request["newver"].ToString() == "1") //新件
                        //{ 
                        //   this.SDS_NR.SelectParameters.Add("ver","1");
                        //    BT_Customer.Visible = false;
                        //}
                        //if (Request["newver"].ToString() == "2")//新版
                        //    this.SDS_NR.SelectParameters.Add("ver", "2");
                        //this.SDS_NR.SelectParameters.Add("contno", "");
                        //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                        //{
                        //    this.SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                        //}
                        //System.Data.DataView dv_contno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                        //SqlCommand oCmd_4 = new SqlCommand();
                        //oCmd_4.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                        //oCmd_4.CommandType = CommandType.StoredProcedure;
                        //oCmd_4.CommandText = "esp_TreatyApply_NewContno";
                        //oCmd_4.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(Request["seno"].ToString()));
                        //if (Request["newver"].ToString() == "1") //新件
                        //{
                        //    oCmd_4.Parameters.AddWithValue("ver", "1");
                        //    BT_Customer.Visible = false;
                        //}
                        //if (Request["newver"].ToString() == "2")//新版
                        //    oCmd_4.Parameters.AddWithValue("ver", "2");
                        //oCmd_4.Parameters.AddWithValue("contno", "");
                        //SqlDataAdapter oda_4 = new SqlDataAdapter(oCmd_4);
                        DataSet ds_4 = new DataSet();
                        //oda_4.Fill(ds_4, "myTable");

                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            sqlConn.Open();
                            SqlCommand oCmd = new SqlCommand();
                            oCmd.Connection = sqlConn;
                            StringBuilder sb = new StringBuilder();
                            sb.Append(@"esp_TreatyApply_NewContno");
                            oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(Request["seno"].ToString()));
                            if (Request["newver"].ToString() == "1") //新件
                            {
                                oCmd.Parameters.AddWithValue("ver", "1");
                                BT_Customer.Visible = false;
                            }
                            if (Request["newver"].ToString() == "2")//新版
                                oCmd.Parameters.AddWithValue("ver", "2");
                            oCmd.Parameters.AddWithValue("contno", "");
                            oCmd.CommandText = sb.ToString();
                            oCmd.CommandType = CommandType.StoredProcedure;
                            oCmd.CommandTimeout = 0;

                            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                            oda.Fill(ds_4);
                        }

                        DataView dv_contno = ds_4.Tables[0].DefaultView;

                        if (dv_contno.Count >= 1)
                        {
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][0].ToString()));
                            GetEngageNDAText(txtComplexNo.Text);
                            btnEngage.Visible = GetEngageNDAVisible(txtComplexNo.Text);
                            h_compno.Value = dv_contno[0][1].ToString();
                            string str_casetype = dv_contno[0][2].ToString();
                            if (txtComplexNo.Text.Substring(6, 1) == "T")
                            {
                                rb_conttype_other.Checked = true;
                                txt_class_other_desc.Enabled = true;
                                PL_CoPromoter.Visible = true;
                            }
                            else
                            {
                                if (str_casetype.IndexOf("B0") >= 0)
                                    cb_conttype_b0.Checked = true;
                                if (str_casetype.IndexOf("B1") >= 0)
                                    cb_conttype_b1.Checked = true;
                                if (str_casetype.IndexOf("D4") >= 0)
                                    cb_conttype_d4.Checked = true;
                                if (str_casetype.IndexOf("D5") >= 0)
                                    cb_conttype_d5.Checked = true;
                                if (str_casetype.IndexOf("D7") >= 0)
                                    cb_conttype_d7.Checked = true;
                                if (str_casetype.IndexOf("NS") >= 0)
                                    cb_conttype_ns.Checked = true;
                                if (str_casetype.IndexOf("RB") >= 0)
                                    cb_conttype_rb.Checked = true;
                                if (txtComplexNo.Text.Substring(6, 1) == "A")
                                    rb_conttype_uo.Checked = true;
                                if (txtComplexNo.Text.Substring(6, 1) == "F")
                                    rb_conttype_ui.Checked = true;
                                if (str_casetype.IndexOf("MN") >= 0)
                                    cb_conttype_m.Checked = true;
                                if (str_casetype.IndexOf("MS") >= 0)
                                    cb_conttype_m.Checked = true;
                            }
                            if (txtComplexNo.Text.Substring(6, 1) == "M")
                            {
                                txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][16].ToString()));
                                x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][17].ToString().Substring(2, 5)));
                                txt_req_dept.Value = dv_contno[0][17].ToString();
                                txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][18].ToString()));
                                txt_promoter_empno.Value = dv_contno[0][12].ToString();
                                txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][19].ToString()));
                                txt_contsdate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][13].ToString()));
                                txt_contedate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][14].ToString()));
                                if (dv_contno[0][11].ToString() == "2")
                                    rb_language_english.Checked = true;
                                else
                                    rb_language_chiness.Checked = true;
                            }
                            if (txtComplexNo.Text.Substring(6, 1) == "F")
                            {
                                txtOrgAbbrName.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][16].ToString()));
                                x_dept.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][17].ToString().Substring(2, 5)));
                                txt_req_dept.Value = dv_contno[0][17].ToString();
                                txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][18].ToString()));
                                txt_promoter_empno.Value = dv_contno[0][12].ToString();
                                txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][19].ToString()));
                                txt_contsdate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][13].ToString()));
                                txt_contedate.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][14].ToString()));
                                txt_promoter_empno.Value = dv_contno[0][7].ToString();
                                txt_promoter_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][8].ToString()));
                                txtTel.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][9].ToString()));
                                if (dv_contno[0][11].ToString() == "2")
                                    rb_language_english.Checked = true;
                                else
                                    rb_language_chiness.Checked = true;
                                if (dv_contno[0][20].ToString() != "")
                                {
                                    txt_otherrequire_desc.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][20].ToString()));
                                    rb_other_T.Checked = true;
                                }
                            }
                            if (txtComplexNo.Text.Substring(6, 1) == "C")
                            {
                                cb_conttype_c.Checked = true;
                                txtOldContno.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][11].ToString()));
                                txtOldContno.Visible = true;
                            }
                            if (str_casetype.IndexOf("已簽約") >= 0) //洽案已簽約時 , 議約需求需填契約修訂
                                spanContractEdit.Visible = true;

                            txt_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][3].ToString()));
                            BindContMoneyType();
                            ddlContMoneyType.SelectedValue = dv_contno[0][4].ToString();
                            txtContMoney.Text = Server.HtmlEncode(oRCM.RemoveXss(dv_contno[0][5].ToString()));
                            if (txtComplexNo.Text.Substring(6, 1) == "F")
                            {
                                BindContType(dv_contno[0][2].ToString());
                            }
                            else
                                BindContType("");
                            if (txtComplexNo.Text.Substring(6, 1) == "C")
                            {
                                ddlContType.SelectedValue = dv_contno[0][2].ToString();
                                if (ddlContMoneyType.SelectedValue == "TWD")
                                    TB_money_rate.Text = "1";
                            }

                            BindData_Customer();
                        }
                        if (Request["newver"] == null)
                        {
                            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(Request["seno"].ToString())) + "A-01";

                        }
                        txtComplexNo.Visible = true;
                        //ds_4.Dispose();
                        //oCmd_4.Dispose();
                        //oda_4.Dispose();
                    }
                }
                else
                {
                    if ((Request["contno"] == null) && (Request["seno"] == null)) //其他議約需求
                    {
                        rb_conttype_bd.Enabled = true;
                        rb_conttype_other.Enabled = true;
                        rb_conttype_other.Checked = true;
                        cb_conttype_c.Enabled = false;
                        BindContType("");
                        txt_class_other_desc.Enabled = true;

                        PL_CoPromoter.Visible = true;
                        btnEngage.Visible = false;
                    }
                }
                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
            }
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            x_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            lb_keyin_emp_no.Text = Server.HtmlEncode(ssoUser.empNo);       //建檔人工號
            lb_keyin_emp_name.Text = Server.HtmlEncode(ssoUser.empName);   //建檔人名稱
            lb_keyin_tel.Text = Server.HtmlEncode(ssoUser.empTelext);      //建檔人分機
            lb_keyin_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期
            lb_modify_emp_no.Text = Server.HtmlEncode(ssoUser.empNo);       //建檔人工號
            lb_modify_emp_name.Text = Server.HtmlEncode(ssoUser.empName);   //建檔人名稱
            lb_modify_tel.Text = Server.HtmlEncode(ssoUser.empTelext);      //建檔人分機
            lb_modify_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期

            if ((Request["contno"] == null) && (Request["seno"] == null)) //其他議約需求
            {
                ViewState["tr_class"] = "T";
            }

            else
            {
                ViewState["tr_class"] = txtComplexNo.Text.Substring(6, 1);
                rb_conttype_other.Enabled = false;
                rb_conttype_bd.Enabled = false;
            }

            cb_conttype_b0.Enabled = false;
            cb_conttype_b1.Enabled = false;
            cb_conttype_d4.Enabled = false;
            cb_conttype_d5.Enabled = false;
            cb_conttype_d7.Enabled = false;
            cb_conttype_rb.Enabled = false;
            cb_conttype_m.Enabled = false;
            cb_conttype_ns.Enabled = false;
            rb_conttype_uo.Enabled = false;
            rb_conttype_ui.Enabled = false;
            //if(ViewState["tr_class"].ToString() =="M")
            //{
            //    btnSaveDraft2.Visible = false;
            //    btnFilesUpload2.Visible = false;
            //}
            if (ViewState["tr_class"].ToString() != "M")
            {
                if (ViewState["tr_class"].ToString() != "F")
                {
                    txtOrgAbbrName.Text = ssoUser.empOrgname;
                    x_dept.Text = ssoUser.empDeptcd;
                    txt_req_dept.Value = ssoUser.empOrgcd + ssoUser.empDeptcd;
                    txt_promoter_name.Text = ssoUser.empName;
                    txt_promoter_empno.Value = ssoUser.empNo;
                    txtTel.Text = ssoUser.empTelext;
                }

            }
            if (ViewState["tr_class"].ToString() == "T")
            {
                BindData_業務窗口();
                ddlContMoneyType.SelectedValue = "TWD";
                TB_money_rate.Text = "1";
            }
        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "ADM_other")
        {
            BindData_業務窗口();
        }
        if (Request.Params.Get("__EVENTTARGET") == "seno_ECP")
        {
            if (h_ECP_success.Value == "Y")
            {
                string script = "<script language='javascript'>alert('申請單送出簽核成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
        //Response.Cache.SetCacheability(HttpCacheability.NoCache);
        //HttpContext.Current.Response.Cache.SetNoServerCaching();
        //HttpContext.Current.Response.Cache.SetNoStore();
    }
    private void BindData_業務窗口()
    {
        //SDS_ADM_業務.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_ADM_業務.SelectParameters.Clear();
        //SDS_ADM_業務.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_ADM_業務.SelectCommand = "select rtrim(com_cname) cname ,com_empno from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =( select com_orgcd from common..comper where com_empno=@業務窗口))";
        //SDS_ADM_業務.SelectParameters.Add("業務窗口", txt_promoter_empno.Value);
        //for (int i = 0; i < SDS_ADM_業務.SelectParameters.Count; i++)
        //{
        //    SDS_ADM_業務.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //System.Data.DataView dv_業務 = (DataView)SDS_ADM_業務.Select(new DataSourceSelectArguments());


        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandType = CommandType.Text;
        //oCmd_1.CommandText = "select rtrim(com_cname) cname ,com_empno from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =( select com_orgcd from common..comper where com_empno=@業務窗口))";
        //oCmd_1.Parameters.AddWithValue("業務窗口", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        // oda_1.Fill(ds_1, "myTable");

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = sqlConn;
            StringBuilder sb = new StringBuilder();
            sb.Append(@"select rtrim(com_cname) cname ,com_empno from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =( select com_orgcd from common..comper where com_empno=@業務窗口))");
            oCmd.Parameters.AddWithValue("業務窗口", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));
            oCmd.CommandText = sb.ToString();
            oCmd.CommandType = CommandType.Text;
            oCmd.CommandTimeout = 0;

            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            oda.Fill(ds_1);
        }
        DataView dv_業務 = ds_1.Tables[0].DefaultView;
        if ((dv_業務.Count >= 1) && (ViewState["tr_class"].ToString() == "T"))
        {
            PL_amd.Visible = true;
            rb_adm_no.Checked = true;
            LB_adm_text.Text = Server.HtmlEncode(dv_業務[0]["cname"].ToString());
            LB_adm_業務_empno.Text = Server.HtmlEncode(dv_業務[0]["com_empno"].ToString());
        }
        else
        {
            PL_amd.Visible = false;
            rb_adm_no.Checked = true;
        }
        //ds_1.Dispose();
        //oCmd_1.Dispose();
        //oda_1.Dispose();
    }

    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //SDS_company.SelectParameters.Add("customers", SQLInjectionReplaceAll(h_compno.Value.ToString()));
        //SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs_2024";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        if (cb_conttype_b0.Checked)
            strCondition += "B0,";
        if (cb_conttype_b1.Checked)
            strCondition += "B1,";
        if (cb_conttype_d4.Checked)
            strCondition += "D4,";
        if (cb_conttype_d5.Checked)
            strCondition += "D5,";
        if (cb_conttype_d7.Checked)
            strCondition += "D7,";
        if (cb_conttype_ns.Checked)
            strCondition += "NS,";
        if (cb_conttype_rb.Checked)
            strCondition += "RB,";
        if (cb_conttype_m.Checked)
            strCondition += "ND,";
        if (rb_conttype_uo.Checked)
            strCondition += "A0,";
        if (rb_conttype_other.Checked)
            strCondition += "OT,";
        if (rb_conttype_bd.Checked)
            strCondition += "BD,";
        if (rb_conttype_ui.Checked)
            strCondition += "F7,FA,F9,F3,FC";

        if (cb_conttype_c.Checked)
            strCondition = "C12";
        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);

        #endregion
        ddlContType.Items.Clear();

        //SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_ContType.SelectCommand = "exec esp_treaty_codetable_query_by_group  '" + SQLInjectionReplaceAll(strCondition) + "' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "10");

            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        ddlContType.Items.Insert(0, " --請選擇-- ");
        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
        }
        #endregion
    }
    private void Bind_oRC_init()
    {
        ViewState["otherrequire_ver"] = 2;
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.SelectParameters.Clear();
        //SDS_oRC.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.SelectCommand = " SELECT  MAX(tcs_ver) FROM treaty_code_OtherRC ";
        //for (int i = 0; i <  SDS_oRC.SelectParameters.Count; i++)
        //{
        //    SDS_oRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_oRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_oRC.Select(new DataSourceSelectArguments());

        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandType = CommandType.Text;
        //oCmd_1.CommandText = "SELECT  MAX(tcs_ver) FROM treaty_code_OtherRC";
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        // oda_1.Fill(ds_1, "myTable");
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = sqlConn;
            StringBuilder sb = new StringBuilder();
            sb.Append(@"SELECT  MAX(tcs_ver) FROM treaty_code_OtherRC");
            oCmd.CommandText = sb.ToString();
            oCmd.CommandType = CommandType.Text;
            oCmd.CommandTimeout = 0;

            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            oda.Fill(ds_1);
        }
        DataView dv = ds_1.Tables[0].DefaultView;
        if (dv.Count >= 1)
        {
            ViewState["otherrequire_ver"] = dv[0][0].ToString();
        }
        //ds_1.Dispose();
        //oCmd_1.Dispose();
        //oda_1.Dispose();
    }


    private void BindContMoneyType()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }

    protected void rb_conttype_bd_CheckedChanged(object sender, EventArgs e)
    {
        BindContType("");
    }
    protected void rb_conttype_other_CheckedChanged(object sender, EventArgs e)
    {
        BindContType("");
    }

    protected void rb_conttype_bd_CheckedChanged1(object sender, EventArgs e)
    {
        BindContType("");
    }
    protected void rb_conttype_other_CheckedChanged1(object sender, EventArgs e)
    {
        BindContType("");
    }

    #region 觸發檔案上傳按鈕
    protected void btnFilesUpload_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }

        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_empno').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_empno').click(function () { $('#txt_promoter_empno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }

        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (txt_name.Text == "")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name.Text').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name.Text", script_alert);
        }

        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須輸入','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "BT_Customer", script_alert);
        }

        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }

        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }

        if ((txt_contedate.Text != "") && (txt_contsdate.Text != ""))
        {
            if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }

        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";

        if (ddlContMoneyType.SelectedValue == "")
            str_error += "★幣別不能挑選空白 \\n ";
        else
        {
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }

        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }

        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_error += "★契約預估金額 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額 只能填寫數字','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
            txtContMoney.Text = "";
        }
        //else
        //{
        //    if (txtContMoney.Text.Trim() == "")
        //        txtContMoney.Text = "";
        //}
        if (TB_急件原因.Text.Trim() != "" && CB_急件.Checked == false)
        {
            str_error += "★請勾選急件原因\\n ";
            string script_alert = "<script language='javascript'> $('#CB_急件').validationEngine('showPrompt', '★請勾選急件原因','','',true); $('#CB_急件').click(function () { $('#CB_急件').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "CB_急件", script_alert);
        }

        if ((txt_confirm_date.Text != ""))
        {
            if (!IsNumber(txt_confirm_date.Text))
                str_danger = "1";
        }

        if (str_danger == "1")
            Response.Redirect("../danger.aspx");

        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            DoSaveDraft("2");
            if (ViewState["seno"] != null)
            {
                Treaty_log(ViewState["seno"].ToString(), "暫存草稿", "", "", "treaty\\TreatyApply_modify.aspx");
                string script = "<script language='javascript'>alert('暫存草稿成功,\\n上傳檔案請按   契約檔案上傳  按鈕！');location.href='./TreatyApply_modify.aspx?seno=" + HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
            else
            {
                string script = "<script language='javascript'>alert('暫存草稿失敗！') ;</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }
    #endregion

    private void DoSaveDraft(string tr_status)
    {
        /*    SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
              SDS_NR.SelectParameters.Clear();
              SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
              SDS_NR.SelectCommand = "esp_TreatyApply_modify";
              ViewState["eb_orgcd"] = txt_req_dept.Value.Substring(0, 2);//取得組織單位
              if ((rb_conttype_bd.Checked) || (rb_conttype_other.Checked))
              {
                  if (rb_conttype_other.Checked)
                      ViewState["tr_class"] = "T";
                  if (rb_conttype_bd.Checked)
                      ViewState["tr_class"] = "S";
                  if (txtComplexNo.Text.Trim() != "")
                  {
                      SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(""));
                      SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
                      SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
                      SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
                      SDS_NR.SelectParameters.Add("tr_sn",    SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
                      SDS_NR.SelectParameters.Add("tr_ver",   SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
                      SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)           
                  }
                  else
                  {
                      SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(""));
                      SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll((DateTime.Now).Year.ToString()));
                      SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txt_req_dept.Value.Substring(0, 2)));
                      SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                      SDS_NR.SelectParameters.Add("tr_sn", SQLInjectionReplaceAll(""));
                      SDS_NR.SelectParameters.Add("tr_ver", SQLInjectionReplaceAll("A"));  //案件編號(版次)   
                      SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll("01"));//案件編號(件次)
                  }
              }
              else
              { 
                  SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(""));
                  SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
                  SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
                  SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
                  SDS_NR.SelectParameters.Add("tr_sn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
                  SDS_NR.SelectParameters.Add("tr_ver", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
                  SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
              }
               SDS_NR.SelectParameters.Add("tr_status", SQLInjectionReplaceAll(tr_status)); //草稿
               SDS_NR.SelectParameters.Add("tr_old_contno", SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
               SDS_NR.SelectParameters.Add("tr_req_dept", SQLInjectionReplaceAll(txt_req_dept.Value.Trim()));
               SDS_NR.SelectParameters.Add("tr_promoter_no", SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
               SDS_NR.SelectParameters.Add("tr_promoter_name", SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
               SDS_NR.SelectParameters.Add("tr_name", SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
               SDS_NR.SelectParameters.Add("tr_compidno", SQLInjectionReplaceAll(""));
               SDS_NR.SelectParameters.Add("tr_compidno_all", SQLInjectionReplaceAll(h_compno.Value.Replace(",", "㊣")));
               SDS_NR.SelectParameters.Add("tr_compname", SQLInjectionReplaceAll(""));
               SDS_NR.SelectParameters.Add("tr_compname_all", SQLInjectionReplaceAll(""));
               if (rb_language_chiness.Checked)
                   SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("1"));//契約語文-中文
               if (rb_language_english.Checked)
                   SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("2"));//契約語文-英文
               #region 案件性質
               SDS_NR.SelectParameters.Add("tr_conttype_b0", cb_conttype_b0.Checked ? "1" : "0");//技術服務
               SDS_NR.SelectParameters.Add("tr_conttype_b1", cb_conttype_b1.Checked ? "1" : "0");//合作開發
               SDS_NR.SelectParameters.Add("tr_conttype_d4", cb_conttype_d4.Checked ? "1" : "0");//技術授權
               SDS_NR.SelectParameters.Add("tr_conttype_d5", cb_conttype_d5.Checked ? "1" : "0");//專利授權
               SDS_NR.SelectParameters.Add("tr_conttype_d7", cb_conttype_d7.Checked ? "1" : "0");//專利讓與
               SDS_NR.SelectParameters.Add("tr_conttype_ns", cb_conttype_ns.Checked ? "1" : "0");//新創事業(洽案)
               SDS_NR.SelectParameters.Add("tr_conttype_rb", cb_conttype_rb.Checked ? "1" : "0");//標案 
               SDS_NR.SelectParameters.Add("tr_conttype_uo", rb_conttype_uo.Checked ? "1" : "0");//國外支出(無收入) 
               SDS_NR.SelectParameters.Add("tr_conttype_ui", rb_conttype_ui.Checked ? "1" : "0");//國外支出(無收入) 
               SDS_NR.SelectParameters.Add("tr_class_other_desc", txt_class_other_desc.Text); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
               #endregion
               #region 契約修訂
               if (spanContractEdit.Visible) {//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
                   SDS_NR.SelectParameters.Add("tr_amend", rbl_amend.SelectedValue);
                   SDS_NR.SelectParameters.Add("tr_amend_other_desc", txtamend_other_desc.Text);
                   lb_Amend_Show.Visible = true;
               }
               else
               {
                   SDS_NR.SelectParameters.Add("tr_amend","0");
                   SDS_NR.SelectParameters.Add("tr_amend_other_desc","");
               }
               #endregion
               SDS_NR.SelectParameters.Add("tr_contsdate", SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/","")));  //契約期間(起)
               SDS_NR.SelectParameters.Add("tr_contedate", SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
               #region 簽約緣由與目的
               #endregion  
               if (ViewState["tr_class"].ToString() == "T")
               {
                   SDS_NR.SelectParameters.Add("tr_promoter_no_other", SQLInjectionReplaceAll(h_px_empno.Value));
                   if (rb_adm_yes.Checked)
                       SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll("1"));
                   if (rb_adm_no.Checked)
                       SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll(""));
               }
               else
               {
                   SDS_NR.SelectParameters.Add("tr_promoter_no_other", SQLInjectionReplaceAll(""));
                   SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll(""));
               }
               Bind_oRC_init();// get other max Ver
               SDS_NR.SelectParameters.Add("tr_otherrequire_ver", SQLInjectionReplaceAll(ViewState["otherrequire_ver"].ToString())); //其他需求
               SDS_NR.SelectParameters.Add("tr_keyin_emp_no", SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
               SDS_NR.SelectParameters.Add("tr_keyin_emp_name", SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
               SDS_NR.SelectParameters.Add("tr_keyin_date", SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
               SDS_NR.SelectParameters.Add("tr_modify_emp_no", SQLInjectionReplaceAll(lb_modify_emp_no.Text.Trim()));  // 修改工號
               SDS_NR.SelectParameters.Add("tr_modify_emp_name", SQLInjectionReplaceAll(lb_modify_emp_name.Text.Trim()));// 修改人
               SDS_NR.SelectParameters.Add("tr_modify_date", SQLInjectionReplaceAll(lb_modify_date.Text.Trim().Replace("/", "")));      // 修改日期
               SDS_NR.SelectParameters.Add("tr_file_flag", SQLInjectionReplaceAll("0"));//附件狀態
               SDS_NR.SelectParameters.Add("tr_conttype", SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
               SDS_NR.SelectParameters.Add("tr_money_type", SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
               //if (txtContMoney.Text.Trim() == "")
               //    SDS_NR.SelectParameters.Add("tr_money", "0");
               //else
               SDS_NR.SelectParameters.Add("tr_money", SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
               SDS_NR.SelectParameters.Add("tr_money_rate", SQLInjectionReplaceAll(匯率));
               SDS_NR.SelectParameters.Add("NewType", SQLInjectionReplaceAll("1")); // 草稿
               SDS_NR.SelectParameters.Add("tr_case_style", SQLInjectionReplaceAll("1")); // 案件類型
               SDS_NR.SelectParameters.Add("急件", SQLInjectionReplaceAll(CB_急件.Checked ==true ? "1" :"" ));  
               SDS_NR.SelectParameters.Add("急件原因", SQLInjectionReplaceAll(TB_急件原因.Text.Trim()));
               for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
               {
                   SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
               }
               SDS_NR.DataBind();
               System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
       */
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand oCmd_1 = new SqlCommand();
            oCmd_1.Connection = sqlConn;
            oCmd_1.CommandType = CommandType.StoredProcedure;
            oCmd_1.CommandText = "esp_TreatyApply_modify";
            ViewState["eb_orgcd"] = txt_req_dept.Value.Substring(0, 2);//取得組織單位
            if ((rb_conttype_bd.Checked) || (rb_conttype_other.Checked))
            {
                if (rb_conttype_other.Checked)
                    ViewState["tr_class"] = "T";
                if (rb_conttype_bd.Checked)
                    ViewState["tr_class"] = "S";
                if (txtComplexNo.Text.Trim() != "")
                {
                    oCmd_1.Parameters.AddWithValue("tr_seno", "");
                    oCmd_1.Parameters.AddWithValue("tr_year", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
                    oCmd_1.Parameters.AddWithValue("tr_orgcd", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
                    oCmd_1.Parameters.AddWithValue("tr_class", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
                    oCmd_1.Parameters.AddWithValue("tr_sn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
                    oCmd_1.Parameters.AddWithValue("tr_ver", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
                    oCmd_1.Parameters.AddWithValue("tr_seqsn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)           
                }
                else
                {
                    oCmd_1.Parameters.AddWithValue("tr_seno", "");
                    oCmd_1.Parameters.AddWithValue("tr_year", oRCM.SQLInjectionReplaceAll((DateTime.Now).Year.ToString()));
                    oCmd_1.Parameters.AddWithValue("tr_orgcd", oRCM.SQLInjectionReplaceAll(txt_req_dept.Value.Substring(0, 2)));
                    oCmd_1.Parameters.AddWithValue("tr_class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
                    oCmd_1.Parameters.AddWithValue("tr_sn", "");
                    oCmd_1.Parameters.AddWithValue("tr_ver", "A");  //案件編號(版次)   
                    oCmd_1.Parameters.AddWithValue("tr_seqsn", "01");//案件編號(件次)
                }
            }
            else
            {
                oCmd_1.Parameters.AddWithValue("tr_seno", "");
                oCmd_1.Parameters.AddWithValue("tr_year", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
                oCmd_1.Parameters.AddWithValue("tr_orgcd", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
                oCmd_1.Parameters.AddWithValue("tr_class", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
                oCmd_1.Parameters.AddWithValue("tr_sn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
                oCmd_1.Parameters.AddWithValue("tr_ver", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
                oCmd_1.Parameters.AddWithValue("tr_seqsn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
            }
            oCmd_1.Parameters.AddWithValue("tr_status", oRCM.SQLInjectionReplaceAll(tr_status)); //草稿
            oCmd_1.Parameters.AddWithValue("tr_old_contno", oRCM.SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
            oCmd_1.Parameters.AddWithValue("tr_req_dept", oRCM.SQLInjectionReplaceAll(txt_req_dept.Value.Trim()));
            oCmd_1.Parameters.AddWithValue("tr_promoter_no", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
            oCmd_1.Parameters.AddWithValue("tr_promoter_name", oRCM.SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
            oCmd_1.Parameters.AddWithValue("tr_name", oRCM.SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
            oCmd_1.Parameters.AddWithValue("tr_compidno", "");
            oCmd_1.Parameters.AddWithValue("tr_compidno_all", oRCM.SQLInjectionReplaceAll(h_compno.Value.Replace(",", "㊣")));
            oCmd_1.Parameters.AddWithValue("tr_compname", "");
            oCmd_1.Parameters.AddWithValue("tr_compname_all", "");
            if (rb_language_chiness.Checked)
                oCmd_1.Parameters.AddWithValue("tr_language", "1");//契約語文-中文
            if (rb_language_english.Checked)
                oCmd_1.Parameters.AddWithValue("tr_language", "2");//契約語文-英文
            #region 案件性質
            oCmd_1.Parameters.AddWithValue("tr_conttype_b0", cb_conttype_b0.Checked ? "1" : "0");//技術服務
            oCmd_1.Parameters.AddWithValue("tr_conttype_b1", cb_conttype_b1.Checked ? "1" : "0");//合作開發
            oCmd_1.Parameters.AddWithValue("tr_conttype_d4", cb_conttype_d4.Checked ? "1" : "0");//技術授權
            oCmd_1.Parameters.AddWithValue("tr_conttype_d5", cb_conttype_d5.Checked ? "1" : "0");//專利授權
            oCmd_1.Parameters.AddWithValue("tr_conttype_d7", cb_conttype_d7.Checked ? "1" : "0");//專利讓與
            oCmd_1.Parameters.AddWithValue("tr_conttype_ns", cb_conttype_ns.Checked ? "1" : "0");//新創事業(洽案)
            oCmd_1.Parameters.AddWithValue("tr_conttype_rb", cb_conttype_rb.Checked ? "1" : "0");//標案 
            oCmd_1.Parameters.AddWithValue("tr_conttype_uo", rb_conttype_uo.Checked ? "1" : "0");//國外支出(無收入) 
            oCmd_1.Parameters.AddWithValue("tr_conttype_ui", rb_conttype_ui.Checked ? "1" : "0");//國外支出(無收入) 
            oCmd_1.Parameters.AddWithValue("tr_class_other_desc", txt_class_other_desc.Text); //txt_class_other_desc.Text.Trim()//案件類別-其他  描述
            #endregion
            #region 契約修訂
            if (spanContractEdit.Visible)
            {//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
                oCmd_1.Parameters.AddWithValue("tr_amend", rbl_amend.SelectedValue);
                oCmd_1.Parameters.AddWithValue("tr_amend_other_desc", txtamend_other_desc.Text);
                lb_Amend_Show.Visible = true;
            }
            else
            {
                oCmd_1.Parameters.AddWithValue("tr_amend", "0");
                oCmd_1.Parameters.AddWithValue("tr_amend_other_desc", "");
            }
            #endregion
            oCmd_1.Parameters.AddWithValue("tr_contsdate", oRCM.SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
            oCmd_1.Parameters.AddWithValue("tr_contedate", oRCM.SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
            #region 簽約緣由與目的
            #endregion
            if (ViewState["tr_class"].ToString() == "T")
            {
                oCmd_1.Parameters.AddWithValue("tr_promoter_no_other", oRCM.SQLInjectionReplaceAll(h_px_empno.Value));
                if (rb_adm_yes.Checked)
                    oCmd_1.Parameters.AddWithValue("tr_org_adm", "1");
                if (rb_adm_no.Checked)
                    oCmd_1.Parameters.AddWithValue("tr_org_adm", "");
            }
            else
            {
                oCmd_1.Parameters.AddWithValue("tr_promoter_no_other", "");
                oCmd_1.Parameters.AddWithValue("tr_org_adm", "");
            }
            Bind_oRC_init();// get other max Ver
            oCmd_1.Parameters.AddWithValue("tr_otherrequire_ver", oRCM.SQLInjectionReplaceAll(ViewState["otherrequire_ver"].ToString())); //其他需求
            oCmd_1.Parameters.AddWithValue("tr_keyin_emp_no", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
            oCmd_1.Parameters.AddWithValue("tr_keyin_emp_name", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
            oCmd_1.Parameters.AddWithValue("tr_keyin_date", oRCM.SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
            oCmd_1.Parameters.AddWithValue("tr_modify_emp_no", oRCM.SQLInjectionReplaceAll(lb_modify_emp_no.Text.Trim()));  // 修改工號
            oCmd_1.Parameters.AddWithValue("tr_modify_emp_name", oRCM.SQLInjectionReplaceAll(lb_modify_emp_name.Text.Trim()));// 修改人
            oCmd_1.Parameters.AddWithValue("tr_modify_date", oRCM.SQLInjectionReplaceAll(lb_modify_date.Text.Trim().Replace("/", "")));      // 修改日期
            oCmd_1.Parameters.AddWithValue("tr_file_flag", "0");//附件狀態
            oCmd_1.Parameters.AddWithValue("tr_conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
            oCmd_1.Parameters.AddWithValue("tr_money_type", oRCM.SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
                                                                                                                                           //if (txtContMoney.Text.Trim() == "")
                                                                                                                                           //    oCmd_1.Parameters.AddWithValue("tr_money", "0");
                                                                                                                                           //else
            oCmd_1.Parameters.AddWithValue("tr_money", oRCM.SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
            oCmd_1.Parameters.AddWithValue("tr_money_rate", oRCM.SQLInjectionReplaceAll(匯率));
            oCmd_1.Parameters.AddWithValue("NewType", "1"); // 草稿
            oCmd_1.Parameters.AddWithValue("tr_case_style", "1"); // 案件類型
            oCmd_1.Parameters.AddWithValue("急件", oRCM.SQLInjectionReplaceAll(CB_急件.Checked == true ? "1" : ""));
            oCmd_1.Parameters.AddWithValue("急件原因", oRCM.SQLInjectionReplaceAll(TB_急件原因.Text.Trim()));
            SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            DataSet ds_1 = new DataSet();
            oda_1.Fill(ds_1, "myTable");
            DataView dv_actno = ds_1.Tables[0].DefaultView;
            if (dv_actno.Count >= 1)
            {
                ViewState["seno"] = dv_actno[0][0].ToString();
            }
        }
        SqlConnection oConn = new SqlConnection();
        oConn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        oConn.Open();

        if (Request["seno"] != null)
        {

            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.Text;
            //SDS_NR.UpdateCommand = " update treaty_requisition set tr_oldcase_seno=@tr_oldcase_seno where tr_seno=@tr_seno ";
            //SDS_NR.UpdateParameters.Add("tr_oldcase_seno", SQLInjectionReplaceAll(Request["seno"].ToString()));
            //SDS_NR.UpdateParameters.Add("tr_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_NR.Update();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmd_NR = new SqlCommand();
                oCmd_NR.Connection = sqlConn;
                oCmd_NR.CommandType = CommandType.Text;
                oCmd_NR.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";
                oCmd_NR.CommandTimeout = 0;
                oCmd_NR.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmd_NR.CommandType = CommandType.Text;
                    oCmd_NR.CommandText = " update treaty_requisition set tr_oldcase_seno=@tr_oldcase_seno where tr_seno=@tr_seno";
                    oCmd_NR.CommandType = CommandType.Text;
                    oCmd_NR.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmd_NR.Parameters.AddWithValue("tr_oldcase_seno", oRCM.SQLInjectionReplaceAll(Request["seno"].ToString()));
                    oCmd_NR.Parameters.AddWithValue("tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmd_NR.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }
        #region 其它需求
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.DeleteParameters.Clear();
        //SDS_oRC.DeleteCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.DeleteCommand = " delete treaty_requisition_oRC where tr_seno =@seno";
        //SDS_oRC.DeleteParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_oRC.Delete();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand oCmd_NR = new SqlCommand();
            oCmd_NR.Connection = sqlConn;
            oCmd_NR.CommandTimeout = 0;
            oCmd_NR.Parameters.Clear();
            try
            {
                sqlConn.Open();
                oCmd_NR.CommandType = CommandType.Text;
                oCmd_NR.CommandText = " delete treaty_requisition_oRC where tr_seno =@seno";
                oCmd_NR.CommandType = CommandType.Text;
                oCmd_NR.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                oCmd_NR.ExecuteNonQuery();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        //ds_1.Dispose();
        //oCmd_1.Dispose();
        //oda_1.Dispose();
        oConn.Close();
        ViewState["tr_oRC_ver"] = "2";

        /*
                if (rb_other_1.Checked == true)
                {
                   SDS_oRC.InsertParameters.Clear();
                   SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                   SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                   SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                   SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                   SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("1"));
                   SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_contno.Text));
                   SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(TB_otherrequire_handle_name.Text));
                   SDS_oRC.Insert();
                }
                if (rb_other_2.Checked == true)
                {
                    SDS_oRC.InsertParameters.Clear();
                    SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                    SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                    SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("2"));
                    SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_asked_name.Text));
                    SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
                    SDS_oRC.Insert();
                }
                if (rb_other_3.Checked == true)
                {
                    SDS_oRC.InsertParameters.Clear();
                    SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                    SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                    SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("3"));
                    SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(""));
                    SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
                    SDS_oRC.Insert();
                }
                if (rb_other_4.Checked == true)
                {
                   　SDS_oRC.InsertParameters.Clear();
                   　SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                   　SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                   　SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                   　SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                   　SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("4"));
                   　SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(""));
                   　SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
                   　SDS_oRC.Insert();
                }
                if (rb_other_T.Checked == true)
                {
                  　SDS_oRC.InsertParameters.Clear();
                  　SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
                  　SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
                  　SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                  　SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                  　SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("T"));
                  　SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_desc.Text));
                  　SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
                  　SDS_oRC.Insert();
                }
        */

        if (rb_other_1.Checked == true)
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmdx = new SqlCommand();
                oCmdx.Connection = sqlConn;
                oCmdx.CommandTimeout = 0;
                oCmdx.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.CommandText = "insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)";
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmdx.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    oCmdx.Parameters.AddWithValue("val", "1");
                    oCmdx.Parameters.AddWithValue("desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_contno.Text));
                    oCmdx.Parameters.AddWithValue("desc2", oRCM.SQLInjectionReplaceAll(TB_otherrequire_handle_name.Text));
                    oCmdx.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }
        if (rb_other_2.Checked == true)
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmdx = new SqlCommand();
                oCmdx.Connection = sqlConn;
                oCmdx.CommandTimeout = 0;
                oCmdx.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.CommandText = "insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)";
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmdx.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    oCmdx.Parameters.AddWithValue("val", "2");
                    oCmdx.Parameters.AddWithValue("desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_asked_name.Text));
                    oCmdx.Parameters.AddWithValue("desc2", "");
                    oCmdx.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }
        if (rb_other_3.Checked == true)
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmdx = new SqlCommand();
                oCmdx.Connection = sqlConn;
                oCmdx.CommandTimeout = 0;
                oCmdx.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.CommandText = "insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)";
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmdx.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    oCmdx.Parameters.AddWithValue("val", "3");
                    oCmdx.Parameters.AddWithValue("desc1", "");
                    oCmdx.Parameters.AddWithValue("desc2", "");
                    oCmdx.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }
        if (rb_other_4.Checked == true)
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmdx = new SqlCommand();
                oCmdx.Connection = sqlConn;
                oCmdx.CommandTimeout = 0;
                oCmdx.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.CommandText = "insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)";
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmdx.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    oCmdx.Parameters.AddWithValue("val", "4");
                    oCmdx.Parameters.AddWithValue("desc1", "");
                    oCmdx.Parameters.AddWithValue("desc2", "");
                    oCmdx.ExecuteNonQuery();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }
        if (rb_other_T.Checked == true)
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmdx = new SqlCommand();
                oCmdx.Connection = sqlConn;
                oCmdx.CommandTimeout = 0;
                oCmdx.Parameters.Clear();
                try
                {
                    sqlConn.Open();
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.CommandText = "insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2)";
                    oCmdx.CommandType = CommandType.Text;
                    oCmdx.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmdx.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
                    oCmdx.Parameters.AddWithValue("val", "T");
                    oCmdx.Parameters.AddWithValue("desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_desc.Text));
                    oCmdx.Parameters.AddWithValue("desc2", "");
                    oCmdx.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
        }

        #endregion

        #region 報院條件
        /*
        this.SDS_sRC.DeleteParameters.Clear();
        this.SDS_sRC.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
        this.SDS_sRC.DeleteCommand = "esp_TreatyApply_sRc_modify";
        this.SDS_sRC.DeleteParameters.Add("seno", ViewState["seno"].ToString());
        this.SDS_sRC.DeleteParameters.Add("class", ViewState["tr_class"].ToString());
        this.SDS_sRC.DeleteParameters.Add("ver", "");
        this.SDS_sRC.DeleteParameters.Add("svalue", "");
        this.SDS_sRC.DeleteParameters.Add("sdoc", "");
        this.SDS_sRC.DeleteParameters.Add("stype", "Delete");
        this.SDS_sRC.Delete();
        string s_value = "";
        ArrayList my報院條件 = new ArrayList();
        ArrayList my報院條件說明 = new ArrayList();
        my報院條件 = (ArrayList)ViewState["my報院條件"];
        my報院條件說明 = (ArrayList)ViewState["my報院條件說明"];
        foreach (string str_obj in my報院條件)
        {
            if (str_obj.IndexOf("CBL_") >= 0)
            {
                CheckBox cb = Plh_Dynax_sRC.FindControl(str_obj) as CheckBox;
                if (cb.Checked == true)
                {
                    s_value = cb.Attributes["Value"];
                }
            }
            if (s_value != "") 
            {
                this.SDS_sRC.InsertParameters.Clear();
                this.SDS_sRC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
                this.SDS_sRC.InsertCommand = "esp_TreatyApply_sRc_modify";
                this.SDS_sRC.InsertParameters.Add("seno", ViewState["seno"].ToString());
                this.SDS_sRC.InsertParameters.Add("class", ViewState["tr_class"].ToString());
                this.SDS_sRC.InsertParameters.Add("ver", "0");
                this.SDS_sRC.InsertParameters.Add("svalue", s_value);
                this.SDS_sRC.InsertParameters.Add("sdoc", "");
                this.SDS_sRC.InsertParameters.Add("stype", "Insert");
                this.SDS_sRC.Insert();
                s_value = "";
            }
        }
        foreach (string str_obj in my報院條件說明)
        {
            if (str_obj.IndexOf("TB_") >= 0)
            {
                TextBox tb = Plh_Dynax_sRC.FindControl(str_obj) as TextBox;
                if (tb != null)
                {
                    if (tb.Text.ToUpper().IndexOf("SCRIPT") >= 0) Response.Redirect("../danger.aspx"); 
                    s_value = tb.ID.Replace("TB_", "");
                    this.SDS_sRC.UpdateParameters.Clear();
                    this.SDS_sRC.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                    this.SDS_sRC.UpdateCommand = "esp_TreatyApply_sRc_modify";
                    this.SDS_sRC.UpdateParameters.Add("seno", ViewState["seno"].ToString());
                    this.SDS_sRC.UpdateParameters.Add("class", ViewState["tr_class"].ToString());
                    this.SDS_sRC.UpdateParameters.Add("ver", "0");
                    this.SDS_sRC.UpdateParameters.Add("svalue", s_value);
                    this.SDS_sRC.UpdateParameters.Add("sdoc", tb.Text.Trim());
                    this.SDS_sRC.UpdateParameters.Add("stype", "Modify");
                    this.SDS_sRC.Update();                    
                }                 
            }
        }
*/
        #endregion

    }
    protected void btnSaveDraft_Click(object sender, EventArgs e)
    {

        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_empno').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須輸入','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "BT_Customer", script_alert);
        }
        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (((txt_contsdate.Text != "")) && ((txt_contedate.Text != "")))
        {
            if ((CheckDateTimeType(txt_contsdate.Text)) && (CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }
        if (txtComplexNo.Text.Trim() != "")
        {
            if ((!IsNatural_Number(txtComplexNo.Text.Substring(0, 4))))
                str_danger = "1";
        }
        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if (ddlContMoneyType.SelectedValue == "")
            str_error += "★幣別不能挑選空白 \\n ";
        else
        {
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (ddlContMoneyType.SelectedValue == "TWD")
            TB_money_rate.Text = "1";
        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        else
        {
            if (ddlContMoneyType.SelectedValue == "TWD")
            {
                if (!(匯率 == "1" || 匯率 == "1.00000"))
                {
                    str_error += "★幣別是新台幣,匯率只能是1\\n ";
                    string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別是新台幣,匯率只能是1','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
                }
            }
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }
        if (txt_req_dept.Value.Length > 7)
            str_danger = "1";
        if (txt_req_dept.Value.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_contno.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (TB_otherrequire_handle_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_asked_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if ((txtContMoney.Text.Trim().Length > 13) || (!IsNumber(txtContMoney.Text)))
        {
            str_error += "★契約預估金額 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額 只能填寫數字','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
            txtContMoney.Text = "";
        }
        //else
        //{
        //    if (txtContMoney.Text.Trim() == "")
        //        txtContMoney.Text = "0";
        //}

        if (TB_急件原因.Text.Trim() != "" && CB_急件.Checked == false)
        {
            str_error += "★請勾選急件原因\\n ";
            string script_alert = "<script language='javascript'> $('#CB_急件').validationEngine('showPrompt', '★請勾選急件原因','','',true); $('#CB_急件').click(function () { $('#CB_急件').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "CB_急件", script_alert);
        }

        if ((txt_confirm_date.Text != ""))
        {
            if (!IsNumber(txt_confirm_date.Text))
                str_danger = "1";
        }

        if (str_danger == "1")
            Response.Redirect("../danger.aspx");


        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            DoSaveDraft("2");
            if (ViewState["seno"] != null)
            {
                Treaty_log(ViewState["seno"].ToString(), "暫存草稿", "", "", "treaty\\TreatyApply_modify.aspx");
                string script = "<script language='javascript'>alert('暫存草稿成功！');location.href='./TreatyApply_View.aspx?seno=" + HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
            else
            {
                string script = "<script language='javascript'>alert('暫存草稿失敗！') ;</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }
    protected void btnSendApply_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }

        if ((ddlContType.SelectedValue == "") || (ddlContType.SelectedValue == " --請選擇-- "))
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }

        if (((txt_contsdate.Text != "")) && ((txt_contedate.Text != "")))
        {
            if ((!CheckDateTimeType(txt_contsdate.Text)) && (!CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }
        if (txtComplexNo.Text.Trim() != "")
        {
            if ((!IsNatural_Number(txtComplexNo.Text.Substring(0, 4))))
                str_danger = "1";
        }
        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((!IsNatural_Number(h_compno.Value.Replace(",", "").Trim())))
        {
            str_error += "★必須挑選客戶\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★必須挑選客戶','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }

        if (ddlContMoneyType.SelectedValue == "")
            str_error += "★幣別不能挑選空白 \\n ";
        else
        {
            if (ddlContMoneyType.SelectedValue == "TWD")
            {
                if (!(匯率 == "1" || 匯率 == "1.00000"))
                {
                    str_error += "★幣別是新台幣,匯率只能是1\\n ";
                    string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別是新台幣,匯率只能是1','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
                }
            }
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (txt_req_dept.Value.Length > 7) str_danger = "1";
        if (txt_req_dept.Value.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_contno.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (TB_otherrequire_handle_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_asked_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_error += "★契約預估金額 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額 只能填寫數字','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
            txtContMoney.Text = "";
        }
        //else
        //{
        //    if (txtContMoney.Text.Trim() == "")
        //        txtContMoney.Text = "0";
        //}

        if (ddlContMoneyType.SelectedValue == "TWD")
            TB_money_rate.Text = "1";

        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }
        if (TB_急件原因.Text.Trim() != "" && CB_急件.Checked == false)
        {
            str_error += "★請勾選急件原因\\n ";
            string script_alert = "<script language='javascript'> $('#CB_急件').validationEngine('showPrompt', '★請勾選急件原因','','',true); $('#CB_急件').click(function () { $('#CB_急件').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "CB_急件", script_alert);
        }
        if ((txt_confirm_date.Text != ""))
        {
            if (!IsNumber(txt_confirm_date.Text))
                str_danger = "1";
        }
        //if (str_danger == "1")
        //    Response.Redirect("../danger.aspx");

        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            if ((txtComplexNo.Text == "") || ((txtComplexNo.Text.Length > 0) && (txtComplexNo.Text.Substring(11, 4) == "A-01")))
            {
                DoSaveDraft("2");
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyApply_ecp_gen_preflow_special";
                //SDS_NR.SelectParameters.Add("tr_seno", ViewState["seno"].ToString());
                //SDS_NR.SelectParameters.Add("empno", ViewState["empno"].ToString());
                //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                DataSet ds_2 = new DataSet();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd_2 = new SqlCommand();
                    oCmd_2.Connection = sqlConn;
                    oCmd_2.CommandText = "esp_TreatyApply_ecp_gen_preflow_special";
                    oCmd_2.CommandType = CommandType.StoredProcedure;
                    oCmd_2.Parameters.AddWithValue("tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    oCmd_2.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                    SqlDataAdapter oda_2 = new SqlDataAdapter(oCmd_2);
                    oda_2.Fill(ds_2, "myTable");
                }
                DataView dv = ds_2.Tables[0].DefaultView;
                if ((dv.Count >= 1))
                {
                    if (dv[0][0].ToString() != "Pass")
                    {
                        //呼叫WS 
                        //SDS_NR.UpdateParameters.Clear();
                        //string pGUID = Guid.NewGuid().ToString(); //表單GUID，由系統取得
                        //SDS_NR.UpdateCommandType  = SqlDataSourceCommandType.StoredProcedure;
                        //SDS_NR.UpdateCommand = "esp_TreatyApply_ecp_gen_preflow";
                        //SDS_NR.UpdateParameters.Add("ecp_guid", pGUID);
                        //SDS_NR.UpdateParameters.Add("formtype", "TREATY01");
                        //SDS_NR.UpdateParameters.Add("tr_seno", ViewState["seno"].ToString());
                        //SDS_NR.UpdateParameters.Add("rtn_flag", "");
                        //SDS_NR.Update();


                        //SqlConnection oConn = new SqlConnection();
                        //oConn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                        //oConn.Open();
                        //SqlCommand oCmd_3 = oConn.CreateCommand();
                        //oCmd_3.CommandText = "esp_TreatyApply_ecp_gen_preflow";
                        //oCmd_3.CommandType = CommandType.StoredProcedure;
                        //oCmd_3.Parameters.AddWithValue("ecp_guid", oRCM.SQLInjectionReplaceAll(pGUID));
                        //oCmd_3.Parameters.AddWithValue("formtype", "TREATY01");
                        //oCmd_3.Parameters.AddWithValue("tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        //oCmd_3.Parameters.AddWithValue("rtn_flag", "");
                        //oCmd_3.ExecuteNonQuery();
                        //oCmd_3.Dispose();
                        //using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        //{
                        //    sqlConn.Open();
                        //    SqlCommand oCmd_3 = new SqlCommand();
                        //    oCmd_3.Connection = sqlConn;
                        //    StringBuilder sb = new StringBuilder();
                        //    oCmd_3.CommandText = "esp_TreatyApply_ecp_gen_preflow";
                        //    oCmd_3.CommandType = CommandType.StoredProcedure;
                        //    oCmd_3.Parameters.AddWithValue("ecp_guid", oRCM.SQLInjectionReplaceAll(pGUID));
                        //    oCmd_3.Parameters.AddWithValue("formtype", "TREATY01");
                        //    oCmd_3.Parameters.AddWithValue("tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        //    oCmd_3.Parameters.AddWithValue("rtn_flag", "");
                        //    oCmd_3.CommandTimeout = 0;
                        //    oCmd_3.ExecuteNonQuery();
                        //}
                        try
                        {
                            string encodedSeno = HttpUtility.HtmlEncode(ViewState["seno"].ToString());                          
                            string script = string.Format(@"<script language='javascript'>
                                                            var senoValue = '{0}';
                                                            seno_ECP(senoValue);
                                                            </script>", encodedSeno);                      
                            Page.ClientScript.RegisterStartupScript(this.GetType(), "seno_ECP", script, false);

                            //TREATY.TREATY01 prj = new TREATY.TREATY01(); //第一個「TREATY01」是Web參考名稱，加入Web參考時可自訂
                            //string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.sysid"];
                            //string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.secureid"];
                            //prj.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.TREATY01"];   //設定webservice url

                            ////呼叫web service創單
                            //String strSignMsg = prj.TREATYCreate(pGUID, sysid, secureid);  //後二參數依序為「核覆單」中ws_sysid, ws_secureid
                            //XmlDocument xmlDoc = new XmlDocument();
                            //xmlDoc.LoadXml(strSignMsg);
                            //strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText; //取得回傳xml字串中的result
                            ////string strSignMsg1 = xmlDoc.SelectSingleNode("//Message").InnerText;
                            ////Response.Write(strSignMsg);

                            //if ("Y".Equals(strSignMsg))
                            //{
                            //    string SheetNo = xmlDoc.SelectSingleNode("//SheetNo").InnerText; //表單單號
                            //    //Response.Write(SheetNo);
                            //    //string script = "<script language='javascript'>window.alert('創單成功!');</script>";
                            //    //ClientScript.RegisterStartupScript(this.GetType(), "n3", script);
                            //    //創單成功後，若系統須做其他工作，程式碼可寫在此
                            //    //SDS_NR.UpdateParameters.Clear();
                            //    //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.Text;
                            //    //SDS_NR.UpdateCommand = "update treaty_ecp_main set ecp_main002=@ecp_main002 where ecp_guid=@ecp_guid ";
                            //    //SDS_NR.UpdateParameters.Add("ecp_guid", SQLInjectionReplaceAll(pGUID));
                            //    //SDS_NR.UpdateParameters.Add("ecp_main002", SQLInjectionReplaceAll(SheetNo));
                            //    //SDS_NR.Update();

                            //    //oCmd_3.CommandText = "update treaty_ecp_main set ecp_main002=@ecp_main002 where ecp_guid=@ecp_guid";
                            //    //oCmd_3.CommandType = CommandType.Text;
                            //    //oCmd_3.Parameters.AddWithValue("ecp_guid", oRCM.SQLInjectionReplaceAll(pGUID));
                            //    //oCmd_3.Parameters.AddWithValue("ecp_main002", oRCM.SQLInjectionReplaceAll(SheetNo));
                            //    //oCmd_3.ExecuteNonQuery();
                            //    //oCmd_3.Dispose();
                            //    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                            //    {
                            //        sqlConn.Open();
                            //        SqlCommand oCmd_3 = new SqlCommand();
                            //        oCmd_3.Connection = sqlConn;
                            //        StringBuilder sb = new StringBuilder();
                            //        sb.Append(@"update treaty_ecp_main set ecp_main002=@ecp_main002 where ecp_guid=@ecp_guid ");
                            //        oCmd_3.Parameters.AddWithValue("ecp_guid", oRCM.SQLInjectionReplaceAll(pGUID));
                            //        oCmd_3.Parameters.AddWithValue("ecp_main002", oRCM.SQLInjectionReplaceAll(SheetNo));
                            //        oCmd_3.CommandText = sb.ToString();
                            //        oCmd_3.CommandType = CommandType.Text;
                            //        oCmd_3.CommandTimeout = 0;
                            //        oCmd_3.ExecuteNonQuery();
                            //    }

                            //    string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyApply_View.aspx?seno=" + HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                            //    ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                            //}
                            //else
                            //{
                            //    string script = "<script language='javascript'>window.alert('創單失敗!');</script>";
                            //    ClientScript.RegisterStartupScript(this.GetType(), "n3", script);
                            //}
                        }
                        catch (Exception ex)
                        {
                            //SDS_NR.UpdateParameters.Clear();
                            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.Text;
                            //SDS_NR.UpdateCommand = "update treaty_requisition set tr_ecp_Treaty01_guid='' ,tr_status='2' where  tr_seno=@seno  ";
                            //SDS_NR.UpdateParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            //SDS_NR.Update();

                            //SqlCommand oCmd_M = new SqlCommand();
                            //oCmd_M.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                            //oCmd_M.CommandText = "update treaty_requisition set tr_ecp_Treaty01_guid='' ,tr_status='2' where  tr_seno=@seno ";
                            //oCmd_M.CommandType = CommandType.Text;
                            //oCmd_M.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            //oCmd_M.ExecuteNonQuery();

                            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                            {
                                sqlConn.Open();
                                SqlCommand oCmd = new SqlCommand();
                                oCmd.Connection = sqlConn;
                                StringBuilder sb = new StringBuilder();
                                sb.Append(@"update treaty_requisition set tr_ecp_Treaty01_guid='' ,tr_status='2' where  tr_seno=@seno ");
                                oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                                oCmd.CommandText = sb.ToString();
                                oCmd.CommandType = CommandType.Text;
                                oCmd.CommandTimeout = 0;
                                oCmd.ExecuteNonQuery();
                            }

                            //SDS_NR.SelectParameters.Clear();
                            //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                            //SDS_NR.SelectCommand = "esp_TreatyApply_ecpFail_Mail";
                            //SDS_NR.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            //SDS_NR.DataBind();
                            //System.Data.DataView dvf = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                            {

                                sqlConn.Open();
                                SqlCommand oCmd = new SqlCommand();
                                oCmd.Connection = sqlConn;
                                StringBuilder sb = new StringBuilder();
                                sb.Append(@"esp_TreatyApply_ecpFail_Mail");
                                oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                                oCmd.Parameters.AddWithValue("message", oRCM.SQLInjectionReplaceAll(ex.ToString()));
                                oCmd.CommandText = sb.ToString();
                                oCmd.CommandType = CommandType.StoredProcedure;
                                oCmd.CommandTimeout = 0;
                                oCmd.ExecuteNonQuery();

                            }
                            //oCmd_M.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                            //oCmd_M.CommandText = "esp_TreatyApply_ecpFail_Mail";
                            //oCmd_M.CommandType = CommandType.StoredProcedure;
                            //oCmd_M.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            //oCmd_M.ExecuteNonQuery();
                            //oCmd_M.Dispose();

                            string script = "<script language='javascript'>window.alert('送出簽核失敗,請重新發送!');</script>";
                            ClientScript.RegisterStartupScript(this.GetType(), "n3", script);
                        }
                        //oCmd_3.Dispose();
                        //oConn.Close();
                    }
                    else  //20201116 組長以上不簽核特殊流程
                    {
                        DoSaveDraft("3");
                        //SDS_NR.SelectParameters.Clear();
                        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                        //SDS_NR.SelectCommand = "esp_TreatyApplyToTreatyCase";//送出承辦
                        //SDS_NR.SelectParameters.Add("requisition_seno", ViewState["seno"].ToString());
                        //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                        //{
                        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                        //}
                        //SDS_NR.DataBind();
                        //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                        DataSet ds_3 = new DataSet();
                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            sqlConn.Open();
                            SqlCommand oCmd_3 = new SqlCommand();
                            oCmd_3.Connection = sqlConn;
                            oCmd_3.CommandText = "esp_TreatyApplyToTreatyCase";
                            oCmd_3.CommandType = CommandType.StoredProcedure;
                            oCmd_3.Parameters.AddWithValue("requisition_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            SqlDataAdapter oda_3 = new SqlDataAdapter(oCmd_3);
                            oda_3.Fill(ds_3, "myTable");
                        }
                        DataView dv_actno = ds_3.Tables[0].DefaultView;
                        if (dv_actno.Count >= 1)
                        {
                        }
                        //ds_3.Dispose();
                        //oCmd_3.Dispose();
                        //oda_3.Dispose();
                        Response.Cache.SetCacheability(HttpCacheability.NoCache);
                        string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyApply_View.aspx?seno=" + HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                    }
                }
                //ds_2.Dispose();
                //oCmd_2.Dispose();
                //oda_2.Dispose();
            }
            else
            {
                DoSaveDraft("3");
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyApplyToTreatyCase";//送出承辦
                //SDS_NR.SelectParameters.Add("requisition_seno", ViewState["seno"].ToString());
                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                //{
                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_NR.DataBind();
                //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                //if (dv_actno.Count >= 1)
                //{
                //}
                DataSet ds_3 = new DataSet();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd_3 = new SqlCommand();
                    oCmd_3.Connection = sqlConn;
                    oCmd_3.CommandText = "esp_TreatyApplyToTreatyCase";
                    oCmd_3.CommandType = CommandType.StoredProcedure;
                    oCmd_3.Parameters.AddWithValue("requisition_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    SqlDataAdapter oda_3 = new SqlDataAdapter(oCmd_3);
                    oda_3.Fill(ds_3, "myTable");
                }
                DataView dv_actno = ds_3.Tables[0].DefaultView;
                if (dv_actno.Count >= 1)
                {
                }
                //ds_3.Dispose();
                //oCmd_3.Dispose();
                //oda_3.Dispose();
                Response.Cache.SetCacheability(HttpCacheability.NoCache);
                string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyApply_View.aspx?seno=" + HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace(e.CommandArgument.ToString(), "");
            BindData_Customer();
        }
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
        if (LB != null)
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", GetUserIP());
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();
        //SqlConnection oConn = new SqlConnection();
        //oConn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //oConn.Open();
        //SqlCommand oCmd = oConn.CreateCommand();
        //oCmd.CommandText = "esp_treaty_log";
        //oCmd.CommandType = CommandType.StoredProcedure;
        //oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(xID));
        //oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        //oCmd.Parameters.AddWithValue("empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        //oCmd.Parameters.AddWithValue("txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
        //oCmd.Parameters.AddWithValue("txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
        //oCmd.Parameters.AddWithValue("xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
        //oCmd.Parameters.AddWithValue("xApp", oRCM.SQLInjectionReplaceAll(xApp));
        //oCmd.ExecuteNonQuery();
        //oCmd.Dispose();
        //oConn.Close();

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = sqlConn;
            oCmd.CommandText = "esp_treaty_log";
            oCmd.CommandType = CommandType.StoredProcedure;
            oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(xID));
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            oCmd.Parameters.AddWithValue("empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            oCmd.Parameters.AddWithValue("txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            oCmd.Parameters.AddWithValue("txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            oCmd.Parameters.AddWithValue("xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
            oCmd.Parameters.AddWithValue("xApp", oRCM.SQLInjectionReplaceAll(xApp));
            oCmd.CommandTimeout = 0;
            oCmd.ExecuteNonQuery();
        }
    }

}