﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Qry_customer_cb.aspx.cs" Inherits="Qry_customer_cb" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript">
        function close_win() {
            window.opener = null;
            window.open("", "_self");
            window.close();
        }
        //重新調整目前視窗            
        function adjustDims() {
            //window.dialogHeight = '' + (window.document.all.tags("body")[0].scrollHeight + 600) + 'px';
            //window.dialogWidth = '' + (window.document.all.tags("body")[0].scrollWidth + 350) + 'px';
            if (parent.$.fn.colorbox != undefined) {
                parent.$.fn.colorbox.resize({
                    height: "90%",
                    width: "92%"
                });
            }
            if (parent.$.fancybox != undefined)
                parent.$.fancybox.close();
        }
        function go(url) {
            var a = document.createElement("a");
            a.href = url;
            document.body.appendChild(a);
            a.click();
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <div style="padding: 15px; padding-top: 10px;">
                <asp:DropDownList ID="DDL_code1" runat="server" AutoPostBack="True"
                    DataTextField="code_valuedesc"
                    DataValueField="code_value"
                    OnSelectedIndexChanged="DDL_code1_SelectedIndexChanged"
                    OnDataBound="DDL_code1_DataBound" Enabled="True" Visible="False">
                </asp:DropDownList><br />
                <asp:DropDownList ID="DDL_code2" runat="server" DataTextField="code_valuedesc" DataValueField="code_value" OnDataBound="DDL_code2_DataBound" Enabled="True" Visible="False" />
                關鍵字:<asp:TextBox ID="TB_mp" runat="server" Width="400px"></asp:TextBox>
                <asp:Button ID="BT_search" runat="server" OnClick="BT_search_Click" Text="查詢" />
                &nbsp; 
             <asp:Button ID="btn_Ins" runat="server" OnClick="btn_Ins_Click" Text="新增" Visible="False" />
                <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False"
                    CellPadding="4" Width="600px"
                    GridLines="None"
                    EnableModelValidation="True" AllowPaging="True"
                    OnPageIndexChanged="SGV_company_PageIndexChanged"
                    OnPageIndexChanging="SGV_company_PageIndexChanging"
                    OnRowCreated="SGV_company_RowCreated"
                    OnRowCommand="SGV_company_RowCommand">
                    <FooterStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
                    <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                    <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
                    <Columns>
                        <asp:BoundField DataField="comp_idno" HeaderText="公司統編" ReadOnly="True">
                            <HeaderStyle HorizontalAlign="Left" />
                            <ItemStyle Width="100px" HorizontalAlign="Left" Font-Size="Small" />
                        </asp:BoundField>
                        <asp:TemplateField HeaderText="公司名稱">
                            <ItemTemplate>
                                <asp:LinkButton ID="LB_comp" runat="server" CommandName="view_case" CommandArgument='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'>  <%#DataBinder.Eval(Container.DataItem, "comp_cname")%></asp:LinkButton>
                            </ItemTemplate>
                            <HeaderStyle HorizontalAlign="Left" />
                            <ItemStyle HorizontalAlign="Left" Width="500px" Font-Size="Small" />
                        </asp:TemplateField>
                    </Columns>
                    <EmptyDataTemplate>
                        <!--當找不到資料時則顯示「查無資料」-->
                        <asp:Label ID="Label1" runat="server" ForeColor="Red" Text="查無資料!"></asp:Label>
                    </EmptyDataTemplate>
                    <FooterStyle BackColor="White" />
                    <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                    <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                    <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                </cc1:SmartGridView>
                註:當客戶名稱與顯示的客戶名稱不同時，請使用者直接到[<a href='https://itriap7.itri.org.tw/cust/mgrcust_custquery.aspx' target='cust'>客戶管理系統</a>],啟動[編輯]更新[客戶名稱].					 
            </div>
        </span>

        <%--        <asp:SqlDataSource ID="SDL_code_1" runat="server" ConnectionString="<%$ ConnectionStrings:pubbs %>" SelectCommand="SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl WHERE (code_value LIKE '__0') and code_type='001' "></asp:SqlDataSource>
        <asp:SqlDataSource ID="SDL_code_2" runat="server" ConnectionString="<%$ ConnectionStrings:pubbs %>" SelectCommand="SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl where 1=0 "></asp:SqlDataSource>
        <asp:SqlDataSource ID="SDS_jssg" runat="server" ConnectionString="<%$ ConnectionStrings:pubbs %>"></asp:SqlDataSource>
        <asp:SqlDataSource ID="SDS_Instblgetcust" runat="server" ConnectionString="<%$ ConnectionStrings:pubbs %>"></asp:SqlDataSource>--%>
    </form>
    <script type="text/javascript">
        $(document).ready(function () {
            $('#TB_mp').defaultValue('可使用 客戶名稱､簡稱或統一編號  查詢');
        });
    </script>
</body>
</html>
