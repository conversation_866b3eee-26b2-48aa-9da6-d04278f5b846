﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="NoAuthRight.aspx.vb" Inherits="danger" %>

<%@ Register src="../Comp/EmployeeMultiSelect/EmployeeMultiSelect.ascx" tagname="EmployeeMultiSelect" tagprefix="uc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <script src="../js/jquery-1.10.2.min.js"></script>
    <script src="../js/jquery.colorbox-min.js"></script>
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript">
        function Find_Empno(obj, arg_sw) {


            $(".ajax_mesg").colorbox({
                href: "../Comp/EmployeeMultiSelect/EmployeeWindow.aspx?hfValue=" + $('#' + obj).val()
               , iframe: true, width: "800px", height: "630px", transition: "none", opacity: "0.5", overlayClose: false
               , title: '多人挑選'
               , onClosed: function () {
                   $('html, body').css('overflow', '');
                   var strURL = '../Comp/EmployeeMultiSelect/ret_employee_kw.aspx';
                   if (arg_sw == "1") {
                       $.getJSON(strURL + '?callback=?', jsonp_callback1);
                   }
                   if (arg_sw == "2") {
                       $.getJSON(strURL + '?callback=?', jsonp_callback2);
                   }
               }
            });
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#TB_empno_string').val(data.c_com_empno);
                    $('#TB_MtName').val(data.c_com_cname);
 
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }
    </script>
    <style type="text/css">
        .td_right { text-align: right}
        .td_left{ text-align: left;}
        .TB_ReadOnly{background-color: rgb(236, 233, 216);}
        #colorbox #cboxClose { top: 0;  right: 0;border: 0px solid #d3d3d3;  background:url('../css/myITRIproject/images/ui-icons_888888_256x240.png') 11.5% 86.9% repeat-x ;z-index:10;}
        #cboxTitle{position:absolute; top:0px; left:0; text-align:center; width:100%;height:24px; color:#FFF;font-weight:bold;background-color:#2d70a4;}
        .empty { color: #aaa; }
    </style>

</head>
<body>
    <form id="form1" runat="server">

沒有權限
        <div style="display:none">
        <asp:textbox id="TB_MtName" runat="server"  class="inputex text-input"></asp:textbox>
        <a onclick="javascript:Find_Empno('TB_empno_string','1');"  >
        <img id="img_promoter_name" src="./images/icon_search.gif" border="0"  class="ajax_mesg btn_mouseout" /></a>&nbsp;
        <div style="display:none"></div><asp:TextBox ID="TB_empno_string" runat="server"></asp:TextBox>
</div>

    </form>
</body>
</html>
