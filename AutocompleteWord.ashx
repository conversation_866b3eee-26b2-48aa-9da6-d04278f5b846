﻿<%@ WebHandler Language="C#" Class="AutocompleteWord" %>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Xml;

using System.Diagnostics;
using System.Text.RegularExpressions;
using System.IO;

using Newtonsoft.Json;
using System.Data.SqlClient;
public class AutocompleteWord : IHttpHandler {

    partial class LimitDDL
    {
        public string it_term { get; set; }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
　/// <summary>
    /// SQL Injection 防範之道, 共用的置換 function.

    public string SQLInjectionReplace(string inputString)
    {
        //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
        //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
        return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    }​
    public void ProcessRequest (HttpContext context)
    {

        string func = this.SQLInjectionReplace((HttpUtility.UrlDecode(context.Request.QueryString["func"]) != null) ? HttpUtility.UrlDecode(context.Request.QueryString["func"]).ToString().Trim() : "");//switch case
        //string strLimitType = (HttpUtility.UrlDecode(context.Request.QueryString["strLimitType"]) != null)  ? HttpUtility.UrlDecode(context.Request.QueryString["strLimitType"].ToString().Trim())  : "";// ""=>關鍵字查詢 "Y"=>限縮查詢
        //string strKWD = (HttpUtility.UrlDecode(context.Request.QueryString["strKWD"]) != null)              ? HttpUtility.UrlDecode(context.Request.QueryString["strKWD"].ToString().Trim())        : "";//關鍵字字串
        //string strLWD = (HttpUtility.UrlDecode(context.Request.QueryString["strLWD"]) != null)              ? HttpUtility.UrlDecode(context.Request.QueryString["strLWD"].ToString().Trim())        : "";//限縮條件字串
        string strTextLimit = this.SQLInjectionReplace((HttpUtility.UrlDecode(context.Request.QueryString["acstr"]) != null)         ? HttpUtility.UrlDecode(context.Request.QueryString["acstr"].ToString().Trim() )        : "");//限縮條件 autocomplate用
                                                                                                                                                                                                         //string strexportType = (HttpUtility.UrlDecode(context.Request.QueryString["exportType"]) != null)   ? HttpUtility.UrlDecode(context.Request.QueryString["exportType"].ToString().Trim())    : "";//匯出類別 "1"=>加到我的收藏 "2"=>匯出
        string topX = this.SQLInjectionReplace((HttpUtility.UrlDecode(context.Request.QueryString["topX"]) != null) ? HttpUtility.UrlDecode(context.Request.QueryString["topX"]).ToString().Trim() : "10");                                                                                                                                                                                            //string strexportArr = (HttpUtility.UrlDecode(context.Request.QueryString["exportArr"]) != null)     ? HttpUtility.UrlDecode(context.Request.QueryString["exportArr"].ToString().Trim())     : "";//匯出doc_id
        if (!IsNumber(topX))
            topX = "10";

        List<LimitDDL> AutoComplete_list = new List<LimitDDL>();
        SqlDataSource SDS_base = new SqlDataSource();
        SDS_base.ID = "SDS_base";
        SDS_base.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        SDS_base.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_APMS"].ConnectionString;
        SDS_base.SelectCommand = "esp_辭庫查詢";
        SDS_base.SelectParameters.Add("topX", topX );    //this.SQLInjectionReplace( )
        SDS_base.SelectParameters.Add("kw", strTextLimit);
        for (int i = 0; i < SDS_base.SelectParameters.Count; i++)
        {
            SDS_base.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        SDS_base.DataBind();
        System.Data.DataView drv = (DataView)SDS_base.Select(new DataSourceSelectArguments());
        if (drv.Count > 0)
        {
            for (int i = 0; i < drv.Count; i++)
            {
                LimitDDL 清單內容 = new LimitDDL();
                清單內容.it_term = drv[i][0].ToString();
                AutoComplete_list.Add(清單內容);
            }
            //將objResult物件序列化成JSON格式再回傳
            //context.Response.ContentType = "application/json";
            //context.Response.Charset = "utf-8";
            //context.Response.Write(new System.Web.Script.Serialization.JavaScriptSerializer().Serialize(AutoComplete_list));
        }
        SDS_base.Dispose();

        //List<LimitDDL> AutoComplete_list = new List<LimitDDL>();
        //LimitDDL 清單內容 = new LimitDDL();
        //清單內容.it_term = "a";
        //AutoComplete_list.Add(清單內容);
        //LimitDDL 清單內容2 = new LimitDDL();
        //清單內容2.it_term = "ab";
        //AutoComplete_list.Add(清單內容2);
        //LimitDDL 清單內容3 = new LimitDDL();
        //清單內容3.it_term = "abc";
        //AutoComplete_list.Add(清單內容3);


        //將objResult物件序列化成JSON格式再回傳
         context.Response.ContentType = "application/json";
        context.Response.Charset = "utf-8";
        context.Response.Write(new System.Web.Script.Serialization.JavaScriptSerializer().Serialize(AutoComplete_list));

    }


    public bool IsReusable {
        get {
            return false;
        }
    }

}