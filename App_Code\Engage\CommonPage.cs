﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text.RegularExpressions;
using System.Data;
using System.Text;

namespace Engage
{
	/// <summary>
	/// CommonPage 類別,科專系統網頁頁面共同的起始頁面(含會自動處理 OnError 事件寄送給相關人員,及共用元件功能)
	/// </summary>
	public class CommonPage : common
	{
		protected Int64 m_seqsn = 0;
		protected int m_ver = 0;
		protected string m_pageMode = "view";
		protected Engage.engageDataContext m_db;
		protected Engage.engageDataContext m_dbhis;


		/* 2013/12/06:<PERSON>(add)
		 * 繼承樹關係:
		 * System.Web.UI.Page
		 *		^
		 *		|
		 *		+--- common	   奕嘉的共用元件(含 FAQ, OnError()錯誤處理常式...),請不用直接修改此檔案,應從奕嘉取得新版本common.cs直接覆蓋。
		 *				^
		 *				|
		 *				+----- CommonPage	系統的所有頁面從此繼承下去，目前先架一個空類別，以利未來的擴充使用,若須修改，請由此檔案修改。
		 */
		public CommonPage()
		{
			this.Title = this.GetConfigString("PageTitle");
			//this.MaintainScrollPositionOnPostBack = true;
			m_db = new Engage.engageDataContext(base.ConnString);
		}

		protected override void OnPreInit(EventArgs e)
		{
			if (Request["seqsn"] != null && !long.TryParse(Request["seqsn"], out m_seqsn))
				Response.Redirect("../Shared/error.aspx?err=para");
			if (Request["ver"] != null && !int.TryParse(Request["ver"], out m_ver))
				Response.Redirect("../Shared/error.aspx?err=para");

			if (Request["pagemode"] != null && SQLInjectionReplace(Request["pagemode"]) != "")
				m_pageMode = SQLInjectionReplace(Request["pagemode"]).ToLower();
			if (m_pageMode.Equals("viewhis") && m_ver > 0)
				m_dbhis = new Engage.engageDataContext(base.ConnString.ConnectionString.Replace("engagedb", "engage_his"));

			base.OnPreInit(e);
		}


		#region SQLInjectionReplace function
		/// <summary>
		/// SQL Injection 防範之道, 共用的置換 function.
		/// </summary>
		/// <param name="inputString"></param>
		/// <returns></returns>
		public string SQLInjectionReplace(string inputString)
		{
			//20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
			//參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
			return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
		}
        #endregion

        #region 移除Shell攻擊注入
        /// <summary>
        /// 移除Shell攻擊注入
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public string RemoveShell(string str)
        {
            string strOri = str;
            if (!string.IsNullOrEmpty(strOri))
            {
                strOri = strOri.Replace("&", "");
                strOri = strOri.Replace("$", "");
                strOri = strOri.Replace(";", "");
                strOri = strOri.Replace("..", "");
            }

            return strOri;
        }
        #endregion

        #region 繫結web控制項資料源
        /// <summary>
        /// 繫結web控制項資料源
        /// </summary>
        /// <param name="sender">web控制項</param>
        /// <param name="dv">資料源</param>
        /// <param name="txtField">文字欄位名</param>
        /// <param name="valField">值欄位名</param>
        public void BindWebControlDataSource(object sender, DataView dv, string txtField, string valField)
		{
			if (sender.GetType() == typeof(DropDownList))
			{
				DropDownList ddl = sender as DropDownList;
				ddl.DataSource = dv;
				ddl.DataTextField = txtField;
				ddl.DataValueField = valField;
				ddl.DataBind();
			}
			else if (sender.GetType() == typeof(RadioButtonList))
			{
				RadioButtonList rbl = sender as RadioButtonList;
				rbl.DataSource = dv;
				rbl.DataTextField = txtField;
				rbl.DataValueField = valField;
				rbl.DataBind();
			}
			else if (sender.GetType() == typeof(CheckBoxList))
			{
				CheckBoxList cbl = sender as CheckBoxList;
				cbl.DataSource = dv;
				cbl.DataTextField = txtField;
				cbl.DataValueField = valField;
				cbl.DataBind();
			}
		}

		/// <summary>
		/// 繫結web控制項資料源
		/// </summary>
		/// <param name="sender">web控制項</param>
		/// <param name="dv">資料源</param>
		/// <param name="txtField">文字欄位名</param>
		/// <param name="valField">值欄位名</param>
		/// <param name="callback">回呼函式</param>
		public void BindWebControlDataSource(object sender, DataView dv, string txtField, string valField, Action callback)
		{
			this.BindWebControlDataSource(sender, dv, txtField, valField);
			callback.Invoke();
		}

		/// <summary>
		/// 繫結web控制項資料源
		/// </summary>
		/// <param name="sender">web控制項</param>
		/// <param name="dsource">List資料源</param>
		/// <param name="txtField">文字欄位名</param>
		/// <param name="valField">值欄位名</param>
		public void BindWebControlDataSource<T>(object sender, List<T> dsource, string txtField, string valField)
		{
			if (sender.GetType() == typeof(DropDownList))
			{
				DropDownList ddl = sender as DropDownList;
				ddl.DataSource = dsource;
				ddl.DataTextField = txtField;
				ddl.DataValueField = valField;
				ddl.DataBind();
			}
			else if (sender.GetType() == typeof(RadioButtonList))
			{
				RadioButtonList rbl = sender as RadioButtonList;
				rbl.DataSource = dsource;
				rbl.DataTextField = txtField;
				rbl.DataValueField = valField;
				rbl.DataBind();
			}
			else if (sender.GetType() == typeof(CheckBoxList))
			{
				CheckBoxList cbl = sender as CheckBoxList;
				cbl.DataSource = dsource;
				cbl.DataTextField = txtField;
				cbl.DataValueField = valField;
				cbl.DataBind();
			}
		}

		/// <summary>
		/// 繫結web控制項資料源
		/// </summary>
		/// <param name="sender">web控制項</param>
		/// <param name="dsource">List資料源</param>
		/// <param name="txtField">文字欄位名</param>
		/// <param name="valField">值欄位名</param>
		/// <param name="callback">回呼函式</param>
		public void BindWebControlDataSource<T>(object sender, List<T> dsource, string txtField, string valField, Action callback)
		{
			this.BindWebControlDataSource(sender, dsource, txtField, valField);
			callback.Invoke();
		}

		#endregion

		#region 系統代碼檔
		/// <summary>
		/// 取得洽案系統的代碼檔
		/// </summary>
		/// <param name="code_type">代碼類別, 102:產業別, 103:成功機率, 104:預估金額, 105:文件種類, 106:權利金收取方式, 150:技術領域一, 152:技術領域二, 156:技術成熟度</param>
		/// <returns></returns>
		public List<Engage.engage_codetbl> GetEngageCodeTable(string code_type)
		{
			var query = from c in m_db.engage_codetbls
						where c.code_type == code_type && c.code_enabled == '1'
						select c;
			return query.ToList();
		}

		public string GetEngageCodeTable(string code_type, string code_value)
		{
			var query = from c in m_db.engage_codetbls
						where c.code_type == code_type && c.code_enabled == '1' && c.code_value == code_value
						select c.code_valuedesc;
			return query.SingleOrDefault();
		}

		/// <summary>
		/// 取得訪談系統的代碼檔
		/// </summary>
		/// <param name="code_type">代碼類別, 007:國家別, 008:縣市別, 009:郵遞區號</param>
		/// <returns></returns>
		public List<Engage.visit_codetbl> GetVisitCodeTable(string code_type)
		{
			var query = from c in m_db.visit_codetbls
						where c.code_type == code_type && c.code_enabled == true
						select c;
			return query.ToList();
		}

		public string GetVisitCodeTable(string code_type, string code_value)
		{
			var query = from c in m_db.visit_codetbls
						where c.code_type == code_type && c.code_enabled == true && c.code_value == code_value
						select c.code_valuedesc;
			return query.SingleOrDefault();
		}

		/// <summary>
		/// 取得契約系統的代碼檔
		/// </summary>
		/// <param name="code_type">代碼類別, 004:稅別, 010:幣別, 008:技資類型, 012:成果來源及歸屬</param>
		/// <returns></returns>
		public List<Engage.cont_codetbl> GetContractCodeTable(string code_type)
		{
			var query = from c in m_db.cont_codetbls
						where c.code_type == code_type && c.code_enabled == true
						select c;
			return query.ToList();
		}

		public string GetContractCodeTable(string code_type, string code_value)
		{
			var query = from c in m_db.cont_codetbls
						where c.code_type == code_type && c.code_enabled == true && c.code_value == code_value
						select c.code_valuedesc;
			return query.SingleOrDefault();
		}

		#endregion

		#region JAVASCRIPT
		/// <summary>
		/// 顯示訊息
		/// </summary>
		/// <param name="msg"></param>
		public void Alert(string msg)//顯示訊息
		{
			ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");", msg), true);
		}
		/// <summary>
		/// 關閉視窗
		/// </summary>
		public void Close()
		{
			ScriptManager.RegisterStartupScript(this.Page, this.GetType(), "close", "window.close();", true);

		}
		/// <summary>
		/// 顯示訊息完後關閉視窗
		/// </summary>
		/// <param name="msg">輸出的訊息</param>
		public void Alert_Close(string msg)//顯示訊息完後關閉視窗
		{
			ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");window.close();", msg), true);

		}
		/// <summary>
		/// 顯示訊息完後關閉 Colorbox 視窗
		/// </summary>
		/// <param name="msg">輸出的訊息</param>
		public void Alert_CloseColorbox(string msg)//顯示訊息完後關閉視窗
		{
			ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");parent.$.colorbox.close();", msg), true);

		}

		/// <summary>
		/// 顯示完訊息後導至網頁
		/// </summary>
		/// <param name="msg">輸出的訊息</param>
		/// <param name="path">導至錨點</param>
		public void Alert_ToAnchor(string msg, string anchor)
		{
			string script = string.Format(@"
					$(document).ready(function () 
					{{
						$('{1}').click().focus();
						alert('{0}');
					}});
				", msg, anchor);
			ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, script, true);
		}

		/// <summary>
		/// 顯示完訊息後導至網頁
		/// </summary>
		/// <param name="msg">輸出的訊息</param>
		/// <param name="path">導至網頁的URL</param>
		public void AlertGo(string msg, string path)//顯示完訊息後導至網頁
		{
			ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");location.href='{1}';", msg, path), true);
		}
		#endregion

		#region 驗證

		#region 判斷是否為數字
		/// <summary>
		/// 判斷是否為數字(不使用小數點)(0~9)
		/// </summary>
		/// <param name="str">欲判斷的值</param>
		/// <returns></returns>
		public bool IsNumeric(string str)
		{
			char[] tmp = str.ToCharArray();
			for (int i = 0; i < tmp.Length; i++)
			{
				if ((int)tmp[i] < 48 || (int)tmp[i] > 57)
				{
					return false;
				}

			}
			return true;
		}

		#endregion

		#region 判斷是否為日期格式
		/// <summary>
		/// 判斷是否為日期格式
		/// </summary>
		/// <param name="str">欲判斷的值</param>
		/// <returns></returns>
		public bool IsDate(string str)
		{
			bool success = false;
			DateTime odate = DateTime.Now;
			if (str.Length > 8)
			{
				success = DateTime.TryParse(str, out odate);
			}
			else if (str.Length == 8)
			{
				string date = string.Format("{0}/{1}/{2}", str.Substring(0, 4), str.Substring(4, 2), str.Substring(6, 2));
				success = DateTime.TryParse(date, out odate);
			}
			else
			{
				success = false;
			}

			return success;
		}

		#endregion

		#region 檢查參數

		/// <summary>
		/// 檢查參數
		/// </summary>
		/// <param name="name">參數名稱</param>
		/// <param name="type">參數資料型態</param>
		/// <param name="isexist">參數是否必須存在</param>
		/// <returns></returns>
		public bool chkRequestData(string name, TypeCode type, bool isexist)
		{

			if (isexist)//檢查是否必須存在
			{
				if (string.IsNullOrEmpty(Request[name]))
				{
					return false;
				}
			}

			#region 檢查資料型態
			if (!string.IsNullOrEmpty(Request[name]))
			{
				switch (type.ToString())
				{
					case "Int32":
						if (!IsNumeric(Request[name]))
						{
							return false;
						}
						break;

				}
			}
			#endregion

			return true;
		}

		#endregion

		#region 檢查mail正確性
		/// <summary>
		/// 判斷Email的正確性
		/// </summary>
		/// <param name="txt_mail">欲判斷mail</param>
		/// <returns></returns>

		public bool MailCheck(string txt_mail)
		{
			Regex myEmailRegex = new Regex(@"([a-zA-Z_0-9.-]+\@[a-zA-Z_0-9.-]+\.\w+)", RegexOptions.IgnoreCase);

			if (!myEmailRegex.IsMatch(txt_mail, 0))
			{
				return false;
			}

			return true;
		}
		#endregion

		#endregion

		#region 取得簽核狀態
		/// <summary>
		/// 取得簽核狀態
		/// </summary>
		/// <param name="oStatus"></param>
		/// <returns></returns>
		public string GetStatusName(object oStatus)
		{
			string tmp = "";
			switch(oStatus.ToString())
			{
				case "0":
					tmp = "未簽核";
					break;
				case "1":
					tmp = "簽核中";
					break;
				case "2":
					tmp = "簽核完成";
					break;
				default:
					tmp = "";
					break;
			}
			return tmp;
		}
		#endregion

		#region 行動裝置不允許下載檔案

		//判斷是否為行動裝置
		public bool checkMobile(HttpRequest request)
		{
			string u = request.ServerVariables["HTTP_USER_AGENT"];
			Regex b = new Regex(@"(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino", RegexOptions.IgnoreCase | RegexOptions.Multiline);
			Regex v = new Regex(@"1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-", RegexOptions.IgnoreCase | RegexOptions.Multiline);
			if ((b.IsMatch(u) || v.IsMatch(u.Substring(0, 4))))
			{
				return true;
			}    

			return false;
		}

        #endregion


        #region 加密編碼
        public string encode(String strData)
        {
            try
            {
                return System.Convert.ToBase64String(System.Text.UTF8Encoding.UTF8.GetBytes(strData));
            }
            catch
            {
                return "";
            }
        }
        #endregion

        #region 解密編碼
        public string decode(String strData)
        {
            try
            {
                return System.Text.Encoding.GetEncoding("utf-8").GetString(Convert.FromBase64String(strData));
            }
            catch
            {
                return "";
            }
        }
        #endregion

        #region Matomo 追蹤碼
        public string getMatomo(string UserID_Input)
        {
            //UserID格式參考 : 890689_許增億_資訊_170E000
            StringBuilder sb_res = new StringBuilder();
            string UserID = UserID_Input;
            //sb_res.Append("<script type='text/javascript'>");
            //sb_res.Append("var _mtm = _mtm || [];");
            //sb_res.Append("_mtm.push({'mtm.startTime': (new Date().getTime()), 'event': 'mtm.Start'});");
            //sb_res.Append("_mtm.push({'UserIDContainer':'" + UserID + "'});");
            //sb_res.Append("var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];");
            //sb_res.Append("g.type='text/javascript'; g.async=true; g.defer=true; g.src='https://piwik.itri.org.tw/piwik/js/container_6ylvkYYr.js'; s.parentNode.insertBefore(g,s);");
            //sb_res.Append("</script>");

            return sb_res.ToString();
        }

        public static string Comm_getMatomo(string UserID_Input)
        {
            //UserID格式參考 : 890689_許增億_資訊_170E000
            StringBuilder sb_res = new StringBuilder();
            string UserID = UserID_Input;
            //sb_res.Append("<script type='text/javascript'>");
            //sb_res.Append("var _mtm = _mtm || [];");
            //sb_res.Append("_mtm.push({'mtm.startTime': (new Date().getTime()), 'event': 'mtm.Start'});");
            //sb_res.Append("_mtm.push({'UserIDContainer':'" + UserID + "'});");
            //sb_res.Append("var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];");
            //sb_res.Append("g.type='text/javascript'; g.async=true; g.defer=true; g.src='https://piwik.itri.org.tw/piwik/js/container_6ylvkYYr.js'; s.parentNode.insertBefore(g,s);");
            //sb_res.Append("</script>");

            return sb_res.ToString();
        }
        #endregion
    }
}