﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;

public partial class web_ret_employee : System.Web.UI.Page
{
    public class employee
    {
        private string Commonkey;
        private string com_empno;
        private string com_cname;
        private string com_telext;
        private string com_orgcd;
        private string com_deptcd;
        private string com_deptid;
        private string com_dept_name;
        private string com_mailadd;
        private string com_orgName;
        public string c_Commonkey
        {
            get { return Commonkey; }
            set { Commonkey = value; }
        }

        public string c_com_empno
        {
            get { return com_empno; }
            set { com_empno = value; }
        }

        public string c_com_cname
        {
            get { return com_cname; }
            set { com_cname = value; }
        }
        public string c_com_telext
        {
            get { return com_telext; }
            set { com_telext = value; }
        }

        public string c_com_orgcd
        {
            get { return com_orgcd; }
            set { com_orgcd = value; }
        }
        public string c_com_deptcd
        {
            get { return com_deptcd; }
            set { com_deptcd = value; }
        }
        public string c_com_deptid
        {
            get { return com_deptid; }
            set { com_deptid = value; }
        }

        public string c_com_dept_name
        {
            get { return com_dept_name; }
            set { com_dept_name = value; }
        }
        public string c_com_mailadd
        {
            get { return com_mailadd; }
            set { com_mailadd = value; }
        }
        public string c_com_orgName
        {
            get { return com_orgName; }
            set { com_orgName = value; }
        }
    }
   
    protected void Page_Load(object sender, System.EventArgs e)
    {
        employee u = new employee();
 
            if (Session["empno"] !=null)
            {
                u.c_com_empno = Server.HtmlEncode(Session["empno"].ToString().Trim());
                u.c_com_cname = Server.HtmlEncode(Session["empname"].ToString().Trim());
                u.c_com_telext = "";
                u.c_com_orgcd =  "";
                u.c_com_deptcd =  "";
                u.c_com_deptid =  "";
                u.c_com_dept_name = "";
                u.c_com_mailadd =  "";
                u.c_com_orgName =  "";
                Session["empno"] = null;
                Session["empname"]  = null;   
            }
            else
            {
                u.c_com_empno = "";
                u.c_com_cname = "error0";
                u.c_com_telext = "";
                u.c_com_orgcd = "";
                u.c_com_deptcd = "";
                u.c_com_deptid = "";
                u.c_com_dept_name = "";
                u.c_com_mailadd = "";
                u.c_com_orgName = "";
            }

        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            if (CallBackFunction.Length > 50)
                Response.Redirect("../danger.aspx");
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        Response.Write(j);  //輸出JSONP的內容
    }


}