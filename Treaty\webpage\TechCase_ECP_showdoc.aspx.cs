﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;

public partial class Treaty_webpage_TechCase_ECP_showdoc : Treaty.common
{
    public List<SqlParameter> sqlParamList = new List<SqlParameter>();

    public string Ecp_guid
    {
        set { ViewState["Ecp_guid"] = value; }
        get
        {
            if (ViewState["Ecp_guid"] == null)
            {
                string guid = Request.QueryString["guid"];
                if (string.IsNullOrEmpty(guid) || !Check_calendarID(guid))
                {
                    Response.Redirect("../danger.aspx");
                }
                ViewState["Ecp_guid"] = oRCM.SQLInjectionReplaceAll(guid);
            }

            return ViewState["Ecp_guid"].ToString();
        }
    }
    public string Seno
    {
        set { ViewState["seno"] = value; }
        get
        {
            string seno = "";
            if (ViewState["seno"] == null)
            {
                if (Request.QueryString["seno"] == null)
                {
                    seno = "";
                }
                else
                {
                    seno = Request.QueryString["seno"];
                    if (string.IsNullOrEmpty(seno) || !IsNumber(seno))
                    {
                        Response.Redirect("../danger.aspx");
                    }
                }

                ViewState["seno"] = oRCM.SQLInjectionReplaceAll(seno);
            }
            return ViewState["seno"].ToString();
        }
    }

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            BindData();
            // 確保在每次頁面加載時都註冊 LinkButton 控件           
            ScriptManager scriptManager = ScriptManager.GetCurrent(this.Page);
            scriptManager.RegisterPostBackControl(gv_data);
        }
    }

    private void BindData()
    {
        DataTable dt = new DataTable();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
        sqlParamList.Add(new SqlParameter("signGUID", oRCM.SQLInjectionReplaceAll(Ecp_guid)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("View_signOther")));
        dt = get_SP();
        if (dt.Rows.Count > 0)
        {
            gv_data.DataSource = dt;
            gv_data.DataBind();

        }
    }

    protected void gv_data_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }

            string args = e.CommandArgument.ToString();
            if (!string.IsNullOrEmpty(args))
            {
                DownloadFile(args);
            }
            else
            {
                string script = string.Format("alert('檔案不存在！');");
                ScriptManager.RegisterStartupScript(this, this.GetType(), "downloadFile", script, true);
            }
        }
    }
    private void DownloadFile(string tcdf_no)
    {
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("file_view")));
        DataTable dt = new DataTable();
        dt = get_SP_File();
        if (dt.Rows.Count > 0)
        {
            string filePath = dt.Rows[0]["tcdf_url"].ToString().Trim();
            if (System.IO.File.Exists(filePath))
            {

                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(dt.Rows[0]["tcdf_doc"].ToString().Trim(), Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(filePath)));
                Response.Flush();
                Response.End();
            }
            else
            {
                Response.End();
            }
        }
    }

    public DataTable get_SP_File()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_treaty_TechCase_modify";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));

        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;

    }


    public DataTable get_SP()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }
}