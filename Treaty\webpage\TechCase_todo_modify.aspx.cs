﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_todo_modify : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    Dictionary<string, string> _dtSend
    {
        set { ViewState["_Send"] = value; }
        get
        {
            if (ViewState["_Send"] == null)
            {
                ViewState["_Send"] = new Dictionary<string, string>();
            }
            return (Dictionary<string, string>)ViewState["_Send"];
        }
    }

    Dictionary<string, string> _dtCC
    {
        set { ViewState["_CC"] = value; }
        get
        {
            if (ViewState["_CC"] == null)
            {
                ViewState["_CC"] = new Dictionary<string, string>();
            }
            return (Dictionary<string, string>)ViewState["_CC"];
        }
    }

    DataTable _dtCare
    {
        get { return (DataTable)ViewState["_Care"]; }
        set { ViewState["_Care"] = value; }
    }

    DataTable _dtGroupCode
    {
        get { return (DataTable)ViewState["_GroupCode"]; }
        set { ViewState["_GroupCode"] = value; }
    }

    DataTable _dtGroupCondition
    {
        get { return (DataTable)ViewState["_GroupCondition"]; }
        set { ViewState["_GroupCondition"] = value; }
    }

    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["t_id"] = "";
            if (Request.QueryString["t_id"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["t_id"].ToString()) || (Request.QueryString["t_id"].Length > 5))
                    Response.Redirect("../danger.aspx");
                ViewState["t_id"] = Request.QueryString["t_id"].ToString();
            }

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;

            Bind_Group_Code();

            _dtGroupCondition = Get_Group_Condition("");

            Bind_Email();

            if (ViewState["t_id"].ToString() != "")
            {
                Bind_Data();
                Bind_Notify();
                Bind_File();
                Bind_Todo_Care();
            }
        }
        if (Request.Params.Get("__EVENTTARGET") == "renew")
        {
            btnQuery_Click(btnQuery, EventArgs.Empty);
        }
    }

    private void Bind_Group_Code()
    {
        DataTable dt = new DataTable();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "code_list");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);


            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        _dtGroupCode = dt;
    }

    private DataTable Get_Group_Condition(string group_code)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "condition_list");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@c_group_code", oRCM.SQLInjectionReplaceAll(group_code));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        return dt;
    }

    private void Bind_Data()
    {
        if (ViewState["t_id"].ToString() == "")
        {
            btnSave.Text = "新增";
            btnQuery.Visible = false;
            tr_case.Visible = false;
        }
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "view_todo");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                if (dt.Rows.Count > 0)
                {
                    TB_name.Text = Server.HtmlEncode(dt.Rows[0]["t_name"].ToString());
                    TB_sdate.Text = Server.HtmlEncode(dt.Rows[0]["t_sdate"].ToString());
                    TB_edate.Text = Server.HtmlEncode(dt.Rows[0]["t_edate"].ToString());
                    TB_說明.Text = Server.HtmlDecode(Server.HtmlEncode(dt.Rows[0]["t_說明"].ToString()));
                }

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Bind_Notify()
    {
        if (ViewState["t_id"].ToString() == "")
        {
            btnSave.Text = "新增";
            btnQuery.Visible = false;
            tr_case.Visible = false;
        }
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "view_notify");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                if (dt.Rows.Count > 0)
                {
                    RBL_repeat.SelectedValue = dt.Rows[0]["notify_repeat"].ToString();
                    RBL_repeat_SelectedIndexChanged(RBL_repeat, EventArgs.Empty);
                    CB_notify_week_7.Checked = dt.Rows[0]["notify_week_7"].ToString() == "1";
                    CB_notify_week_1.Checked = dt.Rows[0]["notify_week_1"].ToString() == "1";
                    CB_notify_week_2.Checked = dt.Rows[0]["notify_week_2"].ToString() == "1";
                    CB_notify_week_3.Checked = dt.Rows[0]["notify_week_3"].ToString() == "1";
                    CB_notify_week_4.Checked = dt.Rows[0]["notify_week_4"].ToString() == "1";
                    CB_notify_week_5.Checked = dt.Rows[0]["notify_week_5"].ToString() == "1";
                    CB_notify_week_6.Checked = dt.Rows[0]["notify_week_6"].ToString() == "1";

                    _dtSend = new Dictionary<string, string>();
                    string[] send = dt.Rows[0]["notify_收件人"].ToString().Split(';');
                    foreach (string s in send)
                    {
                        ListItem i = DDL_Send.Items.FindByValue(s);
                        if (i != null && s != "")
                        {
                            _dtSend.Add(i.Value, i.Text);
                        }
                    }
                    rpt_Send.DataSource = _dtSend;
                    rpt_Send.DataBind();

                    _dtCC = new Dictionary<string, string>();
                    string[] cc = dt.Rows[0]["notify_副本"].ToString().Split(';');
                    foreach (string c in cc)
                    {
                        ListItem i = DDL_CC.Items.FindByValue(c);
                        if (i != null && c != "")
                        {
                            _dtCC.Add(i.Value, i.Text);
                        }
                    }
                    rpt_CC.DataSource = _dtCC;
                    rpt_CC.DataBind();
                }

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void Save_Notify()
    {
        string notify_收件人 = string.Join(";", _dtSend.Select(x => x.Key).ToList());
        string notify_副本 = string.Join(";", _dtCC.Select(x => x.Key).ToList());

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "Add_notify");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_sdate", oRCM.SQLInjectionReplaceAll(TB_sdate.Text));
            sqlCmd.Parameters.AddWithValue("@t_edate", oRCM.SQLInjectionReplaceAll(TB_edate.Text));
            sqlCmd.Parameters.AddWithValue("@notify_repeat", oRCM.SQLInjectionReplaceAll(RBL_repeat.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@notify_week_1", CB_notify_week_1.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_2", CB_notify_week_2.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_3", CB_notify_week_3.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_4", CB_notify_week_4.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_5", CB_notify_week_5.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_6", CB_notify_week_6.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_week_7", CB_notify_week_7.Checked ? "1" : "0");
            sqlCmd.Parameters.AddWithValue("@notify_收件人", oRCM.SQLInjectionReplaceAll(notify_收件人));
            sqlCmd.Parameters.AddWithValue("@notify_副本", oRCM.SQLInjectionReplaceAll(notify_副本));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
    }

    private void Bind_File()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "file_list");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    private DataTable File_View(string tdf_id)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");
            sqlCmd.Parameters.AddWithValue("@tdf_id", oRCM.SQLInjectionReplaceAll(tdf_id));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    private void Bind_Todo_Care()
    {
        sgvList.DataSource = null;
        sgvList.DataBind();
        sgvList.Visible = false;
        _dtCare = Get_Todo_Care();
        string[] group = _dtCare.AsEnumerable().Select(x => x.Field<string>("c_group")).Distinct().ToArray();

        rpt_AND.DataSource = group;
        rpt_AND.DataBind();

        foreach (RepeaterItem item in rpt_AND.Items)
        {
            Repeater rpt_OR = (Repeater)item.FindControl("rpt_OR");
            string c_group = ((TextBox)item.FindControl("TB_c_group")).Text;

            DataView dv = _dtCare.Copy().DefaultView;

            dv.RowFilter = "c_group = '" + c_group + "'";

            rpt_OR.DataSource = dv;
            rpt_OR.DataBind();
        }

        btnQuery_Click(btnQuery, EventArgs.Empty);
    }

    private DataTable Get_Todo_Care()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "view_care");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        return dt;
    }

    private string Get_Todo_Detail_Sql()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "view_care_detail");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

                if (dt.Rows.Count > 0)
                    return dt.Rows[0][0].ToString();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        return "";
    }

    private void Bind_Todo_Detail()
    {
        DataTable dt = new DataTable();
        string str_SQL = Get_Todo_Detail_Sql();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {

            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = str_SQL;

            sqlCmd.CommandType = CommandType.Text;

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        sgvList.DataSource = dt;
        sgvList.DataBind();

        btnBlackList.Visible = dt.Rows.Count > 0;
    }

    private void Bind_Email()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "buztbl_email");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                DDL_Send.DataSource = dt;
                DDL_Send.DataBind();
                DDL_Send.Items.Insert(0, new ListItem("--請選擇-- ", ""));
                DDL_CC.DataSource = dt;
                DDL_CC.DataBind();
                DDL_CC.Items.Insert(0, new ListItem("--請選擇-- ", ""));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {
        sgvList.Visible = true;
        this.sgvList.PageIndex = 0;
        Bind_Todo_Detail();
    }

    protected void btnBack_Click(object sender, EventArgs e)
    {
        Response.Redirect("TechCase_todo_search.aspx");
    }

    protected void btnSave_Click(object sender, EventArgs e)
    {
        string str_error = "";

        if (TB_name.Text == "")
        {
            str_error += "★標題 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#TB_name').validationEngine('showPrompt', '★標題 必須輸入','','',true); $('#TB_name').click(function () { $('#TB_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_name", script_alert);
        }
        if (TB_sdate.Text == "")
        {
            str_error += "★起始日期 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#TB_sdate').validationEngine('showPrompt', '★起始日期 必須輸入','','',true); $('#TB_sdate').click(function () { $('#TB_sdate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_sdate", script_alert);
        }

        if (TB_edate.Text != "")
        {
            if (int.Parse(TB_sdate.Text) > int.Parse(TB_edate.Text))
            {
                str_error += "★結束日期不可小餘起始日期\\n ";
                string script_alert = "<script language='javascript'> $('#TB_edate').validationEngine('showPrompt', '★結束日期不可小餘起始日期','','',true); $('#TB_edate').click(function () { $('#TB_edate').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "TB_edate", script_alert);
            }
        }

        if (RBL_repeat.SelectedValue == "")
        {
            str_error += "★通知頻率 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#RBL_repeat').validationEngine('showPrompt', '★通知頻率 必須輸入','','',true); $('#RBL_repeat').click(function () { $('#RBL_repeat').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "RBL_repeat", script_alert);
        }

        if (RBL_repeat.SelectedValue != "0")
        {
            if (rpt_Send.Items.Count == 0)
            {
                str_error += "★收件人 必須輸入\\n ";
                string script_alert = "<script language='javascript'> $('#DDL_Send').validationEngine('showPrompt', '★收件人 必須輸入','','',true); $('#DDL_Send').click(function () { $('#DDL_Send').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "DDL_Send", script_alert);
            }
        }

        if (RBL_repeat.SelectedValue == "2")
        {
            if ((CB_notify_week_1.Checked || CB_notify_week_2.Checked || CB_notify_week_3.Checked || CB_notify_week_4.Checked || CB_notify_week_5.Checked || CB_notify_week_6.Checked || CB_notify_week_7.Checked) == false)
            {
                str_error += "★重複週期 必須輸入\\n ";
                string script_alert = "<script language='javascript'> $('#CB_notify_week_1').validationEngine('showPrompt', '★重複週期 必須輸入','','',true); $('#CB_notify_week_1').click(function () { $('#CB_notify_week_1').validationEngine('hide'); })</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "CB_notify_week_1", script_alert);
            }
        }

        if (str_error != "")
        {
            string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ; </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
            return;
        }


        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "Add_todo");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_name", oRCM.SQLInjectionReplaceAll(TB_name.Text));
            sqlCmd.Parameters.AddWithValue("@t_sdate", oRCM.SQLInjectionReplaceAll(TB_sdate.Text));
            sqlCmd.Parameters.AddWithValue("@t_edate", oRCM.SQLInjectionReplaceAll(TB_edate.Text));
            sqlCmd.Parameters.AddWithValue("@t_說明", oRCM.SQLInjectionReplaceAll(TB_說明.Text));

            sqlCmd.CommandTimeout = 0;
            try
            {
                DataTable dt = new DataTable();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

                string t_id = dt.Rows[0][0].ToString();
                ViewState["t_id"] = t_id;
                Save_Notify();

                ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@"
        <script type='text/javascript'>
            alert('存檔成功');
            location.replace('./TechCase_todo_modify.aspx?t_id={0}');
        </script>
        ", t_id), false);
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }

    }

    protected void btnFileUpload_Click(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        string path = oRCM.GetValidPathPart(FilePathString, "tip");
        path = oRCM.GetValidPathPart(path, DateTime.Now.Year.ToString());

        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);

        if (FileUpload1.PostedFile.ContentLength > 0)
        {
            string upFileName = FileUpload1.FileName.Replace("&", "＆").Replace("­", "－").Replace("--", "－－").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("/", "").Replace("\\", "").Replace("'''", "’");

            string str_FileName = Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("--", "－－") +
                                    Path.GetExtension(upFileName);

            string filePath = Path.Combine(path, str_FileName);
            bool result = DangerousFileUpload(FileUpload1, filePath);

            if (result == false)
            {
                StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('檔案上傳錯誤');</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                return;
            }
            // FileUpload1.SaveAs(path.Replace("/", "").Replace("..", "").Replace("'''", "’") + oRCM.SQLInjectionReplaceAll(str_FileName));

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treaty_TechCase_todo";

                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@mode", "file_Add");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
                //sqlCmd.Parameters.AddWithValue("@tdf_xid", "");
                //sqlCmd.Parameters.AddWithValue("@tdf_type", "");
                //sqlCmd.Parameters.AddWithValue("@tdf_inspect", "");
                sqlCmd.Parameters.AddWithValue("@tdf_filename", oRCM.SQLInjectionReplaceAll(upFileName));
                //sqlCmd.Parameters.AddWithValue("@tdf_filetxt", "");
                //sqlCmd.Parameters.AddWithValue("@tdf_filetype", "");
                sqlCmd.Parameters.AddWithValue("@tdf_url", oRCM.SQLInjectionReplaceAll(filePath));
                //sqlCmd.Parameters.AddWithValue("@tdf_up_flag", "");

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();

                    StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('檔案上傳成功');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    Bind_File();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
        }

    }

    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tdf_url"].ToString().Trim();
                str_filename = dv[0]["tdf_filename"].ToString().Trim();
            }


            //File_Del(e.CommandArgument.ToString());


            Bind_File();
        }

        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {

                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }

    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");

            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return confirm('確定要刪除 ?');");
                //Label lb_tcdf_no = (Label)e.Row.FindControl("lbl_tcdf_no");
                //LinkButton lb_edit = (LinkButton)e.Row.FindControl("lnkbtn_Edit");
                //lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");      
            }
        }
    }

    protected void lnkbtn_Del_Click(object sender, EventArgs e)
    {
        #region --- modify ---
        string tdf_id = ((LinkButton)sender).CommandArgument;
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_todo";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "file_Del");
            sqlCmd.Parameters.AddWithValue("@tdf_id", oRCM.SQLInjectionReplaceAll(tdf_id));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void sgvList_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.sgvList.PageIndex = e.NewPageIndex;
        Bind_Todo_Detail();
    }

    protected void sgvList_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

        }
    }

    protected void rpt_AND_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.Footer)
        {
            TextBox TB_c_group = (TextBox)e.Item.FindControl("TB_c_group");
            DropDownList DDL_code = (DropDownList)e.Item.FindControl("DDL_code_AND");
            DropDownList DDL_condition = (DropDownList)e.Item.FindControl("DDL_condition_AND");
            DDL_code.Attributes["DDL_condition_ID"] = DDL_condition.ID;
            DDL_code.DataSource = _dtGroupCode;
            DDL_code.DataBind();
            DDL_code.Items.Insert(0, "");
            DDL_condition.DataSource = _dtGroupCondition;
            DDL_condition.DataBind();
            DDL_condition.Items.Insert(0, "");
        }
    }

    protected void rpt_OR_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            ImageButton ib_del = (ImageButton)e.Item.FindControl("IB_del");
            DropDownList DDL_code = (DropDownList)e.Item.FindControl("DDL_code");
            DropDownList DDL_condition = (DropDownList)e.Item.FindControl("DDL_condition");
            TextBox TB_condition_value = (TextBox)e.Item.FindControl("TB_condition_value");

            DDL_code.DataSource = _dtGroupCode;
            DDL_code.DataBind();
            DDL_code.Items.Insert(0, "");
            DDL_code.SelectedValue = DataBinder.Eval(e.Item.DataItem, "c_group_code").ToString();

            DDL_condition.DataSource = _dtGroupCondition;
            DDL_condition.DataBind();
            DDL_condition.Items.Insert(0, "");
            DDL_condition.SelectedValue = DataBinder.Eval(e.Item.DataItem, "c_group_condition").ToString();

            TB_condition_value.Text = DataBinder.Eval(e.Item.DataItem, "c_group_condition_value").ToString();

            if (ib_del != null)
                ib_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

        }

        if (e.Item.ItemType == ListItemType.Footer)
        {
            Repeater rpt_OR = ((Repeater)sender);

            TextBox TB_c_group = (TextBox)rpt_OR.Parent.FindControl("TB_c_group");
            DropDownList DDL_code = (DropDownList)e.Item.FindControl("DDL_code_OR");
            DropDownList DDL_condition = (DropDownList)e.Item.FindControl("DDL_condition_OR");
            DDL_code.Attributes["DDL_condition_ID"] = DDL_condition.ID;
            DDL_code.DataSource = _dtGroupCode;
            DDL_code.DataBind();
            DDL_code.Items.Insert(0, "");
            DDL_condition.DataSource = _dtGroupCondition;
            DDL_condition.DataBind();
            DDL_condition.Items.Insert(0, "");

            Button BT_Add_OR = (Button)e.Item.FindControl("BT_Add_OR");
            if (TB_c_group != null)
            {
                BT_Add_OR.Attributes["c_group"] = TB_c_group.Text;
            }
        }
    }

    protected void BT_Add_OR_Click(object sender, EventArgs e)
    {
        Button BT_Add_OR = (Button)sender;
        string c_group = BT_Add_OR.Attributes["c_group"];

        DropDownList DDL_code = (DropDownList)BT_Add_OR.Parent.FindControl("DDL_code_OR");
        DropDownList DDL_condition = (DropDownList)BT_Add_OR.Parent.FindControl("DDL_condition_OR");
        TextBox TB_condition_value = (TextBox)BT_Add_OR.Parent.FindControl("TB_condition_value");

        if (string.IsNullOrEmpty(DDL_code.SelectedValue) || string.IsNullOrEmpty(DDL_condition.SelectedValue) || string.IsNullOrEmpty(TB_condition_value.Text))
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('請輸入所有條件');
        </script>
        ", false);
            return;
        }

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "Add_care");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@c_group", oRCM.SQLInjectionReplaceAll(c_group));
            sqlCmd.Parameters.AddWithValue("@c_group_code", oRCM.SQLInjectionReplaceAll(DDL_code.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@c_group_condition", oRCM.SQLInjectionReplaceAll(DDL_condition.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@c_group_condition_value", oRCM.SQLInjectionReplaceAll(TB_condition_value.Text));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }

        Bind_Todo_Care();
    }

    protected void BT_Add_AND_Click(object sender, EventArgs e)
    {
        Button BT_Add_OR = (Button)sender;

        TextBox TB_c_group = (TextBox)BT_Add_OR.Parent.FindControl("TB_c_group");
        DropDownList DDL_code = (DropDownList)BT_Add_OR.Parent.FindControl("DDL_code_AND");
        DropDownList DDL_condition = (DropDownList)BT_Add_OR.Parent.FindControl("DDL_condition_AND");
        TextBox TB_condition_value = (TextBox)BT_Add_OR.Parent.FindControl("TB_condition_value");

        if (string.IsNullOrEmpty(TB_c_group.Text))
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'> 
            alert('請輸入代碼');
        </script>
        ", false);
            return;
        }

        var diff = _dtCare.AsEnumerable().Where(x => x.Field<string>("c_group") == TB_c_group.Text).ToList();
        if (diff.Count > 0)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('代碼重複');
        </script>
        ", false);
            return;
        }

        if (string.IsNullOrEmpty(TB_c_group.Text) || string.IsNullOrEmpty(DDL_code.SelectedValue) || string.IsNullOrEmpty(DDL_condition.SelectedValue) || string.IsNullOrEmpty(TB_condition_value.Text))
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('請輸入所有條件');
        </script>
        ", false);
            return;
        }

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "Add_care");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@c_group", oRCM.SQLInjectionReplaceAll(TB_c_group.Text));
            sqlCmd.Parameters.AddWithValue("@c_group_code", oRCM.SQLInjectionReplaceAll(DDL_code.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@c_group_condition", oRCM.SQLInjectionReplaceAll(DDL_condition.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@c_group_condition_value", oRCM.SQLInjectionReplaceAll(TB_condition_value.Text));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }

        Bind_Todo_Care();
    }

    protected void IB_del_OR_Click(object sender, ImageClickEventArgs e)
    {
        ImageButton IB_del = (ImageButton)sender;
        string c_id = IB_del.CommandArgument;

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "del_care");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@c_id", oRCM.SQLInjectionReplaceAll(c_id));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }

        Bind_Todo_Care();
    }

    protected void IB_mod_Click(object sender, EventArgs e)
    {
        ImageButton IB = (ImageButton)sender;
        string seno = IB.CommandArgument;
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            sqlConn.Open();
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandText = "esp_treaty_TechCase_todo";
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@mode", "Add_blacklist");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_id", oRCM.SQLInjectionReplaceAll(ViewState["t_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@t_blacklist", oRCM.SQLInjectionReplaceAll(seno));

            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, @"
        <script type='text/javascript'>
            alert('加入成功');
        </script>
        ", false);

        this.btnQuery_Click(btnQuery, EventArgs.Empty);
    }

    protected void DDL_code_SelectedIndexChanged(object sender, EventArgs e)
    {
        DropDownList DDL_code = (DropDownList)sender;
        string DDL_condition_ID = DDL_code.Attributes["DDL_condition_ID"];
        DropDownList DDL_condition = (DropDownList)DDL_code.Parent.FindControl(DDL_condition_ID);

        if (DDL_condition != null)
        {
            DDL_condition.DataSource = Get_Group_Condition(DDL_code.SelectedValue);
            DDL_condition.DataBind();
            DDL_condition.Items.Insert(0, "");
        }
    }

    protected void RBL_repeat_SelectedIndexChanged(object sender, EventArgs e)
    {
        RadioButtonList RBL = (RadioButtonList)sender;

        if (RBL.SelectedValue == "2")
        {
            CBL_week.Visible = true;
        }
        else
        {
            CBL_week.Visible = false;
            CB_notify_week_7.Checked = false;
            CB_notify_week_1.Checked = false;
            CB_notify_week_2.Checked = false;
            CB_notify_week_3.Checked = false;
            CB_notify_week_4.Checked = false;
            CB_notify_week_5.Checked = false;
            CB_notify_week_6.Checked = false;
        }
    }

    protected void IB_Add_Send_Click(object sender, ImageClickEventArgs e)
    {
        if (_dtSend.Where(x => x.Key == DDL_Send.SelectedItem.Value).Count() == 0)
        {
            if (DDL_Send.SelectedItem.Value != "")
            {
                _dtSend.Add(DDL_Send.SelectedItem.Value, DDL_Send.SelectedItem.Text);

                rpt_Send.DataSource = _dtSend;
                rpt_Send.DataBind();
            }
        }
    }

    protected void IB_del_Send_Click(object sender, ImageClickEventArgs e)
    {
        ImageButton IB_del_Send = (ImageButton)sender;

        _dtSend.Remove(IB_del_Send.CommandArgument);

        rpt_Send.DataSource = _dtSend;
        rpt_Send.DataBind();
    }

    protected void IB_Add_CC_Click(object sender, ImageClickEventArgs e)
    {
        if (_dtCC.Where(x => x.Key == DDL_CC.SelectedItem.Value).Count() == 0)
        {
            if (DDL_CC.SelectedItem.Value != "")
            {
                _dtCC.Add(DDL_CC.SelectedItem.Value, DDL_CC.SelectedItem.Text);

                rpt_CC.DataSource = _dtCC;
                rpt_CC.DataBind();
            }
        }
    }

    protected void IB_del_CC_Click(object sender, ImageClickEventArgs e)
    {
        ImageButton IB_del_CC = (ImageButton)sender;

        _dtCC.Remove(IB_del_CC.CommandArgument);

        rpt_CC.DataSource = _dtCC;
        rpt_CC.DataBind();
    }

    public bool DangerousFileUpload(FileUpload fileUpload, string destFile)
    {
        string fileName, fileExt, path;
        try
        {
            if (fileUpload != null && fileUpload.PostedFile.ContentLength > 0 &&
                Request.ContentType.Contains("multipart/form-data"))
            {
                //fileName = Path.GetFileName(fileUpload.FileName);
                //path = System.Web.HttpContext.Current.Server.MapPath("~/" + fileName);
                fileExt = Path.GetExtension(destFile);
                //com  exe   bat
                if (fileExt == ".com" || fileExt == ".exe" || fileExt == ".bat")
                {
                    return false;
                }

                // 複製檔案
                fileUpload.SaveAs(destFile);
                //File.Copy(path, destFile, true);
                //File.Delete(path);
                return true;
            }
        }
        catch (Exception e)
        {
            return false;
            //Handle Exceptions return false; } return false; } //View: @using (Html.BeginForm("DangerousFileUpload", "Cx", FormMethod.Post, new { enctype = "multipart/form-data" })) { @Html.AntiForgeryToken() }
        }
        return false;

    }
}