﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class AuthRight : System.Web.UI.Page
{
    RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            BindData_Auth_list();
        }
        //ClientScript.GetPostBackEventReference(new PostBackOptions(this.SDS_gv_file));
    }

    private void BindData_Auth_list()
    {
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_AuthRight_search";
        //SDS_gv_file.SelectParameters.Add("kw", TypeCode.String, TB_kw.Text.ToString().Trim());
        //for (int i = 0; i < this.SDS_gv_file.SelectParameters.Count; i++)
        //{
        //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_gv_file.DataBind();
        DataTable dt = new DataTable();

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString))
        {
            try
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"esp_AuthRight_search");
                oCmd.Parameters.AddWithValue("@kw", oRCM.SQLInjectionReplaceAll(TB_kw.Text.ToString().Trim()));
                oCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(TB_kw.Text.ToString().Trim()));
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.CommandTimeout = 0;

                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                oda.Fill(dt);
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
        gv_role_list.DataSource = dt;
        gv_role_list.DataBind();
    }

    protected void gv_role_list_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //this.SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_gv_file.DeleteCommand = "Delete rac_RoleOfUser_category  where rk_id=" + e.CommandArgument;
            //this.SDS_gv_file.Delete();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"Delete rac_RoleOfUser_category  where rk_id=@rk_id");
                oCmd.Parameters.AddWithValue("@rk_id", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.Text;
                oCmd.CommandTimeout = 0;
                try
                {
                    oCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString, Request, Response, ex);

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
            }
            BindData_Auth_list();
        }

    }
    protected void gv_role_list_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
            LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
        }
    }


    protected void BT_add_Click(object sender, EventArgs e)
    {
        if (txt_promoter_empno.Value != "")
        {
            //this.SDS_gv_file.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
            ////this.SDS_gv_file.InsertCommand = "insert rac_RoleOfUser_category(rk_Uid,rk_roleid,rk_category,rk_orglist) values( '" + txt_promoter_empno.Value + "','" + DDL_role.SelectedValue + "','" + DDL_category.SelectedValue + "','00')";
            //SDS_gv_file.InsertCommand = "esp_RoleOfUser_category_insert";
            //SDS_gv_file.InsertParameters.Add("rk_Uid", TypeCode.String, txt_promoter_empno.Value.Trim());
            //SDS_gv_file.InsertParameters.Add("rk_roleid", TypeCode.String, DDL_role.SelectedValue.Trim());
            //SDS_gv_file.InsertParameters.Add("rk_category", TypeCode.String, DDL_category.SelectedValue.Trim());
            //SDS_gv_file.InsertParameters.Add("rk_orglist", TypeCode.String, "00");
            //this.SDS_gv_file.Insert();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"esp_RoleOfUser_category_insert");
                oCmd.Parameters.AddWithValue("@rk_Uid", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value.Trim()));
                oCmd.Parameters.AddWithValue("@rk_roleid", oRCM.SQLInjectionReplaceAll(DDL_role.SelectedValue.Trim()));
                oCmd.Parameters.AddWithValue("@rk_category", oRCM.SQLInjectionReplaceAll(DDL_category.SelectedValue.Trim()));
                oCmd.Parameters.AddWithValue("@rk_orglist", "00");
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.CommandTimeout = 0;
                try
                {
                    oCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["infomgtConn"].ConnectionString, Request, Response, ex);

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
            }
            BindData_Auth_list();
            txt_promoter_empno.Value = "";
            txt_promoter_name.Text = "";
        }
        else
        {
            string script_alert = "<script language='javascript'>alert('新增時需要挑選人員資料!') ;</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }

    }
    protected void BT_search_Click(object sender, EventArgs e)
    {
        BindData_Auth_list();
    }
    protected void gv_role_list_Sorting(object sender, GridViewSortEventArgs e)
    {
        gv_role_list.PageIndex = 0;

        BindData_Auth_list();
    }
}