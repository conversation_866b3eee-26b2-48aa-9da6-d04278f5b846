﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myEngageGdp
	/// </summary>
	public class myEngageGdp : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		string _orgcd = string.Empty;		// 單位
		string _empno = string.Empty;		// 員工工號
		string _empname = string.Empty;		// 員工姓名

		#endregion

		#region 公有屬性

		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		#endregion

		public myEngageGdp() { }

		#region GDP查詢

		public DataSet GdpQuery(string year)
		{
			#region SQL
			string sql = "";
			if (year == "")
				sql = " order by gdp_year desc, code_order";
			else
				sql = " and (gdp.GDP_year = @GDP_year) order by gdp_year desc, code_order";
			
			string strSQL = string.Format(@"
			select gdp.GDP_year as gdp_year, 
				ct.code_valuedesc as gdp_country_name, 
				gdp.GDP as gdp, 
				(select cast(gdp.GDP/cast(GDP as decimal) as decimal(18, 2)) from engage_GDP where GDP_year = gdp.GDP_year and GDP_country = 'TW') as gdp_rate,
				gdp.GDP_country as gdp_country
			from engage_GDP gdp, visitdb..visit_codetbl ct
			where (code_type = '007') AND (code_enabled = 1) and (gdp.GDP_country = ct.code_value)" + sql
			);
			#endregion
			
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = strSQL;

			oCmd.Parameters.AddWithValue("@GDP_year", year);

			DataSet ds = this.getDataSet(oCmd, CommandType.Text);
			return ds;
		}

		public DataSet GdpQuery(string year, string country)
		{
			string sql = string.Format(@"
			select GDP_year, GDP_country, GDP, (select code_valuedesc from visitdb..visit_codetbl where code_type = '007' AND code_value = @GDP_country) as GDP_country_name
			from engage_GDP
			where (GDP_country = @GDP_country) and (GDP_year = @GDP_year)
			");

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@GDP_year", year);
			oCmd.Parameters.AddWithValue("@GDP_country", country);

			DataSet ds = this.getDataSet(oCmd, CommandType.Text);
			return ds;
		}

		#endregion	

		#region GDP新增

		public bool GdpInsert(string year, string country, string gdp)
		{
			#region SQL
			string sql = string.Format(@"
			if not exists(select * from engage_GDP where GDP_year = @GDP_year and GDP_country = @GDP_country)
			begin 
				insert into engage_GDP (GDP_year, GDP_country, GDP) values (@GDP_year, @GDP_country, @GDP)
                select 1
            end
            else
			begin
				select 0
			end
			");
			#endregion

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@GDP_year", year);
			oCmd.Parameters.AddWithValue("@GDP_country", country);
			oCmd.Parameters.AddWithValue("@GDP", gdp);

			bool success = false;
			try
			{
				string szRet = this.getTopOne(oCmd, CommandType.Text);
				success = szRet.Equals("1");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}

		#endregion

		#region GDP修改

		public bool GdpUpdate(string year, string country, string gdp)
		{
			#region SQL
			string sql = string.Format(@"
			update engage_GDP set GDP = @GDP where GDP_year = @GDP_year and GDP_country = @GDP_country
			");
			#endregion

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@GDP_year", year);
			oCmd.Parameters.AddWithValue("@GDP_country", country);
			oCmd.Parameters.AddWithValue("@GDP", gdp);

			bool success = false;
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}

		#endregion

		#region GDP刪除

		public bool GdpDelete(string year, string country)
		{
			#region SQL
			string sql = string.Format(@"
			delete from engage_GDP
			where GDP_year = @GDP_year and GDP_country = @GDP_country
			");
			#endregion

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@GDP_year", year);
			oCmd.Parameters.AddWithValue("@GDP_country", country);

			bool success = false;
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 複製新年度資料

		public bool GdpCopy(string year)
		{
			#region SQL
			string sql = string.Format(@"
			declare @GDP_year_max varchar(4) = '2014'
            select @GDP_year_max = max(GDP_year) from engage_GDP

			if not exists (select * from engage_GDP where GDP_year = @newYear)
			begin
				insert into engage_GDP
					select @newYear as GDP_year, GDP_country, GDP from engage_GDP where GDP_year = @GDP_year_max
                select 1
			end 
            else
			begin
				select 0 
			end
			");
			#endregion

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			oCmd.Parameters.AddWithValue("@newYear", year);

			bool success = false;
			try
			{
				string szRet = this.getTopOne(oCmd, CommandType.Text);
				success = szRet.Equals("1");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
				throw ex;
			}
			return success;
		}

		#endregion

		#region 取得國別

		public DataSet GetCountry()
		{
			#region SQL
			string sql = string.Format(@"
			select   rtrim(code_value) as code_value, rtrim(code_valuedesc) as code_valuedesc
			from     visitdb..visit_codetbl
			where    (code_type = '007') AND (code_enabled = 1)
			order by code_order desc, code_valuedesc asc
			");
			#endregion

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = sql;

			//oCmd.Parameters.AddWithValue("@GDP_year", year);

			DataSet ds = this.getDataSet(oCmd, CommandType.Text);
			return ds;
		}

        #endregion

        #region 取得最大年度

        public int GetMaxYear()
        {
            #region SQL
            string sql = string.Format(@"
			select isnull(max(GDP_year), '2014') from engage_GDP 
			");
            #endregion

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = sql;

            string szYear = this.getTopOne(oCmd, CommandType.Text);
            int nYear = 0;
            int.TryParse(szYear, out nYear);
            return nYear;
        }

        #endregion

    }

}