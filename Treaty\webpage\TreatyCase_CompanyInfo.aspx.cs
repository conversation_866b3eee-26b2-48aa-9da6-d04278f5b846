﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase_CompanyInfo : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }


    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {


            if (Request.QueryString["compno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["compno"]) || (Request.QueryString["compno"].Length == 0) || (Request.QueryString["compno"].Length > 12))
                    Response.Redirect("../danger.aspx");
                ViewState["compno"] = Request.QueryString["compno"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");
            //databinding("caseno", "ASC");
            ViewState["sortorder"] = "ASC";
            ViewState["sortField"] = "treatyno";
            databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }
    #region 判斷是否有點選案件權限[必須是：存在於Buztbl 或是該案件的承辦人就是登入者]
    private bool IsCanRead(string strSeno, string strClass)
    {
        //SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        //sso.GetEmpInfo();
        bool bIsPromoter = false;
        //TreatyFunctions_C oTF = new TreatyFunctions_C();
        //bIsPromoter = oTF.IsLoginEqualPromoter(strSeno, strClass, sso.empNo);
        bool bIsInBuztbl = false;
        //TreatyBase_C oTB = new TreatyBase_C();
        //bIsInBuztbl = oTB.IsLawMember(sso.empNo);
        return bIsPromoter || bIsInBuztbl;
    }
    #endregion


    protected void databinding(string str_sortField, string str_sort)
    {
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.SelectCommand = "esp_treaty_customers_case_query";

        //this.SDS_SC.SelectParameters.Add("tc_compidno",  ViewState["compno"].ToString());
        //this.SDS_SC.SelectParameters.Add("sort_condition",   " order by " + str_sortField + " " + str_sort);


        //for (int i = 0; i < this.SDS_SC.SelectParameters.Count; i++)
        //{
        //    SDS_SC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_SC.DataBind();
        //SGV_log.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_customers_case_query";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_compidno", oRCM.SQLInjectionReplaceAll(ViewState["compno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sort_condition", " order by " + oRCM.SQLInjectionReplaceAll(str_sortField) + " " + oRCM.SQLInjectionReplaceAll(str_sort));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_log.DataSource = dt;
                SGV_log.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_log_DataBound(object sender, EventArgs e)
    {

    }
    protected void SGV_log_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        //if (e.CommandName == "view_case")
        //{
        //    string script = "<script language='javascript'>viewCase(" + e.CommandArgument.ToString() + ");</script>";
        //    ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        //}
    }

    protected void SGV_log_PageIndexChanged(object sender, EventArgs e)
    {
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_log_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_log.PageIndex = e.NewPageIndex;
        SGV_log.DataBind();
    }
    protected void SGV_log_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType != DataControlRowType.Header)
        {
            foreach (TableCell tc in e.Row.Cells)
            {
                tc.Attributes["style"] = "border-color:white";
            }
        }
        Literal LB = (Literal)e.Row.FindControl("LT_contno");
        if (LB != null)
        {
            Label lbx = (Label)e.Row.FindControl("LB_seno");
            string sz_class = LB.Text.Substring(6, 1);
            switch (sz_class)
            {
                case "A":
                case "N":
                case "M":
                case "R":
                case "T":
                case "F":
                    LB.Text = "<a href='#' target='Infok' class='ajax_mesg_comp' onclick='viewCase(" + lbx.Text + ");return false;'>" + LB.Text.ToString() + "</a>";
                    break;
                case "Q":
                    LB.Text = "<a href='#' target='Infok' class='ajax_mesg_comp' onclick='viewCaseQ(" + lbx.Text + ");return false;'>" + LB.Text.ToString() + "</a>";
                    break;
            }
        }
    }
    protected void SGV_log_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_log.SortExpression)
                    {
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_log.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    protected void SDS_SC_Selecting(object sender, SqlDataSourceSelectingEventArgs e)
    {
        for (int i = 0; i < e.Command.Parameters.Count - 1; i++)
        {
            if (e.Command.Parameters[i].Value == null)
            {
                e.Command.Parameters[i].Value = "";
            }
        }
    }
    protected void SGV_log_Sorted(object sender, EventArgs e)
    {

    }
    protected void SGV_log_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        this.SGV_log.PageIndex = 0;
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

}