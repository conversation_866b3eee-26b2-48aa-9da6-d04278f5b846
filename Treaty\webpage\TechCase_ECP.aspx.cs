﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;
using System.IO;
using AMPS;


public partial class Treaty_webpage_TechCase_ECP : Treaty.common
{
    public List<SqlParameter> sqlParamList = new List<SqlParameter>();
    public string Ecp_guid
    {
        set { ViewState["Ecp_guid"] = value; }
        get
        {
            if (ViewState["Ecp_guid"] == null)
            {
                ViewState["Ecp_guid"] = Guid.NewGuid().ToString();
            }

            return ViewState["Ecp_guid"].ToString();
        }
    }
    public string Seno
    {
        set { ViewState["seno"] = value; }
        get
        {
            if (ViewState["seno"] == null)
            {
                string seno = Request.QueryString["seno"];
                if (string.IsNullOrEmpty(seno) || !IsInteger(seno))
                {
                    Response.Redirect("../danger.aspx");
                }
                ViewState["seno"] = oRCM.SQLInjectionReplaceAll(seno);
            }
            return ViewState["seno"].ToString();
        }
    }
    public string Ver
    {
        set { ViewState["ver"] = value; }
        get
        {
            if (ViewState["ver"] == null)
            {
                string ver = Request.QueryString["ver"];
                if (string.IsNullOrEmpty(ver) || !IsNumber(ver))
                {
                    Response.Redirect("../danger.aspx");
                }
                ViewState["ver"] = oRCM.SQLInjectionReplaceAll(ver);
            }
            return ViewState["ver"].ToString();

        }
    }


    /// <summary>
    /// 流程
    /// </summary>
    DataTable SignFlow
    {
        get { return (DataTable)ViewState["_SignFlow"]; }
        set { ViewState["_SignFlow"] = value; }
    }

    /// <summary>
    /// @ECP新增關卡
    /// </summary>
    public bool IsECPNewRight
    {
        get { return ViewState["_IsECPNewRight"] == null ? false : (bool)ViewState["_IsECPNewRight"]; }
        set { ViewState["_IsECPNewRight"] = value; }
    }
    int nRowCount = 0;

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }


    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            this.SignFlow = InitDataTable();
            BindSignFlow();
            BindData();

            //檔案上傳
            btn_FileUp.Attributes.Add("onclick", "tech_fileup(" + Seno + ");");
        }

        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData();
            SetFileOpt();
        }


    }

    private void BindData()
    {
        if (Html())
        {
            DataTable dt_reason = new DataTable();
            sqlParamList.Clear();

            sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("list_ECP_content")));
            dt_reason = get_SP();
            if (dt_reason.Rows.Count > 0)
            {
                DDL_reason.DataSource = dt_reason;
                DDL_reason.DataValueField = "Value";
                DDL_reason.DataTextField = "Text";
                DDL_reason.DataBind();
                DDL_reason.Items.Insert(0, new ListItem("請選擇", ""));
            }
            DataTable dt = new DataTable();

            sqlParamList.Clear();
            sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
            sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("pick_file")));
            dt = get_SP();
            if (dt.Rows.Count > 0)
            {
                gv_data.DataSource = dt;
                gv_data.DataBind();

            }
        }

    }

    protected void gvList_signflow_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        DataTable dt = this.SignFlow;
        LinkButton btn = (LinkButton)e.CommandSource;

        int order = int.Parse(btn.Attributes["Seq"]);
        string arguments = e.CommandArgument.ToString();
        string[] args = arguments.Split(';');
        switch (e.CommandName)
        {

            case "CMD_AddRow_First":
                SetTmpTable();
                AddEmptyRow(0);
                break;

            case "CMD_AddRow":
                SetTmpTable();
                AddEmptyRow(order);

                break;
            case "CMD_DelRow":
                if (!args[1].ToString().Equals("送簽人直屬主管"))
                {
                    //已是最後一筆時，必須新增空白列
                    if (this.gvList_signflow.Rows.Count == 1)
                    {
                        AddEmptyRow(0);
                    }
                    foreach (DataRow row in dt.Rows)
                    {
                        //if (row["rowsn"].ToString() == e.CommandArgument.ToString())
                        if (row["rowsn"].ToString() == args[0].ToString())
                        {
                            if (row["rowtype"].ToString() == "1")
                            {
                                //屬於後來新增的就直接砍掉
                                dt.Rows.Remove(row);
                                break;
                            }
                            else
                            {
                                row["rowtype"] = "4";
                            }
                        }
                    }
                    SetTmpTable();
                    foreach (DataRow row in dt.Rows)
                    {
                        //刪除列，將此順序後的資料，每個都-1
                        if (int.Parse(row["Seq"].ToString()) > order)
                        {
                            row["Seq"] = int.Parse(row["Seq"].ToString()) - 1;
                            //由「資料庫載入」更新為「異動」
                            if (row["rowtype"].ToString() == "2")
                            {
                                row["rowtype"] = "3";
                            }
                        }
                    }
                }
                else
                {
                    string script = "<script language='javascript'>window.alert('簽辦順序為 送簽人直屬主管 不可刪除!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "送出", script, false);
                }
                break;
        }
        DataView dv = dt.DefaultView;
        dv.RowFilter = "rowtype <> 4";
        dv.Sort = "Seq";
        nRowCount = dv.Count;
        this.gvList_signflow.DataSource = dv;
        this.gvList_signflow.DataBind();
    }
    protected void gvList_signflow_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //刪除
            LinkButton lbtnDel = (LinkButton)e.Row.FindControl("lbtnDel");
            if (DataBinder.Eval(e.Row.DataItem, "IS_LOCK").ToString() == "Y")
            {
                lbtnDel.Enabled = false;
                lbtnDel.ForeColor = System.Drawing.Color.Gray;
            }
            else
            {
                lbtnDel.OnClientClick = "return confirm('確認刪除?');";
            }

            //簽核人員
            TextBox txt_com_cname = (TextBox)e.Row.FindControl("txt_com_cname");
            txt_com_cname.Attributes["readonly"] = "readonly";

            TextBox txt_com_empno = (TextBox)e.Row.FindControl("txt_com_empno");
            txt_com_empno.Attributes["readonly"] = "readonly";
            string rowtype = DataBinder.Eval(e.Row.DataItem, "rowtype").ToString();
            //使用 pr_engage_flow 控制可挑選權限@ECP新增關卡
            if ((rowtype.Equals("2") || rowtype.Equals("3")) && !this.IsECPNewRight)
            {
                Panel pnl_Empno = (Panel)e.Row.FindControl("pnl_Empno");
                pnl_Empno.Visible = false;

                Label lbl_Empno1 = (Label)e.Row.FindControl("lbl_Empno1");
                lbl_Empno1.Text = string.Format("{0}({1})", DataBinder.Eval(e.Row.DataItem, "RecUserName"), DataBinder.Eval(e.Row.DataItem, "RecUserID"));
                lbl_Empno1.Visible = true;
            }

            //202411 add
            DropDownList ddl_ecprole = (e.Row.FindControl("ddl_ecprole") as DropDownList);
            ddl_ecprole.DataSource = getECPRole();
            ddl_ecprole.DataValueField = "Value";
            ddl_ecprole.DataTextField = "Text";
            ddl_ecprole.DataBind();

            string sActRoleName = DataBinder.Eval(e.Row.DataItem, "ActRoleName").ToString();
            ddl_ecprole.SelectedValue = sActRoleName;
        }
    }

    /// <summary>
    /// 簽辦角色改下拉選單
    /// </summary>
    /// <returns></returns>
    private DataTable getECPRole()
    {
        DataTable dt = new DataTable();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("list_ECP_role")));
        dt = get_SP();
        return dt;
    }


    protected void btnAddFirst_Click(object sender, EventArgs e)
    {
        SetTmpTable();
        AddEmptyRow(0);

        DataTable dt = this.SignFlow;
        DataView dv = dt.DefaultView;
        dv.RowFilter = "rowtype <> 4";
        dv.Sort = "Seq";
        nRowCount = dv.Count;
        this.gvList_signflow.DataSource = dv;
        this.gvList_signflow.DataBind();

    }

    protected void btnReGen_Click(object sender, EventArgs e)
    {

        this.SignFlow = InitDataTable();
        BindSignFlow();
    }

    public string GetSignClassName(object oValue)
    {
        if (oValue.ToString().Equals("0"))
            return "簽核";
        else if (oValue.ToString().Equals("1"))
            return "會簽";
        else
            return "";
    }

    private void SetTmpTable(string rowsn, string oldrowtype, string newrowtype)
    {
        foreach (DataRow dr in this.SignFlow.Rows)
        {
            if (dr["rowsn"].ToString() == rowsn && dr["rowtype"].ToString() == oldrowtype)
            {
                dr["rowtype"] = newrowtype;
                break;
            }
        }
    }

    private void AddEmptyRow(int order)
    {
        DataTable dt = this.SignFlow;

        //插入列，將此順序後的資料，每個都+1
        foreach (DataRow row in dt.Rows)
        {
            if (int.Parse(row["Seq"].ToString()) > order && row["rowtype"].ToString() != "4")
            {
                row["Seq"] = int.Parse(row["Seq"].ToString()) + 1;

                //由「資料庫載入」更新為「異動」
                if (row["rowtype"].ToString() == "2")
                {
                    row["rowtype"] = "3";
                }
            }
        }

        DataRow dr = dt.NewRow();
        dr["rowtype"] = "1";//1:新增        
        dr["Seq"] = order + 1;
        dr["ActRoleName"] = "被加簽人";
        dr["RecUserID"] = string.Empty;
        dr["RecUserName"] = string.Empty;
        dr["SignType"] = "1";   //依序簽核
        dr["SignSigle"] = "Y";  //單一簽核		
        dr["SignClass"] = "0";  //0:簽核, 1:會簽
        dr["IS_LOCK"] = "N";
        dr["NoticeEmpno"] = string.Empty;
        dr["NoticeName"] = string.Empty;

        dt.Rows.Add(dr);

        SignFlow = dt;
    }

    private DataTable InitDataTable()
    {
        DataTable dt = new DataTable();
        DataColumn identity = new DataColumn("rowsn", typeof(int));
        identity.AutoIncrement = true;
        identity.AutoIncrementSeed = 1;
        identity.AutoIncrementStep = 1;
        dt.Columns.Add(identity);
        dt.Columns.Add("rowtype");//1:新增;2:資料庫載入;3:異動;4:刪除;        
        dt.Columns.Add("Seq", typeof(int));
        dt.Columns.Add("ActRoleName");
        dt.Columns.Add("RecUserID");
        dt.Columns.Add("RecUserName");
        dt.Columns.Add("SignType");
        dt.Columns.Add("SignSigle");
        dt.Columns.Add("SignClass");
        dt.Columns.Add("IS_LOCK");
        dt.Columns.Add("NoticeEmpno");
        dt.Columns.Add("NoticeName");
        return dt;
    }

    private void SetTmpTable()
    {
        int nRowCount = this.gvList_signflow.Rows.Count;
        foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
        {
            HiddenField gv_hf_rowsn = (HiddenField)gvrow.FindControl("gv_hf_rowsn");
            foreach (DataRow dr in this.SignFlow.Rows)
            {
                TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
                TextBox txt_com_empno = (TextBox)gvrow.FindControl("txt_com_empno");
                HiddenField hf_SignClass = (HiddenField)gvrow.FindControl("hf_SignClass");
                //202411 add
                DropDownList ddl_ecprole = (DropDownList)gvrow.FindControl("ddl_ecprole");
                if (gv_hf_rowsn.Value == dr["rowsn"].ToString())
                {
                    dr["RecUserName"] = txt_com_cname.Text.Trim();
                    dr["RecUserID"] = txt_com_empno.Text.Trim();
                    dr["SignClass"] = hf_SignClass.Value;
                    //202411 add
                    dr["ActRoleName"] = ddl_ecprole.SelectedValue;

                    break;
                }
            }
        }
    }

    private void BindSignFlow()
    {

        DataTable dt = new DataTable();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
        sqlParamList.Add(new SqlParameter("版次", oRCM.SQLInjectionReplaceAll(Ver)));
        sqlParamList.Add(new SqlParameter("signGUID", oRCM.SQLInjectionReplaceAll(Ecp_guid)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("pre_flow")));
        dt = get_SP();

        if (dt.Rows[0][0].ToString().Trim() != "0")
        {
            ViewState["contno"] = "";//dt.Rows[0]["contno"].ToString().Trim();
            foreach (DataRow row in dt.Rows)
            {
                DataRow dr = this.SignFlow.NewRow();
                dr["rowtype"] = "2";//2:資料庫載入
                dr["Seq"] = row["Seq"].ToString();
                dr["ActRoleName"] = row["ActRoleName"].ToString();
                dr["RecUserID"] = row["RecUserID"].ToString();
                dr["RecUserName"] = row["RecUserName"].ToString();
                dr["SignType"] = row["SignType"].ToString();
                dr["SignSigle"] = row["SignSigle"].ToString();
                dr["SignClass"] = row["SignClass"].ToString();
                dr["IS_LOCK"] = row["IS_LOCK"].ToString();
                ViewState["聯絡資訊"] = "";//row["CC"].ToString();
                this.SignFlow.Rows.Add(dr);
            }

            DataView dv = this.SignFlow.DefaultView;
            dv.Sort = "Seq";
            nRowCount = dv.Count;
            this.gvList_signflow.DataSource = dv;
            this.gvList_signflow.DataBind();
            foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
            {
                TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
                ViewState["最後簽核人"] = txt_com_cname.Text;
            }
            LT_業管人員.Text = Server.HtmlEncode(ViewState["聯絡資訊"].ToString());

        }
        else
        {
            string script = string.Format(@"
<script language='javascript'>
alert('{0}');
parent.$.colorbox.close();
</script>", Server.HtmlEncode(dt.Rows[0]["msg"].ToString().Trim()));
            ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "送出", script, false);
        }

    }

    /// <summary>
    /// 顯示訊息
    /// </summary>
    /// <param name="msg"></param>
    public void Alert(string msg)//顯示訊息
    {
        ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");", msg), true);
    }

    /// <summary>
    /// 顯示訊息完後關閉 Colorbox 視窗
    /// </summary>
    /// <param name="msg">輸出的訊息</param>
    public void Alert_CloseColorbox(string msg)//顯示訊息完後關閉視窗
    {
        ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");parent.$.colorbox.close();", msg), true);

    }

    protected void DDL_reason_SelectedIndexChanged(object sender, EventArgs e)
    {
        txt_reason.Text = "";
        DataTable dt = new DataTable();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("@ECP_content", oRCM.SQLInjectionReplaceAll(DDL_reason.SelectedItem.Value)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("ECP_content")));
        dt = get_SP();

        if (dt.Rows.Count > 0)
        {
            txt_reason.Text = dt.Rows[0][0].ToString();
        }

    }

    protected void btnSign_Click(object sender, EventArgs e)
    {
        if (Html())
        {

            #region 欄位檢查
            string msg = string.Empty;
            string js = @"$('#_id_').validationEngine('showPrompt', '_msg_','','bottomLeft',true);$('#_id_').click(function () { $('#_id_').validationEngine('hide'); });";
            string str_簽核順序_Old = "0", str_簽核順序_New = "0", str_檢查 = "", str_簽核順序_max = "0";

            if (txt_reason.Text == "")
            {
                msg += js.Replace("_id_", txt_reason.ClientID).Replace("_msg_", "理由說明 需填寫");
            }

            bool isAnyRowSelected = false;//檔案
            bool isAnyFileSelected = false;//檢視檔案
            List<GridViewRow> rows = new List<GridViewRow>();
            // 第一次循環：檢查是否有選中的行和文件，並將行存儲在列表中
            foreach (GridViewRow gr in this.gv_data.Rows)
            {
                CheckBox cb_row = (CheckBox)gr.FindControl("cbAdd");
                CheckBox cbfile = (CheckBox)gr.FindControl("cbfile");

                if (cb_row.Checked)
                {
                    isAnyRowSelected = true;
                }

                if (cbfile.Checked)
                {
                    isAnyFileSelected = true;
                }

                rows.Add(gr);
            }
            // 第二次循環：根據檢查結果生成消息
            foreach (GridViewRow gr in rows)
            {
                CheckBox cb_row = (CheckBox)gr.FindControl("cbAdd");
                CheckBox cbfile = (CheckBox)gr.FindControl("cbfile");

                if (!isAnyRowSelected)
                {
                    msg += js.Replace("_id_", cb_row.ClientID).Replace("_msg_", "請至少選取一個");
                }
                if (!isAnyFileSelected)
                {
                    if (!isAnyRowSelected || cb_row.Checked)
                    {
                        msg += js.Replace("_id_", cbfile.ClientID).Replace("_msg_", "請選取檢視檔案");
                    }
                }
                if (!cb_row.Checked && cbfile.Checked)
                {
                    msg += js.Replace("_id_", cb_row.ClientID).Replace("_msg_", "檔案沒有被選取 不能作為後續之檢視檔案");
                }
            }

            foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
            {
                TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
                TextBox txt_com_empno = (TextBox)gvrow.FindControl("txt_com_empno");
                if (txt_com_cname != null && txt_com_empno != null && (string.IsNullOrWhiteSpace(txt_com_cname.Text) || string.IsNullOrWhiteSpace(txt_com_empno.Text)))
                {
                    msg += js.Replace("_id_", txt_com_cname.ClientID).Replace("_msg_", "簽核人員 欄位尚未填寫");
                }
                if (TB_pass_check.Text != "@880583@")
                {
                    if (txt_com_empno != null && txt_com_empno.Text != "")
                    {
                        DataTable dt = new DataTable();

                        sqlParamList.Clear();
                        sqlParamList.Add(new SqlParameter("check_empno", oRCM.SQLInjectionReplaceAll(txt_com_empno.Text)));
                        sqlParamList.Add(new SqlParameter("mode", oRCM.SQLInjectionReplaceAll("check_order")));
                        dt = get_SP();

                        DataView drv_簽核順序 = dt.DefaultView;
                        if (drv_簽核順序.Count >= 1)
                        {
                            str_簽核順序_New = drv_簽核順序[0][0].ToString();
                        }

                        if (Int32.Parse(str_簽核順序_New) >= Int32.Parse(str_簽核順序_Old))
                            str_簽核順序_Old = str_簽核順序_New;

                        if (Int32.Parse(str_簽核順序_max) <= Int32.Parse(str_簽核順序_Old))
                            str_簽核順序_max = str_簽核順序_Old;

                    }
                }
            }


            if (TB_pass_check.Text != "@880583@")
            {

                //if (Int16.Parse(str_簽核順序_New) < 7 || (str_簽核順序_max != str_簽核順序_New))
                //{
                //    msg += js.Replace("_id_", gvList_signflow.ClientID).Replace("_msg_", "最後一關需為一級主管(組長)以上，且為會簽人員最大者");
                //    str_檢查 = "X";
                //}

                if ((str_簽核順序_max != str_簽核順序_New))
                {
                    msg += js.Replace("_id_", gvList_signflow.ClientID).Replace("_msg_", "最後一關為會簽人員最大者");
                    str_檢查 = "X";
                }

            }

            if (msg != string.Empty)
            {
                ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", string.Format(@"$(document).ready(function () {{{0}}});", msg), true);
                return;
            }


            #endregion

            //檔案新增
            dofileList();

            #region 送簽待確認

            if (1 == 1)
            {
                //刷新GUID
                Ecp_guid = Guid.NewGuid().ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                try
                {
                    treaty_myEzflow_tech.treaty_myEzflow_tech dal_main = new treaty_myEzflow_tech.treaty_myEzflow_tech();
                    dal_main.Ecp_guid = Ecp_guid;
                    dal_main.Seno = Seno;
                    dal_main.EcpContent = txt_reason.Text;
                    dal_main.Ver = Ver;
                    dal_main.Insert_ecp_main();
                }
                catch
                {
                    Alert("未預期例外，儲存發生錯誤(新增流程主檔)");
                    return;
                }

                SetTmpTable();
                DataView dv = this.SignFlow.DefaultView;
                dv.Sort = "Seq";

                bool failed = false;
                //迴圈更新流程
                foreach (DataRowView row in dv)
                {
                    treaty_myEzflow_tech.treaty_myEzflow_tech dal_ins = new treaty_myEzflow_tech.treaty_myEzflow_tech();
                    dal_ins.Seno = Seno;
                    dal_ins.EmpNo = ssoUser.empNo;
                    dal_ins.EmpName = ssoUser.empName;
                    dal_ins.Ecp_guid = Ecp_guid;

                    dal_ins.Seq = int.Parse(row["Seq"].ToString());
                    dal_ins.ActRoleName = row["ActRoleName"].ToString();
                    dal_ins.RecUserID = row["RecUserID"].ToString();
                    dal_ins.RecUserName = row["RecUserName"].ToString();
                    dal_ins.SignType = row["SignType"].ToString();
                    dal_ins.SignSigle = row["SignSigle"].ToString();
                    dal_ins.SignClass = row["SignClass"].ToString();
                    dal_ins.IS_LOCK = row["IS_LOCK"].ToString();
                    dal_ins.Ver = Ver;
                    bool success = false;
                    if (row["rowtype"].ToString() != "4")
                        success = dal_ins.Insert_preflow();
                    else
                    {
                        success = true;
                    }

                    if (!success)
                    {
                        failed = true;
                        break;
                    }
                }

                if (failed)
                {
                    Alert("未預期例外，儲存發生錯誤(新增流程)");
                }
                else
                {
                    treaty_myEzflow_tech.treaty_myEzflow_tech dal = new treaty_myEzflow_tech.treaty_myEzflow_tech();

                    dal.Ecp_guid = Ecp_guid;
                    dal.EmpNo = ssoUser.empNo;
                    dal.EmpName = ssoUser.empName;

                    //送簽-電子表單
                    //bool success = dal.Send_CreateSheet();

                    bool success = true;
                    if (success)
                    {
                        //把正在 treaty_TechCase_signstatus 規劃的 -->更改狀態
                        string script = string.Format(@"alert('{0}'); window.opener=null;window.close();  parent.$('#h_ECP_success').val('Y');   parent.$.colorbox.close();", dal.ReturnMessage);
                        ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
                    }
                    else
                    {
                        treaty_myEzflow_tech.treaty_myEzflow_tech dal_fail = new treaty_myEzflow_tech.treaty_myEzflow_tech();
                        dal_fail.Seno = Seno;
                        dal_fail.Ecp_guid = Ecp_guid;
                        dal_fail.FormType = "TREATY02";
                        dal_fail.Do_Fail_Process();
                        dal_fail.Send_Fail_Mail();
                        Alert("送出簽核失敗,請重新發送!");
                    }
                }
            }
            #endregion

           
        }

    }
   
    public void dofileList()
    {
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("mode", oRCM.SQLInjectionReplaceAll("Clr_sign_file")));
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
        sqlParamList.Add(new SqlParameter("版次", oRCM.SQLInjectionReplaceAll(Ver)));

        do_Edit();//清光先前設定的檔案

        //再逐筆新增
        foreach (GridViewRow row in gv_data.Rows)
        {
            CheckBox chk = (CheckBox)row.FindControl("cbAdd");
            HiddenField HiddenField1 = (HiddenField)row.FindControl("HiddenField1");
            Label lab_type = (Label)row.FindControl("lab_type");
            CheckBox cbfile = (CheckBox)row.FindControl("cbfile");
            if (chk != null && chk.Checked)
            {
                sqlParamList.Clear();
                sqlParamList.Add(new SqlParameter("mode", oRCM.SQLInjectionReplaceAll("add_sign_file")));
                sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
                sqlParamList.Add(new SqlParameter("版次", oRCM.SQLInjectionReplaceAll(Ver)));
                sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(HiddenField1.Value)));
                sqlParamList.Add(new SqlParameter("檔案說明", oRCM.SQLInjectionReplaceAll(lab_type.Text.Trim())));
                sqlParamList.Add(new SqlParameter("signFile", oRCM.SQLInjectionReplaceAll(cbfile.Checked ? "1" : "0")));
                do_Edit();
            }
        }
    }

    protected void cbAdd_CheckedChanged(object sender, EventArgs e)
    {
        BindFileOpt();
    }

    /// <summary>
    /// 紀錄選取的檔案
    /// </summary>
    private void BindFileOpt()
    {
        var sOpt = new StringBuilder();
        foreach (GridViewRow gr in this.gv_data.Rows)
        {
            CheckBox cb_row = (CheckBox)gr.FindControl("cbAdd");
            CheckBox cbfile = (CheckBox)gr.FindControl("cbfile");
            HiddenField HiddenField1 = (HiddenField)gr.FindControl("HiddenField1");
            if (cb_row != null && cb_row.Checked && HiddenField1 != null)
            {
                sOpt.Append(HiddenField1.Value).Append(",");
            }

            if (cbfile != null && cbfile.Checked)
            {
                ViewState["_VSFileOpt"] = cbfile.ClientID;
            }
        }
        ViewState["_FileOpt"] = sOpt.ToString().TrimEnd(',');
    }

    /// <summary>
    /// 選取的檔案(新增檔案後還原選取)
    /// </summary>
    private void SetFileOpt()
    {
        string sOpt = ViewState["_FileOpt"] != null ? ViewState["_FileOpt"].ToString() : "";
        string[] arrOpt = sOpt.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        string sVSOpt = ViewState["_VSFileOpt"] != null ? ViewState["_VSFileOpt"].ToString() : "";
        foreach (GridViewRow gr in this.gv_data.Rows)
        {
            CheckBox cb_row = (CheckBox)gr.FindControl("cbAdd");
            CheckBox cbfile = (CheckBox)gr.FindControl("cbfile");
            HiddenField HiddenField1 = (HiddenField)gr.FindControl("HiddenField1");

            if (cb_row != null && HiddenField1 != null && arrOpt.Contains(HiddenField1.Value))
            {
                cb_row.Checked = true;
            }

            if (cbfile != null && cbfile.ClientID == sVSOpt)
            {
                cbfile.Checked = true;
            }

        }
    }

    protected void gv_data_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }

            string args = e.CommandArgument.ToString();

            if (!string.IsNullOrEmpty(args))
            {
                DownloadFile(args);
            }
            else
            {
                string script = string.Format("alert('檔案不存在！');");
                ScriptManager.RegisterStartupScript(this, this.GetType(), "downloadFile", script, true);

            }
        }
    }

    protected void ddl_order_SelectedIndexChanged(object sender, EventArgs e)
    {
        DropDownList ddl = (DropDownList)sender;
        string s_order = ddl.SelectedValue;
        GridViewRow row = (GridViewRow)ddl.NamingContainer;
        HiddenField hf = (HiddenField)row.FindControl("HiddenField1");

        if (IsDangerWord(s_order))
        {
            Response.Redirect("../danger.aspx");
            return;
        }

        if (hf != null && !string.IsNullOrEmpty(hf.Value))
        {
            try
            {
                sqlParamList.Clear();
                sqlParamList.Add(new SqlParameter("mode", oRCM.SQLInjectionReplaceAll("file_order")));
                sqlParamList.Add(new SqlParameter("tcdf_up_order", oRCM.SQLInjectionReplaceAll(s_order)));
                sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(Seno)));
                sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(hf.Value)));
                do_Edit();
            }
            catch (Exception ex)
            {
                Alert("更新失敗，請稍後再試。");
            }
        }
        else
        {
            // 處理 HiddenField 為空的情況
            Alert("無檔案No，請重試。");
        }
    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb = (LinkButton)e.Row.FindControl("LB_filename");
            if (lb != null)
            {
                ScriptManager scriptManager = ScriptManager.GetCurrent(this.Page);
                scriptManager.RegisterPostBackControl(lb);
            }

            DropDownList ddl = (DropDownList)e.Row.FindControl("ddl_order");
            if (ddl != null)
            {
                //ddl.Items.Add(new ListItem("請選擇", ""));
                for (int i = 1; i <= 30; i++)
                {
                    ddl.Items.Add(new ListItem(i.ToString(), i.ToString()));
                }

                //取檔跟更新的Table 不同 所以暫不取
                string s_order = DataBinder.Eval(e.Row.DataItem, "tcdf_up_order").ToString();
                if (!string.IsNullOrEmpty(s_order))
                {
                    int orderValue;
                    if (int.TryParse(s_order, out orderValue))
                    {
                        ddl.SelectedValue = s_order;
                    }
                }
            }
        }
    }

    private void DownloadFile(string tcdf_no)
    {
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tcdf_no", oRCM.SQLInjectionReplaceAll(tcdf_no)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("file_view")));
        DataTable dt = new DataTable();
        dt = get_SP_File();
        if (dt.Rows.Count > 0)
        {
            string filePath = dt.Rows[0]["tcdf_url"].ToString().Trim();
            if (System.IO.File.Exists(filePath))
            {
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(dt.Rows[0]["tcdf_doc"].ToString().Trim(), Encoding.UTF8));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(filePath)));
                Response.Flush();
                Response.End();
            }
            else
            {
                Response.End();
            }
        }
    }

    public DataTable get_SP_File()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_treaty_TechCase_modify";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));

        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;

    }

    public DataTable get_SP()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }

    public void do_Edit()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();

        SqlCommand oCmd = new SqlCommand();
        //動態設定SqlParameters
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());
        runScalar(oCmd);
    }

    public bool Html()
    {
        string errUrl = "../danger.aspx";
        #region 特殊字元判斷-有MasterPage
        foreach (Control ctrl in Page.Form.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is DropDownList)
                {
                    DropDownList objTextBox = (DropDownList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is RadioButtonList)
                {
                    RadioButtonList objTextBox = (RadioButtonList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is TextBox)
                {
                    TextBox objTextBox = (TextBox)c;
                    if ((Base64.danger_word(objTextBox.Text)) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }
            }
        }
        #endregion

        return true;
    }


}