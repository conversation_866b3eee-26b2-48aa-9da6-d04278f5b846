﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_FileUp.aspx.cs" Inherits="Treaty_webpage_TechCase_FileUp" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>檔案上傳</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function TransferData(strValue) {
            var index = 0;
            var compare = strValue;
            index = compare.indexOf("\\");
            while (index > 1) {
                compare = compare.substr(index + 1, compare.length - index - 1);
                index = compare.indexOf("\\");
            }
            $("#txt_doc").val(compare);
        }

        function close_win() {
            alert("上傳成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 15px">
                <tr>
                    <td class="td_right">文件類型：</td>
                    <td>
                        <asp:DropDownList ID="DDL_FileType" runat="server" DataTextField="Text" DataValueField="Value"></asp:DropDownList>&nbsp;&nbsp;
                    <asp:CheckBox ID="CB_inspect" runat="server" Text="單位承辦人可看到此上傳之附件" Checked="True" />
                    </td>
                </tr>
                <tr>
                    <td class="td_right">上傳檔案：</td>
                    <td>
                        <asp:FileUpload ID="FU_up" runat="server" onpropertychange="TransferData(this.value);" Width="546px" class="genbtnS" /></td>
                </tr>
                <%--說明目前用不到--%>
                <tr style="display: none">
                    <td class="td_right">說明：</td>
                    <td>
                        <asp:TextBox ID="txt_filetxt" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox></td>
                </tr>
                <tr>
                    <td colspan="2">

                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="上傳" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>

            </table>
        </span>
    </form>
</body>
</html>
