<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GPICreate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="pGUID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GPICreateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GPICreateResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="GPICreateSoapIn">
    <wsdl:part name="parameters" element="tns:GPICreate" />
  </wsdl:message>
  <wsdl:message name="GPICreateSoapOut">
    <wsdl:part name="parameters" element="tns:GPICreateResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GPICreateHttpPostIn">
    <wsdl:part name="pGUID" type="s:string" />
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GPICreateHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="GPI01Soap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GPICreate">
      <wsdl:input message="tns:GPICreateSoapIn" />
      <wsdl:output message="tns:GPICreateSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="GPI01HttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GPICreate">
      <wsdl:input message="tns:GPICreateHttpPostIn" />
      <wsdl:output message="tns:GPICreateHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="GPI01Soap" type="tns:GPI01Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GPICreate">
      <soap:operation soapAction="http://tempuri.org/GPICreate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="GPI01Soap12" type="tns:GPI01Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GPICreate">
      <soap12:operation soapAction="http://tempuri.org/GPICreate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="GPI01HttpPost" type="tns:GPI01HttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GPICreate">
      <http:operation location="/GPICreate" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="GPI01">
    <wsdl:port name="GPI01Soap" binding="tns:GPI01Soap">
      <soap:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/GPI/GPI01.asmx" />
    </wsdl:port>
    <wsdl:port name="GPI01Soap12" binding="tns:GPI01Soap12">
      <soap12:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/GPI/GPI01.asmx" />
    </wsdl:port>
    <wsdl:port name="GPI01HttpPost" binding="tns:GPI01HttpPost">
      <http:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/GPI/GPI01.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>