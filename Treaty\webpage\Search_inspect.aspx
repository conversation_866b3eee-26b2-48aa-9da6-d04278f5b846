﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Search_inspect.aspx.cs" Inherits="Search_inspect" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }

        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        $(function () { $('a.iterm_dymanic').cluetip({ width: '830px', showTitle: false, ajaxCache: false }); });
        $(function () { $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false }); });
        $(function () { $('a.iterm_dymanic_company').cluetip({ width: '830px', activation: 'click', sticky: true, closePosition: 'title', arrows: true, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' }); });
        $(function () { $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false }); });

        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

                }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assignInspect.aspx?seno=" + seno
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function ipbshare_check(object) {//智權歸屬共用裡的本院、客戶自動比率調整
            chk_int(object);
            if (object.value > 100 || object.value < 0) {
                alert('所輸入的數字格式必須大於等於0，且小於等於100');
                object.value = '';
                return false;
            }
            var total = 100;
            if (object.id == 'txt_ipbi_percent') {//使用者觸發了本院
                $('#txt_ipbc_percent').val(total - $('#txt_ipbi_percent').val());
            }
            else {//使用者觸發了客戶
                $('#txt_ipbi_percent').val(total - $('#txt_ipbc_percent').val());
            }
            $('#rb_ipb_coparcenary').prop('checked', true);//自動將智權歸屬的共有選項勾選
            $('#txt_ipb_other_desc').val("");//智權歸屬-其他值清空
        }
        function ipbOther() {//如果有變更智權歸屬裡的其他描述，則自動勾選其他
            $('#rb_ipb_other').prop('checked', true);
            $('#txt_ipbi_percent').val("");//智權歸屬-共有-本院的值清空
            $('#txt_ipbc_percent').val("");//智權歸屬-共有-客戶的值清空
        }
        function duty_plan() {//如果有變更責任範圍裡的計畫經費，則自動勾選計畫經費
            $('#rb_duty_plain').attr('checked', true);
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_capital() {//如果有變更責任範圍裡的最高賠償金額，則自動勾選自動賠償金額
            $('#rb_duty_capital').attr('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_other() {//如果有變更責任範圍裡的其他，則自動勾選勾選其他
            $('#rb_duty_other').prop('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
        }
        function treaty_fileup(contno, seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileUp.aspx?contno=" + contno + "&seno=" + seno
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }

        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileModify.aspx?fid=" + fid
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }

        function treaty_defert(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Defer.aspx?seno=" + seno
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Defer_renew", 0);
                }
            });
        }

        function DeleteCase() {
            alert("案件已刪除!");
            location.replace("./TreatyApply.aspx");
        }
        function SendInspect(seno) {
            alert("案件已送審!");
            location.replace("./TreatyCase_view.aspx?seno=" + seno);
        }
        function EndCase(seno) {
            alert("案件已發結案通知!\n,若需填問卷,請按問卷填報 ");
            location.replace("./TreatyCase_view.aspx?seno=" + seno);
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light"></div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">單位別：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="orgcd_name" DataValueField="orgcd"></asp:DropDownList>
                                            <%-- <asp:SqlDataSource ID="SDS_Orgcd" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align="right">契約性質：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True">
                                                <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                            </asp:DropDownList><%--<asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align="right">客戶名稱：</td>
                                        <td>
                                            <asp:TextBox ID="tbxCompName" runat="server" Width="150px"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">承辦法務人員：</td>
                                        <td>
                                            <asp:TextBox ID="tbxHandleName" runat="server" Width="150px"></asp:TextBox></td>
                                        <td align="right">單位承辦人員：</td>
                                        <td>
                                            <asp:TextBox ID="tbxPromoterName" runat="server" Width="150px"></asp:TextBox></td>
                                        <td align="right">單位執行部門：</td>
                                        <td>
                                            <asp:TextBox ID="tbxReqDept" runat="server" Width="150px"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">案源：</td>
                                        <td colspan="5">
                                            <asp:CheckBoxList ID="cbxCaseClass" runat="server" Width="600px" RepeatDirection="Horizontal" BorderWidth="0px">
                                                <asp:ListItem Value="A">新創事業</asp:ListItem>
                                                <asp:ListItem Value="N">洽案系統</asp:ListItem>
                                                <asp:ListItem Value="R">標案系統</asp:ListItem>
                                                <asp:ListItem Value="M">NDA</asp:ListItem>
                                                <asp:ListItem Value="A">無收入</asp:ListItem>
                                                <asp:ListItem Value="U">國外契約</asp:ListItem>
                                                <asp:ListItem Value="C">工服</asp:ListItem>
                                                <asp:ListItem Value="S">新創事業</asp:ListItem>
                                                <asp:ListItem Value="T">其它</asp:ListItem>
                                            </asp:CheckBoxList></td>
                                    </tr>
                                    <tr>
                                        <td align="right">狀態：</td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="cbxCaseStatus1" runat="server" Text="未結件" Enabled="False"></asp:CheckBox>&nbsp;&nbsp;&nbsp;
						<asp:CheckBox ID="cbxCaseStatus2" runat="server" Text="結件" Enabled="False"></asp:CheckBox>&nbsp;&nbsp;&nbsp;
						<asp:CheckBox ID="cbxCaseStatus3" runat="server" Text="需求取消" Enabled="False"></asp:CheckBox>&nbsp;&nbsp;&nbsp;
                                        </td>
                                        <td colspan="2" align="right">
                                            <asp:Button ID="btnQuery" runat="server" Text="查詢" CssClass="button" OnClick="btnQuery_Click"></asp:Button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="6">
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                                        <FooterStyle BackColor="White" />
                                                        <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                                                        <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                                                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                        <Columns>
                                                            <asp:BoundField DataField="org_name" SortExpression="org_name" HeaderText="單位"></asp:BoundField>
                                                            <asp:TemplateField HeaderText="標記">
                                                                <HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" VerticalAlign="Middle" Width="40px"></ItemStyle>
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_defer" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_defer").ToString())) %>'></asp:Label><br />
                                                                    <asp:Label ID="LB_status_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_status_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="洽案／契約名稱" SortExpression="tmp_case_name">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tc_seno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_actno").ToString())) %>'></asp:LinkButton><br />
                                                                    <asp:Label ID="LB_case_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="400px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tc_compname" SortExpression="tc_compname" HeaderText="客戶名稱">
                                                                <ItemStyle Width="300px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tc_petition_day" SortExpression="tc_petition_day" HeaderText="需求日期">
                                                                <HeaderStyle Width="100px" />
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="預估完成日&lt;br&gt;結件日期">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_prefinish_date" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_prefinish_date").ToString())) %>'></asp:Label><hr />
                                                                    <asp:Label ID="LB_case_closedate" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_case_closedate").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="100px" />
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="單位&lt;br&gt;承辦人" SortExpression="tc_promoter_name">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_promoter_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_promoter_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="80px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="法務&lt;br&gt;承辦人員" SortExpression="tc_handle_name">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_handle_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_handle_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="80px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>

                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    </cc1:SmartGridView>
                                                    <%--                                                    <asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                </span>
                                        </td>
                                    </tr>
                                </table>
                        </div>
                        <!-- tabsubmenublock -->


                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc1:Foot runat="server" ID="Foot" />

        <%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        <script type="text/javascript">


            $(document).ready(function () {
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                $(".inputhint").tooltip({
                    position: { my: "left+10 bottom+40", at: "left bottom " },
                    tooltipClass: "custom-tooltip-styling",
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });




                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });

            });

            $(document).ready(function () {
                $(".accordionblock").hide();
                //個別按鈕操作
                $(".itemcontrolbtn").click(function () {
                    //切換子項顯示與隱藏
                    $(this).parent().parent().next(".accordionblock").slideToggle();
                    //ICON樣式切換
                    $(this).toggleClass("iconup");
                    //文字切換  ?:運算式是if else的快捷方式
                    //$(this).text($(this).text() == '展開項目' ? '收合項目' : '展開項目');
                });
                //全部展開
                $(".AllControlOpen").click(function () {
                    $(".accordionblock").slideDown();
                    //$(".itemcontrolbtn").text('收合項目');
                    $(".itemcontrolbtn").addClass("iconup")
                });
                //全部收合
                $(".AllControlClose").click(function () {
                    $(".accordionblock").slideUp();
                    //$(".itemcontrolbtn").text('展開項目');
                    $(".itemcontrolbtn").removeClass("iconup")
                });
            });
        </script>

    </form>
</body>
</html>
