﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;

public partial class TreatyCase2_Evidence : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            if (Request["seno"] != null)
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
                BindEvidence();
                ViewState["sub_seno"] = "0";
                if (Request["sub_seno"] != null)
                {
                    int sj = 0;
                    if (!(int.TryParse(Request["sub_seno"], out sj)))
                        Response.Redirect("../danger.aspx");
                    ViewState["sub_seno"] = Request["sub_seno"];
                    //SDS_SC.SelectParameters.Clear();
                    //SDS_SC.SelectCommand = " select tcie_Evidence,tcie_name from treaty_case2_infringement_evidence where tcie_sub_seno = @sn ";
                    //SDS_SC.SelectParameters.Add("sn", TypeCode.String, ViewState["sub_seno"].ToString());
                    //SDS_SC.DataBind();
                    //System.Data.DataView dv = (DataView)SDS_SC.Select(new DataSourceSelectArguments());

                    #region --- query ---
                    DataTable dt = new DataTable();
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @" select tcie_Evidence,tcie_name from treaty_case2_infringement_evidence where tcie_sub_seno = @sn ";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));


                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                            sqlDA.Fill(dt);

                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv = dt.DefaultView;
                    if (dv.Count >= 1)
                    {
                        DDL_Evidence.SelectedValue = Server.HtmlEncode(dv[0]["tcie_Evidence"].ToString());
                        TB_EvidenceName.Text = Server.HtmlEncode(dv[0]["tcie_name"].ToString());
                    }
                }
            }
            //ViewState["seno"] = 47;
            if (ViewState["seno"] == null)
                Response.Redirect("../NoAuthRight.aspx");
        }
    }
    private void BindEvidence()
    {
        //SDS_Evidence.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'19' ";
        //SDS_Evidence.DataBind();
        //DDL_Evidence.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", "");
            sqlCmd.Parameters.AddWithValue("@code_type", "19");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_Evidence.DataSource = dt;
                DDL_Evidence.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (TB_EvidenceName.Text.ToUpper().IndexOf("SCRIPT") >= 0) Response.Redirect("../danger.aspx");

        if (ViewState["sub_seno"].ToString() != "0")
        {

            //SDS_Evidence.UpdateParameters.Clear();
            //SDS_Evidence.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_Evidence.UpdateCommand = "esp_TreatyCase2_Evidence_modify";
            //SDS_Evidence.UpdateParameters.Add("subseno", ViewState["sub_seno"].ToString());
            //SDS_Evidence.UpdateParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_Evidence.UpdateParameters.Add("Evidence", DDL_Evidence.SelectedValue);
            //SDS_Evidence.UpdateParameters.Add("Ename", TB_EvidenceName.Text);
            //SDS_Evidence.UpdateParameters.Add("mode", "Update");
            //SDS_Evidence.Update();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_Evidence_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@subseno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@Evidence", DDL_Evidence.SelectedValue);
                sqlCmd.Parameters.AddWithValue("@Ename", oRCM.SQLInjectionReplaceAll(TB_EvidenceName.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@mode", "Update");


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

        }
        else
        {
            //SDS_Evidence.InsertParameters.Clear();
            //SDS_Evidence.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_Evidence.InsertCommand = "esp_TreatyCase2_Evidence_modify";
            //SDS_Evidence.InsertParameters.Add("subseno", "0");
            //SDS_Evidence.InsertParameters.Add("seno", ViewState["seno"].ToString());
            //SDS_Evidence.InsertParameters.Add("Evidence", DDL_Evidence.SelectedValue);
            //SDS_Evidence.InsertParameters.Add("Ename", TB_EvidenceName.Text);
            //SDS_Evidence.InsertParameters.Add("mode", "Insert");
            //SDS_Evidence.Insert();

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_Evidence_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@subseno", "0");
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@Evidence", DDL_Evidence.SelectedValue);
                sqlCmd.Parameters.AddWithValue("@Ename", oRCM.SQLInjectionReplaceAll(TB_EvidenceName.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@mode", "Insert");


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
        }

        string script = "<script language='javascript'>close_win();</script>";
        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
    }

}