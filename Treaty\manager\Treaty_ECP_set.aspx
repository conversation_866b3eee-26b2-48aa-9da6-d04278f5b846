﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Treaty_ECP_set.aspx.cs" Inherits="Treaty_ECP_set" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=11.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }

        function Find_Empno(obj, arg_sw) {
            $(".ajax_empno").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback2);
                    }
                }
            });

            $(".ajax_kw").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });

        }
        function Find_empno_kw(obj, arg_sw) {
            var strURL = "../../comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val());
            if (arg_sw == "2") {
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    $('#txt_send_empno').val("");
                    $('#txt_send_name').val("");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#txt_send_empno').val("");
                    $('#txt_send_name').val("");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#txt_send_name').val())
                        , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                        , title: '單人挑選'
                        , onClosed: function () {
                            $('#txt_send_empno').val("");
                            $('#txt_send_name').val("");
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    });

                    break;
                default:
                    $('#txt_send_empno').val(data.c_com_empno);
                    $('#txt_send_name').val(data.c_com_cname);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    $('#txt_sign_empno').val("");
                    $('#txt_sign_name').val("");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#txt_sign_empno').val("");
                    $('#txt_sign_name').val("");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#txt_sign_name').val())
                        , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                        , title: '單人挑選'
                        , onClosed: function () {
                            $('#txt_sign_empno').val("");
                            $('#txt_sign_name').val("");
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    });

                    break;
                default:
                    $('#txt_sign_empno').val(data.c_com_empno);
                    $('#txt_sign_name').val(data.c_com_cname);
            }
        }
     <%--   $(function () {
            $("#<%=chkSelectAll.ClientID %>").click(function () {
                $("#<%=ddlOrgcd.ClientID %> input[type=checkbox]").attr("checked", $("#<%=chkSelectAll.ClientID %>").is(":checked"));
            });
        });--%>

</script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​ .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <div class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="td_right" width="15%">類別：
                                        </td>
                                        <td>
                                            <asp:RadioButtonList ID="rbl_TYPE" runat="server" AutoPostBack="true" OnSelectedIndexChanged="rbl_TYPE_SelectedIndexChanged" RepeatDirection="Horizontal" RepeatLayout="Flow">
                                                <asp:ListItem Value="1" Selected="True">單位</asp:ListItem>
                                                <asp:ListItem Value="2">人員</asp:ListItem>
                                                <asp:ListItem Value="3">簽辦單位處理情形通知對象</asp:ListItem>
                                                <asp:ListItem Value="4">舊案件停接</asp:ListItem>
                                            </asp:RadioButtonList>
                                        </td>
                                    </tr>
                                    <tr id="tr_ECP_org" runat="server">
                                        <td class="td_right" runat="server">送簽單位：                                           
                                        </td>
                                        <td>
                                            <asp:UpdatePanel UpdateMode="Conditional" runat="server">
                                                <ContentTemplate>
                                                    <asp:DropDownList ID="DDL_orglist" DataTextField="Text" DataValueField="Value" AutoPostBack="true" OnSelectedIndexChanged="DDL_orglist_SelectedIndexChanged" runat="server" />
                                                    <asp:ListBox ID="LBX_deptlist1" runat="server" DataTextField="Text" DataValueField="Value" AutoPostBack="true" OnSelectedIndexChanged="LBX_deptlist1_SelectedIndexChanged" Width="200" SelectionMode="Multiple" />
                                                    <asp:ListBox ID="LBX_deptlist2" runat="server" DataTextField="Text" DataValueField="Value" Width="200" SelectionMode="Multiple" />
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                        </td>
                                    </tr>
                                    <tr id="tr_ECP_send" runat="server" visible="false">
                                        <td class="td_right" runat="server">送簽人員：
                                        </td>
                                        <td>
                                            <div style="float: left">
                                                <asp:TextBox ID="txt_send_name" runat="server" Width="65px" />
                                                <div style="display: none">
                                                    <asp:TextBox ID="txt_send_empno" runat="server" Width="65px" />
                                                </div>
                                                <a onclick="javascript:Find_Empno('txt_send_name','1');">
                                                    <asp:Image ID="Image2" runat="server" ImageUrl="../images/icon_search.gif" BorderWidth="0" class="ajax_empno btn_mouseout" /></a><br />
                                            </div>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="td_right">簽核人員：</td>
                                        <td class="td_right">
                                            <div style="float: left">
                                                <asp:TextBox ID="txt_sign_name" runat="server" Width="65px" />
                                                <div style="display: none">
                                                    <asp:TextBox ID="txt_sign_empno" runat="server" Width="65px" />
                                                </div>
                                                <a onclick="javascript:Find_Empno('txt_sign_name','2');">
                                                    <asp:Image ID="Image3" runat="server" ImageUrl="../images/icon_search.gif" BorderWidth="0" class="ajax_empno btn_mouseout" /></a><br />
                                            </div>
                                            <asp:Button ID="btnAdd" TabIndex="1" runat="server" Text="新增" class="genbtn" OnClick="btnAdd_Click"></asp:Button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="6">
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated" Width="100%">
                                                        <FooterStyle BackColor="White" />
                                                        <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                                        <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                                        <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                        <Columns>
                                                            <asp:BoundField HeaderText="類別" DataField="ECP_TYPE_name" ItemStyle-HorizontalAlign="Center" />
                                                            <asp:TemplateField HeaderText="送簽單位" SortExpression="ECP_org">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_單位" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_org").ToString())) %>'></asp:Label>
                                                                    <asp:Label ID="lb_org_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_org_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <%--<ItemStyle HorizontalAlign="Center" />--%>
                                                            </asp:TemplateField>

                                                            <asp:TemplateField HeaderText="送簽人員" SortExpression="ECP_send">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_send").ToString())) %>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_send_name").ToString())) %>
                                                                </ItemTemplate>
                                                                <%--<ItemStyle HorizontalAlign="Center" />--%>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="簽核人員" SortExpression="ECP_sign">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_sign").ToString())) %>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("ECP_sign_name").ToString())) %>
                                                                </ItemTemplate>
                                                                <%--<ItemStyle HorizontalAlign="Center" />--%>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="排序">
                                                                <ItemTemplate>
                                                                    <asp:DropDownList ID="DDL_Hec" runat="server" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate" Visible="false">
                                                                        <asp:ListItem Value="1">1</asp:ListItem>
                                                                        <asp:ListItem Value="2">2</asp:ListItem>
                                                                        <asp:ListItem Value="3">3</asp:ListItem>
                                                                        <asp:ListItem Value="4">4</asp:ListItem>
                                                                        <asp:ListItem Value="5">5</asp:ListItem>
                                                                        <asp:ListItem Value="6">6</asp:ListItem>
                                                                        <asp:ListItem Value="7">7</asp:ListItem>
                                                                        <asp:ListItem Value="8">8</asp:ListItem>
                                                                        <asp:ListItem Value="9">9</asp:ListItem>
                                                                        <asp:ListItem Value="10">10</asp:ListItem>
                                                                        <asp:ListItem Value="11">11</asp:ListItem>
                                                                        <asp:ListItem Value="12">12</asp:ListItem>
                                                                        <asp:ListItem Value="13">13</asp:ListItem>
                                                                        <asp:ListItem Value="14">14</asp:ListItem>
                                                                        <asp:ListItem Value="15">15</asp:ListItem>
                                                                        <asp:ListItem Value="16">16</asp:ListItem>
                                                                        <asp:ListItem Value="17">17</asp:ListItem>
                                                                        <asp:ListItem Value="18">18</asp:ListItem>
                                                                        <asp:ListItem Value="19">19</asp:ListItem>
                                                                        <asp:ListItem Value="20">20</asp:ListItem>
                                                                        <asp:ListItem Value="21">21</asp:ListItem>
                                                                        <asp:ListItem Value="22">22</asp:ListItem>
                                                                        <asp:ListItem Value="23">23</asp:ListItem>
                                                                        <asp:ListItem Value="24">24</asp:ListItem>
                                                                        <asp:ListItem Value="25">25</asp:ListItem>
                                                                        <asp:ListItem Value="26">26</asp:ListItem>
                                                                        <asp:ListItem Value="27">27</asp:ListItem>
                                                                        <asp:ListItem Value="28">28</asp:ListItem>
                                                                        <asp:ListItem Value="29">29</asp:ListItem>
                                                                        <asp:ListItem Value="30">30</asp:ListItem>
                                                                        <asp:ListItem Value="31">31</asp:ListItem>
                                                                        <asp:ListItem Value="32">32</asp:ListItem>
                                                                        <asp:ListItem Value="33">33</asp:ListItem>
                                                                        <asp:ListItem Value="34">34</asp:ListItem>
                                                                        <asp:ListItem Value="35">35</asp:ListItem>
                                                                        <asp:ListItem Value="36">36</asp:ListItem>
                                                                        <asp:ListItem Value="37">37</asp:ListItem>
                                                                        <asp:ListItem Value="38">38</asp:ListItem>
                                                                        <asp:ListItem Value="39">39</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="40px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="刪除">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandArgument='<%# DataBinder.Eval(Container.DataItem,"ECP_id")%>' OnClick="LB_del_Click">[x]</asp:LinkButton>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="50px" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    </cc1:SmartGridView>
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </table>

                            </div>
                            <!-- tabsubmenublock -->
                        </div>
                        <!-- fixwidth -->
                        <br />
                    </div>
                    <!-- WrapperMain -->
                </div>
                <!-- WrapperContent -->
            </div>
            <div style="display: none">
                <rsweb:ReportViewer ID="ReportViewer1" runat="server" Font-Names="Verdana" Font-Size="8pt" WaitMessageFont-Names="Verdana" WaitMessageFont-Size="14pt" Width="1460px" ShowExportControls="False" />

            </div>
        </div>

        <uc1:Foot runat="server" ID="Foot" />

        <script type="text/javascript">
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });
            $(document).ready(
                function () {
                    $(document).ready(function () { $('.headernews').scrollbox({ delay: 4 }); });
                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    $(".inputhint").tooltip({
                        position: { my: "left+10 bottom+40", at: "left bottom " },
                        tooltipClass: "custom-tooltip-styling",
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                });
            $(document).ready(function () {
                $(".accordionblock").hide();
                //個別按鈕操作
                $(".itemcontrolbtn").click(function () {
                    //切換子項顯示與隱藏
                    $(this).parent().parent().next(".accordionblock").slideToggle();
                    //ICON樣式切換
                    $(this).toggleClass("iconup");
                    //文字切換  ?:運算式是if else的快捷方式
                    //$(this).text($(this).text() == '展開項目' ? '收合項目' : '展開項目');
                });
                //全部展開
                $(".AllControlOpen").click(function () {
                    $(".accordionblock").slideDown();
                    //$(".itemcontrolbtn").text('收合項目');
                    $(".itemcontrolbtn").addClass("iconup")
                });
                //全部收合
                $(".AllControlClose").click(function () {
                    $(".accordionblock").slideUp();
                    //$(".itemcontrolbtn").text('展開項目');
                    $(".itemcontrolbtn").removeClass("iconup")
                });
            });
        </script>
    </form>
</body>
</html>
