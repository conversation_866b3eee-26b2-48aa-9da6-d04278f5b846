/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[9],{391:function(ia,y,e){e.r(y);var fa=e(1);ia=e(48);var x=e(142),ha=e(316),ea=e(405),da=window;e=function(){function e(e,x){this.LP=function(e){e=e.split(".");return e[e.length-1].match(/(jpg|jpeg|png|gif)$/i)};x=x||{};this.url=e;this.filename=x.filename||e;this.He=x.customHeaders;this.qea=!!x.useDownloader;this.withCredentials=!!x.withCredentials}e.prototype.mB=function(e){this.He=e};e.prototype.getCustomHeaders=function(){return this.He};
e.prototype.getFileData=function(w){var y=this,r=this,h=new XMLHttpRequest,f=0===this.url.indexOf("blob:")?"blob":"arraybuffer";h.open("GET",this.url,!0);h.withCredentials=this.withCredentials;h.responseType=f;this.He&&Object.keys(this.He).forEach(function(e){h.setRequestHeader(e,y.He[e])});var n=/^https?:/i.test(this.url);h.addEventListener("load",function(f){return Object(fa.b)(this,void 0,void 0,function(){var h,y,z,ba,ca,da;return Object(fa.d)(this,function(aa){switch(aa.label){case 0:if(200!==
this.status&&(n||0!==this.status))return[3,10];r.trigger(e.Events.DOCUMENT_LOADING_PROGRESS,[f.loaded,f.loaded]);if("blob"!==this.responseType)return[3,4];h=this.response;return r.LP(r.filename)?[4,Object(ea.a)(h)]:[3,2];case 1:return y=aa.ea(),r.fileSize=y.byteLength,w(new Uint8Array(y)),[3,3];case 2:z=new FileReader,z.onload=function(e){e=new Uint8Array(e.target.result);r.fileSize=e.length;w(e)},z.readAsArrayBuffer(h),aa.label=3;case 3:return[3,9];case 4:aa.Sf.push([4,8,,9]);ba=new Uint8Array(this.response);
if(!r.LP(r.filename))return[3,6];h=new Blob([ba.buffer]);return[4,Object(ea.a)(h)];case 5:return y=aa.ea(),r.fileSize=y.byteLength,w(new Uint8Array(y)),[3,7];case 6:r.fileSize=ba.length,w(ba),aa.label=7;case 7:return[3,9];case 8:return aa.ea(),r.trigger(e.Events.ERROR,["pdfLoad","Out of memory"]),[3,9];case 9:return[3,11];case 10:ca=f.currentTarget,da=Object(x.b)(ca),r.trigger(e.Events.ERROR,["pdfLoad",this.status+" "+ca.statusText,da]),aa.label=11;case 11:return r.uw=null,[2]}})})},!1);h.onprogress=
function(f){r.trigger(e.Events.DOCUMENT_LOADING_PROGRESS,[f.loaded,0<f.total?f.total:0])};h.addEventListener("error",function(){r.trigger(e.Events.ERROR,["pdfLoad","Network failure"]);r.uw=null},!1);h.send();this.uw=h};e.prototype.getFile=function(){var e=this;return new Promise(function(w){da.utils.isJSWorker&&w(e.url);if(e.qea){var r=Object(fa.a)({url:e.url},e.He?{customHeaders:e.He}:{});w(r)}w(null)})};e.prototype.abort=function(){this.uw&&(this.uw.abort(),this.uw=null)};e.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress",
ERROR:"error"};return e}();Object(ia.a)(e);Object(ha.a)(e);Object(ha.b)(e);y["default"]=e}}]);}).call(this || window)
