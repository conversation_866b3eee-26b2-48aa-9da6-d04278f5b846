﻿<%@ Application Language="C#" %>

<script runat="server">

    void Application_Start(object sender, EventArgs e) 
    {
        // 在應用程式啟動時執行的程式碼

    }
    
    void Application_End(object sender, EventArgs e) 
    {
        //  在應用程式關閉時執行的程式碼

    }
        
    void Application_Error(object sender, EventArgs e) 
    { 
        // 在發生未處理的錯誤時執行的程式碼

    }

    void Session_Start(object sender, EventArgs e) 
    {
        // 在新的工作階段啟動時執行的程式碼
        try
        {
            SSOUtil.SSOLoginUser user = new SSOUtil.SSOLoginUser();
            user.GetEmpInfo();
            string deptid = user.empOrgcd + user.empDeptcd;

            // 啟動新工作階段時執行的程式碼
            loginlog.ws_loginlog LoginLog = new loginlog.ws_loginlog();
            LoginLog.Timeout = 3000;    //預設為 3000 ms
            string page = Request.RawUrl;
            int login_ampx = 0;
            //if (page.ToUpper().IndexOf("ENGAGE") >0)
            //{
            //     LoginLog.insert_loginlog(user.empNo, Request.UserHostAddress.ToString(), DateTime.Now, "登入", "洽案系統", "3103", deptid);
            //     login_ampx++;
            //}
 
            //if (page.ToUpper().IndexOf("TREATY") >0)
            //{
            //    LoginLog.insert_loginlog(user.empNo, Request.UserHostAddress.ToString(), DateTime.Now, "登入", "議約系統", "3114", deptid);
            //    login_ampx++;
            //}
                      
            //if (page.ToUpper().IndexOf("GPI") >0)
            //{
            //    LoginLog.insert_loginlog(user.empNo, Request.UserHostAddress.ToString(), DateTime.Now, "登入", "標案系統", "3103", deptid);
            //    login_ampx++;
            //}
            //if (login_ampx ==0)
            //{
            //    LoginLog.insert_loginlog(user.empNo, Request.UserHostAddress.ToString(), DateTime.Now, "登入", "全院計畫管理系統", "3103", deptid);
            //}                
        }
        catch
        {
            //為避免SOA系統問題造成前端系統無法使用, try .. catch
        }        

    }

    void Session_End(object sender, EventArgs e) 
    {
        // 在工作階段結束時執行的程式碼
        // 注意: 只有在  Web.config 檔案中將 sessionstate 模式設定為 InProc 時，
        // 才會引起 Session_End 事件。如果將 session 模式設定為 StateServer 
        // 或 SQLServer，則不會引起該事件。

    }
       
</script>
