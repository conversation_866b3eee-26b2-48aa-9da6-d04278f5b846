﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_dispatch.aspx.cs" Inherits="Treaty_webpage_TechCase_dispatch" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件承辦資訊</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("分案成功!");
            parent.$.fn.colorbox.close();
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <br />
        <br />
        <span class="stripeMe" style="display: flex; justify-content: center;">
            <asp:DropDownList ID="ddl_Handle" runat="server" DataValueField="value" DataTextField="Text"></asp:DropDownList>
            <asp:Button ID="btn_Save" runat="server" Text="存檔" OnClick="btn_Save_Click" class="ajax_mesg genbtnS" />
        </span>
    </form>
</body>
</html>
