﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Script.Serialization;
using System.Data;
using System.Collections;

/// <summary>
/// Summary description for WebServiceAPI
/// </summary>
public class JSON_API : AMPS.common
{
	public JSON_API()
	{
		//
		// TODO: Add constructor logic here
		//
	}

    /// <summary>
    /// 資料表轉JSON格式
    /// </summary>
    /// <param name="dt">資料表</param>
    /// <returns>回傳JSON格式</returns>
    public string DataTableToJSONFormat(DataTable dt)
    {
        ArrayList list = new ArrayList();
        if (dt != null && dt.Rows.Count > 0)
        {
            foreach (DataRow dr in dt.Rows)
            {
                Hashtable ht = new Hashtable();
                foreach (DataColumn dc in dt.Columns)
                    ht.Add(dc.ColumnName, this.DoRetrunSpecialChar(dr[dc.ColumnName].ToString().Trim()));
                list.Add(ht);
            }
        }
        return this.ToJSONFormat(list);
    }

    /// <summary>
    /// 資料表轉JSON格式
    /// </summary>
    /// <param name="dt">資料表</param>
    /// <param name="column">欄位名</param>
    /// <returns>回傳JSON格式</returns>
    public string DataTableToJSONFormat(DataTable dt, string column)
    {
        Hashtable ht = new Hashtable();
        ht.Add(column, this.DataTableToArrayList(dt));
        return this.ToJSONFormat(ht);
    }

    /// <summary>
    /// 資料表轉ArrayList
    /// </summary>
    /// <param name="dt">資料表</param>
    /// <returns>回傳ArrayList</returns>
    public ArrayList DataTableToArrayList(DataTable dt)
    {
        ArrayList list = new ArrayList();
        if (dt != null && dt.Rows.Count > 0)
        {
            foreach (DataRow dr in dt.Rows)
            {
                Hashtable ht = new Hashtable();
                foreach (DataColumn dc in dt.Columns)
                    ht.Add(dc.ColumnName, this.DoRetrunSpecialChar(dr[dc.ColumnName].ToString().Trim()));
                list.Add(ht);
            }
        }
        return list;
    }

    /// <summary>
    /// 物件轉JSON格式
    /// </summary>
    /// <param name="obj">物件</param>
    /// <returns>回傳JSON格式</returns>
    public string ToJSONFormat(object obj)
    {
        return (new JavaScriptSerializer()).Serialize(obj);
    }
}