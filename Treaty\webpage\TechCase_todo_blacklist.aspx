﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_todo_blacklist.aspx.cs" Inherits="Treaty_webpage_TechCase_todo_blacklist" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">

</script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="stripeMe" style="margin: 15px">
            <br />
            <br />
            <cc1:SmartGridView ID="sgvList" BorderWidth="0px" CellPadding="0" AllowPaging="True" runat="server" AutoGenerateColumns="false" Width="100%" OnPageIndexChanging="sgvList_PageIndexChanging">
                <Columns>
                    <asp:TemplateField HeaderText="刪除">
                        <ItemTemplate>
                            <asp:ImageButton ID="IB_del" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-delete.png" CommandName="Del" CommandArgument='<%# Server.HtmlEncode(Eval("tt_seno").ToString()) %>' OnClick="IB_del_Click" OnClientClick="return  confirm('確定要刪除 ?');" />
                        </ItemTemplate>
                        <HeaderStyle Width="80px"></HeaderStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="單位名稱">
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("單位名稱").ToString())) %>
                        </ItemTemplate>
                        <ItemStyle Width="10px" />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="洽案／契約名稱">
                        <ItemTemplate>
                            <%# Eval("議約編號") %><br />
                            <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tt_seno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_name").ToString())) %>'></asp:LinkButton><br />

                        </ItemTemplate>
                        <ItemStyle Width="400px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="tt_compname" HeaderText="客戶名稱">
                        <ItemStyle Width="250px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="分案日<hr>結案日">
                        <HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" VerticalAlign="Middle" Width="100px"></ItemStyle>
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("分案日").ToString())) %><hr />
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("結案日").ToString())) %>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="洽案<br>承辦人<hr>技轉C<br>承辦人">
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_promoter_name").ToString())) %><hr>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_handle_c_name").ToString())) %>
                        </ItemTemplate>
                        <ItemStyle Width="120px" HorizontalAlign="Center" />
                    </asp:TemplateField>

                    <asp:BoundField DataField="案件類型" HeaderText="案件類型">
                        <ItemStyle Width="100px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="技轉狀態" HeaderText="案件狀態">
                        <ItemStyle Width="100px" HorizontalAlign="Center" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="技轉<br>承辦人">
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_handle_name").ToString())) %>
                        </ItemTemplate>
                        <ItemStyle Width="80px" HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="機密等級">
                        <ItemTemplate>
                            <asp:Image ID="Image1" runat="server" Height="31px" ImageUrl="../images/CONFIDENTIAL.png" Width="80px" />
                        </ItemTemplate>
                        <ItemStyle Width="120px" HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="新創案">
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("新創案").ToString())) %>
                        </ItemTemplate>
                        <ItemStyle Width="10px" HorizontalAlign="Center" />
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="境外實施">
                        <ItemTemplate>
                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("境外實施").ToString())) %>
                        </ItemTemplate>
                        <ItemStyle Width="10px" HorizontalAlign="Center" />
                    </asp:TemplateField>
                </Columns>
                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span style='color:#578c27'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#578c27'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#578c27'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#578c27'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#578c27'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#578c27'>頁</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Left" CssClass="PagerStyle" />
                <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="~/images/first.gif" FirstPageText="第一頁"
                    LastPageImageUrl="~/images/last.gif" LastPageText="最後一頁" NextPageImageUrl="~/images/next.gif"
                    NextPageText="下一頁" PreviousPageImageUrl="~/images/previous.gif" PreviousPageText="上一頁" />
                <EmptyDataTemplate>
                    <div style="text-align: center;">無符合資料</div>
                </EmptyDataTemplate>
            </cc1:SmartGridView>
        </div>
    </form>
</body>
</html>
