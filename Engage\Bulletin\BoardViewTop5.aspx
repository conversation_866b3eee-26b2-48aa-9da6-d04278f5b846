﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="BoardViewTop5.aspx.cs" Inherits="Engage_Bulletin_BoardViewTop5" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
	<title></title>
	<link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui-1.10.3.custom.min.css" />
	<link rel="stylesheet" type="text/css" href="../css/style.css" />
	<link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
	<script type="text/javascript" src="../js/jquery-1.10.2.min.js"></script>
	<script type="text/javascript" src="../js/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="../js/PageExtend.js"></script>
	<style>
	.listtype ul{margin:0 0 0 -40px;*margin:0;_margin:0;}
	.listtype li{list-style:none;padding:10px; border-bottom:1px dashed #CCCCCC;}
	.listtype span input{ width:60px; height:20px;}
	.font-gray{color:#4e4e4e;}
	.font-gray a{color:#4e4e4e; text-decoration:none;}
	.font-gray a:hover{color:#d55500;}
	</style>
</head>
<body>
	<form id="form1" runat="server">
        <asp:Literal ID="lit_Matomo" runat="server"></asp:Literal>

		<div class="WrapperBody">
			<div class="WrapperContent">

				<div class="WrapperMain">
					<br />
					<asp:Panel ID="pnl_normal" runat="server">
						<div class="twocol margin35T">
							<div class="right">
								
							</div>
						</div>
						<div class="gentable font-normal" style="height: 235px">
							<div class="listtype font-gray font-size4 ">
								<asp:Repeater ID="Rep1" runat="server" OnItemDataBound="Rep1_ItemDataBound">
									<HeaderTemplate>
										<ul>
									</HeaderTemplate>
									<ItemTemplate>
										<li>
											<asp:LinkButton ID="btn_view" runat="server" Text='<%#Eval("BB_subject")%>' CommandArgument='<%#Eval("BB_id") %>' OnClick="btn_view_Click"></asp:LinkButton>
											<span style="position: absolute; right: 10px;">
												<asp:ImageButton ID="btn_more" runat="server" ImageUrl="../images/btnMore.gif" OnClick="btn_more_Click" />
											</span>
										</li>
									</ItemTemplate>
								</asp:Repeater>
								<asp:Label ID="lbl_NoData" runat="server" CssClass="font-red" Text="無訊息公告"></asp:Label>
							</div>
						</div>
						<div class="margin5TB" style="position: absolute; right: 10px;">
							<asp:CheckBox ID="cb" runat="server" Text="下次登入不再顯示" AutoPostBack="true" OnCheckedChanged="cb_CheckedChanged" />
							<asp:Button ID="btn_manage1" runat="server" CssClass="genbtn" Text="業務管理" OnClick="btn_manage_Click" />
						</div>
					</asp:Panel>

					<asp:Panel ID="pnl_serviceDown" runat="server" Visible="false">
						

						<div class="tabsubmenublock margin35T">
							<div id="div_content" runat="server"></div>
						</div>
						<span class="font-darkred lineheight03">附件：
							<asp:Repeater ID="repFile" runat="server" OnItemCommand="repFile_ItemCommand">
								<ItemTemplate>
									<span class="font-size4">
										<asp:LinkButton ID="btn_openFile" runat="server" CommandName="OpenFile" CommandArgument='<%#Eval("ba_id")%>' Text='<%#DataBinder.Eval(Container.DataItem,"ba_filename")%>'></asp:LinkButton>
									</span>
								</ItemTemplate>
							</asp:Repeater>
						</span>
						<div class="twocol margin10TB">
							<div class="right">
								<asp:Button ID="btn_manage2" runat="server" CssClass="genbtn" Text="業務管理" OnClick="btn_manage_Click" />
							</div>
						</div>
					</asp:Panel>
				</div>
				<!-- WrapperMain -->
				<br />
			</div>
			<!-- WrapperContent -->
		</div>
		<!-- WrapperBody -->

		<script type="text/javascript">
			function init() {
				PageExtend_init();
			}

			$(document).ready(function () {
				init();
			});

		</script>
	</form>
</body>
</html>
