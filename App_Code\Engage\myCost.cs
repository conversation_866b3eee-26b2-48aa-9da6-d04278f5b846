﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myCost
	/// </summary>
	public class myCost : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private long _seqsn;
		private string _empno;
		private string _empname;

		private int _cost_ver;

		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 洽案流水號
		/// </summary>
		public long Seqsn
		{
			get { return _seqsn; }
			set { _seqsn = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		/// <summary>
		/// 契約簽辦的版次
		/// </summary>
		public int Cost_ver
		{
			get { return _cost_ver; }
			set { _cost_ver = value; }
		}

		public string cost_memo_type = "";
		public string cost_memo = "";


		/// <summary>
		/// 直接成本說明
		/// </summary>
		public string cost_memo1;
		/// <summary>
		/// 分包項目說明
		/// </summary>
		public string cost_memo2;
		/// <summary>
		/// 代購項目說明
		/// </summary>
		public string cost_memo3;
		/// <summary>
		/// 成本法定價估算說明
		/// </summary>
		public string cost_memo4;
		/// <summary>
		/// 技服報價及底價說明
		/// </summary>
		public string cost_memo5;

		/// <summary>
		/// 成功機率
		/// </summary>
		public string eb_success_rate;

		/// <summary>
		/// 幣別
		/// </summary>
		public string cost_moneytype;

		// 估算人
		public string cost_evalempno1;
		public string cost_evalempname1;
		public string cost_evalempno2;
		public string cost_evalempname2;

		//審查方式
		public string cost_reviewtype;

		/// <summary>
		/// 第二種計價法
		/// </summary>
		public string cost_valuation;

		/// <summary>
		/// 會議審查資料-審查日期
		/// </summary>
		public string cost_viewdate;

		//會議審查資料-主持人
		public string cost_viewempno;
		public string cost_viewempname;

		#region 技術服務成本
		//直接成本
		public int cost1_id;
		public int cost1_ver;
		public string cost1_dept;
		public string cost1_deptnm;
		public int cost1_per;
		public int cost1_travel;
		public int cost1_material;
		public int cost1_maintain;
		public int cost1_business;
		public int cost1_equipuse;
		public int cost1_other;
		public int cost1_dircost;

		//分包項目
		public int cost2_id;
		public int cost2_ver;
		public string cost2_dept;
		public string cost2_deptnm;
		public int cost2_enginfee;
		public int cost2_laborfee;
		public int cost2_otherfee;
		public int cost2_total;

		//代購項目
		public int cost3_id;
		public int cost3_ver;
		public string cost3_dept;
		public string cost3_deptnm;
		public int cost3_equipfee;
		public int cost3_otherfee;
		public int cost3_total;

		//(二)成本法訂價估算
		public string edit_flag = "N";
		public decimal cost_overhead_rate = 0;
		public decimal cost_promo_rate = 0;
		public decimal cost_orgmng_rate = 0;
		public decimal cost_headmng_rate = 0;
		public decimal cost_profit_rate = 0;
		public decimal cost_srv_rate = 0;
		public decimal cost_other_rate = 0;
		public decimal cost_os_rate = 0;
		public decimal cost_equip_rate = 0;
		public decimal total_price = 0;
		/// <summary>
		/// 依單位固定分攤比率自動估算
		/// </summary>
		public string cost_share = "0";

		//技服報價及底價
		/// <summary>
		/// 技服建議報價(未稅)
		/// </summary>
		public int cost_suggestfee1 = 0;
		/// <summary>
		/// 技服建議底價(未稅)
		/// </summary>
		public int cost_basefee1 = 0;
		/// <summary>
		/// 本年度預計認列收入數
		/// </summary>
		public int cost_promincome = 0;

		/// <summary>
		/// 技服計價綜合說明
		/// </summary>
		public string cost_memo6 = "";
		#endregion


		#region 技轉成本

		//(一)技轉基本資料項目,1.投入成本
		public decimal cost_rschfee1 = 0;
		public decimal cost_rschfee2 = 0;
		public decimal cost_patentfee1 = 0;
		public decimal cost_patentfee2 = 0;
		public string cost_iomemo = "";

		//(一)技轉基本資料項目,2.移轉家次及金額
		public int cost_transcnt1 = 0;
		public decimal cost_transfee1 = 0;
		public int cost_transcnt2 = 0;
		public decimal cost_transfee2 = 0;
		public string cost_transmemo = "";

		//(二)技術服務報酬(國有成果專用)
		public decimal cost_techsrvfee = 0;
		public string cost_techsrvmemo = "";

		//(三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項
		public int cost4_id = 0;
		public string cost4_resultfrom = "";
		public decimal cost4_tafbyfixfee = 0;
		public decimal cost4_tpfbyfixfee = 0;
		public decimal cost4_pafbyfixfee = 0;
		public decimal cost4_ppfbyfixfee = 0;
		public decimal cost4_cash_total = 0;
		public decimal cost4_tafbyfixfe_stock = 0;
		public decimal cost4_tpfbyfixfee_stock = 0;
		public decimal cost4_pafbyfixfee_stock = 0;
		public decimal cost4_ppfbyfixfe_stock = 0;
		public decimal cost4_stock_total = 0;
		public decimal cost4_stock_value_total = 0;
		public decimal cost4_stock_price = 10;

		//(三)技轉授權金/權利金訂價, 2.權利金非一次計價項目
		public int cost6_id = 0;
		public string cost6_type = "";
		public string cost6_resultfrom = "";
		public string cost6_gatherway = "";
		public int cost6_year = 0;
		public decimal cost6_gather = 0;
		public decimal cost6_estimfee = 0;
		public string cost6_memo2 = "";


		//(四)技轉報價/底價項目
		public decimal cost_suggestfee2 = 0;
		public decimal cost_basefee2 = 0;
		public decimal cost_techincome = 0;
		public string cost_memo10 = "";
		#endregion


		#endregion

		public myCost()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		#region 取得成本訂價的明細
		/// <summary>
		/// 取得成本訂價的明細
		/// </summary>
		/// <returns></returns>
		public DataTable GetDetail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_cost_select_by_seqsn";


			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		#endregion

		#region 取得成本訂價的版本
		/// <summary>
		/// 取得成本訂價的版本
		/// </summary>
		/// <returns></returns>
		public int GetVer()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT cost_ver FROM engage_cost WHERE cost_seqsn = @seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string data = this.getTopOne(oCmd, CommandType.Text);
				int ver = 0;
				if (data != string.Empty)
				{
					ver = int.Parse(data);
				}
				return ver;
			}
			catch
			{
				return 0;
			}
		}

		/// <summary>
		/// 更新成本訂價
		/// </summary>
		/// <returns></returns>
		public bool Update_cost()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_base set eb_success_rate=@eb_success_rate where eb_seqsn=@seqsn

update engage_cost set
	cost_moneytype=@cost_moneytype
	,cost_evalempno1=@cost_evalempno1
	,cost_evalempname1=@cost_evalempname1
	,cost_evalempno2=@cost_evalempno2
	,cost_evalempname2=@cost_evalempname2
	,cost_reviewtype=@cost_reviewtype
	,cost_valuation=@cost_valuation
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
	,cost_viewdate=@cost_viewdate
	,cost_viewempno=@cost_viewempno
	,cost_viewempname=@cost_viewempname
  where cost_seqsn=@seqsn

 if	(@cost_reviewtype='1') 
	and exists(Select count(*) from engage_attfile2 Where ea_seqsn = @seqsn and ea_filetype = 'NE')
	and exists(select eb_execstatus from engage_base where eb_seqsn = @seqsn and eb_execstatus < 'J5')
begin
	--IF<J5更新執行狀態改為'訂價審查完成(J5)'
	update engage_base set eb_execstatus='J5' where eb_seqsn=@seqsn	
end
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@eb_success_rate", eb_success_rate);
			oCmd.Parameters.AddWithValue("@cost_moneytype", cost_moneytype);
			oCmd.Parameters.AddWithValue("@cost_evalempno1", cost_evalempno1);
			oCmd.Parameters.AddWithValue("@cost_evalempname1", cost_evalempname1);
			oCmd.Parameters.AddWithValue("@cost_evalempno2", cost_evalempno2);
			oCmd.Parameters.AddWithValue("@cost_evalempname2", cost_evalempname2);
			oCmd.Parameters.AddWithValue("@cost_reviewtype", cost_reviewtype);
			oCmd.Parameters.AddWithValue("@cost_valuation", cost_valuation);
			oCmd.Parameters.AddWithValue("@cost_viewdate", cost_viewdate);
			oCmd.Parameters.AddWithValue("@cost_viewempno", cost_viewempno);
			oCmd.Parameters.AddWithValue("@cost_viewempname", cost_viewempname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新-成本訂價各項目說明
		/// <summary>
		/// 更新-成本訂價各項目說明
		/// </summary>
		/// <returns></returns>
		public bool Update_costmemo()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @cost_memo_type = 'memo1'
begin
	update engage_cost set cost_memo1=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end
else if @cost_memo_type = 'memo2'
begin
	update engage_cost set cost_memo2=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end
else if @cost_memo_type = 'memo3'
begin
	update engage_cost set cost_memo3=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end
else if @cost_memo_type = 'memo7'
begin
	update engage_cost set cost_memo7=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end
else if @cost_memo_type = 'memo8'
begin
	update engage_cost set cost_memo8=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end
else if @cost_memo_type = 'evaluememo'
begin
	update engage_cost set cost_evaluememo=@cost_memo, cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), cost_modempno=@empno, cost_modname=@empname where cost_seqsn=@seqsn
end

";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_memo_type", cost_memo_type);
			oCmd.Parameters.AddWithValue("@cost_memo", cost_memo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 技術服務成本

		#region 技術服務成本(直接成本)資料, costdept1
		/// <summary>
		/// 取得成本訂價,技術服務成本(直接成本)資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept1()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_costdept1_query";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		/// <summary>
		/// 取得成本訂價,技術服務成本(直接成本)明細資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept1_detail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM engage_costdept1
WHERE cost1_id= @cost1_id";

			oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(直接成本)儲存
		/// </summary>
		/// <returns></returns>
		public bool Update_costdept1()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_costdept1 where cost1_id = @cost1_id)
begin
	declare @ver int
	select @ver=cost_ver from engage_cost where cost_seqsn=@seqsn

	insert into engage_costdept1 
		(cost1_seqsn, cost1_ver, cost1_dept, cost1_deptnm, 
		 cost1_per, cost1_travel, cost1_material, cost1_maintain, cost1_business, cost1_equipuse, cost1_other, cost1_dircost)
	  select 
		@seqsn, @ver, @cost1_dept, @cost1_deptnm, 
		 @cost1_per, @cost1_travel, @cost1_material, @cost1_maintain, @cost1_business, @cost1_equipuse, @cost1_other, @cost1_dircost
end
else
begin
	update engage_costdept1 set
		cost1_dept=@cost1_dept, cost1_deptnm=@cost1_deptnm, 
		cost1_per=@cost1_per, cost1_travel=@cost1_travel, cost1_material=@cost1_material, cost1_maintain=@cost1_maintain, cost1_business=@cost1_business, cost1_equipuse=@cost1_equipuse, cost1_other=@cost1_other, cost1_dircost=@cost1_dircost
	  where cost1_id = @cost1_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);
			oCmd.Parameters.AddWithValue("@cost1_dept", cost1_dept);
			oCmd.Parameters.AddWithValue("@cost1_deptnm", cost1_deptnm);
			oCmd.Parameters.AddWithValue("@cost1_per", cost1_per);
			oCmd.Parameters.AddWithValue("@cost1_travel", cost1_travel);
			oCmd.Parameters.AddWithValue("@cost1_material", cost1_material);
			oCmd.Parameters.AddWithValue("@cost1_maintain", cost1_maintain);
			oCmd.Parameters.AddWithValue("@cost1_business", cost1_business);
			oCmd.Parameters.AddWithValue("@cost1_equipuse", cost1_equipuse);
			oCmd.Parameters.AddWithValue("@cost1_other", cost1_other);
			oCmd.Parameters.AddWithValue("@cost1_dircost", cost1_dircost);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(直接成本)刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costdept1()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_costdept1
WHERE cost1_id= @cost1_id
";
			oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 技術服務成本(分包項目)資料, costdept2
		/// <summary>
		/// 取得成本訂價,技術服務成本(分包項目)資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept2()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_costdept2_query";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		/// <summary>
		/// 取得成本訂價,技術服務成本(分包項目)明細資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept2_detail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM engage_costdept2
WHERE cost2_id= @cost2_id";

			oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(分包項目)儲存
		/// </summary>
		/// <returns></returns>
		public bool Update_costdept2()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_costdept2 where cost2_id = @cost2_id)
begin
	declare @ver int
	select @ver=cost_ver from engage_cost where cost_seqsn=@seqsn

	insert into engage_costdept2 
		(cost2_seqsn, cost2_ver, cost2_dept, cost2_deptnm, 
		 cost2_enginfee, cost2_laborfee, cost2_otherfee, cost2_total)
	  select 
		@seqsn, @ver, @cost2_dept, @cost2_deptnm, 
		@cost2_enginfee, @cost2_laborfee, @cost2_otherfee, @cost2_total
end
else
begin
	update engage_costdept2 set
		cost2_dept=@cost2_dept, cost2_deptnm=@cost2_deptnm, 
		cost2_enginfee=@cost2_enginfee, cost2_laborfee=@cost2_laborfee, cost2_otherfee=@cost2_otherfee, cost2_total=@cost2_total
	  where cost2_id = @cost2_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);
			oCmd.Parameters.AddWithValue("@cost2_dept", cost2_dept);
			oCmd.Parameters.AddWithValue("@cost2_deptnm", cost2_deptnm);
			oCmd.Parameters.AddWithValue("@cost2_enginfee", cost2_enginfee);
			oCmd.Parameters.AddWithValue("@cost2_laborfee", cost2_laborfee);
			oCmd.Parameters.AddWithValue("@cost2_otherfee", cost2_otherfee);
			oCmd.Parameters.AddWithValue("@cost2_total", cost2_total);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(分包項目)刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costdept2()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_costdept2
WHERE cost2_id= @cost2_id
";
			oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 技術服務成本(代購項目)資料, costdept3
		/// <summary>
		/// 取得成本訂價,技術服務成本(代購項目)資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept3()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_costdept3_query";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		/// <summary>
		/// 取得成本訂價,技術服務成本(代購項目)明細資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept3_detail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM engage_costdept3
WHERE cost3_id= @cost3_id";

			oCmd.Parameters.AddWithValue("@cost3_id", cost3_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(代購項目)儲存
		/// </summary>
		/// <returns></returns>
		public bool Update_costdept3()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_costdept3 where cost3_id = @cost3_id)
begin
	declare @ver int
	select @ver=cost_ver from engage_cost where cost_seqsn=@seqsn

	insert into engage_costdept3 
		(cost3_seqsn, cost3_ver, cost3_dept, cost3_deptnm, 
		 cost3_equipfee, cost3_otherfee, cost3_total)
	  select 
		@seqsn, @ver, @cost3_dept, @cost3_deptnm, 
		@cost3_equipfee, @cost3_otherfee, @cost3_total
end
else
begin
	update engage_costdept3 set
		cost3_dept=@cost3_dept, cost3_deptnm=@cost3_deptnm, 
		cost3_equipfee=@cost3_equipfee, cost3_otherfee=@cost3_otherfee, cost3_total=@cost3_total
	  where cost3_id = @cost3_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost3_id", cost3_id);
			oCmd.Parameters.AddWithValue("@cost3_dept", cost3_dept);
			oCmd.Parameters.AddWithValue("@cost3_deptnm", cost3_deptnm);
			oCmd.Parameters.AddWithValue("@cost3_equipfee", cost3_equipfee);
			oCmd.Parameters.AddWithValue("@cost3_otherfee", cost3_otherfee);
			oCmd.Parameters.AddWithValue("@cost3_total", cost3_total);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// 成本訂價,技術服務成本(代購項目)刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costdept3()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_costdept3
WHERE cost3_id=@cost3_id
";
			oCmd.Parameters.AddWithValue("@cost3_id", cost3_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 技術服務成本(二)成本法訂價估算
		/// <summary>
		/// 取得成本訂價,技術服務成本(二)成本法訂價估算資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costEvaluate()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_esti_cost";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);
			oCmd.Parameters.AddWithValue("@edit_flag", edit_flag);
			oCmd.Parameters.AddWithValue("@cost_overhead_rate", cost_overhead_rate);
			oCmd.Parameters.AddWithValue("@cost_promo_rate", cost_promo_rate);
			oCmd.Parameters.AddWithValue("@cost_orgmng_rate", cost_orgmng_rate);
			oCmd.Parameters.AddWithValue("@cost_headmng_rate", cost_headmng_rate);
			oCmd.Parameters.AddWithValue("@cost_profit_rate", cost_profit_rate);
			oCmd.Parameters.AddWithValue("@cost_srv_rate", cost_srv_rate);
			oCmd.Parameters.AddWithValue("@cost_other_rate", cost_other_rate);
			oCmd.Parameters.AddWithValue("@cost_os_rate", cost_os_rate);
			oCmd.Parameters.AddWithValue("@cost_equip_rate", cost_equip_rate);
			SqlParameter oParam = oCmd.Parameters.AddWithValue("@total_price", 0);
			oParam.DbType = DbType.Decimal;
			oParam.Direction = ParameterDirection.Output;

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			total_price = (decimal)oParam.Value;
			return dt;
		}

		/// <summary>
		/// 更新技術服務成本(二)成本法訂價估算
		/// </summary>
		/// <returns></returns>
		public bool Update_costEvaluate(string szBringPrice)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_overhead_rate=@cost_overhead_rate
	,cost_promo_rate=@cost_promo_rate
	,cost_orgmng_rate=@cost_orgmng_rate
	,cost_headmng_rate=@cost_headmng_rate
	,cost_profit_rate=@cost_profit_rate
	,cost_srv_rate=@cost_srv_rate
	,cost_other_rate=@cost_other_rate
	,cost_os_rate=@cost_os_rate
	,cost_equip_rate=@cost_equip_rate
    ,cost_memo4=@cost_memo4
    ,cost_share=@cost_share
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_prom_total=@total_price
  where cost_seqsn=@seqsn

if @bring_price_flag = 'Y'
begin
	update engage_cost set 
		cost_suggestfee1=@total_price
		,cost_basefee1=@total_price
		,cost_promincome=@total_price
	  where cost_seqsn=@seqsn
end	
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_overhead_rate", cost_overhead_rate);
			oCmd.Parameters.AddWithValue("@cost_promo_rate", cost_promo_rate);
			oCmd.Parameters.AddWithValue("@cost_orgmng_rate", cost_orgmng_rate);
			oCmd.Parameters.AddWithValue("@cost_headmng_rate", cost_headmng_rate);
			oCmd.Parameters.AddWithValue("@cost_profit_rate", cost_profit_rate);
			oCmd.Parameters.AddWithValue("@cost_srv_rate", cost_srv_rate);
			oCmd.Parameters.AddWithValue("@cost_other_rate", cost_other_rate);
			oCmd.Parameters.AddWithValue("@cost_os_rate", cost_os_rate);
			oCmd.Parameters.AddWithValue("@cost_equip_rate", cost_equip_rate);
			oCmd.Parameters.AddWithValue("@cost_memo4", cost_memo4);
			oCmd.Parameters.AddWithValue("@cost_share", cost_share);
			oCmd.Parameters.AddWithValue("@total_price", total_price);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@bring_price_flag", szBringPrice);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 更新「技服訂價合計」
		/// </summary>
		/// <param name="szBringPrice"></param>
		/// <returns></returns>
		public bool Update_costPromoteTotal()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_prom_total=@total_price
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@total_price", total_price);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 單位固定分攤比率
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costRate()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @orgcd varchar(2)
select @orgcd=eb_orgcd from engage_base where eb_seqsn = @seqsn
if exists(select * from engage_costrate where ecr_org = @orgcd)
	select * from engage_costrate where ecr_org = @orgcd
else
	select * from engage_costrate where ecr_org = '00'
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 取得「依單位固定分攤比率自動估算」
		/// </summary>
		/// <returns></returns>
		public string GetCostShare()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT cost_share FROM engage_cost WHERE cost_seqsn = @seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string data = this.getTopOne(oCmd, CommandType.Text);
				return data;
			}
			catch
			{
				return "0";
			}
		}

		public string CheckValuation()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_cost_valuation";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			SqlParameter msg = oCmd.Parameters.Add("@approvelist", SqlDbType.NVarChar);
			msg.Direction = ParameterDirection.Output;
			msg.Size = 1000;

			oCmd.Parameters.AddWithValue("@edit_flag", edit_flag);
			oCmd.Parameters.AddWithValue("@rate1", cost_overhead_rate);
			oCmd.Parameters.AddWithValue("@rate2", cost_promo_rate);
			oCmd.Parameters.AddWithValue("@rate3", cost_orgmng_rate);
			oCmd.Parameters.AddWithValue("@rate4", cost_headmng_rate);
			oCmd.Parameters.AddWithValue("@rate5", cost_profit_rate);
			oCmd.Parameters.AddWithValue("@rate6", cost_srv_rate);
			oCmd.Parameters.AddWithValue("@rate7", cost_other_rate);
			oCmd.Parameters.AddWithValue("@rate8", cost_os_rate);
			oCmd.Parameters.AddWithValue("@rate9", cost_equip_rate);

			this.Execute(oCmd, CommandType.StoredProcedure);
			return msg.Value.ToString();
		}

		/// <summary>
		/// 依計價法提供下列資訊予產服中心計價窗口，系統自動mail通知產服計價窗口
		/// </summary>
		/// <returns></returns>
		public bool SendMail_costValuation()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_webmail_engage_cost_valuation";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 技術服務成本(四)技服報價及底價
		/// <summary>
		/// 技術服務成本,更新技服報價及底價
		/// </summary>
		/// <returns></returns>
		public bool Update_TechService()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_suggestfee1=@cost_suggestfee1
	,cost_basefee1=@cost_basefee1
	,cost_promincome=@cost_promincome
	,cost_memo6=@cost_memo6
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@cost_suggestfee1", cost_suggestfee1);
			oCmd.Parameters.AddWithValue("@cost_basefee1", cost_basefee1);
			oCmd.Parameters.AddWithValue("@cost_promincome", cost_promincome);
			oCmd.Parameters.AddWithValue("@cost_memo6", cost_memo6);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#endregion

		#region 技轉成本

		#region (一)技轉基本資料項目,1.投入成本
		/// <summary>
		/// (一)技轉基本資料項目,1.投入成本 
		/// </summary>
		/// <returns></returns>
		public bool Update_costTechTransInput()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_rschfee1=@cost_rschfee1
	,cost_rschfee2=@cost_rschfee2
	,cost_patentfee1=@cost_patentfee1
	,cost_patentfee2=@cost_patentfee2
	,cost_iomemo=@cost_iomemo
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_rschfee1", cost_rschfee1);
			oCmd.Parameters.AddWithValue("@cost_rschfee2", cost_rschfee2);
			oCmd.Parameters.AddWithValue("@cost_patentfee1", cost_patentfee1);
			oCmd.Parameters.AddWithValue("@cost_patentfee2", cost_patentfee2);
			oCmd.Parameters.AddWithValue("@cost_iomemo", cost_iomemo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region (一)技轉基本資料項目,2.移轉家次及金額
		/// <summary>
		/// (一)技轉基本資料項目,2.移轉家次及金額
		/// </summary>
		/// <returns></returns>
		public bool Update_costTechTransCount()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_transcnt1=@cost_transcnt1
	,cost_transfee1=@cost_transfee1
	,cost_transcnt2=@cost_transcnt2
	,cost_transfee2=@cost_transfee2
	,cost_transmemo=@cost_transmemo
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_transcnt1", cost_transcnt1);
			oCmd.Parameters.AddWithValue("@cost_transfee1", cost_transfee1);
			oCmd.Parameters.AddWithValue("@cost_transcnt2", cost_transcnt2);
			oCmd.Parameters.AddWithValue("@cost_transfee2", cost_transfee2);
			oCmd.Parameters.AddWithValue("@cost_transmemo", cost_transmemo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region (二)技術服務報酬(國有成果專用)
		/// <summary>
		/// (二)技術服務報酬(國有成果專用)
		/// </summary>
		/// <returns></returns>
		public bool Update_costTechTransService()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_techsrvfee=@cost_techsrvfee
	,cost_techsrvmemo=@cost_techsrvmemo
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_techsrvfee", cost_techsrvfee);
			oCmd.Parameters.AddWithValue("@cost_techsrvmemo", cost_techsrvmemo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region (三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項目, costdept6
		/// <summary>
		/// 取得(三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項目 資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept4()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_costdept4_query";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		/// <summary>
		/// 取得(三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項目 明細資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept4_detail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM engage_costdept4
WHERE cost4_id = @cost4_id";

			oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		/// <summary>
		/// (三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項目 儲存
		/// </summary>
		/// <returns></returns>
		public bool Update_costdept4()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_costdept4 where cost4_id = @cost4_id)
begin
	declare @ver int
	select @ver=cost_ver from engage_cost where cost_seqsn = @seqsn

	insert into engage_costdept4 
		(cost4_seqsn, cost4_ver, cost4_resultfrom,
		 cost4_tafbyfixfee, cost4_tpfbyfixfee, cost4_pafbyfixfee, cost4_ppfbyfixfee, cost4_cash_total,
		 cost4_tafbyfixfe_stock, cost4_tpfbyfixfee_stock, cost4_pafbyfixfee_stock, cost4_ppfbyfixfe_stock, cost4_stock_total, cost4_stock_value_total, cost4_stock_price
		 )
	  select 
		@seqsn, @ver, @cost4_resultfrom,
		@cost4_tafbyfixfee, @cost4_tpfbyfixfee, @cost4_pafbyfixfee, @cost4_ppfbyfixfee, @cost4_cash_total,
		@cost4_tafbyfixfe_stock, @cost4_tpfbyfixfee_stock, @cost4_pafbyfixfee_stock, @cost4_ppfbyfixfe_stock, @cost4_stock_total, @cost4_stock_value_total, @cost4_stock_price
end
else
begin
	update engage_costdept4 set
		cost4_resultfrom=@cost4_resultfrom, 
		cost4_tafbyfixfee=@cost4_tafbyfixfee, cost4_tpfbyfixfee=@cost4_tpfbyfixfee, cost4_pafbyfixfee=@cost4_pafbyfixfee, cost4_ppfbyfixfee=@cost4_ppfbyfixfee, cost4_cash_total=@cost4_cash_total,
		cost4_tafbyfixfe_stock=@cost4_tafbyfixfe_stock, cost4_tpfbyfixfee_stock=@cost4_tpfbyfixfee_stock, cost4_pafbyfixfee_stock=@cost4_pafbyfixfee_stock,
		cost4_ppfbyfixfe_stock=@cost4_ppfbyfixfe_stock, cost4_stock_total=@cost4_stock_total, cost4_stock_value_total=@cost4_stock_value_total, cost4_stock_price=@cost4_stock_price
	  where cost4_id = @cost4_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);
			oCmd.Parameters.AddWithValue("@cost4_resultfrom", cost4_resultfrom);
			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfee", cost4_tafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee", cost4_tpfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee", cost4_pafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfee", cost4_ppfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_cash_total", cost4_cash_total);
			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfe_stock", cost4_tafbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee_stock", cost4_tpfbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee_stock", cost4_pafbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfe_stock", cost4_ppfbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_stock_total", cost4_stock_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_value_total", cost4_stock_value_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_price", cost4_stock_price);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// (三)技轉授權金/權利金訂價, 1.授權金/權利金一次計價項目 刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costdept4()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_costdept4
WHERE cost4_id = @cost4_id
";
			oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region (三)技轉授權金/權利金訂價, 2.權利金非一次計價項目, costdept6
		/// <summary>
		/// 取得(三)技轉授權金/權利金訂價, 2.權利金非一次計價項目 資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept6()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_costdept6_query";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _cost_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		/// <summary>
		/// 取得(三)技轉授權金/權利金訂價, 2.權利金非一次計價項目 明細資料
		/// </summary>
		/// <returns></returns>
		public DataTable Get_costdept6_detail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT * FROM engage_costdept6
WHERE cost6_id = @cost6_id";

			oCmd.Parameters.AddWithValue("@cost6_id", cost6_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		/// <summary>
		/// (三)技轉授權金/權利金訂價, 2.權利金非一次計價項目 儲存
		/// </summary>
		/// <returns></returns>
		public bool Update_costdept6()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_costdept6 where cost6_id = @cost6_id)
begin
	declare @ver int
	select @ver=cost_ver from engage_cost where cost_seqsn = @seqsn

	insert into engage_costdept6 
		(cost6_seqsn, cost6_ver, cost6_type, cost6_resultfrom, cost6_gatherway, 
		 cost6_year, cost6_gather, cost6_estimfee, cost6_memo2)
	  select 
		@seqsn, @ver, @cost6_type, @cost6_resultfrom, @cost6_gatherway,
		@cost6_year, @cost6_gather, @cost6_estimfee, @cost6_memo2
end
else
begin
	update engage_costdept6 set
		cost6_type=@cost6_type, cost6_resultfrom=@cost6_resultfrom, cost6_gatherway=@cost6_gatherway, 
		cost6_year=@cost6_year, cost6_gather=@cost6_gather, cost6_estimfee=@cost6_estimfee, cost6_memo2=@cost6_memo2
	  where cost6_id = @cost6_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost6_id", cost6_id);
			oCmd.Parameters.AddWithValue("@cost6_type", cost6_type);
			oCmd.Parameters.AddWithValue("@cost6_resultfrom", cost6_resultfrom);
			oCmd.Parameters.AddWithValue("@cost6_gatherway", cost6_gatherway);
			oCmd.Parameters.AddWithValue("@cost6_year", cost6_year);
			oCmd.Parameters.AddWithValue("@cost6_gather", cost6_gather);
			oCmd.Parameters.AddWithValue("@cost6_estimfee", cost6_estimfee);
			oCmd.Parameters.AddWithValue("@cost6_memo2", cost6_memo2);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// (三)技轉授權金/權利金訂價, 2.權利金非一次計價項目 刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costdept6()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_costdept6
WHERE cost6_id = @cost6_id
";
			oCmd.Parameters.AddWithValue("@cost6_id", cost6_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region (四)技轉報價/底價項目
		/// <summary>
		/// (四)技轉報價/底價項目
		/// </summary>
		/// <returns></returns>
		public bool Update_costTechTransSuggest()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_cost set
	cost_suggestfee2=@cost_suggestfee2
	,cost_basefee2=@cost_basefee2
	,cost_techincome=@cost_techincome
	,cost_memo10=@cost_memo10
	,cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost_suggestfee2", cost_suggestfee2);
			oCmd.Parameters.AddWithValue("@cost_basefee2", cost_basefee2);
			oCmd.Parameters.AddWithValue("@cost_techincome", cost_techincome);
			oCmd.Parameters.AddWithValue("@cost_memo10", cost_memo10);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#endregion

		#region 取得「轉委託他人金額超過原承攬總價 50% 以上之服務案計算方式」

		/// <summary>
		/// 取得「轉委託他人金額超過原承攬總價 50% 以上之服務案計算方式」
		/// </summary>
		/// <returns>若轉委託他人金額超過原承攬總價 50%，則回傳值「1:是」, 未超過50%，則回傳值「0:否」</returns>
		public string GetSignEntrustRate()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_entrust_rate";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string data = this.getTopOne(oCmd, CommandType.Text);
				return data;
			}
			catch
			{
				return "0";
			}
		}
		#endregion

		#region 歷次簽辦單
		/// <summary>
		/// 歷次簽辦單
		/// </summary>
		/// <param name="seqsn"></param>
		/// <param name="condition"></param>
		/// <returns></returns>
		public DataTable GetCostHistoryBySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"
	select		cost_seqsn, cost_ver, cost_sendempname, cost_result
				,CONVERT(varchar, convert(datetime,case when cost_senddate='' then null else cost_senddate end), 111) as cost_senddate
	from		engage_his.dbo.engage_cost
	where		cost_seqsn=@seqsn
	order by	cost_ver
	");

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region cost_wts, WTS(Willingness To Sell)估算

		/// <summary>
		/// 取得國際中心窗口負責人
		/// </summary>
		/// <returns></returns>
		public DataTable GetWTSContact()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT top 1 empno,com_cname,com_telext FROM engage_ibc_empno left join common..comper on com_empno=empno WHERE dept = '國際WTS窗口'
";

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 計算「國際工服計價，最低WTS、最高WTS」
		/// </summary>
		/// <returns></returns>
		public DataTable GetCalcWTS()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @min_WTS decimal, @max_WTS decimal
exec pr_engage_cost_calc_wts @seqsn, @min_WTS output, @max_WTS output
select @min_WTS as min_WTS, @max_WTS as max_WTS
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 新增新版次
		/// <summary>
		/// 新增新版次, sp:[pr_engagecost_edit_ver]
		/// </summary>
		/// <param name="bIsCopy"></param>
		/// <returns></returns>
		public bool EngageCostUpgradeVersion()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engagecost_edit_ver";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			msg.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				_returnMessage = msg.Value.ToString();
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion
	}
}