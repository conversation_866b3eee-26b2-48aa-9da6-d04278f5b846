﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;

public partial class Comp_News : System.Web.UI.UserControl
{
	// --- 加入共用 Function Class --- //
	internal RemoveCheckMax oRCM = new RemoveCheckMax();
	/// <summary>
	/// 取得FAQ系統的系統代碼
	/// </summary>
	public string SysCode
	{
		get
		{
			string syscode = string.Empty;
			if(ViewState["VS_SYSCODE"] != null) 
				syscode = ViewState["VS_SYSCODE"].ToString();
			return Server.HtmlEncode(syscode);
		}
		set
		{
			ViewState["VS_SYSCODE"] = value;
		}
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!IsPostBack)
		{
			SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
			sso.GetEmpInfo();

			#region //訊息公告

			//sds_bulletin.SelectParameters["BB_sys"].DefaultValue = this.SysCode;
			//sds_bulletin.SelectParameters["empno"].DefaultValue = sso.empNo;

			//System.Data.DataView dv = (System.Data.DataView)sds_bulletin.Select(new DataSourceSelectArguments());

			DataTable dt = new DataTable();
			#region --- query ---

			using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
			{
				SqlCommand sqlCmd = new SqlCommand();
				sqlCmd.Connection = sqlConn;
				sqlCmd.CommandType = CommandType.StoredProcedure;

				sqlCmd.CommandText = @"pr_bulletin_window_showing";

				// --- 避免匯出查詢過久而當掉 --- //
				sqlCmd.CommandTimeout = 0;

				sqlCmd.Parameters.Clear();
				sqlCmd.Parameters.AddWithValue("@BB_sys", oRCM.SQLInjectionReplaceAll(this.SysCode));
				sqlCmd.Parameters.AddWithValue("@BB_type", oRCM.SQLInjectionReplaceAll("1"));
				sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(sso.empNo));


				try
				{
					sqlConn.Open();

					SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
					dt = new DataTable();
					sqlDA.Fill(dt);

				}
				catch (Exception ex)
				{

					// --- 執行異常通報 --- //
					RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
						ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString,
						Request,
						Response,
						ex
						);

					oRCM.ErrorExceptionDataToDB(logMail);

				}
				finally
				{
					sqlConn.Close();
				}
			}

			#endregion
			DataView dv = dt.DefaultView;
			if (dv.Count > 0)
			{
				//顯示模式, "S":停機, "Y":顯示, "N":關閉(不顯示)
				string showingMode = dv[0][0].ToString();
				
				if(showingMode != "N")
				{
					string script = string.Format(@"
					$(document).ready(function () {{ 
						doOpenBulletin({0});
					}});", (showingMode != "S").ToString().ToLower());
					ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
				}
			}

			#endregion
		}

    }
}