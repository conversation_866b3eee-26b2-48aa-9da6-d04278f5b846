﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Q_customer.aspx.cs" Inherits="Q_customer" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <base target="_self" />
    <script type="text/javascript" src="../Scripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function close_win() {
            window.opener = null;
            window.open("", "_self");
            window.close();
        }
        //重新調整目前視窗            
        function adjustDims() {
            window.dialogHeight = '' + (window.document.all.tags("body")[0].scrollHeight + 600) + 'px';
            window.dialogWidth = '' + (window.document.all.tags("body")[0].scrollWidth + 350) + 'px';
        }
        //
        function go(url) {
            var a = document.createElement("a");
            a.href = url;
            document.body.appendChild(a);
            a.click();
        }

    </script>
</head>
<body>
    <form id="form1" runat="server">
        <asp:DropDownList ID="DDL_code1" runat="server" AutoPostBack="True"
            DataTextField="code_valuedesc"
            DataValueField="code_value"
            OnSelectedIndexChanged="DDL_code1_SelectedIndexChanged"
            OnDataBound="DDL_code1_DataBound" Enabled="True" Visible="False">
        </asp:DropDownList><br />
        <asp:DropDownList ID="DDL_code2" runat="server" DataTextField="code_valuedesc" DataValueField="code_value" OnDataBound="DDL_code2_DataBound" Enabled="True" Visible="False" />
        關鍵字:<asp:TextBox ID="TB_mp" runat="server" Width="350px"></asp:TextBox>
        <asp:Button ID="BT_search" runat="server" OnClick="BT_search_Click" Text="查詢" />
        &nbsp; 
             <asp:Button ID="btn_Ins" runat="server" OnClick="btn_Ins_Click" Text="新增" Visible="False" />


        <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False"
            CellPadding="4" Width="600px"
            GridLines="None"
            EnableModelValidation="True" AllowPaging="True"
            OnPageIndexChanged="SGV_company_PageIndexChanged"
            OnPageIndexChanging="SGV_company_PageIndexChanging"
            OnRowCreated="SGV_company_RowCreated"
            OnRowCommand="SGV_company_RowCommand">
            <FooterStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
            <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="White" />
            <Columns>
                <asp:BoundField DataField="comp_idno" HeaderText="公司統編" ReadOnly="True">
                    <HeaderStyle HorizontalAlign="Left" />
                    <ItemStyle Width="100px" HorizontalAlign="Left" Font-Size="Small" />
                </asp:BoundField>
                <asp:TemplateField HeaderText="公司名稱">
                    <ItemTemplate>
                        <asp:LinkButton ID="LB_comp" runat="server" CommandName="view_case" CommandArgument='<%# Server.HtmlEncode(Eval("comp_idno").ToString()) %>'>  <%#DataBinder.Eval(Container.DataItem, "comp_cname")%></asp:LinkButton>
                    </ItemTemplate>
                    <HeaderStyle HorizontalAlign="Left" />
                    <ItemStyle HorizontalAlign="Left" Width="500px" Font-Size="Small" />
                </asp:TemplateField>
            </Columns>
            <EmptyDataTemplate>
                <!--當找不到資料時則顯示「查無資料」-->
                <asp:Label ID="Label1" runat="server" ForeColor="Red" Text="查無資料!"></asp:Label>
            </EmptyDataTemplate>
            <FooterStyle BackColor="White" />
            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
            <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="1px" HorizontalAlign="Left" CssClass="PagerStyle" Font-Size="Medium" Font-Overline="False" Font-Bold="True" ForeColor="#FF0066" />
            <CustomPagerSettings TextFormat="<span style='color:#004e98'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#004e98'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#004e98'>筆</span>&nbsp;&nbsp;<span style='color:#004e98'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#004e98'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#004e98'>頁</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" />
            <PagerSettings FirstPageImageUrl="../images/move_first.gif" PreviousPageImageUrl="../images/prev.gif" NextPageImageUrl="../images/next.gif" LastPageImageUrl="../images/move_lest.gif" PageButtonCount="10" />
        </cc1:SmartGridView>


        <%--        <asp:SqlDataSource ID="SDL_code_1" runat="server"  ConnectionString="<%$ ConnectionStrings:pubbs %>"  SelectCommand="SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl WHERE (code_value LIKE '__0') and code_type='001' "></asp:SqlDataSource>
        <asp:SqlDataSource ID="SDL_code_2" runat="server" ConnectionString="<%$ ConnectionStrings:pubbs %>" SelectCommand="SELECT code_value, code_valuedesc FROM visitdb..visit_codetbl where 1=0 "   >  </asp:SqlDataSource>
        <asp:SqlDataSource ID="SDS_jssg" runat="server"  ConnectionString="<%$ ConnectionStrings:pubbs %>" > </asp:SqlDataSource>
        <asp:SqlDataSource ID="SDS_Instblgetcust" runat="server"  ConnectionString="<%$ ConnectionStrings:pubbs %>" > </asp:SqlDataSource>--%>
    </form>

</body>
</html>
