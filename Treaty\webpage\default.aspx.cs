﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class webpage_default : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }

    public bool IsDangerWord(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    inputString = inputString.Replace("--", "－－").Replace("'", "’");
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    private string contnoName  //案件編號/名稱
    {
        get
        {
            string custName = this.TB_contnoName.Text.Trim().Replace("-", "").Replace(" ", "");
            if (Regex.IsMatch(custName.Replace("-", ""), "^[(),-_;，。,@a-zA-Z\u0800-\u9fa5]*$") == false)
                Response.Redirect("../danger.aspx");
            return TB_contnoName.Text.Trim();
        }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;
            ddl_amend.Items.Insert(0, new ListItem("", "0"));
            ViewState["sortorder"] = "";
            ViewState["sortField"] = "";
            BindData_法務人員();
            BindContType();
            BindOrg();
            DoSearch();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }
    private void BindContType()
    {
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable   '' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();

        ddlContType.Items.Clear();

        //SqlDataSource SDS_ContType = new SqlDataSource();
        SDS_ContType.ID = "SDS_ContType";
        SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_ContType.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        SDS_ContType.SelectCommand = "esp_treatyCase_codetable";
        SDS_ContType.SelectParameters.Add("code_group", oRCM.SQLInjectionReplaceAll(""));
        SDS_ContType.SelectParameters.Add("code_type", oRCM.SQLInjectionReplaceAll("10"));
        for (int i = 0; i < SDS_ContType.SelectParameters.Count; i++)
        {
            SDS_ContType.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        SDS_ContType.DataBind();
        //ddlContType.DataSource = SDS_ContType;
        ddlContType.DataBind();
        ddlContType.Items.Insert(0, new ListItem("--請選擇-- ", ""));
        SDS_ContType.Dispose();


        #region --- query ---

        //using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        //{
        //    SqlCommand sqlCmd = new SqlCommand();
        //    sqlCmd.Connection = sqlConn;
        //    sqlCmd.CommandType = CommandType.StoredProcedure;

        //    sqlCmd.CommandText = @"esp_treatyCase_codetable";

        //    // --- 避免匯出查詢過久而當掉 --- //
        //    sqlCmd.CommandTimeout = 0;

        //    sqlCmd.Parameters.Clear();

        //    sqlCmd.Parameters.AddWithValue("@code_group", "");
        //    sqlCmd.Parameters.AddWithValue("@code_type", "10");

        //    try
        //    {
        //        sqlConn.Open();

        //        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
        //        DataTable dt = new DataTable();
        //        sqlDA.Fill(dt);

        //        ddlContType.DataSource = dt;
        //        ddlContType.DataBind();

        //    }
        //    catch (Exception ex)
        //    {

        //        // --- 執行異常通報 --- //
        //        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
        //            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
        //            Request,
        //            Response,
        //            ex
        //            );

        //        oRCM.ErrorExceptionDataToDB(logMail);

        //    }
        //    finally
        //    {
        //        sqlConn.Close();
        //    }
        //}

        #endregion
        ddlContType.Items.Insert(0, new ListItem("--請選擇-- ", ""));



    }
    private void BindOrg()
    {
         
        ddlOrgcd.DataBind();
        ddlOrgcd.Items.Clear();
        //SqlDataSource SDS_Orgcd = new SqlDataSource();

        SDS_Orgcd.SelectParameters.Clear();
        SDS_Orgcd.ID = "SDS_Orgcd";
        SDS_Orgcd.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SDS_Orgcd.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
        SDS_Orgcd.SelectCommand = "exec esp_treaty_search_case_orglist @emp_id ";
        SDS_Orgcd.SelectParameters.Add("emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        for (int i = 0; i < SDS_Orgcd.SelectParameters.Count; i++)
        {
            SDS_Orgcd.SelectParameters[i].ConvertEmptyStringToNull = false;
        }
        SDS_Orgcd.DataBind();
        //ddlOrgcd.DataSource = SDS_Orgcd;
        ddlOrgcd.DataBind();
       // ddlOrgcd.Items.Insert(0, new ListItem("--請選擇-- ", "00"));
        SDS_Orgcd.Dispose();

        #region --- query ---

        //using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        //{
        //    SqlCommand sqlCmd = new SqlCommand();
        //    sqlCmd.Connection = sqlConn;
        //    sqlCmd.CommandType = CommandType.StoredProcedure;

        //    sqlCmd.CommandText = @"esp_treaty_search_case_orglist";

        //    // --- 避免匯出查詢過久而當掉 --- //
        //    sqlCmd.CommandTimeout = 0;

        //    sqlCmd.Parameters.Clear();
        //    sqlCmd.Parameters.AddWithValue("@emp_id", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

        //    try
        //    {
        //        sqlConn.Open();

        //        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
        //        DataTable dt = new DataTable();
        //        sqlDA.Fill(dt);

        //        ddlOrgcd.DataSource = dt;
        //        ddlOrgcd.DataBind();

        //    }
        //    catch (Exception ex)
        //    {

        //        // --- 執行異常通報 --- //
        //        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
        //            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
        //            Request,
        //            Response,
        //            ex
        //            );

        //        oRCM.ErrorExceptionDataToDB(logMail);

        //    }
        //    finally
        //    {
        //        sqlConn.Close();
        //    }
        //}

        #endregion
        ddlOrgcd.Items.Insert(0, new ListItem("--請選擇-- ", "00"));

    }
    private void Binddata(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SGV_search.PageIndex = 0;

        //SqlDataSource SDS_search = new SqlDataSource();
        //SDS_search.ID = "SDS_search";
        //SDS_search.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;

        SDS_search.SelectParameters.Clear();
        SDS_search.SelectCommandType = SqlDataSourceCommandType.Text;
        if (str_sortField == "")
        {
            SDS_search.SelectCommand = " SELECT distinct  * from v_treaty_search_case_2022 where tmp_uid =@tmp_uid  and tmp_guid= @tmp_guid  order by tc_seno desc  ";
        }
        else
        {
            SDS_search.SelectCommand = " SELECT distinct  * from v_treaty_search_case_2022 where tmp_uid =@tmp_uid  and tmp_guid= @tmp_guid  order by  " + oRCM.SQLInjectionReplaceAll(str_sortField) + " " + str_sort;
        }
        SDS_search.SelectParameters.Add("tmp_uid", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        SDS_search.SelectParameters.Add("tmp_guid", oRCM.SQLInjectionReplaceAll(ViewState["guid"].ToString()));
        SDS_search.DataBind();
        //SGV_search.DataSource = SDS_search;
        SGV_search.DataBind();
        SDS_search.Dispose();
    }

    private void BindData_法務人員()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_default_法務人員";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_法務 = dt.DefaultView;
        if (dv_法務.Count >= 1 )
        {
            Response.Redirect("Search_inner.aspx");
        }
 
    }

    private void DoSearch()
    {
        if (ddlContType.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (ddlOrgcd.SelectedValue.Length > 4) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlContType.SelectedValue) || (ddlContType.SelectedValue.Length > 4)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(ddlOrgcd.SelectedValue) || (ddlOrgcd.SelectedValue.Length > 2)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxCompName.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxHandleName.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxPromoterName.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxReqDept.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(tbxReqDept.Text)) Response.Redirect("../danger.aspx");
        if (IsDangerWord(txtKeyWord.Text)) Response.Redirect("../danger.aspx");
        string str_class = "";
        str_class += (cbxCaseClass.Items[0].Selected == true) ? cbxCaseClass.Items[0].Value : "";
        str_class += (cbxCaseClass.Items[1].Selected == true) ? cbxCaseClass.Items[1].Value : "";
        str_class += (cbxCaseClass.Items[2].Selected == true) ? cbxCaseClass.Items[2].Value : "";
        str_class += (cbxCaseClass.Items[3].Selected == true) ? cbxCaseClass.Items[3].Value : "";
        str_class += (cbxCaseClass.Items[4].Selected == true) ? cbxCaseClass.Items[4].Value : "";
        str_class += (cbxCaseClass.Items[5].Selected == true) ? cbxCaseClass.Items[5].Value : "";
        str_class += (cbxCaseClass.Items[6].Selected == true) ? cbxCaseClass.Items[6].Value : "";
        //str_class += (cbxCaseClass.Items[7].Selected == true) ? cbxCaseClass.Items[7].Value : "";
        // str_class += (cbxCaseClass.Items[8].Selected == true) ? cbxCaseClass.Items[8].Value : "";
        if (IsDangerWord(str_class)) Response.Redirect("../danger.aspx");
        string str_CaseStatus = "";
        DataSet ds = new DataSet();
        ViewState["guid"] = Guid.NewGuid().ToString();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_search_case_2022";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("orgcd", oRCM.SQLInjectionReplaceAll(ddlOrgcd.SelectedValue)); //將舊案的流水號存入
            sqlCmd.Parameters.AddWithValue("conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue));//洽案(契約)名稱
            sqlCmd.Parameters.AddWithValue("company", oRCM.SQLInjectionReplaceAll(tbxCompName.Text));
            sqlCmd.Parameters.AddWithValue("handle_name", oRCM.SQLInjectionReplaceAll(tbxHandleName.Text));
            sqlCmd.Parameters.AddWithValue("promoter_name", oRCM.SQLInjectionReplaceAll(tbxPromoterName.Text));
            sqlCmd.Parameters.AddWithValue("class", oRCM.SQLInjectionReplaceAll(str_class.Replace(",,", ",")));
            sqlCmd.Parameters.AddWithValue("tc_degree", oRCM.SQLInjectionReplaceAll(str_CaseStatus));
            sqlCmd.Parameters.AddWithValue("req_dept", oRCM.SQLInjectionReplaceAll(tbxReqDept.Text));
            sqlCmd.Parameters.AddWithValue("kw", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
            sqlCmd.Parameters.AddWithValue("top_one", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
            sqlCmd.Parameters.AddWithValue("is_amend", oRCM.SQLInjectionReplaceAll(txtKeyWord.Text));
            sqlCmd.Parameters.AddWithValue("contnoName", oRCM.SQLInjectionReplaceAll(contnoName));
            sqlCmd.Parameters.AddWithValue("guid", oRCM.SQLInjectionReplaceAll(ViewState["guid"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
                //SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                //sqlDA.Fill(ds, "myTable");

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
            SGV_search.PageIndex = 0;
        }
        #endregion
    }
    protected void btnQuery_Click(object sender, EventArgs e)
    {
        DoSearch();
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void btnQuery1_Click(object sender, EventArgs e)
    {
        txtKeyWord.Text = "";
        DoSearch();
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            StringBuilder script;
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            switch (arg[1].Substring(6, 1))
            {
                case "A":
                case "C":
                case "N":
                case "F":
                case "M":
                case "R":
                case "S":
                case "T":
                    Response.Redirect("./TreatyCase_view.aspx?seno=" + arg[0]);
                    break;
                default:
                    script = new StringBuilder("<script type='text/javascript'> alert('請暫時由舊系統內部查詢進入');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
            }
        }

        if (e.CommandName == "New")
        {
            StringBuilder script;
            string[] arg = new string[2];
            arg = e.CommandArgument.ToString().Split(';');
            switch (arg[1].Substring(6, 1))
            {
                case "A":
                case "C":
                case "N":
                case "M":
                case "R":
                case "S":
                case "F":
                case "T":
                    script = new StringBuilder("<script type='text/javascript'>doNewCase('" + arg[0] + "','" + arg[1] + "');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
                default:
                    script = new StringBuilder("<script type='text/javascript'> alert('請暫時由舊系統內部查詢進入');</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
                    break;
            }
        }

    }
    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal lb = (Literal)e.Row.FindControl("LB_tmp_status_flag");

        Label lb_assign_day = (Label)e.Row.FindControl("LB_assign_day");
        Label lB_closedate = (Label)e.Row.FindControl("LB_closedate");
        //if (lb_assign_day != null)
        //{
        //    if (lb_assign_day.Text.Trim() !="")
        //        lb_assign_day.Text = String.Format("{0:yyyy/MM/dd}", Convert.ToDateTime(lb_assign_day.Text));

        //    if (lB_closedate.Text.Trim() != "")
        //        lB_closedate.Text = String.Format("{0:yyyy/MM/dd}", lB_closedate.Text); 
        //}

        if ((lb != null) && (lb.Text != "7"))
        {
            string sz_actno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_case_actno"));
            string sz_seno = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tc_seno"));
            string sz_class = sz_actno.Substring(6, 1);

            Literal lt_newVer = (Literal)e.Row.FindControl("LT_newVer");
            if ((Convert.ToString(DataBinder.Eval(e.Row.DataItem, "tmp_new_version")) != "0"))
            {
                switch (sz_class)
                {
                    case "A":
                    case "C":
                    case "N":
                    case "M":
                    case "R":
                    case "S":
                    case "F":
                    case "T":
                        lt_newVer.Text = "<input type='image'  Class='ajax_mesg' src='../images/text.gif' onclick='doNewCase(\"" + Server.HtmlEncode(oRCM.RemoveXss(sz_seno)) + "\",\"" + Server.HtmlEncode(oRCM.RemoveXss(sz_actno)) + "\");' />";
                        break;
                    default:
                        lt_newVer.Text = "<input type='image'  src='../images/text.gif' onclick='alert(\"請暫時由舊系統內部查詢進入\");' />";
                        break;
                }
            }
        }

        //switch (e.Row.RowType)
        //{
        //    case DataControlRowType.Header:
        //        break;
        //    case DataControlRowType.DataRow:
        //        if (e.Row.RowIndex > -1)
        //        {
        //            if (e.Row.RowIndex % 2 == 0)
        //            {
        //                e.Row.Attributes.Add("onmouseover", "this.style.backgroundColor='#84AAEF';this.style.color='white'");
        //                e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor='#F7F6F3';this.style.color='#333333'");
        //            }
        //            else 
        //            {
        //                e.Row.Attributes.Add("onmouseover", "this.style.backgroundColor='#84AAEF';this.style.color='white'");
        //                e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor='#F7F6F3';this.style.color='#333333'");
        //            }
        //        }
        //        break;
        //}
    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }
        }
    }
    protected void DDL_CaseStatus1_SelectedIndexChanged(object sender, EventArgs e)
    {

    }
    protected void DDL_CaseStatus_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (DDL_CaseStatus.SelectedValue != "4")
        {
            DDL_CaseStatus1.SelectedValue = DDL_CaseStatus.SelectedValue;
            DoSearch();
            SGV_search.PageIndex = 0;
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
        else
            Response.Redirect("./search_draft.aspx");
    }


}