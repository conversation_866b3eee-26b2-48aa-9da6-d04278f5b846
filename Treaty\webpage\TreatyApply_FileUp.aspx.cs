﻿using Aspose.Words;
using Newtonsoft.Json.Linq;
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;
public partial class TreatyApply_FileUp : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    private string seno
    {
        get
        {
            string querySeno = Request.QueryString["seno"];
            if (string.IsNullOrEmpty(querySeno) || !IsNumber(querySeno) || querySeno.Length > 7)
            {
                RedirectToDanger();
            }
            return Server.HtmlEncode(oRCM.RemoveXss(GenerateSafeStr(querySeno)));
        }
    }
    private void RedirectToDanger()
    {
        Response.Redirect("../danger.aspx");
    }


    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            ViewState["contno"] = "";
            ViewState["orgcd"] = "";
            ViewState["class"] = "";
            if (Request.QueryString["contno"] != null)
                ViewState["contno"] = Request.QueryString["contno"].ToString();

            InitPage();
        }
    }

    private void InitPage()
    {
        //SDS_base.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_base.SelectParameters.Clear();
        //SDS_base.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_base.SelectCommand = " select tr_orgcd,tr_class   from treaty_requisition where  tr_seno = @seno ";
        //SDS_base.SelectParameters.Add("seno", SQLInjectionReplaceAll(seno));
        //for (int i = 0; i < this.SDS_base.SelectParameters.Count; i++)
        //{
        //    SDS_base.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_base.DataBind();
        //System.Data.DataView dv= (DataView)SDS_base.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select tr_orgcd,tr_class   from treaty_requisition where  tr_seno = @seno ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(seno));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            ViewState["orgcd"] = dv[0]["tr_orgcd"].ToString().Trim();
            ViewState["class"] = dv[0]["tr_class"].ToString().Trim();
        }

    }

    private bool IsFileExists(String sFilename)
    {
        //App_Data目錄
        //string dirPath = Server.MapPath(dirPath);
        //DirectoryInfo dir = new DirectoryInfo(@dirPath);
        //列舉全部檔案再比對檔名

        FileInfo file = new FileInfo(sFilename);

        if (file != null && file.Exists)//檔案存在的話
        {
            //原本預期下載Excel檔
            return true;
        }
        else
        {
            return false;
        }
    }

    private bool IsFileReady(String sFilename)
    {
        try
        {
            using (FileStream inputStream = System.IO.File.Open(sFilename, FileMode.Open, FileAccess.Read, FileShare.None))
            {
                if (inputStream.Length > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
        catch (Exception)
        {
            return false;
        }
    }

    private bool IsDirExists(string rootPath, string dirName)
    {
        string dirPath = Path.Combine(rootPath, dirName).Replace("..", "");
        string[] dirs = Directory.GetDirectories(rootPath);
        string result = dirs.Where(s => s.Equals(dirPath
            , StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();

        return result != null;
    }


    private readonly string[] allowedExtensions =
    {
    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
    ".odt", ".ods", ".pdf", ".jpg", ".gif", ".txt",
    ".png", ".zip"
    };


    public bool ChkFileExtension(string fileExtension)
    {
        return Array.Exists(allowedExtensions, ext =>
            string.Equals(fileExtension, ext, StringComparison.OrdinalIgnoreCase));
    }


    protected void BT_Save_Click(object sender, EventArgs e)
    {
        //ScriptManager.RegisterStartupScript(Page, Page.GetType(), "showProgress", "<script type='text/javascript'>showProgress();</script>", false);        
        // 輸入驗證
        if (string.IsNullOrEmpty(seno) || !IsNumber(seno) || seno.Length > 7 )
        {
            RedirectToDanger();
            return;
        }

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        //string path = string.Format("{0}\\{1}\\", FilePathString, ViewState["contno"].ToString().Substring(0, 4));
        string path = oRCM.GetValidPathPart(FilePathString, DateTime.Now.Year.ToString());

        DirectoryInfo dir = new DirectoryInfo(FilePathString);
        if (!dir.Exists)
            Directory.CreateDirectory(path);


        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);
        string FileExtension = System.IO.Path.GetExtension(FU_up.FileName);//副檔名

        if (!ChkFileExtension(FileExtension))
        {
            string script = string.Format(@" $(document).ready(function ()  {{  alert('不允許上傳副檔名為 {0} 的檔案！'); }}); ", FileExtension);
            ClientScript.RegisterStartupScript(this.GetType(), "ErrorMessage", script, true);
            return;
        }


        if (FU_up.PostedFile.ContentLength > 0)
        {
            //if (!Directory.Exists(path.Replace("/", "").Replace("..", "")))
            //if (IsDirExists(FilePathString, ViewState["contno"].ToString().Substring(0, 4)) == false)
            //{
            //    Directory.CreateDirectory(path.Replace("\\\\", "\\").Replace("..", ""));
            //}
            string upFileName = Server.HtmlEncode(oRCM.RemoveXss(FU_up.FileName));

            //string filenameNoExt = System.IO.Path.GetFileNameWithoutExtension(FU_up.FileName);//檔名不包含副檔名          
            //string txtUserFileName = string.Format("\\RE_{0}_{1}_", Server.HtmlEncode(oRCM.RemoveXss(seno)), strPreRandom);//使用者自訂義名稱


            //如果畫面textbox沒有指定檔案名稱 就使用原始的檔案名稱
            //string randomstr = "RE_" + ViewState["seno"].ToString() + "_" + strPreRandom + "_";
            try
            {
                string fileName = string.Format("RE_{0}_{1}_{2}{3}",
                                    Server.HtmlEncode(oRCM.RemoveXss(seno)),
                                    strPreRandom,
                                    GenerateSafeStr(Path.GetFileNameWithoutExtension(upFileName)),
                                    GenerateSafeStr(Path.GetExtension(upFileName)));

                string filePath = ResolveUrl(Server.HtmlEncode(Path.Combine(path, Server.HtmlEncode(oRCM.RemoveXss((fileName))))));

                //string destinationFilePath = Server.HtmlEncode(Path.Combine(Server.MapPath(path), Server.HtmlEncode(oRCM.RemoveXss((fileName)))));

                string FilePathExtension = System.IO.Path.GetExtension(filePath);//副檔名

                if (!ChkFileExtension(FilePathExtension))
                {
                    string script = string.Format(@" $(document).ready(function ()  {{  alert('不允許上傳副檔名為 {0} 的檔案！'); }}); ", FilePathExtension);
                    ClientScript.RegisterStartupScript(this.GetType(), "ErrorMessage", script, true);
                    return;
                }
                if (IsSafeFile(filePath))
                {
                    string filetmp_path = Server.MapPath("~/tmpFile/");
                    DirectoryInfo dirTemp = new DirectoryInfo(filetmp_path);
                    string tmpFileName = Server.HtmlEncode(oRCM.RemoveXss((string.Format("{0}{1}", filetmp_path, fileName))));
                    if (!dirTemp.Exists)
                        Directory.CreateDirectory(filetmp_path);

                    FU_up.SaveAs(tmpFileName);
                    File.Copy(tmpFileName, filePath, true);
                    File.Delete(tmpFileName);
                }
                else
                {
                    string script = string.Format(@" $(document).ready(function ()  {{  alert('發現不安全的路徑，將導向至安全頁面！'); }}); ");
                    ClientScript.RegisterStartupScript(this.GetType(), "AlertMessage", script, true);
                    Response.Redirect("../danger.aspx");
                }
               
                string str_alert = "";


                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;
                    sqlCmd.CommandText = @"esp_TreatyApply_file_modify";
                    sqlCmd.CommandTimeout = 0;
                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@req_id", oRCM.SQLInjectionReplaceAll(seno));
                    sqlCmd.Parameters.AddWithValue("@fd_name", oRCM.SQLInjectionReplaceAll(upFileName));
                    sqlCmd.Parameters.AddWithValue("@filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                    sqlCmd.Parameters.AddWithValue("@file_url", oRCM.SQLInjectionReplaceAll(string.Format("{0}\\{1}", path, fileName)));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    sqlCmd.Parameters.AddWithValue("@mode", "Ins");
                    sqlCmd.Parameters.AddWithValue("@fid", "");
                    sqlCmd.Parameters.AddWithValue("@ITRIDocID", oRCM.SQLInjectionReplaceAll(""));
                    sqlCmd.Parameters.AddWithValue("@ITRIRev", oRCM.SQLInjectionReplaceAll(""));
                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );
                        oRCM.ErrorExceptionDataToDB(logMail);
                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                StringBuilder script_over = new StringBuilder("<script type='text/javascript'>alert('" + str_alert + "\\n檔案上傳完成!'); parent.$.fn.colorbox.close();</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);


            }
            catch (Exception ex)
            {
                StringBuilder script_over = new StringBuilder("<script type='text/javascript'> alert('檔案上傳失敗，請重新上傳!'); </script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);
            }

        }

    }

    private string GenerateSafeStr(string str)
    {
        // 清理上傳的文件名，防止目錄穿越攻擊
        string safeFileNameWithoutExtension = str
            .Replace("..", "")
            .Replace("/", "")
            .Replace("\\", "")
            .Replace(":", "")
            .Replace("*", "")
            .Replace("?", "")
            .Replace("\"", "")
            .Replace("<", "")
            .Replace(">", "")
            .Replace("|", "");
        return safeFileNameWithoutExtension;
    }


    /// <summary>
    ///雙重副檔名
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public bool IsSafeFile(string filePath)
    {
        // 抓取主副檔名
        string fileExtension = Path.GetExtension(filePath);
        if (string.IsNullOrEmpty(fileExtension)) return false;

        // 檢查主副檔名是否合法
        if (!ChkFileExtension(fileExtension)) return false;

        // 防範潛在危險的中間檔名部分
        string fileNameWithoutExt = Path.GetFileNameWithoutExtension(filePath);
        if (!string.IsNullOrEmpty(fileNameWithoutExt) &&
        (fileNameWithoutExt.IndexOf(".exe", StringComparison.OrdinalIgnoreCase) >= 0 ||
         fileNameWithoutExt.IndexOf(".bat", StringComparison.OrdinalIgnoreCase) >= 0 ||
         fileNameWithoutExt.IndexOf(".js", StringComparison.OrdinalIgnoreCase) >= 0))
        {
            // 發現不安全的副檔名
            return false;
        }

        return true; // 符合安全規範
    }




}