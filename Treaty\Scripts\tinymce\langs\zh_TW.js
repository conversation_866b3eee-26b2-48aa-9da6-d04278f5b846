tinymce.addI18n('zh_TW',{
"Cut": "\u526a\u4e0b",
"Heading 5": "\u6a19\u984c 5",
"Header 2": "\u6a19\u984c 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u60a8\u7684\u700f\u89bd\u5668\u4e0d\u652f\u63f4\u5b58\u53d6\u526a\u8cbc\u7c3f\uff0c\u53ef\u4ee5\u4f7f\u7528\u5feb\u901f\u9375 Ctrl + X\/C\/V \u4ee3\u66ff\u526a\u4e0b\u3001\u8907\u88fd\u8207\u8cbc\u4e0a\u3002",
"Heading 4": "\u6a19\u984c 4",
"Div": "Div",
"Heading 2": "\u6a19\u984c 2",
"Paste": "\u8cbc\u4e0a",
"Close": "\u95dc\u9589",
"Font Family": "\u5b57\u9ad4",
"Pre": "Pre",
"Align right": "\u7f6e\u53f3\u5c0d\u9f4a",
"New document": "\u65b0\u6587\u4ef6",
"Blockquote": "\u5f15\u7528",
"Numbered list": "\u6578\u5b57\u6e05\u55ae",
"Heading 1": "\u6a19\u984c 1",
"Headings": "\u6a19\u984c",
"Increase indent": "\u589e\u52a0\u7e2e\u6392",
"Formats": "\u683c\u5f0f",
"Headers": "\u6a19\u984c",
"Select all": "\u5168\u9078",
"Header 3": "\u6a19\u984c 3",
"Blocks": "\u5340\u584a",
"Undo": "\u5fa9\u539f",
"Strikethrough": "\u522a\u9664\u7dda",
"Bullet list": "\u9805\u76ee\u6e05\u55ae",
"Header 1": "\u6a19\u984c 1",
"Superscript": "\u4e0a\u6a19",
"Clear formatting": "\u6e05\u9664\u683c\u5f0f",
"Font Sizes": "\u5b57\u578b\u5927\u5c0f",
"Subscript": "\u4e0b\u6a19",
"Header 6": "\u6a19\u984c 6",
"Redo": "\u53d6\u6d88\u5fa9\u539f",
"Paragraph": "\u6bb5\u843d",
"Ok": "\u78ba\u5b9a",
"Bold": "\u7c97\u9ad4",
"Code": "\u7a0b\u5f0f\u78bc",
"Italic": "\u659c\u9ad4",
"Align center": "\u7f6e\u4e2d\u5c0d\u9f4a",
"Header 5": "\u6a19\u984c 5",
"Heading 6": "\u6a19\u984c 6",
"Heading 3": "\u6a19\u984c 3",
"Decrease indent": "\u6e1b\u5c11\u7e2e\u6392",
"Header 4": "\u6a19\u984c 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u76ee\u524d\u5c07\u4ee5\u7d14\u6587\u5b57\u7684\u6a21\u5f0f\u8cbc\u4e0a\uff0c\u60a8\u53ef\u4ee5\u518d\u9ede\u9078\u4e00\u6b21\u53d6\u6d88\u3002",
"Underline": "\u5e95\u7dda",
"Cancel": "\u53d6\u6d88",
"Justify": "\u5de6\u53f3\u5c0d\u9f4a",
"Inline": "Inline",
"Copy": "\u8907\u88fd",
"Align left": "\u7f6e\u5de6\u5c0d\u9f4a",
"Visual aids": "\u5c0f\u5e6b\u624b",
"Lower Greek": "\u5e0c\u81d8\u5b57\u6bcd",
"Square": "\u6b63\u65b9\u5f62",
"Default": "\u9810\u8a2d",
"Lower Alpha": "\u5c0f\u5beb\u82f1\u6587\u5b57\u6bcd",
"Circle": "\u7a7a\u5fc3\u5713",
"Disc": "\u5be6\u5fc3\u5713",
"Upper Alpha": "\u5927\u5beb\u82f1\u6587\u5b57\u6bcd",
"Upper Roman": "\u5927\u5beb\u7f85\u99ac\u6578\u5b57",
"Lower Roman": "\u5c0f\u5beb\u7f85\u99ac\u6578\u5b57",
"Name": "\u540d\u7a31",
"Anchor": "\u52a0\u5165\u9328\u9ede",
"You have unsaved changes are you sure you want to navigate away?": "\u7de8\u8f2f\u5c1a\u672a\u88ab\u5132\u5b58\uff0c\u4f60\u78ba\u5b9a\u8981\u96e2\u958b\uff1f",
"Restore last draft": "\u8f09\u5165\u4e0a\u4e00\u6b21\u7de8\u8f2f\u7684\u8349\u7a3f",
"Special character": "\u7279\u6b8a\u5b57\u5143",
"Source code": "\u539f\u59cb\u78bc",
"B": "\u85cd",
"R": "\u7d05",
"G": "\u7da0",
"Color": "\u984f\u8272",
"Right to left": "\u5f9e\u53f3\u5230\u5de6",
"Left to right": "\u5f9e\u5de6\u5230\u53f3",
"Emoticons": "\u8868\u60c5",
"Robots": "\u6a5f\u5668\u4eba",
"Document properties": "\u6587\u4ef6\u7684\u5c6c\u6027",
"Title": "\u6a19\u984c",
"Keywords": "\u95dc\u9375\u5b57",
"Encoding": "\u7de8\u78bc",
"Description": "\u63cf\u8ff0",
"Author": "\u4f5c\u8005",
"Fullscreen": "\u5168\u87a2\u5e55",
"Horizontal line": "\u6c34\u5e73\u7dda",
"Horizontal space": "\u5bec\u5ea6",
"Insert\/edit image": "\u63d2\u5165\/\u7de8\u8f2f \u5716\u7247",
"General": "\u4e00\u822c",
"Advanced": "\u9032\u968e",
"Source": "\u5716\u7247\u7db2\u5740",
"Border": "\u908a\u6846",
"Constrain proportions": "\u7b49\u6bd4\u4f8b\u7e2e\u653e",
"Vertical space": "\u9ad8\u5ea6",
"Image description": "\u5716\u7247\u63cf\u8ff0",
"Style": "\u6a23\u5f0f",
"Dimensions": "\u5c3a\u5bf8",
"Insert image": "\u63d2\u5165\u5716\u7247",
"Zoom in": "\u653e\u5927",
"Contrast": "\u5c0d\u6bd4",
"Back": "\u5f8c\u9000",
"Gamma": "\u4f3d\u99ac\u503c",
"Flip horizontally": "\u6c34\u5e73\u7ffb\u8f49",
"Resize": "\u8abf\u6574\u5927\u5c0f",
"Sharpen": "\u92b3\u5316",
"Zoom out": "\u7e2e\u5c0f",
"Image options": "\u5716\u7247\u9078\u9805",
"Apply": "\u61c9\u7528",
"Brightness": "\u4eae\u5ea6",
"Rotate clockwise": "\u9806\u6642\u91dd\u65cb\u8f49",
"Rotate counterclockwise": "\u9006\u6642\u91dd\u65cb\u8f49",
"Edit image": "\u7de8\u8f2f\u5716\u7247",
"Color levels": "\u984f\u8272\u5c64\u6b21",
"Crop": "\u88c1\u526a",
"Orientation": "\u65b9\u5411",
"Flip vertically": "\u5782\u76f4\u7ffb\u8f49",
"Invert": "\u53cd\u8f49",
"Insert date\/time": "\u63d2\u5165 \u65e5\u671f\/\u6642\u9593",
"Remove link": "\u79fb\u9664\u9023\u7d50",
"Url": "\u7db2\u5740",
"Text to display": "\u986f\u793a\u6587\u5b57",
"Anchors": "\u52a0\u5165\u9328\u9ede",
"Insert link": "\u63d2\u5165\u9023\u7d50",
"New window": "\u53e6\u958b\u8996\u7a97",
"None": "\u7121",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u4f60\u6240\u586b\u5beb\u7684URL\u5c6c\u65bc\u5916\u90e8\u93c8\u63a5\uff0c\u9700\u8981\u52a0\u4e0ahttp:\/\/:\u524d\u7db4\u55ce\uff1f",
"Target": "\u958b\u555f\u65b9\u5f0f",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u4f60\u6240\u586b\u5beb\u7684URL\u70ba\u96fb\u5b50\u90f5\u4ef6\uff0c\u9700\u8981\u52a0\u4e0amailto:\u524d\u7db4\u55ce\uff1f",
"Insert\/edit link": "\u63d2\u5165\/\u7de8\u8f2f\u9023\u7d50",
"Insert\/edit video": "\u63d2\u4ef6\/\u7de8\u8f2f \u5f71\u97f3",
"Poster": "\u9810\u89bd\u5716\u7247",
"Alternative source": "\u66ff\u4ee3\u5f71\u97f3",
"Paste your embed code below:": "\u8acb\u5c07\u60a8\u7684\u5d4c\u5165\u5f0f\u7a0b\u5f0f\u78bc\u8cbc\u5728\u4e0b\u9762:",
"Insert video": "\u63d2\u5165\u5f71\u97f3",
"Embed": "\u5d4c\u5165\u78bc",
"Nonbreaking space": "\u4e0d\u5206\u884c\u7684\u7a7a\u683c",
"Page break": "\u5206\u9801",
"Paste as text": "\u4ee5\u7d14\u6587\u5b57\u8cbc\u4e0a",
"Preview": "\u9810\u89bd",
"Print": "\u5217\u5370",
"Save": "\u5132\u5b58",
"Could not find the specified string.": "\u7121\u6cd5\u67e5\u8a62\u5230\u6b64\u7279\u5b9a\u5b57\u4e32",
"Replace": "\u66ff\u63db",
"Next": "\u4e0b\u4e00\u500b",
"Whole words": "\u6574\u500b\u55ae\u5b57",
"Find and replace": "\u5c0b\u627e\u53ca\u53d6\u4ee3",
"Replace with": "\u66f4\u63db",
"Find": "\u641c\u5c0b",
"Replace all": "\u66ff\u63db\u5168\u90e8",
"Match case": "\u76f8\u5339\u914d\u6848\u4ef6",
"Prev": "\u4e0a\u4e00\u500b",
"Spellcheck": "\u62fc\u5b57\u6aa2\u67e5",
"Finish": "\u5b8c\u6210",
"Ignore all": "\u5ffd\u7565\u6240\u6709",
"Ignore": "\u5ffd\u7565",
"Add to Dictionary": "\u52a0\u5165\u5b57\u5178\u4e2d",
"Insert row before": "\u63d2\u5165\u5217\u5728...\u4e4b\u524d",
"Rows": "\u5217",
"Height": "\u9ad8\u5ea6",
"Paste row after": "\u8cbc\u4e0a\u5217\u5728...\u4e4b\u5f8c",
"Alignment": "\u5c0d\u9f4a",
"Border color": "\u908a\u6846\u984f\u8272",
"Column group": "\u6b04\u4f4d\u7fa4\u7d44",
"Row": "\u5217",
"Insert column before": "\u63d2\u5165\u6b04\u4f4d\u5728...\u4e4b\u524d",
"Split cell": "\u5206\u5272\u5132\u5b58\u683c",
"Cell padding": "\u5132\u5b58\u683c\u7684\u908a\u8ddd",
"Cell spacing": "\u5132\u5b58\u683c\u5f97\u9593\u8ddd",
"Row type": "\u884c\u7684\u985e\u578b",
"Insert table": "\u63d2\u5165\u8868\u683c",
"Body": "\u4e3b\u9ad4",
"Caption": "\u8868\u683c\u6a19\u984c",
"Footer": "\u9801\u5c3e",
"Delete row": "\u522a\u9664\u5217",
"Paste row before": "\u8cbc\u4e0a\u5217\u5728...\u4e4b\u524d",
"Scope": "\u7bc4\u570d",
"Delete table": "\u522a\u9664\u8868\u683c",
"H Align": "\u6c34\u5e73\u4f4d\u7f6e",
"Top": "\u7f6e\u9802",
"Header cell": "\u6a19\u982d\u5132\u5b58\u683c",
"Column": "\u884c",
"Row group": "\u5217\u7fa4\u7d44",
"Cell": "\u5132\u5b58\u683c",
"Middle": "\u7f6e\u4e2d",
"Cell type": "\u5132\u5b58\u683c\u7684\u985e\u578b",
"Copy row": "\u8907\u88fd\u5217",
"Row properties": "\u5217\u5c6c\u6027",
"Table properties": "\u8868\u683c\u5c6c\u6027",
"Bottom": "\u7f6e\u5e95",
"V Align": "\u5782\u76f4\u4f4d\u7f6e",
"Header": "\u6a19\u982d",
"Right": "\u53f3\u908a",
"Insert column after": "\u63d2\u5165\u6b04\u4f4d\u5728...\u4e4b\u5f8c",
"Cols": "\u6b04\u4f4d\u6bb5",
"Insert row after": "\u63d2\u5165\u5217\u5728...\u4e4b\u5f8c",
"Width": "\u5bec\u5ea6",
"Cell properties": "\u5132\u5b58\u683c\u5c6c\u6027",
"Left": "\u5de6\u908a",
"Cut row": "\u526a\u4e0b\u5217",
"Delete column": "\u522a\u9664\u884c",
"Center": "\u4e2d\u9593",
"Merge cells": "\u5408\u4f75\u5132\u5b58\u683c",
"Insert template": "\u63d2\u5165\u6a23\u7248",
"Templates": "\u6a23\u7248",
"Background color": "\u80cc\u666f\u984f\u8272",
"Custom...": "\u81ea\u8a02",
"Custom color": "\u81ea\u8a02\u984f\u8272",
"No color": "No color",
"Text color": "\u6587\u5b57\u984f\u8272",
"Show blocks": "\u986f\u793a\u5340\u584a\u8cc7\u8a0a",
"Show invisible characters": "\u986f\u793a\u96b1\u85cf\u5b57\u5143",
"Words: {0}": "\u5b57\u6578\uff1a{0}",
"Insert": "\u63d2\u5165",
"File": "\u6a94\u6848",
"Edit": "\u7de8\u8f2f",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u8c50\u5bcc\u7684\u6587\u672c\u5340\u57df\u3002\u6309ALT-F9\u524d\u5f80\u4e3b\u9078\u55ae\u3002\u6309ALT-F10\u547c\u53eb\u5de5\u5177\u6b04\u3002\u6309ALT-0\u5c0b\u6c42\u5e6b\u52a9",
"Tools": "\u5de5\u5177",
"View": "\u6aa2\u8996",
"Table": "\u8868\u683c",
"Format": "\u683c\u5f0f"
});