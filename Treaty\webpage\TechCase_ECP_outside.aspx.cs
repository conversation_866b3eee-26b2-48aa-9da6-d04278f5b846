﻿using NPOI.OpenXmlFormats.Wordprocessing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Treaty;

public partial class TechCase_ECP_outside : common
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public List<SqlParameter> sqlParamList = new List<SqlParameter>();

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }

    public string s_seno
    {
        set { ViewState["tt_seno"] = value; }
        get
        {
            if (ViewState["tt_seno"] == null)
            {
                if (Request.QueryString["tt_seno"] != null)
                {
                    if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                        Response.Redirect("../danger.aspx");
                    ViewState["tt_seno"] = oRCM.SQLInjectionReplaceAll(Request.QueryString["tt_seno"].ToString());
                }
            }

            return ViewState["tt_seno"].ToString();
        }
    }

    private bool _sAuth;

    public bool sAuth
    {
        get { return _sAuth; }
        set { _sAuth = value; }
    }


    private void doAuth()
    {
        sAuth = getAuth();
    }

    private string ecp_guid;

    public string Ecp_guid
    {
        get { return ecp_guid; }
        set { ecp_guid = value; }
    }



    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            doAuth();
            BindUI();
            BindData();
            BT_sign.Visible = sAuth;
            LB_newVer.Visible = !sAuth;
        }
    }

    private void BindUI()
    {
        DataTable dt = new DataTable();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(s_seno)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("list_ver")));
        dt = get_SP();
        ddl_version.DataSource = dt;
        ddl_version.DataTextField = "Text";
        ddl_version.DataValueField = "Value";
        ddl_version.DataBind();

    }

    private void BindData()
    {
        try
        {
            if (Html())
            {
                sqlParamList.Clear();
                sqlParamList.Add(new SqlParameter("signGUID ", oRCM.SQLInjectionReplaceAll(ddl_version.SelectedValue)));
                sqlParamList.Add(new SqlParameter("mode", oRCM.SQLInjectionReplaceAll("list_sign_his")));
                DataTable dt = new DataTable();
                dt = get_SP();
                if (dt.Rows.Count > 0)
                {
                    gv_Data.DataSource = dt;
                    gv_Data.DataBind();
                }
                else
                {
                    gv_Data.DataSource = null;
                    gv_Data.DataBind();
                }
            }
        }
        catch (Exception ex)
        {
            string errorMessage = "發生錯誤：" + ex.Message.Replace("'", "\\'");
            string script = String.Format("alert('{0}');", errorMessage);
            ScriptManager.RegisterStartupScript(this, this.GetType(), "alert", script, true);
        }
    }

    protected void BT_sign_Click(object sender, EventArgs e)
    {
        string sVer = ViewState["ver"] != null ? ViewState["ver"].ToString() : "";
        string url = string.Format("TechCase_ECP.aspx?seno={0}&ver={1}", Server.UrlEncode(s_seno), Server.UrlEncode(sVer));
        Response.Redirect(url);

    }

    protected void BT_detail_Click(object sender, EventArgs e)
    {
        string url = string.Format("TechCase_ECP_showdoc.aspx?seno={0}&guid={1}", Server.UrlEncode(s_seno), Server.UrlEncode(ddl_version.SelectedValue));
        Response.Redirect(url);
    }
	
    protected void btnPrint_Click(object sender, EventArgs e)
    {
        Ecp_guid = ddl_version.SelectedValue;
        string url = string.Format("TechCase_ECP_print.aspx?guid={0}", Server.UrlEncode(Ecp_guid));
        Response.Redirect(url);
    }
	
    protected void ddl_version_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindData();
    }

    protected void gv_Data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowIndex < 0)
            return;
        //string strUrl = System.Web.Configuration.WebConfigurationManager.AppSettings["url.電子簽核"].ToString();
        //LinkButton LB_收件人員 = (LinkButton)e.Row.FindControl("LB_收件人員");
        //LB_收件人員.OnClientClick = "window.open('" + strUrl + "');";
    }



    public bool getAuth()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        string sAuth = string.Empty;
        DataTable dt = new DataTable();
        bool bAuth = false;

        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(s_seno)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("check_otherP")));
        dt = get_SP();

        if (dt.Rows.Count > 0)
        {
            ViewState["ver"] = dt.Rows[0]["簽核版次"].ToString().Trim();
        }

        dt.Clear();
        sqlParamList.Clear();
        sqlParamList.Add(new SqlParameter("tt_seno", oRCM.SQLInjectionReplaceAll(s_seno)));
        sqlParamList.Add(new SqlParameter("@mode", oRCM.SQLInjectionReplaceAll("check_signing_v3")));
        dt = get_SP();

        if (dt.Rows.Count > 0)
        {
            if (sso.empNo == dt.Rows[0]["送簽人員工號"].ToString().Trim())
            {
                bAuth = true;
            }
            else
            {
                LB_newVer.Text = string.Format("{0}簽核中", Server.HtmlEncode(oRCM.RemoveXss(dt.Rows[0]["送簽人員工號"].ToString().Trim())));
                bAuth = false;
            }
            if (  dt.Rows[0]["送簽日期"].ToString().Trim() !="" && dt.Rows[0]["送簽日期"].ToString().Trim() != "1")
            {
                bAuth = false;
            }


        }
        else
        {
            bAuth = false;
        }
        return bAuth;
    }

    public DataTable get_SP()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        DataTable dt = new DataTable();
        #region --- query ---
        SqlCommand oCmd = new SqlCommand();
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());

        dt = this.runParaCmdDS(oCmd).Tables[0];
        #endregion
        return dt;
    }

    public void do_Edit()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();

        SqlCommand oCmd = new SqlCommand();
        //動態設定SqlParameters
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.CommandText = @"esp_TechCase_ECP";
        sqlParamList.Add(new SqlParameter("empno", oRCM.SQLInjectionReplaceAll(sso.empNo)));
        sqlParamList.Add(new SqlParameter("URL", oRCM.SQLInjectionReplaceAll(Request.Url.AbsoluteUri.ToString())));
        sqlParamList.Add(new SqlParameter("VIP", oRCM.SQLInjectionReplaceAll(oRCM.GetIP())));
        oCmd.Parameters.AddRange(sqlParamList.ToArray<SqlParameter>());


        runScalar(oCmd);
    }

    public bool Html()
    {
        string errUrl = "../danger.aspx";
        #region 特殊字元判斷-有MasterPage
        foreach (Control ctrl in Page.Form.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is DropDownList)
                {
                    DropDownList objTextBox = (DropDownList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is RadioButtonList)
                {
                    RadioButtonList objTextBox = (RadioButtonList)c;
                    if (Base64.danger_word(objTextBox.SelectedValue) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }

                if (c is TextBox)
                {
                    TextBox objTextBox = (TextBox)c;
                    if ((Base64.danger_word(objTextBox.Text)) == "1")
                    {
                        Response.Redirect(errUrl);
                        return false;
                    }
                }
            }
        }
        #endregion
        return true;
    }



//    protected void btnPrint_Click(object sender, EventArgs e)
//    {
//        Ecp_guid = ddl_version.SelectedValue;
//        string url = string.Format("TechCase_ECP_print.aspx?guid={0}", Server.UrlEncode(Ecp_guid));
//        Response.Redirect(url);
//    }

}