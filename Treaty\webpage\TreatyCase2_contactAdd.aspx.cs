﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TreatyCase2_contactAdd : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyCase2_Auth";
                //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Auth";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_auth = dt.DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if ((str_auth == "X") || (str_auth == "R"))
                        Response.Redirect("../NoAuthRight.aspx");
                }
            }
            else
                Response.Redirect("../NoAuthRight.aspx");
        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if ((TB_compname.Text == "") || (TB_name.Text == ""))
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('公司 & 姓名 必須填寫 !');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            if (TB_compname.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_name.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_tel.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_mail.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");

            //SDS_SC.SelectParameters.Clear();
            //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_SC.InsertCommand = "insert treaty_case2_contact(tc2c_seno,tc2c_compname,tc2c_name,tc2c_tel,tc2c_mail  ) values(@seno,@compname,@name,@tel,@mail )";
            //this.SDS_SC.InsertParameters.Add("seno", ViewState["seno"].ToString());
            //this.SDS_SC.InsertParameters.Add("compname", TB_compname.Text);
            //this.SDS_SC.InsertParameters.Add("name", TB_name.Text);
            //this.SDS_SC.InsertParameters.Add("tel", TB_tel.Text);
            //this.SDS_SC.InsertParameters.Add("mail", TB_mail.Text);
            //this.SDS_SC.Insert();
            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"insert treaty_case2_contact(tc2c_seno,tc2c_compname,tc2c_name,tc2c_tel,tc2c_mail  ) values(@seno,@compname,@name,@tel,@mail )";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@compname", oRCM.SQLInjectionReplaceAll(TB_compname.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@name", oRCM.SQLInjectionReplaceAll(TB_name.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@tel", oRCM.SQLInjectionReplaceAll(TB_tel.Text.Trim()));
                sqlCmd.Parameters.AddWithValue("@mail", oRCM.SQLInjectionReplaceAll(TB_mail.Text.Trim()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }

    }
}