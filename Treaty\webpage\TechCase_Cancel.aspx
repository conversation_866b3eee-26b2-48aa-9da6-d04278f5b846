﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_Cancel.aspx.cs" Inherits="Treaty_webpage_TechCase_Cancel" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />

    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("需求已取消!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 15px">
                <tr>
                    <td class="td_right">取消原因：</td>
                    <td>
                        <asp:TextBox ID="TB_cancletxt" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox></td>
                </tr>
                <tr>
                    <td colspan="2">                       
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>

            </table>
        </span>

    </form>
</body>
</html>

