﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_ECP_outside.aspx.cs" Inherits="TechCase_ECP_outside" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>境外實施審查表簽核</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            parent.$.fn.colorbox.close();
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="stripeMe" style="margin-left: 15px; margin-top: 25px">

            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="left" nowrap="nowrap"><span class="font-title titlebackicon">簽核版次</span></td>
                    <td>
                        <asp:DropDownList ID="ddl_version" runat="server" DataTextField="Text" DataValueField="Value" OnSelectedIndexChanged="ddl_version_SelectedIndexChanged" AutoPostBack="True"></asp:DropDownList>
                    </td>
                    <td align="left" nowrap="nowrap">
                        <asp:Button ID="BT_detail" runat="server" Text="檢視簽核明細" CssClass="genbtnS inline" OnClick="BT_detail_Click" /><br />
                    </td>
                    <td width="50%" align="right">
                        <asp:Label ID="LB_newVer" runat="server" ForeColor="Red"></asp:Label>
                        <asp:Button ID="BT_sign" runat="server" Text="新增簽核版本" CssClass="genbtnS inline" OnClick="BT_sign_Click" />
                    </td>
                </tr>
            </table>
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                <ContentTemplate>
                    <div class="stripeMe font-normal" style="width: 100%; overflow: auto; margin-top: 10px;">
                        <asp:GridView ID="gv_Data" runat="server" AutoGenerateColumns="False" Width="100%" OnRowDataBound="gv_Data_RowDataBound">
                            <AlternatingRowStyle CssClass="alt" />
                            <Columns>
                                <asp:TemplateField HeaderText="順<br>序">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_關號" runat="server" Text='<%# Server.HtmlEncode(Eval("Seq").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="center" Width="10px" />
                                </asp:TemplateField>
                                <asp:BoundField HeaderText="送簽條件" DataField="送簽條件" ItemStyle-HorizontalAlign="left" />
                                <asp:TemplateField HeaderText="收件人員">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LB_收件人員" runat="server" Text='<%#: Server.HtmlEncode(Eval("收件人員").ToString()) %>'></asp:LinkButton>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Center" Width="10%" />
                                </asp:TemplateField>
                                <asp:BoundField HeaderText="收件時間" DataField="收件時間" ItemStyle-HorizontalAlign="Center" ItemStyle-Width="10%" />
                                <asp:TemplateField HeaderText="簽核<br>結果">
                                    <ItemTemplate>
                                        <asp:Label ID="LB_簽核結果" runat="server" Text='<%# Server.HtmlEncode(Eval("簽核結果").ToString()) %>'></asp:Label>
                                    </ItemTemplate>
                                    <ItemStyle HorizontalAlign="Center" Width="50px" />
                                </asp:TemplateField>
                                <asp:BoundField DataField="簽核時間" HeaderText="簽核時間" ItemStyle-HorizontalAlign="Center" ItemStyle-Width="10%" />
                                <asp:BoundField DataField="簽核意見" HeaderText="簽核意見" ItemStyle-HorizontalAlign="left" ItemStyle-Width="100px" />
                            </Columns>
                            <EmptyDataTemplate>
                                <div style="text-align: left;">無資料</div>
                            </EmptyDataTemplate>
                        </asp:GridView>
                        <%--<asp:SqlDataSource ID="SDS_signinfo" runat="server"   />--%>
                    </div>

                    <div class="left">
                        <asp:Button ID="btnPrint" runat="server" CssClass="genbtnS inline" Text="印表" OnClick="btnPrint_Click" />
                    </div>

                </ContentTemplate>
                
            </asp:UpdatePanel>




        </div>

        <script type="text/javascript">

            function special() { }
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(special);

        </script>

    </form>
</body>


</html>
