﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="DialogMultiCustomerSelect2.aspx.cs" Inherits="webpage_DialogMultiCustomerSelect2" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>DialogMultiCustomerSelect</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript">
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function newGuid() {
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        function choose_cust(theObj) {
            $("#txt_compname").val("");
            var Commonkey = newGuid();
            var kw = '';
            var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
            var ret_url = escape("../subap/colorbox_close.aspx");

            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=' + escape(kw) + "&url=" + ret_url// + '&rd=' + Math.random()
                , iframe: true, width: "75%", height: "60%", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callback);

                    //if ($.colorbox.settings.data != undefined) {
                    //    $(this).val($.colorbox.settings.data);
                    //    $.colorbox.settings.data = undefined;
                    //}
                }
            });
        }
        function jsonp_callback(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    document.getElementById("h_compno").value = '';
                    document.getElementById("txt_compname").value = '';
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    document.getElementById("h_compno").value = '';
                    document.getElementById("txt_compname").value = '';
                    break;
                default:
                    document.getElementById("h_compno").value = data.c_compidno;
                    document.getElementById("txt_compname").value = data.c_compcname;
                    break;
            }
        }
        function chkNeedField() {
            var errString = '';
            //檢查廠商挑選
            if ($("#txt_compname").val() == "")
                errString += '廠商挑選 <--欄位不可空白\n';

            //如果有必填欄位沒填，秀出訊息

            if (errString.length > 0) {
                alert(errString);
                return false;
            }
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">


        <span class="stripeMe">
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td>
                        <asp:Label ID="Label1" runat="server">廠商挑選：</asp:Label>
                        <asp:TextBox ID="txt_compname" runat="server" onchange="choose_cust('0');" autocomplete="off" ReadOnly="True" Width="250px"></asp:TextBox>
                        <a onclick="choose_cust('0');" class="ajax_mesg" href="#">
                            <img id="Img1" src="../images/ssearch.gif" border="0" /></a>
                    </td>
                    <td align="right">
                        <asp:Button ID="btnAdd" runat="server" Text="新增" class="genbtnS" OnClick="btnAdd_Click"></asp:Button>
                        <asp:Button ID="btnExit" Text="離開" class="genbtnS" runat="server" OnClick="btnExit_Click1"></asp:Button>
                    </td>
                </tr>
            </table>
            <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False"
                CellPadding="4" Width="100%"
                GridLines="None"
                OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound" OnPreRender="SGV_company_PreRender">
                <FooterStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                <AlternatingRowStyle CssClass="TRowEven" />
                <Columns>
                    <asp:TemplateField HeaderText="刪除">
                        <HeaderStyle Width="40px" ForeColor="Black"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                        <ItemTemplate>
                            <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Eval("comp_idno") %>'>刪除</asp:LinkButton>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:BoundField DataField="comp_idno" HeaderText="廠商編號">
                        <HeaderStyle Width="100px"></HeaderStyle>
                    </asp:BoundField>
                    <asp:BoundField DataField="comp_cname" HeaderText="廠商中文名稱" />
                    <asp:BoundField DataField="comp_ename" HeaderText="廠商英文名稱" />
                    <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                        <HeaderStyle Width="100px"></HeaderStyle>
                    </asp:BoundField>
                </Columns>
                <EmptyDataTemplate>
                    <!--當找不到資料時則顯示「無資料」-->
                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                </EmptyDataTemplate>
                <FooterStyle BackColor="White" />
                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
            </cc1:SmartGridView>
        </span>

        <asp:HiddenField ID="h_compno" runat="server" />
        <asp:HiddenField ID="xh_compcountry" runat="server" />
        <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
