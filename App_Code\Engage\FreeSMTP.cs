﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;
using System.IO;
using System.Data;
using System.Net.Mail;
using System.Net;

namespace Engage
{
	/// <summary>
	/// Summary description for FreeSMTP
	/// </summary>
	public class FreeSMTP
	{
		public FreeSMTP()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strSubject">主旨</param>
		/// <param name="strBody">內容</param>
		/// <param name="strEMail">收件者 Email</param>		
		/// <param name="AttachFile">上傳檔案</param>
		/// <param name="AttachFileShowName">上傳檔案顯示名稱</param>
		/// <param name="strCCEMail">信件副本</param>
		/// <param name="strFromMail">寄件者 Email</param>
		/// <param name="strPlanIDs">契約簽辦電子檔IDs</param>
		/// <param name="strSeqsn">Seqsn</param>
		public string MailToContact(string strSubject, string strBody, string strEMail, string AttachFile, string AttachFileShowName, string strCCEMail, string strFromMail, string strPlanIDs, string strSeqsn)
		{
			string strReturnMsg = string.Empty;

			System.Configuration.AppSettingsReader app = new System.Configuration.AppSettingsReader();

			//2015/06/01:Hugo(modify), 由 FreeSMTP 改用 Microsoft SmtpClient寄送 Mail.
			//EmailMessage mail = null;
			//SMTP smtp = null;
			SmtpClient smtp = null;
			MailMessage mail = null;

			string strSMTP = (string)app.GetValue("SMTP_Server", typeof(string));
			string strFrom = string.Empty;

			if (strFromMail.Trim().Length > 0)
				strFrom = strFromMail;
			else
				strFrom = (string)app.GetValue("Mail_From", typeof(string)); //抓寄件者的 email

			try
			{
				mail = new MailMessage();
				mail.IsBodyHtml = true;

				//主旨
				mail.Subject = strSubject;

				//內容
				mail.Body = strBody;

				//寄件者
				mail.From = new MailAddress(strFrom);

				//收件者				
				string[] strARec = strEMail.Split(',');
				for (int iRec = 0; iRec < strARec.Length; iRec++)
				{
					if (strARec[iRec].Length > 0)
					{
						mail.To.Add(new MailAddress(strARec[iRec]));
					}
				}

				//附加檔
				if (AttachFile.Length > 0)
				{
					mail.Attachments.Add(new Attachment(AttachFile));
				}

				#region 契約簽辦電子上傳檔的IDs
				if (strPlanIDs.Length > 0)
				{
					string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];

					myDb dalObject = new myDb();
					dalObject.Seqsn = int.Parse(strSeqsn);
					dalObject.iPlan_attfile.ea_filetype = "NA";
					DataTable dt = dalObject.GetFileByMaxVer();

					DataView dv = dt.DefaultView;
					string[] PlanIDs = strPlanIDs.Split(',');
					for (int iPlan = 0; iPlan < PlanIDs.Length; iPlan++)
					{
						dv.RowFilter = string.Format("ea_id={0}", PlanIDs[iPlan]);
						string filename = FilePathString + dv[0]["phy_filename"].ToString();
						if (File.Exists(filename))
						{
							mail.Attachments.Add(new Attachment(filename));
						}
					}
				}

				#endregion

				//副本抄送
				string[] strRCC = strCCEMail.Split(',');
				for (int iCC = 0; iCC < strRCC.Length; iCC++)
				{
					if (strRCC[iCC].Length > 0)
					{
						mail.CC.Add(new MailAddress(strRCC[iCC]));
					}
				}

				smtp = new SmtpClient(strSMTP);
				smtp.Send(mail);
				
			}
			catch (Exception err)
			{
				HttpContext.Current.Response.Write("SEL錯誤訊息：" + err.Message + "<BR>SMTP:" + strSMTP);
				strReturnMsg = err.Message;
			}
			finally
			{
				mail.Dispose();
				smtp.Dispose();
			}

			return strReturnMsg;
		}
	}
}