﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
    /// <summary>
    /// Summary description for SendEmail
    /// </summary>
    public class SendEmail : mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;
        private string _returnMessage;

        private long _seqsn;
        private int _ver = 0;

        private string _empno;
        private string _empname;

        private string _cust_idno;
        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 執行 SP 後，回傳的訊息
        /// </summary>
        public string ReturnMessage
        {
            get { return _returnMessage; }
            set { _returnMessage = value; }
        }

        /// <summary>
        /// 流水號
        /// </summary>
        public long Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }

        /// <summary>
        /// 版次
        /// </summary>
        public int Ver
        {
            get { return _ver; }
            set { _ver = value; }
        }
        /// <summary>
        /// 登入人員
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        public string EmpName
        {
            get { return _empname; }
            set { _empname = value; }
        }

        /// <summary>
        /// 客戶IdNo
        /// </summary>
        public string Cust_IdNo
        {
            get { return _cust_idno; }
            set { _cust_idno = value; }
        }

        #endregion

        public SendEmail()
        {
            //
            // TODO: Add constructor logic here
            //
        }

        #region 基本資料 TPC mail
        /// <summary>
        /// 基本資料mail, 呼叫 sp: [pr_engage_台灣電力_mail]
        /// </summary>
        /// <returns></returns>
        public bool TPC_Mail()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_台灣電力_mail";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
                throw ex;
            }
            return success;
        }
        #endregion

        #region Chk TPC 客戶
        #region 基本資料 TPC 客戶確認
        /// <summary>
        /// 基本資料 TPC 客戶確認, 呼叫 sp: [pr_engage_台灣電力_flag]
        /// </summary>
        /// <returns></returns>
        public bool TPC_Cust()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_台灣電力_flag";

            oCmd.Parameters.AddWithValue("@idno", _cust_idno);

            SqlParameter flag = oCmd.Parameters.Add("@flag", SqlDbType.NVarChar, 100);
            flag.Direction = ParameterDirection.Output;

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                _returnMessage = flag.Value.ToString();
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion
        #endregion
    }
}