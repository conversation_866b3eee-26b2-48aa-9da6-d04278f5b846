﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_modify.aspx.cs" Inherits="TechCase_modify" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<%@ Register Src="../userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="../userControl/Foot_tech.ascx" TagPrefix="uc2" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/autosize.min.js"></script>

    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }

        $(function () {
            $('a.iterm_dymanic').cluetip({
                width: '800px', cluetipClass: 'jtip',
                ajaxCache: false,
                sticky: true,
                closePosition: 'title',
                closeText: '<img src="../Scripts/cluetip/images/cross.png" alt="close" />'
            });
        });

        $(function () { $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false }); });
        $(function () { $('a.iterm_dymanic_company').cluetip({ width: '830px', activation: 'click', sticky: true, closePosition: 'title', arrows: true, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' }); });
        $(function () { $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false }); });

        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function viewCase(seno) {
            var url = './TechCase_view.aspx?tt_seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }

        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TechCase_assignInspect.aspx?tt_seno=" + seno
                , title: '新增審查人'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function ipbshare_check(object) {//智權歸屬共用裡的本院、客戶自動比率調整
            chk_int(object);
            if (object.value > 100 || object.value < 0) {
                alert('所輸入的數字格式必須大於等於0，且小於等於100');
                object.value = '';
                return false;
            }
            var total = 100;
            if (object.id == 'txt_ipbi_percent') {//使用者觸發了本院
                $('#txt_ipbc_percent').val(total - $('#txt_ipbi_percent').val());
            }
            else {//使用者觸發了客戶
                $('#txt_ipbi_percent').val(total - $('#txt_ipbc_percent').val());
            }
            $('#rb_ipb_coparcenary').prop('checked', true);//自動將智權歸屬的共有選項勾選
            $('#txt_ipb_other_desc').val("");//智權歸屬-其他值清空
        }

        function tech_fileup(contno, seno) {
            $(".ajax_mesg").colorbox({
                href: "./TechCase_FileUp.aspx?tt_seno=" + seno
                , title: '檔案上傳'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }

        function CompanyCASE(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }


        function SendInspect(seno) {
            alert("案件已送審!");
            location.replace("./TechCase_view.aspx?tt_seno=" + seno);
        }
        function EndCase(seno) {
            alert("案件已發結案通知!");
            location.replace("./TechCase_view.aspx?tt_seno=" + seno);
        }
        function autogrow(textarea) {
            var adjustedHeight = textarea.clientHeight;
            adjustedHeight = Math.max(textarea.scrollHeight, adjustedHeight);
            if (adjustedHeight > textarea.clientHeight) {
                textarea.style.height = adjustedHeight + 'px';
            }
        }

        function showCompInfoDialog(Compno) {
            var newopen = window.open('https://arpt.itri.org.tw/CustomerRiskDetails.aspx?CustNo=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }

        function Reject_Case(tt_seno, Role2) {
            $(".ajax_mesg_reject").colorbox({
                href: "./TechCase_reject.aspx?tt_seno=" + tt_seno + "&Role2=" + Role2 + "&sReP=" + $("input[id$=hiReject]").attr("id")
                , title: Role2 == "X" ? "退洽案承辦" : "退C組承辦"
                , iframe: true, width: "700px", height: "300px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Dispatch_Case", 0);
                }
            });
        }

    </script>
    <style type="text/css">
        .cluetip-inner {
            width: 98%
        }

        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(245,245,245);
        }

        .CB_ReadOnly {
            background-color: rgb(245,245,245);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }

        a:link {
            background-color: transparent;
            text-decoration: none;
            color: blue;
        }

        a:hover {
            background-color: transparent;
            text-decoration: underline;
            color: red;
        }

        a:active {
            background-color: transparent;
            text-decoration: underline;
            color: green;
        }

        .auto-style1 {
            width: 180px;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left">
                                <span class="font-red">*表示為必填欄位</span>
                                <%--   <%= ViewState["Role"].ToString() %>
                                <%= ViewState["RW"].ToString() %>
                                <%= ViewState["Role2"].ToString() %>
                                <%= ViewState["empno"].ToString() %>--%>
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="lnkbtn_Reject" runat="server" Visible="false" class="ajax_mesg_reject"><img src="../images/icon-1301.gif" border="0"/>退洽案承辦</asp:LinkButton>
                                    <asp:LinkButton ID="lnkbtn_RejectC" runat="server" Visible="false" class="ajax_mesg_reject"><img src="../images/icon-1301.gif" border="0"/>退C組承辦</asp:LinkButton>
                                    <input id="hiReject" type="hidden" name="Hidden1" runat="server" />
                                </span>
                            </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="lnkbtn_Engage" runat="server" OnClick="lnkbtn_Engage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊</asp:LinkButton>
                                    <%--&nbsp;&nbsp;--%>
                                    <asp:Button ID="btn_Tmp_Save" runat="server" Text="暫存" OnClick="btn_Tmp_Save_Click" class="genbtnS"></asp:Button>
                                    <asp:Button ID="btn_Save" runat="server" Text="正式存檔" OnClick="btn_Save_Click" class="genbtnS"></asp:Button>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">

                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td>
                                            <table border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td>
                                                        <asp:Label ID="txt_ComplexNo" runat="server" Text=""></asp:Label></td>
                                                    <td>
                                                        <asp:Literal ID="LT_擬約幫手" runat="server" Text=''></asp:Literal></td>
                                                    <td>
                                                        <div class="font-title titlebackicon">／洽案簽辦人</div>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="洽案簽辦人" runat="server"></asp:Label></td>
                                                    <td>，議約狀態:<asp:Label ID="LB_議約狀態" runat="server" Width="95px"></asp:Label></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                需求單位<br />
                                                及部門
                                            </div>
                                        </td>
                                        <td>
                                            <table border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td>
                                                        <asp:Label ID="txt_OrgAbbrName" runat="server"></asp:Label>&nbsp;
                                                         <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <div class="font-title titlebackicon">／單位承辦人</div>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="txt_promoter_name" runat="server"></asp:Label>，分機:<asp:Label ID="txt_Tel" runat="server"></asp:Label>
                                                        <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約名稱</div>

                                        </td>
                                        <td>
                                            <asp:Label ID="txt_name" runat="server" Width="580px" Height="30px" class="text-input" Enabled="false"></asp:Label>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td>
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="98%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>

                                                            <asp:TemplateField HeaderText="跳票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_退換票" runat="server" Text='<%#  Eval("tmp_退換票").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資本額逾½">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_資本額" runat="server" Text='<%#  Eval("tmp_資本額").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資產遭查封">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_資產遭查封" runat="server" Text='<%#  Eval("tmp_資產遭查封").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="抽換票">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_抽換票" runat="server" Text='<%#  Eval("tmp_抽換票").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="其他風險">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_其他風險" runat="server" Text='<%#  Eval("tmp_其他風險").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="含陸資">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_含陸資" runat="server" Text='<%#  Eval("陸資").ToString() %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商編號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_company" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="65px" />
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商中文名稱<hr>廠商英文名稱">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="lnkbtn_廠商中文名稱" runat="server" Compno='<%# Eval("comp_idno")%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="lnkbtn_廠商中文名稱_Click"></asp:LinkButton><hr />
                                                                    <asp:Label ID="lbl_廠商英文名稱" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="400px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商<br>國別">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="lbl_國別" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_country_name").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="40px" />
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="利益<br>迴避">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="lbl_利益迴避" runat="server" Text='<%#  Server.HtmlEncode(Eval("利益迴避").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="40px" />
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>

                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                案件性質
                                            </div>
                                        </td>
                                        <td class="lineheight03">
                                            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
                                            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                                <ContentTemplate>
                                                    <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" Enabled="false" />
                                                    <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" Enabled="false" />
                                                    <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" Enabled="false" />
                                                    <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" Enabled="false" />
                                                    <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                    <asp:CheckBox ID="chk_技術讓與" runat="server" Text="技術讓與" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                    <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽案)" Enabled="false" />
                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                            <div class="font-title ">
                                                <b>
                                                    <asp:CheckBox ID="chk_技術授權" runat="server" Text="專屬技術授權" Enabled="false" />
                                                    <asp:CheckBox ID="chk_專利授權" runat="server" Text="專屬專利授權" Enabled="false" />
                                                    <asp:CheckBox ID="chk_技術與專利授權" runat="server" Text="專屬技術與專利授權" Enabled="false" />
                                                    <asp:CheckBox ID="chk_全球" runat="server" Text="全球" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                    <asp:CheckBox ID="chk_陸港澳" runat="server" Text="陸港澳" Font-Bold="True" ForeColor="#0060A4" Visible="false" Enabled="false" />
                                                    <asp:CheckBox ID="chk_特定區域" runat="server" Text="境外實施" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                    <asp:CheckBox ID="chk_韓國" runat="server" Text="韓國" Font-Bold="True" ForeColor="#0060A4" Visible="false" Enabled="false" />
                                                    <asp:CheckBox ID="chk_修約" runat="server" Text="修約" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                    <span style="font-size: 8pt; color: red">註：案件性質亦須勾選</span>
                                                </b>
                                            </div>
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PL_relation" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    案件合理性評估
                                                </div>
                                            </td>
                                            <td class="lineheight03">
                                                <asp:RadioButtonList ID="rbl_relation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow" AutoPostBack="true">
                                                    <asp:ListItem Value="A" Selected="True">讓與、專屬授權 及其他授權</asp:ListItem>
                                                    <asp:ListItem Value="B">修約</asp:ListItem>
                                                    <asp:ListItem Value="" style="display: none">舊案</asp:ListItem>
                                                </asp:RadioButtonList>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_Light" runat="server" Visible="false">
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">
                                                    案件燈號
                                                </div>
                                            </td>
                                            <td colspan="3" class="lineheight03">
                                                <asp:RadioButtonList ID="rbl_Light" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow" OnSelectedIndexChanged="rbl_Light_SelectedIndexChanged" AutoPostBack="true"></asp:RadioButtonList>
                                                <asp:TextBox ID="txt_燈號_說明" runat="server"></asp:TextBox>
                                                <span style="float: right; margin-right: 2em;">
                                                    <span class="font-title titlebackicon">第三方鑑價報告</span>
                                                    <asp:CheckBox ID="chk_第三方鑑價報告" runat="server" /></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="4">
                                                <span class="font-title titlebackicon">
                                                    <asp:CheckBox ID="chk_成果歸屬運用辦法" runat="server" OnCheckedChanged="chk_成果歸屬運用辦法_CheckedChanged" AutoPostBack="true" />經濟部成果歸屬運用辦法第18條</span>

                                                <asp:CheckBox ID="chk_公益目的" runat="server" Text="公益目的" />
                                                <asp:CheckBox ID="chk_促進整體產業發展" runat="server" Text="促進整體產業發展" />
                                                <asp:CheckBox ID="chk_提升研發成果運用效益" runat="server" Text="提升研發成果運用效益" />
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC" runat="server"></asp:PlaceHolder>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC_x" runat="server"></asp:PlaceHolder>

                                    <asp:PlaceHolder ID="PL_tt_manage_note" runat="server">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">技轉承辦人意見彙整</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txt_betsum" runat="server" Width="800px" TextMode="MultiLine" Height="60px"></asp:TextBox>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送審資料</div>
                                        </td>
                                        <td align="left">
                                            <asp:Button ID="btn_FileUp" runat="server" Text="檔案上傳" class="ajax_mesg genbtnS"></asp:Button>

                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" Width="98%">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="功能">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                                                <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tcdf_no") %>' ForeColor="Red">刪除</asp:LinkButton>
                                                                <div style="display: none">
                                                                    <asp:LinkButton ID="lnkbtn_Edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Eval("tcdf_no") %>'>維護</asp:LinkButton>
                                                                </div>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="550px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Left" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="文件類型">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_tcdf_type" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetype").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_4" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="45px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="審查">
                                                            <ItemTemplate>
                                                                <asp:DropDownList ID="DDL_Hec" runat="server" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate" Visible="false">
                                                                    <asp:ListItem Value="1">V</asp:ListItem>
                                                                    <asp:ListItem Value="0">x</asp:ListItem>
                                                                </asp:DropDownList>／
                                                                <asp:DropDownList ID="DDL_Hec_order" runat="server" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate_order" Visible="false">
                                                                    <asp:ListItem Value="1">1</asp:ListItem>
                                                                    <asp:ListItem Value="2">2</asp:ListItem>
                                                                    <asp:ListItem Value="3">3</asp:ListItem>
                                                                    <asp:ListItem Value="4">4</asp:ListItem>
                                                                    <asp:ListItem Value="5">5</asp:ListItem>
                                                                    <asp:ListItem Value="6">6</asp:ListItem>
                                                                    <asp:ListItem Value="7">7</asp:ListItem>
                                                                    <asp:ListItem Value="8">8</asp:ListItem>
                                                                    <asp:ListItem Value="9">9</asp:ListItem>
                                                                    <asp:ListItem Value="10">10</asp:ListItem>
                                                                    <asp:ListItem Value="11">11</asp:ListItem>
                                                                    <asp:ListItem Value="12">12</asp:ListItem>
                                                                    <asp:ListItem Value="13">13</asp:ListItem>
                                                                    <asp:ListItem Value="14">14</asp:ListItem>
                                                                    <asp:ListItem Value="15">15</asp:ListItem>
                                                                    <asp:ListItem Value="16">16</asp:ListItem>
                                                                    <asp:ListItem Value="17">17</asp:ListItem>
                                                                    <asp:ListItem Value="18">18</asp:ListItem>
                                                                    <asp:ListItem Value="19">19</asp:ListItem>
                                                                    <asp:ListItem Value="20">20</asp:ListItem>
                                                                    <asp:ListItem Value="21">21</asp:ListItem>
                                                                    <asp:ListItem Value="22">22</asp:ListItem>
                                                                    <asp:ListItem Value="23">23</asp:ListItem>
                                                                    <asp:ListItem Value="24">24</asp:ListItem>
                                                                    <asp:ListItem Value="25">25</asp:ListItem>
                                                                    <asp:ListItem Value="26">26</asp:ListItem>
                                                                    <asp:ListItem Value="27">27</asp:ListItem>
                                                                    <asp:ListItem Value="28">28</asp:ListItem>
                                                                    <asp:ListItem Value="29">29</asp:ListItem>
                                                                    <asp:ListItem Value="30">30</asp:ListItem>
                                                                    <asp:ListItem Value="31">31</asp:ListItem>
                                                                    <asp:ListItem Value="32">32</asp:ListItem>
                                                                    <asp:ListItem Value="33">33</asp:ListItem>
                                                                    <asp:ListItem Value="34">34</asp:ListItem>
                                                                    <asp:ListItem Value="35">35</asp:ListItem>
                                                                    <asp:ListItem Value="36">36</asp:ListItem>
                                                                    <asp:ListItem Value="37">37</asp:ListItem>
                                                                    <asp:ListItem Value="38">38</asp:ListItem>
                                                                    <asp:ListItem Value="39">39</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="120px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>

                                            </span>
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PL_Inspect" runat="server">
                                        <tr valign="top">
                                            <td align="right">
                                                <div class="font-title titlebackicon">審查資訊</div>
                                            </td>
                                            <td width="450">
                                                <asp:CheckBox ID="chk_NotInspect" runat="server" Text="文件不須審查" Enabled="false" Visible="false" />
                                                <asp:Button ID="btn_AddInspect" runat="server" Text="新增審查人" class="ajax_mesg genbtnS" Visible="false" />
                                                <asp:Button ID="btn_SendInspect" runat="server" Text="送出審查" class="genbtnS" OnClick="btn_SendInspect_Click" Visible="false" />
                                                <span class="stripeMe">
                                                    <asp:GridView ID="gv_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="gv_Inspect_RowDataBound" OnRowCommand="gv_Inspect_RowCommand" Width="98%">
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="lbl_tti_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tti_no").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tti_no") %>' ForeColor="Red">刪除</asp:LinkButton>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="50px" HorizontalAlign="Center" ForeColor="Black" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="順序">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="Lb_order" runat="server" Text='<%#  Server.HtmlEncode(Eval("tt_order").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tt_empname" HeaderText="審查人">
                                                                <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tt_inspect_desc" HeaderText="簽核意見">
                                                                <ItemStyle Width="200px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="簽核狀態">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_Istatus" runat="server" Text='<%#  Server.HtmlEncode(Eval("tt_flag").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tt_inspect_time" HeaderText="簽核日期">
                                                                <ItemStyle HorizontalAlign="Left" Width="180px" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>

                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>


                                    <tr id="trManageNote" runat="server" visible="false">
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                備註                               
                                    <div class="font-title titlebackicon">備註</div>
                                        </td>
                                        <td class="lineheight03">
                                            <asp:TextBox ID="txt_ManageNote" runat="server" Width="800px" TextMode="MultiLine" Height="60px"></asp:TextBox>

                                        </td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">

                            <div class="right">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="td_right" colspan="3">
                                            <asp:Button ID="btn_Back" runat="server" Text="回上一頁" OnClick="btn_Back_Click" class="ajax_mesg genbtnS" />
                                            <asp:Button ID="btn_End" runat="server" Text="結案通知" OnClick="btn_End_Click" class="genbtnS" Visible="false"></asp:Button>
                                            <asp:Button ID="btn_Tmp_Save2" runat="server" Text="暫存" OnClick="btn_Tmp_Save_Click" class="genbtnS"></asp:Button>
                                            <asp:Button ID="btn_Save2" runat="server" Text="正式存檔" OnClick="btn_Save_Click" class="genbtnS"></asp:Button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="accordionblock">
                            <div class="tabsubmenublock">
                                <span class="gentable font-normal">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">分案主管</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">送件/分案日期</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>
                                                / 
                                                <asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">承辦人</div>
                                            </td>
                                            <td align="left">
                                                <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>|
                                                <asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>|
                                                <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal>
                                                <td align="right">
                                                    <div class="font-title titlebackicon">進度</div>
                                                </td>
                                                <td>
                                                    <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal>
                                                    <asp:DropDownList ID="DDL_Degree" runat="server" DataTextField="text" DataValueField="value" Visible="false"></asp:DropDownList></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">技轉承辦人_C</div>
                                            </td>
                                            <td align="left">
                                                <asp:Literal ID="lb_handle_c_name" runat="server"></asp:Literal>|
                                                <asp:Literal ID="lb_handle_c_empno" runat="server"></asp:Literal>|
                                                <asp:Literal ID="lb_handle_c_ext" runat="server"></asp:Literal>
                                            </td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">法務承辦人</div>
                                            </td>
                                            <td align="left">
                                                <asp:Literal ID="LT_法務承辦人" runat="server"></asp:Literal>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">修改人</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>|
                                                <asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">修改日期</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                        </tr>
                                    </table>
                                </span>
                            </div>
                            <!-- tabsubmenublock -->
                        </div>
                        <!-- accordionblock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc2:Foot runat="server" ID="Foot" />

        <script type="text/javascript">
            tinymce.init({
                selector: '#txt_betsum',
                width: "800",
                height: "500",
                language: 'zh_TW',
                //font_formats: "新細明體=Microsoft JhengHei;細明體=PMingLiU;標楷體=MingLiU;微軟正黑體=微軟正黑體 ;Arial=arial,helvetica,sans-serif;",| fontselect 
                fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 24pt 36pt",
                content_css: 'css/content.css',
                toolbar: "insertfile undo redo | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor | fontsizeselect",
                statusbar: false,
                plugins: [' code', 'textcolor'],
            });

            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yy/mm/dd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.attr('');
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });

            $(document).ready(function () {
                jQuery('#Form1').validationEngine({});
                $('#txtContMoney').defaultValue('0');
                $('#txt_name').defaultValue('請輸入契約名稱');
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                $(".inputhint").tooltip({
                    position: { my: "left+10 bottom+40", at: "left bottom " },
                    tooltipClass: "custom-tooltip-styling",
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500, height: 300,
                    autoOpen: false,
                    show: { duration: 300 },
                    hide: { duration: 300 }
                });

                $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_otherrequire_desc").attr("title", $("#txt_otherrequire_desc").val());
                $(".help_otherrequire_desc").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txt_manage_note").val());
                $(".help_manage_note").attr("class", "itemhint");

                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: { duration: 300 },
                    hide: { duration: 300 }
                });


            });
        //autosize(document.querySelectorAll('textarea'));
        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
