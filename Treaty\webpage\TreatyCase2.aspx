﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2.aspx.cs" Inherits="TreatyCase2" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.defaultvalue-1.0.js"></script>

    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>


    <script type="text/javascript">

        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("./colorbox_close.aspx");
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }

        function Find_Empno(obj, arg_sw) {
            $(".ajax_mesg").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback2);
                    }
                }
            });

            $(".ajax_kw").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });

        }
        function Find_empno_kw(obj, arg_sw) {
            var strURL = "../../comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val());
            if (arg_sw == "1") {
                $('#txt_promoter_empno').val("");
                $('#txt_promoter_name').val("");
                $('#txtTel').val("");
                $('#txtOrgAbbrName').val("");
                $('#x_dept').val("");
                $('#txt_req_dept').val("");
                $('#txtOrgAbbrName').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback1);
            }
            if (arg_sw == "2") {
                $('#h_px_empno').val("");
                $('#txt_px_name').val("");
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }
        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    //Find_Empno("txt_promoter_name", "1");
                    break;
                default:
                    $('#txt_promoter_empno').val(data.c_com_empno);
                    $('#txt_promoter_name').val(data.c_com_cname);
                    $('#txtTel').val(data.c_com_telext);
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
                    $('#x_dept').val(data.c_com_deptid.substr(3, 5));
                    $('#txt_req_dept').html(htmlEncode(data.c_com_deptid));
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    break;
                case "error2":
                    alert("查到的資料有2筆以上,請填較精確的值!");
                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }

        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url,
                title: '挑選客戶'
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

                }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else {
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    }
                    reflash_topic("company_renew", 0);
                    break;
            }
        }

        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }

        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }


    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light"></div>
                        </div>

                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td>
                                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                                        </td>
                                        <td align="right">
                                            <span class="gentable font-normal">
                                                <asp:Image ID="Image1" runat="server" Height="35px" ImageUrl="../images/CONFIDENTIAL.png" Width="110px" />
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:TextBox ID="txtComplexNo" runat="server" class="inputex inputsizeM" Visible="false" Width="100px"></asp:TextBox>
                                            (舊案號:
                                            <asp:TextBox ID="txtOldContno" runat="server" ReadOnly="True" Width="100px" class="inputex inputsizeS" Visible="false"></asp:TextBox>)</td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">收文日期</div>
                                        </td>
                                        <td class="width40">
                                            <asp:TextBox ID="txt_accept_date" runat="server" class="pickdate inputex inputsizeS text-input" MaxLength="8" Width="80px" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtOrgAbbrName" runat="server" class="text-input TB_ReadOnly" Width="50px"></asp:TextBox>&nbsp;<asp:HiddenField ID="txt_req_dept" runat="server" />
                                            <asp:TextBox ID="x_dept" runat="server" Width="70px" class="text-input TB_ReadOnly"></asp:TextBox>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txt_promoter_name" runat="server" Width="95px" class="ajax_kw inputex text-input"></asp:TextBox>
                                            <a onclick="javascript:Find_Empno('txt_promoter_name','1');" title="挑選客戶">
                                                <img id="img_promoter_name" src="../images/icon_search.gif" border="0" class="ajax_mesg btn_mouseout" /></a>&nbsp;
                       分機 &nbsp;
                                            <asp:TextBox ID="txtTel" runat="server" Width="110px" class="TB_ReadOnly" ReadOnly="True"></asp:TextBox>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>案件名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <div class="left">
                                                    <asp:Button ID="BT_Customer" runat="server" class="ajax_mesg genbtnS" Text="新增" />
                                                </div>
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <HeaderStyle Width="40px" ForeColor="Black"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Eval("comp_idno") %>'>刪除</asp:LinkButton>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="統號">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_company" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="60px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="廠商名稱">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_cName" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>'></asp:Label><br />
                                                                    <asp:Label ID="LB_eName" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="500px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="120px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="代表人">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="TB_comp_chairman" runat="server" Width="50px" Text='<%#  Server.HtmlEncode(Eval("comp_chairman").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--<asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">權侵權檢舉 </div>
                                        </td>
                                        <td colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">權利金收取</div>
                                        </td>
                                        <td colspan="3"></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">訴訟</div>
                                        </td>
                                        <td colspan="3"></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                                &nbsp;
	           <asp:Button class="ajax_mesg genbtnS" ID="btnSendApply2" runat="server" Text="送出承辦單" OnClick="btnSendApply_Click"></asp:Button>&nbsp;
	            
                            </div>
                        </div>
                        <div class="uplineT1">
                            <span class="gentablenoline">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_modify_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_modify_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_send_date" runat="server"></asp:Label></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- uplineT1 -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <div class="WrapperFooter">
            <div class="footerblock fixwidth font-size2 font-white lineheight02">
                版權所有©2014 工業技術研究院｜ 建議瀏覽解析度1024x768以上<br />
                業務窗口：郝任珍(分機:97878)｜網站製作：資科中心｜意見反應｜Help｜網站地圖｜
            </div>
            <!--{* footerblock *}-->
        </div>
        <!-- WrapperFooter -->
        <%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>

    <script type="text/javascript">
        function htmlEncode(str) {
            var ele = document.createElement('span');
            ele.appendChild(document.createTextNode(str));
            return ele.innerHTML;
        }

        $(document).ready(function () {
            jQuery("#Form1").validationEngine({});
            $('#txtContMoney').defaultValue('0');
            $('#txt_promoter_name').defaultValue('請挑選承辦人');
            $('#txt_name').defaultValue('請輸入契約名稱');
            $(".itemhint").tooltip({
                track: true,
                position: { my: "left+15 center", at: "right center" },
                //讓tooltips內可以放置HTML CODE
                content: function () {
                    return $(this).prop('title');
                }
            });
            $(".inputhint").tooltip({
                position: { my: "left+10 bottom+40", at: "left bottom " },
                tooltipClass: "custom-tooltip-styling",
                //讓tooltips內可以放置HTML CODE
                content: function () {
                    return $(this).prop('title');
                }
            });
            //說明dialog
            $("#pagehow01").dialog({
                modal: true,
                position: ["center", 100],
                width: 500,
                height: 300,
                autoOpen: false,
                show: {
                    duration: 300
                },
                hide: {
                    duration: 300
                }
            });
            // $("#txtSignReason").change(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //});
            //$("img.help_txtSignReason").mouseover(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //}); 
        });
        $(function () {
            $(".pickdate").datepicker({
                changeMonth: true,
                changeYear: true,
                dateFormat: 'yymmdd',
                monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                showButtonPanel: true,
                closeText: '關閉',
                yearRange: '2010:2030',
                currentText: '移至今天'

            });

            // hack to add clear button
            // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
            //wrap up the redraw function with our new shiz
            var dpFunc = $.datepicker._generateHTML; //record the original
            $.datepicker._generateHTML = function (inst) {
                var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                //locate the button panel and add our button - with a custom css class.
                $('.ui-datepicker-buttonpane', thishtml).append(
                    $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                    ).click(function () {
                        inst.input.val(''); //attr value chrome not work
                        inst.input.attr('value', '');
                        inst.input.datepicker('hide');
                    })
                );
                thishtml = thishtml.children(); //remove the wrapper div
                return thishtml; //assume okay to return a jQuery
            };
            // 增加清除按鈕 -End		
        });

        /*
                $(document).ready(function () {
                    $('a.help_form').cluetip({ splitTitle: '|' });
                    $('a.help_reqdate').cluetip({ splitTitle: '|' });
                    $('a.help_confirm_date').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_right').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_share').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_other').cluetip({ width: '500px', splitTitle: '|' });
                });
        */

    </script>


</body>
</html>
