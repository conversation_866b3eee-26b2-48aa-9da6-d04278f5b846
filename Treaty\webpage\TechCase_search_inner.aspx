﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_search_inner.aspx.cs" Inherits="TechCase_search_inner" EnableEventValidation="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="~/Treaty/userControl/Foot_tech.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align='right'>單位別：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                        </td>

                                        <td align='right'>契約性質：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right'>案件編號/名稱：</td>
                                        <td>
                                            <asp:TextBox ID="tbx_name" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right'>客戶名稱：</td>
                                        <td>
                                            <asp:TextBox ID="txtCompname" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                       
                                        <td align='right'>單位執行部門：</td>
                                        <td>
                                            <asp:TextBox ID="tbx_req_dept" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right'>狀態：</td>
                                        <td>
                                            <asp:DropDownList ID="ddl_degree" runat="server" DataTextField="Text" DataValueField="Value"></asp:DropDownList>
                                        </td>                                      
                                    </tr>
                                     <tr>
                                        <td align='right'>DD燈號：</td>
                                        <td colspan="3">
                                            <asp:DropDownList ID="ddl_Light" runat="server">
                                            </asp:DropDownList></td>

                                    </tr>
                                    <tr>
                                        <td colspan="4" class="td_right">
                                            <asp:Button ID="btnExcel" runat="server" Class="genbtnS" Text="匯出" OnClick="btnExcel_Click"></asp:Button>
                                            <asp:Button ID="btnClear" runat="server" Class="genbtnS" Text="清除" OnClick="btnClear_Click"></asp:Button>
                                            <asp:Button ID="btnQuery" TabIndex="1" runat="server" Text="查詢" Class="genbtnS" OnClick="btnQuery_Click"></asp:Button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="6">
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                                        <FooterStyle BackColor="White" />
                                                        <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                                        <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                                        <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                        <Columns>

                                                            <asp:TemplateField HeaderText="單位名稱" SortExpression="org_name">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("單位名稱").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="洽案／契約名稱" SortExpression="tmp_case_name">
                                                                <ItemTemplate>
                                                                    <%# Eval("議約編號") %><br />
                                                                    <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tt_seno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_name").ToString())) %>'></asp:LinkButton><br />
                                                                </ItemTemplate>
                                                                <ItemStyle Width="245px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="客戶名稱" HeaderText="客戶名稱">
                                                                <ItemStyle Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="分案日<hr>結案日">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("分案日").ToString())) %><hr />
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("結案日").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="洽案<br>承辦人<hr>C組<br>承辦人">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("洽案承辦人").ToString())) %><hr>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉承辦人_c").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="90px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="案件類型" HeaderText="案件類型">
                                                                <ItemStyle Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="議約狀態<hr>案件狀態">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("議約狀態").ToString())) %><hr />
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉狀態").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="法務<br>承辦人<hr />技轉<br>承辦人">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("法務承辦人").ToString())) %>
                                                                    <hr />
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉承辦人").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="60px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                           <%-- <asp:TemplateField HeaderText="機密<br>等級">
                                                                <ItemTemplate>
                                                                    <asp:Image ID="Image1" runat="server" Height="31px" ImageUrl="../images/CONFIDENTIAL.png" Width="60px" />
                                                                </ItemTemplate>
                                                                <ItemStyle Width="50px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>--%>
                                                            <asp:TemplateField HeaderText="新創案">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("新創案").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="境外實施">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("境外實施").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>


                                                            <asp:TemplateField HeaderText="公益目的">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("公益目的").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="促進產業發展">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("促進產業發展").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="提升成果運用效益">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("提升成果運用效益").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="主管提醒註記">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_remark").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>

                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    </cc1:SmartGridView>
                                                </span>
                                        </td>
                                    </tr>
                                </table>
                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc1:Foot runat="server" ID="Foot" />
        <script type="text/javascript">
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });
            $(document).ready(
                function () {
                    $(document).ready(function () { $('.headernews').scrollbox({ delay: 4 }); });
                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    $(".inputhint").tooltip({
                        position: { my: "left+10 bottom+40", at: "left bottom " },
                        tooltipClass: "custom-tooltip-styling",
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        // position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        //position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                });
            $(document).ready(function () {
                $(".accordionblock").hide();
                //個別按鈕操作
                $(".itemcontrolbtn").click(function () {
                    //切換子項顯示與隱藏
                    $(this).parent().parent().next(".accordionblock").slideToggle();
                    //ICON樣式切換
                    $(this).toggleClass("iconup");
                    //文字切換  ?:運算式是if else的快捷方式
                    //$(this).text($(this).text() == '展開項目' ? '收合項目' : '展開項目');
                });
                //全部展開
                $(".AllControlOpen").click(function () {
                    $(".accordionblock").slideDown();
                    //$(".itemcontrolbtn").text('收合項目');
                    $(".itemcontrolbtn").addClass("iconup")
                });
                //全部收合
                $(".AllControlClose").click(function () {
                    $(".accordionblock").slideUp();
                    //$(".itemcontrolbtn").text('展開項目');
                    $(".itemcontrolbtn").removeClass("iconup")
                });
            });
        </script>
    </form>
</body>
</html>
