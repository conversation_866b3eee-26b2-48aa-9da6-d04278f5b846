﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_todo_modify.aspx.cs" Inherits="Treaty_webpage_TechCase_todo_modify" ValidateRequest="false" %>


<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot_tech.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">

</script>
    <style type="text/css">
        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock stripeMe font-normal">
                            <div style="text-align: right">
                                <asp:Button ID="btnSave" runat="server" Text="存檔" Class="genbtnS" OnClick="btnSave_Click"></asp:Button>
                                <asp:Button ID="btnBack" runat="server" Text="返回" Class="genbtnS" OnClick="btnBack_Click"></asp:Button>
                            </div>
                            <table width="100%" border="0">
                                <tr>
                                    <td align='right'>標題：</td>
                                    <td>
                                        <asp:TextBox ID="TB_name" runat="server"></asp:TextBox>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>起訖日期：</td>
                                    <td>
                                        <asp:TextBox ID="TB_sdate" class="pickdate inputsizeM text-input" runat="server" Width="90px" MaxLength="10" />~
                                           <asp:TextBox ID="TB_edate" class="pickdate inputsizeM text-input" runat="server" Width="90px" MaxLength="10" />

                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>通知頻率：</td>
                                    <td>
                                        <asp:RadioButtonList ID="RBL_repeat" runat="server" OnSelectedIndexChanged="RBL_repeat_SelectedIndexChanged" AutoPostBack="true" RepeatDirection="Horizontal" RepeatLayout="Flow">
                                            <asp:ListItem Value="0" Selected="True">不通知</asp:ListItem>
                                            <asp:ListItem Value="1">一次性</asp:ListItem>
                                            <asp:ListItem Value="2">重複</asp:ListItem>
                                        </asp:RadioButtonList>
                                        <div id="CBL_week" runat="server" visible="false" style="display: inline">
                                            ：
                                            <asp:CheckBox ID="CB_notify_week_1" runat="server" Text="星期一" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_2" runat="server" Text="星期二" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_3" runat="server" Text="星期三" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_4" runat="server" Text="星期四" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_5" runat="server" Text="星期五" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_6" runat="server" Text="星期六" Checked="false" />
                                            <asp:CheckBox ID="CB_notify_week_7" runat="server" Text="星期日" Checked="false" />
                                        </div>

                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>收件人：</td>
                                    <td>
                                        <asp:DropDownList ID="DDL_Send" runat="server" DataValueField="com_mailadd" DataTextField="com_cname"></asp:DropDownList>
                                        <asp:ImageButton ID="IB_Add_Send" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-open.png" OnClick="IB_Add_Send_Click" />
                                        &emsp;
                                        <asp:Repeater ID="rpt_Send" runat="server">
                                            <ItemTemplate>
                                                <asp:ImageButton ID="IB_del_Send" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-delete.png" CommandName="Del" CommandArgument='<%# Server.HtmlEncode(Eval("Key").ToString()) %>' OnClick="IB_del_Send_Click" />
                                                <%# Server.HtmlEncode(Eval("Value").ToString()) %>
                                            </ItemTemplate>
                                        </asp:Repeater>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>副本：</td>
                                    <td>
                                        <div></div>
                                        <asp:DropDownList ID="DDL_CC" runat="server" DataValueField="com_mailadd" DataTextField="com_cname"></asp:DropDownList>
                                        <asp:ImageButton ID="IB_Add_CC" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-open.png" OnClick="IB_Add_CC_Click" />
                                        &emsp;
                                        <asp:Repeater ID="rpt_CC" runat="server">
                                            <ItemTemplate>
                                                <asp:ImageButton ID="IB_del_CC" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-delete.png" CommandName="Del" CommandArgument='<%# Server.HtmlEncode(Eval("Key").ToString()) %>' OnClick="IB_del_CC_Click" />
                                                <%# Server.HtmlEncode(Eval("Value").ToString()) %>
                                            </ItemTemplate>
                                        </asp:Repeater>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>上傳檔案：</td>
                                    <td>
                                        <asp:FileUpload ID="FileUpload1" runat="server" onpropertychange="TransferData(this.value);" />
                                        &emsp;
                        <asp:Button ID="btnFileUpload" runat="server" Text="上傳" CssClass="btn btn-outline-primary btn-sm" OnClick="btnFileUpload_Click" />
                                        <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" Width="98%">
                                            <Columns>
                                                <asp:TemplateField HeaderText="功能">
                                                    <ItemTemplate>
                                                        <asp:Label ID="lbl_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tdf_id").ToString()) %>' Visible="false"></asp:Label>
                                                        <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tdf_id") %>' ForeColor="Red" OnClick="lnkbtn_Del_Click">刪除</asp:LinkButton>
                                                    </ItemTemplate>
                                                    <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                                </asp:TemplateField>
                                                <asp:TemplateField HeaderText="附件名稱">
                                                    <ItemTemplate>
                                                        <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tdf_filename").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tdf_id") %>'> </asp:LinkButton>
                                                    </ItemTemplate>
                                                    <HeaderStyle Width="550px"></HeaderStyle>
                                                    <ItemStyle HorizontalAlign="Left" />
                                                </asp:TemplateField>
                                            </Columns>
                                            <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                            <PagerSettings Position="Bottom" />
                                            <PagerStyle HorizontalAlign="Left" />
                                        </asp:GridView>
                                    </td>
                                </tr>
                                <tr>
                                    <td align='right'>說明：</td>
                                    <td>
                                        <asp:TextBox ID="TB_說明" runat="server" Text='' TextMode="MultiLine" Width="650px" Height="280px"></asp:TextBox>
                                    </td>
                                </tr>
                                <asp:Panel ID="tr_case" runat="server">
                                    <tr>
                                        <td align='right'>條件：</td>
                                        <td>
                                            <div class="tabsubmenublock stripeMe font-normal">
                                                <table border="1">
                                                    <asp:Repeater ID="rpt_AND" runat="server" OnItemDataBound="rpt_AND_ItemDataBound">
                                                        <HeaderTemplate>
                                                            <thead>
                                                                <th>代碼</th>
                                                                <th></th>
                                                            </thead>
                                                        </HeaderTemplate>
                                                        <ItemTemplate>
                                                            <tr>
                                                                <td>
                                                                    <asp:TextBox ID="TB_c_group" runat="server" Text='<%# Container.DataItem %>' Width="30" Enabled="false"></asp:TextBox></td>
                                                                <td>
                                                                    <table border="1" style="display: inline-flex;">
                                                                        <asp:Repeater ID="rpt_OR" runat="server" OnItemDataBound="rpt_OR_ItemDataBound">
                                                                            <ItemTemplate>
                                                                                <tr>
                                                                                    <td style="width: 25px">
                                                                                        <asp:ImageButton ID="IB_del" runat="server" Height="20px" Width="20px" ImageUrl="~/images/icon-delete.png" CommandName="Del" CommandArgument='<%# Server.HtmlEncode(Eval("c_id").ToString()) %>' OnClick="IB_del_OR_Click" /></td>
                                                                                    <td>
                                                                                        <asp:DropDownList ID="DDL_code" runat="server" DataTextField="Text" DataValueField="Value" Enabled="false">
                                                                                        </asp:DropDownList>
                                                                                    </td>
                                                                                    <td>
                                                                                        <asp:DropDownList ID="DDL_condition" runat="server" DataTextField="Text" DataValueField="Value" Enabled="false">
                                                                                        </asp:DropDownList>
                                                                                    </td>
                                                                                    <td>
                                                                                        <asp:TextBox ID="TB_condition_value" runat="server" Text='<%# Server.HtmlEncode(Eval("c_group_condition_value").ToString()) %>' Enabled="false"></asp:TextBox>
                                                                                    </td>
                                                                                    <td>OR
                                                                                    </td>
                                                                                </tr>
                                                                            </ItemTemplate>
                                                                            <FooterTemplate>
                                                                                <tr>
                                                                                    <td></td>

                                                                                    <td>
                                                                                        <asp:DropDownList ID="DDL_code_OR" runat="server" DataTextField="Text" DataValueField="Value" OnSelectedIndexChanged="DDL_code_SelectedIndexChanged" AutoPostBack="true">
                                                                                        </asp:DropDownList>
                                                                                    </td>
                                                                                    <td>
                                                                                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="false" UpdateMode="Conditional">
                                                                                            <ContentTemplate>
                                                                                                <asp:DropDownList ID="DDL_condition_OR" runat="server" DataTextField="Text" DataValueField="Value">
                                                                                                </asp:DropDownList>
                                                                                            </ContentTemplate>
                                                                                            <Triggers>
                                                                                                <asp:AsyncPostBackTrigger ControlID="DDL_code_OR" EventName="SelectedIndexChanged" />
                                                                                            </Triggers>
                                                                                        </asp:UpdatePanel>
                                                                                    </td>

                                                                                    <td>
                                                                                        <asp:TextBox ID="TB_condition_value" runat="server" Text=''></asp:TextBox>
                                                                                    </td>
                                                                                    <td>
                                                                                        <asp:Button ID="BT_Add_OR" runat="server" Text="新增OR" OnClick="BT_Add_OR_Click" />
                                                                                    </td>
                                                                                </tr>
                                                                            </FooterTemplate>
                                                                        </asp:Repeater>
                                                                    </table>
                                                                    AND
                                                                </td>
                                                            </tr>
                                                        </ItemTemplate>
                                                        <FooterTemplate>
                                                            <tr>
                                                                <td>
                                                                    <asp:TextBox ID="TB_c_group" runat="server" Text='' Width="30"></asp:TextBox></td>
                                                                <td>
                                                                    <table>
                                                                        <tr>
                                                                            <td style="width: 25px"></td>
                                                                            <td>
                                                                                <asp:DropDownList ID="DDL_code_AND" runat="server" DataTextField="Text" DataValueField="Value"
                                                                                    OnSelectedIndexChanged="DDL_code_SelectedIndexChanged" AutoPostBack="true">
                                                                                </asp:DropDownList>
                                                                            </td>
                                                                            <td>
                                                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="false" UpdateMode="Conditional">
                                                                                    <ContentTemplate>
                                                                                        <asp:DropDownList ID="DDL_condition_AND" runat="server" DataTextField="Text" DataValueField="Value">
                                                                                        </asp:DropDownList>
                                                                                    </ContentTemplate>
                                                                                    <Triggers>
                                                                                        <asp:AsyncPostBackTrigger ControlID="DDL_code_AND" EventName="SelectedIndexChanged" />
                                                                                    </Triggers>
                                                                                </asp:UpdatePanel>
                                                                            </td>
                                                                            <td>
                                                                                <asp:TextBox ID="TB_condition_value" runat="server" Text=''></asp:TextBox>
                                                                            </td>
                                                                            <td>
                                                                                <asp:Button ID="BT_Add_AND" runat="server" Text="新增AND" OnClick="BT_Add_AND_Click" />
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </FooterTemplate>
                                                    </asp:Repeater>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </asp:Panel>
                            </table>
                        </div>
                        <br />
                        <div>
                            <asp:Button ID="btnQuery" runat="server" Text="預覽" Class="genbtnS" OnClick="btnQuery_Click"></asp:Button>
                            <asp:Button ID="btnBlackList" runat="server" Text="排除清單" Class="genbtnS ajax-Blacklist" OnClientClick="Blacklist()" Visible="false"></asp:Button>

                        </div>
                        <div class="stripeMe">
                            <cc1:SmartGridView ID="sgvList" BorderWidth="0px" CellPadding="0" AllowPaging="True" runat="server" AutoGenerateColumns="false" Width="100%" OnPageIndexChanging="sgvList_PageIndexChanging" OnRowDataBound="sgvList_RowDataBound">
                                <Columns>
                                    <asp:TemplateField HeaderText="功能">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="IB_mod" runat="server" Text="排除" ForeColor="Red" CommandArgument='<%# Server.HtmlEncode(Eval("tt_seno").ToString()) %>' OnClick="IB_mod_Click" OnClientClick="return confirm('確定要排除 ?');" />


                                        </ItemTemplate>
                                        <ItemStyle HorizontalAlign="Center" />
                                        <HeaderStyle Width="80px"></HeaderStyle>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="單位名稱">
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("單位名稱").ToString())) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="10px" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="洽案／契約名稱">
                                        <ItemTemplate>
                                            <%# Eval("議約編號") %><br />
                                            <asp:LinkButton ID="LB_View" CssClass="ajax-ViewDetail" runat="server" CommandName="View" CommandArgument='<%# Eval("tt_seno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_name").ToString())) %>' OnClientClick='<%# "ViewDetail("+ Server.HtmlEncode(Eval("tt_seno").ToString())+")" %>'></asp:LinkButton><br />
                                        </ItemTemplate>
                                        <ItemStyle Width="400px" />
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="tt_compname" HeaderText="客戶名稱">
                                        <ItemStyle Width="250px" />
                                    </asp:BoundField>
                                    <asp:TemplateField HeaderText="分案日<hr>結案日">
                                        <HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Center" VerticalAlign="Middle" Width="100px"></ItemStyle>
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("分案日").ToString())) %><hr />
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("結案日").ToString())) %>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="洽案<br>承辦人<hr>技轉C<br>承辦人">
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_promoter_name").ToString())) %><hr>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_handle_c_name").ToString())) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="120px" HorizontalAlign="Center" />
                                    </asp:TemplateField>

                                    <asp:BoundField DataField="案件類型" HeaderText="案件類型">
                                        <ItemStyle Width="100px" />
                                    </asp:BoundField>
                                    <asp:BoundField DataField="技轉狀態" HeaderText="案件狀態">
                                        <ItemStyle Width="100px" HorizontalAlign="Center" />
                                    </asp:BoundField>
                                    <asp:TemplateField HeaderText="技轉<br>承辦人">
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_handle_name").ToString())) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="80px" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="機密等級">
                                        <ItemTemplate>
                                            <asp:Image ID="Image1" runat="server" Height="31px" ImageUrl="../images/CONFIDENTIAL.png" Width="80px" />
                                        </ItemTemplate>
                                        <ItemStyle Width="120px" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="新創案">
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("新創案").ToString())) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="10px" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="境外實施">
                                        <ItemTemplate>
                                            <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("境外實施").ToString())) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="10px" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                </Columns>
                                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span style='color:#578c27'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#578c27'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#578c27'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#578c27'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#578c27'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#578c27'>頁</span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Left" CssClass="PagerStyle" />
                                <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="~/images/first.gif" FirstPageText="第一頁"
                                    LastPageImageUrl="~/images/last.gif" LastPageText="最後一頁" NextPageImageUrl="~/images/next.gif"
                                    NextPageText="下一頁" PreviousPageImageUrl="~/images/previous.gif" PreviousPageText="上一頁" />
                                <EmptyDataTemplate>
                                    <div style="text-align: center;">無符合資料</div>
                                </EmptyDataTemplate>
                            </cc1:SmartGridView>
                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc1:Foot runat="server" ID="Foot" />
        <script type="text/javascript">
            function reflash_topic(tag, arg) {
                __doPostBack(tag, arg);
            }
            function Blacklist() {
                $(".ajax-Blacklist").colorbox({
                    href: "./TechCase_todo_blacklist.aspx?t_id=" +<%= ViewState["t_id"].ToString() %> ,
                    iframe: true, width: "90%", height: "90%", transition: "none", opacity: "0.5",
                    onClosed: function () {
                        $('html, body').css('overflow', '');
                        reflash_topic("renew", 0);
                    }
                });
            }


            function ViewDetail(tt_seno) {
                $(".ajax-ViewDetail").colorbox({
                    href: "./TechCase_view.aspx?tt_seno=" + tt_seno,
                    iframe: true, width: "90%", height: "90%", transition: "none", opacity: "0.5",
                    onClosed: function () {
                        $('html, body').css('overflow', '');
                        reflash_topic("renew", 0);
                    }
                });
            }

            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });

            tinymce.init({
                selector: '#TB_說明',
                width: "100%",
                height: "250",
                language: 'zh_TW',
                fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 24pt 36pt",
                toolbar: "insertfile undo redo | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor | fontsizeselect",
                statusbar: false,
                plugins: [' code', 'textcolor'],
            });
        </script>
    </form>
</body>
</html>
