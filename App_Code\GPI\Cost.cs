﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// 成本訂價
    /// </summary>
    public class Cost : GPI.mySQLHelper
    {
        #region  私有變數
        private string _errorMessage;

        private int _seqsn;
        private int cost_id;
        private int cost_ver;
        private string cost_evaltype;
        private int cost_suggestfee1;
        private int cost_basefee1;
        private int cost_promincome;
        private int cost_suggestfee2;
        private int cost_basefee2;
        private int cost_techincome;
        private string cost_memo1;
        private string cost_memo2;
        private string cost_memo6;
        private string cost_evaluememo;
        private string cost_transmemo;

        //計畫經費
        private int cost1_id;
        private int cost1_ver;
        private string cost1_dept;
        private string cost1_deptnm;
        private int cost1_per;
        private int cost1_travel;
        private int cost1_material;
        private int cost1_maintain;
        private int cost1_business;
        private int cost1_equipuse;
        private int cost1_manage;
        private int cost1_foundfee;
        private int cost1_other;
        private int cost1_land;
        private int cost1_improve;
        private int cost1_house;
        private int cost1_instrument;
        private int cost1_infoequip;
        private int cost1_transport;
        private int cost1_otherequip;
        private int cost1_rent;
        private int cost1_total;

        //院內委託項目
        private int cost2_id;
        private int cost2_ver;
        private string cost2_dept;
        private string cost2_deptnm;
        private int cost2_enginfee;
        private int cost2_laborfee;
        private int cost2_otherfee;
        private int cost2_total;

        //技轉授權金
        private int cost4_id;
        private int cost4_ver;
        private string cost4_resultfrom;
        private int cost4_tafbyfixfee;
        private int cost4_pafbyfixfee;
        private int cost4_cash_total;

        private string _keyinempno;
        private string _keyinempname;
        private string _keyindate;
        private string _modempno;
        private string _modempname;
        private string _moddate;
        private string _modify;
        private string _delempno;
        private string _delempname;
        private string _delete;


        #endregion

        #region  建構子
        public Cost()
        {
            _errorMessage = String.Empty;

            _seqsn = 0;
            cost_id = 0;
            cost_ver = 0;
            cost_evaltype = string.Empty;
            cost_suggestfee1 = 0;
            cost_basefee1 = 0;
            cost_promincome = 0;
            cost_suggestfee2 = 0;
            cost_basefee2 = 0;
            cost_techincome = 0;
            cost_memo1 = string.Empty;
            cost_memo2 = string.Empty;
            cost_memo6 = string.Empty;
            cost_evaluememo = string.Empty;
            cost_transmemo = string.Empty;

            //計畫經費
            cost1_id = 0;
            cost1_ver = 0;
            cost1_dept = string.Empty;
            cost1_deptnm = string.Empty;
            cost1_per = 0;
            cost1_travel = 0;
            cost1_material = 0;
            cost1_maintain = 0;
            cost1_business = 0;
            cost1_equipuse = 0;
            cost1_manage = 0;
            cost1_foundfee = 0;
            cost1_other = 0;
            cost1_land = 0;
            cost1_improve = 0;
            cost1_house = 0;
            cost1_instrument = 0;
            cost1_infoequip = 0;
            cost1_transport = 0;
            cost1_otherequip = 0;
            cost1_rent = 0;
            cost1_total = 0;

            //院內委託項目
            cost2_id = 0;
            cost2_ver = 0;
            cost2_dept = string.Empty;
            cost2_deptnm = string.Empty;
            cost2_enginfee = 0;
            cost2_laborfee = 0;
            cost2_otherfee = 0;
            cost2_total = 0;

            //技轉授權金
            cost4_id = 0;
            cost4_ver = 0;
            cost4_resultfrom = string.Empty;
            cost4_tafbyfixfee = 0;
            cost4_pafbyfixfee = 0;
            cost4_cash_total = 0;

            _keyinempno = String.Empty;
            _keyinempname = String.Empty;
            _keyindate = String.Empty;
            _modempno = String.Empty;
            _modempname = String.Empty;
            _moddate = String.Empty;
            _modify = String.Empty;
            _delempno = String.Empty;
            _delempname = String.Empty;
            _delete = String.Empty;

        }
        #endregion

        #region  公有屬性
        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 標案流水號
        /// </summary>
        public int Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }
        /// <summary>
        /// 流水號
        /// </summary>
        public int Cost_id
        {
            get { return cost_id; }
            set { cost_id = value; }
        }
        /// <summary>
        /// 版本
        /// </summary>
        public int Cost_ver
        {
            get { return cost_ver; }
            set { cost_ver = value; }
        }
        /// <summary>
        /// 成本訂價法
        /// </summary>
        public string Cost_evaltype
        {
            get { return cost_evaltype; }
            set { cost_evaltype = value; }
        }
        /// <summary>
        /// 技術建議報價(未稅)
        /// </summary>
        public int Cost_suggestfee1
        {
            get { return cost_suggestfee1; }
            set { cost_suggestfee1 = value; }
        }
        /// <summary>
        /// 技服建議底價(未稅)
        /// </summary>
        public int Cost_basefee1
        {
            get { return cost_basefee1; }
            set { cost_basefee1 = value; }
        }
        /// <summary>
        /// 簽約年度預計認列收入數
        /// </summary>
        public int Cost_promincome
        {
            get { return cost_promincome; }
            set { cost_promincome = value; }
        }
        /// <summary>
        /// 技轉建議報價 (未稅)
        /// </summary>
        public int Cost_suggestfee2
        {
            get { return cost_suggestfee2; }
            set { cost_suggestfee2 = value; }
        }
        /// <summary>
        /// 技轉建議底價 (未稅)
        /// </summary>
        public int Cost_basefee2
        {
            get { return cost_basefee2; }
            set { cost_basefee2 = value; }
        }
        /// <summary>
        /// 簽約年度預計認列收入數
        /// </summary>
        public int Cost_techincome
        {
            get { return cost_techincome; }
            set { cost_techincome = value; }
        }
        /// <summary>
        /// 計畫經費說明
        /// </summary>
        public string Cost_memo1
        {
            get { return cost_memo1; }
            set { cost_memo1 = value; }
        }
        /// <summary>
        /// 院內委託項目說明
        /// </summary>
        public string Cost_memo2
        {
            get { return cost_memo2; }
            set { cost_memo2 = value; }
        }
        /// <summary>
        /// 計價綜合說明
        /// </summary>
        public string Cost_memo6
        {
            get { return cost_memo6; }
            set { cost_memo6 = value; }
        }
        /// <summary>
        /// 計價評估項目及說明
        /// </summary>
        public string Cost_evaluememo
        {
            get { return cost_evaluememo; }
            set { cost_evaluememo = value; }
        }
        /// <summary>
        /// 移轉家次及金額說明
        /// </summary>
        public string Cost_transmemo
        {
            get { return cost_transmemo; }
            set { cost_transmemo = value; }
        }

        #region 計畫經費

        /// <summary>
        /// 識別號
        /// </summary>
        public int Cost1_id
        {
            get { return cost1_id; }
            set { cost1_id = value; }
        }
        /// <summary>
        /// 送核版次(標案一律是”1”不分)
        /// </summary>
        public int Cost1_ver
        {
            get { return cost1_ver; }
            set { cost1_ver = value; }
        }
        /// <summary>
        /// 執行部門
        /// </summary>
        public string Cost1_dept
        {
            get { return cost1_dept; }
            set { cost1_dept = value; }
        }
        /// <summary>
        /// 執行部門名稱
        /// </summary>
        public string Cost1_deptnm
        {
            get { return cost1_deptnm; }
            set { cost1_deptnm = value; }
        }
        /// <summary>
        /// 人事費
        /// </summary>
        public int Cost1_per
        {
            get { return cost1_per; }
            set { cost1_per = value; }
        }
        /// <summary>
        /// 旅運費
        /// </summary>
        public int Cost1_travel
        {
            get { return cost1_travel; }
            set { cost1_travel = value; }
        }
        /// <summary>
        /// 材料費
        /// </summary>
        public int Cost1_material
        {
            get { return cost1_material; }
            set { cost1_material = value; }
        }
        /// <summary>
        /// 維護費
        /// </summary>
        public int Cost1_maintain
        {
            get { return cost1_maintain; }
            set { cost1_maintain = value; }
        }
        /// <summary>
        /// 業務費
        /// </summary>
        public int Cost1_business
        {
            get { return cost1_business; }
            set { cost1_business = value; }
        }
        /// <summary>
        /// 設備使用費
        /// </summary>
        public int Cost1_equipuse
        {
            get { return cost1_equipuse; }
            set { cost1_equipuse = value; }
        }
        /// <summary>
        /// 管理費
        /// </summary>
        public int Cost1_manage
        {
            get { return cost1_manage; }
            set { cost1_manage = value; }
        }
        /// <summary>
        /// 預期利潤
        /// </summary>
        public int Cost1_foundfee
        {
            get { return cost1_foundfee; }
            set { cost1_foundfee = value; }
        }
        /// <summary>
        /// 其它
        /// </summary>
        public int Cost1_other
        {
            get { return cost1_other; }
            set { cost1_other = value; }
        }
        /// <summary>
        /// 土地
        /// </summary>
        public int Cost1_land
        {
            get { return cost1_land; }
            set { cost1_land = value; }
        }
        /// <summary>
        /// 土地改良物
        /// </summary>
        public int Cost1_improve
        {
            get { return cost1_improve; }
            set { cost1_improve = value; }
        }
        /// <summary>
        /// 房屋建築及設備
        /// </summary>
        public int Cost1_house
        {
            get { return cost1_house; }
            set { cost1_house = value; }
        }
        /// <summary>
        /// 儀器及機械設備
        /// </summary>
        public int Cost1_instrument
        {
            get { return cost1_instrument; }
            set { cost1_instrument = value; }
        }
        /// <summary>
        /// 資訊設備
        /// </summary>
        public int Cost1_infoequip
        {
            get { return cost1_infoequip; }
            set { cost1_infoequip = value; }
        }
        /// <summary>
        /// 交通及運輸設備
        /// </summary>
        public int Cost1_transport
        {
            get { return cost1_transport; }
            set { cost1_transport = value; }
        }
        /// <summary>
        /// 雜項設備
        /// </summary>
        public int Cost1_otherequip
        {
            get { return cost1_otherequip; }
            set { cost1_otherequip = value; }
        }
        /// <summary>
        /// 其他權利
        /// </summary>
        public int Cost1_rent
        {
            get { return cost1_rent; }
            set { cost1_rent = value; }
        }
        /// <summary>
        /// 計畫經費合計
        /// </summary>
        public int Cost1_total
        {
            get { return cost1_total; }
            set { cost1_total = value; }
        }
        
        #endregion

        #region 院內委託項目
        /// <summary>
        /// 識別號
        /// </summary>
        public int Cost2_id
        {
            get { return cost2_id; }
            set { cost2_id = value; }
        }
        /// <summary>
        /// 送核版次
        /// </summary>
        public int Cost2_ver
        {
            get { return cost2_ver; }
            set { cost2_ver = value; }
        }
        /// <summary>
        /// 執行部門
        /// </summary>
        public string Cost2_dept
        {
            get { return cost2_dept; }
            set { cost2_dept = value; }
        }
        /// <summary>
        /// 執行部門名稱
        /// </summary>
        public string Cost2_deptnm
        {
            get { return cost2_deptnm; }
            set { cost2_deptnm = value; }
        }
        /// <summary>
        /// 工程委託
        /// </summary>
        public int Cost2_enginfee
        {
            get { return cost2_enginfee; }
            set { cost2_enginfee = value; }
        }
        /// <summary>
        /// 勞務委託
        /// </summary>
        public int Cost2_laborfee
        {
            get { return cost2_laborfee; }
            set { cost2_laborfee = value; }
        }
        /// <summary>
        /// 其它
        /// </summary>
        public int Cost2_otherfee
        {
            get { return cost2_otherfee; }
            set { cost2_otherfee = value; }
        }
        /// <summary>
        /// 合計
        /// </summary>
        public int Cost2_total
        {
            get { return cost2_total; }
            set { cost2_total = value; }
        }
        #endregion

        #region 技轉授權金
        /// <summary>
        /// 識別號
        /// </summary>
        public int Cost4_id
        {
            get { return cost4_id; }
            set { cost4_id = value; }
        }
        /// <summary>
        /// 送核版次
        /// </summary>
        public int Cost4_ver
        {
            get { return cost4_ver; }
            set { cost4_ver = value; }
        }
        /// <summary>
        /// 成果來源
        /// </summary>
        public string Cost4_resultfrom
        {
            get { return cost4_resultfrom; }
            set { cost4_resultfrom = value; }
        }
        /// <summary>
        /// 技術授權金
        /// </summary>
        public int Cost4_tafbyfixfee
        {
            get { return cost4_tafbyfixfee; }
            set { cost4_tafbyfixfee = value; }
        }
        /// <summary>
        /// 專利授權(讓與)金
        /// </summary>
        public int Cost4_pafbyfixfee
        {
            get { return cost4_pafbyfixfee; }
            set { cost4_pafbyfixfee = value; }
        }
        /// <summary>
        /// 技轉訂價合計
        /// </summary>
        public int Cost4_cash_total
        {
            get { return cost4_cash_total; }
            set { cost4_cash_total = value; }
        }

        #endregion

        /// <summary>
        /// 填寫人工號
        /// </summary>
        public string KeyinEmpNo
        {
            get { return _keyinempno; }
            set { _keyinempno = value; }
        }
        /// <summary>
        /// 填寫人姓名
        /// </summary>
        public string KeyinEmpName
        {
            get { return _keyinempname; }
            set { _keyinempname = value; }
        }
        /// <summary>
        /// 填寫日期
        /// </summary>
        public string KeyinDate
        {
            get { return _keyindate; }
            set { _keyindate = value; }
        }
        /// <summary>
        /// 修改人工號
        /// </summary>
        public string ModEmpNo
        {
            get { return _modempno; }
            set { _modempno = value; }
        }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModEmpName
        {
            get { return _modempname; }
            set { _modempname = value; }
        }
        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModDate
        {
            get { return _moddate; }
            set { _moddate = value; }
        }
        /// <summary>
        /// 是否可修改Y. 是 N. 否, Default:Y
        /// </summary>
        public string Modify
        {
            get { return _modify; }
            set { _modify = value; }
        }
        /// <summary>
        /// 刪除人工號
        /// </summary>
        public string DelEmpNo
        {
            get { return _delempno; }
            set { _delempno = value; }
        }
        /// <summary>
        /// 刪除人姓名
        /// </summary>
        public string DelEmpName
        {
            get { return _delempname; }
            set { _delempname = value; }
        }
        /// <summary>
        /// 刪除註記(Default:N)
        /// </summary>
        public string Main_Delete
        {
            get { return _delete; }
            set { _delete = value; }
        }

        #endregion

        #region  公有函式
        /// <summary>
        /// 取得成本訂價版本
        /// </summary>
        /// <returns></returns>
        public int GetVer()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT cost_ver FROM gpi_cost
WHERE cost_seqsn = @cost_seqsn
";
            oCmd.Parameters.AddWithValue("@cost_seqsn", _seqsn);

            string data = this.getTopOne(oCmd, CommandType.Text);
            int ver = 1;//成本訂價預設為「1」
            if (data != string.Empty)
            {
                ver = int.Parse(data);
            }
            return ver;
        }
        /// <summary>
        /// 取得成本訂價明細
        /// </summary>
        /// <returns></returns>
        public DataTable GetDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_cost_select_by_seqsn";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost_ver", cost_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public DataTable GetHistoryList()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT
cost_seqsn
,cost_ver
,cost_modempno
,cost_modname
,cost_moddate
FROM engage_his..gpi_cost
WHERE cost_seqsn = @main_seqsn
";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        #region 新增版次
        /// <summary>
        /// 新增版次
        /// </summary>
        /// <returns></returns>
        public bool NewVer()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_cost_edit_ver";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@empno", _modempno);
            oCmd.Parameters.AddWithValue("@empname", _modempname);

            SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar,100);
            msg.Direction = ParameterDirection.Output;

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                _errorMessage = msg.Value.ToString();
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        #endregion


        #region 成本訂價法
        /// <summary>
        /// 成本訂價法
        /// </summary>
        /// <returns></returns>
        public bool Update_evaltype()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
UPDATE gpi_cost 
SET cost_evaltype = @cost_evaltype
    ,cost_modempno = @mod_empno
    ,cost_modname = @mod_empname
    ,cost_moddate = CONVERT(varchar,GETDATE(),112)
WHERE cost_seqsn = @main_seqsn AND cost_ver = @cost_ver

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost_ver", cost_ver);
            oCmd.Parameters.AddWithValue("@cost_evaltype", cost_evaltype);
            oCmd.Parameters.AddWithValue("@mod_empno", _modempno);
            oCmd.Parameters.AddWithValue("@mod_empname", _modempname);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        #endregion

        #region 計畫經費
        /// <summary>
        /// 取得成本訂價計畫經費資料
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept1()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_costdept1_query";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", cost_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得成本訂價計畫經費明細資料
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept1_detail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT * FROM gpi_costdept1
WHERE cost1_id= @cost1_id";

            oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public bool Update_costdept1()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_codtdept1_update";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost1_ver", cost1_ver);
            oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);
            oCmd.Parameters.AddWithValue("@cost1_dept", cost1_dept);
            oCmd.Parameters.AddWithValue("@cost1_deptnm", cost1_deptnm);
            oCmd.Parameters.AddWithValue("@cost1_per", cost1_per);
            oCmd.Parameters.AddWithValue("@cost1_travel", cost1_travel);
            oCmd.Parameters.AddWithValue("@cost1_material", cost1_material);
            oCmd.Parameters.AddWithValue("@cost1_maintain", cost1_maintain);
            oCmd.Parameters.AddWithValue("@cost1_business", cost1_business);
            oCmd.Parameters.AddWithValue("@cost1_equipuse", cost1_equipuse);
            oCmd.Parameters.AddWithValue("@cost1_other", cost1_other);
            oCmd.Parameters.AddWithValue("@cost1_land", cost1_land);
            oCmd.Parameters.AddWithValue("@cost1_improve", cost1_improve);
            oCmd.Parameters.AddWithValue("@cost1_house", cost1_house);
            oCmd.Parameters.AddWithValue("@cost1_instrument", cost1_instrument);
            oCmd.Parameters.AddWithValue("@cost1_infoequip", cost1_infoequip);
            oCmd.Parameters.AddWithValue("@cost1_transport", cost1_transport);
            oCmd.Parameters.AddWithValue("@cost1_otherequip", cost1_otherequip);
            oCmd.Parameters.AddWithValue("@cost1_rent", cost1_rent);
            oCmd.Parameters.AddWithValue("@cost1_total", cost1_total);
            oCmd.Parameters.AddWithValue("@cost_memo1", cost_memo1);
            oCmd.Parameters.AddWithValue("@mod_empno", _modempno);
            oCmd.Parameters.AddWithValue("@cost1_manage", cost1_manage);
            oCmd.Parameters.AddWithValue("@cost1_foundfee", cost1_foundfee);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Delete_costdept1()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
DELETE gpi_costdept1
WHERE cost1_id= @cost1_id
";
            oCmd.Parameters.AddWithValue("@cost1_id", cost1_id);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

        #region 院內委託項目
        /// <summary>
        /// 取得院內委託項目
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept2()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_costdept2_query";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", cost_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        /// <summary>
        /// 取得成本訂價計畫經費明細資料
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept2_detail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT * FROM gpi_costdept2
WHERE cost2_id= @cost2_id";

            oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public bool Update_costdept2()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_codtdept2_update";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost2_ver", cost2_ver);
            oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);
            oCmd.Parameters.AddWithValue("@cost2_dept", cost2_dept);
            oCmd.Parameters.AddWithValue("@cost2_deptnm", cost2_deptnm);
            oCmd.Parameters.AddWithValue("@cost2_enginfee", cost2_enginfee);
            oCmd.Parameters.AddWithValue("@cost2_laborfee", cost2_laborfee);
            oCmd.Parameters.AddWithValue("@cost2_otherfee", cost2_otherfee);
            oCmd.Parameters.AddWithValue("@cost2_total", cost2_total);
            oCmd.Parameters.AddWithValue("@cost_memo2", cost_memo2);
            oCmd.Parameters.AddWithValue("@mod_empno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Delete_costdept2()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
DELETE gpi_costdept2
WHERE cost2_id= @cost2_id
";
            oCmd.Parameters.AddWithValue("@cost2_id", cost2_id);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        #region 技服報價及底價
        public bool Update_memo6()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_pricing_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost_ver", cost_ver);
            oCmd.Parameters.AddWithValue("@cost_suggestfee1", cost_suggestfee1);
            oCmd.Parameters.AddWithValue("@cost_basefee1", cost_basefee1);
            oCmd.Parameters.AddWithValue("@cost_promincome", cost_promincome);
            oCmd.Parameters.AddWithValue("@cost_memo6", cost_memo6);
            oCmd.Parameters.AddWithValue("@mod_empno", _modempno);
            oCmd.Parameters.AddWithValue("@cost_evaltype", cost_evaltype);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        #endregion

        #region 技轉授權金
        /// <summary>
        /// 取得授權金一次計價項
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept4()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_costdept4_query";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", cost_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public bool Update_evaluememo()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
UPDATE gpi_cost
   SET cost_evaluememo = @cost_evaluememo
      ,cost_moddate = convert(varchar,getdate(),112)
      ,cost_modempno = @cost_modempno
      ,cost_modname = @cost_modname
 WHERE cost_seqsn = @cost_seqsn 
    AND cost_ver = @cost_ver
";
            oCmd.Parameters.AddWithValue("@cost_evaluememo", cost_evaluememo);
            oCmd.Parameters.AddWithValue("@cost_modempno", _modempno);
            oCmd.Parameters.AddWithValue("@cost_modname", _modempname);
            oCmd.Parameters.AddWithValue("@cost_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost_ver", cost_ver);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 取得成本訂價計畫經費明細資料
        /// </summary>
        /// <returns></returns>
        public DataTable Get_costdept4_detail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT * FROM gpi_costdept4
WHERE cost4_id= @cost4_id";

            oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
        public bool Update_costdept4()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
IF NOT EXISTS (SELECT cost4_id FROM gpi_costdept4 WHERE cost4_id= @cost4_id)
BEGIN
    INSERT INTO gpi_costdept4
           (cost4_seqsn
           ,cost4_ver
           ,cost4_resultfrom
           ,cost4_tafbyfixfee
           ,cost4_pafbyfixfee
           ,cost4_cash_total)
     VALUES
           (@cost4_seqsn
           ,@cost4_ver
           ,@cost4_resultfrom
           ,@cost4_tafbyfixfee
           ,@cost4_pafbyfixfee
           ,@cost4_cash_total)
END
ELSE
BEGIN
    UPDATE gpi_costdept4
       SET cost4_resultfrom = @cost4_resultfrom
          ,cost4_tafbyfixfee = @cost4_tafbyfixfee
          ,cost4_pafbyfixfee = @cost4_pafbyfixfee
          ,cost4_cash_total = @cost4_cash_total
     WHERE cost4_id = @cost4_id
END
";
            oCmd.Parameters.AddWithValue("@cost4_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost4_ver", cost4_ver);
            oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);
            oCmd.Parameters.AddWithValue("@cost4_resultfrom", cost4_resultfrom);
            oCmd.Parameters.AddWithValue("@cost4_tafbyfixfee", cost4_tafbyfixfee);
            oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee", cost4_pafbyfixfee);
            oCmd.Parameters.AddWithValue("@cost4_cash_total", cost4_cash_total);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool Delete_costdept4()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
DELETE gpi_costdept4
WHERE cost4_id= @cost4_id
";
            oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #endregion

        #region 技轉報價/底價項目
        public bool Update_transmemo()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
UPDATE gpi_cost 
SET cost_suggestfee2 = @cost_suggestfee2
    ,cost_basefee2 = @cost_basefee2
    ,cost_techincome = @cost_techincome
    ,cost_transmemo = @cost_transmemo
    ,cost_modempno = @mod_empno
    ,cost_modname = @mod_empname
    ,cost_moddate = CONVERT(varchar,GETDATE(),112)
WHERE cost_seqsn = @main_seqsn AND cost_ver = @cost_ver

";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@cost_ver", cost_ver);
            oCmd.Parameters.AddWithValue("@cost_suggestfee2", cost_suggestfee2);
            oCmd.Parameters.AddWithValue("@cost_basefee2", cost_basefee2);
            oCmd.Parameters.AddWithValue("@cost_techincome", cost_techincome);
            oCmd.Parameters.AddWithValue("@cost_transmemo", cost_transmemo);
            oCmd.Parameters.AddWithValue("@mod_empno", _modempno);
            oCmd.Parameters.AddWithValue("@mod_empname", _modempname);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        #endregion

        /// <summary>
        /// 檢查是否已填寫「風險評估」
        /// </summary>
        /// <returns></returns>
        public string Get_rams_show()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_rams_show";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            return this.getTopOne(oCmd, CommandType.StoredProcedure);
        }
        #endregion
    }
}