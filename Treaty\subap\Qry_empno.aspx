﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Qry_empno.aspx.cs" Inherits="subap_Qry_empno" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="UserControl/userctrl_RegexValidator.ascx" TagName="userctrl_RegexValidator" TagPrefix="uc1" %>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>查詢員工資料</title>
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <base target="_self" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <table>
                <tr>
                    <td>單位：&nbsp;
                    <asp:DropDownList ID="ddl_orgcd" runat="server" DataTextField="orgcd_name" DataValueField="orgcd" />
                    </td>
                    <td>關鍵字(以下欄位搜尋)：
                        <asp:TextBox ID="tbx_keyword" runat="server"></asp:TextBox>&nbsp;
                        <asp:Button ID="btn_Query" runat="server" CssClass="button" onmouseout="this.className='btn_mouseout'" onmouseover="this.className='btn_mouseover'" Text="查詢" OnClick="btn_Query_Click" /></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <hr />
                        <cc1:SmartGridView ID="SGV_emp" runat="server" AutoGenerateColumns="False"
                            CellPadding="4" Width="600px"
                            GridLines="None"
                            EnableModelValidation="True" AllowPaging="True"
                            OnPageIndexChanged="SGV_empno_PageIndexChanged"
                            OnPageIndexChanging="SGV_empno_PageIndexChanging"
                            OnRowCreated="SGV_empno_RowCreated"
                            OnRowCommand="SGV_empno_RowCommand">
                            <FooterStyle BackColor="#5d7b9d" Font-Bold="True" />
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                            <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" />
                            <Columns>
                                <asp:BoundField DataField="org_abbr_chnm2" HeaderText="單位" SortExpression="com_orgcd">
                                    <HeaderStyle Width="10%"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                </asp:BoundField>
                                <asp:TemplateField HeaderText="員工工號" SortExpression="com_empno">
                                    <HeaderStyle Width="15%"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                    <ItemTemplate>
                                        <asp:LinkButton ID="LB_empno" runat="server" CommandName="view_case" CommandArgument='<%# Server.HtmlEncode(Eval("com_empno").ToString()) %>'>  <%#DataBinder.Eval(Container.DataItem, "com_empno")%></asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:BoundField DataField="com_cname" HeaderText="姓名" SortExpression="com_cname">
                                    <HeaderStyle Width="15%"></HeaderStyle>
                                    <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                </asp:BoundField>
                                <asp:BoundField DataField="com_deptid" HeaderText="部門" SortExpression="com_deptid">
                                    <HeaderStyle Width="10%"></HeaderStyle>
                                </asp:BoundField>
                                <asp:BoundField DataField="com_telext" HeaderText="電話" SortExpression="com_telext">
                                    <HeaderStyle Width="15%"></HeaderStyle>
                                </asp:BoundField>
                                <asp:BoundField DataField="com_mailadd" HeaderText="Email" SortExpression="com_mailadd">
                                    <HeaderStyle Width="35%"></HeaderStyle>
                                </asp:BoundField>
                            </Columns>
                            <EmptyDataTemplate>
                                <!--當找不到資料時則顯示「查無資料」-->
                                <asp:Label ID="Label1" runat="server" ForeColor="Red" Text="查無資料!"></asp:Label>
                            </EmptyDataTemplate>
                            <FooterStyle BackColor="White" />
                            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />

                            <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="1px" HorizontalAlign="Left" CssClass="PagerStyle" Font-Size="Medium" Font-Overline="False" Font-Bold="True" ForeColor="#FF0066" />
                            <CustomPagerSettings TextFormat="<span style='color:#004e98'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#004e98'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#004e98'>筆</span>&nbsp;&nbsp;<span style='color:#004e98'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#004e98'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#004e98'>頁</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" />
                            <PagerSettings FirstPageImageUrl="../images/move_first.gif" PreviousPageImageUrl="../images/prev.gif" NextPageImageUrl="../images/next.gif" LastPageImageUrl="../images/move_lest.gif" PageButtonCount="10" />
                        </cc1:SmartGridView>

                    </td>
                </tr>
            </table>
        </span>
        <%--        <asp:SqlDataSource ID="ds_org" runat="server" ConnectionString="<%$ ConnectionStrings:common %>" SelectCommand="select org_orgcd AS orgcd, org_orgcd + '-' + org_abbr_chnm2 AS orgcd_name from common..orgcod where org_status = 'A'" />
        <asp:SqlDataSource ID="SDS_emp" runat="server" ConnectionString="<%$ ConnectionStrings:common %>" />--%>
        <uc1:userctrl_RegexValidator ID="uc_regex" runat="server" />
    </form>
</body>
</html>
