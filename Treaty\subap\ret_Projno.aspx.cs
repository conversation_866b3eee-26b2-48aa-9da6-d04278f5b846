﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;


public partial class web_ret_proj : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class Proj
    {
        private string orgcd;
        private string pojno;
        private string pojcname;
        public string c_orgcd
        {
            get { return orgcd; }
            set { orgcd = value; }
        }
        public string c_pojno
        {
            get { return pojno; }
            set { pojno = value; }
        }
        public string c_pojcname
        {
            get { return pojcname; }
            set { pojcname = value; }
        }
    }
    protected void Page_Load(object sender, System.EventArgs e)
    {
        Proj u = new Proj();
        SqlCommand oCmd = new SqlCommand();
        if (Session["projno"] != null)
        {
            //string strSQL = @" SELECT top 1 s20_orgcd,s20_pojno,s20_pojcname FROM common..prs020 where s20_pojno = @pojno ";
            //oCmd.Parameters.AddWithValue("@pojno", Session["projno"].ToString());
            //oCmd.CommandText = strSQL;
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString);
            //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"SELECT top 1 s20_orgcd,s20_pojno,s20_pojcname FROM common..prs020 where s20_pojno = @pojno ";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@pojno", oRCM.SQLInjectionReplaceAll(Session["projno"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(ds);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            if (ds.Tables[0].DefaultView.Count == 1)
            {
                u.c_orgcd = ds.Tables[0].Rows[0][0].ToString().Trim().Replace("\"", "\\\"");
                u.c_pojno = ds.Tables[0].Rows[0][1].ToString().Trim().Replace("\"", "\\\"");
                u.c_pojcname = ds.Tables[0].Rows[0][2].ToString().Trim().Replace("\"", "\\\"");
            }
            if (ds.Tables[0].DefaultView.Count == 0)
            {
                u.c_orgcd = "";
                u.c_pojno = "error0";
                u.c_pojcname = "";
            }
            if (ds.Tables[0].DefaultView.Count > 1)
            {
                u.c_orgcd = "";
                u.c_pojno = "error2";
                u.c_pojcname = "";
            }
        }
        else
        {
            u.c_orgcd = "";
            u.c_pojno = "error0";
            u.c_pojcname = "";
        }
        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        Response.Write(j);  //輸出JSONP的內容
    }

}