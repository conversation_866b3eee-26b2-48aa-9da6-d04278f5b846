﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_ECP_showdoc.aspx.cs" Inherits="Treaty_webpage_TechCase_ECP_showdoc" %>


<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <%--<script type="text/javascript" src="../Scripts/autoheight.js"></script>--%>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>


    <script src="../Scripts/pdf/webviewer.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {

        });
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="stripeMe" style="margin-left: 15px; margin-top: 25px">
            <div id='viewer' class="w-100" style="height: 600px;"></div>
            <br />
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                <ContentTemplate>
                    <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" Width="90%" OnRowCommand="gv_data_RowCommand">
                        <AlternatingRowStyle CssClass="alt" />
                        <Columns>
                            <asp:TemplateField HeaderText="文件名稱">
                                <ItemTemplate>
                                    <asp:LinkButton ID="LB_filename" runat="server" Text='<%# Server.HtmlEncode(Eval("檔名").ToString()) %>' CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' CommandName="xDownload"></asp:LinkButton>
                                </ItemTemplate>
                            </asp:TemplateField>
                        </Columns>
                    </asp:GridView>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
    </form>


    <script>

        const urlParams = new URLSearchParams(window.location.search);
        // 獲取 'seno' 參數的值
        const seno = urlParams.get('seno');
        const guid = urlParams.get('guid');
        WebViewer({
            //https://pdfjs.express/
            path: '../Scripts/pdf/lib', // path to the PDF.js Express'lib' folder on your server
            licenseKey: 'IlvvD6IOFIt2VWd97nYW',
            //'cXhXX7QAzhsDbCIe5Xr8',
            //initialDoc: "./pdfShow.ashx",
            disabledElements: [
                'printButton',
                'downloadButton',
            ]
            // initialDoc: '/path/to/my/file.pdf',  // You can also use documents on your server
        }, document.getElementById('viewer'))
            .then(instance => {
                // now you can access APIs through the WebViewer instance
                const { Core, UI } = instance;
                instance.UI.enableElements(['outlinesPanelButton']);//加入大綱
                instance.setFitMode(instance.FitMode.FitWidth);//設定符合視窗


                // adding an event listener for when a document is loaded
                Core.documentViewer.addEventListener('documentLoaded', () => {
                    console.log('document loaded');
                });

                // adding an event listener for when the page number has changed
                Core.documentViewer.addEventListener('pageNumberUpdated', (pageNumber) => {
                    console.log(`Page number is: ${pageNumber}`);
                });

                Core.createDocument(`../../../../webpage/pdfShow.ashx?seno=${seno}&guid=${guid}`, { extension: 'pdf' })
                    .then(doc => {
                        instance.UI.loadDocument(doc);
                    });

            });


    </script>

</body>
</html>
