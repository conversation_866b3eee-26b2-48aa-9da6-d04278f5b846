﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
    /// <summary>
    /// mySign_Report 的摘要描述
    /// </summary>
    public class mySign_Report : mySQLHelper
    {
        public mySign_Report()
        {
            //
            // TODO: 在這裡新增建構函式邏輯
            //
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 基本資料
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetPlanMainData(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"SELECT 
	                                eb_year+eb_orgcd+eb_class+eb_sn as eb_planno,
	                                eb_year,eb_compname,
                                    eb_planname, 
                                    eb_planer,
	                                eb_precontfdate,eb_conttype_b0,eb_conttype_b1,eb_conttype_d4,eb_conttype_d5,eb_conttype_d7,
	                                eb_join,eb_pc_flag,eb_securelevel,eb_secure_memo,
                                    case eb_securelevel when '01' then '限閱' when '02' then '機密' when '03' then '極機密' end as eb_securelevel_n,
                                    case isnull(eb_secure_memo, '') when '' then '' else '極機密理由說明：' + eb_secure_memo end as eb_secure_memo_n,
                                    case when eb_securelevel='01' or eb_securelevel='02' then '機密' else '極機密' end as eb_securelevel1_n
                                 FROM engage_base WHERE eb_seqsn=@seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 基本資料
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetExecOrgAndDept(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"SELECT eb_orgcd, eb_execdept, org_abbr_chnm1 AS org_name, eb_execdeptnm AS dept_name
	                             FROM engage_base 
	                             LEFT OUTER JOIN common.dbo.orgcod ON org_orgcd = substring(eb_execdept,1,2)
	                             WHERE eb_seqsn = @seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 基本資料
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignCont(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"IF @ver=''
                                    BEGIN
	                                    SELECT sign.*, eb_gov_assist
	                                    FROM engage_signcont sign
	                                    INNER JOIN engage_base ON eb_seqsn=esc_seqsn
	                                    WHERE esc_seqsn=@seqsn
	 
                                    END
                                 ELSE
                                    BEGIN
	                                    SELECT sign.*
	                                    FROM engage_his.dbo.engage_signcont sign
	                                    WHERE esc_seqsn=@seqsn AND esc_ver=@ver
                                    END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦草約
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem1Checked(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"DECLARE @planno nvarchar(15)
                                SELECT @planno=eb_year+eb_orgcd+eb_class+eb_sn+'A01' from engage_base where eb_seqsn=@seqsn
			
                                IF @ver=''
                                BEGIN
	                                SELECT		esc_comefrom,
				                                CASE esc_comefrom 
					                                when '1' then tc_planno + tc_ver + tc_seqsn
					                                when '2' then @planno
				                                END as esi1_contno,
				                                parent.*,
				                                code_conttype.esi1_conttype_name,
				                                isnull(esi1_total,0) AS totalmoney,
				                                code_valuedesc AS signstatusdesc,
				                                treaty.*,engage_signcont.esc_moneyrate
	                                FROM		engage_signitem1 parent
	                                INNER JOIN	engage_signcont ON esc_seqsn=esi1_seqsn
	                                LEFT OUTER JOIN
                                        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
                                         FROM treaty_code_table
                                         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	                                LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	                                LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	                                WHERE		esi1_seqsn=@seqsn and esi1_checked=1
	                                order by esi1_contno
                                END
                                ELSE
                                BEGIN
	                                SELECT		esc_comefrom,
				                                CASE esc_comefrom 
					                                when '1' then tc_planno + tc_ver + tc_seqsn
					                                when '2' then @planno
				                                END as esi1_contno,
				                                parent.*,
				                                code_conttype.esi1_conttype_name,
				                                isnull(esi1_total,0) AS totalmoney,
				                                code_valuedesc AS signstatusdesc,
				                                treaty.*,engage_his.dbo.engage_signcont.esc_moneyrate
	                                FROM		engage_his.dbo.engage_signitem1 parent  
	                                INNER JOIN	engage_his.dbo.engage_signcont ON esc_seqsn=esi1_seqsn
	                                LEFT OUTER JOIN
                                        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
                                         FROM treaty_code_table
                                         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	                                LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	                                LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	                                WHERE		esi1_seqsn=@seqsn and esi1_ver=@ver and esi1_checked=1
	                                order by esi1_contno
                                END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦草約
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem1Checked1(string seqsn, int ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"DECLARE @planno nvarchar(15)
                                SELECT @planno=eb_year+eb_orgcd+eb_class+eb_sn+'A01' from engage_base where eb_seqsn=@seqsn

                                DECLARE @max_ver int
                                select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

                                if @ver = 0 or @ver = @max_ver
                                BEGIN			
	                                SELECT		esc_comefrom,
                                                case when esi1_tran_timestamp<>'' then  esi1_tran_timestamp else tc_planno + tc_ver + tc_seqsn        
                                                         end as tran_timestamp1,
				                                CASE esc_comefrom 
					                                when '1' then tc_planno + tc_ver + tc_seqsn
					                                when '2' then @planno
				                                END as esi1_contno1,
				                                parent.*,
				                                (select sub1.tc_planno + sub1.tc_ver + sub1.tc_seqsn from v_engage_sign_choose_treaty_case sub1 where sub1.tc_seno=esi1_tc_seqno_orignal) as esi1_contno_orignal,
				                                code_conttype.esi1_conttype_name,
				                                isnull(esi1_total,0) AS totalmoney,
				                                code_valuedesc AS signstatusdesc,
				                                treaty.*
				                                ,CONVERT(varchar, convert(datetime,esi1_contsdate), 111) as esi1_contsdate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_contedate), 111) as esi1_contedate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_presigndate), 111) as esi1_presigndate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_actualsigndate), 111) as esi1_actualsigndate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_web_date), 111) as esi1_web_date_slash
	                                FROM		engage_signitem1 parent
	                                INNER JOIN	engage_signcont ON esc_seqsn=esi1_seqsn
	                                LEFT OUTER JOIN
                                        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
                                         FROM treaty_code_table
                                         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	                                LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	                                LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	                                WHERE		esi1_seqsn=@seqsn
	                                order by esi1_contno1

                                END
                                ELSE
                                BEGIN
	                                SELECT		esc_comefrom,
                                                case when esi1_tran_timestamp<>'' then  esi1_tran_timestamp else tc_planno + tc_ver + tc_seqsn        
                                                        end as tran_timestamp1,
				                                CASE esc_comefrom 
					                                when '1' then tc_planno + tc_ver + tc_seqsn
					                                when '2' then @planno
				                                END as esi1_contno1,
				                                parent.*,
				                                (select sub1.tc_planno + sub1.tc_ver + sub1.tc_seqsn from v_engage_sign_choose_treaty_case sub1 where sub1.tc_seno=esi1_tc_seqno_orignal) as esi1_contno_orignal,
				                                code_conttype.esi1_conttype_name,
				                                isnull(esi1_total,0) AS totalmoney,
				                                code_valuedesc AS signstatusdesc,
				                                treaty.*
				                                ,CONVERT(varchar, convert(datetime,esi1_contsdate), 111) as esi1_contsdate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_contedate), 111) as esi1_contedate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_presigndate), 111) as esi1_presigndate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_actualsigndate), 111) as esi1_actualsigndate_slash
				                                ,CONVERT(varchar, convert(datetime,esi1_web_date), 111) as esi1_web_date_slash
	                                FROM		engage_his.dbo.engage_signitem1 parent  
	                                INNER JOIN	engage_his.dbo.engage_signcont ON esc_seqsn=esi1_seqsn and esc_ver=esi1_ver
	                                LEFT OUTER JOIN
                                        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
                                         FROM treaty_code_table
                                         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	                                LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	                                LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	                                WHERE		esi1_seqsn=@seqsn and esi1_ver=@ver
	                                order by esi1_contno1
                                END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦草約
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetMoneyType(string subtype)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select * from treaty_code_table where code_type='20' and code_subtype=@subtype";

            oCmd.Parameters.AddWithValue("@subtype", subtype);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦草約
        /// </summary>
        /// <returns></returns>
        public DataTable dtSignDraft(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_sign_DraftRpt";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦草約
        /// </summary>
        /// <returns></returns>
        public DataTable dtFlowDsp(string seqsn, string empno)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_flow_dsp";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@empno", empno);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 簽辦相關附件
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetAttachDoc(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"create table #tmp (SN int IDENTITY(1,1) not null, doc_name nvarchar(512))

                                IF @ver=''
                                BEGIN
                                    insert into #tmp 
    
	                                SELECT		doc.doc_name
	                                FROM		dbo.v_engage_sign_doc doc LEFT OUTER JOIN
				                                dbo.engage_signitem2 ON 
					                                esi2_seqsn = doc.doc_seqsn AND 
					                                esi2_doctype = doc.doc_type AND 
					                                esi2_attdocid = doc.doc_attdocid
	                                WHERE		doc_seqsn = @seqsn and esi2_checked=1
                                END
                                BEGIN
	                                insert into #tmp 
	                                SELECT		doc.doc_name				
	                                FROM		dbo.v_engage_sign_doc doc LEFT OUTER JOIN
				                                engage_his.dbo.engage_signitem2 ON
					                                esi2_seqsn = doc.doc_seqsn AND 
					                                esi2_doctype = doc.doc_type AND 
					                                esi2_attdocid = doc.doc_attdocid AND
					                                esi2_ver = @ver
	                                WHERE		doc_seqsn = @seqsn and esi2_checked=1
                                END

                                IF @ver=''
                                BEGIN
                                    insert into #tmp
	                                SELECT		ea_doc
	                                FROM		engage_attfile2 
	                                JOIN		engage_signcont ON esc_seqsn = ea_seqsn AND esc_ver = ea_ver
	                                WHERE		ea_seqsn = @seqsn AND ea_filetype = 'NG'
                                END
                                ELSE
                                BEGIN
	                                insert into #tmp
	                                SELECT		ea_doc
	                                FROM		engage_attfile2 
	                                WHERE		ea_seqsn = @seqsn AND ea_ver = @ver AND ea_filetype = 'NG'
                                END 

                                select * from #tmp

                                drop table #tmp";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責(A & B & rptSignMain)
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem4(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"IF @ver=''
                                BEGIN
	                                SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	                                FROM		engage_signitem4 item4
	                                JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE		esi4_seqsn = @seqsn and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
	
                                END
                                ELSE
                                BEGIN
	                                SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	                                FROM		engage_his.dbo.engage_signitem4 item4
	                                JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE		esi4_seqsn = @seqsn AND esi4_ver = @ver and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
	
                                END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責(D)
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem4_1(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            /*oCmd.CommandText = @"IF @ver=''
                                BEGIN
	                                SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
				                                ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
                                                ,convert(varchar, esi4_signdate, 120) AS esi4_signdate1
	                                FROM		engage_signitem4 item4
	                                JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE		esi4_seqsn = @seqsn and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
                                END
                                ELSE
                                BEGIN
	                                SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
				                                ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
                                                ,convert(varchar, esi4_signdate, 120) AS esi4_signdate1
	                                FROM		engage_his.dbo.engage_signitem4 item4
	                                JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE		esi4_seqsn = @seqsn AND esi4_ver = @ver and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
                                END";*/

            oCmd.CommandText = @"SELECT  item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname,
                                         case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype,
                                         convert(varchar, esi4_signdate, 120) AS esi4_signdate1
                                 FROM  engage_signitem4 item4
                                 JOIN  common.dbo.depcod ON esi4_signdept = dep_deptid
                                 JOIN  common.dbo.orgcod ON dep_orgcd = org_orgcd
                                 WHERE  esi4_seqsn = @seqsn and isnull(esi4_eztype,'')<>'eg08' order by esi4_id";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責(C & E)
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem4_2(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"IF @ver=''
                                BEGIN
	                                SELECT	
		                                --item4.*,
		                                dep_deptname AS deptname, org_abbr_chnm1 AS orgname,esi4_signname
		                                ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else convert(varchar, item4.esi4_signdate, 120) end as show_signdate
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else item4.esi4_comment end as show_comment
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else item4.esi4_result end as show_result
	                                FROM	engage_signitem4 item4
	                                JOIN	common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN	common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE	esi4_seqsn = @seqsn and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
	
                                END
                                ELSE
                                BEGIN
	                                SELECT		
		                                --item4.*,
		                                dep_deptname AS deptname, org_abbr_chnm1 AS orgname,esi4_signname
		                                ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
                                        ,convert(varchar, esi4_signdate, 120) AS show_signdate1
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else convert(varchar, item4.esi4_signdate, 120) end as show_signdate
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else item4.esi4_comment end as show_comment
		                                ,case rtrim(item4.esi4_comment) when '轉紙本簽核' then NULL else item4.esi4_result end as show_result
	                                FROM	engage_his.dbo.engage_signitem4 item4
	                                JOIN	common.dbo.depcod ON esi4_signdept = dep_deptid
	                                JOIN	common.dbo.orgcod ON dep_orgcd = org_orgcd
	                                WHERE	esi4_seqsn = @seqsn AND esi4_ver = @ver and isnull(esi4_eztype,'')<>'eg08' order by esi4_id
                                END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtSignApplyApprove(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"declare @approvelist nvarchar(max)
                                 exec dbo.pr_engage_sign_apply_approve @seqsn, @approvelist output
                                 select @approvelist as approvelist";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtCheckCompanyAlert(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"declare @msglist nvarchar(max)
                                 exec [pr_engage_check_company_alert] @seqsn, @msglist output
                                 select @msglist as msglist";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignOrgPrompt(string orgcd)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_sign_prompt";

            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtEG08(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_eg08_rpt";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtEcpMain(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select ISNULL(ecp_memo,'') as ecp_memo 
                              from engage_signcont 
                              join engage_ecp_main on ecp_guid=esc_ecp_eg06_guid 
                              where esc_seqsn=@seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 本次簽辦草約之核定權責
        /// </summary>
        /// <returns></returns>
        public DataTable dtDuty(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_duty_report";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 契約分發對象或部門
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem5(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"IF @ver='' or exists (select esc_ver from engage_signcont where esc_ver = @ver)
                                    BEGIN
	                                    SELECT		item5.*, dep_deptname AS deptname
	                                    FROM		engage_signitem5 item5
	                                    JOIN		common.dbo.depcod ON esi5_sendtodept = dep_deptid
	                                    WHERE		esi5_seqsn = @seqsn
                                    END
                                 ELSE
                                    BEGIN
	                                    SELECT		item5.*, dep_deptname AS deptname
	                                    FROM		engage_his.dbo.engage_signitem5 item5
	                                    JOIN		common.dbo.depcod ON esi5_sendtodept = dep_deptid
	                                    WHERE		esi5_seqsn = @seqsn AND esi5_ver = @ver
                                    END";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 契約修約原因
        /// </summary>
        /// <returns></returns>
        public DataTable dtContModReason(string esi1_id)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"declare @esi1_tc_seqno_orignal bigint, @esi1_cm_id bigint
                                declare @contno nvarchar(20)
                                declare @msg1 nvarchar(max) = ''
                                declare @msg2 nvarchar(max) = ''

                                select @esi1_tc_seqno_orignal=esi1_tc_seqno_orignal,@esi1_cm_id=esi1_cm_id from engage_signitem1 where esi1_id=@esi1_id
                                select @contno=tc_planno+tc_ver+tc_seqsn from v_engage_sign_choose_treaty_case where tc_seno=@esi1_tc_seqno_orignal

                                SELECT @msg1=@msg1+NCHAR(9632)+rtrim(code_valuedesc)+'  '
                                    FROM contractDB..c_codetbl
                                    join contractDB..c_contmod_detail ON cmd_modtype='1' and cmd_modreasonitem = code_value and cm_id = @esi1_cm_id
	                                WHERE code_type = '113'
	                                ORDER BY code_order

                                SELECT @msg2=@msg2+NCHAR(9632)+rtrim(code_valuedesc)+case when code_value='99' then '，請說明( '+RTRIM(cmd_modremark)+' )' else '' end+' ' 
                                    FROM contractDB..c_codetbl
                                    join contractDB..c_contmod_detail ON cmd_modtype='2' and cmd_modreasonitem = code_value and cm_id = @esi1_cm_id
	                                WHERE code_type = '114'
	                                ORDER BY code_order

                                select @contno contno_orignal,@msg1 msg1, @msg2 msg2";

            oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 契約修約原因
        /// </summary>
        /// <returns></returns>
        public DataTable dtDetailReason(string esi1_id)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
                select NCHAR(9632)+rtrim(code_valuedesc)+case when code_value='99' then '，請說明( '+RTRIM(cmd_modremark)+' )' else '' end as Msg
                from engage_signitem1
                join contractDB..c_contmod_detail on cm_id = esi1_cm_id and cmd_modtype='2'
                join contractDB..c_codetbl on code_type = '114' and code_value=cmd_modreasonitem
                where esi1_id = @esi1_id";

            oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 報院–無賠償上限
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem1ById(string esi1_id)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select 
	                            esi1_id, esi1_seqsn, esi1_tc_seno,
	                            esi1_treaty_flag, esi1_treaty_result, esi1_risk, esi1_exec_reason
	                            /*,tc_duty, tc_special_other_desc
	                            ,case 
		                            when (tc_duty = '2' 
				                            or CHARINDEX(N'賠償無上限', tc_special_other_desc) > 0 
				                            or CHARINDEX(N'無賠償上限', tc_special_other_desc) > 0 
				                            or CHARINDEX(N'無上限賠償', tc_special_other_desc) > 0 
			                              ) then '1'
		                            else '0'
	                            end*/
	                            --負擔無賠償上限之契約責任者
	                            ,case when exists(select tc_seno FROM treaty_case_sRC where tcsRC_val='4' and tc_seno = esi1_tc_seno) then '1' else '0' end as display_flag

                            from engage_signitem1
                            left join v_engage_sign_choose_treaty_case on tc_seno = esi1_tc_seno
                            where esi1_id = @esi1_id";

            oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 報院–無賠償上限
        /// </summary>
        /// <returns></returns>
        public DataTable dtGetSignItem1_Proc(string esi1_id)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select * from engage_signitem1_proc where proc_esi1_id=@esi1_id order by proc_date asc";

            oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtContno(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select distinct esi1_contno from v_engage_continfo_for_docinfo where eb_seqsn = @seqsn and esi1_contno like '%N%'";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - BarCode用
        /// </summary>
        /// <returns></returns>
        public DataTable GetBarCode(string seqsn,string contno)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT eb_seqsn, sign_ver, 草約編號, 版本 
FROM  v_engage_DOC_timestamp WHERE eb_seqsn = @seqsn and 草約編號=@contno 
";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@contno", contno);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtContinfo(string seqsn, string contno, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_continfo_rpt";
            
            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@contno", contno);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtSigncont(string seqsn, string ver)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_signcont_rpt";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@ver", ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtEzComment(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            //oCmd.CommandText = @"/*
            //                    select rtrim(簽核人員) as signname, convert(nvarchar(4000), 簽核意見) as comment, 簽核結果 as status,簽核時間 as signtime from v_engage_ez_comment inner join engage_signcont on resdd002 = esc_formno  where resdd001 = 'eg06' and esc_seqsn = @seqsn order by signtime
            //                    */
            //                    select rtrim(esi4_signname) as signname,esi4_comment as comment, esi4_result as [status], convert(varchar, esi4_signdate, 120) as signtime
            //                      ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
            //                    from engage_signitem4 
            //                    where esi4_seqsn = @seqsn order by signtime";

            oCmd.CommandText = @"declare @signname nvarchar(10)
                                 declare @signdate nvarchar(20)
                                 declare @signopinion nvarchar(max)
                                 select @signname=esc_signname_final, @signdate=convert(nvarchar, cast(esc_signdate+' 23:59:59' as datetime), 120), @signopinion=esc_signopinion from engage_signcont where esc_seqsn=@seqsn

                                 select row_number() over (order by esi4_signname) row, rtrim(esi4_signname) as signname,esi4_comment as comment, esi4_result as [status], convert(varchar, esi4_signdate, 120) as signtime
		                                ,case esi4_eztype when 'eg07' then '修正簽辦' when 'eg08' then '報院簽辦' else '一般簽辦' end show_eztype
                                 into #temp
                                 from engage_signitem4 
                                 where esi4_seqsn = @seqsn order by signtime

                                 if exists(select * FROM engage_signcont WHERE esc_seqsn=@seqsn and esc_signresult='01'and esc_result_eg08='同意')
                                   insert into #temp (signname,comment,[status],signtime,show_eztype, row) values (@signname,@signopinion,'同意',@signdate,'報院簽辦',99)

                                 select * from #temp order by signtime

                                 drop table #temp                                 
                               ";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtSecurity(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select isnull(eb_securelevel,'') eb_securelevel,eb_secure_memo from engage_base where eb_seqsn=@seqsn ";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - Time stamp
        /// </summary>
        /// <returns></returns>
        public DataTable dtTimestamp(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"declare @timestamp varchar(max)=''
                                select @timestamp=(
	                                select esi1_tran_timestamp+','  
	                                from engage_signitem1 
	                                where esi1_seqsn=@seqsn and esi1_checked='1' and isnull(esi1_tran_timestamp,'')<>'' 
	                                order by esi1_tran_timestamp
	                                for xml path('')
                                )
                                --select @timestamp=substring(@timestamp,1,len(@timestamp)-1)
                                select @timestamp=REPLACE(@timestamp,',', '  ')
                                select @timestamp as timestamp";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        /// <summary>
        ///   洽案系統 - 契約簽辦單 - 用借印申請單
        /// </summary>
        /// <returns></returns>
        public DataTable dtEcpMain1(string seqsn)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select ISNULL(ecp_memo,'') as ecp_memo1 
                              from engage_signcont 
                              join engage_ecp_main on ecp_guid=esc_ecp_eg07_guid 
                              where esc_seqsn=@seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }
    }
}