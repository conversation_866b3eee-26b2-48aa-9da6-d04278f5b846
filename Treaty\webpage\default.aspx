﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="default.aspx.cs" Inherits="webpage_default" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot.ascx" TagName="Foot" TagPrefix="uc2" %>
<%@ Register Src="../../Comp/News.ascx" TagName="News" TagPrefix="uc3" %>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function doNewCase(seno, contno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_NewVer.aspx?contno=" + contno
                , iframe: true, width: "440px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '議約需求'
                , onClosed: function () {
                    if ($.colorbox.data == "1")
                        location.replace('./TreatyApply.aspx?seno=' + seno + '&newver=1');
                    if ($.colorbox.data == "2")
                        location.replace('./TreatyApply.aspx?seno=' + seno + '&newver=2');
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }

        .Mask {
            display: none;
            position: fixed;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 2;
            top: 0;
            left: 0;
            opacity: 0.5;
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                                &nbsp;
                                <uc3:News ID="News1" runat="server" SysCode="F2-29" />
                            </div>
                            <div class="right font-light">
                                <table border="0" cellspacing="5" cellpadding="0">
                                    <tr>
                                        <td width="80" align="right">
                                            <div class="font-title titlebackicon">關鍵字</div>
                                        </td>
                                        <td width="300">
                                           <font color='blue'><b>案源：</b></font><asp:Dropdownlist ID="cbxCaseClass" runat="server" Width="100px" RepeatDirection="Horizontal" >
                                                    <asp:ListItem Value="N">洽案系統</asp:ListItem>
                                                    <asp:ListItem Value="R">標案系統</asp:ListItem>
                                                    <asp:ListItem Value="M">NDA</asp:ListItem>
                                                    <asp:ListItem Value="C">工服</asp:ListItem>
                                                    <asp:ListItem Value="A">國外無收入</asp:ListItem>
                                                    <asp:ListItem Value="F">國內無收入</asp:ListItem>
<%--                                                <asp:ListItem Value="U">國外契約</asp:ListItem>
                                                    <asp:ListItem Value="S">新創事業</asp:ListItem>--%>
                                                    <asp:ListItem Value="T" Selected>其它(議約)</asp:ListItem>
                                                </asp:Dropdownlist>
                                            <asp:TextBox ID="txtKeyWord" runat="server" Width="150px" title="ex：單位、議約編號、議約名稱、推廣人、客戶名稱" class="inputex width100 inputhint" />
                                        </td>
                                        <td>
                                            <asp:Button ID="btnQuery" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery_Click"></asp:Button>
                                            <span class="font-normal font-size3 font-bold"><img src="../images/icon-1301.gif" alt="icon" /><a href="#" id="advancesearchopen">進階查詢</a></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="twocol margin5TB">
                            <div class="left">
                                <span class="font-title font-bold font-size3">案件列表</span>&nbsp;&nbsp;
                               條件：<select><option>個人</option>
                                       <option>業務</option>
                                       <option>主管</option>
                                   </select>&nbsp;
                                      <asp:DropDownList ID="DDL_CaseStatus" runat="server" OnSelectedIndexChanged="DDL_CaseStatus_SelectedIndexChanged" AutoPostBack="True">
                                          <asp:ListItem Value="0">全部(不含草稿&簽核中)</asp:ListItem>
                                          <asp:ListItem Value="1">法務承辦</asp:ListItem>
                                          <asp:ListItem Value="2">結束</asp:ListItem>
                                          <asp:ListItem Value="3">需求取消</asp:ListItem>
                                          <asp:ListItem Value="4">草稿&簽核中</asp:ListItem>
                                      </asp:DropDownList>
                            </div>
                            <div class="right font-light"></div>
                        </div>
                        <div id="advancesearch" class="gentablenoline font-normal margin5TB">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align="right">單位別：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="orgcd_name" DataValueField="orgcd" AppendDataBoundItems="True" DataSourceID="SDS_Orgcd">
                                            <asp:ListItem Value="00">   --請選擇--  </asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:SqlDataSource ID="SDS_Orgcd" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                                    </td>
                                    <td align="right">契約性質：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True" DataSourceID="SDS_ContType">
                                            <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                        </asp:DropDownList>
                                        <asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                                    </td>
                                </tr>
                                <tr>
                                    <td align="right">案件編號/名稱：</td>
                                    <td>
                                        <asp:TextBox ID="TB_contnoName" runat="server" Width="150px"></asp:TextBox></td>
                                    <td align="right">客戶名稱：</td>
                                    <td>
                                        <asp:TextBox ID="tbxCompName" runat="server" Width="150px"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td align="right">單位承辦人員：</td>
                                    <td>
                                        <asp:TextBox ID="tbxPromoterName" runat="server" Width="150px"></asp:TextBox></td>
                                    <td align="right">單位執行部門：</td>
                                    <td>
                                        <asp:TextBox ID="tbxReqDept" runat="server" Width="150px"></asp:TextBox></td>
                                </tr>
                                <tr>
                                    <td align="right">狀態：</td>
                                    <td>
                                        <asp:DropDownList ID="DDL_CaseStatus1" runat="server">
                                            <asp:ListItem Value="0">全部(不含草稿)</asp:ListItem>
                                            <asp:ListItem Value="1">法務承辦</asp:ListItem>
                                            <asp:ListItem Value="2">結束</asp:ListItem>
                                            <asp:ListItem Value="3">需求取消</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td align="right">承辦法務人員：</td>
                                    <td>
                                        <asp:TextBox ID="tbxHandleName" runat="server" Width="150px"></asp:TextBox></td>
                                </tr>

                                <tr>
                                    <td align="right">案件顯示：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlShowCase" runat="server" Width="96px">
                                            <asp:ListItem Value="0" Selected="True">全部件次</asp:ListItem>
                                            <asp:ListItem Value="1">最新件次</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td align="right">是否為修約：</td>
                                    <td>
                                        <asp:DropDownList ID="ddl_amend" runat="server">
                                            <asp:ListItem Value="1">是</asp:ListItem>
                                            <asp:ListItem Value="2">否</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                            </table>
                            <div class="twocol margin5TB">
                                <div class="right">
                                    <button id="advancesearchclear" class="genbtn">清除</button>
                                    <button id="advancesearchclose" class="genbtn">取消</button>
                                    <asp:Button ID="btnQuery1" runat="server" Text="查詢" class="genbtn" OnClick="btnQuery1_Click" />
                                </div>
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <table width="100%" border="0" cellspacing="5" cellpadding="0" class="font-size3 font-bold">
                                <tr>
                                    <td>
                                        <div class="bgstepblue">議約需求</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblueS">分案</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">法務承辦</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">案件審核</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblueS">結束</div>
                                    </td>
                                    <td>
                                        <img src="../images/icon-2601.gif" /></td>
                                    <td>
                                        <div class="bgstepblue">契約簽辦</div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <!-- fixwidth -->
                    <div class="fixwidth">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td colspan="6">
                                    <div class="twocol margin5TB">
                                        <span class="stripeMe">
                                            <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated" DataSourceID="SDS_search">
                                                <FooterStyle BackColor="White" />
                                                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                <Columns>
                                                    <asp:TemplateField HeaderText="新件次">
                                                        <ItemTemplate>
                                                            <asp:Literal ID="LB_tmp_status_flag" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_status_flag").ToString())) %>' Visible="false"></asp:Literal>
                                                            <asp:Literal ID="LT_newVer" runat="server"></asp:Literal>
                                                            <asp:ImageButton ID="IB_newVer" runat="server" CommandName="New" CommandArgument='<%# Eval("tc_seno")+";"+Eval("tmp_case_actno") %>' ImageUrl="../images/text.gif" Visible="false" Class="ajax_mesg" />
                                                        </ItemTemplate>
                                                        <ItemStyle HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="單位" SortExpression="org_name">
                                                        <ItemTemplate>
                                                            <asp:Label ID="LB_org_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("org_name").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <ItemStyle Width="20px" HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="洽案／契約名稱" SortExpression="tmp_case_name">
                                                        <ItemTemplate>
                                                            <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tc_seno")+";"+Eval("tmp_case_actno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_actno").ToString())) %>'></asp:LinkButton><br />
                                                            <asp:Label ID="LB_case_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_name").ToString())) %>'></asp:Label>&nbsp;
                                                        </ItemTemplate>
                                                        <ItemStyle Width="210px" />
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="tc_compname" SortExpression="tc_compname" HeaderText="客戶名稱">
                                                        <ItemStyle Width="210px" />
                                                    </asp:BoundField>
                                                    <asp:TemplateField HeaderText="分案日期&lt;hr class=dghr&gt;結件日期">
                                                        <ItemTemplate>
                                                            <asp:Label ID="LB_assign_day" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_assign_date").ToString())) %>'></asp:Label><hr />
                                                            <asp:Label ID="LB_closedate" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_case_closedate").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <ItemStyle HorizontalAlign="Center" Width="75px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="單位&lt;br&gt;承辦人" SortExpression="tc_promoter_name">
                                                        <ItemTemplate>
                                                            <asp:Label ID="LB_promoter_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_promoter_name").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <HeaderStyle HorizontalAlign="Center" Width="50px" />
                                                        <ItemStyle HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="tc_handle_name" SortExpression="tc_handle_name" HeaderText="法務">
                                                        <ItemStyle HorizontalAlign="Center" Width="50px" />
                                                    </asp:BoundField>
                                                    <asp:TemplateField HeaderText="洽案&lt;br&gt;狀態">
                                                        <ItemTemplate>
                                                            <asp:Label ID="Label1" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_other_status_name").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <ItemStyle Width="90px" HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                    <asp:BoundField DataField="tmp_conttype_name" HeaderText="契約性質">
                                                        <ItemStyle Width="60px" />
                                                    </asp:BoundField>

                                                    <asp:TemplateField HeaderText="議約&lt;br&gt;狀態">
                                                        <ItemTemplate>
                                                            <asp:Label ID="LB_statusName" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_status_name").ToString())) %>'></asp:Label>
                                                        </ItemTemplate>
                                                        <ItemStyle Width="40px" HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="機密等級">
                                                        <ItemTemplate>
                                                            <asp:Image ID="Image1" runat="server" Height="31px" ImageUrl="../images/CONFIDENTIAL.png" Width="80px" />
                                                        </ItemTemplate>
                                                        <ItemStyle Width="80px" HorizontalAlign="Center" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <EmptyDataTemplate>
                                                    <!--當找不到資料時則顯示「無資料」-->
                                                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                </EmptyDataTemplate>
                                                <FooterStyle BackColor="White" />
                                                <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                            </cc1:SmartGridView>
                                            <asp:SqlDataSource ID="SDS_search" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                                        </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- fixwidth -->
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <uc2:Foot runat="server" ID="Foot" />
        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <script type="text/javascript">
            $(document).ready(function () {
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                $(".inputhint").tooltip({
                    position: { my: "left+10 bottom+40", at: "left bottom " },
                    tooltipClass: "custom-tooltip-styling",
                    //讓tooltips內可以放置HTML CODE
                    content: function () {
                        return $(this).prop('title');
                    }
                });
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: { duration: 300 },
                    hide: { duration: 300 }
                });

            });
            $("#advancesearch").dialog({
                open: function (type, data) { $(this).parent().appendTo("form").css({ "z-index": "101" }); },
                //position: ["center", 100],
                width: 800,
                height: 310,
                autoOpen: false,
                show: { duration: 300 },
                hide: { duration: 300 }
            });
            $("#advancesearchopen").click(function () {
                if ($("#txtKeyWord").val() != "") {
                    $("#TB_contnoName").val($("#txtKeyWord").val());
                    $("#tbxCompName").val($("#txtKeyWord").val());
                    $("#tbxPromoterName").val($("#txtKeyWord").val());
                    $("#tbxReqDept").val($("#txtKeyWord").val());
                    $("#tbxHandleName").val($("#txtKeyWord").val());
                }
                $("#advancesearch").dialog("open");
            });
            $("#advancesearchclose").click(function () {
                $("#advancesearch").dialog("close");
            });
            $("#advancesearchclear").click(function () {
                $("#ddlOrgcd").val("00");
                $("#ddlContType").val("");
                $("#TB_contnoName").val("");
                $("#tbxCompName").val("");
                $("#tbxPromoterName").val("");
                $("#tbxReqDept").val("");
                $('input[type=checkbox]').attr('checked', false);
                $("#DDL_CaseStatus1").val("0");
                $("#tbxHandleName").val("");
                $("#ddlShowCase").val("0");
                $("#ddl_amend").val("0");
                return false;
            });
        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
