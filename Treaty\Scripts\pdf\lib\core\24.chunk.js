/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[24],{402:function(ia,y,e){e.r(y);var fa=e(1),x=e(14),ha=e(2);ia=e(48);var ea=e(32),da=e(9);e=function(){function e(){this.init()}e.prototype.init=function(){this.c0=!1;this.ue=this.$j=this.connection=null;this.Sp={};this.Ba=this.PB=null};e.prototype.bea=function(e){for(var w=this,r=0;r<e.length;++r){var h=e[r];switch(h.at){case "create":this.Sp[h.author]||(this.Sp[h.author]=h.aName);this.c7(h);break;case "modify":this.Ba.Tq(h.xfdf).then(function(e){w.Ba.$R(e[0])});
break;case "delete":this.Ba.Tq("<delete><id>"+h.aId+"</id></delete>")}}};e.prototype.c7=function(e){var w=this;this.Ba.Tq(e.xfdf).then(function(r){r=r[0];r.authorId=e.author;w.Ba.$R(r);w.Ba.trigger(x.a.UPDATE_ANNOTATION_PERMISSION,[r])})};e.prototype.D6=function(e,x,r){this.$j&&this.$j(e,x,r)};e.prototype.preloadAnnotations=function(e){this.addEventListener("webViewerServerAnnotationsEnabled",this.D6.bind(this,e,"add",{imported:!1}),{once:!0})};e.prototype.initiateCollaboration=function(w,y,r){var h=
this;if(w){h.ue=y;h.Ba=r.La();r.addEventListener(x.d.DOCUMENT_UNLOADED,function(){h.disableCollaboration()});h.yea(w);var f=new XMLHttpRequest;f.addEventListener("load",function(){if(200===f.status&&0<f.responseText.length)try{var n=JSON.parse(f.responseText);h.connection=exports.Sa.hfa(Object(ea.h)(h.ue,"blackbox/"),"annot");h.PB=n.id;h.Sp[n.id]=n.user_name;h.Ba.gT(n.id);h.connection.sla(function(e){e.t&&e.t.startsWith("a_")&&e.data&&h.bea(e.data)},function(){h.connection.send({t:"a_retrieve",dId:w});
h.trigger(e.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,[h.Sp[n.id],h.PB])},function(){h.disableCollaboration()})}catch(ca){Object(ha.g)(ca.message)}});f.open("GET",Object(ea.h)(this.ue,"demo/SessionInfo.jsp"));f.withCredentials=!0;f.send();h.c0=!0;h.Ba.Tba(function(e){return h.Sp[e.Author]||e.Author})}else Object(ha.g)("Document ID required for collaboration")};e.prototype.disableCollaboration=function(){this.$j&&(this.Ba.removeEventListener(da.a.Events.ANNOTATION_CHANGED,this.$j),this.$j=null);
this.connection&&this.connection.Rn();this.Ba&&this.Ba.gT("Guest");this.init();this.trigger(e.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED)};e.prototype.yea=function(e){var w=this;this.$j&&this.Ba.removeEventListener(da.a.Events.ANNOTATION_CHANGED,this.$j);this.$j=function(r,h,f){return Object(fa.b)(this,void 0,void 0,function(){var n,x,y,z,ba,da,ea,ha,ia;return Object(fa.d)(this,function(aa){switch(aa.label){case 0:if(f.imported)return[2];n={t:"a_"+h,dId:e,annots:[]};return[4,w.Ba.q3()];case 1:x=
aa.ea();"delete"!==h&&(y=(new DOMParser).parseFromString(x,"text/xml"),z=new XMLSerializer);for(ba=0;ba<r.length;ba++)da=r[ba],ha=ea=void 0,"add"===h?(ea=y.querySelector('[name="'+da.Id+'"]'),ha=z.serializeToString(ea),ia=null,da.InReplyTo&&(ia=w.Ba.LE(da.InReplyTo).authorId||"default"),n.annots.push({at:"create",aId:da.Id,author:w.PB,aName:w.Sp[w.PB],parent:ia,xfdf:"<add>"+ha+"</add>"})):"modify"===h?(ea=y.querySelector('[name="'+da.Id+'"]'),ha=z.serializeToString(ea),n.annots.push({at:"modify",
aId:da.Id,xfdf:"<modify>"+ha+"</modify>"})):"delete"===h&&n.annots.push({at:"delete",aId:da.Id});0<n.annots.length&&w.connection.send(n);return[2]}})})}.bind(w);this.Ba.addEventListener(da.a.Events.ANNOTATION_CHANGED,this.$j)};e.Events={WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:"webViewerServerAnnotationsEnabled",WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:"webViewerServerAnnotationsDisabled"};return e}();Object(ia.a)(e);y["default"]=e}}]);}).call(this || window)
