﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase_assign : Treaty.common  
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private Control GetPostBackControl(Page page)
    {
        Control control = null;
        string postBackControlName = Request.Params.Get("__EVENTTARGET");
        string eventArgument = Request.Params.Get("__EVENTARGUMENT");
        if (postBackControlName != null && postBackControlName.Length > 0)
        {
            control = Page.FindControl(postBackControlName);
        }
        else
        {
            foreach (string str in Request.Form)
            {
                Control c = Page.FindControl(str);
                if (c is Button)
                {
                    control = c;
                    break;
                }
            }
        }
        return control;
    }

    protected void Page_Load(object sender, EventArgs e)
    {

         //string controlName = Request.Params.Get("__EVENTTARGET");
        //Control control = GetPostBackControl(this);

        if (!IsPostBack)
        {
            if (Request["seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
            }
            if (ViewState["seno"] == null)
                Response.Redirect("../danger.aspx");
           // ViewState["seno"] = "17";
            ViewState["sortorder"] = "Desc";
            ViewState["sortField"] = "unX";
        }

    ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_log));
    databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }
    protected void databinding(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //this.SDS_SC.SelectParameters.Clear();
        //this.SDS_SC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_SC.SelectCommand = "esp_TreatyCase_distribution_rule";
        //this.SDS_SC.SelectParameters.Add("empno",   ssoUser.empNo);
        //this.SDS_SC.SelectParameters.Add("seno",    ViewState["seno"].ToString());
        //this.SDS_SC.SelectParameters.Add("org_flag", RBL_1.SelectedValue);
        //this.SDS_SC.SelectParameters.Add("SortExpression",  str_sortField);
        //this.SDS_SC.SelectParameters.Add("sortOrder", str_sort);
        //for (int i = 0; i < this.SDS_SC.SelectParameters.Count; i++)
        //{
        //    SDS_SC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_SC.DataBind();
        //SGV_log.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_distribution_rule";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("org_flag", oRCM.SQLInjectionReplaceAll(RBL_1.SelectedValue));
            sqlCmd.Parameters.AddWithValue("SortExpression", oRCM.SQLInjectionReplaceAll(str_sortField));
            sqlCmd.Parameters.AddWithValue("sortOrder", oRCM.SQLInjectionReplaceAll(str_sort));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_log.DataSource = dt;
                SGV_log.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_log_DataBound(object sender, EventArgs e)
    {
    }
    protected void SGV_log_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "CaseAssign")
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            //this.SDS_SC.InsertParameters.Clear();
            //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.StoredProcedure;
            //this.SDS_SC.InsertCommand = "esp_TreatyCase_Assign";
            //this.SDS_SC.InsertParameters.Add("seno", TypeCode.String, ViewState["seno"].ToString());
            //this.SDS_SC.InsertParameters.Add("tc_case_assign_empno", TypeCode.String, ssoUser.empNo);
            //this.SDS_SC.InsertParameters.Add("tc_case_assign_name", TypeCode.String, ssoUser.empName);
            //this.SDS_SC.InsertParameters.Add("tc_case_handle_empno", TypeCode.String, e.CommandArgument.ToString() );
            //this.SDS_SC.Insert();

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_Assign";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("tc_case_assign_empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("tc_case_assign_name", oRCM.SQLInjectionReplaceAll(ssoUser.empName));
                sqlCmd.Parameters.AddWithValue("tc_case_handle_empno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

        }
    }
    protected void SGV_log_PageIndexChanged(object sender, EventArgs e)
    {
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_log_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.SGV_log.PageIndex = e.NewPageIndex;
        SGV_log.DataBind();
    }
    protected void SGV_log_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType != DataControlRowType.Header)
        {
            LinkButton LB = (LinkButton)e.Row.FindControl("LB_emp");
            if (LB != null)
            {
                LB.Attributes.Add("onclick", "return  confirm('確定要指定 ?');");
                string law_emp =Server.HtmlEncode(oRCM.RemoveXss(Convert.ToString(DataBinder.Eval(e.Row.DataItem, "emp_no"))));
                string str_unX = Server.HtmlEncode(oRCM.RemoveXss(Convert.ToString(DataBinder.Eval(e.Row.DataItem, "unX"))));
                Literal lt_unX = (Literal)e.Row.FindControl("LT_unX");
                lt_unX.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_handle_Info.aspx?class=AMNRT&emp=" + law_emp + "' onClick='return false;'>" + str_unX + "</a>";

                string str_unQ = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "unQ"));
                Literal lt_unQ = (Literal)e.Row.FindControl("LT_unQ");
                lt_unQ.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_handle_Info.aspx?class=Q&emp=" + law_emp + "' onClick='return false;'>" + str_unQ + "</a>";

                string str_unL = Convert.ToString(DataBinder.Eval(e.Row.DataItem, "unL"));
                Literal lt_unL = (Literal)e.Row.FindControl("LT_unL");
                lt_unL.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_handle_Info.aspx?class=L&emp=" + law_emp + "' onClick='return false;'>" + str_unL + "</a>";
            }

            foreach (TableCell tc in e.Row.Cells)
            {
                tc.Attributes["style"] = "border-color:white";
            }
            if (e.Row.RowType == System.Web.UI.WebControls.DataControlRowType.DataRow)
            {
                e.Row.Attributes.Add("onmouseover", "this.originalstyle=this.style.backgroundColor;this.style.backgroundColor='#EEFFAA'");
                e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor=this.originalstyle;");
            }
        }
    }
    protected void SGV_log_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {
                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_log.SortExpression)
                    {
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_log.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                }
            }
        }
    }
    protected void SGV_log_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
 
       if (ViewState["sortorder"].ToString()=="asc"  )
          ViewState["sortorder"] = "desc";
       else
          ViewState["sortorder"] = "asc";
       this.SGV_log.PageIndex = 0;
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        //SGV_log.DataBind();
    }
    protected void SGV_log_Sorted(object sender, EventArgs e)
    {

    }
    protected void SDS_SC_Selecting(object sender, SqlDataSourceSelectingEventArgs e)
    {
        for (int i = 0; i < e.Command.Parameters.Count - 1; i++)
        {
            if (e.Command.Parameters[i].Value == null)
            {
                e.Command.Parameters[i].Value = "";
            }
        }
    }
    protected void RBL_1_SelectedIndexChanged(object sender, EventArgs e)
    {
        this.SGV_log.PageIndex = 0;
        ViewState["sortorder"] = "Desc";
        ViewState["sortField"] = "unX";
        databinding(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
}