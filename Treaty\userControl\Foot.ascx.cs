﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;
public partial class Treaty_userControl_Foot : System.Web.UI.UserControl
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack) {
            BindContact();
        }
    }

    private void BindContact()
    {
        //SDS_Contact.SelectParameters.Clear();
        //SDS_Contact.SelectCommandType = SqlDataSourceCommandType.Text ;
        //SDS_Contact.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'26' ";
        //SDS_Contact.DataBind();
        //System.Data.DataView dv = (DataView)SDS_Contact.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(""));
            sqlCmd.Parameters.AddWithValue("@code_type", oRCM.SQLInjectionReplaceAll("26"));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            LT_contact.Text = dv[0][3].ToString();
        }       
 
    }

}