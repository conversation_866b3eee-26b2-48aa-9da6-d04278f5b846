﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase_valuation_FileUp : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string seno
    {
        get
        {
            string str_seno = "";
            if (Request.QueryString["seno"] != null)
            {
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                if (Regex.IsMatch(ViewState["seno"].ToString(), "^[0-9]*$") == false)
                    Response.Redirect("../danger.aspx");
                str_seno = ViewState["seno"].ToString();
                return str_seno;
            }
            else
                return str_seno;
        }
    }
    public string fid
    {
        get
        {
            string str_seno = "";
            if (Request.QueryString["fid"] != null)
            {
                ViewState["fid"] = Request.QueryString["fid"].ToString();
                if (Regex.IsMatch(ViewState["fid"].ToString(), "^[0-9]*$") == false)
                    Response.Redirect("../danger.aspx");
                str_seno = ViewState["fid"].ToString();
                return str_seno;
            }
            else
                return str_seno;
        }
    }



    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull, string mode)
    {
        if (mode == "Select")
        {
            foreach (Parameter parameter in dataSource.SelectParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Insert")
        {
            foreach (Parameter parameter in dataSource.InsertParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Updat")
        {
            foreach (Parameter parameter in dataSource.UpdateParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Delete")
        {
            foreach (Parameter parameter in dataSource.DeleteParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
    }


    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            InitPage();
        }
    }
    private void InitPage()
    {

        SqlCommand oCmd_1 = new SqlCommand();
        oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd_1.CommandType = CommandType.Text;
        oCmd_1.CommandText = "select tc_orgcd,tc_class ,file_text=( select tcdf_filetxt   from  treaty_case_計價_file where tc_seno = @seno and tcdf_no=@fid  )  from treaty_case where  tc_seno = @seno";
        oCmd_1.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(seno));
        oCmd_1.Parameters.AddWithValue("fid", oRCM.SQLInjectionReplaceAll(fid));
        SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        oda_1.Fill(ds_1, "myTable");
        DataView dv = ds_1.Tables[0].DefaultView;
        if (dv.Count >= 1)
        {
            ViewState["orgcd"] = dv[0]["tc_orgcd"].ToString().Trim();
            ViewState["class"] = dv[0]["tc_class"].ToString().Trim();
            txt_filetxt.Text = Server.HtmlEncode(dv[0]["file_text"].ToString().Trim());
        }
        ds_1.Dispose();
        oCmd_1.Dispose();
        oda_1.Dispose();
        if (fid != "")
        {
            PH_up.Visible = false;
        }
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string str_error = "", str_alert = "";

        if (fid == "")
        {
            string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
            string path = string.Format("{0}\\{1}\\", FilePathString, "CA");
            string strPreRandom = Path.GetRandomFileName().Substring(0, 5);

            if (FU_up.PostedFile.ContentLength > 0)
            {
                //if (!Directory.Exists(path.Replace("/", "").Replace("..", "")))
                //{
                //    Directory.CreateDirectory(path.Replace("/", "").Replace("..", ""));
                //}
                string upFileName = FU_up.FileName.Replace("&", "＆").Replace("­", "－");
                oRCM.GetValidPathPart(FilePathString, "CA");
                string fileName = "CV_" + ViewState["seno"].ToString() + "_" + strPreRandom + "_" +
                                        Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("--", "－－") +
                                        Path.GetExtension(upFileName);
                string filePath = Path.Combine(path, fileName);
                FU_up.SaveAs(filePath);
                //FU_up.SaveAs(path.Replace("/", "").Replace("..", "") + fileName);

                //System.Web.UI.WebControls.SqlDataSource SqlDataSource1 = new System.Web.UI.WebControls.SqlDataSource();
                //SqlDataSource1.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SqlDataSource1.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SqlDataSource1.InsertCommand = "esp_TreatyCase_valuation";
                //SqlDataSource1.InsertParameters.Add("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                //SqlDataSource1.InsertParameters.Add("fd_name", upFileName.Replace("--", "－－"));
                //SqlDataSource1.InsertParameters.Add("filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                //SqlDataSource1.InsertParameters.Add("file_url", oRCM.SQLInjectionReplaceAll(filePath));
                //SqlDataSource1.InsertParameters.Add("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                //SqlDataSource1.InsertParameters.Add("mode", "file_Ins");
                //SqlDataSource1.InsertParameters.Add("fid", "");
                //ConvertSqlParametersEmptyStringToNull(SqlDataSource1, false, "Insert");
                //SqlDataSource1.Insert();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase_valuation";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("fd_name", upFileName.Replace("--", "－－"));
                    sqlCmd.Parameters.AddWithValue("filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                    sqlCmd.Parameters.AddWithValue("file_url", oRCM.SQLInjectionReplaceAll(filePath));
                    sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                    sqlCmd.Parameters.AddWithValue("mode", "file_Ins");
                    sqlCmd.Parameters.AddWithValue("fid", "");

                    try
                    {
                        sqlConn.Open();

                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                if (str_error != "")
                {
                    StringBuilder script_over = new StringBuilder("<script type='text/javascript'>alert('" + str_alert + "\\n檔案上傳完成!'); parent.$.fn.colorbox.close();</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);
                }
                else
                {
                    StringBuilder script_over = new StringBuilder("<script type='text/javascript'> alert('檔案上傳完成!'); parent.$.fn.colorbox.close();</script>");
                    ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);
                }
            }
        }
        else
        {
            //System.Web.UI.WebControls.SqlDataSource SqlDataSource1 = new System.Web.UI.WebControls.SqlDataSource();
            //SqlDataSource1.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SqlDataSource1.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SqlDataSource1.InsertCommand = "esp_TreatyCase_valuation";
            //SqlDataSource1.InsertParameters.Add("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SqlDataSource1.InsertParameters.Add("filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
            //SqlDataSource1.InsertParameters.Add("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            //SqlDataSource1.InsertParameters.Add("mode", "file_modify");
            //SqlDataSource1.InsertParameters.Add("fid", oRCM.SQLInjectionReplaceAll(fid));
            //ConvertSqlParametersEmptyStringToNull(SqlDataSource1, false, "Update");
            //SqlDataSource1.Insert();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase_valuation";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("mode", "file_modify");
                sqlCmd.Parameters.AddWithValue("fid", oRCM.SQLInjectionReplaceAll(fid));

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            if (str_error != "")
            {
                StringBuilder script_over = new StringBuilder("<script type='text/javascript'>alert('" + str_alert + "\\n檔案上傳完成!'); parent.$.fn.colorbox.close();</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);
            }
            else
            {
                StringBuilder script_over = new StringBuilder("<script type='text/javascript'> alert('檔案上傳完成!'); parent.$.fn.colorbox.close();</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script_over.ToString(), false);
            }
        }
    }
}