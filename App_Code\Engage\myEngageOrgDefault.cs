﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;


namespace Engage
{
	/// <summary>
	/// Summary description for myEngageOrgDefault
	/// </summary>
	public class myEngageOrgDefault : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private string _empno;
		private string _empname;

		string _orgcd = string.Empty;		// 管理單位
		string _type = string.Empty;		// P1, P2, C1, C2, S1, S2
		string _emp_list = string.Empty;	// 工號/姓名/流程順序
		string _rate1 = string.Empty;		// 研製間接費用
		string _rate2 = string.Empty;		// 推廣費用
		string _rate3 = string.Empty;		// 院所管理費
		string _rate4 = string.Empty;		// 損失準備
		string _rate5 = string.Empty;		// 預期利潤/公費
		string _rate6 = string.Empty;		// 售後服務(保固)費
		string _rate7 = string.Empty;		// 其他
		string _rate8 = string.Empty;		// 分包工作管理費用
		string _rate9 = string.Empty;		// 代購工作管理費用
		string _plan_reviewtype = string.Empty;		// 規劃構想審查及簽核方式
		string _cost_reviewtype = string.Empty;		// 成本訂價審查及簽核方式
		string _editempno = string.Empty;		// 建檔人/修改人工號
		string _editempname = string.Empty;		// 建檔人/修改人姓名
		string _editdate = string.Empty;		// 建檔/修改日期
		string _onestep_sign = string.Empty;		// 適用一段式簽核

		#endregion

		#region 公有屬性

		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

	
		public string Orgcd
		{
			get { return _orgcd; }
			set { _orgcd = value; }
		}

		public string Type
		{
			get { return _type; }
			set { _type = value; }
		}

		public string Emp_List
		{
			get { return _emp_list; }
			set { _emp_list = value; }
		}

		public string Rate1
		{
			get { return _rate1; }
			set { _rate1 = value; }
		}

		public string Rate2
		{
			get { return _rate2; }
			set { _rate2 = value; }
		}

		public string Rate3
		{
			get { return _rate3; }
			set { _rate3 = value; }
		}

		public string Rate4
		{
			get { return _rate4; }
			set { _rate4 = value; }
		}

		public string Rate5
		{
			get { return _rate5; }
			set { _rate5 = value; }
		}

		public string Rate6
		{
			get { return _rate6; }
			set { _rate6 = value; }
		}

		public string Rate7
		{
			get { return _rate7; }
			set { _rate7 = value; }
		}

		public string Rate8
		{
			get { return _rate8; }
			set { _rate8 = value; }
		}

		public string Rate9
		{
			get { return _rate9; }
			set { _rate9 = value; }
		}

		public string Plan_ReviewType
		{
			get { return _plan_reviewtype; }
			set { _plan_reviewtype = value; }
		}

		public string Cost_ReviewType
		{
			get { return _cost_reviewtype; }
			set { _cost_reviewtype = value; }
		}

		public string EditEmpno
		{
			get { return _editempno; }
			set { _editempno = value; }
		}

		public string EditEmpname
		{
			get { return _editempname; }
			set { _editempname = value; }
		}

		public string EditDate
		{
			get { return _editdate; }
			set { _editdate = value; }
		}

		public string Onestep_sign
		{
			get { return _onestep_sign; }
			set { _onestep_sign = value; }
		}

		#endregion

		public myEngageOrgDefault() { }


		#region 「提供能查詢單位」資料
		/// <summary>
		/// 依據 empno 員工工號，取得「提供能查詢單位」給 DropDownList_Org 使用。
		/// </summary>
		/// <returns>DataTable</returns>
		public DataTable GetOrgRight()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_org_default_right_query";


			oCmd.Parameters.AddWithValue("@empno", _empno);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}
		#endregion

		#region GetOne
		/// <summary>
		/// 依據 orgcd 單位代碼, 取得該單位相關的設定資料
		/// </summary>
		/// <param name="orgcd"></param>
		/// <returns></returns>
		public DataSet GetOne(string orgcd)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_org_default_query";


			oCmd.Parameters.AddWithValue("@orgcd", orgcd);

			DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
			return ds;
		}
		#endregion

		/// <summary>
		/// 取得「該類別」的固定人員
		/// </summary>
		/// <param name="orgcd"></param>
		/// <param name="type"></param>
		/// <returns></returns>
		public DataTable GetEmpList(string orgcd, string type)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"SELECT eed_empno, eed_empname, eed_serial FROM engage_empno_default WHERE eed_org = @orgcd and eed_type = @type order by eed_serial;";

			oCmd.Parameters.AddWithValue("@orgcd", orgcd);
			oCmd.Parameters.AddWithValue("@type", type);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}


		public bool UpdateEmpno()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_org_default_update_empno";

			oCmd.Parameters.AddWithValue("@orgcd", _orgcd);		
			oCmd.Parameters.AddWithValue("@type", _type);			
			oCmd.Parameters.AddWithValue("@emp_list", _emp_list);
			oCmd.Parameters.AddWithValue("@editempno", _editempno);
			oCmd.Parameters.AddWithValue("@editempname", _editempname);			
			oCmd.Parameters.AddWithValue("@editdate", _editdate);			

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#region 取得「單位管理預設值-成本訂價」
		/// <summary>
		/// 取得「單位管理預設值-成本訂價」
		/// </summary>
		/// <param name="orgcd"></param>
		/// <returns></returns>
		public DataTable GetCostrate(string orgcd)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"SELECT * 
					,CONVERT(varchar,CONVERT(datetime,rate_mod_date),111) AS rate_mod_date_slash
				FROM engage_costrate WHERE ecr_org = @orgcd";

			oCmd.Parameters.AddWithValue("@orgcd", orgcd);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 取得「單位管理預設值-成本訂價」的異動歷程
		/// <summary>
		/// 取得「單位管理預設值-成本訂價」的異動歷程
		/// </summary>
		/// <param name="orgcd"></param>
		/// <returns></returns>
		public DataTable GetCostrateHis(string orgcd)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"SELECT *
					,CONVERT(varchar,CONVERT(datetime,rate_mod_date),111) AS rate_mod_date_slash
				FROM engage_costrate_his WHERE ecr_org = @orgcd order by ecr_create_date desc";

			oCmd.Parameters.AddWithValue("@orgcd", orgcd);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region 更新「單位管理預設值-成本訂價」
		/// <summary>
		/// 更新「單位管理預設值-成本訂價」
		/// </summary>
		/// <returns></returns>
		public bool UpdateCostrate()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_org_default_update_costrate";

			oCmd.Parameters.AddWithValue("@orgcd", _orgcd);
			oCmd.Parameters.AddWithValue("@rate1", _rate1);
			oCmd.Parameters.AddWithValue("@rate2", _rate2);
			oCmd.Parameters.AddWithValue("@rate3", _rate3);
			oCmd.Parameters.AddWithValue("@rate4", _rate4);
			oCmd.Parameters.AddWithValue("@rate5", _rate5);
			oCmd.Parameters.AddWithValue("@rate6", _rate6);
			oCmd.Parameters.AddWithValue("@rate7", _rate7);
			oCmd.Parameters.AddWithValue("@rate8", _rate8);
			oCmd.Parameters.AddWithValue("@rate9", _rate9);
			oCmd.Parameters.AddWithValue("@cost_reviewtype", "");	//另外獨立出去，改用 UpdateCostReviewtype()，但為了保持SP完整性，仍然需帶參數
			oCmd.Parameters.AddWithValue("@editempno", _editempno);
			oCmd.Parameters.AddWithValue("@editempname", _editempname);
			oCmd.Parameters.AddWithValue("@editdate", _editdate);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion


		#region 更新「規劃構想-審核方式和適用一段式簽核」
		/// <summary>
		/// 更新「規劃構想-審核方式和適用一段式簽核」
		/// </summary>
		/// <returns></returns>
		public bool UpdatePlantype()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_org_default_update_plantype";

			oCmd.Parameters.AddWithValue("@orgcd", _orgcd);
			oCmd.Parameters.AddWithValue("@plan_reviewtype", _plan_reviewtype);
			oCmd.Parameters.AddWithValue("@editempno", _editempno);
			oCmd.Parameters.AddWithValue("@editempname", _editempname);
			oCmd.Parameters.AddWithValue("@editdate", _editdate);
			oCmd.Parameters.AddWithValue("@onestep_sign", _onestep_sign);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新「成本訂價-審核方式」
		/// <summary>
		/// 更新「成本訂價-審核方式」
		/// </summary>
		/// <returns></returns>
		public bool UpdateCostReviewtype()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists(select * from engage_costrate where ecr_org = @orgcd and ecr_keyindate is null)
begin
	--新建資料, 須補上建檔人和建檔日期
	update engage_costrate 
	set
		ecr_keyinempno = case when ecr_moddate is not null then ecr_modempno else @editempno end , 
		ecr_keyinname = case when ecr_moddate is not null then ecr_modname else @editempname end,
		ecr_keyindate = case when ecr_moddate is not null then ecr_moddate else @editdate end
	where  ecr_org = @orgcd	
end

UPDATE engage_costrate
SET ecr_cost_reviewtype = @cost_reviewtype,
	ecr_modempno = @editempno,
	ecr_modname = @editempname,
	ecr_moddate = @editdate
WHERE ecr_org = @orgcd ";

			oCmd.Parameters.AddWithValue("@orgcd", _orgcd);
			oCmd.Parameters.AddWithValue("@cost_reviewtype", _cost_reviewtype);
			oCmd.Parameters.AddWithValue("@editempno", _editempno);
			oCmd.Parameters.AddWithValue("@editempname", _editempname);
			oCmd.Parameters.AddWithValue("@editdate", _editdate);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion
	}
}