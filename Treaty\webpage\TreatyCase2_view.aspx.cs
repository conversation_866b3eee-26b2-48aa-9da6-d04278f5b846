﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase2_view : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }

    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.Image1));
        if (Request["seno"] != null)
        {
            if ((Request["seno"].Length == 0) || (Request["seno"].Length > 8))
                Response.Redirect("../danger.aspx");
            if (!IsNumber(Request["seno"].ToString()))
                Response.Redirect("../danger.aspx");
            //if (!IsNumber(Request["newver"].ToString()))
            //    Response.Redirect("../danger.aspx");
        }
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }

            ViewState["seno"] = Request["seno"].ToString();
            BindData();
            BindData_Customer();
            Bind_contact();
            BindData_impeach();
            BindData_Evidence();
            BindData_file();
            Bind_cop();
            BindDefer();
            BindData_Lawer();
            Bind法律檢察署();
            Bind處理紀錄();
            Bind契約();
            Bind後續契約簽訂情形();

            string strCaseNo = "";
            if (Request["contno"] != null)
            {
                if (Request["contno"].Length > 15)
                    Response.Redirect("../danger.aspx");
                if (!IsNatural_Number(Request["contno"].ToString()))
                    Response.Redirect("../danger.aspx");
                strCaseNo = Request["contno"].ToString();
            }
            else
            {

                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
            }

            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            x_dept.Attributes.Add("readOnly", "readonly");
            ViewState["tr_class"] = "L";
        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "impeach_renew")
        {
            BindData_impeach();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Evidence_renew")
        {
            BindData_Evidence();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Lawer_renew")
        {
            BindData_Lawer();
        }

        if (Request.Params.Get("__EVENTTARGET") == "CR_renew")
        {
            Bind處理紀錄();
        }
        if (Request.Params.Get("__EVENTTARGET") == "DL_renew")
        {
            BindData_Lawer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "FU_renew")
        {
            Bind後續契約簽訂情形();
        }


        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Defer_renew")
        {
            BindDefer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "cop_renew")
        {
            Bind_cop();
        }
        if (Request.Params.Get("__EVENTTARGET") == "contact_renew")
        {
            Bind_contact();
        }

        if (Request.Params.Get("__EVENTTARGET") == "Court_renew")
        {
            Bind法律檢察署();
        }


        if (Request.Params.Get("__EVENTTARGET") == "contract_renew")
        {
            Bind契約();
        }
        Bind可結案檢查();
    }
    private void Bind可結案檢查()
    {
        //SDS_CloseCase.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_CloseCase.SelectParameters.Clear();
        //SDS_CloseCase.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_CloseCase.SelectCommand = " select tc2_degree from treaty_case2 where tc2_degree <>'Z' and tc2_seno=@seno";
        //SDS_CloseCase.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //for (int i = 0; i < this.SDS_CloseCase.SelectParameters.Count; i++)
        //{
        //    SDS_CloseCase.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_CloseCase.DataBind();
        //System.Data.DataView dv_actno = (DataView)SDS_CloseCase.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select tc2_degree from treaty_case2 where tc2_degree <>'Z' and tc2_seno=@seno";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_actno = dt.DefaultView;
        if (dv_actno.Count >= 1)
        {
            btnEdit.Visible = true;
        }

    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_TreatyCase2_log";
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplaceAll(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplaceAll(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplaceAll(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplaceAll(GetUserIP()));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplaceAll(xApp));
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    public void BindData()
    {
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommand = " select * from treaty_case2 where tc2_seno = @sn ";
        //SDS_NR.SelectParameters.Add("sn", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * from treaty_case2 where tc2_seno = @sn ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            string str_tr_year = dv[0]["tc2_year"].ToString().Trim();
            string str_tr_orgcd = dv[0]["tc2_orgcd"].ToString().Trim();
            string str_tr_class = dv[0]["tc2_class"].ToString().Trim();
            ViewState["tr_class"] = "L";

            string str_tr_sn = dv[0]["tc2_sn"].ToString().Trim();
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = string.Format("{0}{1}{2}{3}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn);//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;
            txt_accept_date.Text = dv[0]["tc2_accept_date"].ToString().Trim();
            #region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + SQLInjectionReplaceAll(dv[0]["tc2_promoter_no"].ToString().Trim() )+ "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tc2_promoter_no"].ToString().Trim()));


                try
                {

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt = new DataTable();
                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_emp = dt.DefaultView;
            if (dv_emp.Count >= 1)
            {
                txt_req_dept.Text = dv_emp[0]["com_deptid"].ToString().Trim();
                x_dept.Text = dv_emp[0]["com_deptid"].ToString().Trim().Substring(2, 5);
                txt_promoter_name.Text = dv_emp[0]["com_cname"].ToString().Trim();
                txt_promoter_empno.Value = dv_emp[0]["com_empno"].ToString().Trim();
                txtTel.Text = dv_emp[0]["com_telext"].ToString().Trim();
                txtOrgAbbrName.Text = dv_emp[0]["orgName"].ToString().Trim();
                ViewState["com_orgcd"] = dv_emp[0]["com_orgcd"].ToString().Trim();
            }

            txt_name.Text = dv[0]["tc2_name"].ToString().Trim();//洽案（契約名稱）
            #endregion
            #region 客戶
            h_compno.Value = dv[0]["tc2_compidno_all"].ToString().Trim().Replace("㊣", ",");//簽約對象(多)
            BindData_Customer();
            #endregion
            TB_Addr.Text = dv[0]["tc2_compaddr"].ToString();
            TB_ManageNote.Text = dv[0]["tc2_manage_note"].ToString();
            TB_tort_impeach.Text = dv[0]["tc2_tort_impeach"].ToString();
            TB_PlanBudget.Text = dv[0]["tc2_bugdet"].ToString();
            TB_RoySummary.Text = dv[0]["tc2_breviary"].ToString();
            TB_prosecution_date.Text = dv[0]["tc2_prosecution_date"].ToString();
            RBL_litigation_type.SelectedValue = dv[0]["tc2_litigation_type"].ToString();
            TB_Litigation_name.Text = dv[0]["tc2_litigation_name"].ToString();
            TB_Litigation_target.Text = dv[0]["tc2_litigation_target"].ToString();
            lb_assign_name.Text = dv[0]["tc2_assign_name"].ToString();//分案主管
            lb_assign_date.Text = dv[0]["tc2_assign_datetime"].ToString().Length > 0 ? Convert.ToDateTime(dv[0]["tc2_assign_datetime"].ToString().Trim()).ToString("yyyy/MM/dd") : "";  //分案日期
            lb_handle_name.Text = dv[0]["tc2_handle_name"].ToString();//法務承辦人姓名
            lb_handle_empno.Text = dv[0]["tc2_handle_empno"].ToString();//法務承辦人工號
            lb_handle_ext.Text = dv[0]["tc2_handle_ext"].ToString();//法務承辦人分機
            lb_expect_close_date.Text = dv[0]["tc2_prefinish_date"].ToString().Length > 0 ? DateTime.ParseExact(dv[0]["tc2_prefinish_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";//預估完成日
            lb_process_date.Text = dv[0]["tc2_process_date"].ToString();//處理天數
            if (dv[0]["tc2_degree"].ToString().Trim() != "Z")//如果狀態不是結件，不秀
            {
                if (lb_process_date.Text.Trim() == "0")
                    lb_process_date.Text = "";
                if (lb_contract_count.Text.Trim() == "0")
                    lb_contract_count.Text = "";

            }
            if ((dv[0]["tc2_degree"].ToString().Trim() == "Z") || (dv[0]["tc2_degree"].ToString().Trim() == "C")) //如果狀態不是結件，不秀
            {
                BT_rework.Visible = true;
            }
            else
                BT_rework.Visible = false;

            lb_contract_count.Text = dv[0]["tc2_contract_count"].ToString(); //產出契約數

            lb_case_closedate.Text = dv[0]["tc2_case_closedate"].ToString().Trim();//需求結件日期

            //SDS_DDL_degree.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_DDL_degree.SelectCommandType = SqlDataSourceCommandType.Text;
            //SDS_DDL_degree.SelectCommand = "exec esp_treatyCase_codetable '','08'  ";
            //SDS_DDL_degree.DataBind();
            //DDL_Degree.DataBind();

            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treatyCase_codetable";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@code_group", "");
                sqlCmd.Parameters.AddWithValue("@code_type", "08");


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);
                    DDL_Degree.DataSource = dt;
                    DDL_Degree.DataBind();

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DDL_Degree.SelectedValue = dv[0]["tc2_degree"].ToString().Trim();//進度
            LT_L_Degree.Text = Server.HtmlEncode(DDL_Degree.SelectedItem.Text);
            lb_send_date.Text = dv[0]["tc2_keyin_date"].ToString();
            lb_modify_emp_name.Text = dv[0]["tc2_modify_emp_name"].ToString();//修改人
            lb_modify_emp_no.Text = dv[0]["tc2_modify_emp_no"].ToString();//修改工號
            lb_modify_date.Text = dv[0]["tc2_modify_date"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tc2_modify_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : ""; //修改日期
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            Treaty_log(ViewState["seno"].ToString(), "檢視承辦單", "", "", "treaty\\TreatyCase2_modify.aspx");
        }
    }
    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = "esp_TreatyCase2_MultiCustomer_List";
        //SDS_company.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_company.SelectParameters.Add("customers" , SQLInjectionReplaceAll(h_compno.Value.ToString()));
        //SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase2_MultiCustomer_List";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindData_impeach()
    {
        //SDS_impeach.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_impeach.SelectParameters.Clear();
        //SDS_impeach.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_impeach.SelectCommand = "esp_TreatyCase2_IP_modify";
        //SDS_impeach.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()) );
        //SDS_impeach.SelectParameters.Add("ip_type", SQLInjectionReplaceAll("")  );
        //SDS_impeach.SelectParameters.Add("ipno", SQLInjectionReplaceAll("")  );
        //SDS_impeach.SelectParameters.Add("ipname", SQLInjectionReplaceAll("") );
        //SDS_impeach.SelectParameters.Add("country", SQLInjectionReplaceAll("") );
        //SDS_impeach.SelectParameters.Add("patentno", SQLInjectionReplaceAll("")  );
        //SDS_impeach.SelectParameters.Add("mode", SQLInjectionReplaceAll("List"));
        //for (int i = 0; i < SDS_impeach.SelectParameters.Count; i++)
        //{
        //    SDS_impeach.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_impeach.DataBind();
        //GV_impeach.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_IP_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ip_type", "");
            sqlCmd.Parameters.AddWithValue("@ipno", "");
            sqlCmd.Parameters.AddWithValue("@ipname", "");
            sqlCmd.Parameters.AddWithValue("@country", "");
            sqlCmd.Parameters.AddWithValue("@patentno", "");
            sqlCmd.Parameters.AddWithValue("@mode", "List");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_impeach.DataSource = dt;
                GV_impeach.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindData_Evidence()
    {
        //SDS_Evidence.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Evidence.SelectParameters.Clear();
        //SDS_Evidence.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Evidence.SelectCommand = "esp_TreatyCase2_Evidence_modify";
        //SDS_Evidence.SelectParameters.Add("subseno", SQLInjectionReplaceAll("0"));
        //SDS_Evidence.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_Evidence.SelectParameters.Add("Evidence", SQLInjectionReplaceAll("") );
        //SDS_Evidence.SelectParameters.Add("Ename", SQLInjectionReplaceAll("") );
        //SDS_Evidence.SelectParameters.Add("mode", SQLInjectionReplaceAll("List"));
        //for (int i = 0; i < SDS_Evidence.SelectParameters.Count; i++)
        //{
        //    SDS_Evidence.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_Evidence.DataBind();
        //GV_Evidence.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_Evidence_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@subseno", "0");
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@Evidence", "");
            sqlCmd.Parameters.AddWithValue("@Ename", "");
            sqlCmd.Parameters.AddWithValue("@mode", "List");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Evidence.DataSource = dt;
                GV_Evidence.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindData_Lawer()
    {
        //SDS_Lawer.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_Lawer.SelectParameters.Clear();
        //SDS_Lawer.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Lawer.SelectCommand = "esp_TreatyCase2_Lawer";
        //SDS_Lawer.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString())  );
        //SDS_Lawer.SelectParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo) );
        //SDS_Lawer.SelectParameters.Add("subseno", SQLInjectionReplaceAll("")  );
        //SDS_Lawer.SelectParameters.Add("recordate", SQLInjectionReplaceAll("")  );
        //SDS_Lawer.SelectParameters.Add("Lawer", SQLInjectionReplaceAll("")  );
        //SDS_Lawer.SelectParameters.Add("docu", SQLInjectionReplaceAll("") );
        //SDS_Lawer.SelectParameters.Add("mode", SQLInjectionReplaceAll("List") );
        //for (int i = 0; i < SDS_Lawer.SelectParameters.Count; i++)
        //{
        //    SDS_Lawer.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_Lawer.DataBind();
        //GV_Lawer.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_Lawer";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@subseno", "");
            sqlCmd.Parameters.AddWithValue("@recordate", "");
            sqlCmd.Parameters.AddWithValue("@Lawer", "");
            sqlCmd.Parameters.AddWithValue("@docu", "");
            sqlCmd.Parameters.AddWithValue("@mode", "List");


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Lawer.DataSource = dt;
                GV_Lawer.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind_contact()
    {
        //SDS_contact.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_contact.SelectParameters.Clear();
        //SDS_contact.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_contact.SelectCommand = " select * from treaty_case2_contact where tc2c_seno=@seno";
        //SDS_contact.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_contact.DataBind();
        //SGV_contact.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * from treaty_case2_contact where tc2c_seno=@seno";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));



            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_contact.DataSource = dt;
                SGV_contact.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_contact_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            //SDS_contact.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_contact.DeleteParameters.Clear();
            //SDS_contact.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_contact.DeleteCommand = " delete  treaty_case2_contact where tc2c_sub_seno=@sub_seno";
            //SDS_contact.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_contact.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete  treaty_case2_contact where tc2c_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            Bind_contact();
        }
    }
    private void Bind_cop()
    {
        //SDS_cop.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_cop.SelectParameters.Clear();
        //SDS_cop.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_cop.SelectCommand = "esp_TreatyCase2_cop";
        //SDS_cop.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //for (int i = 0; i < this.SDS_cop.SelectParameters.Count; i++)
        //{
        //    SDS_cop.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_cop.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_cop.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_cop";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));


            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            lb_cop.Text = dvR[0][0].ToString();
        }
        else
            lb_cop.Text = "";

    }
    private void BindDefer()
    {
        //SDS_Defer.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Defer.SelectParameters.Clear();
        //SDS_Defer.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Defer.SelectCommand = "esp_TreatyCase2_Defer";
        //SDS_Defer.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_Defer.DataBind();
        //GV_Defer.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_Defer";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Defer.DataSource = dt;
                GV_Defer.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind法律檢察署()
    {
        //SDS_法律檢察署.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_法律檢察署.SelectParameters.Clear();
        //SDS_法律檢察署.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_法律檢察署.SelectCommand = "esp_TreatyCase2_court";
        //SDS_法律檢察署.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_法律檢察署.DataBind();
        //RT_法律檢察署.DataBind();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_court";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                RT_法律檢察署.DataSource = dt;
                RT_法律檢察署.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind處理紀錄()
    {
        //SDS_處理紀錄.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_處理紀錄.SelectParameters.Clear();
        //SDS_處理紀錄.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_處理紀錄.SelectCommand = "esp_TreatyCase2_record";
        //SDS_處理紀錄.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_處理紀錄.SelectParameters.Add("subseno", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("recordate", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("nextdate", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("type", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("iterm", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("docu", SQLInjectionReplaceAll(""));
        //SDS_處理紀錄.SelectParameters.Add("mode", "List");
        //for (int i = 0; i < SDS_處理紀錄.SelectParameters.Count; i++)
        //{
        //    SDS_處理紀錄.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_處理紀錄.DataBind();
        //gv_處理紀錄.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_record";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@subseno", "");
            sqlCmd.Parameters.AddWithValue("@empno", "");
            sqlCmd.Parameters.AddWithValue("@recordate", "");
            sqlCmd.Parameters.AddWithValue("@nextdate", "");
            sqlCmd.Parameters.AddWithValue("@type", "");
            sqlCmd.Parameters.AddWithValue("@iterm", "");
            sqlCmd.Parameters.AddWithValue("@docu", "");
            sqlCmd.Parameters.AddWithValue("@mode", "List");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_處理紀錄.DataSource = dt;
                gv_處理紀錄.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind契約()
    {
        //SDS_Contract.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Contract.SelectParameters.Clear();
        //SDS_Contract.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Contract.SelectCommand = "esp_TreatyCase2_Contract";
        //SDS_Contract.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_Contract.SelectParameters.Add("sub_seno", SQLInjectionReplaceAll("0"));
        //SDS_Contract.SelectParameters.Add("contno", SQLInjectionReplaceAll("0"));
        //SDS_Contract.SelectParameters.Add("mode", SQLInjectionReplaceAll("List"));
        //SDS_Contract.DataBind();
        //GV_Contract.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_Contract";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sub_seno", "0");
            sqlCmd.Parameters.AddWithValue("@contno", "0");
            sqlCmd.Parameters.AddWithValue("@mode", "List");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Contract.DataSource = dt;
                GV_Contract.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void Bind後續契約簽訂情形()
    {
        //SDS_後續契約簽訂情形.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_後續契約簽訂情形.SelectParameters.Clear();
        //SDS_後續契約簽訂情形.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_後續契約簽訂情形.SelectCommand = "esp_TreatyCase2_followup";
        //SDS_後續契約簽訂情形.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_後續契約簽訂情形.SelectParameters.Add("sub_seno", SQLInjectionReplaceAll("0"));
        //SDS_後續契約簽訂情形.SelectParameters.Add("treaty_seno", SQLInjectionReplaceAll("0"));
        //SDS_後續契約簽訂情形.SelectParameters.Add("abstract", SQLInjectionReplaceAll("0"));
        //SDS_後續契約簽訂情形.SelectParameters.Add("mode", SQLInjectionReplaceAll("List"));
        //SDS_後續契約簽訂情形.DataBind();
        //gv_後續契約簽訂情形.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_followup";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@sub_seno", "0");
            sqlCmd.Parameters.AddWithValue("@treaty_seno", "0");
            sqlCmd.Parameters.AddWithValue("@abstract", "0");
            sqlCmd.Parameters.AddWithValue("@mode", "List");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_後續契約簽訂情形.DataSource = dt;
                gv_後續契約簽訂情形.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + LB.Text.ToString() + "\");' >" + LB.Text.ToString() + "</a>";
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace("," + e.CommandArgument, "");
            BindData_Customer();
        }
    }
    private void BindData_file()
    {
        //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyCase2_files";
        //SDS_gv_file.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_gv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("Edit"));
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_files";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "Edit");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //ViewState["Customers"] = Convert.ToString(ViewState["Customers"]).Trim().Replace("," + e.CommandArgument, "");
            //BindData();
            string str_file_url = "";
            string str_filename = "";
            //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_gv_file.SelectParameters.Clear();
            //SDS_gv_file.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.SelectCommand = "esp_TreatyCase2_file_modify";
            //SDS_gv_file.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_gv_file.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < SDS_gv_file.SelectParameters.Count; i++)
            //{
            //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_gv_file.DataBind();
            //System.Data.DataView dv = (DataView)SDS_gv_file.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            FileInfo fi = new FileInfo(str_file_url.Replace("/", "").Replace("..", ""));
            if (fi.Exists)
            {
                fi.Delete();
                Treaty_log(ViewState["seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase2_modify.aspx");
                //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SDS_gv_file.DeleteParameters.Clear();
                //SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.Text;
                //SDS_gv_file.DeleteCommand = "delete treaty_case_draft_file where tcdf_no=@fid";
                //SDS_gv_file.DeleteParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                //SDS_gv_file.Delete();

                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"delete treaty_case_draft_file where tcdf_no=@fid";

                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
            }
        }
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";
            //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_gv_file.SelectParameters.Clear();
            //SDS_gv_file.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.SelectCommand = "esp_TreatyCase2_file_modify";
            //SDS_gv_file.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_gv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_gv_file.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_gv_file.SelectParameters.Count; i++)
            //{
            //    SDS_gv_file.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_gv_file.DataBind();
            //System.Data.DataView dv = (DataView)SDS_gv_file.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyCase2_modify.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                Response.Flush();
                Response.End();
            }
        }
        BindData_file();
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            Label lb_tcdf_type = (Label)e.Row.FindControl("LB_tcdf_type");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");
                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "EfileCoentent(" + ViewState["seno"].ToString() + "," + lb_tcdf_no.Text + ",'CR');");

            }
        }
    }
    protected void GV_Defer_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lB_defer_date = (Label)e.Row.FindControl("LB_defer_date");
            Label lB_keyin_date = (Label)e.Row.FindControl("LB_keyin_date");
            if (lB_defer_date != null)
            {
                lB_defer_date.Text = lB_defer_date.Text.ToString().Trim().Length > 0 ? DateTime.ParseExact(lB_defer_date.Text.ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
                lB_keyin_date.Text = lB_keyin_date.Text.ToString().Trim().Length > 0 ? DateTime.ParseExact(lB_keyin_date.Text.ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            }
        }
    }
    protected void GV_impeach_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            //SDS_impeach.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_impeach.DeleteParameters.Clear();
            //SDS_impeach.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_impeach.DeleteCommand = "delete treaty_case2_ip where tc2p_sub_seno=@sub_seno";
            //SDS_impeach.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_impeach.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_ip where tc2p_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindData_impeach();
        }
    }
    protected void GV_impeach_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void GV_Evidence_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            //SDS_Evidence.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_Evidence.DeleteParameters.Clear();
            //SDS_Evidence.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_Evidence.DeleteCommand = "delete treaty_case2_infringement_evidence where tcie_sub_seno=@sub_seno";
            //SDS_Evidence.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_Evidence.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_infringement_evidence where tcie_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindData_Evidence();
        }
    }
    protected void GV_Evidence_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            ImageButton im_Evidence_no = (ImageButton)e.Row.FindControl("Evidence_no");
            if (im_Evidence_no != null)
            {
                Label lb_FileCount = (Label)e.Row.FindControl("LB_FileCount");
                Label lb_sub_seno = (Label)e.Row.FindControl("LB_sub_seno");
                LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_Evidence_del");

                im_Evidence_no.Attributes.Add("onclick", "Efile(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + ",'IE');");

                LinkButton lb_Evidence_edit = (LinkButton)e.Row.FindControl("LB_Evidence_edit");
                if (lb_Evidence_edit != null)
                {
                    lb_Evidence_edit.Attributes.Add("onclick", "find_Evidence(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + " );");
                }
            }
        }
    }
    protected void GV_Lawer_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            //SDS_Lawer.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_Lawer.DeleteParameters.Clear();
            //SDS_Lawer.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_Lawer.DeleteCommand = "delete treaty_case2_lawer where tc2l_sub_seno=@sub_seno";
            //SDS_Lawer.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_Lawer.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_lawer where tc2l_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            BindData_Lawer();
        }
    }
    protected void GV_Lawer_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            ImageButton im_Lawer_no = (ImageButton)e.Row.FindControl("Lawer_no");
            if (im_Lawer_no != null)
            {
                Label lb_FileCount = (Label)e.Row.FindControl("LB_委任律師_FileCount");
                Label lb_sub_seno = (Label)e.Row.FindControl("LB_委任律師_sub_seno");
                LinkButton lb_委任律師_edit = (LinkButton)e.Row.FindControl("LB_委任律師_edit");
                im_Lawer_no.Attributes.Add("onclick", "Efile(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + ",'DL');");
            }
        }
    }
    protected void RT_法律檢察署_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        #region 將repeater的資料帶入

        if (e.Item.ItemIndex < 0)
        {
            return;
        }
        Control ctl = null;

        ctl = e.Item.FindControl("LB_Court");//法院檢察署
        if (ctl != null)
        {
            Label lb_Court = (Label)ctl;
            lb_Court.Text = Convert.ToString(DataBinder.Eval(e.Item.DataItem, "court")).Trim();
        }

        ctl = e.Item.FindControl("LB_Year");//年度
        if (ctl != null)
        {
            Label lb_Year = (Label)ctl;
            lb_Year.Text = Convert.ToString(DataBinder.Eval(e.Item.DataItem, "tcp_year")).Trim();
        }

        ctl = e.Item.FindControl("LB_Word");//字
        if (ctl != null)
        {
            Label lb_Word = (Label)ctl;
            lb_Word.Text = Convert.ToString(DataBinder.Eval(e.Item.DataItem, "tcp_word")).Trim();

        }

        ctl = e.Item.FindControl("LB_No");//號
        if (ctl != null)
        {
            Label lb_No = (Label)ctl;
            lb_No.Text = Convert.ToString(DataBinder.Eval(e.Item.DataItem, "tcp_no")).Trim();

        }

        ctl = e.Item.FindControl("LB_Stock");//股
        if (ctl != null)
        {
            Label lb_Stock = (Label)ctl;
            lb_Stock.Text = Convert.ToString(DataBinder.Eval(e.Item.DataItem, "tcp_stock")).Trim();
        }
        #endregion	
    }
    protected void gv_處理紀錄_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //SDS_處理紀錄.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_處理紀錄.DeleteParameters.Clear();
            //SDS_處理紀錄.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_處理紀錄.DeleteCommand = "delete treaty_case2_record where tc2r_sub_seno=@sub_seno";
            //SDS_處理紀錄.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_處理紀錄.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_record where tc2r_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Bind處理紀錄();
        }
    }
    protected void gv_處理紀錄_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            ImageButton im_處理紀錄_no = (ImageButton)e.Row.FindControl("IB_處理紀錄_no");
            if (im_處理紀錄_no != null)
            {
                Label lb_FileCount = (Label)e.Row.FindControl("LB_處理紀錄_FileCount");
                Label lb_sub_seno = (Label)e.Row.FindControl("LB_處理紀錄_sub_seno");
                im_處理紀錄_no.Attributes.Add("onclick", "Efile(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + ",'CR');");

                //LinkButton lb_處理紀錄_edit = (LinkButton)e.Row.FindControl("LB_處理紀錄_edit");
                //lb_處理紀錄_edit.Attributes.Add("onclick", "Precord_Modify(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + " );");
            }
        }
    }
    protected void gv_後續契約簽訂情形_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //SDS_後續契約簽訂情形.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_後續契約簽訂情形.DeleteParameters.Clear();
            //SDS_後續契約簽訂情形.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_後續契約簽訂情形.DeleteCommand = "delete treaty_case2_followup where  tc2u_sub_seno=@sub_seno";
            //SDS_後續契約簽訂情形.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_後續契約簽訂情形.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_followup where  tc2u_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Bind後續契約簽訂情形();
        }
    }
    protected void gv_後續契約簽訂情形_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            ImageButton im_後續契約簽訂情形_no = (ImageButton)e.Row.FindControl("IB_後續契約簽訂情形_no");
            if (im_後續契約簽訂情形_no != null)
            {
                Label lb_FileCount = (Label)e.Row.FindControl("LB_後續契約簽訂情形_FileCount");
                Label lb_sub_seno = (Label)e.Row.FindControl("LB_後續契約簽訂情形_sub_seno");
                im_後續契約簽訂情形_no.Attributes.Add("onclick", "Efile(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + ",'FU');");

                //LinkButton lb_後續契約簽訂情形_edit = (LinkButton)e.Row.FindControl("LB_後續契約簽訂情形_edit");
                //lb_後續契約簽訂情形_edit.Attributes.Add("onclick", "Treaty_Modify(" + ViewState["seno"].ToString() + "," + lb_sub_seno.Text + " );");
            }
        }
    }
    protected void GV_Contract_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            //SDS_Contract.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_Contract.DeleteParameters.Clear();
            //SDS_Contract.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_Contract.DeleteCommand = "delete treaty_case2_contract  where tc2t_contno=@contno and tc2t_seno=@seno";
            //SDS_Contract.DeleteParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_Contract.DeleteParameters.Add("contno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_Contract.Delete();
            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_contract  where tc2t_contno=@contno and tc2t_seno=@seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@contno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Bind契約();
        }
    }
    protected void GV_Contract_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void RT_法律檢察署_ItemCommand(object source, RepeaterCommandEventArgs e)
    {
        if (e.CommandName == "Delete")
        {
            //SDS_法律檢察署.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_法律檢察署.DeleteParameters.Clear();
            //SDS_法律檢察署.DeleteCommandType = SqlDataSourceCommandType.Text;
            //SDS_法律檢察署.DeleteCommand = "delete treaty_case2_court where tcp_sub_seno=@sub_seno";
            //SDS_法律檢察署.DeleteParameters.Add("sub_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_法律檢察署.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"delete treaty_case2_court where tcp_sub_seno=@sub_seno";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Bind法律檢察署();
        }
    }
    protected void btnEdit_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyCase2_modify.aspx?seno=" + ViewState["seno"].ToString());
    }
    protected void BT_rework_Click(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = "esp_TreatyCase2_reWork";
        //SDS_NR.UpdateParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.UpdateParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_NR.Update();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_reWork";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        BT_rework.Visible = false;
        string script = "<script language='javascript'>alert('案件已重開！');location.href='./TreatyCase2_view.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
    }
}