﻿<%@ Control Language="C#" AutoEventWireup="true" CodeFile="News.ascx.cs" Inherits="Comp_News" %>
<span class="font-normal font-size3 font-bold">
	<img src='<%= ResolveClientUrl("~/images/icon-1301.gif") %>' />
	<a id="lk_bulletin" runat="server" href="javascript::" onclick="doOpenBulletin(true);return false;">訊息公告</a>
	<%--<asp:SqlDataSource ID="sds_bulletin" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"
		SelectCommand="pr_bulletin_window_showing" SelectCommandType="StoredProcedure">
		<SelectParameters>
			<asp:Parameter ConvertEmptyStringToNull="false" Name="BB_sys" />
			<asp:Parameter ConvertEmptyStringToNull="false" Name="BB_type" DefaultValue="1" />
			<asp:Parameter ConvertEmptyStringToNull="false" Name="empno" />
		</SelectParameters>
	</asp:SqlDataSource>--%>
</span>
<script type="text/javascript">
	//訊息公告-開啟視窗
	function doOpenBulletin(v_closeButton) {
		var url = '<%=ResolveClientUrl("https://amps.itri.org.tw/Engage/Bulletin/BoardViewTop5.aspx?sys=") + this.SysCode %>';
		$.colorbox({
			iframe: true, transition: "none", opacity: "0.3", overlayClose: false,
			href: encodeURI(url), innerWidth: '600', innerHeight: '350', title: "訊息公告",
			closeButton: v_closeButton
		});
	}
	//訊息公告-關閉視窗
	function doCloseBulletin(url) {
		$.colorbox.close();
		window.open(url, 'MsgWin');
	}
</script>
