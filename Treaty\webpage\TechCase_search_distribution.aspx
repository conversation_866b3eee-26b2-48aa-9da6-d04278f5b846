﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_search_distribution.aspx.cs" Inherits="TechCase_search_distribution" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot_tech.ascx" TagPrefix="uc1" TagName="Foot" %>



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>

    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light"></div>
                        </div>
                        <div class="tabsubmenublock">
                            <div class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <asp:Button ID="btnQuery" runat="server" Text="查詢" CssClass="button" OnClick="btnQuery_Click"></asp:Button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                                        <FooterStyle BackColor="White" />
                                                        <PagerSettings Mode="NumericFirstLast" FirstPageImageUrl="../images/icon-04.gif" FirstPageText="第一頁" PreviousPageImageUrl="../images/icon-05.gif" PreviousPageText="上一頁" NextPageImageUrl="../images/icon-06.gif" NextPageText="下一頁" LastPageImageUrl="../images/icon-07.gif" LastPageText="最後一頁" />
                                                        <CustomPagerSettings PagingMode="Default" TextFormat="<span style='color:#000'>每頁</span><span style='color:#ffa500'>{0}</span><span style='color:#000'>筆/共</span><span style='color:#ffa500'>{1}</span><span style='color:#000'>筆</span>&#160;&#160;&#160;&#160;<span style='color:#000'>第</span><span style='color:#ffa500'>{2}</span><span style='color:#000'>頁/共</span><span style='color:#ffa500'>{3}</span><span style='color:#000'>頁</span<&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;" />
                                                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                        <Columns>
                                                            <asp:TemplateField>
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_bomb" runat="server" Text="&#128163;" Visible='<%#  Server.HtmlEncode(Eval("IsBomb").ToString())=="1" %>'></asp:Label>
                                                                    <%--<%# Eval("tt_seno") %>--%>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="技轉狀態 <hr> 技轉狀態c">
                                                                <HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" VerticalAlign="Middle" Width="100px"></ItemStyle>
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_技轉狀態" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉狀態").ToString())) %>'></asp:Label><hr />
                                                                    <asp:Label ID="LB_技轉狀態c" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉狀態c").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="單位名稱" SortExpression="單位名稱" HeaderText="單位名稱">
                                                                <ItemStyle Width="100px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="洽案／契約名稱" SortExpression="tt_name">
                                                                <ItemTemplate>
                                                                    <%# Server.HtmlEncode(oRCM.RemoveXss(Eval("議約編號").ToString())) %><br />
                                                                    <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tt_seno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tt_name").ToString())) %>'></asp:LinkButton><br />
                                                                </ItemTemplate>
                                                                <ItemStyle Width="400px" />
                                                            </asp:TemplateField>

                                                            <asp:BoundField DataField="客戶名稱" SortExpression="客戶名稱" HeaderText="客戶名稱">
                                                                <ItemStyle Width="250px" />
                                                            </asp:BoundField>


                                                            <asp:TemplateField HeaderText="技轉分案人<hr>技轉承辦人" SortExpression="技轉承辦人">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉分案人").ToString())) %><hr>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉承辦人").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="120px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="技轉分案人_c<hr>技轉承辦人_c" SortExpression="tc_handle_name">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉分案人_c").ToString())) %><hr>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("技轉承辦人_c").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="140px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="洽案<br>承辦人" SortExpression="洽案承辦人">
                                                                <ItemTemplate>
                                                                    <%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("洽案承辦人").ToString())) %>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="100px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="建檔日" SortExpression="建檔日" HeaderText="建檔日">
                                                                <ItemStyle Width="100px" HorizontalAlign="Center" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    </cc1:SmartGridView>
                                                </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <br />
                </div>
            </div>
        </div>
        <uc1:Foot runat="server" ID="Foot" />

    </form>
</body>
</html>
