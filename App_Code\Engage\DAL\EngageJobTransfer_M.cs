﻿using System;

namespace DAL.Model
{
	/// <summary>
	/// Summary description for EngageJobTransfer.
	/// </summary>
	[Serializable]
	public class EngageJobTransfer_M
	{
		#region Property

		public string srh_login_id = string.Empty;
		public string srh_orgcd = string.Empty;
		public string srh_registerdate1 = string.Empty;
		public string srh_registerdate2 = string.Empty;
		public string srh_keyword = string.Empty;
		public string srh_source_class = string.Empty;

		public string srh_execstatus = string.Empty;
		public string srh_execdept = string.Empty;
		public string srh_planerempno = string.Empty;
		public string srh_promoempno = string.Empty;
		public string srh_keyinempno = string.Empty;
		public string srh_agency = string.Empty;

		public string log_seqsn = string.Empty;
		public string log_infoclass = string.Empty;
		public string log_exec_empno = string.Empty;
		public string log_exec_name = string.Empty;
		public string log_adjustclass = string.Empty;

		public string log_postadjust_empno = string.Empty;
		public string log_postadjust_name = string.Empty;

		public string log_selected = string.Empty;

		#endregion

		public EngageJobTransfer_M()
		{
			//
			// TODO: Add constructor logic here
			//
		}
	}

	public class EngageJobTransfer_Treaty_M
	{
		#region Property

		public string srh_login_id = string.Empty;
		public string srh_orgcd = string.Empty;
		public string srh_registerdate1 = string.Empty;
		public string srh_registerdate2 = string.Empty;
		public string srh_keyword = string.Empty;
		public string srh_source_class = string.Empty;
		public string srh_degree = string.Empty;

		public string srh_handlempno = string.Empty;
		public string srh_promoempno = string.Empty;
		public string srh_keyinempno = string.Empty;
		public string srh_agency = string.Empty;

		public string log_seqsn = string.Empty;
		public string log_infoclass = string.Empty;
		public string log_exec_empno = string.Empty;
		public string log_exec_name = string.Empty;
		public string log_adjustclass = string.Empty;

		public string log_postadjust_empno = string.Empty;
		public string log_postadjust_name = string.Empty;

		public string log_selected = string.Empty;

		#endregion

		public EngageJobTransfer_Treaty_M()
		{
			//
			// TODO: Add constructor logic here
			//
		}
	}

}
