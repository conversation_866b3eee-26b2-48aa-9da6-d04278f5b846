﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TechCase_assignInspect : Treaty.common
{    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["tt_seno"] = "2604";
            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }
            databinding();
        }
    }
    protected void databinding()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "pre_inspect");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_AssignInspect.DataSource = dt;
                DDL_AssignInspect.DataValueField = "Value";
                DDL_AssignInspect.DataTextField = "Text";
                DDL_AssignInspect.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (!IsNatural_Number(DDL_AssignInspect.SelectedValue) || (DDL_AssignInspect.SelectedValue.Length == 0) || (DDL_AssignInspect.SelectedValue.Length > 7))
            Response.Redirect("../danger.aspx");

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mode", "Add_inspect");
            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_empno", oRCM.SQLInjectionReplaceAll(DDL_AssignInspect.SelectedValue));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

    }
}