﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_XFileModify.aspx.cs" Inherits="TreatyCase2_XFileModify" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            parent.$.fn.colorbox.close();
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>


</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 10px">
                <tr>
                    <td class="td_right">文件：</td>
                    <td>
                        <asp:Label ID="txt_doc" runat="server"></asp:Label></td>
                </tr>
                <tr>
                    <td class="td_right">說明：</td>
                    <td>
                        <asp:TextBox ID="txt_filetxt" runat="server" Width="500px" Height="64px" TextMode="MultiLine"></asp:TextBox></td>
                </tr>
                <tr>
                    <td colspan="2">
                        <%--                        <asp:SqlDataSource ID="SDS_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                        <asp:SqlDataSource ID="SDS_fileType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="更新" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>

            </table>
        </span>
    </form>
</body>
</html>
