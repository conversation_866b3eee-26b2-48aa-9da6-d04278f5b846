﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_cop.aspx.cs" Inherits="Treaty_webpage_TreatyCase2_cop" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>新增審查人</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("指定成功!");
            parent.$.fn.colorbox.close();
        }

    </script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <br />
            <br />
            <table style="margin-left: 15px">
                <tr>
                    <td class="td_right">新增協同法務：
                        <asp:DropDownList ID="DDL_cop" runat="server" DataTextField="empName" DataValueField="empNo" Height="20px" Width="145px"></asp:DropDownList>&nbsp;&nbsp;
                        <asp:Button ID="BT_Save" runat="server" Text="新增" class="genbtnS" OnClick="BT_Save_Click" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <span class="stripeMe">
                            <cc1:SmartGridView ID="SGV_cop" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_cop_RowCommand" OnRowDataBound="SGV_cop_RowDataBound">
                                <FooterStyle Font-Bold="True" ForeColor="Black" />
                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                <AlternatingRowStyle CssClass="TRowEven" />
                                <Columns>
                                    <asp:TemplateField HeaderText="功能">
                                        <HeaderStyle Width="40px" ForeColor="Black"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                        <ItemTemplate>
                                            <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Eval("empno")+ ";" +Eval("empName") %>'>刪除</asp:LinkButton>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="工號">
                                        <ItemTemplate>
                                            <asp:Literal ID="LB_company" runat="server" Text='<%# Server.HtmlEncode(Eval("empno").ToString()) %>'></asp:Literal>
                                        </ItemTemplate>
                                        <HeaderStyle Width="80px" />
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="empName" HeaderText="姓名">
                                        <HeaderStyle Width="100px"></HeaderStyle>
                                    </asp:BoundField>
                                    <asp:BoundField DataField="tel" HeaderText="電話">
                                        <HeaderStyle Width="100px"></HeaderStyle>
                                    </asp:BoundField>
                                </Columns>
                                <EmptyDataTemplate>
                                    <!--當找不到資料時則顯示「無資料」-->
                                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料，請新增!"></asp:Label>
                                </EmptyDataTemplate>
                                <FooterStyle BackColor="White" />
                                <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                            </cc1:SmartGridView>
                            <%--                <asp:SqlDataSource ID="SDS_cop" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                            <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
                            <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

                        </span>
                        <div style="display: none">
                            <asp:TextBox ID="TB_empNo" runat="server"></asp:TextBox>
                            <asp:TextBox ID="TB_empName" runat="server"></asp:TextBox>

                        </div>

                    </td>
                </tr>
            </table>

        </span>
    </form>
</body>
</html>
