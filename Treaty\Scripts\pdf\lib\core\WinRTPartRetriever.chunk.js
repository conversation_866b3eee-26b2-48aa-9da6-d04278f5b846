/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[18],{394:function(ia,y,e){e.r(y);var fa=e(1),x=e(215);ia=e(384);var ha=e(84);e=e(316);var ea={},da=function(e){function w(w,r){var h=e.call(this,w,r)||this;h.url=w;h.range=r;h.status=x.a.NOT_STARTED;return h}Object(fa.c)(w,e);w.prototype.start=function(e){var r=this;"undefined"===typeof ea[this.range.start]&&(ea[this.range.start]={Wr:function(h){var f=atob(h),n,w=f.length;h=new Uint8Array(w);for(n=0;n<w;++n)h[n]=f.charCodeAt(n);
f=h.length;n="";for(var x=0;x<f;)w=h.subarray(x,x+1024),x+=1024,n+=String.fromCharCode.apply(null,w);r.Wr(n,e)},FN:function(){r.status=x.a.ERROR;e({code:r.status})}},window.external.Rja(this.url),this.status=x.a.STARTED);r.vz()};return w}(ia.ByteRangeRequest);ia=function(e){function w(w,r,h,f){w=e.call(this,w,h,f)||this;w.xv=da;return w}Object(fa.c)(w,e);w.prototype.Ft=function(e,r){return e+"?"+r.start+"&"+(r.stop?r.stop:"")};return w}(ha.a);Object(e.a)(ia);Object(e.b)(ia);y["default"]=ia}}]);}).call(this || window)
