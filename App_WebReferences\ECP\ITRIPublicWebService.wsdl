<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="createFlow">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ApplicationID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ModuleID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ProcessPageID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sheetNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sheetnoparam" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="subject" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="userID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fillerID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fillerOrgID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ownerID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ownerOrgID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="submitOrgID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="importance" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="AgentSchema" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dataXML" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="flowParameter" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firstParam" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="addSignXML" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="localeString" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="debugPage" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="createFlowResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="createFlowResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="abortProcess">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="UserID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="executiveComment" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="abortProcessResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="abortProcessResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getViewSignOpinion">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="UserID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="processSerialNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="rootURL" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getViewSignOpinionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getViewSignOpinionResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getNewSheetNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="PA01" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="COM01" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="PA02" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="COM02" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getNewSheetNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getNewSheetNoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="completeWorkItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="userID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="workItemOID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="executiveResult" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="executiveComment" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="serialNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="addUserID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="addActName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="isFIREST_GET_FIRST_WIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FormID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="completeWorkItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="completeWorkItemResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="createFlowSoapIn">
    <wsdl:part name="parameters" element="tns:createFlow" />
  </wsdl:message>
  <wsdl:message name="createFlowSoapOut">
    <wsdl:part name="parameters" element="tns:createFlowResponse" />
  </wsdl:message>
  <wsdl:message name="abortProcessSoapIn">
    <wsdl:part name="parameters" element="tns:abortProcess" />
  </wsdl:message>
  <wsdl:message name="abortProcessSoapOut">
    <wsdl:part name="parameters" element="tns:abortProcessResponse" />
  </wsdl:message>
  <wsdl:message name="getViewSignOpinionSoapIn">
    <wsdl:part name="parameters" element="tns:getViewSignOpinion" />
  </wsdl:message>
  <wsdl:message name="getViewSignOpinionSoapOut">
    <wsdl:part name="parameters" element="tns:getViewSignOpinionResponse" />
  </wsdl:message>
  <wsdl:message name="getNewSheetNoSoapIn">
    <wsdl:part name="parameters" element="tns:getNewSheetNo" />
  </wsdl:message>
  <wsdl:message name="getNewSheetNoSoapOut">
    <wsdl:part name="parameters" element="tns:getNewSheetNoResponse" />
  </wsdl:message>
  <wsdl:message name="completeWorkItemSoapIn">
    <wsdl:part name="parameters" element="tns:completeWorkItem" />
  </wsdl:message>
  <wsdl:message name="completeWorkItemSoapOut">
    <wsdl:part name="parameters" element="tns:completeWorkItemResponse" />
  </wsdl:message>
  <wsdl:message name="createFlowHttpPostIn">
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
    <wsdl:part name="ApplicationID" type="s:string" />
    <wsdl:part name="ModuleID" type="s:string" />
    <wsdl:part name="ProcessPageID" type="s:string" />
    <wsdl:part name="sheetNo" type="s:string" />
    <wsdl:part name="sheetnoparam" type="s:string" />
    <wsdl:part name="subject" type="s:string" />
    <wsdl:part name="userID" type="s:string" />
    <wsdl:part name="fillerID" type="s:string" />
    <wsdl:part name="fillerOrgID" type="s:string" />
    <wsdl:part name="ownerID" type="s:string" />
    <wsdl:part name="ownerOrgID" type="s:string" />
    <wsdl:part name="submitOrgID" type="s:string" />
    <wsdl:part name="importance" type="s:string" />
    <wsdl:part name="AgentSchema" type="s:string" />
    <wsdl:part name="dataXML" type="s:string" />
    <wsdl:part name="flowParameter" type="s:string" />
    <wsdl:part name="firstParam" type="s:string" />
    <wsdl:part name="addSignXML" type="s:string" />
    <wsdl:part name="localeString" type="s:string" />
    <wsdl:part name="debugPage" type="s:string" />
  </wsdl:message>
  <wsdl:message name="createFlowHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="abortProcessHttpPostIn">
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
    <wsdl:part name="FormID" type="s:string" />
    <wsdl:part name="FormNo" type="s:string" />
    <wsdl:part name="UserID" type="s:string" />
    <wsdl:part name="executiveComment" type="s:string" />
  </wsdl:message>
  <wsdl:message name="abortProcessHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getViewSignOpinionHttpPostIn">
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
    <wsdl:part name="FormID" type="s:string" />
    <wsdl:part name="FormNo" type="s:string" />
    <wsdl:part name="UserID" type="s:string" />
    <wsdl:part name="processSerialNumber" type="s:string" />
    <wsdl:part name="rootURL" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getViewSignOpinionHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getNewSheetNoHttpPostIn">
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
    <wsdl:part name="FormID" type="s:string" />
    <wsdl:part name="PA01" type="s:string" />
    <wsdl:part name="COM01" type="s:string" />
    <wsdl:part name="PA02" type="s:string" />
    <wsdl:part name="COM02" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getNewSheetNoHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="completeWorkItemHttpPostIn">
    <wsdl:part name="userID" type="s:string" />
    <wsdl:part name="workItemOID" type="s:string" />
    <wsdl:part name="executiveResult" type="s:string" />
    <wsdl:part name="executiveComment" type="s:string" />
    <wsdl:part name="serialNumber" type="s:string" />
    <wsdl:part name="addUserID" type="s:string" />
    <wsdl:part name="addActName" type="s:string" />
    <wsdl:part name="isFIREST_GET_FIRST_WIN" type="s:string" />
    <wsdl:part name="FormID" type="s:string" />
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
  </wsdl:message>
  <wsdl:message name="completeWorkItemHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="ITRIPublicWebServiceSoap">
    <wsdl:operation name="createFlow">
      <wsdl:input message="tns:createFlowSoapIn" />
      <wsdl:output message="tns:createFlowSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="abortProcess">
      <wsdl:input message="tns:abortProcessSoapIn" />
      <wsdl:output message="tns:abortProcessSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getViewSignOpinion">
      <wsdl:input message="tns:getViewSignOpinionSoapIn" />
      <wsdl:output message="tns:getViewSignOpinionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getNewSheetNo">
      <wsdl:input message="tns:getNewSheetNoSoapIn" />
      <wsdl:output message="tns:getNewSheetNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="completeWorkItem">
      <wsdl:input message="tns:completeWorkItemSoapIn" />
      <wsdl:output message="tns:completeWorkItemSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="ITRIPublicWebServiceHttpPost">
    <wsdl:operation name="createFlow">
      <wsdl:input message="tns:createFlowHttpPostIn" />
      <wsdl:output message="tns:createFlowHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="abortProcess">
      <wsdl:input message="tns:abortProcessHttpPostIn" />
      <wsdl:output message="tns:abortProcessHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getViewSignOpinion">
      <wsdl:input message="tns:getViewSignOpinionHttpPostIn" />
      <wsdl:output message="tns:getViewSignOpinionHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getNewSheetNo">
      <wsdl:input message="tns:getNewSheetNoHttpPostIn" />
      <wsdl:output message="tns:getNewSheetNoHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="completeWorkItem">
      <wsdl:input message="tns:completeWorkItemHttpPostIn" />
      <wsdl:output message="tns:completeWorkItemHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ITRIPublicWebServiceSoap" type="tns:ITRIPublicWebServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="createFlow">
      <soap:operation soapAction="http://tempuri.org/createFlow" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="abortProcess">
      <soap:operation soapAction="http://tempuri.org/abortProcess" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getViewSignOpinion">
      <soap:operation soapAction="http://tempuri.org/getViewSignOpinion" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getNewSheetNo">
      <soap:operation soapAction="http://tempuri.org/getNewSheetNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="completeWorkItem">
      <soap:operation soapAction="http://tempuri.org/completeWorkItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ITRIPublicWebServiceSoap12" type="tns:ITRIPublicWebServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="createFlow">
      <soap12:operation soapAction="http://tempuri.org/createFlow" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="abortProcess">
      <soap12:operation soapAction="http://tempuri.org/abortProcess" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getViewSignOpinion">
      <soap12:operation soapAction="http://tempuri.org/getViewSignOpinion" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getNewSheetNo">
      <soap12:operation soapAction="http://tempuri.org/getNewSheetNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="completeWorkItem">
      <soap12:operation soapAction="http://tempuri.org/completeWorkItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ITRIPublicWebServiceHttpPost" type="tns:ITRIPublicWebServiceHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="createFlow">
      <http:operation location="/createFlow" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="abortProcess">
      <http:operation location="/abortProcess" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getViewSignOpinion">
      <http:operation location="/getViewSignOpinion" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getNewSheetNo">
      <http:operation location="/getNewSheetNo" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="completeWorkItem">
      <http:operation location="/completeWorkItem" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ITRIPublicWebService">
    <wsdl:port name="ITRIPublicWebServiceSoap" binding="tns:ITRIPublicWebServiceSoap">
      <soap:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/ITRIPublicWebService.asmx" />
    </wsdl:port>
    <wsdl:port name="ITRIPublicWebServiceSoap12" binding="tns:ITRIPublicWebServiceSoap12">
      <soap12:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/ITRIPublicWebService.asmx" />
    </wsdl:port>
    <wsdl:port name="ITRIPublicWebServiceHttpPost" binding="tns:ITRIPublicWebServiceHttpPost">
      <http:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/ITRIPublicWebService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>