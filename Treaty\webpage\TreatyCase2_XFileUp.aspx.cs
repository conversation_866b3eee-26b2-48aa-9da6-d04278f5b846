﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web.UI;

public partial class TreatyCase2_XFileUp : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        if (str == "")
            return true;
        else
        {
            System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
            return reg1.IsMatch(str);
        }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request.QueryString["sub_seno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["sub_seno"]) || (Request.QueryString["sub_seno"].Length > 8))
                    Response.Redirect("../danger.aspx");
                ViewState["sub_seno"] = Request.QueryString["sub_seno"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");
            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");

            if (Request.QueryString["FType"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["FType"]) || (Request.QueryString["FType"].Length == 0) || (Request.QueryString["FType"].Length > 3))
                    Response.Redirect("../danger.aspx");
                ViewState["FType"] = Request.QueryString["FType"].ToString();
            }
            else
                Response.Redirect("../danger.aspx");
            ViewState["contno"] = "";
            //SDS_log.SelectCommandType  = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_log.SelectCommand = " select tc2_year+tc2_orgcd+tc2_class+tc2_sn  from treaty_case2 where tc2_seno=" + ViewState["seno"].ToString();
            //System.Data.DataView dv_contnoToseno = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select tc2_year+tc2_orgcd+tc2_class+tc2_sn  from treaty_case2 where tc2_seno=@seno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_contnoToseno = dt.DefaultView;
            if (dv_contnoToseno.Count >= 1)
            {
                ViewState["contno"] = dv_contnoToseno[0][0].ToString();
            }
            //SDS_file.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'19' ";
            //SDS_file.DataBind();

        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_TreatyCase2_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (txt_filetxt.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        string path = string.Format("{0}\\{1}\\", FilePathString, ViewState["contno"].ToString().Substring(0, 4));
        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);
        if (Request.ServerVariables["HTTP_VIA"] != null)
        {
            ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
        }
        if (FU_up.PostedFile.ContentLength > 0)
        {
            //if (!Directory.Exists(path.Replace("/", "").Replace("..", "")))
            //{
            //    Directory.CreateDirectory(path.Replace("/", "").Replace("..", ""));
            //}
            oRCM.GetValidPathPart(FilePathString, ViewState["contno"].ToString().Substring(0, 4));
            string str_FileName = "\\" + ViewState["FType"].ToString() + "_" + ViewState["seno"].ToString() + "_" + ViewState["sub_seno"].ToString() + "_" + strPreRandom +
                                    Path.GetFileNameWithoutExtension(FU_up.FileName).Replace("/", "").Replace("....", "").Replace("...", "").Replace("..", "") +
                                    Path.GetExtension(FU_up.FileName);

            FU_up.SaveAs(path.Replace("/", "").Replace("..", "") + str_FileName);
            //System.Web.UI.WebControls.SqlDataSource SqlDataSource1 = new System.Web.UI.WebControls.SqlDataSource();
            //SqlDataSource1.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SqlDataSource1.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SqlDataSource1.InsertCommand = "esp_TreatyCase2_XFile_modify";
            //SqlDataSource1.InsertParameters.Add("seno", ViewState["seno"].ToString());
            //SqlDataSource1.InsertParameters.Add("sub_seno", ViewState["sub_seno"].ToString());
            //SqlDataSource1.InsertParameters.Add("fd_name", FU_up.FileName);
            //SqlDataSource1.InsertParameters.Add("filetxt", txt_filetxt.Text);
            //SqlDataSource1.InsertParameters.Add("file_url", path + str_FileName);
            //SqlDataSource1.InsertParameters.Add("empno",ssoUser.empNo);
            //SqlDataSource1.InsertParameters.Add("fid","");
            //SqlDataSource1.InsertParameters.Add("FType", ViewState["FType"].ToString());
            //SqlDataSource1.InsertParameters.Add("mode","insert");
            //SqlDataSource1.Insert();

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_XFile_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@sub_seno", oRCM.SQLInjectionReplaceAll(ViewState["sub_seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fd_name", oRCM.SQLInjectionReplaceAll(FU_up.FileName));
                sqlCmd.Parameters.AddWithValue("@filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                sqlCmd.Parameters.AddWithValue("@file_url", oRCM.SQLInjectionReplaceAll(path + str_FileName));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@fid", "");
                sqlCmd.Parameters.AddWithValue("@FType", oRCM.SQLInjectionReplaceAll(ViewState["FType"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "insert");


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "附件上傳", "", "", "FU_up.FileName");
            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
    }
}