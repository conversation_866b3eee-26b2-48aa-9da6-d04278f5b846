﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;
using System.Xml;
using System.Configuration;
using System.Text;

namespace treaty_myEzflow
{
    /// <summary>
    /// Summary description for myEzflow
    /// </summary>
    public class treaty_myEzflow
    {
        #region 私有變數
        private string _conn = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        private string _errorMessage;
        private string _returnMessage;

        private string _empno;
        private string _empname;
        private string _seno;
        private string _ecp_guid;
        private string _formtype;
        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 執行 SP 後，回傳的訊息
        /// </summary>
        public string ReturnMessage
        {
            get { return _returnMessage; }
            set { _returnMessage = value; }
        }

        /// <summary>
        /// 登入人員
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        public string EmpName
        {
            get { return _empname; }
            set { _empname = value; }
        }

        public string Seno
        {
            get { return _seno; }
            set { _seno = value; }
        }

        /// <summary>
        /// 表單流水號
        /// </summary>
        public string Ecp_guid
        {
            get { return _ecp_guid; }
            set { _ecp_guid = value; }
        }
        /// <summary>
        /// 表單類別
        /// </summary>
        public string FormType
        {
            get
            {
                if (string.IsNullOrEmpty(_formtype))
                {
                    _formtype = "TREATY01";
                }
                return _formtype;
            }
            set { _formtype = value; }
        }

        //簽核
        //public string GUID = "";
        public int Seq = 0;
        public string ActRoleName = "";
        public string RecUserID = "";
        public string RecUserName = "";
        public string SignType = "1";
        public string SignSigle = "Y";
        public string SignClass = "";
        public string IS_LOCK = "";

        public string NoticeEmpno = "";
        public string EcpMemo = "";
        #endregion
        public treaty_myEzflow()
        {
            //
            // TODO: Add constructor logic here
            //
        }
        public static Int32 ToInt32(string str_Integer)
        {
            return Convert.ToInt32(str_Integer);
        }
        private static void setPara(SqlParameter sqlPara, DataRow dr)
        {
            string strLocation = "Data::SetPara";
            sqlPara.ParameterName = dr["COLUMN_NAME"].ToString();
            switch (dr["TYPE_NAME"].ToString())
            {
                case "bigint":
                    sqlPara.SqlDbType = SqlDbType.BigInt;
                    break;
                case "binary":
                    sqlPara.SqlDbType = SqlDbType.Binary;
                    break;
                case "char":
                    sqlPara.SqlDbType = SqlDbType.Char;
                    sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                    break;
                case "datetime":
                    sqlPara.SqlDbType = SqlDbType.DateTime;
                    break;
                case "decimal":
                    sqlPara.SqlDbType = SqlDbType.Decimal;
                    break;
                case "float":
                    sqlPara.SqlDbType = SqlDbType.Float;
                    break;
                case "int":
                    sqlPara.SqlDbType = SqlDbType.Int;
                    break;
                case "nchar":
                    sqlPara.SqlDbType = SqlDbType.NChar;
                    sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                    break;
                case "ntext":
                    sqlPara.SqlDbType = SqlDbType.NText;
                    break;
                case "nvarchar":
                    sqlPara.SqlDbType = SqlDbType.NVarChar;
                    sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                    break;
                case "smallint":
                    sqlPara.SqlDbType = SqlDbType.SmallInt;
                    break;
                case "text":
                    sqlPara.SqlDbType = SqlDbType.Text;
                    break;
                case "tinyint":
                    sqlPara.SqlDbType = SqlDbType.TinyInt;
                    break;
                case "varbinary":
                    sqlPara.SqlDbType = SqlDbType.VarBinary;
                    break;
                case "varchar":
                    sqlPara.SqlDbType = SqlDbType.VarChar;
                    sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                    break;
                default:
                    Exception exErr = new Exception("系統未設定此型別 " + dr["info_name"] + ", 請通知系統人員");
                    exErr.Source = strLocation;
                    throw exErr;
            }

            if (ToInt32(dr["COLUMN_TYPE"].ToString()) == 2)
                sqlPara.Direction = ParameterDirection.Output;

        }
        public DataView runSp(string pSPName, SqlParameter[] pParas)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(_conn);
            oCmd.Connection.Open();
            oCmd.CommandType = CommandType.StoredProcedure;
            oCmd.CommandText = pSPName;
            // Add Parameter into SqlCommand.SqlParameter
            for (int i = 0; i < pParas.Length; i++)
            {
                oCmd.Parameters.Add(pParas[i]);
            }

            // execute SQL and return a dataview
            SqlDataAdapter cmdSQL = new SqlDataAdapter(oCmd);
            DataSet myds = new DataSet();
            cmdSQL.Fill(myds, "myTable");

            // Release Resource 
            if (pParas != null)
                oCmd.Parameters.Clear();

            oCmd = null;

            return myds.Tables["myTable"].DefaultView;
        }

        public int Execute(SqlCommand oCmd, CommandType oType)
        {
            int rows = 0;
            oCmd.Connection = new SqlConnection(_conn);
            oCmd.Connection.Open();
            oCmd.CommandType = oType;
            SqlTransaction oTrans = oCmd.Connection.BeginTransaction();
            oCmd.Transaction = oTrans;
            try
            {
                rows = oCmd.ExecuteNonQuery();
                oTrans.Commit();

            }
            catch (Exception e)
            {
                oTrans.Rollback();
                throw new Exception(e.Message);
            }
            finally
            {
                oCmd.Connection.Close();
            }

            return rows;
        }
        public SqlParameter[] getParameters(string pSPName, object[] pParas)
        {
            string strLocation = "Data::GetParameters";
            SqlParameter[] Paras = new SqlParameter[pParas.Length];
            DataView dvMain;

            SqlParameter[] myParas = new SqlParameter[1];
            myParas[0] = new SqlParameter("@procedure_name", SqlDbType.NVarChar, 128);
            myParas[0].Value = pSPName;

            dvMain = this.runSp("sp_sproc_columns", myParas);

            //判斷parameters的參數個數是否正確
            if ((dvMain.Count - 1) != pParas.Length)
            {
                Exception exErr = new Exception("參數個數不一致!!");
                exErr.Source = strLocation;
                throw exErr;
            }

            for (int i = 0; i < pParas.Length; i++)
            {
                Paras[i] = new SqlParameter();
                setPara(Paras[i], dvMain.Table.Rows[i + 1]);
                Paras[i].Value = pParas[i];
            }

            //Release Resource
            dvMain = null;
            myParas = null;

            return Paras;
        }
        public DataTable runSpTable(SqlCommand oCmd, CommandType oType, string pSPName, Object[] pParas)
        {
            SqlParameter[] paras;
            _conn = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnString"].ConnectionString;
            // set properties of SqlCommand
            oCmd.Connection = new SqlConnection(_conn);
            oCmd.Connection.Open();
            oCmd.CommandText = pSPName;
            oCmd.CommandType = CommandType.StoredProcedure;

            //to get Parameters
            paras = getParameters(pSPName, pParas);

            //若參數個數不一致, 則丟出Exception
            if (paras.Length != pParas.Length)
            {
                Exception exErr = new Exception("參數個數不一致!!");
                throw exErr;
            }

            // Add Parameter into SqlCommand.SqlParameter		
            for (int i = 0; i < paras.Length; i++)
            {
                oCmd.Parameters.Add(paras[i]);
            }

            // execute SQL and return a dataview
            SqlDataAdapter cmdSQL = new SqlDataAdapter(oCmd);
            DataSet myds = new DataSet();
            cmdSQL.Fill(myds, "myTable");

            // Release Resource 
            if (paras != null)
                oCmd.Parameters.Clear();

            oCmd = null;

            return myds.Tables["myTable"];
        }

        #region 取得簽核預覽流程
        /// <summary>
        /// 取得簽核預覽流程
        /// </summary>
        /// <returns></returns>
        public DataTable Get_preflow()
        {
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                oCmd.CommandText = @"esp_ECP_簽核名單_預設";
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.Parameters.AddWithValue("@tr_seno", Seno);
                oCmd.Parameters.AddWithValue("@empno", EmpNo);
                oCmd.Parameters.AddWithValue("@signGUID", Ecp_guid);
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(oCmd);

                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    _errorMessage = ex.Message;
                }
                return dt;
            }
        }
        #endregion

        #region 新增流程主檔
        /// <summary>
        /// 新增流程主檔
        /// </summary>
        /// <returns></returns>
        public bool Insert_ecp_main()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"esp_ECP_簽核名單_main";

            oCmd.Parameters.AddWithValue("@ecp_guid", Ecp_guid);
            oCmd.Parameters.AddWithValue("@formtype", FormType);
            oCmd.Parameters.AddWithValue("@tr_seno", Seno);
            oCmd.Parameters.AddWithValue("@rtn_flag", "");

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

        #region 新增預設簽核流程
        /// <summary>
        /// 新增預設簽核流程
        /// </summary>
        /// <returns></returns>
        public bool Insert_preflow()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"esp_ECP_簽核名單_更新";

            oCmd.Parameters.AddWithValue("@tr_seno", _seno);
            oCmd.Parameters.AddWithValue("@signGUID", Ecp_guid);
            oCmd.Parameters.AddWithValue("@電子表單", "TREATY01");
            oCmd.Parameters.AddWithValue("@順序", Seq);
            oCmd.Parameters.AddWithValue("@角色", ActRoleName);
            oCmd.Parameters.AddWithValue("@簽核人工號", RecUserID);
            oCmd.Parameters.AddWithValue("@簽核人", RecUserName);
            oCmd.Parameters.AddWithValue("@送簽人工號", EmpNo);
            oCmd.Parameters.AddWithValue("@送簽人", EmpName);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

        #region 更新-送簽人
        /// <summary>
        /// 更新-送簽人
        /// </summary>
        /// <returns></returns>
        public bool Update_SheetNo(string SheetNo)
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"esp_ECP_簽核名單_創單成功";
            oCmd.Parameters.AddWithValue("@ecp_main002", SheetNo);
            oCmd.Parameters.AddWithValue("@ecp_guid", Ecp_guid);
            oCmd.Parameters.AddWithValue("@empno", EmpNo);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

        #region 送簽，呼叫創單 Web Service.

        public bool Send_CreateSheet()
        {
            bool success = false;
            try
            {
                //string pGUID = Guid.NewGuid().ToString();
                TREATY.TREATY01 prj = new TREATY.TREATY01(); //第一個「TREATY01」是Web參考名稱，加入Web參考時可自訂
                string sysid = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.sysid"];
                string secureid = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.secureid"];
                prj.Url = System.Web.Configuration.WebConfigurationManager.AppSettings["TREATY.TREATY01"];   //設定webservice url
                string strSignMsg = "";

                strSignMsg = prj.TREATYCreate(Ecp_guid, sysid, secureid);  //後二參數依序為「核覆單」中ws_sysid, ws_secureid
                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(strSignMsg);
                strSignMsg = xmlDoc.SelectSingleNode("//Result").InnerText;

                if ("Y".Equals(strSignMsg))
                {
                    string SheetNo = xmlDoc.SelectSingleNode("//SheetNo").InnerText; //表單單號
                    success = Update_SheetNo(SheetNo);
                    _returnMessage = "議約申請已送出簽核";
                }
                else
                {
                    _returnMessage = "WS創單失敗!";
                    success = false;
                }
                return success;
            }
            catch (Exception ex)
            {
                _returnMessage = ex.Message;
                return false;
            }
        }
        #endregion

        public bool Do_Fail_Process()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"esp_ECP_簽核名單_失敗處理";

            oCmd.Parameters.AddWithValue("@tr_seno", Seno);
            oCmd.Parameters.AddWithValue("@ecp_guid", Ecp_guid);
            oCmd.Parameters.AddWithValue("@formtype", FormType);
            oCmd.Parameters.AddWithValue("@empno", EmpNo);
            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }

        #region 簽核失敗-Mail通知
        public bool Send_Fail_Mail()
        {
            bool success = false;

            SqlCommand oCmd = new SqlCommand();

            oCmd.CommandText = @"esp_TreatyApply_ecpFail_Mail";

            oCmd.Parameters.AddWithValue("@seno", Seno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

    }
}