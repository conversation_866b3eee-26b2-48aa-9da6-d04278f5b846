﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;
using treaty_myEzflow;

public partial class TreatyApply_ECP : Treaty.common
{
    public string Ecp_guid
    {
        set { ViewState["Ecp_guid"] = value; }
        get
        {
            if (ViewState["Ecp_guid"] == null)
            {
                ViewState["Ecp_guid"] = Guid.NewGuid().ToString();
            }

            return ViewState["Ecp_guid"].ToString();
        }
    }
    public string Seno
    {
        set { ViewState["seno"] = value; }
        get
        {
            if (ViewState["seno"] == null)
            {
                if (!IsNumber(Request.QueryString["seno"]))
                    Response.Redirect("../error.aspx");
                if (Request.QueryString["seno"] == "")
                    Response.Redirect("../error.aspx");
                //return (Request.QueryString["seno"] == null) ? "0" : Request.QueryString["seno"];
                ViewState["seno"] = oRCM.SQLInjectionReplaceAll(Request.QueryString["seno"].ToString());
            }

            //ViewState["seno"] = "36734";
            return ViewState["seno"].ToString();
        }
    }

    /// <summary>
    /// 表單類別
    /// </summary>
    public string FormType
    {
        set { ViewState["formtype"] = value; }
        get
        {
            if (Request.QueryString["formtype"] == null)
            {
                ViewState["formtype"] = "TREATY01";
            }
            else
            {
                if (ViewState["formtype"] == null)
                {
                    string amount = Request.QueryString["formtype"].ToString();
                    if (Regex.IsMatch(amount, "^[a-zA-Z0-9]*$") == false)
                        Response.Redirect("../error.aspx");
                    ViewState["formtype"] = Request.QueryString["formtype"].ToUpper();
                }
            }
            return ViewState["formtype"].ToString();
        }
    }
    /// <summary>
    /// 流程
    /// </summary>
    DataTable SignFlow
    {
        get { return (DataTable)ViewState["_SignFlow"]; }
        set { ViewState["_SignFlow"] = value; }
    }

    /// <summary>
    /// @ECP新增關卡
    /// </summary>
    public bool IsECPNewRight
    {
        get { return ViewState["_IsECPNewRight"] == null ? false : (bool)ViewState["_IsECPNewRight"]; }
        set { ViewState["_IsECPNewRight"] = value; }
    }
    int nRowCount = 0;

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            this.SignFlow = InitDataTable();
            BindSignFlow();
        }
    }

    protected void gvList_signflow_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        DataTable dt = this.SignFlow;
        LinkButton btn = (LinkButton)e.CommandSource;

        int order = int.Parse(btn.Attributes["Seq"]);
        string arguments = e.CommandArgument.ToString();
        string[] args = arguments.Split(';');
        switch (e.CommandName)
        {

            case "CMD_AddRow_First":
                SetTmpTable();
                AddEmptyRow(0);
                break;

            case "CMD_AddRow":
                SetTmpTable();
                AddEmptyRow(order);

                break;
            case "CMD_DelRow":
                if (!args[1].ToString().Equals("送簽人直屬主管"))
                {
                    //已是最後一筆時，必須新增空白列
                    if (this.gvList_signflow.Rows.Count == 1)
                    {
                        AddEmptyRow(0);
                    }
                    foreach (DataRow row in dt.Rows)
                    {
                        //if (row["rowsn"].ToString() == e.CommandArgument.ToString())
                        if (row["rowsn"].ToString() == args[0].ToString())
                        {
                            if (row["rowtype"].ToString() == "1")
                            {
                                //屬於後來新增的就直接砍掉
                                dt.Rows.Remove(row);
                                break;
                            }
                            else
                            {
                                row["rowtype"] = "4";
                            }
                        }
                    }
                    SetTmpTable();
                    foreach (DataRow row in dt.Rows)
                    {
                        //刪除列，將此順序後的資料，每個都-1
                        if (int.Parse(row["Seq"].ToString()) > order)
                        {
                            row["Seq"] = int.Parse(row["Seq"].ToString()) - 1;
                            //由「資料庫載入」更新為「異動」
                            if (row["rowtype"].ToString() == "2")
                            {
                                row["rowtype"] = "3";
                            }
                        }
                    }
                }
                else
                {
                    string script = "<script language='javascript'>window.alert('簽辦順序為 送簽人直屬主管 不可刪除!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "送出", script, false);
                }
                break;
        }
        DataView dv = dt.DefaultView;
        dv.RowFilter = "rowtype <> 4";
        dv.Sort = "Seq";
        nRowCount = dv.Count;
        this.gvList_signflow.DataSource = dv;
        this.gvList_signflow.DataBind();
    }
    protected void gvList_signflow_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowIndex < 0)
            return;

        //刪除
        LinkButton lbtnDel = (LinkButton)e.Row.FindControl("lbtnDel");
        if (DataBinder.Eval(e.Row.DataItem, "IS_LOCK").ToString() == "Y")
        {
            lbtnDel.Enabled = false;
            lbtnDel.ForeColor = System.Drawing.Color.Gray;
        }
        else
        {
            lbtnDel.OnClientClick = "return confirm('確認刪除?');";
        }

        //簽核人員
        TextBox txt_com_cname = (TextBox)e.Row.FindControl("txt_com_cname");
        txt_com_cname.Attributes["readonly"] = "readonly";

        TextBox txt_com_empno = (TextBox)e.Row.FindControl("txt_com_empno");
        txt_com_empno.Attributes["readonly"] = "readonly";
        string rowtype = DataBinder.Eval(e.Row.DataItem, "rowtype").ToString();
        //使用 pr_engage_flow 控制可挑選權限@ECP新增關卡
        if ((rowtype.Equals("2") || rowtype.Equals("3")) && !this.IsECPNewRight)
        {
            Panel pnl_Empno = (Panel)e.Row.FindControl("pnl_Empno");
            pnl_Empno.Visible = false;

            Label lbl_Empno1 = (Label)e.Row.FindControl("lbl_Empno1");
            lbl_Empno1.Text = string.Format("{0}({1})", DataBinder.Eval(e.Row.DataItem, "RecUserName"), DataBinder.Eval(e.Row.DataItem, "RecUserID"));
            lbl_Empno1.Visible = true;
        }
    }

    protected void btnSign_Click(object sender, EventArgs e)
    {
        //簽核人員
        #region 欄位檢查
        string msg = string.Empty;
        string js = @"$('#_id_').validationEngine('showPrompt', '_msg_','','bottomLeft',true);$('#_id_').click(function () { $('#_id_').validationEngine('hide'); });";
        string str_簽核順序_Old = "0", str_簽核順序_New = "0", str_檢查 = "", str_簽核順序_max = "0";
        foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
        {
            TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
            TextBox txt_com_empno = (TextBox)gvrow.FindControl("txt_com_empno");
            if (txt_com_cname != null && txt_com_empno != null && (string.IsNullOrWhiteSpace(txt_com_cname.Text) || string.IsNullOrWhiteSpace(txt_com_empno.Text)))
            {
                msg += js.Replace("_id_", txt_com_cname.ClientID).Replace("_msg_", "簽核人員 欄位尚未填寫");
            }
            if (TB_pass_check.Text != "@880583@")
            {
                if (txt_com_empno != null && txt_com_empno.Text != "")
                {
                    DataTable dt = new DataTable();
                    #region --- query ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;

                        sqlCmd.CommandText = @"esp_ECP_簽核名單_check";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(txt_com_empno.Text));
                        sqlCmd.Parameters.AddWithValue("mode", oRCM.SQLInjectionReplaceAll("check_order"));

                        try
                        {
                            sqlConn.Open();

                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dt = new DataTable();
                            sqlDA.Fill(dt);

                        }
                        catch (Exception ex)
                        {

                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }
                    #endregion
                    DataView drv_簽核順序 = dt.DefaultView;
                    if (drv_簽核順序.Count >= 1)
                    {
                        str_簽核順序_New = drv_簽核順序[0][0].ToString();
                    }

                    if (Int32.Parse(str_簽核順序_New) >= Int32.Parse(str_簽核順序_Old))
                        str_簽核順序_Old = str_簽核順序_New;

                    if (Int32.Parse(str_簽核順序_max) <= Int32.Parse(str_簽核順序_Old))
                        str_簽核順序_max = str_簽核順序_Old;
                      
                }
            }
        }
        if (TB_pass_check.Text != "@880583@")
        {
            if(ViewState["contno"].ToString().Substring(4,2) !="56")
            {
                if (Int16.Parse(str_簽核順序_New) < 7 || (str_簽核順序_max != str_簽核順序_New))
                {
                    msg += js.Replace("_id_", gvList_signflow.ClientID).Replace("_msg_", "最後一關需為一級主管(組長)以上，且為會簽人員最大者");
                    str_檢查 = "X";
                }
            }
            else
            {
                if (  (str_簽核順序_max != str_簽核順序_New))
                {
                    msg += js.Replace("_id_", gvList_signflow.ClientID).Replace("_msg_", "最後一關為會簽人員最大者");
                    str_檢查 = "X";
                }
            }
        }
        if (msg != string.Empty)
        {
            ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", string.Format(@"$(document).ready(function () {{{0}}});", msg), true);
            return;
        }


        #endregion

        //刷新GUID
        Ecp_guid = Guid.NewGuid().ToString();
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        try
        {
            treaty_myEzflow.treaty_myEzflow dal_main = new treaty_myEzflow.treaty_myEzflow();
            dal_main.Ecp_guid = Ecp_guid;
            dal_main.Seno = Seno;
            dal_main.Insert_ecp_main();
        }
        catch
        {
            Alert("未預期例外，儲存發生錯誤(新增流程主檔)");
            return;
        }
        SetTmpTable();
        DataView dv = this.SignFlow.DefaultView;
        dv.Sort = "Seq";

        bool failed = false;
        //迴圈更新流程
        foreach (DataRowView row in dv)
        {
            treaty_myEzflow.treaty_myEzflow dal_ins = new treaty_myEzflow.treaty_myEzflow();
            dal_ins.Seno = Seno;
            dal_ins.EmpNo = ssoUser.empNo;
            dal_ins.EmpName = ssoUser.empName;
            dal_ins.Ecp_guid = Ecp_guid;
            dal_ins.Seq = int.Parse(row["Seq"].ToString());
            dal_ins.ActRoleName = row["ActRoleName"].ToString();
            dal_ins.RecUserID = row["RecUserID"].ToString();
            dal_ins.RecUserName = row["RecUserName"].ToString();
            dal_ins.SignType = row["SignType"].ToString();
            dal_ins.SignSigle = row["SignSigle"].ToString();
            dal_ins.SignClass = row["SignClass"].ToString();
            dal_ins.IS_LOCK = row["IS_LOCK"].ToString();

            bool success = false;
            if (row["rowtype"].ToString() != "4")
                success = dal_ins.Insert_preflow();
            else
            {
                success = true;
            }

            if (!success)
            {
                failed = true;
                break;
            }
        }

        if (failed)
        {
            Alert("未預期例外，儲存發生錯誤(新增流程)");
        }
        else
        {
            treaty_myEzflow.treaty_myEzflow dal = new treaty_myEzflow.treaty_myEzflow();

            dal.Ecp_guid = Ecp_guid;
            dal.EmpNo = ssoUser.empNo;
            dal.EmpName = ssoUser.empName;

            //送簽-電子表單
            bool success = dal.Send_CreateSheet();
            if (success)
            {
                string script = string.Format(@"  alert('{0}'); window.opener=null;window.close();  parent.$('#h_ECP_success').val('Y');   parent.$.colorbox.close();", dal.ReturnMessage);

                ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
            }
            else
            {
                treaty_myEzflow.treaty_myEzflow dal_fail = new treaty_myEzflow.treaty_myEzflow();
                dal_fail.Seno = Seno;
                dal_fail.Ecp_guid = Ecp_guid;
                dal_fail.FormType = "TREATY01";
                dal_fail.Do_Fail_Process();
                dal_fail.Send_Fail_Mail();
                Alert("送出簽核失敗,請重新發送!");
            }
        }
    }

    protected void btnAddFirst_Click(object sender, EventArgs e)
    {
        SetTmpTable();
        AddEmptyRow(0);

        DataTable dt = this.SignFlow;
        DataView dv = dt.DefaultView;
        dv.RowFilter = "rowtype <> 4";
        dv.Sort = "Seq";
        nRowCount = dv.Count;
        this.gvList_signflow.DataSource = dv;
        this.gvList_signflow.DataBind();

    }

    protected void btnReGen_Click(object sender, EventArgs e)
    {

        this.SignFlow = InitDataTable();
        BindSignFlow();
    }

    public string GetSignClassName(object oValue)
    {
        if (oValue.ToString().Equals("0"))
            return "簽核";
        else if (oValue.ToString().Equals("1"))
            return "會簽";
        else
            return "";
    }

    private void AddEmptyRow(int order)
    {
        DataTable dt = this.SignFlow;

        //插入列，將此順序後的資料，每個都+1
        foreach (DataRow row in dt.Rows)
        {
            if (int.Parse(row["Seq"].ToString()) > order && row["rowtype"].ToString() != "4")
            {
                row["Seq"] = int.Parse(row["Seq"].ToString()) + 1;

                //由「資料庫載入」更新為「異動」
                if (row["rowtype"].ToString() == "2")
                {
                    row["rowtype"] = "3";
                }
            }
        }

        DataRow dr = dt.NewRow();
        dr["rowtype"] = "1";//1:新增        
        dr["Seq"] = order + 1;
        dr["ActRoleName"] = "被加簽人";
        dr["RecUserID"] = string.Empty;
        dr["RecUserName"] = string.Empty;
        dr["SignType"] = "1";   //依序簽核
        dr["SignSigle"] = "Y";  //單一簽核
                                //dr["SignClass"] = (this.FormType.Equals("EG01") ? "1" : "0");	//0:簽核, 1:會簽
        dr["SignClass"] = "0";  //0:簽核, 1:會簽
        dr["IS_LOCK"] = "N";
        dr["NoticeEmpno"] = string.Empty;
        dr["NoticeName"] = string.Empty;

        dt.Rows.Add(dr);

        SignFlow = dt;
    }


    private void SetTmpTable(string rowsn, string oldrowtype, string newrowtype)
    {
        foreach (DataRow dr in this.SignFlow.Rows)
        {
            if (dr["rowsn"].ToString() == rowsn && dr["rowtype"].ToString() == oldrowtype)
            {
                dr["rowtype"] = newrowtype;
                break;
            }
        }
    }

    private DataTable InitDataTable()
    {
        DataTable dt = new DataTable();
        DataColumn identity = new DataColumn("rowsn", typeof(int));
        identity.AutoIncrement = true;
        identity.AutoIncrementSeed = 1;
        identity.AutoIncrementStep = 1;
        dt.Columns.Add(identity);
        dt.Columns.Add("rowtype");//1:新增;2:資料庫載入;3:異動;4:刪除;        
        dt.Columns.Add("Seq", typeof(int));
        dt.Columns.Add("ActRoleName");
        dt.Columns.Add("RecUserID");
        dt.Columns.Add("RecUserName");
        dt.Columns.Add("SignType");
        dt.Columns.Add("SignSigle");
        dt.Columns.Add("SignClass");
        dt.Columns.Add("IS_LOCK");
        dt.Columns.Add("NoticeEmpno");
        dt.Columns.Add("NoticeName");
        return dt;
    }

    private void SetTmpTable()
    {
        int nRowCount = this.gvList_signflow.Rows.Count;
        foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
        {
            HiddenField gv_hf_rowsn = (HiddenField)gvrow.FindControl("gv_hf_rowsn");
            foreach (DataRow dr in this.SignFlow.Rows)
            {
                TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
                TextBox txt_com_empno = (TextBox)gvrow.FindControl("txt_com_empno");
                HiddenField hf_SignClass = (HiddenField)gvrow.FindControl("hf_SignClass");
                if (gv_hf_rowsn.Value == dr["rowsn"].ToString())
                {
                    dr["RecUserName"] = txt_com_cname.Text.Trim();
                    dr["RecUserID"] = txt_com_empno.Text.Trim();
                    dr["SignClass"] = hf_SignClass.Value;
                    break;
                }
            }
        }
    }

    private void BindSignFlow()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        this.SignFlow.Clear();
        treaty_myEzflow.treaty_myEzflow dal = new treaty_myEzflow.treaty_myEzflow();
        dal.EmpNo = ssoUser.empNo;
        dal.Seno = Seno;
        dal.Ecp_guid = Ecp_guid;
        DataTable dt = dal.Get_preflow();
        if (dt.Rows[0][0].ToString().Trim() != "0")
        {
            ViewState["contno"] = dt.Rows[0]["contno"].ToString().Trim();
            foreach (DataRow row in dt.Rows)
            {
                DataRow dr = this.SignFlow.NewRow();
                dr["rowtype"] = "2";//2:資料庫載入
                dr["Seq"] = row["Seq"].ToString();
                dr["ActRoleName"] = row["ActRoleName"].ToString();
                dr["RecUserID"] = row["RecUserID"].ToString();
                dr["RecUserName"] = row["RecUserName"].ToString();
                dr["SignType"] = row["SignType"].ToString();
                dr["SignSigle"] = row["SignSigle"].ToString();
                dr["SignClass"] = row["SignClass"].ToString();
                dr["IS_LOCK"] = row["IS_LOCK"].ToString();
                ViewState["聯絡資訊"]=row["CC"].ToString();
                this.SignFlow.Rows.Add(dr);
            }

            DataView dv = this.SignFlow.DefaultView;
            dv.Sort = "Seq";
            nRowCount = dv.Count;
            this.gvList_signflow.DataSource = dv;
            this.gvList_signflow.DataBind();
            foreach (GridViewRow gvrow in this.gvList_signflow.Rows)
            {
                TextBox txt_com_cname = (TextBox)gvrow.FindControl("txt_com_cname");
                ViewState["最後簽核人"] = txt_com_cname.Text;
            }
            LT_業管人員.Text = Server.HtmlEncode(ViewState["聯絡資訊"].ToString());

        }
        else
        {
            string script = string.Format(@"
<script language='javascript'>
alert('{0}');
parent.$.colorbox.close();
</script>", Server.HtmlEncode(dt.Rows[0]["msg"].ToString().Trim()));
            ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "送出", script, false);
        }

    }

    /// <summary>
    /// 顯示訊息
    /// </summary>
    /// <param name="msg"></param>
    public void Alert(string msg)//顯示訊息
    {
        ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");", msg), true);
    }

    /// <summary>
    /// 顯示訊息完後關閉 Colorbox 視窗
    /// </summary>
    /// <param name="msg">輸出的訊息</param>
    public void Alert_CloseColorbox(string msg)//顯示訊息完後關閉視窗
    {
        ScriptManager.RegisterStartupScript(this.Page, GetType(), msg, string.Format("alert(\"{0}\");parent.$.colorbox.close();", msg), true);

    }
}