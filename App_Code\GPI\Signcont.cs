﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Signcont 的摘要描述
    /// </summary>
    public class Signcont : GPI.mySQLHelper
    {
        #region  私有變數
        private string _errorMessage;

        private int _seqsn;
        private string _main_no;
        private string _mailtype;
        private int _main_amt;

        //基本資料

        private string btn;

        private string main_type;
        private string main_enddate;
        private string main_receive;
        private string main_award;

        private string main_sdate;
        private string main_edate;


        private string main_no;

        //成本定價
        private int cost_suggestfee1;
        private int cost_suggestfee2;
        private int cost_basefee1;
        private int cost_basefee2;
        private int cost_promincome;
        private int cost_techincome;

        //投標檢核特殊案件
        private string no53;

        //更新產服人員簽辦意見

        private string signUserID;
        private string signOpinionTxt;




        //追蹤檔
        private int trac_ver;
        private string trac_bidmark;
        private string trac_biddate;
        private string trac_nobidreason;
        private string trac_contend;
        private string trac_contender;
        private string trac_winbid;
        private int trac_bidamt;
        private string trac_finaldate;
        private int trac_income;
        private int trac_payamt;
        private string trac_bidoff;
        private string trac_nowinreason;
        private string trac_bidoffreason;
        private string trac_memo;
        private string trac_noticedate;
        private string trac_finalenddate;
        private string trac_againreason;
        private string trac_reprintdate;
        private string trac_receivedate;
        private string trac_rereason;
        private string trac_close;
        private string trac_closeempno;
        private string trac_closedate;
        private string trac_outbid;
        private string trac_outplan;
        private string trac_outmemo;
        private string trac_receiveempno;
        private string rprint_flag;

        //用借印申請
        private int of_id;
        private string of_guid;
        private string of_reason;
        private string of_other_txt;
        private string of_seal;
        private string of_date;
        private string of_date1;
        private string of_date2;

        //分發對象和部門
        private int esi5_id;
        private int esi5_ver;
        private string esi5_sendtoempno;
        private string esi5_sendtoname;
        private string esi5_sendtodept;
        private string esi5_nodelete;

        private string _keyinempno;
        private string _keyinempname;
        private string _keyindate;
        private string _modempno;
        private string _modempname;
        private string _moddate;
        private string _modify;
        private string _delempno;
        private string _delempname;
        private string _delete;


        #endregion

        #region  建構子
        public Signcont()
        {
            _errorMessage = String.Empty;

            _seqsn = 0;
            _main_no = string.Empty;
            _mailtype = string.Empty;
            _main_amt = 0;


            btn = string.Empty;

            main_type = string.Empty;
            main_enddate = string.Empty;
            main_receive = string.Empty;
            main_award = string.Empty;


            //成本定價
            cost_suggestfee1 = 0;
            cost_suggestfee2 = 0;
            cost_basefee1 = 0;
            cost_basefee2 = 0;
            cost_promincome = 0;
            cost_techincome = 0;

            //投標檢核特殊案件
            no53 = string.Empty;

            //更新產服人員簽辦意見
            signUserID = string.Empty;
            signOpinionTxt = string.Empty;

            //追蹤檔
            trac_ver = 0;
            trac_bidmark = String.Empty;
            trac_biddate = String.Empty;
            trac_nobidreason = String.Empty;
            trac_contend = String.Empty;
            trac_contender = String.Empty;
            trac_winbid = String.Empty;
            trac_bidamt = 0;
            trac_finaldate = String.Empty;
            trac_income = 0;
            trac_payamt = 0;
            trac_bidoff = String.Empty;
            trac_nowinreason = String.Empty;
            trac_bidoffreason = String.Empty;
            trac_memo = String.Empty;
            trac_noticedate = String.Empty;
            trac_finalenddate = String.Empty;
            trac_againreason = String.Empty;
            trac_reprintdate = String.Empty;
            trac_receivedate = String.Empty;
            trac_rereason = String.Empty;
            trac_close = String.Empty;
            trac_closeempno = String.Empty;
            trac_closedate = String.Empty;
            trac_outbid = String.Empty;
            trac_outplan = String.Empty;
            trac_outmemo = String.Empty;
            trac_receiveempno = String.Empty;
            rprint_flag = String.Empty;

            //用借印申請
            of_id = 0;
            of_guid = string.Empty;
            of_reason = String.Empty;
            of_other_txt = String.Empty;
            of_seal = String.Empty;
            of_date = String.Empty;
            of_date1 = String.Empty;
            of_date2 = String.Empty;

            //分發對象和部門
            esi5_id = 0;
            esi5_ver = 1;
            esi5_sendtoempno = String.Empty;
            esi5_sendtoname = String.Empty;
            esi5_sendtodept = String.Empty;
            esi5_nodelete = String.Empty;

            _keyinempno = String.Empty;
            _keyinempname = String.Empty;
            _keyindate = String.Empty;
            _modempno = String.Empty;
            _modempname = String.Empty;
            _moddate = String.Empty;
            _modify = String.Empty;
            _delempno = String.Empty;
            _delempname = String.Empty;
            _delete = String.Empty;

        }
        #endregion

        #region  公有屬性
        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 標案流水號
        /// </summary>
        public int Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }
        /// <summary>
        /// 標案案號
        /// </summary>
        public string Main_no
        {
            get { return _main_no; }
            set { _main_no = value; }
        }
        /// <summary>
        /// 發信種類
        /// </summary>
        public string Main_mailtype
        {
            get { return _mailtype; }
            set { _mailtype = value; }
        }
        /// <summary>
        /// 標案金額
        /// </summary>
        public int Main_amt
        {
            get { return _main_amt; }
            set { _main_amt = value; }
        }


        /// <summary>
        /// 標案類別
        /// </summary>
        public string Main_type
        {
            get { return main_type; }
            set { main_type = value; }
        }

        /// <summary>
        /// 按鈕
        /// </summary>
        public string Btn
        {
            get { return btn; }
            set { btn = value; }
        }

        /// <summary>
        /// 招標方式
        /// </summary>
        public string Main_receive
        {
            get { return main_receive; }
            set { main_receive = value; }
        }


        /// <summary>
        /// 決標方式
        /// </summary>
        public string Main_award
        {
            get { return main_award; }
            set { main_award = value; }
        }


        /// <summary>
        /// 標案截止日
        /// </summary>
        public string Main_enddate
        {
            get { return main_enddate; }
            set { main_enddate = value; }
        }

        /// <summary>
        /// 標案執行日起
        /// </summary>
        public string Main_sdate
        {
            get { return main_sdate; }
            set { main_sdate = value; }
        }


        /// <summary>
        /// 標案執行日迄
        /// </summary>
        public string Main_edate
        {
            get { return main_edate; }
            set { main_edate = value; }
        }


        /// <summary>
        /// 標案案號
        /// </summary>
        public string Main_no_
        {
            get { return main_no; }
            set { main_no = value; }
        }


        /// <summary>
        /// 技服底價金額
        /// </summary>
        public int Cost_basefee1
        {
            get { return cost_basefee1; }
            set { cost_basefee1 = value; }
        }

        /// <summary>
        /// 技服報價金額
        /// </summary>
        public int Cost_suggestfee1
        {
            get { return cost_suggestfee1; }
            set { cost_suggestfee1 = value; }
        }

        /// <summary>
        /// 技服收入金額
        /// </summary>
        public int Cost_promincome
        {
            get { return cost_promincome; }
            set { cost_promincome = value; }
        }


        /// <summary>
        /// 技轉底價金額
        /// </summary>
        public int Cost_basefee2
        {
            get { return cost_basefee2; }
            set { cost_basefee2 = value; }
        }

        /// <summary>
        /// 技轉報價金額
        /// </summary>
        public int Cost_suggestfee2
        {
            get { return cost_suggestfee2; }
            set { cost_suggestfee2 = value; }
        }

        /// <summary>
        /// 技轉收入金額
        /// </summary>
        public int Cost_techincome
        {
            get { return cost_techincome; }
            set { cost_techincome = value; }
        }


        /// <summary>
        /// 特殊標案
        /// </summary>
        public string No53
        {
            get { return no53; }
            set { no53 = value; }
        }

        /// <summary>
        /// 產服簽辦人工號
        /// </summary>
        public string SignUserID
        {
            get { return signUserID; }
            set { signUserID = value; }
        }

        /// <summary>
        /// 產服簽辦人意見
        /// </summary>
        public string SignOpinionTxt
        {
            get { return signOpinionTxt; }
            set { signOpinionTxt = value; }
        }



        #region 追蹤檔
        /// <summary>
        /// 版本
        /// </summary>
        public int Trac_ver
        {
            get { return trac_ver; }
            set { trac_ver = value; }
        }
        /// <summary>
        /// 本院是否投標
        /// </summary>
        public string Trac_bidmark
        {
            get { return trac_bidmark; }
            set { trac_bidmark = value; }
        }
        /// <summary>
        /// 正式投標日期
        /// </summary>
        public string Trac_biddate
        {
            get { return trac_biddate; }
            set { trac_biddate = value; }
        }
        /// <summary>
        /// 未投標原因
        /// </summary>
        public string Trac_nobidreason
        {
            get { return trac_nobidreason; }
            set { trac_nobidreason = value; }
        }
        /// <summary>
        /// 是否有民間企業參與競標
        /// </summary>
        public string Trac_contend
        {
            get { return trac_contend; }
            set { trac_contend = value; }
        }
        /// <summary>
        /// 競標廠商
        /// </summary>
        public string Trac_contender
        {
            get { return trac_contender; }
            set { trac_contender = value; }
        }
        /// <summary>
        /// 本院是否得標
        /// </summary>
        public string Trac_winbid
        {
            get { return trac_winbid; }
            set { trac_winbid = value; }
        }
        /// <summary>
        /// 得標金額
        /// </summary>
        public int Trac_bidamt
        {
            get { return trac_bidamt; }
            set { trac_bidamt = value; }
        }
        /// <summary>
        /// 決標日期
        /// </summary>
        public string Trac_finaldate
        {
            get { return trac_finaldate; }
            set { trac_finaldate = value; }
        }
        /// <summary>
        /// 認列收入金額
        /// </summary>
        public int Trac_income
        {
            get { return trac_income; }
            set { trac_income = value; }
        }
        /// <summary>
        /// 代收代付金額
        /// </summary>
        public int Trac_payamt
        {
            get { return trac_payamt; }
            set { trac_payamt = value; }
        }
        /// <summary>
        /// 流標原因代碼
        /// </summary>
        public string Trac_bidoff
        {
            get { return trac_bidoff; }
            set { trac_bidoff = value; }
        }
        /// <summary>
        /// 未得標原因
        /// </summary>
        public string Trac_nowinreason
        {
            get { return trac_nowinreason; }
            set { trac_nowinreason = value; }
        }
        /// <summary>
        /// 流標原因其他
        /// </summary>
        public string Trac_bidoffreason
        {
            get { return trac_bidoffreason; }
            set { trac_bidoffreason = value; }
        }
        /// <summary>
        /// 追蹤備註
        /// </summary>
        public string Trac_memo
        {
            get { return trac_memo; }
            set { trac_memo = value; }
        }
        /// <summary>
        /// 公告日期
        /// </summary>
        public string Trac_noticedate
        {
            get { return trac_noticedate; }
            set { trac_noticedate = value; }
        }
        /// <summary>
        /// 截標日期
        /// </summary>
        public string Trac_finalenddate
        {
            get { return trac_finalenddate; }
            set { trac_finalenddate = value; }
        }
        /// <summary>
        /// 本院再投標原因
        /// </summary>
        public string Trac_againreason
        {
            get { return trac_againreason; }
            set { trac_againreason = value; }
        }
        /// <summary>
        /// 開放重印日期
        /// </summary>
        public string Trac_reprintdate
        {
            get { return trac_reprintdate; }
            set { trac_reprintdate = value; }
        }
        /// <summary>
        /// 檢核表收件日期
        /// </summary>
        public string Trac_receivedate
        {
            get { return trac_receivedate; }
            set { trac_receivedate = value; }
        }
        /// <summary>
        /// 重印原因
        /// </summary>
        public string Trac_rereason
        {
            get { return trac_rereason; }
            set { trac_rereason = value; }
        }
        /// <summary>
        /// 結件處理
        /// </summary>
        public string Trac_close
        {
            get { return trac_close; }
            set { trac_close = value; }
        }
        /// <summary>
        /// 結件人員
        /// </summary>
        public string Trac_closeempno
        {
            get { return trac_closeempno; }
            set { trac_closeempno = value; }
        }
        /// <summary>
        /// 結件日期
        /// </summary>
        public string Trac_closedate
        {
            get { return trac_closedate; }
            set { trac_closedate = value; }
        }
        /// <summary>
        /// 策略性退出標案
        /// </summary>
        public string Trac_outbid
        {
            get { return trac_outbid; }
            set { trac_outbid = value; }
        }
        /// <summary>
        /// 策略性退出計畫
        /// </summary>
        public string Trac_outplan
        {
            get { return trac_outplan; }
            set { trac_outplan = value; }
        }
        /// <summary>
        /// 策略性備註
        /// </summary>
        public string Trac_outmemo
        {
            get { return trac_outmemo; }
            set { trac_outmemo = value; }
        }
        /// <summary>
        /// 收件處理人員工號
        /// </summary>
        public string Trac_receiveempno
        {
            get { return trac_receiveempno; }
            set { trac_receiveempno = value; }
        }
        /// <summary>
        /// Y 開放重印 --N 未開放
        /// </summary>
        public string Rprint_flag
        {
            get { return rprint_flag; }
            set { rprint_flag = value; }
        }
        #endregion

        #region 用借印申請
        /// <summary>
        /// 識別號
        /// </summary>
        public int Of_id
        {
            get { return of_id; }
            set { of_id = value; }
        }
        /// <summary>
        /// 簽核guid
        /// </summary>
        public string Of_guid
        {
            get { return of_guid; }
            set { of_guid = value; }
        }

        /// <summary>
        /// 申請事由
        /// </summary>
        public string Of_reason
        {
            get { return of_reason; }
            set { of_reason = value; }
        }
        /// <summary>
        /// 其他
        /// </summary>
        public string Of_other_txt
        {
            get { return of_other_txt; }
            set { of_other_txt = value; }
        }
        /// <summary>
        /// 用/借印
        /// </summary>
        public string Of_seal
        {
            get { return of_seal; }
            set { of_seal = value; }
        }
        /// <summary>
        /// 用印日期
        /// </summary>
        public string Of_date
        {
            get { return of_date; }
            set { of_date = value; }
        }
        /// <summary>
        /// 日期起
        /// </summary>
        public string Of_date1
        {
            get { return of_date1; }
            set { of_date1 = value; }
        }
        /// <summary>
        /// 日期迄
        /// </summary>
        public string Of_date2
        {
            get { return of_date2; }
            set { of_date2 = value; }
        }
        #endregion

        #region 分發對象和部門
        /// <summary>
        /// 分發對象和部門識別號
        /// </summary>
        public int Esi5_id
        {
            get { return esi5_id; }
            set { esi5_id = value; }
        }
        /// <summary>
        /// 送簽版次
        /// </summary>
        public int Esi5_ver
        {
            get { return esi5_ver; }
            set { esi5_ver = value; }
        }
        /// <summary>
        /// 分發對象工號
        /// </summary>
        public string Esi5_sendtoempno
        {
            get { return esi5_sendtoempno; }
            set { esi5_sendtoempno = value; }
        }
        /// <summary>
        /// 分發對象姓名
        /// </summary>
        public string Esi5_sendtoname
        {
            get { return esi5_sendtoname; }
            set { esi5_sendtoname = value; }
        }
        /// <summary>
        /// 分發部門
        /// </summary>
        public string Esi5_sendtodept
        {
            get { return esi5_sendtodept; }
            set { esi5_sendtodept = value; }
        }
        /// <summary>
        /// 是否可刪除(1:不可/0:不可)
        /// </summary>
        public string Esi5_nodelete
        {
            get { return esi5_nodelete; }
            set { esi5_nodelete = value; }
        }

        #endregion

        /// <summary>
        /// 填寫人工號
        /// </summary>
        public string KeyinEmpNo
        {
            get { return _keyinempno; }
            set { _keyinempno = value; }
        }
        /// <summary>
        /// 填寫人姓名
        /// </summary>
        public string KeyinEmpName
        {
            get { return _keyinempname; }
            set { _keyinempname = value; }
        }
        /// <summary>
        /// 填寫日期
        /// </summary>
        public string KeyinDate
        {
            get { return _keyindate; }
            set { _keyindate = value; }
        }
        /// <summary>
        /// 修改人工號
        /// </summary>
        public string ModEmpNo
        {
            get { return _modempno; }
            set { _modempno = value; }
        }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModEmpName
        {
            get { return _modempname; }
            set { _modempname = value; }
        }
        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModDate
        {
            get { return _moddate; }
            set { _moddate = value; }
        }
        /// <summary>
        /// 是否可修改Y. 是 N. 否, Default:Y
        /// </summary>
        public string Modify
        {
            get { return _modify; }
            set { _modify = value; }
        }
        /// <summary>
        /// 刪除人工號
        /// </summary>
        public string DelEmpNo
        {
            get { return _delempno; }
            set { _delempno = value; }
        }
        /// <summary>
        /// 刪除人姓名
        /// </summary>
        public string DelEmpName
        {
            get { return _delempname; }
            set { _delempname = value; }
        }
        /// <summary>
        /// 刪除註記(Default:N)
        /// </summary>
        public string Main_Delete
        {
            get { return _delete; }
            set { _delete = value; }
        }

        #endregion

        #region  公有函式
        /// <summary>
        /// 取得明細
        /// </summary>
        /// <returns></returns>
        public DataTable GetDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_trac_select_by_seqsn";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            if (trac_ver != 0)
            {
                oCmd.Parameters.AddWithValue("@ver", trac_ver);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }



        /// <summary>
        /// 業務人員修改資料
        /// </summary>
        /// <returns></returns>
        public string Update_for_bu()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_ecp_update_for_bu";

            oCmd.Parameters.AddWithValue("@btn", btn);
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", trac_ver);

            oCmd.Parameters.AddWithValue("@main_type", main_type);
            oCmd.Parameters.AddWithValue("@main_enddate", main_enddate);

            oCmd.Parameters.AddWithValue("@main_sdate", main_sdate);
            oCmd.Parameters.AddWithValue("@main_edate", main_edate);

            oCmd.Parameters.AddWithValue("@main_no", main_no);

            oCmd.Parameters.AddWithValue("@main_receive", main_receive);
            oCmd.Parameters.AddWithValue("@main_award", main_award);

            oCmd.Parameters.AddWithValue("@cost_suggestfee1", cost_suggestfee1);
            oCmd.Parameters.AddWithValue("@cost_basefee1", cost_basefee1);
            oCmd.Parameters.AddWithValue("@cost_promincome", cost_promincome);

            oCmd.Parameters.AddWithValue("@cost_suggestfee2", cost_suggestfee2);
            oCmd.Parameters.AddWithValue("@cost_basefee2", cost_basefee2);
            oCmd.Parameters.AddWithValue("@cost_techincome", cost_techincome);

            oCmd.Parameters.AddWithValue("@no53", no53);

            oCmd.Parameters.AddWithValue("@SignUserID", signUserID);
            oCmd.Parameters.AddWithValue("@SignOpinionTxt", signOpinionTxt);


            //正式得標

            oCmd.Parameters.AddWithValue("@trac_bidmark", trac_bidmark);
            oCmd.Parameters.AddWithValue("@trac_biddate", trac_biddate);
            oCmd.Parameters.AddWithValue("@trac_nobidreason", trac_nobidreason);


            //填寫得標資訊
            oCmd.Parameters.AddWithValue("@trac_winbid", trac_winbid);
            oCmd.Parameters.AddWithValue("@trac_bidamt", trac_bidamt);
            oCmd.Parameters.AddWithValue("@trac_finaldate", trac_finaldate);
            oCmd.Parameters.AddWithValue("@trac_income", trac_income);
            oCmd.Parameters.AddWithValue("@trac_payamt", trac_payamt);
            oCmd.Parameters.AddWithValue("@trac_bidoff", trac_bidoff);
            oCmd.Parameters.AddWithValue("@trac_nowinreason", trac_nowinreason);
            oCmd.Parameters.AddWithValue("@trac_bidoffreason", trac_bidoffreason);


            oCmd.Parameters.AddWithValue("@trac_modempno", _modempno);



            SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
            rtn_flag.Direction = ParameterDirection.Output;

            this.Execute2(oCmd, CommandType.StoredProcedure);

            return rtn_flag.Value.ToString();

            /*

                try
                {

                    this.Execute(oCmd, CommandType.StoredProcedure);
                    success = true;
                }
                catch (Exception ex)
                {
                    _errorMessage = ex.Message;
                }
                return success;
            */

        }



        /// <summary>
        /// 正式投標
        /// </summary>
        /// <returns></returns>
        public bool Update_trace_t1()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_trace_update_t1";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", trac_ver);
            oCmd.Parameters.AddWithValue("@trac_bidmark", trac_bidmark);
            oCmd.Parameters.AddWithValue("@trac_biddate", trac_biddate);
            oCmd.Parameters.AddWithValue("@trac_nobidreason", trac_nobidreason);
            oCmd.Parameters.AddWithValue("@trac_contend", trac_contend);
            oCmd.Parameters.AddWithValue("@trac_contender", trac_contender);
            oCmd.Parameters.AddWithValue("@trac_modempno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        /// <summary>
        /// 填寫得標資訊
        /// </summary>
        /// <returns></returns>
        public bool Update_trace_t2()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_trace_update_t2";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", trac_ver);
            oCmd.Parameters.AddWithValue("@trac_winbid", trac_winbid);
            oCmd.Parameters.AddWithValue("@trac_bidamt", trac_bidamt);
            oCmd.Parameters.AddWithValue("@trac_finaldate", trac_finaldate);
            oCmd.Parameters.AddWithValue("@trac_income", trac_income);
            oCmd.Parameters.AddWithValue("@trac_payamt", trac_payamt);
            oCmd.Parameters.AddWithValue("@trac_bidoff", trac_bidoff);
            oCmd.Parameters.AddWithValue("@trac_nowinreason", trac_nowinreason);
            oCmd.Parameters.AddWithValue("@trac_bidoffreason", trac_bidoffreason);
            oCmd.Parameters.AddWithValue("@trac_memo", trac_memo);
            oCmd.Parameters.AddWithValue("@trac_noticedate", trac_noticedate);
            oCmd.Parameters.AddWithValue("@trac_finalenddate", trac_finalenddate);
            oCmd.Parameters.AddWithValue("@trac_againreason", trac_againreason);
            oCmd.Parameters.AddWithValue("@trac_modempno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        /// <summary>
        /// 再次投標
        /// </summary>
        /// <returns></returns>
        public bool SetWinBidOff()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prSetWinBidOff";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@trac_noticedate", trac_noticedate);
            oCmd.Parameters.AddWithValue("@trac_finalenddate", trac_finalenddate);
            oCmd.Parameters.AddWithValue("@trac_againreason", trac_againreason);
            oCmd.Parameters.AddWithValue("@main_amt", _main_amt);
            oCmd.Parameters.AddWithValue("@trac_modempno", _modempno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        /// <summary>
        /// 再次投標發信通知
        /// </summary>
        /// <returns></returns>
        public bool GetMailMessage()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetMailMessage";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@mail_mailtype", _mailtype);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        #region 用借印申請
        /// <summary>
        /// 更新用借印申請
        /// </summary>
        /// <returns></returns>
        public bool Update_officeicalseal()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_officeicalseal_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@of_id", of_id);
            oCmd.Parameters.AddWithValue("@of_reason", of_reason);
            oCmd.Parameters.AddWithValue("@of_other_txt", of_other_txt);
            oCmd.Parameters.AddWithValue("@of_seal", of_seal);
            oCmd.Parameters.AddWithValue("@of_date", of_date);
            oCmd.Parameters.AddWithValue("@of_date1", of_date1);
            oCmd.Parameters.AddWithValue("@of_date2", of_date2);
            oCmd.Parameters.AddWithValue("@empno", _keyinempno);

            try
            {
                of_id = int.Parse(this.getTopOne(oCmd, CommandType.StoredProcedure));
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 取得用借印申請
        /// </summary>
        /// <returns></returns>
        public DataTable Get_officeicalseal()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT of_order =ROW_NUMBER() OVER(ORDER BY of_keyindate) ,*
FROM view_gpi_officialseal
WHERE 1 = 1";

            if (_seqsn != 0)
            {
                oCmd.CommandText += @" AND of_seqsn = @main_seqsn";
                oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            }

            if (of_id != 0)
            {
                oCmd.CommandText += @" AND of_id = @of_id";
                oCmd.Parameters.AddWithValue("@of_id", of_id);
            }

            if (of_guid != string.Empty)
            {
                oCmd.CommandText += @" AND of_guid = @of_guid";
                oCmd.Parameters.AddWithValue("@of_guid", of_guid);
            }

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;

        }
        /// <summary>
        /// 刪除用借印申請
        /// </summary>
        /// <returns></returns>
        public bool Delete_officialseal()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_officeicalseal_delete";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@of_id", of_id);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 檢查是否需要簽核
        /// </summary>
        /// <returns>表單Guid</returns>
        public string Check_ecp()
        {

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_check_ecp";

            oCmd.Parameters.AddWithValue("@of_id", of_id);
            SqlParameter rtn_flag = oCmd.Parameters.Add("@rtn_flag", SqlDbType.VarChar, 1);
            rtn_flag.Direction = ParameterDirection.Output;

            this.Execute2(oCmd, CommandType.StoredProcedure);

            return rtn_flag.Value.ToString();

        }

        #endregion

        #region 分發對象
        /// <summary>
        /// 取得分發對象
        /// </summary>
        /// <returns></returns>
        public DataTable Get_signitem5()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_signitem5_select";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", trac_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;

        }
        /// <summary>
        /// 取得分發對象明細
        /// </summary>
        /// <returns></returns>
        public DataTable Get_signitem5_detail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT		item5.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
				--該會計人員不可做刪除
				,case  when eed_empno is not null then '1' else '0' end bDefault
	FROM		gpi_signitem5 item5
	JOIN		common.dbo.depcod ON esi5_sendtodept=dep_deptid
	JOIN		common.dbo.orgcod ON dep_orgcd=org_orgcd
	--各單位之預設會計人員
	JOIN		gpi_main ON main_seqsn=esi5_seqsn
	LEFT JOIN	engage_empno_default ON (eed_org = main_orgcd) AND (eed_type = 'S2') and eed_empno=esi5_sendtoempno
	WHERE		esi5_seqsn=@seqsn AND esi5_ver=@ver AND esi5_id = @esi5_id
";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", esi5_ver);
            oCmd.Parameters.AddWithValue("@esi5_id", esi5_id);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;

        }
        /// <summary>
        /// 更新分發對象
        /// </summary>
        /// <returns></returns>
        public bool Update_signitem5()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_signitem5_update";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@item_id", esi5_id);
            oCmd.Parameters.AddWithValue("@sendtoempno", esi5_sendtoempno);
            oCmd.Parameters.AddWithValue("@sendtoname", esi5_sendtoname);
            oCmd.Parameters.AddWithValue("@sendtodept", esi5_sendtodept);
            oCmd.Parameters.AddWithValue("@esi5_nodelete", esi5_nodelete);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 刪除分發對象
        /// </summary>
        /// <returns></returns>
        public bool Delete_signitem5()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
DELETE gpi_signitem5
WHERE esi5_seqsn = @main_seqsn AND esi5_id = @esi5_id AND esi5_ver = @esi5_ver
";
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@esi5_id", esi5_id);
            oCmd.Parameters.AddWithValue("@esi5_ver", esi5_ver);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        #endregion

        #region 契約簽辦主檔
        /// <summary>
        /// 取得分發對象
        /// </summary>
        /// <returns></returns>
        public DataTable Get_signcont()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
SELECT [esc_contsdate]
      ,[esc_contedate]
      ,[esc_lastprintdate]
      ,[esc_docno]
      ,[esc_actualsigndate]
      ,[esc_signstatus]
 FROM  [v_gpi_signcont]
WHERE esc_seqsn = @seqsn
";
            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;

        }
        #endregion

        public bool Save_tsc()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_trace_tsc_update";

            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@trac_reprintdate", trac_reprintdate);
            oCmd.Parameters.AddWithValue("@trac_receivedate", trac_receivedate);
            oCmd.Parameters.AddWithValue("@trac_rereason", trac_rereason);
            oCmd.Parameters.AddWithValue("@trac_close", trac_close);
            oCmd.Parameters.AddWithValue("@trac_closeempno", trac_closeempno);
            oCmd.Parameters.AddWithValue("@trac_closedate", trac_closedate);
            oCmd.Parameters.AddWithValue("@trac_outbid", trac_outbid);
            oCmd.Parameters.AddWithValue("@trac_outplan", trac_outplan);
            oCmd.Parameters.AddWithValue("@trac_outmemo", trac_outmemo);
            oCmd.Parameters.AddWithValue("@trac_memo", trac_memo);
            oCmd.Parameters.AddWithValue("@rac_receiveempno,", trac_receiveempno);
            oCmd.Parameters.AddWithValue("@rprint_flag", rprint_flag);
            try
            {
                _seqsn = int.Parse(this.getTopOne2(oCmd, CommandType.StoredProcedure));
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        /// <summary>
        /// 取得追蹤版本清單
        /// </summary>
        /// <returns></returns>
        public DataTable Get_tracehis()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"select this_ver,this_keyindate,this_keyinempno,this_keyinempname from gpi_tracehis where this_seqsn = @seqsn";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #region 列印用借印申請(added by cary)
        /// <summary>
        /// report_gpi_applylist
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_applylist()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_applylist_new";

            oCmd.Parameters.AddWithValue("@seqsn", of_id);
            oCmd.Parameters.AddWithValue("@guid", of_guid);
            oCmd.Parameters.AddWithValue("@main_seqsn", _seqsn);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_gpi_reportFooter
        /// </summary>
        /// <returns></returns>
        public DataTable report_gpi_reportFooter(string orgcd)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_gpi_reportFooter";

            oCmd.Parameters.AddWithValue("@type", "2");
            oCmd.Parameters.AddWithValue("@orgcd", orgcd);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// report_ecp_command1
        /// </summary>
        /// <returns></returns>
        public DataTable report_ecp_command1()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"report_ecp_command";

            oCmd.Parameters.AddWithValue("@seqsn", of_id);
            oCmd.Parameters.AddWithValue("@formno", "GPI02");
            oCmd.Parameters.AddWithValue("@flag", 1);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        /// <summary>
        /// pr_gpi_signitem5_select
        /// </summary>
        /// <returns></returns>
        public DataTable pr_gpi_signitem5_select()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_signitem5_select";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ver", esi5_ver);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        #endregion
        /// <summary>
        /// 遇民間業者競標本院作法
        /// </summary>
        /// <param name="seqsn"></param>
        /// <param name="result"></param>
        /// <param name="empno"></param>
        public void setTrace_response(string seqsn, string result, string empno)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
update gpi_trace
set trac_respResult=(case when @trac_respResult='D' then NULL else @trac_respResult end)
,trac_respEmpno=(case when @trac_respResult='D' then NULL else @trac_respEmpno end)
,trac_respDate=(case when @trac_respResult='D' then NULL else getdate() end)
where trac_seqsn=@trac_seqsn

";
            oCmd.Parameters.AddWithValue("@trac_seqsn", seqsn);
            oCmd.Parameters.AddWithValue("@trac_respResult", result);
            oCmd.Parameters.AddWithValue("@trac_respEmpno", empno);

            try
            {
                this.Execute(oCmd, CommandType.Text);

            }
            catch (Exception ex)
            {
                throw;
            }

        }

        #endregion

    }
}