﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace GPI
{
    /// <summary>
    /// Summary description for Default
    /// </summary>
    public class Default : GPI.mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;

        #endregion

        #region 建構子

        public Default()
        {
            _errorMessage = String.Empty;
        }

        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }


        #endregion

        #region 公有函式
        public bool Insert()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"";
            oCmd.Parameters.AddWithValue("@_errorMessage", _errorMessage);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        public DataTable Get()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"";
            oCmd.Parameters.AddWithValue("@_errorMessage", _errorMessage);
            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

        #endregion
    }
}