﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TechCase_search_distribution : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    public bool IsDanger(string str)
    {
        //if( str.ToUpper().IndexOf("-") >=0 ) return true ;
        if (str.ToUpper().IndexOf("%") >= 0) return true;
        if (str.ToUpper().IndexOf("\"") >= 0) return true;
        if (str.ToUpper().IndexOf("'") >= 0) return true;
        if (str.ToUpper().IndexOf("$") >= 0) return true;
        if (str.ToUpper().IndexOf("{") >= 0) return true;
        return false;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["empno"] = ssoUser.empNo;
            ViewState["empName"] = ssoUser.empName;

            ViewState["sortorder"] = "";
            ViewState["sortField"] = "";

            Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }

    private void Bind_Data(string str_sortField, string str_sort)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        SGV_search.PageIndex = 0;

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Parameters.Clear();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = "esp_treaty_TechCase_search_distribution";

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.Parameters.AddWithValue("@login", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                if (str_sortField.Trim() != "")
                {
                    dt.DefaultView.Sort = str_sortField + " " + str_sort;
                }

                SGV_search.DataSource = dt;
                SGV_search.DataBind();

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void btnQuery_Click(object sender, EventArgs e)
    {

        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }

    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        SGV_search.PageIndex = e.NewPageIndex;
    }

    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "View")
        {
            Response.Redirect("./TechCase_view.aspx?tt_seno=" + e.CommandArgument.ToString());
        }
    }

    protected void SGV_search_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label LB_bomb = (Label)e.Row.FindControl("LB_bomb");
            if (LB_bomb != null)
            {
                int tt_seno = Convert.ToInt32(DataBinder.Eval(e.Row.DataItem, "tt_seno"));
                DataTable dt = Case_View(tt_seno);

                if (dt.Rows.Count > 0)
                {
                    string tt_degree = dt.Rows[0]["tt_degree"].ToString();//狀態
                    string tt_keyin_date = dt.Rows[0]["tt_keyin_date"].ToString();//立案日期
                    string tt_assign_datetime = dt.Rows[0]["tt_assign_datetime"].ToString();//分案日期
                    string tt_assign_c_datetime = dt.Rows[0]["tt_assign_c_datetime"].ToString();//分案日期 C

                    DateTime today = Convert.ToDateTime(DateTime.Now.ToShortDateString());

                    if (tt_degree == "0" || tt_degree == "3")
                    {
                        DateTime keyin_date = DateTime.Now;
                        DateTime.TryParse(tt_keyin_date, out keyin_date);
                        keyin_date = Convert.ToDateTime(keyin_date.ToShortDateString());
                        if (keyin_date.AddDays(3) < today)
                        {
                            LB_bomb.Visible = true;
                        }
                    }
                    else if (tt_degree == "5")
                    {
                        DateTime assign_datetime = DateTime.Now;
                        DateTime.TryParse(tt_assign_datetime, out assign_datetime);
                        assign_datetime = Convert.ToDateTime(assign_datetime.ToShortDateString());
                        if (assign_datetime.AddDays(3) < today)
                        {
                            LB_bomb.Visible = true;
                        }
                    }
                }
            }
        }
    }

    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {
        ViewState["sortorder"] = "asc";
        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Bind_Data(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }

    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {
                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {
                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }
                }
            }
        }
    }

    private DataTable Case_View(int tt_seno)
    {
        DataTable dt = new DataTable();
        #region --- query --- 
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tt_seno", tt_seno);
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view_case");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        return dt;
    }
}