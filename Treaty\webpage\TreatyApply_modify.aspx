﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyApply_modify.aspx.cs" Inherits="TreatyApply_modify" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <script src="../Scripts/languages/jquery.validationEngine-zh_TW.js" type="text/javascript" charset="utf-8"> </script>
    <script src="../Scripts/jquery.validationEngine.js" type="text/javascript" charset="utf-8"></script>

    <!--
        <script type="text/javascript" src="../Scripts/calendar/jquery-ui-1.8.7.custom.min.js"></script> 
        <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    -->
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function Find_Empno(obj, arg_sw) {
            $(".ajax_empno").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx"
                , iframe: true, width: "700px", height: "630px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback2);
                    }
                }
            });
            $(".ajax_kw").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });
        }
        function Find_empno_kw(obj, arg_sw) {
            var strURL = "../../comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val());
            if (arg_sw == "1") {
                $.getJSON(strURL + '&callback=?', jsonp_callback1);
            }
            if (arg_sw == "2") {
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }

        }
        function jsonp_callback1(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    $('#txt_promoter_empno').val("");
                    $('#txt_promoter_name').val("");
                    $('#txtTel').val("");
                    $('#txtOrgAbbrName').val("");
                    $('#x_dept').val("");
                    $('#txt_req_dept').val("");
                    $('#txtOrgAbbrName').val("");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#txt_promoter_empno').val("");
                    $('#txt_promoter_name').val("");
                    $('#txtTel').val("");
                    $('#txtOrgAbbrName').val("");
                    $('#x_dept').val("");
                    $('#txt_req_dept').val("");
                    $('#txtOrgAbbrName').val("");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#txt_promoter_name').val())
                        , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                        , title: '單人挑選'
                        , onClosed: function () {
                            $('#txt_promoter_empno').val("");
                            $('#txt_promoter_name').val("");
                            $('#txtTel').val("");
                            $('#txtOrgAbbrName').val("");
                            $('#x_dept').val("");
                            $('#txt_req_dept').val("");
                            $('#txtOrgAbbrName').val("");
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback1);
                        }
                    });
                    break;
                default:
                    $('#txt_promoter_empno').val(data.c_com_empno);
                    $('#txt_promoter_name').val(data.c_com_cname);
                    $('#txtTel').val(data.c_com_telext);
                    $('#txtOrgAbbrName').val(data.c_com_orgcd);
                    $('#x_dept').val(data.c_com_deptcd);
                    $('#txt_req_dept').val(data.c_com_deptid);
                    $('#txtOrgAbbrName').val(data.c_com_orgName);
            }
            reflash_topic("ADM_other", 0);
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    $('#h_px_empno').val("");
                    $('#txt_px_name').val("");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#h_px_empno').val("");
                    $('#txt_px_name').val("");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#txt_px_name').val())
                        , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                        , title: '單人挑選'
                        , onClosed: function () {
                            $('#h_px_empno').val("");
                            $('#txt_px_name').val("");
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    });

                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }
        function find_customer() {
            $.colorbox.settings.data = $("#h_compno").val();
            $(".ajax_customer").colorbox({
                href: "./DialogMultiCustomerSelect2.aspx",
                iframe: true, width: "85%", height: "90%", transition: "none", opacity: "0.5", overlayClose: false,
                onClosed: function () {
                    $('html, body').css('overflow', '');
                    if ($.colorbox.settings.data != undefined) {
                        $("#h_compno").val($.colorbox.settings.data);
                        $.colorbox.settings.data = undefined;
                        reflash_topic("company_renew", 0);
                    }
                }
            });
        }
        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_customer").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

                }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function ipbshare_check(object) {//智權歸屬共用裡的本院、客戶自動比率調整
            chk_int(object);
            if (object.value > 100 || object.value < 0) {
                alert('所輸入的數字格式必須大於等於0，且小於等於100');
                object.value = '';
                return false;
            }
            var total = 100;
            if (object.id == 'txt_ipbi_percent') {//使用者觸發了本院
                $('#txt_ipbc_percent').val(total - $('#txt_ipbi_percent').val());
            }
            else {//使用者觸發了客戶
                $('#txt_ipbi_percent').val(total - $('#txt_ipbc_percent').val());
            }
            $('#rb_ipb_coparcenary').prop('checked', true);//自動將智權歸屬的共有選項勾選
            $('#txt_ipb_other_desc').val("");//智權歸屬-其他值清空
        }
        function ipbOther() {//如果有變更智權歸屬裡的其他描述，則自動勾選其他
            $('#rb_ipb_other').prop('checked', true);
            $('#txt_ipbi_percent').val("");//智權歸屬-共有-本院的值清空
            $('#txt_ipbc_percent').val("");//智權歸屬-共有-客戶的值清空
        }
        function duty_plan() {//如果有變更責任範圍裡的計畫經費，則自動勾選計畫經費
            $('#rb_duty_plain').attr('checked', true);
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_capital() {//如果有變更責任範圍裡的最高賠償金額，則自動勾選自動賠償金額
            $('#rb_duty_capital').attr('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_other() {//如果有變更責任範圍裡的其他，則自動勾選勾選其他
            $('#rb_duty_other').prop('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
        }
        function treaty_fileup() {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_FileUp.aspx?contno=" + $("#txtComplexNo").html() + "&seno=<%= Server.HtmlEncode(Request.QueryString["seno"]) %>"
                , title: '上傳檔案'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApplyFileModify.aspx?fid=" + fid
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function seno_ECP() {  //編輯一般收款明細
            $.colorbox({
                href: 'TreatyApply_ECP.aspx?seno=<%= Server.HtmlEncode(Request.QueryString["seno"]) %>'
                , title: '簽核明細'
                , iframe: true, width: "850px", height: "750px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("seno_ECP", 0);
                }
            });
        }

        function autogrow(textarea) {
            var adjustedHeight = textarea.clientHeight;
            adjustedHeight = Math.max(textarea.scrollHeight, adjustedHeight);
            if (adjustedHeight > textarea.clientHeight) {
                textarea.style.height = adjustedHeight + 'px';
            }
        }

    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <br />
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" Height="30px" ImageUrl="../images/CONFIDENTIAL.png" Width="85px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="btnEngage" runat="server" OnClick="btnEngage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊</asp:LinkButton>&nbsp;&nbsp;
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">

                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:Label ID="txtComplexNo" runat="server" Text=""></asp:Label>
                                            (舊/原案號:
                                            <asp:TextBox ID="txtOldContno" runat="server" class="inputex inputsizeS"></asp:TextBox>)</td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                        </td>
                                        <td class="width40">
                                            <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" class="radio-input" />
                                            <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" class="radio-input" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtOrgAbbrName" runat="server" Width="40px" class="TB_ReadOnly"></asp:TextBox>&nbsp;
                                            <asp:HiddenField ID="txt_req_dept" runat="server" />
                                            <asp:TextBox ID="x_dept" runat="server" Width="70px" class="TB_ReadOnly"></asp:TextBox>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txt_promoter_name" runat="server" Width="95px" class="ajax_kw inputex text-input"></asp:TextBox>
                                            <a onclick="javascript:Find_Empno('txt_promoter_empno','1');">
                                                <img id="img_promoter_name" src="../images/icon_find.png" border="0" class="ajax_empno btn_mouseout" /></a>&nbsp;
                                                分機 &nbsp;
                                            <asp:TextBox ID="txtTel" runat="server" Width="110px" class="TB_ReadOnly"></asp:TextBox>&nbsp;
                                                <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約名稱</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txt_name" runat="server" Width="608px" Height="30px" class="text-input"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">急件</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="CB_急件" runat="server" />
                                            <asp:TextBox ID="TB_急件原因" runat="server" Width="608px" TextMode="MultiLine" Height="20px"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td colspan="3">
                                            <!-- 簽約對象 -->
                                            <div class="twocol margin5TB">
                                                <div class="left">
                                                    <asp:Button ID="BT_Customer" runat="server" class="ajax_customer genbtnS" Text="新增" />
                                                </div>
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="100%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                        <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                        <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                        <AlternatingRowStyle CssClass="TRowEven" />
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <HeaderStyle Width="40px" ForeColor="Black"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center"></ItemStyle>
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Eval("comp_idno") %>'>刪除</asp:LinkButton>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="comp_idno" HeaderText="廠商編號">
                                                                <HeaderStyle Width="90px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_cname" HeaderText="廠商中文名稱">
                                                                <HeaderStyle Width="250px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_ename" HeaderText="廠商英文名稱">
                                                                <HeaderStyle Width="350px"></HeaderStyle>
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                    </cc1:SmartGridView>
                                                    <%--  <asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                                    <asp:HiddenField ID="h_compno" runat="server" />
                                                </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                            案件性質</td>
                                        <td colspan="3" class="lineheight03">
                                            <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" />
                                            <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" />
                                            <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" />
                                            <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" />
                                            <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" />
                                            <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽案)" />
                                            <asp:CheckBox ID="cb_conttype_rb" runat="server" Text="標案" />
                                            <asp:CheckBox ID="cb_conttype_m" runat="server" Text="保密契約" />
                                            <asp:Label ID="lb_standar_flag" runat="server" Font-Bold="True" ForeColor="red" Visible="False">(常用版本)</asp:Label>
                                            <asp:CheckBox ID="cb_conttype_c" runat="server" Text="工服" Visible="False" />
                                            <br />
                                            <asp:RadioButton ID="rb_conttype_uo" runat="server" Text="國外支出(無收入)" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_ui" runat="server" Text="國內支出(無收入)" GroupName="ConttypeA" />
                                            <asp:RadioButton ID="rb_conttype_bd" runat="server" Text="新創事業" GroupName="ConttypeT" />
                                            <asp:RadioButton ID="rb_conttype_other" runat="server" Text="其他" GroupName="ConttypeT" />
                                            <asp:TextBox ID="txt_class_other_desc" runat="server" Width="350px" Enabled="false"></asp:TextBox>
                                            <span id="spanContractEdit" runat="server" visible="false">
                                                <br />
                                                <font color="#ff0000">契約修訂
						    <asp:radiobuttonlist id="rbl_amend" runat="server" repeatlayout="Flow" repeatdirection="Horizontal" Visible="false">
							    <asp:listitem value="1" selected="True">展延</asp:listitem>
							    <asp:listitem value="2">中止</asp:listitem>
							    <asp:listitem value="3">其他</asp:listitem>
						    </asp:radiobuttonlist>
                            <asp:textbox id="txtamend_other_desc" runat="server" width="400px"></asp:textbox></font>
                                            </span>
                                            <span style="color: red">
                                                <br />
                                                (若為收入性質契約，請至洽案管理進行相關案件登錄及申請契約需求！)</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約性質</div>
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True">
                                                <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                            </asp:DropDownList>
                                            <asp:Label ID="lb_Amend_Show" runat="server" ForeColor="Red" Visible="false">(修約)</asp:Label>
                                            <%--   <asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約預估金額</div>
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlContMoneyType" runat="server" Width="100px" DataTextField="subtype_desc" DataValueField="code_subtype" />
                                            <%-- <asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  " />--%>
                                            <asp:TextBox ID="txtContMoney" runat="server" MaxLength="13" class="text-input inputsizeS" />
                                            &nbsp;元/ 匯率: 
                        <asp:TextBox ID="TB_money_rate" runat="server" class="inputsizeS text-input" Width="55px"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                契約期間<br />
                                                (預定)
                                            </div>
                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txt_contsdate" class="pickdate inputex inputsizeS  text-input" runat="server" Width="80px" MaxLength="8" />&nbsp;至&nbsp;
				       <asp:TextBox ID="txt_contedate" class="pickdate inputex inputsizeS  text-input" runat="server" Width="80px" MaxLength="8"></asp:TextBox>

                                        </td>

                                    </tr>
                                    <asp:PlaceHolder ID="PL_CoPromoter" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">協同承辦人</div>
                                            </td>
                                            <td colspan="3">
                                                <asp:TextBox ID="txt_px_name" runat="server" Width="65px" />
                                                <asp:HiddenField ID="h_px_empno" runat="server" />
                                                <a onclick="javascript:Find_Empno('h_px_empno','2');">
                                                    <asp:Image ID="Image3" runat="server" ImageUrl="../images/icon_find.png" BorderWidth="0" class="ajax_empno btn_mouseout" /></a> &nbsp;&nbsp;&nbsp;&nbsp;
					      <asp:PlaceHolder ID="PL_amd" runat="server" Visible="False">
                              <font style="color: red; font-weight: bold;">是否同意『貴單位所指定之業務窗口: <asp:label id="LB_adm_text" runat="server" ForeColor="Red" > </asp:label> 君』，與您有相同之權限？</font>
                              <asp:Label ID="LB_adm_業務_empno" runat="server" Visible="False" />
                              <asp:RadioButton ID="rb_adm_yes" runat="server" Text="同意" GroupName="rb_adm"></asp:RadioButton>
                              <asp:RadioButton ID="rb_adm_no" runat="server" Text="不同意" GroupName="rb_adm" Checked="true"></asp:RadioButton>
                              <%-- <asp:SqlDataSource ID="SDS_ADM_業務" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                          </asp:PlaceHolder>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                </table>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">其他需求</div>
                                        </td>
                                        <td class="lineheight03">
                                            <asp:CheckBox ID="rb_other_1" runat="server" Text="1.本案契約與前案號" />&nbsp;<asp:TextBox ID="txt_otherrequire_contno" class="inputsizeS" runat="server" />&nbsp;之契約相同,承辦法務同仁為&nbsp;<asp:TextBox ID="TB_otherrequire_handle_name" class="inputsizeS" runat="server" /><br />
                                            <asp:CheckBox ID="rb_other_2" runat="server" Text="2.本案前已與法務同仁" />&nbsp;<asp:TextBox ID="txt_otherrequire_asked_name" class="inputsizeS" runat="server" />&nbsp;討論,請分案予前述法務同仁<br />
                                            <asp:CheckBox ID="rb_other_3" runat="server" Text="3.本案請法務同仁僅提供法律原則意見,毌庸修改契約文字" /><br />
                                            <asp:CheckBox ID="rb_other_4" runat="server" Text="4.請法務同仁僅提供本院常用契約(草稿)供參考" /><br />
                                            <asp:CheckBox ID="rb_other_T" runat="server" Text="5.其他。" />
                                            <asp:TextBox ID="txt_otherrequire_desc" runat="server" Width="530px" TextMode="MultiLine" Height="60px" onkeyup="autogrow(this);"></asp:TextBox>
                                            <%--  <asp:SqlDataSource ID="SDS_oRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">附件資料</div>
                                        </td>
                                        <td>
                                            <span class="stripeMe">
                                                <asp:Button class="ajax_mesg genbtnS" ID="btnFilesUpload2" runat="server" Text="新增"></asp:Button>&nbsp;
                   <img src="../images/tooltiphint.gif" class="itemhint" title="契約需求申請<hr>1.當使用客戶契約簽約時，請上傳客戶契約，謝謝！<br>2.如有相關文件送法務承辦同仁參考時，亦請一併上傳。" />
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="功能">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_tcdf_no" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                                                <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>刪除</asp:LinkButton>
                                                                <asp:LinkButton ID="LB_edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'>維護</asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="LinkButton1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="250px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="修改概要">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_2" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="300px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="常用版本">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_3" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="70px" HorizontalAlign="Center"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="撰寫人">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_4" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="LB_1" runat="server" Text='<%#Server.HtmlEncode(Eval("tcdf_up_date").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="70px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                                <%-- <asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                                <asp:Button class="ajax_mesg genbtnS" ID="btnSaveDraft2" runat="server" Text="暫存草稿" OnClick="btnSaveDraft_Click"></asp:Button>&nbsp;
                                 <%--OnClick="btnSendApply_Click"--%>
                                <asp:Button class="ajax_簽核 genbtnS" ID="btnSendApply2" runat="server" Text="送出需求單" OnClick="btnSendApply_Click"></asp:Button>&nbsp;
                                <asp:Image ID="IMG_send" runat="server" ImageUrl="../images/loding2.gif" Visible="false" />
                                <asp:Button class="ajax_mesg genbtnS" ID="btnDelete2" runat="server" Text="刪除" Visible="False" OnClick="btnDelete2_Click"></asp:Button>
                            </div>
                            <asp:HiddenField ID="h_ECP_success" runat="server" />
                        </div>
                        <div class="uplineT1">
                            <span class="gentablenoline">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_keyin_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">建檔日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_keyin_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_emp_name" runat="server"></asp:Label>|<asp:Label ID="lb_modify_emp_no" runat="server"></asp:Label>|<asp:Label ID="lb_modify_tel" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">上次修改日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_modify_date" runat="server"></asp:Label></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件日期</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="lb_send_date" runat="server"></asp:Label></td>
                                    </tr>
                                </table>
                            </span>
                        </div>
                        <!-- uplineT1 -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc1:Foot runat="server" ID="Foot" />

        <%--        <asp:SqlDataSource ID="SDS_NR" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
        <asp:SqlDataSource ID="SDS_log" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>

    <script type="text/javascript">
        $(document).ready(function () {
      
            jQuery("#Form1").validationEngine({});

            $('#txtContMoney').defaultValue('0');
            $('#txt_promoter_name').defaultValue('請挑選承辦人');
            $('#txt_name').defaultValue('請輸入契約名稱');
            $(".itemhint").tooltip({
                track: true,
                position: { my: "left+15 center", at: "right center" },
                //讓tooltips內可以放置HTML CODE
                content: function () {
                    return $(this).prop('title');
                }
            });
            $(".inputhint").tooltip({
                position: { my: "left+10 bottom+40", at: "left bottom " },
                tooltipClass: "custom-tooltip-styling",
                //讓tooltips內可以放置HTML CODE
                content: function () {
                    return $(this).prop('title');
                }
            });
            //說明dialog
            $("#pagehow01").dialog({
                modal: true,
                position: ["center", 100],
                width: 500,
                height: 300,
                autoOpen: false,
                show: {
                    duration: 300
                },
                hide: {
                    duration: 300
                }
            });
            // $("#txtSignReason").change(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //});
            //$("img.help_txtSignReason").mouseover(function () {
            //    $("img.help_txtSignReason").css("background-color", "yellow");
            //    $("img.help_txtSignReason").attr("title", $("#txtSignReason").val());
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //    $("img.help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
            //}); 
        });
        $(function () {
            $(".pickdate").datepicker({
                changeMonth: true,
                changeYear: true,
                dateFormat: 'yy/mm/dd',
                monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                showButtonPanel: true,
                closeText: '關閉',
                currentText: '移至今天'

            });

            // hack to add clear button
            // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
            //wrap up the redraw function with our new shiz
            var dpFunc = $.datepicker._generateHTML; //record the original
            $.datepicker._generateHTML = function (inst) {
                var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                //locate the button panel and add our button - with a custom css class.
                $('.ui-datepicker-buttonpane', thishtml).append(
                    $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                    ).click(function () {
                        inst.input.attr('');
                        inst.input.attr('value', '');
                        inst.input.datepicker('hide');
                    })
                );
                thishtml = thishtml.children(); //remove the wrapper div
                return thishtml; //assume okay to return a jQuery
            };
            // 增加清除按鈕 -End				
        });
        /*
                $(document).ready(function () {
                    $('a.help_form').cluetip({ splitTitle: '|' });
                    $('a.help_reqdate').cluetip({ splitTitle: '|' });
                    $('a.help_confirm_date').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_right').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_share').cluetip({ width: '500px', splitTitle: '|' });
                    $('a.help_other').cluetip({ width: '500px', splitTitle: '|' });
                });
        */
     
    </script>


</body>
</html>
