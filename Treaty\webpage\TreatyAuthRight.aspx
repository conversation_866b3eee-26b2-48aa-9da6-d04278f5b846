﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyAuthRight.aspx.cs" Inherits="TreatyAuthRight" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <link href="../css/colorbox.css" rel="stylesheet" />
    <script type="text/javascript">
        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }

        var xGuid = newGuid();
        function Find_Empno(obj, arg_sw) {
            $(".ajax_empno").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    if (arg_sw == "1") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback1);
                    }
                    if (arg_sw == "2") {
                        $.getJSON(strURL + '?callback=?', jsonp_callback2);
                    }
                }
            });

            $(".ajax_kw").colorbox({
                href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#' + obj).val())
                , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , title: '單人挑選'
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                    $.getJSON(strURL + '?callback=?', jsonp_callback1);
                }
            });

        }
        function Find_empno_kw(obj, arg_sw) {
            var strURL = "../../comp/EmployeeSingleSelect/ret_employee_kw.aspx?keyword=" + escape($('#' + obj).val());

            if (arg_sw == "2") {
                $.getJSON(strURL + '&callback=?', jsonp_callback2);
            }
        }
        function jsonp_callback2(data) {
            switch (data.c_com_cname) {
                case "danger":
                    alert("有危險字眼!");
                    $('#h_px_empno').val("");
                    $('#txt_px_name').val("");
                    break;
                case "error0":
                    alert("查無此人 或 空值!");
                    $('#h_px_empno').val("");
                    $('#txt_px_name').val("");
                    break;
                case "error2":
                    $.colorbox({
                        href: "../../comp/EmployeeSingleSelect/EmployeeSingleWindow.aspx?cname=" + escape($('#txt_px_name').val())
                        , iframe: true, width: "700px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                        , title: '單人挑選'
                        , onClosed: function () {
                            $('#h_px_empno').val("");
                            $('#txt_px_name').val("");
                            $('html, body').css('overflow', '');
                            var strURL = '../../comp/EmployeeSingleSelect/ret_employee_kw.aspx';
                            $.getJSON(strURL + '?callback=?', jsonp_callback2);
                        }
                    });

                    break;
                default:
                    $('#h_px_empno').val(data.c_com_empno);
                    $('#txt_px_name').val(data.c_com_cname);
            }
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <table border="0" width="500px">

            <tr>
                <td>新進人員:<asp:TextBox ID="txt_px_name" runat="server" Width="65px" />
                    <asp:HiddenField ID="h_px_empno" runat="server" />
                    <a onclick="javascript:Find_Empno('txt_px_name','2');">
                        <asp:Image ID="Image3" runat="server" ImageUrl="../images/icon_search.gif" BorderWidth="0" class="ajax_empno btn_mouseout" /></a><br />
                </td>
                <td>複製目前:
                    <asp:DropDownList ID="DDL_list" runat="server" DataTextField="emp_name" DataValueField="emp_no"></asp:DropDownList>的權限
             <%--<asp:SqlDataSource ID="SDS_list"   runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT  emp_no, emp_name FROM   treaty_buztbl" />--%>
                </td>

                <td>
                    <asp:Button ID="BT_add" runat="server" Text="新增" OnClick="BT_add_Click" /></td>
            </tr>
        </table>


        <cc1:SmartGridView ID="gv_list" OnRowCommand="gv_role_list_RowCommand" OnRowDataBound="gv_list_RowDataBound" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" AllowSorting="True" OnSorting="gv_list_Sorting" Width="500px">
            <FooterStyle Font-Bold="True" ForeColor="Black" />
            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
            <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
            <AlternatingRowStyle CssClass="TRowEven" />
            <Columns>
                <asp:TemplateField HeaderText="維護">
                    <ItemTemplate>
                        <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("m_sno") %>'>刪除</asp:LinkButton>
                    </ItemTemplate>
                    <HeaderStyle Width="60px" HorizontalAlign="Center" ForeColor="Black" />
                </asp:TemplateField>
                <asp:BoundField DataField="emp_name" HeaderText="人名">
                    <HeaderStyle Width="150px"></HeaderStyle>
                </asp:BoundField>
                <asp:BoundField DataField="角色" HeaderText="角色" SortExpression="角色">
                    <HeaderStyle Width="150px"></HeaderStyle>
                    <ItemStyle HorizontalAlign="Center" />
                </asp:BoundField>
                <asp:TemplateField HeaderText="接NDA">

                    <ItemTemplate>
                        <asp:DropDownList ID="DDL_NDA" runat="server" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate">
                            <asp:ListItem Value="1">●</asp:ListItem>
                            <asp:ListItem Value="0">　╳</asp:ListItem>
                        </asp:DropDownList>
                    </ItemTemplate>
                    <HeaderStyle Width="50px" />
                    <ItemStyle HorizontalAlign="Right" />
                </asp:TemplateField>
            </Columns>
            <EmptyDataTemplate>
                <!--當找不到資料時則顯示「無資料」-->
                <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料，請新增!"></asp:Label>
            </EmptyDataTemplate>
            <FooterStyle BackColor="White" />
            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
        </cc1:SmartGridView>
        <%--       <asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" ></asp:SqlDataSource>
       <asp:SqlDataSource ID="SDS_NDA" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" ></asp:SqlDataSource>
 <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
