﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Search_inner.aspx.cs" Inherits="Search_inner" EnableEventValidation="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="~/Treaty/userControl/Foot.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }
        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        //$(function () { $('a.iterm_file').cluetip({ activation: 'click', width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' }); });
        function file_View(fid) {
            $(".iterm_file").colorbox({
                href: "./TreatyCase_AllFile.aspx?contno=" + fid
                , title: '議約案件相關檔案'
                , iframe: true, width: "880px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }
        function doNewCase(seno, contno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyApply_NewVer.aspx?contno=" + contno
                , iframe: true, width: "440px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    if ($.colorbox.data != "undefined") {
                        if ($.colorbox.data != "") {
                            if (contno.indexOf("Q") > 0)
                                window.location = "./TreatyApplyQ.aspx?seno=" + seno + "&newver=" + $.colorbox.data;
                            else
                                window.location = "./TreatyApply.aspx?seno=" + seno + "&newver=" + $.colorbox.data;
                        }
                    }
                }
            });
        }
        function showCompInfoDialog(Compno) {
            var newopen = window.open('https://cust.itri.org.tw/mgrcust_custdata.aspx?comp_idno=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align='right'>單位別：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="orgcd_name" DataValueField="orgcd"></asp:DropDownList>
                                        </td>
                                        <td align='right'>案件類型：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlCaseStyle" runat="server" Width="150px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList>
                                        </td>
                                        <td align='right'>契約性質：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right' nowrap>承辦法務人員：</td>
                                        <td>
                                            <asp:TextBox ID="tbxHandleName" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right'>單位承辦人員：</td>
                                        <td>
                                            <asp:TextBox ID="tbxPromoterName" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right' nowrap>重大效益案件：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlImportant" runat="server">
                                                <asp:ListItem Value="-1">全部</asp:ListItem>
                                                <asp:ListItem Value="1">是</asp:ListItem>
                                                <asp:ListItem Value="0">否</asp:ListItem>
                                            </asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align='right'>案源：</td>
                                        <td colspan="3">
                                            <asp:CheckBoxList ID="cbxCaseClass" runat="server" RepeatDirection="Horizontal" BorderWidth="0px" RepeatLayout="Flow" RepeatColumns="5" Width="420px">
                                                <asp:ListItem Value="N">洽案系統</asp:ListItem>
                                                <asp:ListItem Value="R">標案系統</asp:ListItem>
                                                <asp:ListItem Value="M">NDA</asp:ListItem>
                                                <asp:ListItem Value="A">國外無收入</asp:ListItem>
                                                <asp:ListItem Value="F">國內無收入</asp:ListItem>
                                                <asp:ListItem Value="C">工服</asp:ListItem>
                                                <asp:ListItem Value="S">新創事業</asp:ListItem>
                                                <asp:ListItem Value="L">智權糾紛</asp:ListItem>
                                                <asp:ListItem Value="Q">契約及法律問題</asp:ListItem>
                                                <asp:ListItem Value="T">其它</asp:ListItem>
                                            </asp:CheckBoxList>
                                            <span style="vertical-align: top;">
                                                <asp:CheckBox ID="CB_常用版本" runat="server" Text="常用版本" />
                                                &nbsp;&nbsp;&nbsp; </span>

                                        </td>
                                        <td align='right'>案件顯示：</td>
                                        <td>
                                            <asp:DropDownList ID="ddlShowCase" runat="server" Width="96px">
                                                <asp:ListItem Value="0" Selected="True">全部件次</asp:ListItem>
                                                <asp:ListItem Value="1">最新件次</asp:ListItem>
                                            </asp:DropDownList></td>
                                    </tr>
                                    <tr>
                                        <td align='right'>狀態：</td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="cbxCaseStatus1" runat="server" Text="未結件" Checked="True"></asp:CheckBox>&nbsp;&nbsp; 
		       		<asp:CheckBox ID="cbxCaseStatus2" runat="server" Text="結件"></asp:CheckBox>&nbsp;&nbsp; 
		       		<asp:CheckBox ID="cbxCaseStatus3" runat="server" Text="需求取消"></asp:CheckBox>&nbsp;&nbsp;&nbsp; 
		       		<asp:CheckBox ID="cbxOverDate" runat="server" Text="逾期"></asp:CheckBox>&nbsp;&nbsp; 
		       		<asp:CheckBox ID="CB_急件" runat="server" Text="急件"></asp:CheckBox>&nbsp;&nbsp; 
                    <asp:CheckBox ID="CB_陸資" runat="server" Text="陸資"></asp:CheckBox>&nbsp;&nbsp; 
                    <asp:CheckBox ID="CB_計價" runat="server" Text="計價"></asp:CheckBox>&nbsp;&nbsp; 
                                        </td>
                                        <td align='right'>收文日期：</td>
                                        <td>
                                            <asp:TextBox ID="txtReceiveSDate" runat="server" MaxLength="8" Width="75px" class="pickdate inputex inputsizeS"></asp:TextBox>
                                            &nbsp;~ 
		       		<asp:TextBox ID="txtReceiveEDate" runat="server" MaxLength="8" Width="75px" class="pickdate inputex inputsizeS"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align="right">關鍵字查詢： </td>
                                        <td>
                                            <asp:TextBox ID="txtKeyWord" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right'>客戶名稱：</td>
                                        <td>
                                            <asp:TextBox ID="txtCompname" runat="server" Width="150px" MaxLength="20"></asp:TextBox></td>
                                        <td align='right'>結件日期：</td>
                                        <td>
                                            <asp:TextBox ID="txtCloseSDate" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox>
                                            &nbsp;~ 
		       		<asp:TextBox ID="txtCloseEDate" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td align='right'>修約類：</td>
                                        <td>
                                            <asp:DropDownList ID="ddl_amend" runat="server">
                                                <asp:ListItem Value="0">全部</asp:ListItem>
                                                <asp:ListItem Value="1">是</asp:ListItem>
                                                <asp:ListItem Value="2">否</asp:ListItem>
                                            </asp:DropDownList></td>
                                        <td align='right'>預估金額：</td>
                                        <td>
                                            <asp:DropDownList ID="DDL_money" runat="server">
                                                <asp:ListItem Value=""></asp:ListItem>
                                                <asp:ListItem Value="1">100萬以下</asp:ListItem>
                                                <asp:ListItem Value="2">100萬~300萬</asp:ListItem>
                                                <asp:ListItem Value="3">300萬~500萬</asp:ListItem>
                                                <asp:ListItem Value="4">500萬~1000萬</asp:ListItem>
                                                <asp:ListItem Value="5">1000萬以上</asp:ListItem>
                                            </asp:DropDownList>
                                        </td>
                                        <td colspan="2" class="td_right">
                                            <asp:CheckBox ID="CB_成果有特殊限制者" runat="server" Text="契約內對於成果有特殊限制者" /></td>
                                    </tr>
                                    <tr>
                                        <td  align='right'>
                                            <div class="twocol margin5TB">核心關鍵技術：</div> </td>
                                        <td align='left' nowrap colspan="5"> 
                                            <asp:DropDownList ID="DDL_核心關鍵技術" runat="server">
                                                <asp:ListItem Value="-1">全部</asp:ListItem>
                                                <asp:ListItem Value="1">是</asp:ListItem>
                                                <asp:ListItem Value="0">否</asp:ListItem>
                                            </asp:DropDownList>／核心關鍵技術_列管迄日：
                                            <asp:TextBox ID="TB_列管迄日_s" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox>&nbsp;~ 
		       		                        <asp:TextBox ID="TB_列管迄日_e" runat="server" Width="75px" MaxLength="8" class="pickdate inputex inputsizeS"></asp:TextBox>
                                        </td>
                                         
                                    </tr>

                                    <tr><td colspan="1" class="td_left">
                                        </td>
                                        <td colspan="5" class="td_right">
                                            <asp:Button ID="BT_報院條件" runat="server" Class="genbtnS" Text="報院條件" OnClick="BT_報院條件_Click" />
                                            <asp:Button ID="btnExcel" runat="server" Class="genbtnS" Text="匯出Excel" OnClick="btnExcel_Click"></asp:Button>
                                            <asp:Button ID="btnPDF" runat="server" Class="genbtnS" Text="匯出PDF" OnClick="btnPDF_Click"></asp:Button>
                                            <asp:Button ID="btnQuery" TabIndex="1" runat="server" Text="查詢" Class="genbtnS" OnClick="btnQuery_Click"></asp:Button>
                                        </td>
                                    </tr>



                                    <tr>
                                        <td colspan="6">
                                            <div class="twocol margin5TB">
                                                <span class="stripeMe">
                                                    <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" OnRowCommand="SGV_search_RowCommand" OnRowDataBound="SGV_search_RowDataBound" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated">
                                                        <FooterStyle BackColor="White" />
                                                        <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                                        <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                                        <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                                        <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="標記">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LT_標記" runat="server"></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="新件">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LT_新件次" runat="server"></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="單&lt;BR&gt;位" SortExpression="org_name">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_單位" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("org_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="洽案／契約(案件)名稱" SortExpression="tmp_case_name">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LB_View" runat="server" CommandName="View" CommandArgument='<%# Eval("tc_seno")+";"+Eval("tmp_case_actno") %>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_actno").ToString())) %>'></asp:LinkButton>
                                                                    <asp:Literal ID="LT_常用版本" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_flag").ToString())) %>'></asp:Literal>
                                                                    <asp:Literal ID="LT_擬約幫手" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_擬約幫手").ToString())) %>'></asp:Literal><br />
                                                                    <asp:Label ID="LB_case_name" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_case_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="245px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="跳票">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_退換票" runat="server" Text='<%# oRCM.RemoveXss( Eval("tmp_退換票").ToString() ) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="逾資本額½">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_資本額" runat="server" Text='<%#   oRCM.RemoveXss(Eval("tmp_資本額").ToString())  %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="資產遭查封">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_資產遭查封" runat="server" Text='<%#   oRCM.RemoveXss(Eval("tmp_資產遭查封").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="抽換票">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_抽換票" runat="server" Text='<%#   oRCM.RemoveXss(Eval("tmp_抽換票").ToString())  %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="其他風險">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_其他風險" runat="server" Text='<%#   oRCM.RemoveXss(Eval("tmp_其他風險").ToString())  %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="含陸資">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_含陸資" runat="server" Text='<%#  oRCM.RemoveXss(Eval("陸資").ToString())  %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="客戶名稱" SortExpression="tc_compname">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="LB_客戶名稱" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_compname").ToString())) %>' Visible="false"></asp:Literal>
                                                                    <asp:LinkButton ID="LBx_客戶名稱" runat="server" Compno='<%# Eval("tc_compidno")%>' Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_compname").ToString())) %>' OnClick="LBx_客戶名稱_Click"></asp:LinkButton>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="150px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="收文日&lt;br&gt;結件日期">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_prefinish_date" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_petition_day").ToString())) %>'></asp:Label><hr />
                                                                    <asp:Label ID="LB_case_closedate" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_case_closedate").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="承辦人&lt;br&gt;法務人員" SortExpression="tc_handle_name">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_承辦人" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_promoter_name").ToString())) %>'></asp:Label><br />
                                                                    <asp:Label ID="LB_法務承辦人員" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tc_handle_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="70px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tmp_other_status_name" SortExpression="tmp_other_status_name" HeaderText="案源狀態"></asp:BoundField>
                                                            <asp:TemplateField HeaderText="契約性質<br>(案件分類)" SortExpression="tmp_conttype_name">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="Label1" runat="server" Text='<%#  Server.HtmlEncode(oRCM.RemoveXss(Eval("tmp_conttype_name").ToString())) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <ItemStyle Width="100px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="tc_money" SortExpression="tc_money" HeaderText="預估金額" ItemStyle-HorizontalAlign="Right">
                                                                <ItemStyle HorizontalAlign="Right"></ItemStyle>
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="重大&lt;br&gt;效益">
                                                                <ItemTemplate>
                                                                    <asp:DropDownList ID="DDL_Hec" runat="server" AutoPostBack="True" OnSelectedIndexChanged="HecUpdate" Visible="false">
                                                                        <asp:ListItem Value="1">是</asp:ListItem>
                                                                        <asp:ListItem Value="0">否</asp:ListItem>
                                                                    </asp:DropDownList>
                                                                </ItemTemplate>
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="檢視&lt;br&gt;附件">
                                                                <ItemTemplate>
                                                                    <a href="#" class='iterm_file' onclick="file_View('<%#((string)Eval("tmp_case_actno")).Replace("-", "") %>');" title="相關編號全部檔案">附件</a>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Right" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>
                                                            <!--當找不到資料時則顯示「無資料」-->
                                                            <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                                        </EmptyDataTemplate>
                                                        <FooterStyle BackColor="White" />
                                                        <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    </cc1:SmartGridView>
                                                </span>
                                        </td>
                                    </tr>

                                </table>


                        </div>
                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <div style="display: none">
            <rsweb:ReportViewer ID="ReportViewer1" runat="server" Font-Names="Verdana" Font-Size="8pt" WaitMessageFont-Names="Verdana" WaitMessageFont-Size="14pt" Width="1460px" ShowExportControls="False" />
            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        </div>
        <uc1:Foot runat="server" ID="Foot" />
        <script type="text/javascript">
            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yymmdd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    yearRange: '2010:2030',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.val(''); //attr value chrome not work
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });
            $(document).ready(
                function () {
                    $(document).ready(function () { $('.headernews').scrollbox({ delay: 4 }); });
                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    $(".inputhint").tooltip({
                        position: { my: "left+10 bottom+40", at: "left bottom " },
                        tooltipClass: "custom-tooltip-styling",
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        // position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                    $(".itemhint").tooltip({
                        track: true,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () {
                            return $(this).prop('title');
                        }
                    });
                    //說明dialog
                    $("#pagehow01").dialog({
                        modal: true,
                        //position: ["center", 100],
                        width: 500,
                        height: 300,
                        autoOpen: false,
                        show: {
                            duration: 300
                        },
                        hide: {
                            duration: 300
                        }
                    });

                });
            $(document).ready(function () {
                $(".accordionblock").hide();
                //個別按鈕操作
                $(".itemcontrolbtn").click(function () {
                    //切換子項顯示與隱藏
                    $(this).parent().parent().next(".accordionblock").slideToggle();
                    //ICON樣式切換
                    $(this).toggleClass("iconup");
                    //文字切換  ?:運算式是if else的快捷方式
                    //$(this).text($(this).text() == '展開項目' ? '收合項目' : '展開項目');
                });
                //全部展開
                $(".AllControlOpen").click(function () {
                    $(".accordionblock").slideDown();
                    //$(".itemcontrolbtn").text('收合項目');
                    $(".itemcontrolbtn").addClass("iconup")
                });
                //全部收合
                $(".AllControlClose").click(function () {
                    $(".accordionblock").slideUp();
                    //$(".itemcontrolbtn").text('展開項目');
                    $(".itemcontrolbtn").removeClass("iconup")
                });
            });
        </script>
    </form>
</body>
</html>
