﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase2_CourtAdd.aspx.cs" Inherits="Treaty_webpage_TreatyCase2_CourtAdd" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("新增成功!");
            parent.$.fn.colorbox.close();
        }
    </script>
    <style type="text/css">
        .empty {
            color: #aaa;
        }

        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">

        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 40px">
                <tr>
                    <td class="td_right">法律/檢察署：</td>
                    <td>
                        <asp:DropDownList ID="DDL_court" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" AppendDataBoundItems="True"></asp:DropDownList>
                        <%--<asp:SqlDataSource ID="SDS_court" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">年度：</td>
                    <td>
                        <asp:TextBox ID="TB_year" runat="server" class="inputsizeS" MaxLength="3"></asp:TextBox>
                        ex:100</td>
                </tr>
                <tr>
                    <td class="td_right">字：</td>
                    <td>
                        <asp:TextBox ID="TB_word" runat="server" class="inputsizeS" MaxLength="4"></asp:TextBox>
                        ex:1234</td>
                </tr>
                <tr>
                    <td class="td_right">號：</td>
                    <td>
                        <asp:TextBox ID="TB_no" runat="server" class="inputsizeS" MaxLength="5"></asp:TextBox>
                        ex:12345</td>
                </tr>

                <tr>
                    <td class="td_right">股別：</td>
                    <td>
                        <asp:TextBox ID="TB_stock" runat="server" class="inputsizeS" MaxLength="2"></asp:TextBox>
                        ex:XX</td>
                </tr>

                <tr>
                    <td class="td_right" colspan="2">
                        <div style="float: right">
                            <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" />&nbsp;
                        </div>
                    </td>
                </tr>
            </table>

            <%--   <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />
            <asp:SqlDataSource ID="SDS_auth" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>

        </span>

        <script type="text/javascript">

</script>
    </form>
</body>
</html>
