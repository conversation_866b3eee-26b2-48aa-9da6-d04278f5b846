﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;
using System.Web.UI.WebControls;

public partial class Treaty_manager_tip_view : System.Web.UI.Page
{

    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request.QueryString["tid"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["tid"].ToString()) || (Request.QueryString["tid"].Length > 5))
                    Response.Redirect("../danger.aspx");
                ViewState["tid"] = Request.QueryString["tid"].ToString();
                BindComp();
                BindFileList();
            }

            if (Request.QueryString["tdf_id"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["tdf_id"].ToString()) || (Request.QueryString["tdf_id"].Length > 5))
                    Response.Redirect("../danger.aspx");
                string tdf_id = Request.QueryString["tdf_id"].ToString();
                FileDownload(tdf_id);
            }
        }


    }

    private void BindComp()
    {
        DataTable dt = new DataTable();
        try
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"esp_treaty_TechCase_tip");
                oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(ViewState["tid"].ToString()));
                oCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@tip_title", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@tipContent", oRCM.SQLInjectionReplaceAll(""));
                oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("View"));
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.CommandTimeout = 0;

                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                oda.Fill(dt);
            }
        }
        catch (Exception ex)
        {
            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

            oRCM.ErrorExceptionDataToDB(logMail);
        }
        System.Data.DataView dv_search = dt.DefaultView;
        if (dv_search.Count >= 1)
        {

            TB_content.Text = Server.HtmlDecode(Server.HtmlEncode(dv_search[0]["tip_content"].ToString()));

        }
    }

    private void BindFileList()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(ViewState["tid"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mod", "file_list");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    private DataTable File_View(string tdf_id)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mod", "file_view");
            sqlCmd.Parameters.AddWithValue("@tdf_id", oRCM.SQLInjectionReplaceAll(tdf_id));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    private void FileDownload(string tdf_id)
    {
        if (oRCM.IsPC(Request) == false)
        {
            Response.Redirect("../DownloadFail.aspx");
        }
        string str_file_url = "";
        string str_filename = "";
        DataTable dt = File_View(tdf_id);
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            str_file_url = dv[0]["tdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
            str_filename = dv[0]["tdf_filename"].ToString().Trim();
        }
        if (str_file_url != "")
        {

            Response.Clear();
            Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
            Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
            Response.Flush();
            Response.End();
        }
    }
}
