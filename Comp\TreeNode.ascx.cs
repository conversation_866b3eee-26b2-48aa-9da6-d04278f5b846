﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data.SqlClient;
public partial class Comp_TreeNode : System.Web.UI.UserControl
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public string SysId
    {
        set
        {
            LT_SysID.Text = value;
        }
    }

    protected void Page_Load(object sender, EventArgs e)
    {

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //common com = new common();
        ViewState["empno"] = ssoUser.empNo;
        MainTree();
    }
    private void MainTree()
    {
        //sds_TreeMenu.SelectParameters.Clear();
        //sds_TreeMenu.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //sds_TreeMenu.SelectCommand = "esp_APMS_tree";
        //sds_TreeMenu.SelectParameters.Add("empno", ViewState["empno"].ToString());
        //sds_TreeMenu.SelectParameters.Add("SysId", LT_SysID.Text);
        //sds_TreeMenu.SelectParameters.Add("pid", "0");
        //for (int i = 0; i < this.sds_TreeMenu.SelectParameters.Count; i++)
        //{
        //    sds_TreeMenu.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //sds_TreeMenu.DataBind();
        //System.Data.DataView dv = (DataView)sds_TreeMenu.Select(new DataSourceSelectArguments());
        DataTable dt = new DataTable();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_APMS"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_APMS_tree";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("SysId", oRCM.SQLInjectionReplaceAll(LT_SysID.Text));
            sqlCmd.Parameters.AddWithValue("pid", oRCM.SQLInjectionReplaceAll("0"));


            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_APMS"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion        
        DataView dv = dt.DefaultView;
        LT_tree.Text = "<div class='nav'><ul id='nav_js'>";
        if (dv.Count >= 1)
        {
            for (int s_count = 0; s_count < dv.Count; s_count++)
            {
                LT_tree.Text += "<li><a href='" + Server.HtmlEncode(dv[s_count]["t_nodeSideURL"].ToString()) + Server.HtmlEncode(dv[s_count]["t_nodeURL"].ToString()) + "' target='" + Server.HtmlEncode(dv[s_count]["t_nodeTarget"].ToString()) + "' title=''>" + Server.HtmlEncode(dv[s_count]["t_nodeName"].ToString()) + "</a>";
                if (dv[s_count]["t_nid"].ToString() != "0")
                    TreeChild(dv[s_count]["t_nid"].ToString());
                LT_tree.Text += "</li>";
            }
        }
        LT_tree.Text += "</ul></div>";
    }
    protected void TreeChild(string pid)
    {
        //sds_TreeMenu.SelectParameters.Clear();
        //sds_TreeMenu.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //sds_TreeMenu.SelectCommand = "esp_APMS_treeChild";
        //sds_TreeMenu.SelectParameters.Add("empno", ViewState["empno"].ToString());
        //sds_TreeMenu.SelectParameters.Add("SysId", LT_SysID.Text);
        //sds_TreeMenu.SelectParameters.Add("pid", pid);
        //for (int i = 0; i < this.sds_TreeMenu.SelectParameters.Count; i++)
        //{
        //    sds_TreeMenu.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //sds_TreeMenu.DataBind();
        //System.Data.DataView dv = (DataView)sds_TreeMenu.Select(new DataSourceSelectArguments());

        DataTable dt = new DataTable();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_APMS"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_APMS_treeChild";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("SysId", oRCM.SQLInjectionReplaceAll(LT_SysID.Text));
            sqlCmd.Parameters.AddWithValue("pid", oRCM.SQLInjectionReplaceAll(pid));


            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_APMS"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion        
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            LT_tree.Text += "<ul>";
            for (int s_count = 0; s_count < dv.Count; s_count++)
            {
                LT_tree.Text += "<li><a href='" + Server.HtmlEncode(dv[s_count]["t_nodeSideURL"].ToString() + dv[s_count]["t_nodeURL"].ToString()) + "' target='" + Server.HtmlEncode(dv[s_count]["t_nodeTarget"].ToString()) + "' title=''>" + Server.HtmlEncode(dv[s_count]["t_nodeName"].ToString()) + "</a>";
                if (dv[s_count]["t_nid"].ToString() != "0")
                    TreeChild(dv[s_count]["t_nid"].ToString());
                LT_tree.Text += "</li>";
            }
            LT_tree.Text += "</ul>";
        }
        else
            return;
    }
}