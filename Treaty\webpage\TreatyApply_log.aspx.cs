﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

public partial class TreatyApply_log : Treaty.common  
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private void Page_Load(object sender, System.EventArgs e)
    {
        //this.SDS_log.SelectParameters.Clear();
        //this.SDS_log.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_log.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //this.SDS_log.SelectParameters.Add("customers", TypeCode.String, ViewState["Customers"].ToString());
        //this.SDS_log.DataBind();
        //SGV_log.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(ViewState["Customers"].ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);

                SGV_log.DataSource = dt;
                SGV_log.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }



    protected void btnExit_Click1(object sender, EventArgs e)
    {
        this.ClientScript.RegisterStartupScript(typeof(string), "", "<script>parent.$.colorbox.settings.data = '" + ViewState["Customers"].ToString() + "'; parent.$.colorbox.close();</script>");
    }
}