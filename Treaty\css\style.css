@charset "utf-8";
/* common */
*{font-family:<PERSON><PERSON><PERSON>, Microsoft JhengHei,微軟正黑體, Arial, Helvetica, sans-serif;}
html,body{height:100%; margin:0; padding:0;}
body{font-size:13px; margin:0;background:url(../images/bgbody.gif) repeat-x;}
h1,h2,h3,h4,h5,h6{margin:0; padding:0;}

h1{font-size:3em;}
h2{font-size:1.615em;}
h3{font-size:1.154em;}

.Wrapper<PERSON>ooter{background:#1086c1; }
.WrapperFooter .footerblock{text-align:center; padding:10px 0;}
.WrapperContent{padding:0 5px;}
.WrapperContent .WrapperHeader{height:110px;}
.WrapperContent .WrapperHeader .logo{position:absolute; margin-top:5px;}
.WrapperContent .WrapperHeader .headernews{position:absolute; margin-top:55px; height:17px; overflow: hidden;}
.WrapperContent .WrapperHeader .headernews ul{margin:0 0 0 -40px; *margin:0; _margin:0;}
.WrapperContent .WrapperHeader .headernews li{list-style:none;}
.WrapperContent .WrapperHeader .infolink{text-align:right; padding-top:5px; position:relative; right:0;}
.WrapperContent .WrapperHeader .basemenu{position:absolute;margin-top:55px;}
.WrapperContent .WrapperHeader .staffmenucontrol{position:absolute; margin-top:65px;}
.WrapperContent .WrapperMain .outlineblock{ border:1px solid #d2e1e7; background:#f3f7f8; padding:5px;}

.fortreemenublock{width:260px; padding:5px; height:270px; overflow-y:scroll; background:#fff; border:1px solid #CCC; margin-bottom:10px;}
.bgstep{background:url(../images/bgstep.gif) no-repeat; text-align:center; width:139px; height:28px; padding-top:5px;}
.bgstepS{background:url(../images/bgstepS.gif) no-repeat; text-align:center; width:71px; height:28px; padding-top:5px;}
.bgstepL{background:url(../images/bgstepL.gif) no-repeat; text-align:center; width:209px; height:28px; padding-top:5px;}
.bgsteplink{background:url(../images/bgsteplink.gif) no-repeat; text-align:center; width:139px; height:28px; padding-top:5px;}
.bgsteplinkS{background:url(../images/bgsteplinkS.gif) no-repeat; text-align:center; width:71px; height:28px; padding-top:5px;}
.bgsteplinkL{background:url(../images/bgsteplinkL.gif) no-repeat; text-align:center; width:209px; height:28px; padding-top:5px;}
.bgstepblue{background:url(../images/bgstepblue.gif) no-repeat; text-align:center; width:139px; height:28px; padding-top:5px;}
.bgstepblueS{background:url(../images/bgstepblueS.gif) no-repeat; text-align:center; width:71px; height:28px; padding-top:5px;}
.bgstepblueL{background:url(../images/bgstepblueL.gif) no-repeat; text-align:center; width:209px; height:28px; padding-top:5px;}


.secondaccordionblock{padding-left:40px;}
.itemcontrolbtn{background:url(../images/icon-open.png) no-repeat; font-weight:normal; margin-right:3px; cursor:pointer; color:#0099CC;}
.itemcontrolbtn:hover{color:#039;}
.itemcontrolbtn2{background:url(../images/icon-open.png) no-repeat; font-weight:normal; margin-right:3px; cursor:pointer; color:#0099CC;}
.itemcontrolbtn2:hover{color:#039;}
.itemcontrolbtnS2{background:url(../images/icon-open.png) no-repeat; font-weight:normal; margin-right:3px; cursor:pointer; color:#0099CC;}
.itemcontrolbtnS2:hover{color:#039;}
.iconup{background:url(../images/icon-close.png) no-repeat; font-weight:normal; margin-right:3px;}
.AllControlOpen{cursor:pointer; color:#0099CC;}
.AllControlOpen:hover{color:#039;}
.AllControlClose{cursor:pointer; color:#0099CC;}
.AllControlClose:hover{color:#039;}
/* case tabs */
.tabmenublock{padding:0;}
.SlimTabBtnCurrent{margin:0; padding:5px 15px; background:#FFFFFF; border:1px solid #d3d3d3; border-bottom:0; font-weight:bold;}
.SlimTabBtn{margin:0; padding:0;}
.SlimTabBtn a{margin:0;color:#0075a1; text-decoration:none; background:url(../images/bgTab.gif) repeat-x #cee9f6; padding:5px 15px 4px 15px; border:1px solid #d3d3d3;}
.SlimTabBtn a:hover{color:#049fe7; background:#fff;}
.tabsubmenublock{margin:4px 0 0 0; padding:10px 5px 5px 5px; background:#FFFFFF; border:1px solid #d3d3d3;}
.tabsubmenublock{
	margin-top:4px\9;/* all ie */
	margin-top:1px\0;/* ie 8 */
	*margin-top:1px\0;/* ie 7 */
	_margin-top:1px\0;/* ie 6 */
}
/* ie10 11 */
.ie9  .tabsubmenublock{margin-top:4px}
.ie10 .tabsubmenublock{margin-top:4px}
.ie11 .tabsubmenublock{margin-top:4px}
/* btnstyle */
.genbtn{background:url(../images/BgBtnOut.gif) repeat-x bottom #9fdef8; border:1px solid #9fdef8; padding:5px 15px;*padding:5px;_padding:5px;color:#2e6b87; font-size:15px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px; font-weight:bold;}
.genbtnhover{background:url(../images/BgBtnOver.gif) repeat-x bottom #1abcff; border:1px solid #1abcff; padding:5px 15px;*padding:5px;_padding:5px;color:#05336d; font-size:15px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.keybtn{background:url(../images/BginfoBtnOut.gif) repeat-x bottom #ffdd98; border:1px solid #ffdd98; padding:5px 15px;*padding:5px;_padding:5px;color:#87712e; font-size:15px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.keybtnhover{background:url(../images/BginfoBtnOver.gif) repeat-x bottom #ffb51a; border:1px solid #ffb51a; padding:5px 15px;*padding:5px;_padding:5px;color:#491a00; font-size:15px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.infobtn{background:url(../images/BgkeyBtnOut.gif) repeat-x bottom #c8eda9; border:1px solid #c8eda9; padding:5px 15px;*padding:5px;_padding:5px;color:#64872e; font-size:15px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.infobtnhover{background:url(../images/BgkeyBtnOver.gif) repeat-x bottom #84e335; border:1px solid #84e335; padding:5px 15px;*padding:5px;_padding:5px;color:#000; font-size:15px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}

.genbtnS{background:url(../images/BgBtnOut.gif) repeat-x bottom #9fdef8; border:1px solid #9fdef8; padding:3px 10px;*padding:5px;_padding:5px;color:#2e6b87; font-size:12px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px; font-weight:bold;}
.genbtnShover{background:url(../images/BgBtnOver.gif) repeat-x bottom #1abcff; border:1px solid #1abcff; padding:3px 10px;*padding:5px;_padding:5px;color:#05336d; font-size:12px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.keybtnS{background:url(../images/BginfoBtnOut.gif) repeat-x bottom #ffdd98; border:1px solid #ffdd98; padding:3px 10px;*padding:5px;_padding:5px;color:#87712e; font-size:12px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.keybtnShover{background:url(../images/BginfoBtnOver.gif) repeat-x bottom #ffb51a; border:1px solid #ffb51a; padding:3px 10px;*padding:5px;_padding:5px;color:#491a00; font-size:12px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.infobtnS{background:url(../images/BgkeyBtnOut.gif) repeat-x bottom #c8eda9; border:1px solid #c8eda9; padding:3px 10px;*padding:5px;_padding:5px;color:#64872e; font-size:12px; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}
.infobtnShover{background:url(../images/BgkeyBtnOver.gif) repeat-x bottom #84e335; border:1px solid #84e335; padding:3px 10px;*padding:5px;_padding:5px;color:#000; font-size:12px; cursor:pointer; margin:0px 2px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-weight:bold;}

/* pageslide */
#staffmenu{display:none;}
.staffblocktitle{position:absolute; padding:10px 0 0 5px;}
.closeblock{background:#43bbef; width:220px; padding:10px; text-align:right;}
#pageslide {
    /* These styles MUST be included. Do not change. */
    display: none;
    position: absolute;
    position: fixed;
    top: 0;
    height: 100%;
    z-index: 999999;
    /* Specify the width of your pageslide here */
    width: 240px;
	overflow:auto;
	overflow-x: hidden;
    /* These styles are optional, and describe how the pageslide will look */
    background-color: #e6f3f8;
   
}

/* 版面控制項 */
.twocol{ margin-top:10px;}
.twocol:after{display:block; clear:both; content:"."; visibility:hidden; height:0;}
.twocol .right{float:right;}
.twocol .left{float:left;}

.halfliqid{margin:0 auto; max-width:1400px; min-width:970px;}
.fixwidth{width:970px;margin:0 auto;}
.fullliquid{width:100%; min-width:970px; padding:0;}
.menuwidth{width:980px;}
.overheightblock{height:400px;}

/* 間距微調與美編 */
.margin5TB{margin:5px 0;}
.margin10TB{margin:10px 0;}
.underlineT1{border-bottom:1px solid #27afe7; padding-bottom:5px;}
.underlineT2{border-bottom:1px solid #ccc; padding-bottom:5px;}
.uplineT1{border-top:1px dashed #CCCCCC; padding-top:5px; margin-top:10px;}
.titlebackicon{background:url(../images/twocoltitleback.gif) no-repeat right; padding-right:10px;}
/* 覆寫jquery UI */
.custom-tooltip-styling{background:#FFFAD9; border:1px solid #fde549;}
.ui-tooltip{max-width:450px;}

/* 下拉選單 */
.nav{margin-top:0px;margin-top:2px \0;*margin-top:0px;_margin-top:0px; float:right;}
:root .nav{margin-top:0px \0/;}/* IE9 */
.nav ul{padding:0;margin:0;*margin:0;_margin:0;list-style-type:none;}
.nav ul li {float:left; position: relative; border:none;height:24px;}

.nav ul li a{color:#fff; font-weight:bold; font-size:15px; text-decoration:none;padding:10px 15px; height:25px;height:23px \0;*height:23px;_height:23px;}
.nav ul li a:link{text-decoration:none;}
.nav ul li a:visited{text-decoration:none;}
.nav ul li a:hover{text-decoration:none;color:#f1ff11;} 
.nav ul li a:active{text-decoration:none; }
/*navigation layer 2*/
.nav ul li ul{position:absolute;right:0px;top:0px;visibility: hidden;margin:0px;z-index:99;background-image:none;padding:5px 0 0 0;display:block;height:auto;margin-top:0;background:#1c9cd4;}
.nav ul li ul li{margin:0px;padding-left:5px;border:none; text-align:left; height:22px;}
.nav ul li ul li a{display:block;position: relative;font-weight:normal;font-size:13px;width:130px;padding:0 5px 0 0;color:#fff;letter-spacing:1px;
line-height:1.55em;border:none;z-index:1;}


/* 分頁 */
div.pagination {
    overflow: hidden;
    font-size: 9pt;
    padding: 10px 0;
}

div.pagination ul {
    list-style: none;
    padding: 2px 0;
    line-height: 16px;
}

div.pagination li {
    display: inline;
}
div.pagination.black2 {
      text-align:center;
      padding: 7px;
      margin: 3px;
}

div.pagination.black2 a {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #000000;
    text-decoration: none; /* no underline */
    color: #000000;
}

div.pagination.black2 a:hover, div.pagination.black2 a:active {
      border: 1px solid #000000;
      background-color:#000;
      color: #fff;
}

div.pagination.black2 li.current {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #000000;
    font-weight: bold;
    background-color: #000000;
    color: #FFF;
}

div.pagination.black2 li.disabled {
    padding: 2px 5px 2px 5px;
    margin: 2px;
    border: 1px solid #EEE;
    color: #DDD;
}

/* 20140923 add ochison */
#techblock{overflow:hidden;}

/* 20141017 add ochison */
.secretfooter{text-align:center;}
	  
/* 雙色表單 */
.gentable table{border-collapse:collapse;}
.gentable td{padding:5px; border-collapse:collapse; border-bottom:1px dotted #bababa;}
.gentable td td{border:0; padding:5px 0;}

.gentablenoline table{border-collapse:collapse;}
.gentablenoline td{padding:4px; border-collapse:collapse; border:0;}
.gentablenoline td td{border:0; padding:5px 0;}

.stripeMe table{border-collapse:collapse; border:1px solid #9fdef8;}
.stripeMe td{padding:10px 5px; border-collapse:collapse; border:0; border-bottom:1px dashed #bbd4df;}
.stripeMe th{color:#242424; padding:10px 5px; border-collapse:collapse; border:0; background:#9fdef8;}
.stripeMe th a{color:#395FB7; text-decoration:none;}
.stripeMe th a:hover{color:#0087bf; text-decoration:underline;}
.stripeMe tr.alt td{background:#eaf3f6;}
.stripeMe tr.spe td{background:#FFF7E8;}
.stripeMe tr.over td {}
.stripeMe td td{border:0;}
.stripeMefuline table{border-collapse:collapse; border:1px solid #9fdef8;}
.stripeMefuline td{padding:10px 5px; border-collapse:collapse; border:0; border:1px solid #bbd4df;}
.stripeMefuline th{color:#242424; padding:10px 5px; border-collapse:collapse; border:0; background:#9fdef8;}

.stripeMeFC table{border-collapse:collapse; border:1px solid #9fdef8;}
.stripeMeFC td{padding:10px 5px; border-collapse:collapse; border:0; border-bottom:1px dashed #bbd4df;}
.stripeMeFC th{color:#242424; padding:10px 5px; border-collapse:collapse; border:0; background:#9fdef8;}
.stripeMeFC th a{color:#395FB7; text-decoration:none;}
.stripeMeFC th a:hover{color:#0087bf; text-decoration:underline;}
.stripeMeFC tr.alt td{background:#eaf3f6;}
.stripeMeFC tr.stripeMeHideTd td{background:#EEE;}
.stripeMeFC td table{border-collapse:collapse; border:0;}
.stripeMeFC td table th{border-bottom:1px solid #999; background:transparent;}
.stripeMeFC td table td{border-bottom:1px dotted #999;}
.stripeMeFC td table tr:last-child td{border:0;}

.stripeMeS table{border-collapse:collapse; border:1px solid #9fdef8;}
.stripeMeS td{padding:5px; border-collapse:collapse; border:0; border-bottom:1px dashed #bbd4df;}
.stripeMeS th{color:#242424; padding:5px; border-collapse:collapse; border:0; background:#9fdef8;}
.stripeMeS th a{color:#395FB7; text-decoration:none;}
.stripeMeS th a:hover{color:#0087bf; text-decoration:underline;}
.stripeMeS tr.alt td{background:#eaf3f6;}
.stripeMeS tr.spe td{background:#FFF7E8;}


/* input */
.inputsizeSSS{width:40px;}
.inputsizeSS{width:60px;}
.inputsizeS{width:100px;}
.inputsizeM{width:150px;}
.inputsizeMM{width:200px;}
.inputsizeL{width:300px;}
.inputsizeLL{width:350px;}
.inputsizeXL{width:500px;}
.width10{width:10%;}
.width15{width:15%;}
.width20{width:20%;}
.width25{width:25%;}
.width30{width:30%;}
.width35{width:35%;}
.width40{width:40%;}
.width60{width:60%;}
.width65{width:65%;}
.width70{width:70%;}
.width75{width:75%;}
.width80{width:80%;}
.width85{width:85%;}
.width90{width:90%;}
.width95{width:95%;}
.width99{width:99%;}
.width100{width:100%;}
.inputex{padding:5px 0; border:1px solid #999;}
	

/* line-hight */
.lineheight01{line-height:1.2em;}
.lineheight02{line-height:1.5em;}
.lineheight03{line-height:1.8em;}
.lineheight04{line-height:2em;}

/* font */
.font-size1{font-size:0.769em;}
.font-size2{font-size:0.923em;}
.font-size3{font-size:1.154em;}
.font-size4{font-size:1.385em;}
.font-size5{font-size:1.615em;}
.font-size6{font-size:1.843em;}
.font-size7{font-size:2em;}
.font-size8{font-size:2.2em;}
.font-size9{font-size:2.5em;}
.font-bold{font-weight:bold;}

/* 藍色淺底系統 */
/* 一般用字 */
.font-normal{color:#181818;}
.font-normal a{color:#006bb6; text-decoration:none;}
.font-normal a:hover{color:#00ccff; text-decoration:underline;}
.font-inlinelink a{color:#00aeee;}
/* 有色用字 */
.font-stress{color:#2173b3;}
.font-stress a{color:#2173b3; text-decoration:none;}
.font-stress a:hover{color:#00aeee;}
/* 標題用字 */
.font-title{color:#0060a4;}
.font-title a{color:#0060a4; text-decoration:none;}
.font-title a:hover{color:#2173b3;}
.font-subtitle{color:#00830f;}
.font-subtitle a{color:#00830f; text-decoration:none;}
.font-subtitle a:hover{color:#2173b3;}
/* 說明用字 */
.font-light{color:#666;}
.font-light a{color:#666; text-decoration:none;}
.font-light a:hover{color:#00aeee;}
.font-nowork{color:#C5C5C5;}
.font-nowork a{color:#c5c5c5; text-decoration:none;}
.font-nowork a:hover{color:#00aeee;}
/* 純白字 */
.font-white{color:#fff;}
.font-white a{color:#fff; text-decoration:none;}
.font-white a:hover{color:#FF6;}
/* leftmenu used */
.font-whiteleftmenu{color:#fff;}
.font-whiteleftmenu a{color:#CAF3FF; text-decoration:none;}
.font-whiteleftmenu a:hover{color:#FFFFFF; text-decoration:underline;}

.font-red{color:#FF0000;}

/* 黃字 */
.font-yellow{color:#f37800;}
.font-yellow a{color:#f37800; text-decoration:none;}
.font-yellow a:hover{color:#f3a000; text-decoration:underline;}

.font-copyword{color:#C5E9EF;}
/*
*{
scrollbar-3Dlight-color:#433E30;
scrollbar-arrow-color:#756842;
scrollbar-darkshadow-color:#555144;
scrollbar-face-color:#AB9962;
scrollbar-highlight-color:#CAC5A6;
scrollbar-shadow-color:#766E38;
scrollbar-track-color:#605F53;
}
SCROLLBAR-3DLIGHT-COLOR 捲軸邊框3D底部顏色 
SCROLLBAR-ARROW-COLOR 捲軸箭頭顏色 
SCROLLBAR-DARKSHADOW-COLOR 捲軸右下邊框陰影顏色 
SCROLLBAR-FACE-COLOR 捲軸整體面顏色 
SCROLLBAR-HIGHLIGHT-COLOR 捲軸邊框亮面顏色 
SCROLLBAR-SHADOW-COLOR 捲軸左上邊框陰影顏色 
SCROLLBAR-TRACK-COLOR 捲軸底部顏色 

*/