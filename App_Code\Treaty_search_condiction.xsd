﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Treaty_search_condiction" targetNamespace="http://tempuri.org/Treaty_search_condiction.xsd" xmlns:mstns="http://tempuri.org/Treaty_search_condiction.xsd" xmlns="http://tempuri.org/Treaty_search_condiction.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="ConnString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.ConnString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="esp_search_condictionTableAdapter" GeneratorDataComponentClassName="esp_search_condictionTableAdapter" Name="esp_search_condiction" UserDataComponentName="esp_search_condictionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnString (Web.config)" DbObjectName="engagedb.dbo.esp_search_condiction" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.esp_search_condiction</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@search_form" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="char" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="@empno" Precision="0" ProviderType="Char" Scale="0" Size="6" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="empmo" DataSetColumn="empmo" />
              <Mapping SourceColumn="search_form" DataSetColumn="search_form" />
              <Mapping SourceColumn="search_condiction" DataSetColumn="search_condiction" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Treaty_search_condiction" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="Treaty_search_condiction" msprop:Generator_DataSetName="Treaty_search_condiction">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="esp_search_condiction" msprop:Generator_TableClassName="esp_search_condictionDataTable" msprop:Generator_TableVarName="tableesp_search_condiction" msprop:Generator_TablePropName="esp_search_condiction" msprop:Generator_RowDeletingName="esp_search_condictionRowDeleting" msprop:Generator_RowChangingName="esp_search_condictionRowChanging" msprop:Generator_RowEvHandlerName="esp_search_condictionRowChangeEventHandler" msprop:Generator_RowDeletedName="esp_search_condictionRowDeleted" msprop:Generator_UserTableName="esp_search_condiction" msprop:Generator_RowChangedName="esp_search_condictionRowChanged" msprop:Generator_RowEvArgName="esp_search_condictionRowChangeEvent" msprop:Generator_RowClassName="esp_search_condictionRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="empmo" msprop:Generator_ColumnVarNameInTable="columnempmo" msprop:Generator_ColumnPropNameInRow="empmo" msprop:Generator_ColumnPropNameInTable="empmoColumn" msprop:Generator_UserColumnName="empmo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="6" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="search_form" msprop:Generator_ColumnVarNameInTable="columnsearch_form" msprop:Generator_ColumnPropNameInRow="search_form" msprop:Generator_ColumnPropNameInTable="search_formColumn" msprop:Generator_UserColumnName="search_form" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="search_condiction" msprop:Generator_ColumnVarNameInTable="columnsearch_condiction" msprop:Generator_ColumnPropNameInRow="search_condiction" msprop:Generator_ColumnPropNameInTable="search_condictionColumn" msprop:Generator_UserColumnName="search_condiction" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>