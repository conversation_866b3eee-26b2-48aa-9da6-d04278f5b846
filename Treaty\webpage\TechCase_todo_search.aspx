﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_todo_search.aspx.cs" Inherits="Treaty_webpage_TechCase_todo_search" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="~/Treaty/userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="~/Treaty/userControl/Foot_tech.ascx" TagPrefix="uc1" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />

    <script type="text/javascript">

        $(function () {
            $(".pickdate").datepicker({
                changeMonth: true,
                changeYear: true,
                dateFormat: 'yymmdd',
                monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                showButtonPanel: true,
                closeText: '關閉',
                currentText: '移至今天'

            });

            // hack to add clear button
            // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
            //wrap up the redraw function with our new shiz
            var dpFunc = $.datepicker._generateHTML; //record the original
            $.datepicker._generateHTML = function (inst) {
                var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                //locate the button panel and add our button - with a custom css class.
                $('.ui-datepicker-buttonpane', thishtml).append(
                    $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                    ).click(function () {
                        inst.input.val(''); //attr value chrome not work
                        inst.input.attr('value', '');
                        inst.input.datepicker('hide');
                    })
                );
                thishtml = thishtml.children(); //remove the wrapper div
                return thishtml; //assume okay to return a jQuery
            };
            // 增加清除按鈕 -End				
        });
    </script>
    <style type="text/css">
      
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header runat="server" ID="Header" />

                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="tabsubmenublock ">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align='right'>單位別：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlOrgcd" runat="server" Width="150px" DataTextField="orgcd_name" DataValueField="orgcd"></asp:DropDownList>
                                    </td>
                                    <td align='right'>案件類型：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlCaseStyle" runat="server" Width="150px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList>
                                    </td>
                                    <td align='right'>契約性質：</td>
                                    <td>
                                        <asp:DropDownList ID="ddlContType" runat="server" Width="165px" DataTextField="subtype_desc" DataValueField="code_subtype"></asp:DropDownList>
                                    </td>
                                </tr>

                            </table>
                        </div>

                        <div style="text-align: right">
                            <span style="display: none">
                                <asp:TextBox ID="TB_sdate" class="pickdate inputsizeM text-input" runat="server" Width="90px" MaxLength="10" />
                                <asp:Button ID="TB_SendMail" runat="server" Class="genbtnS" Text="測試寄信" OnClick="TB_SendMail_Click"></asp:Button>
                            </span>
                            <asp:Button ID="btnAdd" runat="server" Class="genbtnS" Text="新增" OnClick="btnAdd_Click"></asp:Button>
                        </div>
                        <br />
                        <div class="stripeMe">
                            <cc1:SmartGridView ID="SGV_search" runat="server" AutoGenerateColumns="False" CellPadding="4" GridLines="None" AllowPaging="True" AllowSorting="True" OnPageIndexChanged="SGV_search_PageIndexChanged" OnPageIndexChanging="SGV_search_PageIndexChanging" OnSorting="SGV_search_Sorting" OnRowCreated="SGV_search_RowCreated" Width="100%">
                                <Columns>
                                    <asp:TemplateField HeaderText="標題">
                                        <ItemTemplate>
                                            <asp:HyperLink ID="hlk_name" runat="server" NavigateUrl=' <%# "~/Treaty/webpage/TechCase_todo_modify.aspx?t_id="+ Server.HtmlEncode(Eval("t_id").ToString()) %>'>
                                                <%# Server.HtmlEncode(Eval("t_name").ToString()) %>
                                            </asp:HyperLink>
                                        </ItemTemplate>
                                    </asp:TemplateField>


                                    <asp:TemplateField HeaderText="說明">
                                        <ItemTemplate>
                                            <asp:Literal ID="Lit_說明" runat="server" Text='<%# Server.HtmlDecode( Server.HtmlEncode(Eval("t_說明").ToString())) %>'></asp:Literal>
                                        </ItemTemplate>
                                        <ItemStyle Width="500" HorizontalAlign="left" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="起訖日期">
                                        <ItemTemplate>
                                            <%# Server.HtmlEncode(Eval("t_sdate").ToString()) %>
                                            <hr />
                                            <%# Server.HtmlEncode(Eval("t_edate").ToString()) %>
                                        </ItemTemplate>
                                        <ItemStyle Width="80" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:BoundField DataField="t_mod_empname" HeaderText="異動人員" ItemStyle-Width="60" />
                                    <asp:BoundField DataField="t_mod_date" HeaderText="異動日期" ItemStyle-Width="95" />
                                </Columns>
                                <FooterStyle BackColor="White" />
                                <CustomPagerSettings PagingMode="Webabcd" TextFormat="<span>每頁</span><span>{0}</span><span>筆/共</span><span>{1}</span><span>筆</span>　<span>第</span><span>{2}</span><span>頁/共</span><span>{3}</span><span>頁</span>" />
                                <PagerStyle VerticalAlign="Middle" BorderStyle="None" BorderWidth="0px" HorizontalAlign="Center" CssClass="pagination" />
                                <PagerSettings Mode="NumericFirstLast" FirstPageText="首頁" LastPageText="末頁" NextPageText="下頁" PreviousPageText="前頁" />
                                <AlternatingRowStyle CssClass="td-bg" BackColor="White"></AlternatingRowStyle>

                                <EmptyDataTemplate>
                                    <!--當找不到資料時則顯示「無資料」-->
                                    <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無資料!"></asp:Label>
                                </EmptyDataTemplate>
                                <FooterStyle BackColor="White" />
                                <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                            </cc1:SmartGridView>
                        </div>

                        <!-- tabsubmenublock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->
        <div style="display: none">

            <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        </div>
        <uc1:Foot runat="server" ID="Foot" />

    </form>
</body>
</html>
