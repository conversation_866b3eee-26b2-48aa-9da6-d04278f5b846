﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;


namespace GPI
{
    /// <summary>
    /// Statist 的摘要描述
    /// </summary>
    public class Statist : GPI.mySQLHelper
    {
        #region 私有變數

        private string _errorMessage;

        //查詢
        private string main_gpiyear_S;
        private string main_gpiyear_E;
        private string range1;
        private string range2;
        private string range3;
        private string range4;
        private string main_gpiorgcd_1;
        private string main_gpiorgcd_2;
        private string main_gpiorgcd_3;
        private string main_receive;
        private string main_award; //20200707 IRENE Add 決標方式
        private string main_type;
        private string main_custtype;
        private string keyword;
        private string main_gpino;
        private string main_no;
        private string main_custname;
        private string main_gpiname;
        private string main_sdate_S;
        private string main_sdate_E;
        private string main_enddate_S;
        private string main_enddate_E;
        private string main_edate_S;
        private string main_edate_E;
        private string main_bid;
        private int main_amt_S;
        private int main_amt_E;
        private int trac_bidamt_S;
        private string main_deposit;
        private int trac_bidamt_E;
        private string main_continue;
        private string main_resource;
        private string main_status;
        private string main_promoempname;
        private string chk_no5;
        private string chk_no3;
        private string chk_no51_item;
        private string main_role;
        private string chk_no8;
        private string chk_no53;
        private string main_curr;
        private string chk_no2;
        private string chk_no1;
        private string gpi_dupl;
        private string trac_contend;
        private string trac_outplan;
        private string chk_no32;
        private string query_type;
        private string trac_respResult;
        private string reBid;

        //統計
        private string allYear;
        private string allMain_curr;
        private string allMain_type;
        private string allMain_custtype;
        private string allMain_gpiorgcd;
        private string allMain_dodept;
        private string rblStatisticsBase;
        private string allMain_role;
        private string allMain_receive;
        private int rowOnPage;
        private string _IP;   //******** IRENE
        public string tmp_url;//******** IRENE
        public string tmp_sql;//20200924 IRENE
        #endregion

        #region 建構子

        public Statist()
        {
            _errorMessage = String.Empty;
            _IP=String.Empty; //20200915 IRENE
            //查詢
            main_gpiyear_S = String.Empty;
            main_gpiyear_E = String.Empty;
            range1 = String.Empty;
            range2 = String.Empty;
            range3 = String.Empty;
            range4 = String.Empty;
            main_gpiorgcd_1 = String.Empty;
            main_gpiorgcd_2 = String.Empty;
            main_gpiorgcd_3 = String.Empty;
            main_receive = String.Empty;
            main_award = String.Empty; //20200707 IRENE Add 決標方式
            main_type = String.Empty;
            main_custtype = String.Empty;
            keyword = String.Empty;
            main_gpino = String.Empty;
            main_no = String.Empty;
            main_custname = String.Empty;
            main_gpiname = String.Empty;
            main_sdate_S = String.Empty;
            main_sdate_E = String.Empty;
            main_enddate_S = String.Empty;
            main_enddate_E = String.Empty;
            main_edate_S = String.Empty;
            main_edate_E = String.Empty;
            main_bid = String.Empty;
            main_amt_S = 0;
            main_amt_E = 0;
            trac_bidamt_S = 0;
            main_deposit = String.Empty;
            trac_bidamt_E = 0;
            main_continue = String.Empty;
            main_resource = String.Empty;
            main_status = String.Empty;
            main_promoempname = String.Empty;
            chk_no5 = String.Empty;
            chk_no3 = String.Empty;
            chk_no51_item = String.Empty;
            main_role = String.Empty;
            chk_no8 = String.Empty;
            chk_no53 = String.Empty;
            main_curr = String.Empty;
            chk_no2 = String.Empty;
            chk_no1 = String.Empty;
            gpi_dupl = String.Empty;
            trac_contend = String.Empty;
            trac_outplan = String.Empty;
            chk_no32 = String.Empty;
            query_type = String.Empty;

            //統計
            allYear = String.Empty;
            allMain_curr = String.Empty;
            allMain_type = String.Empty;
            allMain_custtype = String.Empty;
            allMain_gpiorgcd = String.Empty;
            allMain_dodept = String.Empty;
            rblStatisticsBase = String.Empty;
            allMain_role = String.Empty;
            allMain_receive = String.Empty;
            rowOnPage = 10;
            tmp_url = String.Empty; //******** IRENE ADD
            tmp_sql = String.Empty; //******** IRENE ADD
        }

        #endregion

        #region 公有屬性
        /// <summary>
        /// IP
        /// 2020/09 IRENE ADD
        /// </summary>
        public string IP
        {
            get { return _IP; }
            set { _IP = value; }
        }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        #region 查詢
        /// <summary>
        /// 年度(起)
        /// </summary>
        public string Main_gpiyear_S
        {
            get { return main_gpiyear_S; }
            set { main_gpiyear_S = value; }
        }
        /// <summary>
        /// 年度(迄)
        /// </summary>
        public string Main_gpiyear_E
        {
            get { return main_gpiyear_E; }
            set { main_gpiyear_E = value; }
        }
        /// <summary>
        /// 案件範圍-全部
        /// </summary>
        public string Range1
        {
            get { return range1; }
            set { range1 = value; }
        }
        /// <summary>
        /// 案件範圍-得標
        /// </summary>
        public string Range2
        {
            get { return range2; }
            set { range2 = value; }
        }

        /// <summary>
        /// 案件範圍-未得標
        /// </summary>
        public string Range3
        {
            get { return range3; }
            set { range3 = value; }
        }

        /// <summary>
        /// 案件範圍-不同意投標
        /// </summary>
        public string Range4
        {
            get { return range4; }
            set { range4 = value; }
        }

        /// <summary>
        /// 單位
        /// </summary>
        public string Main_gpiorgcd_1
        {
            get { return main_gpiorgcd_1; }
            set { main_gpiorgcd_1 = value; }
        }
        /// <summary>
        /// 組
        /// </summary>
        public string Main_gpiorgcd_2
        {
            get { return main_gpiorgcd_2; }
            set { main_gpiorgcd_2 = value; }
        }
        /// <summary>
        /// 部門
        /// </summary>
        public string Main_gpiorgcd_3
        {
            get { return main_gpiorgcd_3; }
            set { main_gpiorgcd_3 = value; }
        }
        /// <summary>
        /// 招標方式
        /// </summary>
        public string Main_receive
        {
            get { return main_receive; }
            set { main_receive = value; }
        }
        /// <summary>
        /// 決標方式 20200707 IRENE Add
        /// </summary>
        public string Main_award
        {
            get { return main_award; }
            set { main_award = value; }
        }
        /// <summary>
        /// 案件類別
        /// </summary>
        public string Main_type
        {
            get { return main_type; }
            set { main_type = value; }
        }
        /// <summary>
        /// 招標單位屬性
        /// </summary>
        public string Main_custtype
        {
            get { return main_custtype; }
            set { main_custtype = value; }
        }
        /// <summary>
        /// 關鍵字
        /// </summary>
        public string Keyword
        {
            get { return keyword; }
            set { keyword = value; }
        }
        /// <summary>
        /// 規劃案號
        /// </summary>
        public string Main_gpino
        {
            get { return main_gpino; }
            set { main_gpino = value; }
        }

        /// <summary>
        /// 標案案號
        /// </summary>
        public string Main_no
        {
            get { return main_no; }
            set { main_no = value; }
        }
        /// <summary>
        /// 招標單位
        /// </summary>
        public string Main_custname
        {
            get { return main_custname; }
            set { main_custname = value; }
        }
        /// <summary>
        /// 標案名稱
        /// </summary>
        public string Main_gpiname
        {
            get { return main_gpiname; }
            set { main_gpiname = value; }
        }

        /// <summary>
        /// 執行期間(起)1
        /// </summary>
        public string Main_sdate_S
        {
            get { return main_sdate_S; }
            set { main_sdate_S = value; }
        }
        /// <summary>
        /// 執行期間(起)2
        /// </summary>
        public string Main_sdate_E
        {
            get { return main_sdate_E; }
            set { main_sdate_E = value; }
        }
        /// <summary>
        /// 結標日期(起)
        /// </summary>
        public string Main_enddate_S
        {
            get { return main_enddate_S; }
            set { main_enddate_S = value; }
        }
        /// <summary>
        /// 結標日期(迄)
        /// </summary>
        public string Main_enddate_E
        {
            get { return main_enddate_E; }
            set { main_enddate_E = value; }
        }
        /// <summary>
        /// 執行期間(迄)1
        /// </summary>
        public string Main_edate_S
        {
            get { return main_edate_S; }
            set { main_edate_S = value; }
        }
        /// <summary>
        /// 執行期間(迄)2
        /// </summary>
        public string Main_edate_E
        {
            get { return main_edate_E; }
            set { main_edate_E = value; }
        }
        /// <summary>
        /// 投標方式
        /// </summary>
        public string Main_bid
        {
            get { return main_bid; }
            set { main_bid = value; }
        }
        /// <summary>
        /// 招標金額
        /// </summary>
        public int Main_amt_S
        {
            get { return main_amt_S; }
            set { main_amt_S = value; }
        }
        /// <summary>
        /// 招標金額2
        /// </summary>
        public int Main_amt_E
        {
            get { return main_amt_E; }
            set { main_amt_E = value; }
        }
        /// <summary>
        /// 得標金額1
        /// </summary>
        public int Trac_bidamt_S
        {
            get { return trac_bidamt_S; }
            set { trac_bidamt_S = value; }
        }
        /// <summary>
        /// 押標金申請
        /// </summary>
        public string Main_deposit
        {
            get { return main_deposit; }
            set { main_deposit = value; }
        }
        /// <summary>
        /// 得標金額2
        /// </summary>
        public int Trac_bidamt_E
        {
            get { return trac_bidamt_E; }
            set { trac_bidamt_E = value; }
        }
        /// <summary>
        /// 延續性
        /// </summary>
        public string Main_continue
        {
            get { return main_continue; }
            set { main_continue = value; }
        }
        /// <summary>
        /// 本院標案分類
        /// </summary>
        public string Main_resource
        {
            get { return main_resource; }
            set { main_resource = value; }
        }
        /// <summary>
        /// 標案進度
        /// </summary>
        public string Main_status
        {
            get { return main_status; }
            set { main_status = value; }
        }
        /// <summary>
        /// 業務推廣人
        /// </summary>
        public string Main_promoempname
        {
            get { return main_promoempname; }
            set { main_promoempname = value; }
        }
        /// <summary>
        /// 屬規劃案
        /// </summary>
        public string Chk_no5
        {
            get { return chk_no5; }
            set { chk_no5 = value; }
        }
        /// <summary>
        /// 第一次招標案
        /// </summary>
        public string Chk_no3
        {
            get { return chk_no3; }
            set { chk_no3 = value; }
        }
        /// <summary>
        /// 是否有分包
        /// </summary>
        public string Chk_no51_item
        {
            get { return chk_no51_item; }
            set { chk_no51_item = value; }
        }
        /// <summary>
        /// 本院角色
        /// </summary>
        public string Main_role
        {
            get { return main_role; }
            set { main_role = value; }
        }
        /// <summary>
        /// 科專人員參與
        /// </summary>
        public string Chk_no8
        {
            get { return chk_no8; }
            set { chk_no8 = value; }
        }
        /// <summary>
        /// 屬特殊案件
        /// </summary>
        public string Chk_no53
        {
            get { return chk_no53; }
            set { chk_no53 = value; }
        }
        /// <summary>
        /// 核心業務別
        /// </summary>
        public string Main_curr
        {
            get { return main_curr; }
            set { main_curr = value; }
        }
        /// <summary>
        /// 重要施政計畫
        /// </summary>
        public string Chk_no2
        {
            get { return chk_no2; }
            set { chk_no2 = value; }
        }
        /// <summary>
        /// 業者執行能力
        /// </summary>
        public string Chk_no1
        {
            get { return chk_no1; }
            set { chk_no1 = value; }
        }
        /// <summary>
        /// 重案案件
        /// </summary>
        public string Gpi_dupl
        {
            get { return gpi_dupl; }
            set { gpi_dupl = value; }
        }
        /// <summary>
        /// 企業參與競標
        /// </summary>
        public string Trac_contend
        {
            get { return trac_contend; }
            set { trac_contend = value; }
        }
        /// <summary>
        /// 屬策略退出計畫
        /// </summary>
        public string Trac_outplan
        {
            get { return trac_outplan; }
            set { trac_outplan = value; }
        }
        /// <summary>
        /// 重要施政計畫
        /// </summary>
        public string Chk_no32
        {
            get { return chk_no32; }
            set { chk_no32 = value; }
        }
        /// <summary>
        /// 查詢類型( 1 查詢 2 統計 3匯出)
        /// </summary>
        public string Query_type
        {
            get { return query_type; }
            set { query_type = value; }
        }
        /// <summary>
        /// 遇民間業者競標本院做法
        /// </summary>
        public string Trac_respResult
        {
            get { return trac_respResult; }
            set { trac_respResult = value; }
        }
        /// <summary>
        /// 是否再次投標
        /// </summary>
        public string ReBid
        {
            get { return reBid; }
            set { reBid = value; }
        }

        #endregion

        #region 統計
        /// <summary>
        /// 年度
        /// </summary>
        public string AllYear
        {
            get { return allYear; }
            set { allYear = value; }
        }
        /// <summary>
        /// 核心業務
        /// </summary>
        public string AllMain_curr
        {
            get { return allMain_curr; }
            set { allMain_curr = value; }
        }
        /// <summary>
        /// 標案類別
        /// </summary>
        public string AllMain_type
        {
            get { return allMain_type; }
            set { allMain_type = value; }
        }
        /// <summary>
        /// 招標單位屬性
        /// </summary>
        public string AllMain_custtype
        {
            get { return allMain_custtype; }
            set { allMain_custtype = value; }
        }
        /// <summary>
        /// 單位
        /// </summary>
        public string AllMain_gpiorgcd
        {
            get { return allMain_gpiorgcd; }
            set { allMain_gpiorgcd = value; }
        }
        /// <summary>
        /// 規劃部門
        /// </summary>
        public string AllMain_dodept
        {
            get { return allMain_dodept; }
            set { allMain_dodept = value; }
        }
        /// <summary>
        /// 統計基礎
        /// </summary>
        public string RblStatisticsBase
        {
            get { return rblStatisticsBase; }
            set { rblStatisticsBase = value; }
        }
        /// <summary>
        /// 本院角色
        /// </summary>
        public string AllMain_role
        {
            get { return allMain_role; }
            set { allMain_role = value; }
        }
        /// <summary>
        /// 招標方式
        /// </summary>
        public string AllMain_receive
        {
            get { return allMain_receive; }
            set { allMain_receive = value; }
        }
        /// <summary>
        /// 設定有幾頁
        /// </summary>
        public int RowOnPage
        {
            get { return rowOnPage; }
            set { rowOnPage = value; }
        }
        #endregion

        #endregion

        #region 公有函式
        public bool Insert()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = "";
            oCmd.Parameters.AddWithValue("_errorMessage", _errorMessage);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

        public DataSet Get()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_static_casequery";
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@main_gpiyear_S", main_gpiyear_S);
            oCmd.Parameters.AddWithValue("@main_gpiyear_E", main_gpiyear_E);
            oCmd.Parameters.AddWithValue("@range1", range1);
            oCmd.Parameters.AddWithValue("@range2", range2);
            oCmd.Parameters.AddWithValue("@range3", range3);
            oCmd.Parameters.AddWithValue("@range4", range4);
            oCmd.Parameters.AddWithValue("@main_gpiorgcd_1", main_gpiorgcd_1);
            oCmd.Parameters.AddWithValue("@main_gpiorgcd_2", main_gpiorgcd_2);
            oCmd.Parameters.AddWithValue("@main_gpiorgcd_3", main_gpiorgcd_3);
            oCmd.Parameters.AddWithValue("@main_receive", main_receive);
            oCmd.Parameters.AddWithValue("@main_award", main_award);
            oCmd.Parameters.AddWithValue("@main_type", main_type);
            oCmd.Parameters.AddWithValue("@main_custtype", main_custtype);
            oCmd.Parameters.AddWithValue("@keyword", keyword);
            oCmd.Parameters.AddWithValue("@main_gpino", main_gpino);
            oCmd.Parameters.AddWithValue("@main_no", main_no);
            oCmd.Parameters.AddWithValue("@main_custname", main_custname);
            oCmd.Parameters.AddWithValue("@main_gpiname", main_gpiname);
            oCmd.Parameters.AddWithValue("@main_sdate_S", main_sdate_S);
            oCmd.Parameters.AddWithValue("@main_sdate_E", main_sdate_E);
            oCmd.Parameters.AddWithValue("@main_enddate_S", main_enddate_S);
            oCmd.Parameters.AddWithValue("@main_enddate_E", main_enddate_E);
            oCmd.Parameters.AddWithValue("@main_edate_S", main_edate_S);
            oCmd.Parameters.AddWithValue("@main_edate_E", main_edate_E);
            oCmd.Parameters.AddWithValue("@main_bid", main_bid);
            oCmd.Parameters.AddWithValue("@main_amt_S", main_amt_S);
            oCmd.Parameters.AddWithValue("@main_amt_E", main_amt_E);
            oCmd.Parameters.AddWithValue("@trac_bidamt_S", trac_bidamt_S);
            oCmd.Parameters.AddWithValue("@main_deposit", main_deposit);
            oCmd.Parameters.AddWithValue("@trac_bidamt_E", trac_bidamt_E);
            oCmd.Parameters.AddWithValue("@main_continue", main_continue);
            oCmd.Parameters.AddWithValue("@main_resource", main_resource);
            oCmd.Parameters.AddWithValue("@main_status", main_status);
            oCmd.Parameters.AddWithValue("@main_promoempname", main_promoempname);
            oCmd.Parameters.AddWithValue("@chk_no5", chk_no5);
            oCmd.Parameters.AddWithValue("@chk_no3", chk_no3);
            oCmd.Parameters.AddWithValue("@chk_no51_item", chk_no51_item);
            oCmd.Parameters.AddWithValue("@main_role", main_role);
            oCmd.Parameters.AddWithValue("@chk_no8", chk_no8);
            oCmd.Parameters.AddWithValue("@chk_no53", chk_no53);
            oCmd.Parameters.AddWithValue("@main_curr", main_curr);
            oCmd.Parameters.AddWithValue("@chk_no2", chk_no2);
            oCmd.Parameters.AddWithValue("@chk_no1", chk_no1);
            oCmd.Parameters.AddWithValue("@gpi_dupl", gpi_dupl);
            oCmd.Parameters.AddWithValue("@trac_contend", trac_contend);
            oCmd.Parameters.AddWithValue("@trac_outplan", trac_outplan);
            oCmd.Parameters.AddWithValue("@chk_no32", chk_no32);
            oCmd.Parameters.AddWithValue("@query_type", query_type);
            oCmd.Parameters.AddWithValue("@trac_respResult", trac_respResult);
            oCmd.Parameters.AddWithValue("@reBid", ReBid);

            oCmd.Parameters.AddWithValue("@PageSize", PageSize);
            oCmd.Parameters.AddWithValue("@CurrentPage", CurrentPage);
            oCmd.Parameters.AddWithValue("@SortFields", SortFields);
            oCmd.Parameters.AddWithValue("@SortDirection", SortDirection);
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataSet ds = this.getDataSet(oCmd, CommandType.StoredProcedure);
            return ds;
        }

        #region 統計

        public DataTable GetStatist()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetStatisics";
            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public DataTable GetStatistByDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetStatisicsByDetail";

            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@allMain_receive", allMain_receive);
            oCmd.Parameters.AddWithValue("@rowOnPage", rowOnPage);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }


        public DataTable GetStatistByYear()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetStatisicsByYear";

            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }

        #endregion

        #region 招標單位分析
        public DataTable GetBidsDtatistics()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetBidsDtatistics";

            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }
        public DataTable GetBidsDtatisticsByYear()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetBidsDtatisticsByYear";

            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }




        public DataTable GetBidsStatistByDetail()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"prGetBidsStatisicsByDetail";

            oCmd.Parameters.AddWithValue("@allYear", allYear);
            oCmd.Parameters.AddWithValue("@allMain_curr", allMain_curr);
            oCmd.Parameters.AddWithValue("@allMain_type", allMain_type);
            oCmd.Parameters.AddWithValue("@allMain_custtype", allMain_custtype);
            oCmd.Parameters.AddWithValue("@allMain_gpiorgcd", allMain_gpiorgcd);
            oCmd.Parameters.AddWithValue("@allMain_dodept", allMain_dodept);
            oCmd.Parameters.AddWithValue("@rblStatisticsBase", rblStatisticsBase);
            oCmd.Parameters.AddWithValue("@allMain_role", allMain_role);
            oCmd.Parameters.AddWithValue("@allMain_receive", allMain_receive);
            oCmd.Parameters.AddWithValue("@rowOnPage", rowOnPage);
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo); //******** IRENE Add SSO員工帳號
            oCmd.Parameters.AddWithValue("@IP", _IP); //******** IRENE ADD

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
            return dt;
        }


        #endregion

        public string getCodeString(string codeType)
        {

            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"

select code_value+',' from gpi_code where code_type=@code_type order by code_value for xml path('') 
";
            oCmd.Parameters.AddWithValue("@code_type", codeType);

            string data = this.getTopOne(oCmd, CommandType.Text);

            return data;
        }

        #endregion


        /// <summary>
        /// 招標單位分析"匯出"_機密系統LOG機制
        /// ******** IRENE ADD
        /// </summary>
        public void Insert_Confidential_LOG()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"

            INSERT INTO gpi_tmpSQL
                   (tmp_sql
                   ,tmp_userid
                   ,tmp_url
                   ,tmp_ip
                   ,tmp_datetime
                    )
             VALUES
                   (@tmp_sql --'匯出' or '印表'
                   ,@Empno
                   ,@tmp_url --依清單類別匯出
                   ,@IP
                   ,getdate()
                   )
            ";
            oCmd.Parameters.AddWithValue("@tmp_sql",tmp_sql); //匯出 or 印表
            oCmd.Parameters.AddWithValue("@Empno", AccountInfo.EmpNo);
            oCmd.Parameters.AddWithValue("@tmp_url",tmp_url);
            oCmd.Parameters.AddWithValue("@IP", Common.GetIP4Address());
 
            this.Execute(oCmd, CommandType.Text);

        }


    }
}