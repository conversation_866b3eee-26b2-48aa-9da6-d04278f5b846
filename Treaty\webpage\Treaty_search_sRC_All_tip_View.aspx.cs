﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_Treaty_search_sRC_All_tip_View : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            if (Request.QueryString["case_id"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["case_id"].ToString()) || (Request.QueryString["case_id"].Length > 8))
                    Response.Redirect("../danger.aspx");
                string columnName = (Request.QueryString["columnName"] + "");
                if (columnName == "")
                    Response.Redirect("../danger.aspx");
                ViewState["case_id"] = Request.QueryString["case_id"].ToString();
                ViewState["columnName"] = Request.QueryString["columnName"].ToString();
                Bind_Data();
            }
        }
    }

    private void Bind_Data()
    {
        DataTable dt = new DataTable();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_search_sRC_All";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@case_id", oRCM.SQLInjectionReplaceAll(ViewState["case_id"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view");
            try
            {
                sqlConn.Open();

                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_search = dt.DefaultView;
        if (dv_search.Count >= 1)
        {
            string columnName = ViewState["columnName"].ToString();
            TB_content.Text = Server.HtmlDecode(Server.HtmlEncode(dv_search[0][columnName].ToString()));
        }
    }
}