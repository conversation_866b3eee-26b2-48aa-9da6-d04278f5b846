{"action": {"add": "Tambah", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> semua", "calendar": "<PERSON><PERSON><PERSON>", "calibrate": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>", "clearAll": "Kosongkan semua", "close": "tutup", "undo": "<PERSON><PERSON><PERSON> asal", "redo": "sedia", "comment": "Komen", "reply": "<PERSON><PERSON> balasan", "copy": "Salinan", "delete": "Padam", "group": "Ku<PERSON><PERSON><PERSON>", "ungroup": "Nyahkumpulan", "download": "<PERSON>at turun", "edit": "Sunting", "extract": "Ekstrak", "enterFullscreen": "<PERSON><PERSON><PERSON>", "exitFullscreen": "<PERSON><PERSON><PERSON> dari skrin penuh", "fit": "<PERSON><PERSON><PERSON>", "fitToPage": "<PERSON><PERSON> m<PERSON>uh", "fitToWidth": "<PERSON><PERSON><PERSON> dengan lebar", "more": "<PERSON><PERSON>", "openFile": "Buka fail", "pagePrev": "Halaman sebelumnya", "pageNext": "Muka surat seterusnya", "pageSet": "Tetapkan halaman", "print": "Cetak", "proceed": "Teruskan", "name": "nama", "rename": "<PERSON><PERSON><PERSON> semula", "ok": "okey", "rotate": "<PERSON><PERSON>", "rotate3D": "Putar", "rotateClockwise": "Putar mengikut arah jam", "rotateCounterClockwise": "<PERSON>ar lawan jam", "save": "Jimat", "post": "Pos", "create": "Buat", "showMoreResults": "<PERSON><PERSON><PERSON><PERSON><PERSON> lebih banyak hasil", "sign": "<PERSON><PERSON>", "style": "<PERSON><PERSON>", "submit": "Hantar", "zoom": "<PERSON><PERSON>", "zoomIn": "Mengezum masuk", "zoomOut": "<PERSON><PERSON> keluar", "zoomSet": "Tetapkan zum", "zoomControls": "<PERSON><PERSON><PERSON>", "draw": "<PERSON><PERSON>", "type": "taip", "upload": "<PERSON>at naik", "link": "<PERSON><PERSON><PERSON>", "darkMode": "Mod gelap", "lightMode": "<PERSON><PERSON> ca<PERSON>a", "fileAttachmentDownload": "Muat turun fail yang di<PERSON>an", "prevResult": "Keputusan sebelumnya", "nextResult": "Keputusan seterusnya", "prev": "Sebelumnya", "next": "Seterusnya", "startFormEditing": "<PERSON><PERSON><PERSON>", "exitFormEditing": "<PERSON><PERSON><PERSON> dari <PERSON>", "exit": "<PERSON><PERSON><PERSON>", "addOption": "Tambah Pilihan", "formFieldEdit": "<PERSON> <PERSON><PERSON>", "viewShortCutKeysFor3D": "<PERSON><PERSON>", "markAllRead": "Tandai semua sebagai dibaca", "insertPage": "<PERSON><PERSON><PERSON>", "insertBlankPageAbove": "Sisipkan halaman kosong di atas", "insertBlankPageBelow": "<PERSON>sip<PERSON> halaman kosong di bawah", "pageManipulation": "<PERSON><PERSON><PERSON><PERSON>", "replace": "Gantikan", "setDestination": "Tetapkan Destinasi", "showLess": "kurang <PERSON>", "showMore": "... lagi", "chooseFile": "<PERSON><PERSON><PERSON> fail", "selectYourOption": "<PERSON><PERSON><PERSON> pilihan anda", "open": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "moveToTop": "Bergerak ke atas", "moveToBottom": "<PERSON><PERSON> ke bawah", "redactPages": "Sunting halaman", "playAudio": "Mainkan audio", "pauseAudio": "jeda audio", "selectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "unselect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addMark": "Tambah Mark", "viewFile": "Lihat fail", "switchLanguage": "Bahasa"}, "annotation": {"areaMeasurement": "<PERSON><PERSON>an", "arc": "Ark<PERSON>", "arcMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "arrow": "anak panah", "callout": "<PERSON><PERSON><PERSON> ciri", "crop": "<PERSON><PERSON>", "caret": "kekurangan", "dateFreeText": "<PERSON><PERSON><PERSON>", "formFillCheckmark": "<PERSON><PERSON><PERSON>", "formFillCross": "Menyeberang", "distanceMeasurement": "Jarak", "countMeasurement": "<PERSON>", "ellipse": "Ellipse", "eraser": "Pemadam", "fileattachment": "Lam<PERSON><PERSON>", "freehand": "<PERSON><PERSON>", "freeHandHighlight": "<PERSON><PERSON><PERSON>", "freetext": "<PERSON><PERSON>", "highlight": "Serlahkan", "image": "<PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON>", "perimeterMeasurement": "Perimeter", "polygon": "Poligon", "polygonCloud": "awan", "polyline": "Polyline", "rectangle": "segi empat tepat", "redact": "sunting", "formFillDot": "titik", "signature": "Tandatangan", "squiggly": "Dengan be<PERSON>kuk-lekuk", "stamp": "setem", "stickyNote": "Catatan", "strikeout": "Strikeout", "underline": "<PERSON><PERSON> bawah", "custom": "Ada<PERSON>", "rubberStamp": "<PERSON><PERSON>ah", "note": "Catatan", "textField": "<PERSON><PERSON>", "signatureFormField": "Medan <PERSON>", "checkBoxFormField": "Medan Kotak semak", "radioButtonFormField": "Medan Butang Radio", "listBoxFormField": "Medan Kotak Senarai", "comboBoxFormField": "Padang Kotak Kombo", "link": "<PERSON><PERSON><PERSON>", "other": "Lain-lain", "3D": "3D", "sound": "<PERSON><PERSON><PERSON>", "changeView": "<PERSON><PERSON>"}, "rubberStamp": {"Approved": "Dilulus<PERSON>", "AsIs": "As Is", "Completed": "Se<PERSON><PERSON>", "Confidential": "Sulit", "Departmental": "Jabatan", "Draft": "Draf", "Experimental": "<PERSON><PERSON><PERSON><PERSON>", "Expired": "<PERSON><PERSON> tempoh", "Final": "<PERSON><PERSON><PERSON>", "ForComment": "Untuk Komen", "ForPublicRelease": "Untuk <PERSON>", "InformationOnly": "<PERSON><PERSON><PERSON><PERSON>", "NotApproved": "Tidak diterima", "NotForPublicRelease": "<PERSON><PERSON>n <PERSON>", "PreliminaryResults": "Keputusan <PERSON>", "Sold": "<PERSON><PERSON><PERSON><PERSON>", "TopSecret": "<PERSON><PERSON><PERSON> su<PERSON>", "Void": "batal", "SHSignHere": "Tandatangan di sini", "SHWitness": "saksi", "SHInitialHere": "Inisial Di Sini", "SHAccepted": "Diterima", "SBRejected": "<PERSON><PERSON><PERSON>"}, "component": {"attachmentPanel": "Lam<PERSON>ran", "leftPanel": "Panel", "toolsHeader": "Alatan", "searchOverlay": "<PERSON><PERSON>", "searchPanel": "<PERSON><PERSON>", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "Komen", "outlinePanel": "<PERSON><PERSON> besar", "outlinesPanel": "<PERSON><PERSON> besar", "bookmarkPanel": "<PERSON><PERSON> buku", "bookmarksPanel": "<PERSON><PERSON> buku", "newBookmark": "<PERSON><PERSON>", "bookmarkTitle": "<PERSON><PERSON><PERSON>", "bookmarkPage": "<PERSON><PERSON>", "signaturePanel": "Tandatangan", "layersPanel": "<PERSON><PERSON><PERSON>", "thumbnailsPanel": "Gambar kecil", "toolsButton": "Alatan", "redaction": "<PERSON><PERSON><PERSON>", "viewControlsOverlay": "<PERSON><PERSON>", "calibration": "<PERSON><PERSON><PERSON><PERSON>", "zoomOverlay": "<PERSON><PERSON><PERSON>", "textPopup": "Teks Pop Timbul", "createStampButton": "<PERSON><PERSON><PERSON>", "filter": "Penap<PERSON>", "pageReplaceModalTitle": "Gantikan <PERSON>", "files": "Fail", "editText": "<PERSON>", "redactionPanel": "Panel Redaksi"}, "message": {"toolsOverlayNoPresets": "Tiada Pratetap", "badDocument": "Gagal memuatkan dokumen. Dokumen itu sama ada rosak atau tidak sah.", "customPrintPlaceholder": "cth. 3, 4-10", "encryptedAttemptsExceeded": "Gagal memuatkan dokumen yang disulitkan. Terlalu banyak percubaan.", "encryptedUserCancelled": "Gagal memuatkan dokumen yang disulitkan. Kemasukan kata laluan dibatalkan.", "enterPassword": "Dokumen ini dilindungi kata laluan. <PERSON>la masukkan kata laluan", "incorrectPassword": "<PERSON><PERSON>an salah, percu<PERSON>an yang tinggal: {{ remainingAttempts }}", "noAnnotations": "<PERSON>la membuat anotasi untuk meninggalkan ulasan.", "noAnnotationsReadOnly": "Dokumen ini tidak mempunyai anotasi.", "noAnnotationsFilter": "<PERSON>la membuat anotasi dan penapis akan dipaparkan di sini.", "noBookmarks": "<PERSON><PERSON><PERSON> penanda halaman tersedia", "noOutlines": "Dokumen ini tidak mempunyai garis besar.", "noAttachments": "Dokumen ini tidak mempunyai lampiran.", "noResults": "Tiada keputusan dijumpai.", "numResultsFound": "keputusan dijumpai", "notSupported": "Jenis fail itu tidak disokong.", "passwordRequired": "kata laluan diperlukan", "enterPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kata laluan", "preparingToPrint": "Bersedia untuk mencetak...", "annotationReplyCount": "{{count}} <PERSON><PERSON>", "annotationReplyCount_plural": "{{count}} <PERSON><PERSON><PERSON>", "printTotalPageCount": "<PERSON><PERSON><PERSON>: {{count}} halaman", "printTotalPageCount_plural": "<PERSON><PERSON><PERSON>: {{count}} halaman", "processing": "Memproses...", "searching": "<PERSON><PERSON><PERSON>...", "searchCommentsPlaceholder": "<PERSON><PERSON> komen", "searchDocumentPlaceholder": "Cari dokumen", "signHere": "Tandatangan di sini", "insertTextHere": "Sisipkan teks di sini", "imageSignatureAcceptedFileTypes": "<PERSON>ya {{acceptedFileTypes}} diterima", "enterMeasurement": "Masukkan ukuran antara dua titik", "errorEnterMeasurement": "Nombor yang anda masukkan tidak sah, anda boleh memasukkan nilai seperti 7.5 atau 7 1/2", "linkURLorPage": "Pautan URL atau Halaman", "warning": "<PERSON><PERSON>", "enterContentEditingMode": "<PERSON><PERSON> akan memasuki mod penyuntingan kandungan.", "existingAnnotationWarning": "<PERSON><PERSON><PERSON> sedia ada mungkin tidak sepadan dengan teks selepas ia diedit.", "changesCannotBeUndone": "<PERSON><PERSON>ng perubahan yang dibuat tidak boleh dibuat asal.", "doNotShowAgain": "<PERSON><PERSON> tun<PERSON>kkan ini kepada saya lagi", "enterReplacementText": "Masukkan teks yang ingin anda gantikan", "sortBy": "<PERSON><PERSON>", "emptyCustomStampInput": "Teks setem tidak boleh kosong", "unpostedComment": "Komen Tidak Disiarkan", "lockedLayer": "<PERSON><PERSON><PERSON>", "layerVisibililtyNoChange": "Ke<PERSON>lihatan lapisan tidak boleh diubah", "untitled": "Tidak bertajuk", "selectHowToLoadFile": "<PERSON><PERSON>h cara memuatkan dokumen anda", "openFileByUrl": "Buka fail mengikut URL:", "enterUrlHere": "Masukkan URL di sini", "openLocalFile": "Buka fail tempatan:", "selectFile": "<PERSON><PERSON><PERSON> fail", "selectPageToReplace": "<PERSON><PERSON><PERSON> halaman dalam dokumen yang anda ingin gantikan.", "embeddedFiles": "<PERSON><PERSON>", "pageNum": "<PERSON><PERSON><PERSON><PERSON>", "viewBookmark": "<PERSON><PERSON> pada <PERSON>", "error": "ralat"}, "option": {"type": {"caret": "kekurangan", "custom": "Ada<PERSON>", "ellipse": "Ellipse", "fileattachment": "Lam<PERSON><PERSON>", "freehand": "<PERSON><PERSON>", "callout": "<PERSON><PERSON><PERSON> ciri", "freetext": "<PERSON><PERSON>", "line": "<PERSON><PERSON>", "polygon": "Poligon", "polyline": "Polyline", "rectangle": "segi empat tepat", "redact": "sunting", "signature": "Tandatangan", "stamp": "setem", "stickyNote": "<PERSON><PERSON>", "highlight": "Serlahkan", "strikeout": "Strikeout", "underline": "<PERSON><PERSON> bawah", "squiggly": "Dengan be<PERSON>kuk-lekuk", "3D": "3D", "other": "Lain-lain"}, "notesOrder": {"dropdownLabel": "<PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON>", "status": "Status", "author": "Pengarang", "type": "taip", "color": "<PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON>", "modifiedDate": "<PERSON><PERSON><PERSON>"}, "toolbarGroup": {"dropdownLabel": "Kumpulan Bar Alat", "toolbarGroup-View": "Lihat", "toolbarGroup-Annotate": "<PERSON><PERSON><PERSON>", "toolbarGroup-Shapes": "<PERSON><PERSON>", "toolbarGroup-Insert": "<PERSON>sip<PERSON>", "toolbarGroup-Measure": "ukur", "toolbarGroup-Edit": "Sunting", "toolbarGroup-FillAndSign": "<PERSON><PERSON> dan <PERSON>", "toolbarGroup-Forms": "<PERSON><PERSON>", "toolbarGroup-Redact": "sunting"}, "annotationColor": {"StrokeColor": "Strok", "FillColor": "isi", "TextColor": "Teks"}, "colorPalette": {"colorLabel": "<PERSON><PERSON>"}, "displayMode": {"layout": "<PERSON><PERSON>", "pageTransition": "<PERSON><PERSON><PERSON>"}, "documentControls": {"placeholder": "1, 3, 5-10", "selectTooltip": "<PERSON><PERSON><PERSON> berbilang halaman", "closeTooltip": "<PERSON><PERSON><PERSON> pilihan berbilang"}, "bookmarkOutlineControls": {"edit": "Sunting", "done": "Se<PERSON><PERSON>", "reorder": "<PERSON><PERSON> semula"}, "layout": {"cover": "<PERSON><PERSON>", "double": "<PERSON><PERSON>", "single": "<PERSON><PERSON>"}, "mathSymbols": "simbol matematik", "notesPanel": {"separator": {"today": "<PERSON> ini", "yesterday": "Semalam", "unknown": "Tidak diketahui"}, "noteContent": {"noName": "(tiada nama)", "noDate": "(tiada tarikh)"}}, "pageTransition": {"continuous": "<PERSON><PERSON>", "default": "Halaman demi <PERSON>", "reader": "Pembaca"}, "print": {"all": "<PERSON><PERSON><PERSON>", "current": "Muka surat ini", "pages": "Halaman untuk dicetak", "specifyPages": "<PERSON><PERSON><PERSON>", "view": "Pandangan Semasa", "pageQuality": "<PERSON><PERSON><PERSON>", "qualityNormal": "<PERSON><PERSON><PERSON><PERSON>", "qualityHigh": "tinggi", "includeAnnotations": "Sertakan anota<PERSON>", "includeComments": "<PERSON><PERSON><PERSON>", "printSettings": "Tetapan <PERSON>"}, "printInfo": {"author": "Pengarang", "subject": "Subjek", "date": "<PERSON><PERSON><PERSON>"}, "redaction": {"markForRedaction": "<PERSON>dakan untuk redaksi"}, "searchPanel": {"caseSensitive": "<PERSON><PERSON><PERSON><PERSON> huruf besar", "wholeWordOnly": "<PERSON><PERSON><PERSON><PERSON>", "wildcard": "<PERSON><PERSON> <PERSON>"}, "toolsOverlay": {"currentStamp": "<PERSON><PERSON>", "currentSignature": "Tandatangan Se<PERSON>a", "signatureAltText": "Tandatangan"}, "stampOverlay": {"addStamp": "Tambah Setem Baharu"}, "signatureOverlay": {"addSignature": "Tambah Tandatangan Baharu"}, "signatureModal": {"modalName": "<PERSON><PERSON><PERSON> Baharu", "dragAndDrop": "Seret & Lepaskan imej anda di sini", "or": "Ataupun", "pickImage": "<PERSON><PERSON><PERSON>", "selectImage": "<PERSON><PERSON><PERSON> imej anda di sini"}, "pageReplacementModal": {"dragAndDrop": "Seret & Lepaskan fail anda di sini", "or": "Ataupun", "chooseFile": "<PERSON><PERSON><PERSON> fail", "localFile": "<PERSON><PERSON>", "pageReplaceInputLabel": "Gantikan <PERSON>", "pageReplaceInputFromSource": "<PERSON><PERSON> halaman"}, "filterAnnotModal": {"color": "<PERSON><PERSON>", "includeReplies": "<PERSON><PERSON><PERSON>", "filters": "Penap<PERSON>", "user": "pengguna", "type": "taip", "status": "Status"}, "state": {"accepted": "Diterima", "rejected": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "set": "Tetapkan status:", "setBy": "ditetapkan oleh", "none": "tiada", "marked": "<PERSON><PERSON><PERSON>", "unmarked": "Tidak bertanda"}, "measurementOverlay": {"scale": "Nisbah Skala", "angle": "sudut", "distance": "Jarak", "perimeter": "Perimeter", "area": "<PERSON><PERSON>an", "distanceMeasurement": "Pengukuran Jarak", "perimeterMeasurement": "Pengukuran Perimeter", "arcMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "areaMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "countMeasurement": "<PERSON>", "radius": "<PERSON><PERSON><PERSON>", "count": "<PERSON>", "length": "Panjang"}, "measurementOption": {"scale": "<PERSON><PERSON><PERSON>"}, "stylePopup": {"textStyle": "<PERSON><PERSON>", "colors": "<PERSON><PERSON>", "invalidFontSize": "Saiz fon mestilah kurang da<PERSON>ada", "labelText": "Teks Label", "labelTextPlaceholder": "Tambahkan teks label"}, "styleOption": {"style": "<PERSON><PERSON>", "solid": "Pa<PERSON>t", "cloudy": "<PERSON><PERSON><PERSON>"}, "slider": {"opacity": "Kelegapan", "thickness": "Strok", "text": "Saiz Teks"}, "shared": {"page": "<PERSON><PERSON>", "precision": "Ketepatan", "enableSnapping": "<PERSON><PERSON><PERSON> menjentik untuk alat ukuran"}, "watermark": {"title": "Tera air", "addWatermark": "Tambah Tera Air", "size": "Saiz", "location": "Pilih lokasi untuk mengedit tera air", "text": "Teks", "style": "<PERSON><PERSON>", "resetAllSettings": "Tetapkan <PERSON>", "font": "buat", "addNew": "Tambah baru", "locations": {"center": "Pusat", "topLeft": "<PERSON><PERSON>", "topRight": "<PERSON><PERSON>", "topCenter": "Pusat Atas", "bottomLeft": "<PERSON><PERSON><PERSON><PERSON> bawah kiri", "bottomRight": "<PERSON><PERSON><PERSON>", "bottomCenter": "P<PERSON><PERSON>"}}, "thumbnailPanel": {"delete": "Padam", "rotateClockwise": "ikut arah jam", "rotateCounterClockwise": "lawan jam"}, "thumbnailsControlOverlay": {"move": "Pi<PERSON><PERSON><PERSON> halaman"}, "richText": {"bold": "berani", "italic": "Italic", "underline": "<PERSON><PERSON> bawah", "strikeout": "Strikeout", "alignLeft": "Teks dijajarkan ke kiri", "alignRight": "Teks dijajarkan ke kanan", "alignCenter": "Pusat pen<PERSON>jaran teks", "justifyCenter": "Pusat justify teks", "alignTop": "Jajarkan atas", "alignMiddle": "Jajarkan tengah", "alignBottom": "<PERSON><PERSON><PERSON><PERSON> bah<PERSON>an bawah"}, "customStampModal": {"modalName": "<PERSON><PERSON><PERSON>", "stampText": "<PERSON><PERSON> setem", "timestampText": "Teks cap masa", "Username": "<PERSON><PERSON>", "Date": "<PERSON><PERSON><PERSON>", "Time": "<PERSON><PERSON>", "fontStyle": "gaya fon", "dateFormat": "Format tarikh", "month": "bulan", "day": "<PERSON>", "year": "tahun", "hour": "Jam", "minute": "minit", "second": "Kedua", "textColor": "Warna teks", "backgroundColor": "<PERSON><PERSON> latar belakang"}, "pageRedactModal": {"addMark": "Tambah Mark", "pageSelection": "<PERSON><PERSON><PERSON><PERSON>", "current": "Muka surat ini", "specify": "<PERSON><PERSON><PERSON>", "odd": "Muka surat ganjil sahaja", "even": "Muka surat genap sahaja", "header": "<PERSON><PERSON><PERSON>", "specifyPlaceholder": "1, 3, 5-10"}, "lineStyleOptions": {"title": "<PERSON><PERSON>"}}, "warning": {"deletePage": {"deleteTitle": "<PERSON><PERSON>", "deleteMessage": "<PERSON><PERSON><PERSON> anda pasti mahu memadamkan halaman yang dipilih. Ini tidak boleh dibuat asal", "deleteLastPageMessage": "<PERSON>a tidak boleh memadam semua halaman dalam dokumen."}, "extractPage": {"title": "Ekstrak Halaman", "message": "<PERSON><PERSON>h anda pasti mahu mengekstrak halaman yang dipilih?", "confirmBtn": "Ekstrak", "secondaryBtn": "Ekstrak dan <PERSON>"}, "redaction": {"applyTile": "<PERSON><PERSON><PERSON>", "applyMessage": "Tindakan ini akan mengalih keluar semua item yang dipilih untuk redaksi secara kekal. Ia tidak boleh dibuat asal."}, "deleteBookmark": {"title": "<PERSON><PERSON><PERSON>?", "message": "<PERSON><PERSON>h anda pasti mahu memadamkan penanda halaman ini? Anda tidak boleh membuat asal tindakan ini."}, "deleteOutline": {"title": "<PERSON><PERSON><PERSON>?", "message": "<PERSON><PERSON><PERSON> anda pasti mahu memadamkan garis besar ini?\n\n Garis besar yang mempunyai sebarang garis besar bersarang akan menyebabkan keseluruhannya dipadamkan dan tidak boleh diganti melainkan anda menetapkannya semula."}, "selectPage": {"selectTitle": "<PERSON><PERSON><PERSON>", "selectMessage": "<PERSON>la pilih halaman dan cuba lagi."}, "colorPicker": {"deleteTitle": "Padam<PERSON> warna tersuai", "deleteMessage": "Padamkan warna tersuai yang dipilih? Ia akan dialih keluar daripada palet warna anda."}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(The)", "ellipse": "(O)", "eraser": "(DAN)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(saya)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(Esc)", "signature": "(S)", "squiggly": "(G)", "image": "(saya)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(KEPADA)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Shift + Seret", "zoom3D": "Shift + Tatal"}, "tool": {"pan": "kuali", "select": "<PERSON><PERSON><PERSON>", "Marquee": "Zum Marquee", "Link": "Pautkan URL atau Halaman", "Standard": "Standard", "Custom": "Ada<PERSON>"}, "link": {"url": "URL", "page": "<PERSON><PERSON>", "enterurl": "Masukkan URL yang anda ingin pautkan", "enterpage": "<PERSON><PERSON><PERSON> nombor halaman yang anda ingin pautkan", "urlLink": "p<PERSON>an <PERSON>"}, "Model3D": {"add3D": "Tambah objek 3D", "enterurl": "Masukkan URL objek 3D dalam format glTF", "enterurlOrLocalFile": "Masukkan URL atau muat naik objek 3D dalam format glTF", "formatError": "Hanya format glTF (.glb) disokong"}, "OpenFile": {"enterUrlOrChooseFile": "Masukkan URL atau pilih fail untuk dimuatkan ke dalam WebViewer", "enterUrl": "Masukkan URL fail", "extension": "Sambungan fail", "existingFile": "<PERSON>ail sudah dibuka", "addTab": "Tambah Tab"}, "datePicker": {"previousMonth": "<PERSON><PERSON><PERSON> lepas", "nextMonth": "<PERSON><PERSON><PERSON> depan", "months": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "<PERSON>", "3": "April", "4": "<PERSON><PERSON><PERSON>", "5": "Jun", "6": "Jul<PERSON>", "7": "Ogos", "8": "September", "9": "Oktober", "10": "November", "11": "Disember"}, "monthsShort": {"0": "Jan", "1": "Feb", "2": "<PERSON><PERSON>", "3": "Apr", "4": "<PERSON><PERSON><PERSON>", "5": "Jun", "6": "Jul", "7": "Ogos", "8": "tujuh", "9": "Okt", "10": "Nov", "11": "Dis"}, "weekdays": {"0": "<PERSON><PERSON>", "1": "<PERSON>in", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "Juma<PERSON>", "6": "Sabtu"}, "weekdaysShort": {"0": "<PERSON><PERSON><PERSON>", "1": "saya", "2": "milik awak", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON>b"}, "today": "<PERSON> ini", "invalidDateTime": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>: Input hendaklah sepadan dengan format"}, "formField": {"formFieldPopup": {"fieldName": "<PERSON><PERSON>", "fieldValue": "<PERSON><PERSON>", "readOnly": "Baca sahaja", "multiSelect": "Pelbagai Pilihan", "required": "<PERSON><PERSON><PERSON><PERSON>", "multiLine": "Berbilang talian", "apply": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "flags": "<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>", "radioGroups": "Butang radio dengan Nama Medan yang sama akan tergolong dalam kumpulan yang sama.", "nameRequired": "<PERSON><PERSON> medan diperlukan", "size": "Saiz", "width": "<PERSON><PERSON>", "height": "Ketinggian", "invalidField": {"duplicate": "<PERSON><PERSON> sudah wujud", "empty": "<PERSON><PERSON> tidak boleh kosong"}}, "apply": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "types": {"text": "Teks", "signature": "Tandatangan", "checkbox": "Kotak semak", "radio": "Butang radio", "listbox": "Kotak senarai", "combobox": "Kotak kombo"}}, "digitalSignatureModal": {"certification": "<PERSON><PERSON><PERSON><PERSON>", "Certification": "<PERSON><PERSON><PERSON><PERSON>", "signature": "tandatangan", "Signature": "Tandatangan", "valid": "sah", "invalid": "tidak sah", "unknown": "tidak <PERSON>i", "title": "{{type}} Sifat", "header": {"documentIntegrity": "Integriti Dokumen", "identitiesTrust": "Identiti & Amanah", "generalErrors": "<PERSON><PERSON><PERSON><PERSON>", "digestStatus": "Status Digest"}, "documentPermission": {"noChangesAllowed": "{{editor}} telah menya<PERSON>kan bahawa tiada perubahan dibenarkan untuk dokumen ini", "formfillingSigningAllowed": "{{editor}} telah men<PERSON> bahawa <PERSON> dan <PERSON> dibenarkan untuk dokumen ini. <PERSON><PERSON>da per<PERSON>han lain dibenarkan.", "annotatingFormfillingSigningAllowed": "{{editor}} te<PERSON> men<PERSON> bah<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON> diben<PERSON>an untuk dokumen ini. <PERSON><PERSON>da per<PERSON>han lain dibenarkan.", "unrestricted": "{{editor}} telah menyatakan bahawa tiada sekatan untuk dokumen ini."}, "digestAlgorithm": {"preamble": "Algoritma ringkasan yang digunakan untuk menandatangani tandatangan:", "unknown": "Algoritma ringkasan yang digunakan untuk menandatangani tandatangan tidak diketahui."}, "trustVerification": {"none": "Tiada keputusan pengesahan amanah terperinci tersedia.", "current": "<PERSON><PERSON><PERSON> amanah dicuba berkenaan dengan masa semasa", "signing": "<PERSON><PERSON><PERSON> amanah dicuba berkenaan dengan masa menandatangani: {{trustVerificationTime}}", "timestamp": "Pengesahan amanah dicuba berkenaan dengan cap waktu terbenam yang selamat: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "Identiti penandatangan ialah", "valid": "sah.", "unknown": "tidak diket<PERSON>."}, "summaryBox": {"summary": "{{jenis}} digital ialah {{status}}", "signedBy": ", ditandatangani oleh {{name}}"}}, "digitalSignatureVerification": {"certifier": "<PERSON><PERSON><PERSON><PERSON>", "certified": "di<PERSON><PERSON><PERSON>", "Certified": "<PERSON><PERSON><PERSON><PERSON>", "signer": "tanda", "signed": "ditandatangani", "Signed": "Ditandatangani", "by": "oleh", "on": "pada", "disallowedChange": "<PERSON><PERSON><PERSON>: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Medan tandatangan yang tidak ditandatangani: {{fieldName}}", "trustVerification": {"current": "<PERSON>sa pengesahan yang digunakan ialah masa semasa", "signing": "<PERSON><PERSON> pen<PERSON> adalah dari jam pada komputer penandatangan", "timestamp": "<PERSON><PERSON> pen<PERSON>ahan adalah daripada cap waktu selamat yang dibenamkan dalam dokumen", "verifiedTrust": "Keputusan pengesahan amanah: <PERSON><PERSON><PERSON><PERSON>", "noTrustVerification": "Tiada keputusan pengesahan amanah terperinci tersedia."}, "permissionStatus": {"noPermissionsStatus": "Tiada status kebenaran untuk melaporkan.", "permissionsVerificationDisabled": "Pengesahan kebenaran telah di<PERSON>.", "hasAllowedChanges": "Dokumen mempunyai perubahan yang diben<PERSON>an oleh tetapan kebenaran tandatangan.", "invalidatedByDisallowedChanges": "Dokumen mempunyai perubahan yang tidak dibenarkan oleh tetapan kebenaran tandatangan.", "unmodified": "Dokumen itu tidak diubah suai sejak ia dibuat"}, "trustStatus": {"trustVerified": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> dalam {{verificationType}} ber<PERSON><PERSON>.", "untrusted": "Kepercayaan tidak dapat diwujudkan.", "trustVerificationDisabled": "<PERSON><PERSON><PERSON> amanah telah di<PERSON>.", "noTrustStatus": "Tiada status amanah untuk dilaporkan."}, "digestStatus": {"digestInvalid": "Pencernaan tidak betul.", "digestVerified": "<PERSON><PERSON> betul.", "digestVerificationDisabled": "Pengesahan ringkasan telah di<PERSON>.", "weakDigestAlgorithmButDigestVerifiable": "Digest adalah betul, tetapi algoritma digest lemah dan tidak selamat.", "noDigestStatus": "Tiada status ringkasan untuk dilaporkan.", "unsupportedEncoding": "Tiada SignatureHandler yang dipasang dapat mengecam pengekodan tandatangan"}, "documentStatus": {"noError": "Tiada ralat umum untuk dilaporkan.", "corruptFile": "SignatureHandler melaporkan rasuah fail.", "unsigned": "Tandatangan itu belum lagi ditandatangani secara kriptografi.", "badByteRanges": "SignatureHandler melaporkan rasuah dalam ByteRanges dalam tandatangan digital.", "corruptCryptographicContents": "SignatureHandler melaporkan rasuah dalam Kandungan tandatangan digital."}, "signatureDetails": {"signatureDetails": "<PERSON><PERSON><PERSON>", "contactInformation": "Maklumat per<PERSON>bungan", "location": "<PERSON><PERSON>", "reason": "Sebab", "signingTime": "<PERSON><PERSON>", "noContactInformation": "Tiada maklumat hubungan diberikan", "noLocation": "Tiada lokasi disediakan", "noReason": "<PERSON><PERSON><PERSON> alasan di<PERSON>n", "noSigningTime": "<PERSON>iada masa tandatangan di<PERSON>ui"}, "panelMessages": {"noSignatureFields": "Dokumen ini tidak mempunyai medan tandatangan", "certificateDownloadError": "<PERSON><PERSON> di<PERSON>ui semasa cuba memuat turun sijil yang dipercayai", "localCertificateError": "Terdapat beberapa isu dengan membaca sijil tempatan"}}, "cropPopUp": {"title": "Halaman untuk <PERSON>kas", "allPages": "<PERSON><PERSON><PERSON>", "singlePage": "Muka surat ini", "multiPage": "<PERSON><PERSON><PERSON><PERSON>", "cropDimensions": "<PERSON><PERSON><PERSON>", "dimensionInput": {"unitOfMeasurement": "Unit", "autoTrim": "Auto-trim"}, "cropModal": {"applyTitle": "<PERSON><PERSON><PERSON> tanaman?", "applyMessage": "Tindakan ini akan memangkas semua halaman yang dipilih yang dipilih secara kekal. Ia tidak boleh dibuat asal.", "cancelTitle": "Batalkan pemang<PERSON>an?", "cancelMessage": "<PERSON><PERSON><PERSON> anda pasti mahu membatalkan pemangkasan semua halaman yang dipilih?"}}, "redactionPanel": {"noMarkedRedactions": "<PERSON><PERSON> menyunting dengan menandakan teks, kawasan, halaman atau membuat carian.", "redactionSearchPlaceholder": "Tambahkan mengikut carian kata kunci atau corak", "redactionCounter": "Ditanda untuk Redaksi", "clearMarked": "<PERSON><PERSON>", "redactAllMarked": "<PERSON><PERSON><PERSON>", "redactionItem": {"regionRedaction": "Penyuntingan wilayah", "fullPageRedaction": "<PERSON><PERSON><PERSON><PERSON> halaman penuh", "fullVideoFrameRedaction": "Penyuntingan Video", "audioRedaction": "Penyuntingan Audio", "fullVideoFrameAndAudioRedaction": "Penyuntingan Video dan Audio", "image": "<PERSON><PERSON><PERSON>"}, "expand": "Kembangkan", "collapse": "<PERSON><PERSON><PERSON>", "searchResults": "Keputusan Carian", "search": {"creditCards": "<PERSON><PERSON> kredit", "phoneNumbers": "Nombor telefon", "images": "<PERSON><PERSON><PERSON>", "emails": "E-mel", "pattern": "Corak", "start": "<PERSON><PERSON> membuat carian anda"}}, "wv3dPropertiesPanel": {"propertiesHeader": "Properties", "miscValuesHeader": "<PERSON><PERSON><PERSON>", "emptyPanelMessage": "<PERSON>lih elemen untuk melihat sifatnya"}, "languageModal": {"selectLanguage": "<PERSON><PERSON><PERSON>"}}