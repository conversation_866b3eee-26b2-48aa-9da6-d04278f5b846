/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[13],{398:function(ia,y,e){function fa(e){e.Ia();e.advance();var f=e.current.textContent;e.Ua();return f}function x(e){var f=[];for(e.Ia();e.advance();){var h=e.Ma();"field"===h?f.push(String(e.fa("name"))):Object(aa.j)("unrecognised field list element: "+h)}e.Ua();return f}function ha(e,f){return f?"false"!==e:"true"===e}function ea(e,f){var h=e.Ma();switch(h){case "javascript":return{name:"JavaScript",javascript:e.current.textContent};
case "uri":return{name:"URI",uri:e.fa("uri")};case "goto":h=null;e.Ia();if(e.advance()){var n=e.fa("fit");h={page:e.fa("page"),fit:n};if("0"===h.page)Object(aa.j)("null page encountered in dest");else switch(f=f(Number(h.page)),n){case "Fit":case "FitB":break;case "FitH":case "FitBH":h.top=f.pa({x:0,y:e.fa("top")||0}).y;break;case "FitV":case "FitBV":h.left=f.pa({x:e.fa("left")||0,y:0}).x;break;case "FitR":n=f.pa({x:e.fa("left")||0,y:e.fa("top")||0});f=f.pa({x:e.fa("right")||0,y:e.fa("bottom")||0});
f=new pa.d(n.x,n.y,f.x,f.y);h.top=f.ja;h.left=f.ma;h.bottom=f.ka;h.right=f.na;break;case "XYZ":n=f.pa({x:e.fa("left")||0,y:e.fa("top")||0});h.top=n.y;h.left=n.x;h.zoom=e.fa("zoom")||0;break;default:Object(aa.j)("unknown dest fit: "+n)}h={name:"GoTo",dest:h}}else Object(aa.j)("missing dest in GoTo action");e.Ua();return h;case "submit-form":h={name:"SubmitForm",url:e.fa("url"),format:e.fa("format"),method:e.fa("method")||"POST",exclude:ha(e.fa("exclude"),!1)};f=e.fa("flags");h.flags=f?f.split(" "):
[];for(e.Ia();e.advance();)switch(f=e.Ma(),f){case "fields":h.fields=x(e);break;default:Object(aa.j)("unrecognised submit-form child: "+f)}e.Ua();return h;case "reset-form":h={name:"ResetForm",exclude:ha(e.fa("exclude"),!1)};for(e.Ia();e.advance();)switch(f=e.Ma(),f){case "fields":h.fields=x(e);break;default:Object(aa.j)("unrecognised reset-form child: "+f)}e.Ua();return h;case "hide":h={name:"Hide",hide:ha(e.fa("hide"),!0)};for(e.Ia();e.advance();)switch(f=e.Ma(),f){case "fields":h.fields=x(e);break;
default:Object(aa.j)("unrecognised hide child: "+f)}e.Ua();return h;case "named":return{name:"Named",action:e.fa("name")};default:Object(aa.j)("Encountered unexpected action type: "+h)}return null}function da(e,f,h){var n={};for(e.Ia();e.advance();){var r=e.Ma();switch(r){case "action":r=e.fa("trigger");if(f?-1!==f.indexOf(r):1){n[r]=[];for(e.Ia();e.advance();){var w=ea(e,h);Object(ja.isNull)(w)||n[r].push(w)}e.Ua()}else Object(aa.j)("encountered unexpected trigger on field: "+r);break;default:Object(aa.j)("encountered unknown action child: "+
r)}}e.Ua();return n}function ba(e){return new xa.a(e.fa("r")||0,e.fa("g")||0,e.fa("b")||0,e.fa("a")||1)}function w(e,f){var h=e.fa("name"),n=e.fa("type")||"Type1",r=e.fa("size"),w=f.pa({x:0,y:0});r=f.pa({x:Number(r),y:0});f=w.x-r.x;w=w.y-r.y;h={name:h,type:n,size:Math.sqrt(f*f+w*w)||0,strokeColor:[0,0,0],fillColor:[0,0,0]};for(e.Ia();e.advance();)switch(n=e.Ma(),n){case "stroke-color":h.strokeColor=ba(e);break;case "fill-color":h.fillColor=ba(e);break;default:Object(aa.j)("unrecognised font child: "+
n)}e.Ua();return h}function z(e){return{value:e.fa("value"),displayValue:e.fa("display-value")||void 0}}function r(e){var f=[];for(e.Ia();e.advance();){var h=e.Ma();switch(h){case "option":f.push(z(e));break;default:Object(aa.j)("unrecognised options child: "+h)}}e.Ua();return f}function h(e,f){var h=e.fa("name"),n={type:e.fa("type"),quadding:e.fa("quadding")||"Left-justified",maxLen:e.fa("max-len")||-1},x=e.fa("flags");Object(ja.isString)(x)&&(n.flags=x.split(" "));for(e.Ia();e.advance();)switch(x=
e.Ma(),x){case "actions":n.actions=da(e,["C","F","K","V"],function(){return f});break;case "default-value":n.defaultValue=fa(e);break;case "font":n.font=w(e,f);break;case "options":n.options=r(e);break;default:Object(aa.j)("unknown field child: "+x)}e.Ua();return new window.Annotations.ga.ra(h,n)}function f(e,f){switch(e.type){case "Tx":try{if(Object(na.c)(e.actions))return new ka.a.DatePickerWidgetAnnotation(e,f)}catch(sa){Object(aa.j)(sa)}return new ka.a.TextWidgetAnnotation(e,f);case "Ch":return e.flags.get(za.WidgetFlags.COMBO)?
new ka.a.ChoiceWidgetAnnotation(e,f):new ka.a.ListWidgetAnnotation(e,f);case "Btn":return e.flags.get(za.WidgetFlags.PUSH_BUTTON)?new ka.a.PushButtonWidgetAnnotation(e,f):e.flags.get(za.WidgetFlags.RADIO)?new ka.a.RadioButtonWidgetAnnotation(e,f):new ka.a.CheckButtonWidgetAnnotation(e,f);case "Sig":return new ka.a.SignatureWidgetAnnotation(e,f);default:Object(aa.j)("Unrecognised field type: "+e.type)}return null}function n(e,f){var h={number:e.fa("number")};for(e.Ia();e.advance();){var n=e.Ma();switch(n){case "actions":h.actions=
da(e,["O","C"],f);break;default:Object(aa.j)("unrecognised page child: "+n)}}e.Ua();return h}function ca(e,r,z,y){var ca=[],ea={};e.Ia();var fa=[],ha={},ia=[];Object(la.a)(function(){if(e.advance()){var z=e.Ma();switch(z){case "calculation-order":fa="calculation-order"===e.Ma()?x(e):[];break;case "document-actions":ha=da(e,["Init","Open"],r);break;case "pages":z=[];for(e.Ia();e.advance();){var y=e.Ma();switch(y){case "page":z.push(n(e,r));break;default:Object(aa.j)("unrecognised page child: "+y)}}e.Ua();
ia=z;break;case "field":y=h(e,r(1));ea[y.name]=y;break;case "widget":z={border:{style:"Solid",width:1},backgroundColor:[],fieldName:e.fa("field"),page:e.fa("page"),index:e.fa("index")||0,rotation:e.fa("rotation")||0,flags:[],isImporting:!0};(y=e.fa("appearance"))&&(z.appearance=y);(y=e.fa("flags"))&&(z.flags=y.split(" "));for(e.Ia();e.advance();)switch(y=e.Ma(),y){case "rect":var ja=e,ka=r(Number(z.page));y=ka.pa({x:ja.fa("x1")||0,y:ja.fa("y1")||0});ja=ka.pa({x:ja.fa("x2")||0,y:ja.fa("y2")||0});y=
new pa.d(y.x,y.y,ja.x,ja.y);y.normalize();z.rect={x1:y.x1,y1:y.y1,x2:y.x2,y2:y.y2};break;case "border":y=e;ja={style:y.fa("style")||"Solid",width:y.fa("width")||1,color:[0,0,0]};for(y.Ia();y.advance();)switch(ka=y.Ma(),ka){case "color":ja.color=ba(y);break;default:Object(aa.j)("unrecognised border child: "+ka)}y.Ua();z.border=ja;break;case "background-color":z.backgroundColor=ba(e);break;case "actions":z.actions=da(e,"E X D U Fo Bl PO PC PV PI".split(" "),r);break;case "appearances":y=e;ja=Object(na.b)(z,
"appearances");for(y.Ia();y.advance();)if(ka=y.Ma(),"appearance"===ka){ka=y.fa("name");var sa=Object(na.b)(ja,ka);ka=y;for(ka.Ia();ka.advance();){var la=ka.Ma();switch(la){case "Normal":Object(na.b)(sa,"Normal").data=ka.current.textContent;break;default:Object(aa.j)("unexpected appearance state: ",la)}}ka.Ua()}else Object(aa.j)("unexpected appearances child: "+ka);y.Ua();break;case "extra":y=e;ja=r;ka={};for(y.Ia();y.advance();)switch(sa=y.Ma(),sa){case "font":ka.font=w(y,ja(1));break;default:Object(aa.j)("unrecognised extra child: "+
sa)}y.Ua();y=ka;y.font&&(z.font=y.font);break;case "captions":ja=e;y={};(ka=ja.fa("Normal"))&&(y.Normal=ka);(ka=ja.fa("Rollover"))&&(y.Rollover=ka);(ja=ja.fa("Down"))&&(y.Down=ja);z.captions=y;break;default:Object(aa.j)("unrecognised widget child: "+y)}e.Ua();(y=ea[z.fieldName])?(z=f(y,z),ca.push(z)):Object(aa.j)("ignoring widget with no corresponding field data: "+z.fieldName);break;default:Object(aa.j)("Unknown element encountered in PDFInfo: "+z)}return!0}return!1},function(){e.Ua();z({calculationOrder:fa,
widgets:ca,fields:ea,documentActions:ha,pages:ia,custom:[]})},y)}e.r(y);e.d(y,"parse",function(){return ca});var aa=e(2),ja=e(0);e.n(ja);var ka=e(106),pa=e(3),xa=e(17),la=e(19),na=e(93),za=e(30)}}]);}).call(this || window)
