﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_userControl_Header : System.Web.UI.UserControl
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            ViewState["user_id_string"] = Server.HtmlEncode(ssoUser.empNo) + "_" + Server.HtmlEncode(ssoUser.empOrgcd) + Server.HtmlEncode(ssoUser.empDeptcd) + "_" + ssoUser.empName;
            LT_loginName.Text = Server.HtmlEncode(ssoUser.empName);
            TreeNode.SysId = "T";
        }
    }
}