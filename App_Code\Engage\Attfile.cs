﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;

namespace Engage
{
	/// <summary>
	/// Summary description for Attfile
	/// </summary>
	public class Attfile : mySQLHelper
	{
		 #region 私有變數

        private string _errorMessage;

        private string _filetype;
        private int _eaid;
        private Int64 _seqsn;
        private int _ver;

        private string _valid;
        private string _doc;
        //private string _uploaddate;
        private string _filename;
        private string _filetxt;
        private string _file_url;
        private string _empno;


        #endregion

        #region 建構子

        public Attfile()
        {
            _errorMessage = String.Empty;
            _eaid = 0;
            _seqsn = 0;
            _ver = 0;

            _filetype = String.Empty;
            _valid = String.Empty;
            _doc = String.Empty;
            _filename = String.Empty;
            _filetxt = String.Empty;
            _file_url = String.Empty;
            _empno = String.Empty;
        }

        #endregion

        #region 公有屬性

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// 文件類型(NA:規劃書、NE:成本會議審查附件檔、NG:契約簽辦附件檔、NP:計價法試算電子檔)
        /// </summary>
        public string FileType
        {
            get { return _filetype; }
            set { _filetype = value; }
        }

        /// <summary>
        /// 流水號
        /// </summary>
        public Int64 Seqsn
        {
            get { return _seqsn; }
            set { _seqsn = value; }
        }


        /// <summary>
        /// 檔案流水號
        /// </summary>
        public int EaId
        {
            get { return _eaid; }
            set { _eaid = value; }
        }
        /// <summary>
        /// 版次
        /// </summary>
        public int Ver
        {
            get { return _ver; }
            set { _ver = value; }
        }

		/// <summary>
		/// 1-有效。0-無效
		/// </summary>
		public string FileValid
		{
			get { return _valid; }
			set { _valid = value; }
		}

		/// <summary>
		/// 文件名稱
		/// </summary>
		public string FileDoc
		{	get { return _doc; }
			set { _doc = value; }
		}
        /// <summary>
        /// 檔案名稱
        /// </summary>
        public string FileName
        {
            get { return _filename; }
            set { _filename = value; }
        }
        /// <summary>
        /// 電子檔說明
        /// </summary>
        public string FileTxt
        {
            get { return _filetxt; }
            set { _filetxt = value; }
        }

        /// <summary>
        /// 檔案儲存位置含檔名
        /// </summary>
        public string File_URL
        {
            get { return _file_url; }
            set { _file_url = value; }
        }
        /// <summary>
        /// 工號
        /// </summary>
        public string EmpNo
        {
            get { return _empno; }
            set { _empno = value; }
        }

        #endregion

        #region 公有函式
        public bool Insert()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_file_insert";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            //oCmd.Parameters.AddWithValue("@ver", _ver);
            oCmd.Parameters.AddWithValue("@ea_filetype", _filetype);
            oCmd.Parameters.AddWithValue("@ea_doc", _doc);
            oCmd.Parameters.AddWithValue("@ea_filename", _filename);
            oCmd.Parameters.AddWithValue("@ea_file_url", _file_url);
            oCmd.Parameters.AddWithValue("@ea_keyinempno", _empno);

            try
            {
                _eaid = int.Parse(this.getTopOne(oCmd, CommandType.StoredProcedure));
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        public bool InsertLog()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_engage_file_download";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@ea_filename", _filename);
            oCmd.Parameters.AddWithValue("@ea_keyinempno", _empno);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }
        /// <summary>
        /// 刪除檔案
        /// </summary>
        /// <returns>Y/N</returns>
        public string Delete()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_gpi_file_delete";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_filename", _filename);
            oCmd.Parameters.AddWithValue("@gpi_keyinempno", _empno);

            return this.getTopOne(oCmd, CommandType.StoredProcedure);
        }
        public bool UpdateFileTxt()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
UPDATE gpi_attfile1 
SET gpi_filetxt=@gpi_filetxt 
WHERE gpi_seqsn=@gpi_seqsn AND gpi_filetype=@gpi_filetype
";

            oCmd.Parameters.AddWithValue("@gpi_seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@gpi_filetype", _filetype);
            oCmd.Parameters.AddWithValue("@gpi_filetxt", _filetxt);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;

        }

		/// <summary>
		/// NA:規劃書, NE:成本會議審查附件檔, NG:契約簽辦附件檔, NP:計價法試算電子檔
		/// </summary>
		/// <returns></returns>
        public DataTable GetFile2()
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar(8),ea_uploaddate,112) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  --where ea_seqsn=@seqsn AND ea_filetype=@filetype
	  where 1=1 
";

			if (_seqsn != 0)
			{
				oCmd.CommandText += " AND ea_seqsn=@seqsn";
				oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			}
			if (_filetype != "")
			{
				oCmd.CommandText += " AND ea_filetype=@filetype";
				oCmd.Parameters.AddWithValue("@filetype", _filetype);
			}
			if (_valid != "")
			{
				oCmd.CommandText += " AND ea_id IN (SELECT max(ea_id) FROM engage_attfile2 WHERE ea_seqsn=@seqsn AND ea_filetype=@filetype AND ea_valid = @ea_valid GROUP BY ea_doc)";
				oCmd.Parameters.AddWithValue("@ea_valid", _valid);
			}
			if (_eaid != 0)
			{
				oCmd.CommandText += " AND ea_id = @ea_id";
				oCmd.Parameters.AddWithValue("@ea_id", _eaid);
			}

            DataTable dt = this.getDataTable(oCmd, CommandType.Text);
            return dt;
        }

		public DataTable GetFile2_his()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @ea_doc nvarchar(200), @ea_ver tinyint
	select @ea_ver=ea_ver, @ea_doc=ea_doc from engage_attfile2 where ea_id=@ea_id 

	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar(8),ea_uploaddate,112) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  where ea_seqsn=@seqsn AND ea_filetype=@filetype
			and ea_doc=@ea_doc and ea_ver < @ea_ver
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@filetype", _filetype);
			oCmd.Parameters.AddWithValue("@ea_id", _eaid);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		public DataTable GetFile1()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	select ea_id, ea_seqsn, ea_ver, ea_valid, ea_filetype, ea_doc,CONVERT(varchar(8),ea_uploaddate,112) as ea_uploaddate, 
			ea_filename, ea_filetxt, ea_keyinempno, ea_keyinempname, ea_file_url
			,phy_filename='\'+eb_class+'\'+eb_year+'\'+ea_filename
	  from engage_attfile2
	  join engage_base on ea_seqsn=eb_seqsn
	  --where ea_seqsn=@seqsn AND ea_filetype=@filetype
	  where 1=1 
";

			//oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			//oCmd.Parameters.AddWithValue("@filetype", _filetype);
			if (_seqsn != 0)
			{
				oCmd.CommandText += " AND ea_seqsn=@seqsn";
				oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			}
			if (_filetype != "")
			{
				oCmd.CommandText += " AND ea_filetype=@filetype";
				oCmd.Parameters.AddWithValue("@filetype", _filetype);
			}
			if (_eaid != 0)
			{
				oCmd.CommandText += " AND ea_id = @ea_id";
				oCmd.Parameters.AddWithValue("@ea_id", _eaid);
			}

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
        #endregion
    }



}