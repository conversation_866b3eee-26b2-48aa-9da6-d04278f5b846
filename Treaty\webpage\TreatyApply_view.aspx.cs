﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyApply_view : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }

    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {

            case "A":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視無收入資訊";
                break;
            case "N":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視NDA資訊";
                break;
            case "F":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
                break;
            //case "U":
            //    strResult = "檢視國外契約資訊";
            //    break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視標案資訊";
                break;
            case "C":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
                break;
            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion

    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;

        btnEngage.Text = Server.HtmlDecode(Server.HtmlEncode(GetEngageNDAText(strCaseNo)));
        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "A":
                bResult = true;
                break;
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            //case "U":
            //    bResult = true;
            //    break;
            case "R":
                bResult = true;
                break;
            case "C":
                bResult = true;
                break;
            case "F":
                bResult = true;
                break;
            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion

    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    protected void btnEngage_Click(object sender, EventArgs e)
    {
        /*        string strCaseNo = txtComplexNo.Text.Trim();
                //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
                string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
                string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
                string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
                string strWinOpen = string.Empty; //宣告開窗的URL字串
                string script = "";
                switch (ViewState["tr_class"].ToString())
                {
                    case "N": //洽案
                        strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                        break;

                    case "R": //標案
                        strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                        break;

                    case "M": // NDA
                        strWinOpen = string.Format("{0}/nda/NDA_readonly.aspx?kind=NDA&seqsn=&contno={1}&style=2&readstyle=2", strNDA_Path, strCaseNo.Substring(0, 12));
                        break;

                    case "A": // 國外契約   norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af <https://webdev5.itri.org.tw/norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af> 

                        strWinOpen = string.Format("{0}/norcont/norcont/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                        break;
                }
                script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
                Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
         */

        string strCaseNo = txtComplexNo.Text.Trim();
        string strCaseNo_C = txtOldContno.Text.Trim();
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
        string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();
        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案/Engage/Base/caseBase.aspx?contno=xxxxx
                strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                      // strWinOpen = string.Format("{0}/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-",""));
                strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
                break;

            case "A": // 國外契約   norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af <https://webdev5.itri.org.tw/norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af> 
                strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                break;
            case "F": // 國內契約  
                strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
                break;
            case "C": // 工服
                strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                break;

        }
        script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        //script = @" <script> alert('" + strWinOpen + "'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);

    }
    #endregion

    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​


    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        //txt_px_name.Attributes.Add("onChange", "Find_empno_kw('txt_px_name',2);");
        txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));


        if (!IsPostBack)
        {
            cb_conttype_b0.Checked = false;
            cb_conttype_b0.Enabled = false;
            cb_conttype_b1.Checked = false;
            cb_conttype_b1.Enabled = false;
            cb_conttype_d4.Checked = false;
            cb_conttype_d4.Enabled = false;
            cb_conttype_d5.Checked = false;
            cb_conttype_d5.Enabled = false;
            cb_conttype_d7.Checked = false;
            cb_conttype_d7.Enabled = false;

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            if (Request.QueryString["contno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["contno"].Replace("-", "")) || (Request.QueryString["contno"].Length > 15))
                    Response.Redirect("../danger.aspx");
                ViewState["contno"] = Request.QueryString["contno"].ToString();

                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyApply_contnoToseno";
                //SDS_NR.SelectParameters.Add("contno", SQLInjectionReplaceAll(ViewState["contno"].ToString()));
                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                //{
                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_NR.DataBind();
                //System.Data.DataView dv_contnoToseno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                SqlCommand oCmd_3 = new SqlCommand();
                oCmd_3.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                oCmd_3.CommandText = "esp_TreatyApply_contnoToseno";
                oCmd_3.CommandType = CommandType.StoredProcedure;
                oCmd_3.Parameters.AddWithValue("contno", oRCM.SQLInjectionReplaceAll(ViewState["contno"].ToString()));
                SqlDataAdapter oda_3 = new SqlDataAdapter(oCmd_3);
                DataSet ds_3 = new DataSet();
                oda_3.Fill(ds_3, "myTable");
                DataView dv_contnoToseno = ds_3.Tables[0].DefaultView;
                if (dv_contnoToseno.Count >= 1)
                {
                    ViewState["seno"] = dv_contnoToseno[0][0].ToString();
                }
                ds_3.Dispose();
                oCmd_3.Dispose();
                oda_3.Dispose();
            }
            if (Request["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length > 8))
                    Response.Redirect("..\\danger.aspx");
                ViewState["seno"] = Request["seno"];
            }

            if (ViewState["seno"] == null)
                Response.Redirect("..\\danger.aspx");

            SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
            ssoUser.GetEmpInfo();
            //SDS_auth.SelectParameters.Clear();
            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_auth.SelectCommand = "esp_TreatyApply_Auth";
            //SDS_auth.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_auth.SelectParameters.Add("contno", SQLInjectionReplaceAll(""));
            //SDS_auth.SelectParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());


            SqlCommand oCmd_1 = new SqlCommand();
            oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd_1.CommandType = CommandType.StoredProcedure;
            oCmd_1.CommandText = "esp_TreatyApply_Auth";
            oCmd_1.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            oCmd_1.Parameters.AddWithValue("contno", "");
            oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            DataSet ds_1 = new DataSet();
            oda_1.Fill(ds_1, "myTable");
            DataView dv_auth = ds_1.Tables[0].DefaultView;
            if (dv_auth.Count >= 1)
            {
                string str_auth = dv_auth[0][0].ToString();
                if (str_auth == "W") btEdit.Visible = true;
                if (str_auth == "X")
                    Response.Redirect("../NoAuthRight.aspx");
            }
            ds_1.Dispose();
            oCmd_1.Dispose();
            oda_1.Dispose();
            Treaty_log(ViewState["seno"].ToString(), "檢視申請單", "", ViewState["seno"].ToString(), "treaty\\TreatyApply_view.aspx");
            BindData();
            BindData_file();
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            txt_req_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }

            ViewState["isPC"] = "false";
            Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", " <script>reflash_topic('Client', p);</script> ");
            //ScriptManager.RegisterStartupScript(this.Page, GetType(), "platformCheck", "reflash_topic('Client', p);");
        }
        if (Request.Params.Get("__EVENTTARGET") == "Client")
        {
            string p = Request.Params.Get("__EVENTARGUMENT").ToString();
            bool isWin, isMac, isLinux, isUnix = false;
            isWin = p.IndexOf("Win") > -1;  //Windows : Win32、Win16
            isMac = p.IndexOf("Mac") > -1;  //MacOS: MacIntel、Macintosh、MacPPC、Mac68K
            isUnix = p.IndexOf("X11") > -1; //Unix
            isLinux = p.IndexOf("Linux") > -1; //Linux: Linux x86_64、Linux x86_64 X11、Linux ppc64

            //Linux 要多加判斷排除，因為行動裝置Android 系統的Platform參數會是 
            //Linux armv7l、Linux armv8l、Linux aarch64、Linux i686(both Chrome on ChromeOS or Linux x86-64)
            if (p.IndexOf("Linux a") > -1 || p.IndexOf("Linux i") > -1)
            {
                isLinux = false;
            }
            if (isWin || isMac || isLinux || isUnix)
            {
                ViewState["isPC"] = "true";
            }
            else
            {
                ViewState["isPC"] = "false";
            }
        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (IsPostBack)
            Bind_sRC_init(ViewState["tr_class"].ToString());
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        HttpContext.Current.Response.Cache.SetNoServerCaching();
        HttpContext.Current.Response.Cache.SetNoStore();

        Plh_Dynax_sRC.Visible = false;
    }

    private void BindContMoneyType()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            //sqlCmd.CommandType = CommandType.Text;
            //sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";
            //// --- 避免匯出查詢過久而當掉 --- //
            //sqlCmd.CommandTimeout = 0;
            //sqlCmd.Parameters.Clear();

            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_traetyApply_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
            sqlCmd.Parameters.AddWithValue("mode", "MoneyType");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = "esp_treaty_MultiCustomer_List_by_NOs";
        //SDS_company.SelectParameters.Add("customers", SQLInjectionReplaceAll( h_compno.Value.ToString()));
        //SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    public void BindData()
    {

        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_NR.SelectCommand = " select *,( select RecUserName FROM   treaty_ecp_preflow where PARENTGUID= tr_ecp_Treaty01_guid) ECPName from treaty_requisition where  tr_seno = @sn ";
        //SDS_NR.SelectParameters.Add("sn", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());


        SqlCommand oCmd_1 = new SqlCommand();
        oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd_1.CommandType = CommandType.StoredProcedure;
        oCmd_1.CommandText = "esp_traetyApply_modify";
        oCmd_1.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        oCmd_1.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
        oCmd_1.Parameters.AddWithValue("@url", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
        oCmd_1.Parameters.AddWithValue("mode", "view");
        SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        oda_1.Fill(ds_1, "myTable");
        DataView dv = ds_1.Tables[0].DefaultView;
        if (dv.Count >= 1)
        {
            string str_tr_year = Server.HtmlEncode(dv[0]["tr_year"].ToString().Trim());
            string str_tr_orgcd = Server.HtmlEncode(dv[0]["tr_orgcd"].ToString().Trim());
            string str_tr_class = Server.HtmlEncode(dv[0]["tr_class"].ToString().Trim());
            ViewState["tr_class"] = str_tr_class;
            string str_tr_sn = Server.HtmlEncode(dv[0]["tr_sn"].ToString().Trim());
            string str_tr_ver = Server.HtmlEncode(dv[0]["tr_ver"].ToString().Trim());
            string str_tr_seqsn = Server.HtmlEncode(dv[0]["tr_seqsn"].ToString().Trim());
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = Server.HtmlEncode(oRCM.RemoveXss(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn)));//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;
            txtOldContno.Text = Server.HtmlEncode(dv[0]["tr_old_contno"].ToString().Trim());
            btnEngage.Visible = GetEngageNDAVisible(str_actcontno);
            switch (dv[0]["tr_status"].ToString())
            {
                case "2":
                    btEdit.Visible = true;
                    LB_status.Text = "草稿";
                    LB_status.Visible = true;
                    break;
                case "E":
                    LB_status.Text = "主管 " + Server.HtmlEncode(dv[0]["ECPName"].ToString()) + " 簽核中";
                    LB_status.Visible = true;
                    btEdit.Visible = false;
                    break;
                case "3":
                    btEdit.Visible = false;
                    break;
                case "P":
                    btEdit.Visible = false;
                    LB_status.Text = "法務內稽中，暫停轉派法務承辦";
                    break;


            }
            switch (dv[0]["tr_language"].ToString().Trim())
            {
                case "0": LB_language.Text = "其他"; break;
                case "1": LB_language.Text = "中文"; break;
                case "2": LB_language.Text = "英文"; break;
            }

            #region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + SQLInjectionReplaceAll(dv[0]["tr_promoter_no"].ToString().Trim()) + "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tr_promoter_no"].ToString().Trim()));
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);


                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_emp = dt.DefaultView;
            if (dv_emp.Count >= 1)
            {
                txt_req_dept.Text = Server.HtmlEncode(dv_emp[0]["com_deptid"].ToString().Trim());
                txt_promoter_name.Text = Server.HtmlEncode(dv_emp[0]["com_cname"].ToString().Trim());
                txt_promoter_empno.Value = Server.HtmlEncode(dv_emp[0]["com_empno"].ToString().Trim());
                txtTel.Text = Server.HtmlEncode(dv_emp[0]["com_telext"].ToString().Trim());
                txtOrgAbbrName.Text = Server.HtmlEncode(dv_emp[0]["orgName"].ToString().Trim());
                ViewState["com_orgcd"] = Server.HtmlEncode(dv_emp[0]["com_orgcd"].ToString().Trim());
            }

            txt_name.Text = Server.HtmlEncode(dv[0]["tr_name"].ToString().Trim());//洽案（契約名稱）
            #endregion
            #region 客戶
            h_compno.Value = Server.HtmlEncode(dv[0]["tr_compidno_all"].ToString().Trim().Replace("㊣", ","));//簽約對象(多)
            BindData_Customer();
            #endregion
            #region 案件性質
            switch (dv[0]["tr_class"].ToString().Trim())
            {
                case "A":
                    #region A
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_uo.Enabled = true;
                    rb_conttype_ui.Enabled = true;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    #endregion
                    break;
                case "C":
                    #region C
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    cb_conttype_c.Checked = true;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_uo.Checked = false;
                    #endregion
                    break;
                case "M":
                    #region M
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Checked = true;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    if (dv[0]["tr_case_flag"].ToString().Trim() == "1")
                        lb_standar_flag.Visible = true;

                    #endregion
                    break;
                case "N":
                    #region N
                    if (dv[0]["tr_conttype_b0"].ToString().Trim() == "1")
                        cb_conttype_b0.Checked = true;

                    if (dv[0]["tr_conttype_b1"].ToString().Trim() == "1")
                        cb_conttype_b1.Checked = true;
                    else
                        cb_conttype_b1.Checked = false;
                    if (dv[0]["tr_conttype_d4"].ToString().Trim() == "1")
                        cb_conttype_d4.Checked = true;

                    if (dv[0]["tr_conttype_d5"].ToString().Trim() == "1")
                        cb_conttype_d5.Checked = true;
                    else
                        cb_conttype_d5.Checked = false;
                    if (dv[0]["tr_conttype_d7"].ToString().Trim() == "1")
                        cb_conttype_d7.Checked = true;

                    if (dv[0]["tr_conttype_ns"].ToString().Trim() == "1")
                        cb_conttype_ns.Checked = true;

                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    if (dv[0]["tr_amend"].ToString().Trim().Trim() == "1")
                    {
                        lb_Amend_Show.Visible = true;
                        spanContractEdit.Visible = true;
                        rblContractEdit.SelectedValue = Server.HtmlEncode(dv[0]["tr_amend"].ToString().Trim().Trim());
                        txtContractEdit.Text = Server.HtmlEncode(dv[0]["tr_amend_other_desc"].ToString().Trim().Trim());
                    }

                    #endregion
                    break;
                case "R":
                    #region R
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = true;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    #endregion
                    break;
                case "S":
                    #region S
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_bd.Checked = true;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    #endregion
                    break;
                case "T":
                    #region T
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = true;
                    rb_conttype_other.Checked = true;
                    cb_conttype_c.Enabled = false;
                    txt_class_other_desc.Text = Server.HtmlEncode(dv[0]["tr_class_other_desc"].ToString().Trim());
                    PL_CoPromoter.Visible = true;
                    h_px_empno.Value = Server.HtmlEncode(dv[0]["tr_promoter_no_other"].ToString().Trim());


                    if (h_px_empno.Value != "")
                    {
                        //SDS_emp.SelectCommand = " select rtrim(com_cname) com_cname from common..comper where com_empno ='" + SQLInjectionReplaceAll(h_px_empno.Value) + "'  ";
                        //SDS_emp.DataBind();
                        //System.Data.DataView dv_px = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

                        SqlCommand oCmd_4 = new SqlCommand();
                        oCmd_4.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                        oCmd_4.CommandText = "select rtrim(com_cname) com_cname from common..comper where com_empno =@com_empno";
                        oCmd_4.Parameters.AddWithValue("com_empno", oRCM.SQLInjectionReplaceAll(h_px_empno.Value));
                        oCmd_4.CommandType = CommandType.Text;
                        SqlDataAdapter oda_4 = new SqlDataAdapter(oCmd_4);
                        DataSet ds_4 = new DataSet();
                        oda_4.Fill(ds_4, "myTable");
                        DataView dv_px = ds_4.Tables[0].DefaultView;
                        if (dv_px.Count >= 1)
                        {
                            txt_px_name.Text = Server.HtmlEncode(dv_px[0]["com_cname"].ToString().Trim());
                        }
                        ds_4.Dispose();
                        oCmd_4.Dispose();
                        oda_4.Dispose();
                    }
                    //SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT top 1 adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = '"+ViewState["com_orgcd"].ToString()+"' )";
                    //SDS_emp.DataBind();
                    //System.Data.DataView dv_oA = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
                    SqlCommand oCmd_5 = new SqlCommand();
                    oCmd_5.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);

                    oCmd_5.CommandText = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT top 1 adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = @adm_org )";
                    oCmd_5.Parameters.AddWithValue("adm_org", oRCM.SQLInjectionReplaceAll(ViewState["com_orgcd"].ToString()));
                    oCmd_5.CommandType = CommandType.Text;
                    SqlDataAdapter oda_5 = new SqlDataAdapter(oCmd_5);
                    DataSet ds_5 = new DataSet();
                    oda_5.Fill(ds_5, "myTable");
                    DataView dv_oA = ds_5.Tables[0].DefaultView;
                    if (dv_oA.Count >= 1)
                    {
                        if (dv[0]["tr_org_adm"].ToString().Trim() != "")
                        {
                            PH_rb_adm.Visible = true;
                            LB_adm_text.Text = Server.HtmlEncode(dv_oA[0]["cname"].ToString().Trim());
                        }
                    }
                    ds_5.Dispose();
                    oCmd_5.Dispose();
                    oda_5.Dispose();
                    #endregion
                    break;

                case "F":
                    #region A
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_ui.Enabled = true;
                    rb_conttype_ui.Checked = true;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    #endregion
                    break;
            }
            BindContType(dv[0]["tr_conttype"].ToString().Trim());
            #endregion
            #region 契約預估金額
            BindContMoneyType();
            ddlContMoneyType.SelectedValue = IIf(dv[0]["tr_money_type"].ToString().Trim() == "", "TWD", dv[0]["tr_money_type"].ToString().Trim());
            LB_ContMoneyType.Text = Server.HtmlEncode(ddlContMoneyType.SelectedItem.Text);
            txtContMoney.Text = Server.HtmlEncode(dv[0]["tr_money"].ToString().Trim());
            TB_money_rate.Text = Server.HtmlEncode(dv[0]["tr_money_rate"].ToString().Trim());

            #endregion
            #region 契約期間
            txt_contsdate.Text = dv[0]["tr_contsdate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tr_contsdate"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            txt_contedate.Text = dv[0]["tr_contedate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tr_contedate"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            txt_confirm_date.Text = dv[0]["tr_confirm_date"].ToString().Trim().Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tr_confirm_date"].ToString()).Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            #endregion

            ViewState["ver"] = dv[0]["tr_sRC_ver"].ToString().Trim();
            if (dv[0]["tr_otherrequire_ver"].ToString().Trim() == "")
            {
                PL_olderVer.Visible = true;
                #region 智權歸屬
                switch (dv[0]["tr_ipb"].ToString())
                {
                    case "0":
                        rb_ipb_itri.Checked = true;
                        break;
                    case "1":
                        rb_ipb_coparcenary.Checked = true;
                        txt_ipbi_percent.Text = Server.HtmlEncode(dv[0]["tr_ipbi_percent"].ToString().Trim());
                        txt_ipbc_percent.Text = Server.HtmlEncode(dv[0]["tr_ipbc_percent"].ToString().Trim());
                        break;
                    case "2":
                        rb_ipb_customer.Checked = true;
                        break;
                    case "3":
                        rb_ipb_other.Checked = true;
                        txt_ipb_other_desc.Text = Server.HtmlEncode(dv[0]["tr_ipb_other_desc"].ToString().Trim());
                        break;
                }
                #endregion
                txtSignReason.Text = Server.HtmlEncode(dv[0]["tr_sign_reason"].ToString().Trim());
                txt_ip_apply.Text = Server.HtmlEncode(dv[0]["tr_ip_apply"].ToString().Trim());
                txt_income_divvy.Text = Server.HtmlEncode(dv[0]["tr_income_divvy"].ToString().Trim());
                #region 責任範圍
                switch (dv[0]["tr_duty"].ToString())
                {
                    case "0":
                        rb_duty_plain.Checked = true;
                        txt_duty_plain_budget.Text = Server.HtmlEncode(dv[0]["tr_duty_plain_budget"].ToString().Trim());
                        break;
                    case "1":
                        rb_duty_capital.Checked = true;
                        txt_duty_capitalsum.Text = Server.HtmlEncode(dv[0]["tr_duty_capitalsum"].ToString().Trim());
                        break;
                    case "2":
                        rb_duty_assumpsit.Checked = true;
                        break;
                    case "3":
                        rb_duty_other.Checked = true;
                        txt_duty_other_desc.Text = Server.HtmlEncode(dv[0]["tr_duty_other_desc"].ToString().Trim());
                        break;
                }
                #endregion
                Bind_sRC(str_tr_class, Server.HtmlEncode(dv[0]["tr_sRC_ver"].ToString().Trim()));
            }
            else
            {
                PL_olderVer.Visible = false;
            }
            #region 其他需求
            Bind_oRC(ViewState["seno"].ToString(), dv[0]["tr_otherrequire_ver"].ToString().Trim());
            //switch (dv[0]["tr_otherrequire_type"].ToString() )
            //{
            //    case "1":
            //        rb_other_1.Checked = true;
            //        txt_otherrequire_contno.Text=dv[0]["tr_otherrequire_contno"].ToString().Trim();
            //        TB_otherrequire_handle_name.Text = dv[0]["tr_otherrequire_handle_name"].ToString().Trim();
            //        break;
            //    case "2":
            //        rb_other_2.Checked = true;
            //        txt_otherrequire_asked_name.Text=dv[0]["tr_otherrequire_asked_name"].ToString().Trim();
            //        break;
            //    case "3":
            //        rb_other_3.Checked = true; 
            //        break;
            //        dv[0]["tr_otherrequire_reason"].ToString().Trim();
            //    case "4": 
            //        rb_other_4.Checked = true;
            //        break;
            //    case "5":
            //        rb_other_5.Checked = true;
            //        break;
            //    default:
            //        rb_other_6.Checked = true;
            //        dv[0]["tr_otherrequire_desc"].ToString().Trim();
            //        break;
            //}
            #endregion
            lb_keyin_emp_no.Text = Server.HtmlEncode(dv[0]["tr_keyin_emp_no"].ToString().Trim());
            lb_keyin_emp_name.Text = Server.HtmlEncode(dv[0]["tr_keyin_emp_name"].ToString().Trim());
            lb_keyin_tel.Text = Server.HtmlEncode(GetEmptel(dv[0]["tr_keyin_emp_no"].ToString().Trim()));
            lb_keyin_date.Text = Server.HtmlEncode(dv[0]["tr_keyin_date"].ToString().Trim());
            lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tr_modify_emp_no"].ToString().Trim());
            lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tr_modify_emp_name"].ToString().Trim());
            lb_modify_tel.Text = Server.HtmlEncode(GetEmptel(dv[0]["tr_modify_emp_no"].ToString().Trim()));
            lb_send_date.Text = Server.HtmlEncode(dv[0]["tr_modify_date"].ToString().Trim());
            lb_send_date.Text = Server.HtmlEncode(dv[0]["tr_modify_date"].ToString().Trim());
            if (dv[0]["tr_急件"].ToString().Trim() == "1")
            {
                CB_急件.Checked = true;
                TB_急件原因.Text = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tr_急件原因"].ToString()));
            }
        }

        ds_1.Dispose();
        oCmd_1.Dispose();
        oda_1.Dispose();
    }
    private void BindData_file()
    {

        //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyApply_files";
        //SDS_gv_file.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_traetyApply_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
            sqlCmd.Parameters.AddWithValue("mode", "Apply_files");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        if (cb_conttype_b0.Checked)
            strCondition += "B0,";

        if (cb_conttype_b1.Checked)
            strCondition += "B1,";

        if (cb_conttype_d4.Checked)
            strCondition += "D4,";

        if (cb_conttype_d5.Checked)
            strCondition += "D5,";

        if (cb_conttype_d7.Checked)
            strCondition += "D7,";
        if (cb_conttype_ns.Checked)
            strCondition += "NS,";
        if (cb_conttype_rb.Checked)
            strCondition += "RB,";
        if (cb_conttype_m.Checked)
            strCondition += "ND,";
        if (rb_conttype_uo.Checked || rb_conttype_ui.Checked)
            strCondition += "UN,";
        if (rb_conttype_bd.Checked)
            strCondition += "BD,";
        if (rb_conttype_other.Checked)
            strCondition += "OT,";

        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);

        #endregion

        //SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  
        //SDS_ContType.SelectCommand = "exec esp_treaty_codetable_query_by_group  '" + SQLInjectionReplaceAll(strCondition )+ "' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();


        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "10");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
            LB_ContType.Text = Server.HtmlEncode(ddlContType.SelectedItem.Text);
        }
        #endregion
    }
    private void Bind_sRC_init(string str_class)
    {

        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_traetyApplyCase_sRC";
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(str_class));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(ViewState["ver"].ToString()));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());

        SqlCommand oCmd_1 = new SqlCommand();
        oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd_1.CommandType = CommandType.StoredProcedure;
        oCmd_1.CommandText = "esp_traetyApply_modify";
        oCmd_1.Parameters.AddWithValue("class", oRCM.SQLInjectionReplaceAll(str_class));
        oCmd_1.Parameters.AddWithValue("ver", oRCM.SQLInjectionReplaceAll(ViewState["ver"].ToString()));
        oCmd_1.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
        oCmd_1.Parameters.AddWithValue("mode", "sRC");
        SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        oda_1.Fill(ds_1, "myTable");
        DataView dv = ds_1.Tables[0].DefaultView;
        if (dv.Count >= 1)
        {
            ArrayList my報院條件 = new ArrayList();
            ArrayList my報院條件說明 = new ArrayList();
            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif'  class='itemhint'></td><td colspan='3' class='lineheight03'>";
            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = "CBL_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString()) + "_" + Server.HtmlEncode(dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_x.Attributes["value"] = Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        CBL_x.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        my報院條件.Add(CBL_x.ID);
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = "CBL_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString()) + "_" + Server.HtmlEncode(dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_y.Attributes["value"] = Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        CBL_y.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        my報院條件.Add(CBL_y.ID);
                        TextBox tb = new TextBox();
                        tb.ID = "TB_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 20;
                        tb.Width = 555;
                        tb.Enabled = true;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        my報院條件說明.Add(tb.ID);
                        break;
                }
                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }

            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
        ds_1.Dispose();
        oCmd_1.Dispose();
        oda_1.Dispose();
    }
    private void Bind_sRC(string str_class, string str_ver)
    {
        ArrayList my報院條件 = new ArrayList();
        ArrayList my報院條件說明 = new ArrayList();

        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString; 
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_TreatyApply_sRc_modify";
        //SDS_sRC.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("svalue", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("sdoc", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("stype", SQLInjectionReplaceAll("List"));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());


        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_traetyApply_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
            sqlCmd.Parameters.AddWithValue("@mode", "sRC");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                my報院條件.Add(dvR[i]["trsRC_val"].ToString());
                if (dvR[i]["trsRC_val"].ToString().IndexOf("T") != -1)
                    my報院條件說明.Add(dvR[i]["trsRC_val"].ToString() + "©" + dvR[i]["trsRC_desc"].ToString());
            }
        }
        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = "esp_traetyApplyCase_sRC";
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(str_class));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());

        #region --- query ---       
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_traetyApply_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
            sqlCmd.Parameters.AddWithValue("@mode", "sRC");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {

            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif'  class='itemhint'></td><td colspan='3' class='lineheight03'>";
            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + Server.HtmlEncode(oRCM.RemoveXss(dv[sRC_count]["tcs_codeName"].ToString())) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = "CBL_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString()) + "_" + Server.HtmlEncode(dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(oRCM.RemoveXss(dv[sRC_count]["tcs_codeName"].ToString()));
                        CBL_x.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        CBL_x.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        foreach (string obj in my報院條件)
                        {
                            if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                                CBL_x.Checked = true;
                        }
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = "CBL_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString()) + "_" + Server.HtmlEncode(dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(oRCM.RemoveXss(dv[sRC_count]["tcs_codeName"].ToString()));
                        CBL_y.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        CBL_y.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        foreach (string obj in my報院條件)
                        {
                            if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                                CBL_y.Checked = true;
                        }
                        TextBox tb = new TextBox();
                        tb.ID = "TB_" + Server.HtmlEncode(dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 20;
                        tb.Width = 555;
                        tb.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        foreach (string str_obj in my報院條件說明)
                        {
                            if (str_obj.Split('©')[0].ToString() == dv[sRC_count]["tcs_code"].ToString())
                                tb.Text = Server.HtmlEncode(oRCM.RemoveXss(str_obj)).Split('©')[1].ToString();
                        }
                        break;
                }

                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }

            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
    }
    private void Bind_oRC(string str_seno, string str_ver)
    {
        if (str_ver == "2")
            PH_oRC_new.Visible = true;
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.SelectParameters.Clear();
        //SDS_oRC.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.SelectCommand = " select * from treaty_requisition_oRC where tr_seno=@seno and troRC_ver=@ver";
        //SDS_oRC.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_oRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_oRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_oRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_oRC.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_traetyApply_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(oRCM.GetClientIP(Request)));
            sqlCmd.Parameters.AddWithValue("@mode", "oRC");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                switch (dvR[i]["troRC_val"].ToString())
                {
                    case "1":
                        rb_other_1.Checked = true;
                        txt_otherrequire_contno.Text = Server.HtmlEncode(oRCM.RemoveXss(dvR[i]["troRC_desc1"].ToString()));
                        TB_otherrequire_handle_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dvR[i]["troRC_desc2"].ToString()));
                        break;
                    case "2":
                        rb_other_2.Checked = true;
                        txt_otherrequire_asked_name.Text = Server.HtmlEncode(oRCM.RemoveXss(dvR[i]["troRC_desc1"].ToString()));
                        break;
                    case "3":
                        rb_other_3.Checked = true;
                        break;
                    case "4":
                        rb_other_4.Checked = true;
                        break;
                    case "T":
                        rb_other_T.Checked = true;
                        txt_otherrequire_desc.Text = Server.HtmlEncode(oRCM.RemoveXss(dvR[i]["troRC_desc1"].ToString()));
                        break;
                }
            }
        }
    }

    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {

    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {

        if (e.CommandName == "xDownload")
        {

            if (ViewState["isPC"].ToString() == "false")
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";

            //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyApply_file_modify";
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tcdf_url"].ToString().Trim()));
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyApply_view.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                Response.Flush();
                Response.End();
            }
        }
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //ImageButton ib = (ImageButton)e.Row.FindControl("btnDelete");
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");

                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
            }
        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();

        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString; 
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplace(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplace(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplace(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplace(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplace(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplace(xIP));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplace(xApp));
        //SDS_log.Insert();

        SqlConnection oConn = new SqlConnection();
        oConn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        oConn.Open();
        SqlCommand oCmd = oConn.CreateCommand();
        oCmd.CommandText = "esp_treaty_log";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("seno", oRCM.SQLInjectionReplaceAll(xID));
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
        oCmd.Parameters.AddWithValue("empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        oCmd.Parameters.AddWithValue("txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
        oCmd.Parameters.AddWithValue("txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
        oCmd.Parameters.AddWithValue("xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
        oCmd.Parameters.AddWithValue("xApp", oRCM.SQLInjectionReplaceAll(xApp));
        oCmd.ExecuteNonQuery();
        oCmd.Dispose();
        oConn.Close();

    }
    protected void btEdit_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyApply_modify.aspx?seno=" + ViewState["seno"].ToString());
    }
    protected void btnDelete_Click(object sender, EventArgs e)
    {

    }

    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }


}