﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_view.aspx.cs" Inherits="TechCase_view" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>
<%@ Register Src="../userControl/Header.ascx" TagName="Header" TagPrefix="uc1" %>
<%@ Register Src="../userControl/Foot_tech.ascx" TagName="Foot" TagPrefix="uc2" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />

    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/jquery-migrate-1.2.1.js"></script>
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />

    <script type="text/javascript" src="../Scripts/autosize.min.js"></script>
    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script src="../Scripts/read-more.min.js"></script>
    <!-- readmore -->
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        var p = navigator.platform;

        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "90%", height: "80%", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }

        function tech_Inspect(seno, tt_no) {
            $(".ajax_mesg_inspect").colorbox({
                href: "./TechCase_Inspect.aspx?tt_seno=" + seno + "&tti_no=" + tt_no
                , iframe: true, width: "700px", height: "520px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }

        function tech_Inspect_his(seno) {
            $(".ajax_mesg_inspect_his").colorbox({
                href: "./TechCase_Inspect_his.aspx?tt_seno=" + seno
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }

        function tech_Cancel(seno) {
            if (confirm('確定要取消需求?\t <<取消前請知會法務人員>> ')) {
                $(".ajax_mesg").colorbox({
                    href: "./TechCase_Cancel.aspx?tt_seno=" + seno
                    , title: '議約需求取消'
                    , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                        reflash_topic("case_renew", 0);
                    }
                });
            }
        }

        function EndCase(seno) {
            alert('案件已發結案通知!\n');
            location.replace('./TechCase_view.aspx?tt_seno=' + seno);
        }

        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assignInspect.aspx?seno=" + seno
                , title: '新增審查人'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }

        function showCompInfoDialog(Compno) {
            var newopen = window.open('https://cust.itri.org.tw/mgrcust_custdata.aspx?comp_idno=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }

        function Dispatch_Case(tt_seno, Role2) {
            $(".ajax_mesg_dispatch").colorbox({
                href: "./TechCase_dispatch.aspx?tt_seno=" + tt_seno + "&Role2=" + Role2
                , title: Role2 == "X" ? "技轉分案" : "技轉分案(C組)"
                , iframe: true, width: "300px", height: "150px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Dispatch_Case", 0);
                }
            });
        }

        function OpenOutside(tt_seno) {
            $(".ajax_outside").colorbox({
                href: "./TechCase_ECP_outside.aspx?tt_seno=" + tt_seno
                , title: "境外實施審查表簽核"
                , iframe: true, width: "1000px", height: "950px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Outside", 0);
                }
            });
        }

        $(document).ready(function () {
            $(".giveboxH").each(function () {
                var $giveboxH = $(this);
                var gheight = $giveboxH.height();

                if (gheight < 100) {
                    $giveboxH.removeClass("givebox");
                }
            });
            //https://github.com/asirokas/jReadMore
            $(".givebox").readMore({
                readMoreHeight: 95,
                readMoreLinkClass: 'giveboxbtn',
                readMoreText: "閱讀更多",
                readLessText: "收合內容",
            })
        });
    </script>
    <style type="text/css">
        .cluetip-inner {
            width: 98%
        }

        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        .empty {
            color: #aaa;
        }

        .ui-cluetip-header, .ui-cluetip-content {
            overflow: auto;
            max-heisht: 4em;
        }

        .mce-content-body body {
            font-size: 16px;
        }

        .auto-style1 {
            width: 180px;
        }

        .auto-style2 {
            width: 11%;
        }

        .givebox {
            word-break: break-all;
        }


        .giveboxbtn:hover {
            background: #8f8f8f;
            color: #fff;
        }

        .giveboxbtn {
            border: 1px solid #8f8f8f;
            color: #5e5e5e;
            border-radius: 5px;
            padding: 1px 10px;
            background: transparent;
            transition: all ease-in-out 0.2s;
            margin-top: 10px;
            display: inline-block;
        }

        .timelinebox {
            border-radius: 10px;
            height: auto;
            overflow: auto;
            text-align: left;
            background-color: rgba(59, 59, 59, 0.1);
            padding: 1em;
            font-size: 12pt;
            width: 95%
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">
        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left">
                                <%-- <%= ViewState["Role"].ToString() %>
                                <%= ViewState["RW"].ToString() %>
                                <%= ViewState["Role2"].ToString() %>
                                <%= ViewState["empno"].ToString() %>--%>
                                <span class="font-normal font-size3 font-bold"></span>
                            </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="lnkbtn_Engage" runat="server" OnClick="lnkbtn_Engage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊</asp:LinkButton>&nbsp;&nbsp;
                                    <asp:Button ID="lnkbtn_Dispatch" runat="server" Visible="false" class="genbtnS ajax_mesg_dispatch" Text="技轉分案"></asp:Button>
                                    <asp:Button ID="lnkbtn_DispatchC" runat="server" Visible="false" class="genbtnS ajax_mesg_dispatch" Text="技轉分案(C組)"></asp:Button>
                                    <asp:Button class="ajax_mesg_inspect genbtnS" ID="btn_Inspect2" runat="server" Text="案件審查" Visible="False" />
                                    <asp:Button ID="btn_Edit" runat="server" class="ajax_mesg genbtnS" Text="編輯" OnClick="btn_Edit_Click" Visible="False" />
                                    <asp:Button ID="btn_End" runat="server" class="genbtnS" Text="結案通知" OnClick="btn_End_Click" Visible="false"></asp:Button>
                                    <%--<asp:LinkButton ID="lnkbtn_Register" runat="server"><img src="../images/icon-1301.gif" />立案設定</asp:LinkButton>--%>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="font-size: 14pt;">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td>
                                            <table border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td>
                                                        <asp:DropDownList ID="ddl_SeqSn" runat="server" Width="160px" DataTextField="contno" DataValueField="seno" AutoPostBack="True" OnSelectedIndexChanged="ddl_SeqSn_SelectedIndexChanged"></asp:DropDownList></td>
                                                    <td>
                                                        <asp:Literal ID="LT_擬約幫手" runat="server" Text=''></asp:Literal>
                                                        <asp:Label ID="txt_ComplexNo" runat="server" Text="" Visible="false"></asp:Label>／ </td>
                                                    <td>
                                                        <div class="font-title titlebackicon">洽案簽辦人</div>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="洽案簽辦人" runat="server"></asp:Label></td>
                                                    <td>，議約狀態:<asp:Label ID="LB_議約狀態" runat="server" Width="95px"></asp:Label></td>
                                                </tr>
                                            </table>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <span class="font-red">*</span>需求單位<br>
                                                及部門
                                            </div>
                                        </td>
                                        <td>
                                            <table border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td>
                                                        <asp:Label ID="txt_OrgAbbrName" runat="server"></asp:Label>&nbsp;
                                                         <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                                        ／
                                                    </td>
                                                    <td>
                                                        <div class="font-title titlebackicon">單位承辦人</div>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="txt_promoter_name" runat="server"></asp:Label>，分機:<asp:Label ID="txt_Tel" runat="server"></asp:Label>
                                                        <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                <span class="font-red">*</span>契約名稱
                                            </div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_name" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>簽約對象</div>
                                        </td>
                                        <td>
                                            <span class="stripeMe">
                                                <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="97%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound" htmlencode="false">
                                                    <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                    <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                    <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                    <AlternatingRowStyle CssClass="TRowEven" />
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="跳票">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_退換票" runat="server" Text='<%#  Eval("tmp_退換票").ToString() %>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="資本額逾½">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_資本額" runat="server" Text='<%#  Eval("tmp_資本額").ToString() %>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="資產遭查封">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_資產遭查封" runat="server" Text='<%#  Eval("tmp_資產遭查封").ToString()%>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="抽換票">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_抽換票" runat="server" Text='<%#  Eval("tmp_抽換票").ToString() %>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="其他風險">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_其他風險" runat="server" Text='<%#  Eval("tmp_其他風險").ToString()%>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="含陸資">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_含陸資" runat="server" Text='<%# Eval("陸資").ToString() %>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="廠商編號">
                                                            <ItemTemplate>
                                                                <asp:Literal ID="lbl_company" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString()) %>'></asp:Literal>
                                                            </ItemTemplate>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                            <HeaderStyle Width="65px" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="廠商中文名稱<hr>廠商英文名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="lnkbtn_廠商中文名稱" runat="server" Compno='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString())%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="lnkbtn_廠商中文名稱_Click"></asp:LinkButton><hr>
                                                                <asp:Label ID="lbl_lbl_廠商英文名稱" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label><hr>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="450px" />
                                                        </asp:TemplateField>
                                                        <asp:BoundField DataField="comp_country_name" HeaderText="廠商國別">
                                                            <HeaderStyle Width="60px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:BoundField>
                                                        <asp:BoundField DataField="利益迴避" HeaderText="利益迴避">
                                                            <HeaderStyle Width="10px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" ForeColor="Red" />
                                                        </asp:BoundField>
                                                    </Columns>
                                                    <EmptyDataTemplate>
                                                        <!--當找不到資料時則顯示「無資料」-->
                                                        <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                    </EmptyDataTemplate>
                                                    <FooterStyle BackColor="White" />
                                                    <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                </cc1:SmartGridView>
                                                <asp:HiddenField ID="h_compno" runat="server" />
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">
                                                案件性質
                                            </div>
                                        </td>
                                        <td class="lineheight03">
                                            <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" Enabled="false" />
                                            <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" Enabled="false" />
                                            <b>
                                                <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" Enabled="false" Font-Bold="True" ForeColor="#0060A4" />
                                                <asp:CheckBox ID="chk_技術讓與" runat="server" Text="技術讓與" class="font-title" Enabled="false" Font-Bold="True" ForeColor="#0060A4" />
                                            </b>
                                            <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽)" Enabled="false" /><br />
                                            <asp:PlaceHolder ID="PH_法務內部資訊" runat="server">
                                                <div class="font-title">
                                                    <b>
                                                        <asp:CheckBox ID="chk_技術授權" runat="server" Text="專屬技術授權" Enabled="false" />
                                                        <asp:CheckBox ID="chk_專利授權" runat="server" Text="專屬專利授權" Enabled="false" />
                                                        <asp:CheckBox ID="chk_技術與專利授權" runat="server" Text="專屬技術與專利授權" Enabled="false" />
                                                        <asp:CheckBox ID="chk_特定區域" runat="server" Text="境外實施" Font-Bold="True" ForeColor="#0060A4" Enabled="false" />
                                                        <asp:CheckBox ID="chk_全球" runat="server" Text="全球" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                        <asp:CheckBox ID="chk_陸港澳" runat="server" Text="陸港澳" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                        <asp:CheckBox ID="chk_韓國" runat="server" Text="韓國" Font-Bold="True" ForeColor="#0060A4" Enabled="false" Visible="false" />
                                                        <asp:CheckBox ID="chk_修約" runat="server" Text="修約" Font-Bold="True" ForeColor="Red" Enabled="false" />
                                                        <span style="font-size: 10pt; color: red">註：案件性質亦須勾選</span>
                                                    </b>
                                                </div>
                                            </asp:PlaceHolder>

                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="PL_relation" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    案件合理性評估
                                                </div>
                                            </td>
                                            <td class="lineheight03">
                                                <asp:RadioButtonList ID="rbl_relation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
                                                    <asp:ListItem Value="A">讓與、專屬授權 及其他授權</asp:ListItem>
                                                    <asp:ListItem Value="B">修約</asp:ListItem>
                                                    <asp:ListItem Value="" style="display: none">舊案</asp:ListItem>
                                                </asp:RadioButtonList>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_Light" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">案件燈號</div>
                                            </td>
                                            <td class="lineheight03">

                                                <asp:RadioButtonList ID="rbl_Light" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow" Enabled="false"></asp:RadioButtonList>
                                                <asp:TextBox ID="txt_燈號_說明" runat="server" Enabled="false"></asp:TextBox>

                                                <span style="float: right; margin-right: 2em;">
                                                    <span class="font-title titlebackicon">第三方鑑價報告
                                                    </span>
                                                    <asp:CheckBox ID="chk_第三方鑑價報告" runat="server" Enabled="false" />
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="4">
                                                <span class="font-title titlebackicon">
                                                    <asp:CheckBox ID="chk_成果歸屬運用辦法" runat="server" Enabled="false" />經濟部成果歸屬運用辦法第18條</span>

                                                <asp:CheckBox ID="chk_公益目的" runat="server" Text="公益目的" Enabled="false" />
                                                <asp:CheckBox ID="chk_促進整體產業發展" runat="server" Text="促進整體產業發展" Enabled="false" />
                                                <asp:CheckBox ID="chk_提升研發成果運用效益" runat="server" Text="提升研發成果運用效益" Enabled="false" />
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC" runat="server"></asp:PlaceHolder>
                                    <tr>
                                        <td><%----%></td>
                                        <td style="padding: 0px;">
                                            <div style="background-color: #666; border-radius: 10px; font-size: 5pt; margin-right: 5px;">
                                                &emsp;
                                            </div>
                                        </td>
                                    </tr>
                                    <asp:PlaceHolder ID="Plh_Dynax_sRC_x" runat="server"></asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PH_degree_Z" runat="server">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    技轉承辦人<br />
                                                    意見彙整
                                                </div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <div class='timelinebox'>
                                                    <div class='giveboxH givebox lh-lg'>
                                                        <asp:Literal ID="txt_betsum" runat="server"></asp:Literal>
                                                    </div>
                                                </div>

                                                <%--<asp:TextBox ID="txt_betsum" runat="server" Width="800px" TextMode="MultiLine" Height="60px" Font-Size="Medium" Enabled="false" ></asp:TextBox>--%>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送審資料</div>
                                        </td>
                                        <td style="font-size: 12pt">
                                            <asp:Button ID="btn_FileUp" runat="server" Text="檔案上傳" class="ajax_mesg genbtnS" Visible="false"></asp:Button>
                                            <span class="stripeMe">
                                                <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" Width="98%">
                                                    <Columns>
                                                        <asp:TemplateField HeaderText="功能">
                                                            <ItemTemplate>

                                                                <asp:LinkButton ID="lnkbtn_Del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tcdf_no") %>' ForeColor="Red">刪除</asp:LinkButton>

                                                                <div style="display: none">
                                                                    <asp:Label ID="lbl_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tcdf_no").ToString()) %>'></asp:Label>
                                                                </div>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="附件名稱">
                                                            <ItemTemplate>
                                                                <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_no").ToString() )  %>'> </asp:LinkButton>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="500px"></HeaderStyle>
                                                        </asp:TemplateField>
                                                        <asp:BoundField DataField="tcdf_filetype" HeaderText="文件類型">
                                                            <HeaderStyle Width="140px" />
                                                        </asp:BoundField>
                                                        <asp:TemplateField HeaderText="上傳者">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_4" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="60px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="上傳日期">
                                                            <ItemTemplate>
                                                                <asp:Label ID="lbl_1" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="100px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="審查">
                                                            <ItemTemplate>
                                                                <asp:Label ID="Label1" runat="server" Visible='<%#System.Web.HttpUtility.HtmlEncode(Eval("tcdf_inspect").ToString())=="1" %>'>V</asp:Label>
                                                            </ItemTemplate>
                                                            <HeaderStyle Width="10px"></HeaderStyle>
                                                            <ItemStyle HorizontalAlign="Center" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                    <PagerSettings Position="Bottom" />
                                                    <PagerStyle HorizontalAlign="Left" />
                                                </asp:GridView>
                                            </span>
                                        </td>
                                    </tr>

                                    <asp:PlaceHolder ID="PH_degree_C" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">需求取消原因</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txtRequestCancel" runat="server" Width="608px" TextMode="MultiLine" Height="60px" ReadOnly="true"></asp:TextBox>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_Inspect" runat="server">
                                        <tr valign="top">
                                            <td align="right">
                                                <div class="font-title titlebackicon">審查資訊</div>
                                            </td>
                                            <td>
                                                <div style="width: 98%;">
                                                    <asp:Button class="ajax_mesg_inspect genbtnS" ID="btn_Inspect" runat="server" Text="案件審查" Visible="False" />
                                                    <asp:Button class="ajax_mesg_inspect_his genbtnS" ID="btn_Inspect_His" runat="server" Text="審查歷史" Style="float: right" />
                                                </div>
                                                <br />
                                                <span class="stripeMe">
                                                    <asp:GridView ID="gv_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="gv_Inspect_RowDataBound" OnRowCommand="gv_Inspect_RowCommand" Width="98%">
                                                        <Columns>
                                                            <asp:BoundField DataField="tt_order" HeaderText="順序">
                                                                <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tt_empname" HeaderText="審查人">
                                                                <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tt_inspect_desc" HeaderText="簽核意見">
                                                                <ItemStyle Width="350px" />
                                                            </asp:BoundField>
                                                            <asp:TemplateField HeaderText="簽核狀態">
                                                                <ItemTemplate>
                                                                    <asp:Literal ID="lbl_Istatus" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tt_flag").ToString()) %>'></asp:Literal>
                                                                </ItemTemplate>
                                                                <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                            </asp:TemplateField>
                                                            <asp:BoundField DataField="short_inspect_time" HeaderText="簽核日期">
                                                                <ItemStyle HorizontalAlign="center" Width="100px" />
                                                            </asp:BoundField>

                                                        </Columns>
                                                        <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                </span>
                                            </td>
                                        </tr>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="PL_tt_manage_note" runat="server" Visible="false">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">備註</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txt_ManageNote" runat="server" Width="800px" TextMode="MultiLine" Height="60px" onchange="updateTitle(this)" ReadOnly="true" Font-Size="14pt"></asp:TextBox>

                                            </td>
                                        </tr>

                                    </asp:PlaceHolder>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td class="td_right" colspan="5">
                                        <div style="float: left">
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="bt_cancle" Text="需求取消" OnClick="bt_cancle_Click" Visible="false" />

                                        </div>
                                        <span class="font-normal font-size3 font-bold">
                                            <asp:LinkButton ID="lbtn_outside" runat="server" class="ajax_outside"> <img src="../../images/icon-1301.gif" style="border:none;" />境外實施自主管理表簽核</asp:LinkButton>                                         
                                        </span>
                                        <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btn_Edit2" Text="編輯" OnClick="btn_Edit_Click" Visible="False" />
                                        <asp:Button runat="server" class="genbtnS" ID="btn_End2" Text="結案通知" OnClick="btn_End_Click" Visible="false"></asp:Button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">分案主管</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">送件/分案日期</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>&nbsp;&nbsp;/&nbsp;&nbsp;<asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">技轉承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">進度</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal>
                                            <asp:DropDownList ID="DDL_Degree" runat="server" DataTextField="text" DataValueField="value" Visible="false"></asp:DropDownList>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">技轉承辦人_C</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_handle_c_name" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_c_empno" runat="server"></asp:Literal>|
                                            <asp:Literal ID="lb_handle_c_ext" runat="server"></asp:Literal>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">法務承辦人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="LT_法務承辦人" runat="server"></asp:Literal>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改人</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>
                                            |
                                            <asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                        <td align="right">
                                            <div class="font-title titlebackicon">修改日期</div>
                                        </td>
                                        <td align="left">
                                            <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                    </tr>
                                </table>
                            </span>
                        </div>

                   
                    </div>
                    <br />
                </div>
            </div>
        </div>
           <asp:HiddenField ID="h_ECP_success" runat="server" />
        <uc2:Foot runat="server" ID="Foot" />
        <script type="text/javascript">
            //tinymce.init({
            //    selector: '#txt_betsum',
            //    width: "800",
            //    height: "500",
            //    menubar: false,
            //    statusbar: false,
            //    toolbar: false,
            //    menubar: false,
            //    content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
            //    readonly: 1
            //});

            $(document).ready(function () {

                $(document).bind('cbox_open', function () { $('html').css({ overflow: 'hidden' }); })
                    .bind('cbox_closed', function () { $('html').css({ overflow: 'auto' }); });
                $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_otherrequire_desc").attr("title", $("#txt_otherrequire_desc").val());
                $(".help_otherrequire_desc").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txt_manage_note").val());
                $(".help_manage_note").attr("class", "help_manage_note itemhint");
                $('a.iterm_dymanic').cluetip({
                    width: '800px', cluetipClass: 'jtip',
                    ajaxCache: false,
                    sticky: true,
                    closePosition: 'title',
                    closeText: '<img src="../Scripts/cluetip/images/cross.png" alt="close" />'
                });
                $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false });
                $('a.iterm_dymanic_company').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });
                $('a.iterm_dymanic_Valuation').cluetip({ activation: 'click', local: false, width: '830px', dropShadow: true, sticky: true, closePosition: 'title', arrows: true, ajaxCache: false, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' });

                $(".itemhint").tooltip(
                    {
                        track: true, effect: "slideDown", delay: 250,
                        position: { my: "left+15 center", at: "right center" },
                        //讓tooltips內可以放置HTML CODE
                        content: function () { return $(this).prop('title'); }
                    }
                );
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: {
                        duration: 300
                    },
                    hide: {
                        duration: 300
                    }
                });

            });
            function updateTitle(me) { me.title = me.value; }
            $('#txt_ManageNote').mouseover(function () {
                $(this).attr('title', $(this).val())
            })
            $("input[type='checkbox']").click(
                function () {
                    this.checked = !this.checked;
                }
            );

            $(document).ready(function () {
                jQuery('#Form1').validationEngine({});

            });

            function tech_fileup(contno, seno) {
                $(".ajax_mesg").colorbox({
                    href: "./TechCase_FileUp.aspx?tt_seno=" + seno
                    , title: '檔案上傳'
                    , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                    , onClosed: function () {
                        $('html, body').css('overflow', '');
                        reflash_topic("file_renew", 0);
                    }
                });
            }
        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
