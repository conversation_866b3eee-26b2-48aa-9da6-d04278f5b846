{"version": 3, "sources": ["webpack:///./src/ui/node_modules/html2canvas/dist/npm/Color.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Bounds.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Length.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/NodeContainer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Util.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/background.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Path.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Vector.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/listStyle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/TextContainer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Feature.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textDecoration.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/border.js", "webpack:///./src/ui/node_modules/css-line-break/dist/Util.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/ListItem.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/renderer/CanvasRenderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Logger.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/padding.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/overflowWrap.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/position.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textTransform.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Input.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/TextBounds.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/renderer/ForeignObjectRenderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Unicode.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Font.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Proxy.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Window.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/NodeParser.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/StackingContext.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Size.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/BezierCurve.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/borderRadius.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/display.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/float.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/font.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/letterSpacing.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/lineBreak.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/margin.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/overflow.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textShadow.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/transform.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/visibility.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/word-break.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/zIndex.js", "webpack:///./src/ui/node_modules/css-line-break/dist/index.js", "webpack:///./src/ui/node_modules/css-line-break/dist/LineBreak.js", "webpack:///./src/ui/node_modules/css-line-break/dist/Trie.js", "webpack:///./src/ui/node_modules/css-line-break/dist/linebreak-trie.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Circle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Renderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Gradient.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Angle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Clone.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/ResourceLoader.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/PseudoNodeContent.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/index.js"], "names": ["Object", "defineProperty", "exports", "value", "_slicedToArray", "arr", "i", "Array", "isArray", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "push", "length", "err", "sliceIterator", "TypeError", "_createClass", "defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "HEX3", "HEX6", "RGB", "RGBA", "Color", "instance", "_classCallCheck", "this", "array", "_ref", "Math", "min", "match", "parseInt", "hex3", "Number", "rgb", "rgba", "NAMED_COLORS", "toLowerCase", "substring", "hex6", "_ref2", "r", "g", "b", "a", "default", "transparent", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "TRANSPARENT", "parseBoundCurves", "calculatePaddingBoxPath", "calculateBorderBoxPath", "parsePathForBorder", "parseDocumentSize", "calculateContentBox", "calculatePaddingBox", "parseBounds", "Bounds", "_Vector2", "_interopRequireDefault", "_BezierCurve2", "obj", "__esModule", "x", "y", "w", "h", "left", "top", "width", "height", "clientRect", "scrollX", "scrollY", "createPathFromCurves", "node", "fromClientRect", "getBoundingClientRect", "bounds", "borders", "borderWidth", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "document", "body", "documentElement", "Error", "max", "scrollWidth", "offsetWidth", "clientWidth", "scrollHeight", "offsetHeight", "clientHeight", "curves", "borderSide", "topLeftOuter", "topLeftInner", "topRightOuter", "topRightInner", "bottomRightOuter", "bottomRightInner", "bottomLeftOuter", "bottomLeftInner", "outer1", "inner1", "outer2", "inner2", "path", "subdivide", "reverse", "CORNER", "borderRadius", "tlh", "TOP_LEFT", "getAbsoluteValue", "tlv", "trh", "TOP_RIGHT", "trv", "brh", "BOTTOM_RIGHT", "brv", "blh", "BOTTOM_LEFT", "blv", "factors", "maxFactor", "apply", "topWidth", "rightHeight", "bottomWidth", "leftHeight", "getCurvePoints", "r1", "r2", "position", "kappa", "sqrt", "ox", "oy", "xm", "ym", "calculateLengthFromValueWithUnit", "LENGTH_TYPE", "_NodeContainer", "PX", "PERCENTAGE", "Length", "type", "substr", "parsedValue", "parseFloat", "isNaN", "parent<PERSON>ength", "isPercentage", "v", "container", "unit", "style", "font", "fontSize", "getRootFontSize", "parent", "_Color", "_Color2", "_Util", "_background", "_border", "_borderRadius", "_display", "_float", "_font", "_letterSpacing", "_lineBreak", "_listStyle", "_margin", "_overflow", "_overflowWrap", "_padding", "_position", "_textDecoration", "_textShadow", "_textTransform", "_transform", "_visibility", "_wordBreak", "_zIndex", "_Bounds", "_Input", "_ListItem", "INPUT_TAGS", "NodeContainer", "resourceLoader", "index", "_this", "tagName", "childNodes", "listItems", "start", "listStart", "defaultView", "ownerDocument", "pageXOffset", "pageYOffset", "getComputedStyle", "display", "parseDisplay", "IS_INPUT", "parsePosition", "background", "INPUT_BACKGROUND", "parseBackground", "border", "INPUT_BORDERS", "parseBorder", "HTMLInputElement", "getInputBorderRadius", "parseBorderRadius", "color", "INPUT_COLOR", "float", "parseCSSFloat", "parseFont", "letterSpacing", "parseLetterSpacing", "listStyle", "DISPLAY", "LIST_ITEM", "parseListStyle", "lineBreak", "parseLineBreak", "margin", "parse<PERSON><PERSON><PERSON>", "opacity", "overflow", "indexOf", "parseOverflow", "OVERFLOW", "HIDDEN", "overflowWrap", "parseOverflowWrap", "wordWrap", "parsePadding", "textDecoration", "parseTextDecoration", "textShadow", "parseTextShadow", "textTransform", "parseTextTransform", "transform", "parseTransform", "visibility", "parseVisibility", "wordBreak", "parseWordBreak", "zIndex", "parseZIndex", "POSITION", "STATIC", "isTransformed", "listOwner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listIndex", "hasAttribute", "addEventListener", "curvedBounds", "image", "getImage", "reformatInputBounds", "parentClips", "getClipPaths", "VISIBLE", "concat", "isRootElement", "isFloating", "isAbsolutelyPositioned", "contains", "NONE", "VISIBILITY", "RELATIVE", "FLOAT", "isPositioned", "auto", "INLINE", "INLINE_BLOCK", "INLINE_FLEX", "INLINE_GRID", "INLINE_LIST_ITEM", "INLINE_TABLE", "SVGSVGElement", "setAttribute", "s", "XMLSerializer", "loadImage", "encodeURIComponent", "serializeToString", "img", "currentSrc", "src", "canvas", "loadCanvas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getAttribute", "bit", "distance", "copyCSSStyles", "property", "item", "setProperty", "getPropertyValue", "SMALL_IMAGE", "parseBackgroundImage", "calculateBackgroundRepeatPath", "calculateBackgroundPosition", "calculateBackgroungPositioningArea", "calculateBackgroungPaintingArea", "calculateGradientBackgroundSize", "calculateBackgroundSize", "BACKGROUND_ORIGIN", "BACKGROUND_CLIP", "BACKGROUND_SIZE", "BACKGROUND_REPEAT", "_Length2", "_Size2", "REPEAT", "NO_REPEAT", "REPEAT_X", "REPEAT_Y", "AUTO", "CONTAIN", "COVER", "LENGTH", "BORDER_BOX", "PADDING_BOX", "CONTENT_BOX", "BackgroundSize", "size", "AUTO_SIZE", "backgroundImage", "targetRatio", "currentRatio", "parseBackgroundClip", "clip", "<PERSON><PERSON><PERSON><PERSON>", "paddingBox", "PADDING_SIDES", "LEFT", "RIGHT", "TOP", "BOTTOM", "backgroundPositioningArea", "repeat", "round", "backgroundColor", "parseBackgroundImages", "backgroundClip", "parseBackgroundOrigin", "parseBackgroundRepeat", "backgroundRepeat", "trim", "sources", "map", "method", "args", "positions", "backgroundPosition", "split", "repeats", "sizes", "backgroundSize", "source", "parseBackgroundSize", "parseBackgoundPosition", "whitespace", "results", "quote", "definition", "mode", "numParen", "appendResult", "prefix", "prefix_i", "for<PERSON>ach", "c", "test", "PATH", "VECTOR", "BEZIER_CURVE", "CIRCLE", "_Path", "Vector", "parseListStyleType", "LIST_STYLE_TYPE", "LIST_STYLE_POSITION", "INSIDE", "OUTSIDE", "DISC", "SQUARE", "DECIMAL", "CJK_DECIMAL", "DECIMAL_LEADING_ZERO", "LOWER_ROMAN", "UPPER_ROMAN", "LOWER_GREEK", "LOWER_ALPHA", "UPPER_ALPHA", "ARABIC_INDIC", "ARMENIAN", "BENGALI", "CAMBODIAN", "CJK_EARTHLY_BRANCH", "CJK_HEAVENLY_STEM", "CJK_IDEOGRAPHIC", "DEVANAGARI", "ETHIOPIC_NUMERIC", "GEORGIAN", "GUJARATI", "GURMUKHI", "HEBREW", "HIRAGANA", "HIRAGANA_IROHA", "JAPANESE_FORMAL", "JAPANESE_INFORMAL", "KANNADA", "KATAKANA", "KATAKANA_IROHA", "KHMER", "KOREAN_HANGUL_FORMAL", "KOREAN_HANJA_FORMAL", "KOREAN_HANJA_INFORMAL", "LAO", "LOWER_ARMENIAN", "MALAYALAM", "MONGOLIAN", "MYANMAR", "ORIYA", "PERSIAN", "SIMP_CHINESE_FORMAL", "SIMP_CHINESE_INFORMAL", "TAMIL", "TELUGU", "THAI", "TIBETAN", "TRAD_CHINESE_FORMAL", "TRAD_CHINESE_INFORMAL", "UPPER_ARMENIAN", "DISCLOSURE_OPEN", "DISCLOSURE_CLOSED", "parseListStylePosition", "listStyleImage", "listStyleType", "listStylePosition", "_TextBounds", "TextContainer", "text", "data", "parseTextBounds", "CAPITALIZE", "TEXT_TRANSFORM", "LOWERCASE", "replace", "capitalize", "UPPERCASE", "toUpperCase", "m", "p1", "p2", "_ForeignObject<PERSON><PERSON>er", "isGreenPixel", "FEATURES", "createRange", "range", "testElement", "createElement", "TEST_HEIGHT", "append<PERSON><PERSON><PERSON>", "selectNode", "rangeBounds", "rangeHeight", "<PERSON><PERSON><PERSON><PERSON>", "testRangeBounds", "Image", "ctx", "getContext", "drawImage", "toDataURL", "e", "testSVG", "_value", "Promise", "resolve", "onload", "onerror", "complete", "setTimeout", "testBase64", "from", "window", "fetch", "fillStyle", "fillRect", "greenImageSrc", "svg", "createForeignObjectSVG", "loadSerializedSVG", "then", "getImageData", "reject", "catch", "testForeignObject", "crossOrigin", "XMLHttpRequest", "responseType", "TEXT_DECORATION_LINE", "TEXT_DECORATION", "TEXT_DECORATION_STYLE", "SOLID", "DOUBLE", "DOTTED", "DASHED", "WAVY", "UNDERLINE", "OVERLINE", "LINE_THROUGH", "BLINK", "parseLine", "line", "textDecorationLine", "textDecorationColor", "textDecorationStyle", "parseTextDecorationStyle", "BORDER_SIDES", "BORDER_STYLE", "SIDES", "keys", "side", "borderColor", "borderStyle", "parseBorderStyle", "toCodePoints", "str", "codePoints", "charCodeAt", "extra", "fromCodePoint", "String", "arguments", "codeUnits", "result", "codePoint", "fromCharCode", "chars", "lookup", "Uint8Array", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "bytes", "polyUint16Array", "polyUint32Array", "_i2", "createCounterText", "inlineListItemElement", "_NodeContainer2", "_TextContainer2", "_Unicode", "ancestorTypes", "ROMAN_UPPER", "wrapper", "bottom", "right", "innerWidth", "textAlign", "MARGIN_TOP", "styleImage", "createTextNode", "fromTextNode", "integers", "values", "createAdditiveCounter", "symbols", "fallback", "suffix", "reduce", "string", "integer", "createCounterStyleWithSymbolResolver", "codePointRangeLength", "isNumeric", "resolver", "createCounterStyleFromRange", "codePointRangeStart", "codePointRangeEnd", "abs", "floor", "createCounterStyleFromSymbols", "createCJKCounter", "numbers", "multipliers", "negativeSign", "flags", "tmp", "digit", "coefficient", "appendSuffix", "defaultSuffix", "cjkSuffix", "koreanSuffix", "CJK_TEN_COEFFICIENTS", "CJK_ZEROS", "addColorStops", "gradient", "canvasGradient", "maxStop", "colorStops", "colorStop", "stop", "f", "addColorStop", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customCanvas", "options", "scale", "translate", "textBaseline", "logger", "log", "clipPaths", "callback", "save", "restore", "destination", "fill", "_path", "_this2", "beginPath", "point", "moveTo", "lineTo", "bezierCurveTo", "startControl", "endControl", "end", "arc", "radius", "PI", "closePath", "linearGradient", "createLinearGradient", "direction", "x1", "y1", "x0", "y0", "_this3", "center", "radialGradient", "createRadialGradient", "midX", "midY", "invF", "imageSize", "offsetX", "offsetY", "createPattern", "resizeImage", "textBounds", "textShadows", "_this4", "fontStyle", "fontVariant", "fontWeight", "fontFamily", "join", "shadowColor", "shadowOffsetX", "shadowOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "blur", "fillText", "baseline", "fontMetrics", "getMetrics", "rectangle", "middle", "ceil", "globalAlpha", "matrix", "<PERSON><PERSON>", "enabled", "id", "Date", "now", "console", "_len", "_key", "Function", "bind", "call", "error", "_len2", "_key2", "_Length", "OVERFLOW_WRAP", "NORMAL", "BREAK_WORD", "ABSOLUTE", "FIXED", "STICKY", "inlineSelectElement", "inlineTextAreaElement", "inlineInputElement", "_Circle2", "INPUT_BORDER_COLOR", "INPUT_BACKGROUND_COLOR", "INPUT_BORDER", "RADIO_BORDER_RADIUS", "RADIO_BORDER_RADIUS_TUPLE", "INPUT_RADIO_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS_TUPLE", "INPUT_CHECKBOX_BORDER_RADIUS", "inlineFormElement", "checked", "getInputValue", "option", "selectedIndex", "allowLinebreak", "whiteSpace", "placeholder", "TextBounds", "_Feature", "_Feature2", "getWrapperBounds", "textList", "breakWords", "parentNode", "offset", "SUPPORT_RANGE_BOUNDS", "getRangeBounds", "replacementNode", "splitText", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setStart", "setEnd", "ForeignObject<PERSON><PERSON><PERSON>", "element", "windowWidth", "windowHeight", "xmlns", "createElementNS", "foreignObject", "setAttributeNS", "_cssLineBreak", "get", "breaker", "LineBreaker", "words", "bk", "FontMetrics", "_data", "_document", "span", "verticalAlign", "offsetTop", "lineHeight", "_parseMetrics", "Proxy", "proxy", "SUPPORT_CORS_XHR", "SUPPORT_RESPONSE_TYPE", "xhr", "XDomainRequest", "status", "response", "reader", "FileReader", "readAsDataURL", "responseText", "open", "imageTimeout", "timeout", "ontimeout", "send", "renderElement", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Renderer2", "_ForeignObjectRenderer2", "_<PERSON><PERSON>", "_Font", "windowBounds", "documentBackgroundColor", "bodyBackgroundColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foreignObjectRendering", "SUPPORT_FOREIGNOBJECT_DRAWING", "supportForeignObject", "cloner", "DocumentCloner", "inlineFonts", "ready", "render", "cloneWindow", "clonedElement", "stack", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonedDocument", "imageStore", "renderOptions", "all", "remove<PERSON><PERSON><PERSON>", "_StackingContext2", "parseNodeTree", "IGNORED_NODE_NAMES", "nextNode", "childNode", "nextS<PERSON>ling", "Text", "HTMLElement", "nodeName", "isVisible", "SHOULD_TRAVERSE_CHILDREN", "treatAsRealStackingContext", "createsRealStackingContext", "createsStackingContext", "parentStack", "getRealParentStackingContext", "childS<PERSON>ck", "contexts", "children", "_container", "_treatAsRealStackingContext", "_parentStack", "_childStack", "isPositionedWithZIndex", "isBodyWithTransparentRoot", "StackingContext", "getOpacity", "Size", "_Vector", "lerp", "t", "BezierCurve", "firstHalf", "ab", "bc", "cd", "abbc", "bccd", "dest", "_value$split$map", "create", "_value$split$map2", "horizontal", "vertical", "BLOCK", "RUN_IN", "FLOW", "FLOW_ROOT", "TABLE", "FLEX", "GRID", "RUBY", "SUBGRID", "TABLE_ROW_GROUP", "TABLE_HEADER_GROUP", "TABLE_FOOTER_GROUP", "TABLE_ROW", "TABLE_CELL", "TABLE_COLUMN_GROUP", "TABLE_COLUMN", "TABLE_CAPTION", "RUBY_BASE", "RUBY_TEXT", "RUBY_BASE_CONTAINER", "RUBY_TEXT_CONTAINER", "CONTENTS", "setDisplayBit", "parseDisplayValue", "INLINE_START", "INLINE_END", "weight", "parseFontWeight", "LINE_BREAK", "STRICT", "SCROLL", "NUMBER", "currentValue", "<PERSON><PERSON><PERSON><PERSON>", "shadows", "numParens", "appendValue", "appendShadow", "splice", "toFloat", "MATRIX", "parseTransformOrigin", "parseTransformMatrix", "webkitTransform", "mozTransform", "msTransform", "oTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "mozTransformOrigin", "msTransformOrigin", "oTransformOrigin", "origin", "matrix3d", "COLLAPSE", "WORD_BREAK", "BREAK_ALL", "KEEP_ALL", "order", "_LineBreak", "inlineBreakOpportunities", "lineBreakAtIndex", "codePointsToCharacterClasses", "UnicodeTrie", "BREAK_ALLOWED", "BREAK_NOT_ALLOWED", "BREAK_MANDATORY", "classes", "LETTER_NUMBER_MODIFIER", "_Trie", "_linebreakTrie", "_linebreakTrie2", "SP", "BA", "HY", "CL", "CP", "EX", "IN", "NS", "OP", "IS", "NU", "PO", "PR", "SY", "AL", "EB", "EM", "H2", "H3", "ID", "JL", "JV", "JT", "SA", "BK", "CR", "LF", "CM", "NL", "SG", "WJ", "ZW", "GL", "ZWJ", "B2", "BB", "CB", "QU", "AI", "CJ", "HL", "RI", "XX", "createTrieFromBase64", "ALPHABETICS", "HARD_LINE_BREAKS", "SPACE", "PREFIX_POSTFIX", "LINE_BREAKS", "KOREAN_SYLLABLE_BLOCK", "HYPHEN", "types", "indicies", "categories", "classType", "prev", "isAdjacentWithSpaceIgnored", "currentIndex", "classTypes", "current", "n", "_next", "previousNonSpaceClassType", "_lineBreakAtIndex", "forbiddenBreaks", "beforeIndex", "afterIndex", "before", "prevIndex", "_prevIndex", "_type", "count", "cssFormattedClasses", "_codePointsToCharacte", "_codePointsToCharacte2", "_codePointsToCharacte3", "_codePointsToCharacte4", "isLetterNumber", "Break", "output", "_cssFormattedClasses", "_cssFormattedClasses2", "forbiddenBreakpoints", "_codePoints", "required", "arr2", "_toConsumableArray", "_cssFormattedClasses3", "_cssFormattedClasses4", "lastEnd", "nextIndex", "<PERSON><PERSON>", "UTRIE2_INDEX_2_MASK", "UTRIE2_INDEX_2_BLOCK_LENGTH", "UTRIE2_OMITTED_BMP_INDEX_1_LENGTH", "UTRIE2_INDEX_1_OFFSET", "UTRIE2_UTF8_2B_INDEX_2_LENGTH", "UTRIE2_UTF8_2B_INDEX_2_OFFSET", "UTRIE2_INDEX_2_BMP_LENGTH", "UTRIE2_LSCP_INDEX_2_LENGTH", "UTRIE2_DATA_MASK", "UTRIE2_DATA_BLOCK_LENGTH", "UTRIE2_LSCP_INDEX_2_OFFSET", "UTRIE2_SHIFT_1_2", "UTRIE2_INDEX_SHIFT", "UTRIE2_SHIFT_1", "UTRIE2_SHIFT_2", "view32", "Uint32Array", "view16", "Uint16Array", "<PERSON><PERSON><PERSON><PERSON>", "initialValue", "errorValue", "highStart", "highValueIndex", "ix", "module", "Circle", "_Gradient", "_TextContainer", "<PERSON><PERSON><PERSON>", "renderNodeBackgroundAndBorders", "renderNodeContent", "child", "renderTextNode", "drawShape", "_image", "contentBox", "_width", "_height", "paths", "HAS_BACKGROUND", "hasRenderableBorders", "some", "backgroundPaintingArea", "renderBackgroundImage", "renderBorder", "renderBackgroundRepeat", "renderBackgroundGradient", "backgroundImageSize", "_offsetX", "_offsetY", "renderRepeat", "gradientBounds", "parseGradient", "GRADIENT_TYPE", "LINEAR_GRADIENT", "renderLinearGradient", "RADIAL_GRADIENT", "renderRadialGradient", "curvePoints", "_opacity", "setOpacity", "renderStackContent", "_splitStackingContext", "splitStackingContexts", "_splitStackingContext2", "negativeZIndex", "zeroOrAutoZIndexOrTransformedOrOpacity", "positiveZIndex", "nonPositionedFloats", "nonPositionedInlineLevel", "_splitDescendants", "splitDescendants", "_splitDescendants2", "inlineLevel", "nonInlineLevel", "sort", "sortByZIndex", "renderStack", "renderNode", "get<PERSON><PERSON><PERSON>", "isInlineLevel", "transformWebkitRadialGradientArgs", "RadialGrad<PERSON>", "LinearGradient", "RADIAL_GRADIENT_SHAPE", "_<PERSON>le", "SIDE_OR_CORNER", "PERCENTAGE_ANGLES", "ENDS_WITH_LENGTH", "FROM_TO_COLORSTOP", "RADIAL_SHAPE_DEFINITION", "ELLIPSE", "LENGTH_FOR_POSITION", "shape", "parseColorStops", "parseLinearGradient", "transformObsoleteColorStops", "parseRadialGradient", "firstColorStopIndex", "lineLength", "HAS_LENGTH", "lastSpaceIndex", "lastIndexOf", "_color", "_stop", "absoluteValuedColorStops", "previousColorStop", "_stop2", "steps", "stepSize", "hasPrefix", "angle", "parseAngle", "HAS_SIDE_OR_CORNER", "HAS_DIRECTION", "calculateGradientDirection", "parseSide<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsePercentageAngle", "gradientCenter", "gradientRadius", "calculateRadius", "radian", "HALF_WIDTH", "HALF_HEIGHT", "HALF_LINE_LENGTH", "sin", "cos", "parseTopRight", "acos", "_angle$split$map", "_angle$split$map2", "ratio", "atan", "<PERSON><PERSON><PERSON><PERSON>", "closest", "stat", "corner", "d", "optimumDistance", "opti<PERSON><PERSON><PERSON><PERSON>", "Infinity", "extent", "rx", "ry", "_c", "_corner", "idx", "RADIUS", "matchStartPosition", "matchShapeExtent", "matchStartRadius", "matchEndPosition", "matchEndRadius", "matchPosition", "matchRadius", "filter", "ANGLE", "_Proxy", "_ResourceLoader2", "_CanvasRenderer2", "_P<PERSON>udo<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyInline", "renderer", "referenceElement", "scrolledElements", "copyStyles", "inlineImages", "pseudoContentData", "counters", "quote<PERSON><PERSON><PERSON>", "inlineImage", "backgroundImages", "HTMLImageElement", "clone<PERSON><PERSON><PERSON><PERSON>", "styleSheets", "sheet", "href", "res", "createStyleSheetFontsFromText", "getSheetFonts", "fonts", "acc", "formats", "blob", "dataUri", "fontFace", "cssText", "fontCss", "textContent", "HTMLCanvasElement", "HTMLIFrameElement", "tempIframe", "generateIframeKey", "_parseBounds", "cache", "getIframeDocumentElement", "async", "<PERSON><PERSON><PERSON><PERSON>", "logging", "useCORS", "innerHeight", "<PERSON>rame<PERSON><PERSON><PERSON>", "HTMLStyleElement", "cssRules", "css", "rule", "clone", "nodeType", "Node", "TEXT_NODE", "nodeValue", "createElementClone", "styleBefore", "styleAfter", "clonedReferenceElement", "HTMLBodyElement", "createPseudoHideStyles", "parseCounterReset", "contentBefore", "resolvePseudoContent", "ELEMENT_NODE", "ignoreElements", "contentAfter", "popCounters", "inlineAllImages", "inlinePseudoElement", "PSEUDO_BEFORE", "PSEUDO_AFTER", "scrollTop", "scrollLeft", "cloneCanvasContents", "CSSRule", "FONT_FACE_RULE", "format", "baseHref", "doc", "implementation", "createHTMLDocument", "base", "head", "clonedCan<PERSON>", "clonedCtx", "putImageData", "contentItems", "pseudoElt", "content", "anonymousReplacedElement", "PSEUDO_CONTENT_ITEM_TYPE", "IMAGE", "TEXT", "className", "PSEUDO_HIDE_ELEMENT_CLASS_BEFORE", "PSEUDO_HIDE_ELEMENT_CLASS_AFTER", "insertBefore", "createStyles", "styles", "innerHTML", "initNode", "random", "DATA_URI_REGEXP", "contentWindow", "html", "atob", "decodeURIComponent", "createIframeContainer", "cloneIframeContainer", "documentClone", "write", "iframeLoad", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "scrolling", "onreadystatechange", "interval", "setInterval", "readyState", "clearInterval", "serializeDoctype", "scrollTo", "navigator", "userAgent", "onclone", "doctype", "restoreOwnerScroll", "adoptNode", "name", "internalSubset", "publicId", "systemId", "ResourceStore", "Resource<PERSON><PERSON>der", "_window", "<PERSON><PERSON><PERSON><PERSON>", "location", "_index", "hasResourceInCache", "isSVG", "SUPPORT_SVG_DRAWING", "isInlineImage", "isSameOrigin", "addImage", "_loadImage", "SUPPORT_CORS_IMAGES", "xhrImage", "imageLoadHandler", "supportsDataImages", "isInlineBase64Image", "SUPPORT_BASE64_DRAWING", "url", "link", "_link", "protocol", "hostname", "port", "_this5", "images", "resources", "_keys", "_resources", "INLINE_SVG", "INLINE_BASE64", "INLINE_IMG", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TOKEN_TYPE", "STRING", "ATTRIBUTE", "URL", "COUNTER", "COUNTERS", "OPENQUOTE", "CLOSEQUOTE", "counterReset", "counterNames", "counterResets", "lenCounterResets", "_counterResets$i$spli", "_counterResets$i$spli2", "counterName", "counter", "len<PERSON><PERSON><PERSON>s", "pop", "tokens", "counterIncrement", "_counterIncrement$spl", "_counterIncrement$spl2", "incrementValue", "token", "_counter", "formatCounterValue", "_counters", "glue", "getQuote", "isString", "isEscaped", "isFunction", "functionName", "char<PERSON>t", "_counters2", "addOtherToken", "identifier", "isOpening", "quotes", "_extends", "assign", "hasOwnProperty", "_Logger2", "_Window", "html2canvas", "conf", "config", "defaultOptions", "devicePixelRatio"], "mappings": "2FAIAA,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIC,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAInB,IAAII,EAAO,oBASPC,EAAO,oBASPC,EAAM,2DASNC,EAAO,8EAkBPC,EAAQ,WACR,SAASA,EAAMvC,IAhDnB,SAAyBwC,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAiDxGoB,CAAgBC,KAAMH,GAEtB,IAb2BI,EAavBC,EAAOxC,MAAMC,QAAQL,IAbE2C,EAaiB3C,EAZzC,CAAC6C,KAAKC,IAAIH,EAAM,GAAI,KAAME,KAAKC,IAAIH,EAAM,GAAI,KAAME,KAAKC,IAAIH,EAAM,GAAI,KAAMA,EAAMzB,OAAS,EAAIyB,EAAM,GAAK,OApC1G,SAAc3C,GACrB,IAAI+C,EAAQ/C,EAAM+C,MAAMZ,GACxB,QAAIY,GACO,CAACC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAKC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAKC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAK,MA6C9EE,CAAKjD,IA9BxD,SAAaA,GACnB,IAAI+C,EAAQ/C,EAAM+C,MAAMV,GACxB,QAAIU,GACO,CAACG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAK,MA2BMI,CAAInD,IArBrE,SAAcA,GACrB,IAAI+C,EAAQ/C,EAAM+C,MAAMT,GACxB,SAAIS,GAASA,EAAM7B,OAAS,IACjB,CAACgC,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,KAkBOK,CAAKpD,KAR/EqD,EAQoGrD,EARlFsD,iBACP,IAhChB,SAActD,GACrB,IAAI+C,EAAQ/C,EAAM+C,MAAMX,GACxB,QAAIW,GACO,CAACC,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAKP,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAKP,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAK,MAoCVC,CAAKxD,IAAU,CAAC,EAAG,EAAG,EAAG,MAC3IyD,EAAQxD,EAAe2C,EAAM,GAC7Bc,EAAID,EAAM,GACVE,EAAIF,EAAM,GACVG,EAAIH,EAAM,GACVI,EAAIJ,EAAM,GAEdf,KAAKgB,EAAIA,EACThB,KAAKiB,EAAIA,EACTjB,KAAKkB,EAAIA,EACTlB,KAAKmB,EAAIA,EAeb,OAZAvC,EAAaiB,EAAO,CAAC,CACjBT,IAAK,gBACL9B,MAAO,WACH,OAAkB,IAAX0C,KAAKmB,IAEjB,CACC/B,IAAK,WACL9B,MAAO,WACH,OAAkB,OAAX0C,KAAKmB,GAAyB,IAAXnB,KAAKmB,EAAU,QAAUnB,KAAKgB,EAAI,IAAMhB,KAAKiB,EAAI,IAAMjB,KAAKkB,EAAI,IAAMlB,KAAKmB,EAAI,IAAM,OAASnB,KAAKgB,EAAI,IAAMhB,KAAKiB,EAAI,IAAMjB,KAAKkB,EAAI,QAIhKrB,EA7BC,GAgCZxC,EAAQ+D,QAAUvB,EAGlB,IAAIc,EAAe,CACfU,YAAa,CAAC,EAAG,EAAG,EAAG,GACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,EAAG,EAAG,EAAG,MACjBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,MAAO,CAAC,IAAK,GAAI,GAAI,MACrBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,MAAO,CAAC,IAAK,IAAK,GAAI,MACtBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,GAAI,GAAI,MACvBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,SAAU,CAAC,EAAG,EAAG,IAAK,MACtBC,SAAU,CAAC,EAAG,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,GAAI,MAC9BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,EAAG,IAAK,EAAG,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,EAAG,IAAK,MAC3BC,eAAgB,CAAC,GAAI,IAAK,GAAI,MAC9BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,QAAS,CAAC,IAAK,EAAG,EAAG,MACrBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,cAAe,CAAC,GAAI,GAAI,IAAK,MAC7BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,EAAG,IAAK,IAAK,MAC7BC,WAAY,CAAC,IAAK,EAAG,IAAK,MAC1BC,SAAU,CAAC,IAAK,GAAI,IAAK,MACzBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,WAAY,CAAC,GAAI,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,YAAa,CAAC,GAAI,IAAK,GAAI,MAC3BC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,KAAM,CAAC,IAAK,IAAK,EAAG,MACpBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,MAAO,CAAC,EAAG,IAAK,EAAG,MACnBC,YAAa,CAAC,IAAK,IAAK,GAAI,MAC5BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,OAAQ,CAAC,GAAI,EAAG,IAAK,MACrBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,EAAG,MACzBC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,qBAAsB,CAAC,IAAK,IAAK,IAAK,MACtCC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,cAAe,CAAC,GAAI,IAAK,IAAK,MAC9BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,IAAK,EAAG,MAClBC,UAAW,CAAC,GAAI,IAAK,GAAI,MACzBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,OAAQ,CAAC,IAAK,EAAG,EAAG,MACpBC,iBAAkB,CAAC,IAAK,IAAK,IAAK,MAClCC,WAAY,CAAC,EAAG,EAAG,IAAK,MACxBC,aAAc,CAAC,IAAK,GAAI,IAAK,MAC7BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,GAAI,IAAK,IAAK,MAC/BC,gBAAiB,CAAC,IAAK,IAAK,IAAK,MACjCC,kBAAmB,CAAC,EAAG,IAAK,IAAK,MACjCC,gBAAiB,CAAC,GAAI,IAAK,IAAK,MAChCC,gBAAiB,CAAC,IAAK,GAAI,IAAK,MAChCC,aAAc,CAAC,GAAI,GAAI,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,MAAO,CAAC,IAAK,IAAK,EAAG,MACrBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,UAAW,CAAC,IAAK,GAAI,EAAG,MACxBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,GAAI,MACrBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,EAAG,IAAK,MACtBC,cAAe,CAAC,IAAK,GAAI,IAAK,MAC9BC,IAAK,CAAC,IAAK,EAAG,EAAG,MACjBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,GAAI,GAAI,MAC3BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,WAAY,CAAC,IAAK,IAAK,GAAI,MAC3BC,SAAU,CAAC,GAAI,IAAK,GAAI,MACxBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,IAAK,CAAC,IAAK,IAAK,IAAK,MACrBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,YAAa,CAAC,IAAK,IAAK,GAAI,OAGdpN,EAAQqN,YAAc,IAAI7K,EAAM,CAAC,EAAG,EAAG,EAAG,K,kCCpP5D1C,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQsN,iBAAmBtN,EAAQuN,wBAA0BvN,EAAQwN,uBAAyBxN,EAAQyN,mBAAqBzN,EAAQ0N,kBAAoB1N,EAAQ2N,oBAAsB3N,EAAQ4N,oBAAsB5N,EAAQ6N,YAAc7N,EAAQ8N,YAASjN,EAE1P,IAAIU,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAIf+L,EAAWC,EAFD,EAAQ,OAMlBC,EAAgBD,EAFD,EAAQ,OAI3B,SAASA,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAIvF,IAQIJ,EAAS9N,EAAQ8N,OAAS,WAC1B,SAASA,EAAOM,EAAGC,EAAGC,EAAGC,IAX7B,SAAyB9L,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAYxGoB,CAAgBC,KAAMmL,GAEtBnL,KAAK6L,KAAOJ,EACZzL,KAAK8L,IAAMJ,EACX1L,KAAK+L,MAAQJ,EACb3L,KAAKgM,OAASJ,EAUlB,OAPAhN,EAAauM,EAAQ,KAAM,CAAC,CACxB/L,IAAK,iBACL9B,MAAO,SAAwB2O,EAAYC,EAASC,GAChD,OAAO,IAAIhB,EAAOc,EAAWJ,KAAOK,EAASD,EAAWH,IAAMK,EAASF,EAAWF,MAAOE,EAAWD,YAIrGb,EAjBmB,GAkE1BiB,GA9Cc/O,EAAQ6N,YAAc,SAAqBmB,EAAMH,EAASC,GACxE,OAAOhB,EAAOmB,eAAeD,EAAKE,wBAAyBL,EAASC,IAG9C9O,EAAQ4N,oBAAsB,SAA6BuB,EAAQC,GACzF,OAAO,IAAItB,EAAOqB,EAAOX,KAAOY,EA9BzB,GA8BuCC,YAAaF,EAAOV,IAAMW,EAjClE,GAiC+EC,YAAaF,EAAOT,OAASU,EAhC1G,GAgCyHC,YAAcD,EA9BxI,GA8BsJC,aAAcF,EAAOR,QAAUS,EAjCtL,GAiCmMC,YAAcD,EA/B9M,GA+B8NC,eAGjNrP,EAAQ2N,oBAAsB,SAA6BwB,EAAQG,EAASF,GAElG,IAAIG,EAAaD,EAtCX,GAsCwBrP,MAC1BuP,EAAeF,EAtCX,GAsC0BrP,MAC9BwP,EAAgBH,EAtCX,GAsC2BrP,MAChCyP,EAAcJ,EAtCX,GAsCyBrP,MAEhC,OAAO,IAAI6N,EAAOqB,EAAOX,KAAOkB,EAAcN,EAxCvC,GAwCqDC,YAAaF,EAAOV,IAAMc,EAAaH,EA3C7F,GA2C0GC,YAAaF,EAAOT,OAASU,EA1CrI,GA0CoJC,YAAcD,EAxCnK,GAwCiLC,YAAcK,EAAcF,GAAeL,EAAOR,QAAUS,EA3C9O,GA2C2PC,YAAcD,EAzCtQ,GAyCsRC,YAAcE,EAAaE,KAGtSzP,EAAQ0N,kBAAoB,SAA2BiC,GAC3E,IAAIC,EAAOD,EAASC,KAChBC,EAAkBF,EAASE,gBAE/B,IAAKD,IAASC,EACV,MAAM,IAAIC,MAA8E,IAE5F,IAAIpB,EAAQ5L,KAAKiN,IAAIjN,KAAKiN,IAAIH,EAAKI,YAAaH,EAAgBG,aAAclN,KAAKiN,IAAIH,EAAKK,YAAaJ,EAAgBI,aAAcnN,KAAKiN,IAAIH,EAAKM,YAAaL,EAAgBK,cAE9KvB,EAAS7L,KAAKiN,IAAIjN,KAAKiN,IAAIH,EAAKO,aAAcN,EAAgBM,cAAerN,KAAKiN,IAAIH,EAAKQ,aAAcP,EAAgBO,cAAetN,KAAKiN,IAAIH,EAAKS,aAAcR,EAAgBQ,eAExL,OAAO,IAAIvC,EAAO,EAAG,EAAGY,EAAOC,IAGV3O,EAAQyN,mBAAqB,SAA4B6C,EAAQC,GACtF,OAAQA,GACJ,KA9DE,EA+DE,OAAOxB,EAAqBuB,EAAOE,aAAcF,EAAOG,aAAcH,EAAOI,cAAeJ,EAAOK,eACvG,KA/DI,EAgEA,OAAO5B,EAAqBuB,EAAOI,cAAeJ,EAAOK,cAAeL,EAAOM,iBAAkBN,EAAOO,kBAC5G,KAhEK,EAiED,OAAO9B,EAAqBuB,EAAOM,iBAAkBN,EAAOO,iBAAkBP,EAAOQ,gBAAiBR,EAAOS,iBACjH,KAjEG,EAkEH,QACI,OAAOhC,EAAqBuB,EAAOQ,gBAAiBR,EAAOS,gBAAiBT,EAAOE,aAAcF,EAAOG,gBAIzF,SAA8BO,EAAQC,EAAQC,EAAQC,GAC7E,IAAIC,EAAO,GAyBX,OAxBIJ,aAAkB/C,EAAclK,QAChCqN,EAAKlQ,KAAK8P,EAAOK,UAAU,IAAK,IAEhCD,EAAKlQ,KAAK8P,GAGVE,aAAkBjD,EAAclK,QAChCqN,EAAKlQ,KAAKgQ,EAAOG,UAAU,IAAK,IAEhCD,EAAKlQ,KAAKgQ,GAGVC,aAAkBlD,EAAclK,QAChCqN,EAAKlQ,KAAKiQ,EAAOE,UAAU,IAAK,GAAMC,WAEtCF,EAAKlQ,KAAKiQ,GAGVF,aAAkBhD,EAAclK,QAChCqN,EAAKlQ,KAAK+P,EAAOI,UAAU,IAAK,GAAOC,WAEvCF,EAAKlQ,KAAK+P,GAGPG,IAwDPG,GArDyBvR,EAAQwN,uBAAyB,SAAgC8C,GAC1F,MAAO,CAACA,EAAOE,aAAcF,EAAOI,cAAeJ,EAAOM,iBAAkBN,EAAOQ,kBAGzD9Q,EAAQuN,wBAA0B,SAAiC+C,GAC7F,MAAO,CAACA,EAAOG,aAAcH,EAAOK,cAAeL,EAAOO,iBAAkBP,EAAOS,kBAGhE/Q,EAAQsN,iBAAmB,SAA0B6B,EAAQC,EAASoC,GACzF,IAAIC,EAAMD,EAAaD,EAAOG,UA3G1B,GA2GuCC,iBAAiBxC,EAAOT,OAC/DkD,EAAMJ,EAAaD,EAAOG,UA3G1B,GA2GuCC,iBAAiBxC,EAAOR,QAC/DkD,EAAML,EAAaD,EAAOO,WA7G1B,GA6GwCH,iBAAiBxC,EAAOT,OAChEqD,EAAMP,EAAaD,EAAOO,WA7G1B,GA6GwCH,iBAAiBxC,EAAOR,QAChEqD,EAAMR,EAAaD,EAAOU,cA/G1B,GA+G2CN,iBAAiBxC,EAAOT,OACnEwD,EAAMV,EAAaD,EAAOU,cA/G1B,GA+G2CN,iBAAiBxC,EAAOR,QACnEwD,EAAMX,EAAaD,EAAOa,aAjH1B,GAiH0CT,iBAAiBxC,EAAOT,OAClE2D,EAAMb,EAAaD,EAAOa,aAjH1B,GAiH0CT,iBAAiBxC,EAAOR,QAElE2D,EAAU,GACdA,EAAQpR,MAAMuQ,EAAMI,GAAO1C,EAAOT,OAClC4D,EAAQpR,MAAMiR,EAAMH,GAAO7C,EAAOT,OAClC4D,EAAQpR,MAAM0Q,EAAMS,GAAOlD,EAAOR,QAClC2D,EAAQpR,MAAM6Q,EAAMG,GAAO/C,EAAOR,QAClC,IAAI4D,EAAYzP,KAAKiN,IAAIyC,MAAM1P,KAAMwP,GAEjCC,EAAY,IACZd,GAAOc,EACPX,GAAOW,EACPV,GAAOU,EACPR,GAAOQ,EACPP,GAAOO,EACPL,GAAOK,EACPJ,GAAOI,EACPF,GAAOE,GAGX,IAAIE,EAAWtD,EAAOT,MAAQmD,EAC1Ba,EAAcvD,EAAOR,OAASuD,EAC9BS,EAAcxD,EAAOT,MAAQsD,EAC7BY,EAAazD,EAAOR,OAAS0D,EAEjC,MAAO,CACH7B,aAAciB,EAAM,GAAKG,EAAM,EAAIiB,EAAe1D,EAAOX,KAAMW,EAAOV,IAAKgD,EAAKG,EAAKL,EAAOG,UAAY,IAAI3D,EAAShK,QAAQoL,EAAOX,KAAMW,EAAOV,KACjJgC,aAAcgB,EAAM,GAAKG,EAAM,EAAIiB,EAAe1D,EAAOX,KAAOY,EA/I7D,GA+I2EC,YAAaF,EAAOV,IAAMW,EAlJtG,GAkJmHC,YAAavM,KAAKiN,IAAI,EAAG0B,EAAMrC,EA/IjJ,GA+I+JC,aAAcvM,KAAKiN,IAAI,EAAG6B,EAAMxC,EAlJhM,GAkJ6MC,aAAckC,EAAOG,UAAY,IAAI3D,EAAShK,QAAQoL,EAAOX,KAAOY,EA/IhR,GA+I8RC,YAAaF,EAAOV,IAAMW,EAlJzT,GAkJsUC,aACxUqB,cAAemB,EAAM,GAAKE,EAAM,EAAIc,EAAe1D,EAAOX,KAAOiE,EAAUtD,EAAOV,IAAKoD,EAAKE,EAAKR,EAAOO,WAAa,IAAI/D,EAAShK,QAAQoL,EAAOX,KAAOW,EAAOT,MAAOS,EAAOV,KAC7KkC,cAAekB,EAAM,GAAKE,EAAM,EAAIc,EAAe1D,EAAOX,KAAO1L,KAAKC,IAAI0P,EAAUtD,EAAOT,MAAQU,EAjJhG,GAiJ8GC,aAAcF,EAAOV,IAAMW,EApJ1I,GAoJuJC,YAAaoD,EAAWtD,EAAOT,MAAQU,EAjJ7L,GAiJ2MC,YAAc,EAAIwC,EAAMzC,EAjJnO,GAiJiPC,YAAa0C,EAAM3C,EApJrQ,GAoJkRC,YAAakC,EAAOO,WAAa,IAAI/D,EAAShK,QAAQoL,EAAOX,KAAOW,EAAOT,MAAQU,EAnJnW,GAmJkXC,YAAaF,EAAOV,IAAMW,EApJ9Y,GAoJ2ZC,aAC7ZuB,iBAAkBoB,EAAM,GAAKE,EAAM,EAAIW,EAAe1D,EAAOX,KAAOmE,EAAaxD,EAAOV,IAAMiE,EAAaV,EAAKE,EAAKX,EAAOU,cAAgB,IAAIlE,EAAShK,QAAQoL,EAAOX,KAAOW,EAAOT,MAAOS,EAAOV,IAAMU,EAAOR,QACjNkC,iBAAkBmB,EAAM,GAAKE,EAAM,EAAIW,EAAe1D,EAAOX,KAAO1L,KAAKC,IAAI4P,EAAaxD,EAAOT,MAAQU,EAnJtG,GAmJoHC,aAAcF,EAAOV,IAAM3L,KAAKC,IAAI2P,EAAavD,EAAOR,OAASS,EAtJtL,GAsJmMC,aAAcvM,KAAKiN,IAAI,EAAGiC,EAAM5C,EArJjO,GAqJgPC,aAAc6C,EAAM9C,EApJnQ,GAoJmRC,YAAakC,EAAOU,cAAgB,IAAIlE,EAAShK,QAAQoL,EAAOX,KAAOW,EAAOT,MAAQU,EArJ1W,GAqJyXC,YAAaF,EAAOV,IAAMU,EAAOR,OAASS,EApJla,GAoJkbC,aACvbyB,gBAAiBqB,EAAM,GAAKE,EAAM,EAAIQ,EAAe1D,EAAOX,KAAMW,EAAOV,IAAMmE,EAAYT,EAAKE,EAAKd,EAAOa,aAAe,IAAIrE,EAAShK,QAAQoL,EAAOX,KAAMW,EAAOV,IAAMU,EAAOR,QACjLoC,gBAAiBoB,EAAM,GAAKE,EAAM,EAAIQ,EAAe1D,EAAOX,KAAOY,EArJhE,GAqJ8EC,YAAaF,EAAOV,IAAMmE,EAAY9P,KAAKiN,IAAI,EAAGoC,EAAM/C,EArJtI,GAqJoJC,aAAcgD,EAAMjD,EAtJtK,GAsJsLC,YAAakC,EAAOa,aAAe,IAAIrE,EAAShK,QAAQoL,EAAOX,KAAOY,EArJ9P,GAqJ4QC,YAAaF,EAAOV,IAAMU,EAAOR,OAASS,EAtJpT,GAsJoUC,eAIpU,CACTqC,SAAU,EACVI,UAAW,EACXG,aAAc,EACdG,YAAa,IAGbS,EAAiB,SAAwBzE,EAAGC,EAAGyE,EAAIC,EAAIC,GACvD,IAAIC,GAAcnQ,KAAKoQ,KAAK,GAAK,GAAK,EAA1B,EACRC,EAAKL,EAAKG,EACVG,EAAKL,EAAKE,EACVI,EAAKjF,EAAI0E,EACTQ,EAAKjF,EAAI0E,EAEb,OAAQC,GACJ,KAAKzB,EAAOG,SACR,OAAO,IAAIzD,EAAclK,QAAQ,IAAIgK,EAAShK,QAAQqK,EAAGkF,GAAK,IAAIvF,EAAShK,QAAQqK,EAAGkF,EAAKF,GAAK,IAAIrF,EAAShK,QAAQsP,EAAKF,EAAI9E,GAAI,IAAIN,EAAShK,QAAQsP,EAAIhF,IAC/J,KAAKkD,EAAOO,UACR,OAAO,IAAI7D,EAAclK,QAAQ,IAAIgK,EAAShK,QAAQqK,EAAGC,GAAI,IAAIN,EAAShK,QAAQqK,EAAI+E,EAAI9E,GAAI,IAAIN,EAAShK,QAAQsP,EAAIC,EAAKF,GAAK,IAAIrF,EAAShK,QAAQsP,EAAIC,IAC9J,KAAK/B,EAAOU,aACR,OAAO,IAAIhE,EAAclK,QAAQ,IAAIgK,EAAShK,QAAQsP,EAAIhF,GAAI,IAAIN,EAAShK,QAAQsP,EAAIhF,EAAI+E,GAAK,IAAIrF,EAAShK,QAAQqK,EAAI+E,EAAIG,GAAK,IAAIvF,EAAShK,QAAQqK,EAAGkF,IAC9J,KAAK/B,EAAOa,YACZ,QACI,OAAO,IAAInE,EAAclK,QAAQ,IAAIgK,EAAShK,QAAQsP,EAAIC,GAAK,IAAIvF,EAAShK,QAAQsP,EAAKF,EAAIG,GAAK,IAAIvF,EAAShK,QAAQqK,EAAGC,EAAI+E,GAAK,IAAIrF,EAAShK,QAAQqK,EAAGC,O,kCCtMvKvO,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQuT,iCAAmCvT,EAAQwT,iBAAc3S,EAEjE,IAMgCqN,EAN5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEfyR,EAAiB,EAAQ,OAIGvF,EAFauF,IAEQvF,EAAIC,WAIzD,IAEIqF,EAAcxT,EAAQwT,YAAc,CACpCE,GAAI,EACJC,WAAY,GAGZC,EAAS,WACT,SAASA,EAAO3T,IAVpB,SAAyBwC,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAWxGoB,CAAgBC,KAAMiR,GAEtBjR,KAAKkR,KAA0C,MAAnC5T,EAAM6T,OAAO7T,EAAMkB,OAAS,GAAaqS,EAAYG,WAAaH,EAAYE,GAC1F,IAAIK,EAAcC,WAAW/T,GAI7B0C,KAAK1C,MAAQgU,MAAMF,GAAe,EAAIA,EAoB1C,OAjBAxS,EAAaqS,EAAQ,CAAC,CAClB7R,IAAK,eACL9B,MAAO,WACH,OAAO0C,KAAKkR,OAASL,EAAYG,aAEtC,CACC5R,IAAK,mBACL9B,MAAO,SAA0BiU,GAC7B,OAAOvR,KAAKwR,eAAiBD,GAAgBvR,KAAK1C,MAAQ,KAAO0C,KAAK1C,SAE1E,CAAC,CACD8B,IAAK,SACL9B,MAAO,SAAgBmU,GACnB,OAAO,IAAIR,EAAOQ,OAInBR,EA7BE,GAgCb5T,EAAQ+D,QAAU6P,EAQqB5T,EAAQuT,iCAAmC,SAA0Cc,EAAWpU,EAAOqU,GAC1I,OAAQA,GACJ,IAAK,KACL,IAAK,IACD,OAAO,IAAIV,EAAO3T,EAAQqU,GAC9B,IAAK,KACL,IAAK,MACD,IAAInT,EAAS,IAAIyS,EAAO3T,GAExB,OADAkB,EAAOlB,OAAkB,OAATqU,EAAgBN,WAAWK,EAAUE,MAAMC,KAAKC,UAbtD,SAASC,EAAgBL,GAC3C,IAAIM,EAASN,EAAUM,OACvB,OAAOA,EAASD,EAAgBC,GAAUX,WAAWK,EAAUE,MAAMC,KAAKC,UAWUC,CAAgBL,GACrFlT,EACX,QAEI,OAAO,IAAIyS,EAAO,Q,kCC1E9B9T,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAwDgCiO,EAxD5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf4S,EAAS,EAAQ,MAEjBC,GAoD4B3G,EApDK0G,IAoDgB1G,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAlDnF4G,EAAQ,EAAQ,MAEhBC,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAElBC,EAAgB,EAAQ,MAExBC,EAAW,EAAQ,MAEnBC,EAAS,EAAQ,MAEjBC,EAAQ,EAAQ,MAEhBC,EAAiB,EAAQ,MAEzBC,EAAa,EAAQ,MAErBC,EAAa,EAAQ,MAErBC,EAAU,EAAQ,MAElBC,EAAY,EAAQ,MAEpBC,EAAgB,EAAQ,MAExBC,EAAW,EAAQ,MAEnBC,EAAY,EAAQ,MAEpBC,EAAkB,EAAQ,MAE1BC,EAAc,EAAQ,MAEtBC,EAAiB,EAAQ,MAEzBC,EAAa,EAAQ,MAErBC,EAAc,EAAQ,MAEtBC,EAAa,EAAQ,MAErBC,EAAU,EAAQ,MAElBC,EAAU,EAAQ,MAElBC,EAAS,EAAQ,MAEjBC,EAAY,EAAQ,MAMxB,IAAIC,EAAa,CAAC,QAAS,WAAY,UAEnCC,EAAgB,WAChB,SAASA,EAAcxH,EAAM2F,EAAQ8B,EAAgBC,GACjD,IAAIC,EAAQhU,MANpB,SAAyBF,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAQxGoB,CAAgBC,KAAM6T,GAEtB7T,KAAKgS,OAASA,EACdhS,KAAKiU,QAAU5H,EAAK4H,QACpBjU,KAAK+T,MAAQA,EACb/T,KAAKkU,WAAa,GAClBlU,KAAKmU,UAAY,GACS,iBAAf9H,EAAK+H,QACZpU,KAAKqU,UAAYhI,EAAK+H,OAE1B,IAAIE,EAAcjI,EAAKkI,cAAcD,YACjCpI,EAAUoI,EAAYE,YACtBrI,EAAUmI,EAAYG,YACtB7C,EAAQ0C,EAAYI,iBAAiBrI,EAAM,MAC3CsI,GAAU,EAAIpC,EAASqC,cAAchD,EAAM+C,SAE3CE,EAAyB,UAAdxI,EAAK6E,MAAkC,aAAd7E,EAAK6E,KAEzCb,GAAW,EAAI4C,EAAU6B,eAAelD,EAAMvB,UAiClD,GA/BArQ,KAAK4R,MAAQ,CACTmD,WAAYF,EAAWnB,EAAOsB,kBAAmB,EAAI5C,EAAY6C,iBAAiBrD,EAAOkC,GACzFoB,OAAQL,EAAWnB,EAAOyB,eAAgB,EAAI9C,EAAQ+C,aAAaxD,GACnE/C,cAAexC,aAAgBiI,EAAYe,kBAAoBhJ,aAAgBgJ,mBAAqBR,GAAW,EAAInB,EAAO4B,sBAAsBjJ,IAAQ,EAAIiG,EAAciD,mBAAmB3D,GAC7L4D,MAAOX,EAAWnB,EAAO+B,YAAc,IAAIvD,EAAQ9Q,QAAQwQ,EAAM4D,OACjEb,QAASA,EACTe,OAAO,EAAIlD,EAAOmD,eAAe/D,EAAM8D,OACvC7D,MAAM,EAAIY,EAAMmD,WAAWhE,GAC3BiE,eAAe,EAAInD,EAAeoD,oBAAoBlE,EAAMiE,eAC5DE,UAAWpB,IAAYpC,EAASyD,QAAQC,WAAY,EAAIrD,EAAWsD,gBAAgBtE,GAAS,KAC5FuE,WAAW,EAAIxD,EAAWyD,gBAAgBxE,EAAMuE,WAChDE,QAAQ,EAAIxD,EAAQyD,aAAa1E,GACjC2E,QAASlF,WAAWO,EAAM2E,SAC1BC,UAAgD,IAAtC5C,EAAW6C,QAAQpK,EAAK4H,UAAkB,EAAInB,EAAU4D,eAAe9E,EAAM4E,UAAY1D,EAAU6D,SAASC,OACtHC,cAAc,EAAI9D,EAAc+D,mBAAmBlF,EAAMiF,aAAejF,EAAMiF,aAAejF,EAAMmF,UACnGpK,SAAS,EAAIqG,EAASgE,cAAcpF,GACpCvB,SAAUA,EACV4G,gBAAgB,EAAI/D,EAAgBgE,qBAAqBtF,GACzDuF,YAAY,EAAIhE,EAAYiE,iBAAiBxF,EAAMuF,YACnDE,eAAe,EAAIjE,EAAekE,oBAAoB1F,EAAMyF,eAC5DE,WAAW,EAAIlE,EAAWmE,gBAAgB5F,GAC1C6F,YAAY,EAAInE,EAAYoE,iBAAiB9F,EAAM6F,YACnDE,WAAW,EAAIpE,EAAWqE,gBAAgBhG,EAAM+F,WAChDE,QAAQ,EAAIrE,EAAQsE,aAAazH,IAAa4C,EAAU8E,SAASC,OAASpG,EAAMiG,OAAS,SAGzF7X,KAAKiY,kBAEL5L,EAAKuF,MAAM2F,UAAY,uBAGvB5C,IAAYpC,EAASyD,QAAQC,UAAW,CACxC,IAAIiC,GAAY,EAAIvE,EAAUwE,cAAcnY,MAC5C,GAAIkY,EAAW,CACX,IAAIE,EAAYF,EAAU/D,UAAU3V,OACpC0Z,EAAU/D,UAAU5V,KAAKyB,MACzBA,KAAKoY,UAAY/L,EAAKgM,aAAa,UAAkC,iBAAfhM,EAAK/O,MAAqB+O,EAAK/O,MAAsB,IAAd8a,EAAiD,iBAAxBF,EAAU7D,UAAyB6D,EAAU7D,UAAY,EAAI6D,EAAU/D,UAAUiE,EAAY,GAAGA,UAAY,GAKrN,QAAjB/L,EAAK4H,SACL5H,EAAKiM,iBAAiB,QAAQ,WAC1BtE,EAAMxH,QAAS,EAAIiH,EAAQvI,aAAamB,EAAMH,EAASC,GACvD6H,EAAMuE,cAAe,EAAI9E,EAAQ9I,kBAAkBqJ,EAAMxH,OAAQwH,EAAMpC,MAAMsD,OAAQlB,EAAMpC,MAAM/C,iBAGzG7O,KAAKwY,MAAQC,EAASpM,EAAMyH,GAC5B9T,KAAKwM,OAASqI,GAAW,EAAInB,EAAOgF,sBAAqB,EAAIjF,EAAQvI,aAAamB,EAAMH,EAASC,KAAY,EAAIsH,EAAQvI,aAAamB,EAAMH,EAASC,GACrJnM,KAAKuY,cAAe,EAAI9E,EAAQ9I,kBAAkB3K,KAAKwM,OAAQxM,KAAK4R,MAAMsD,OAAQlV,KAAK4R,MAAM/C,cAqEjG,OA5DAjQ,EAAaiV,EAAe,CAAC,CACzBzU,IAAK,eACL9B,MAAO,WACH,IAAIqb,EAAc3Y,KAAKgS,OAAShS,KAAKgS,OAAO4G,eAAiB,GAG7D,OAFgB5Y,KAAK4R,MAAM4E,WAAa1D,EAAU6D,SAASkC,QAExCF,EAAYG,OAAO,EAAC,EAAIrF,EAAQ7I,yBAAyB5K,KAAKuY,gBAAkBI,IAExG,CACCvZ,IAAK,WACL9B,MAAO,WACH,OAAO0C,KAAK+Y,kBAAoB/Y,KAAKgZ,eAAiBhZ,KAAKiZ,2BAEhE,CACC7Z,IAAK,YACL9B,MAAO,WACH,QAAQ,EAAI6U,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQmD,OAASnZ,KAAK4R,MAAM2E,QAAU,GAAKvW,KAAK4R,MAAM6F,aAAenE,EAAY8F,WAAWP,UAE1J,CACCzZ,IAAK,yBACL9B,MAAO,WACH,OAAO0C,KAAK4R,MAAMvB,WAAa4C,EAAU8E,SAASC,QAAUhY,KAAK4R,MAAMvB,WAAa4C,EAAU8E,SAASsB,WAE5G,CACCja,IAAK,eACL9B,MAAO,WACH,OAAO0C,KAAK4R,MAAMvB,WAAa4C,EAAU8E,SAASC,SAEvD,CACC5Y,IAAK,aACL9B,MAAO,WACH,OAAO0C,KAAK4R,MAAM8D,QAAUlD,EAAO8G,MAAMH,OAE9C,CACC/Z,IAAK,gBACL9B,MAAO,WACH,OAAuB,OAAhB0C,KAAKgS,SAEjB,CACC5S,IAAK,gBACL9B,MAAO,WACH,OAAgC,OAAzB0C,KAAK4R,MAAM2F,YAEvB,CACCnY,IAAK,yBACL9B,MAAO,WACH,OAAO0C,KAAKuZ,iBAAmBvZ,KAAK4R,MAAMiG,OAAO2B,OAEtD,CACCpa,IAAK,gBACL9B,MAAO,WACH,OAAO,EAAI6U,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQyD,UAAW,EAAItH,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ0D,gBAAiB,EAAIvH,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ2D,eAAgB,EAAIxH,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ4D,eAAgB,EAAIzH,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ6D,oBAAqB,EAAI1H,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ8D,gBAEnb,CACC1a,IAAK,6BACL9B,MAAO,WACH,OAAO,EAAI6U,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ0D,gBAAiB,EAAIvH,EAAM+G,UAAUlZ,KAAK4R,MAAM+C,QAASpC,EAASyD,QAAQ8D,kBAI3IjG,EA9IS,GAiJpBxW,EAAQ+D,QAAUyS,EAGlB,IAAI4E,EAAW,SAAkBpM,EAAMyH,GACnC,GAAIzH,aAAgBA,EAAKkI,cAAcD,YAAYyF,eAAiB1N,aAAgB0N,cAAe,CAC/F,IAAIvN,GAAS,EAAIiH,EAAQvI,aAAamB,EAAM,EAAG,GAC/CA,EAAK2N,aAAa,QAASxN,EAAOT,MAAQ,MAC1CM,EAAK2N,aAAa,SAAUxN,EAAOR,OAAS,MAC5C,IAAIiO,EAAI,IAAIC,cACZ,OAAOpG,EAAeqG,UAAU,sBAAwBC,mBAAmBH,EAAEI,kBAAkBhO,KAEnG,OAAQA,EAAK4H,SACT,IAAK,MAED,IAAIqG,EAAMjO,EACV,OAAOyH,EAAeqG,UAAUG,EAAIC,YAAcD,EAAIE,KAC1D,IAAK,SAED,IAAIC,EAASpO,EACb,OAAOyH,EAAe4G,WAAWD,GACrC,IAAK,SACD,IAAIE,EAAYtO,EAAKuO,aAAa,wCAClC,GAAID,EACA,OAAOA,EAKnB,OAAO,O,kCC/OXxd,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEID,EAAQ6b,SAAW,SAAkB2B,EAAKvd,GACrD,OAAyB,IAAjBud,EAAMvd,IAGHD,EAAQyd,SAAW,SAAkB3Z,EAAGD,GACnD,OAAOf,KAAKoQ,KAAKpP,EAAIA,EAAID,EAAIA,IAGb7D,EAAQ0d,cAAgB,SAAuBnJ,EAAO9S,GAEtE,IAAK,IAAIrB,EAAImU,EAAMpT,OAAS,EAAGf,GAAK,EAAGA,IAAK,CACxC,IAAIud,EAAWpJ,EAAMqJ,KAAKxd,GAET,YAAbud,GACAlc,EAAO8S,MAAMsJ,YAAYF,EAAUpJ,EAAMuJ,iBAAiBH,IAGlE,OAAOlc,GAGOzB,EAAQ+d,YAAc,kF,kCCvBxCje,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQge,qBAAuBhe,EAAQ4X,gBAAkB5X,EAAQie,8BAAgCje,EAAQke,4BAA8Ble,EAAQme,mCAAqCne,EAAQoe,gCAAkCpe,EAAQqe,gCAAkCre,EAAQse,wBAA0Bte,EAAQue,kBAAoBve,EAAQwe,gBAAkBxe,EAAQye,gBAAkBze,EAAQ0e,uBAAoB7d,EAEtZ,IAEIgU,EAAU7G,EAFD,EAAQ,OAMjB2Q,EAAW3Q,EAFD,EAAQ,OAMlB4Q,EAAS5Q,EAFD,EAAQ,OAMhBD,EAAWC,EAFD,EAAQ,OAIlBoI,EAAU,EAAQ,MAElBT,EAAW,EAAQ,MAEvB,SAAS3H,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAIvF,IAAIwQ,EAAoB1e,EAAQ0e,kBAAoB,CAChDG,OAAQ,EACRC,UAAW,EACXC,SAAU,EACVC,SAAU,GAGVP,EAAkBze,EAAQye,gBAAkB,CAC5CQ,KAAM,EACNC,QAAS,EACTC,MAAO,EACPC,OAAQ,GAGRZ,EAAkBxe,EAAQwe,gBAAkB,CAC5Ca,WAAY,EACZC,YAAa,EACbC,YAAa,GAGbhB,EAAoBve,EAAQue,kBAAoBC,EAIhDgB,EAAiB,SAASA,EAAeC,GAGzC,OA7BJ,SAAyBhd,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCA2B5GoB,CAAgBC,KAAM6c,GAEdC,GACJ,IAAK,UACD9c,KAAK8c,KAAOhB,EAAgBS,QAC5B,MACJ,IAAK,QACDvc,KAAK8c,KAAOhB,EAAgBU,MAC5B,MACJ,IAAK,OACDxc,KAAK8c,KAAOhB,EAAgBQ,KAC5B,MACJ,QACItc,KAAK1C,MAAQ,IAAI0e,EAAS5a,QAAQ0b,KAyC1CC,GArC0B1f,EAAQse,wBAA0B,SAAiCqB,EAAiBxE,EAAOhM,GACrH,IAAIT,EAAQ,EACRC,EAAS,EACT8Q,EAAOE,EAAgBF,KAC3B,GAAIA,EAAK,GAAGA,OAAShB,EAAgBS,SAAWO,EAAK,GAAGA,OAAShB,EAAgBU,MAAO,CACpF,IAAIS,EAAczQ,EAAOT,MAAQS,EAAOR,OACpCkR,EAAe1E,EAAMzM,MAAQyM,EAAMxM,OACvC,OAAOiR,EAAcC,IAAkBJ,EAAK,GAAGA,OAAShB,EAAgBU,OAAS,IAAIP,EAAO7a,QAAQoL,EAAOT,MAAOS,EAAOT,MAAQmR,GAAgB,IAAIjB,EAAO7a,QAAQoL,EAAOR,OAASkR,EAAc1Q,EAAOR,QAmB7M,OAhBI8Q,EAAK,GAAGxf,QACRyO,EAAQ+Q,EAAK,GAAGxf,MAAM0R,iBAAiBxC,EAAOT,QAG9C+Q,EAAK,GAAGA,OAAShB,EAAgBQ,MAAQQ,EAAK,GAAGA,OAAShB,EAAgBQ,KAC1EtQ,EAASwM,EAAMxM,OACR8Q,EAAK,GAAGA,OAAShB,EAAgBQ,KACxCtQ,EAASD,EAAQyM,EAAMzM,MAAQyM,EAAMxM,OAC9B8Q,EAAK,GAAGxf,QACf0O,EAAS8Q,EAAK,GAAGxf,MAAM0R,iBAAiBxC,EAAOR,SAG/C8Q,EAAK,GAAGA,OAAShB,EAAgBQ,OACjCvQ,EAAQC,EAASwM,EAAMxM,OAASwM,EAAMzM,OAGnC,IAAIkQ,EAAO7a,QAAQ2K,EAAOC,IAGC3O,EAAQqe,gCAAkC,SAAyCsB,EAAiBxQ,GACtI,IAAIsQ,EAAOE,EAAgBF,KACvB/Q,EAAQ+Q,EAAK,GAAGxf,MAAQwf,EAAK,GAAGxf,MAAM0R,iBAAiBxC,EAAOT,OAASS,EAAOT,MAC9EC,EAAS8Q,EAAK,GAAGxf,MAAQwf,EAAK,GAAGxf,MAAM0R,iBAAiBxC,EAAOR,QAAU8Q,EAAK,GAAGxf,MAAQyO,EAAQS,EAAOR,OAE5G,OAAO,IAAIiQ,EAAO7a,QAAQ2K,EAAOC,IAGrB,IAAI6Q,EAzDT,SAkHPM,GAvDkC9f,EAAQoe,gCAAkC,SAAyC9N,EAAQyP,GAC7H,OAAQA,GACJ,KAAKvB,EAAgBa,WACjB,OAAO,EAAIjJ,EAAQ5I,wBAAwB8C,GAC/C,KAAKkO,EAAgBc,YACrB,QACI,OAAO,EAAIlJ,EAAQ7I,yBAAyB+C,KAIftQ,EAAQme,mCAAqC,SAA4C6B,EAAkB7Q,EAAQG,EAASuI,GACjK,IAAIoI,GAAa,EAAI7J,EAAQxI,qBAAqBuB,EAAQ0I,GAE1D,OAAQmI,GACJ,KAAKzB,EAAkBc,WACnB,OAAOlQ,EACX,KAAKoP,EAAkBgB,YACnB,IAAI7P,EAAcJ,EAAQqG,EAASuK,cAAcC,MAAMxO,iBAAiBxC,EAAOT,OAC3Ec,EAAeF,EAAQqG,EAASuK,cAAcE,OAAOzO,iBAAiBxC,EAAOT,OAC7Ea,EAAaD,EAAQqG,EAASuK,cAAcG,KAAK1O,iBAAiBxC,EAAOT,OACzEe,EAAgBH,EAAQqG,EAASuK,cAAcI,QAAQ3O,iBAAiBxC,EAAOT,OACnF,OAAO,IAAI0H,EAAQtI,OAAOmS,EAAWzR,KAAOkB,EAAauQ,EAAWxR,IAAMc,EAAY0Q,EAAWvR,MAAQgB,EAAcF,EAAcyQ,EAAWtR,OAASY,EAAaE,GAC1K,KAAK8O,EAAkBe,YACvB,QACI,OAAOW,IAIejgB,EAAQke,4BAA8B,SAAqClL,EAAUyM,EAAMtQ,GACzH,OAAO,IAAIpB,EAAShK,QAAQiP,EAAS,GAAGrB,iBAAiBxC,EAAOT,MAAQ+Q,EAAK/Q,OAAQsE,EAAS,GAAGrB,iBAAiBxC,EAAOR,OAAS8Q,EAAK9Q,UAGvG3O,EAAQie,8BAAgC,SAAuCvG,EAAY1E,EAAUyM,EAAMc,EAA2BpR,GAEtK,OADauI,EAAW8I,QAEpB,KAAK9B,EAAkBK,SACnB,MAAO,CAAC,IAAIhR,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,MAAO1L,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,IAAK,IAAIN,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,KAAOW,EAAOT,OAAQ5L,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,IAAK,IAAIN,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,KAAOW,EAAOT,OAAQ5L,KAAK2d,MAAMhB,EAAK9Q,OAAS4R,EAA0B9R,IAAMuE,EAAS3E,IAAK,IAAIN,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,MAAO1L,KAAK2d,MAAMhB,EAAK9Q,OAAS4R,EAA0B9R,IAAMuE,EAAS3E,KAC7d,KAAKqQ,EAAkBM,SACnB,MAAO,CAAC,IAAIjR,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAAItL,KAAK2d,MAAMtR,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,EAAIqR,EAAK/Q,OAAQ5L,KAAK2d,MAAMtR,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,EAAIqR,EAAK/Q,OAAQ5L,KAAK2d,MAAMtR,EAAOR,OAASQ,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAAItL,KAAK2d,MAAMtR,EAAOR,OAASQ,EAAOV,OAC3d,KAAKiQ,EAAkBI,UACnB,MAAO,CAAC,IAAI/Q,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAAItL,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,IAAK,IAAIN,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,EAAIqR,EAAK/Q,OAAQ5L,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,IAAK,IAAIN,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,EAAIqR,EAAK/Q,OAAQ5L,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,EAAIoR,EAAK9Q,SAAU,IAAIZ,EAAShK,QAAQjB,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAAItL,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,EAAIoR,EAAK9Q,UACplB,QACI,MAAO,CAAC,IAAIZ,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,MAAO1L,KAAK2d,MAAMtR,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,KAAOW,EAAOT,OAAQ5L,KAAK2d,MAAMtR,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,KAAOW,EAAOT,OAAQ5L,KAAK2d,MAAMtR,EAAOR,OAASQ,EAAOV,MAAO,IAAIV,EAAShK,QAAQjB,KAAK2d,MAAMtR,EAAOX,MAAO1L,KAAK2d,MAAMtR,EAAOR,OAASQ,EAAOV,SAIjVzO,EAAQ4X,gBAAkB,SAAyBrD,EAAOkC,GAC5E,MAAO,CACHiK,gBAAiB,IAAI7L,EAAQ9Q,QAAQwQ,EAAMmM,iBAC3Cf,gBAAiBgB,EAAsBpM,EAAOkC,GAC9CmK,eAAgBd,EAAoBvL,EAAMqM,gBAC1CZ,iBAAkBa,EAAsBtM,EAAMyL,oBAI5B,SAA6BY,GACnD,OAAQA,GACJ,IAAK,cACD,OAAOpC,EAAgBc,YAC3B,IAAK,cACD,OAAOd,EAAgBe,YAE/B,OAAOf,EAAgBa,aAGvBwB,EAAwB,SAA+Bb,GACvD,OAAQA,GACJ,IAAK,cACD,OAAOzB,EAAkBe,YAC7B,IAAK,cACD,OAAOf,EAAkBgB,YAEjC,OAAOhB,EAAkBc,YAGzByB,EAAwB,SAA+BC,GACvD,OAAQA,EAAiBC,QACrB,IAAK,YACD,OAAOtC,EAAkBI,UAC7B,IAAK,WACL,IAAK,mBACD,OAAOJ,EAAkBK,SAC7B,IAAK,WACL,IAAK,mBACD,OAAOL,EAAkBM,SAC7B,IAAK,SACD,OAAON,EAAkBG,OAOjC,OAAOH,EAAkBG,QAGzB8B,EAAwB,SAA+BpM,EAAOkC,GAC9D,IAAIwK,EAAUjD,EAAqBzJ,EAAMoL,iBAAiBuB,KAAI,SAAUvB,GACpE,GAA+B,QAA3BA,EAAgBwB,OAAkB,CAClC,IAAIpf,EAAM0U,EAAeqG,UAAU6C,EAAgByB,KAAK,IACxDzB,EAAgByB,KAAOrf,EAAM,CAACA,GAAO,GAEzC,OAAO4d,KAEP0B,EAAY9M,EAAM+M,mBAAmBC,MAAM,KAC3CC,EAAUjN,EAAMwM,iBAAiBQ,MAAM,KACvCE,EAAQlN,EAAMmN,eAAeH,MAAM,KAEvC,OAAON,EAAQC,KAAI,SAAUS,EAAQjL,GACjC,IAAI+I,GAAQgC,EAAM/K,IAxKf,QAwK+BsK,OAAOO,MAAM,KAAKL,IAAIU,GACpD5O,GAAYqO,EAAU3K,IAzKvB,QAyKuCsK,OAAOO,MAAM,KAAKL,IAAIW,GAEhE,MAAO,CACHF,OAAQA,EACRnB,OAAQM,EAAgD,iBAAnBU,EAAQ9K,GAAsB8K,EAAQ9K,GAAS8K,EAAQ,IAC5F/B,KAAMA,EAAKte,OAAS,EAAI,CAACse,EAAK,GAAIC,GAAa,CAACD,EAAK,GAAIA,EAAK,IAC9DzM,SAAUA,EAAS7R,OAAS,EAAI,CAAC6R,EAAS,GAAIA,EAAS,IAAM,CAACA,EAAS,GAAIA,EAAS,SAK5F4O,EAAsB,SAA6BnC,GACnD,MAAgB,SAATA,EAAkBC,EAAY,IAAIF,EAAeC,IAGxDoC,EAAyB,SAAgC7O,GACzD,OAAQA,GACJ,IAAK,SACL,IAAK,QACD,OAAO,IAAI2L,EAAS5a,QAAQ,QAChC,IAAK,OACL,IAAK,MACD,OAAO,IAAI4a,EAAS5a,QAAQ,MAChC,IAAK,OACD,OAAO,IAAI4a,EAAS5a,QAAQ,KAEpC,OAAO,IAAI4a,EAAS5a,QAAQiP,IAG5BgL,EAAuBhe,EAAQge,qBAAuB,SAA8B7C,GACpF,IAAI2G,EAAa,OACbC,EAAU,GAEVX,EAAO,GACPD,EAAS,GACTa,EAAQ,KACRC,EAAa,GACbC,EAAO,EACPC,EAAW,EAEXC,EAAe,WACf,IAAIC,EAAS,GACb,GAAIlB,EAAQ,CACwB,MAA5Bc,EAAWnO,OAAO,EAAG,KACrBmO,EAAaA,EAAWnO,OAAO,EAAGmO,EAAW9gB,OAAS,IAGtD8gB,GACAb,EAAKlgB,KAAK+gB,EAAWjB,QAGzB,IAAIsB,EAAWnB,EAAO/H,QAAQ,IAAK,GAAK,EACZ,MAAxB+H,EAAOrN,OAAO,EAAG,IAAcwO,EAAW,IAC1CD,EAASlB,EAAOrN,OAAO,EAAGwO,GAAU/e,cACpC4d,EAASA,EAAOrN,OAAOwO,IAGZ,UADfnB,EAASA,EAAO5d,gBAEZwe,EAAQ7gB,KAAK,CACTmhB,OAAQA,EACRlB,OAAQA,EACRC,KAAMA,IAIlBA,EAAO,GACPD,EAASc,EAAa,IA+D1B,OA5DA9G,EAAMoG,MAAM,IAAIgB,SAAQ,SAAUC,GAC9B,GAAa,IAATN,IAAcJ,EAAWW,KAAKD,GAAlC,CAGA,OAAQA,GACJ,IAAK,IACIR,EAEMA,IAAUQ,IACjBR,EAAQ,MAFRA,EAAQQ,EAIZ,MACJ,IAAK,IACD,GAAIR,EACA,MACG,GAAa,IAATE,EAEP,YADAA,EAAO,GAGPC,IAEJ,MACJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAAY,CACnB,GAAiB,IAAbC,EAGA,OAFAD,EAAO,OACPE,IAGAD,IAGR,MAEJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAEP,YADAE,IAEG,GAAa,IAATF,GACU,IAAbC,IAAmBhB,EAAOne,MAAM,UAGhC,OAFAoe,EAAKlgB,KAAK+gB,EAAWjB,aACrBiB,EAAa,IAOhB,IAATC,EACAf,GAAUqB,EAEVP,GAAcO,MAItBJ,IACOL,I,kCC7VXjiB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEAD,EAAQ0iB,KAAO,CACtBC,OAAQ,EACRC,aAAc,EACdC,OAAQ,I,kCCNZ/iB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAI6iB,EAAQ,EAAQ,MAoBpB9iB,EAAQ+D,QAhBK,SAASgf,EAAO3U,EAAGC,IAFhC,SAAyB5L,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAG5GoB,CAAgBC,KAAMogB,GAEtBpgB,KAAKkR,KAAOiP,EAAMJ,KAAKC,OACvBhgB,KAAKyL,EAAIA,EACTzL,KAAK0L,EAAIA,I,kCCbbvO,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ6Y,eAAiB7Y,EAAQgjB,mBAAqBhjB,EAAQijB,gBAAkBjjB,EAAQkjB,yBAAsBriB,EAE9G,IAAIkU,EAAc,EAAQ,MAEtBmO,EAAsBljB,EAAQkjB,oBAAsB,CACpDC,OAAQ,EACRC,QAAS,GAGTH,EAAkBjjB,EAAQijB,gBAAkB,CAC5CnH,MAAO,EACPuH,KAAM,EACNR,OAAQ,EACRS,OAAQ,EACRC,QAAS,EACTC,YAAa,EACbC,qBAAsB,EACtBC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,GACbC,aAAc,GACdC,SAAU,GACVC,QAAS,GACTC,UAAW,GACXC,mBAAoB,GACpBC,kBAAmB,GACnBC,gBAAiB,GACjBC,WAAY,GACZC,iBAAkB,GAClBC,SAAU,GACVC,SAAU,GACVC,SAAU,GACVC,OAAQ,GACRC,SAAU,GACVC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,GACnBC,QAAS,GACTC,SAAU,GACVC,eAAgB,GAChBC,MAAO,GACPC,qBAAsB,GACtBC,oBAAqB,GACrBC,sBAAuB,GACvBC,IAAK,GACLC,eAAgB,GAChBC,UAAW,GACXC,UAAW,GACXC,QAAS,GACTC,MAAO,GACPC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,MAAO,GACPC,OAAQ,GACRC,KAAM,GACNC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,IAGnBxD,EAAqBhjB,EAAQgjB,mBAAqB,SAA4BnP,GAC9E,OAAQA,GACJ,IAAK,OACD,OAAOoP,EAAgBI,KAC3B,IAAK,SACD,OAAOJ,EAAgBJ,OAC3B,IAAK,SACD,OAAOI,EAAgBK,OAC3B,IAAK,UACD,OAAOL,EAAgBM,QAC3B,IAAK,cACD,OAAON,EAAgBO,YAC3B,IAAK,uBACD,OAAOP,EAAgBQ,qBAC3B,IAAK,cACD,OAAOR,EAAgBS,YAC3B,IAAK,cACD,OAAOT,EAAgBU,YAC3B,IAAK,cACD,OAAOV,EAAgBW,YAC3B,IAAK,cACD,OAAOX,EAAgBY,YAC3B,IAAK,cACD,OAAOZ,EAAgBa,YAC3B,IAAK,eACD,OAAOb,EAAgBc,aAC3B,IAAK,WACD,OAAOd,EAAgBe,SAC3B,IAAK,UACD,OAAOf,EAAgBgB,QAC3B,IAAK,YACD,OAAOhB,EAAgBiB,UAC3B,IAAK,qBACD,OAAOjB,EAAgBkB,mBAC3B,IAAK,oBACD,OAAOlB,EAAgBmB,kBAC3B,IAAK,kBACD,OAAOnB,EAAgBoB,gBAC3B,IAAK,aACD,OAAOpB,EAAgBqB,WAC3B,IAAK,mBACD,OAAOrB,EAAgBsB,iBAC3B,IAAK,WACD,OAAOtB,EAAgBuB,SAC3B,IAAK,WACD,OAAOvB,EAAgBwB,SAC3B,IAAK,WACD,OAAOxB,EAAgByB,SAC3B,IAAK,SACD,OAAOzB,EAAgB0B,OAC3B,IAAK,WACD,OAAO1B,EAAgB2B,SAC3B,IAAK,iBACD,OAAO3B,EAAgB4B,eAC3B,IAAK,kBACD,OAAO5B,EAAgB6B,gBAC3B,IAAK,oBACD,OAAO7B,EAAgB8B,kBAC3B,IAAK,UACD,OAAO9B,EAAgB+B,QAC3B,IAAK,WACD,OAAO/B,EAAgBgC,SAC3B,IAAK,iBACD,OAAOhC,EAAgBiC,eAC3B,IAAK,QACD,OAAOjC,EAAgBkC,MAC3B,IAAK,uBACD,OAAOlC,EAAgBmC,qBAC3B,IAAK,sBACD,OAAOnC,EAAgBoC,oBAC3B,IAAK,wBACD,OAAOpC,EAAgBqC,sBAC3B,IAAK,MACD,OAAOrC,EAAgBsC,IAC3B,IAAK,iBACD,OAAOtC,EAAgBuC,eAC3B,IAAK,YACD,OAAOvC,EAAgBwC,UAC3B,IAAK,YACD,OAAOxC,EAAgByC,UAC3B,IAAK,UACD,OAAOzC,EAAgB0C,QAC3B,IAAK,QACD,OAAO1C,EAAgB2C,MAC3B,IAAK,UACD,OAAO3C,EAAgB4C,QAC3B,IAAK,sBACD,OAAO5C,EAAgB6C,oBAC3B,IAAK,wBACD,OAAO7C,EAAgB8C,sBAC3B,IAAK,QACD,OAAO9C,EAAgB+C,MAC3B,IAAK,SACD,OAAO/C,EAAgBgD,OAC3B,IAAK,OACD,OAAOhD,EAAgBiD,KAC3B,IAAK,UACD,OAAOjD,EAAgBkD,QAC3B,IAAK,sBACD,OAAOlD,EAAgBmD,oBAC3B,IAAK,wBACD,OAAOnD,EAAgBoD,sBAC3B,IAAK,iBACD,OAAOpD,EAAgBqD,eAC3B,IAAK,kBACD,OAAOrD,EAAgBsD,gBAC3B,IAAK,oBACD,OAAOtD,EAAgBuD,kBAC3B,IAAK,OACL,QACI,OAAOvD,EAAgBnH,OAa/B2K,GATiBzmB,EAAQ6Y,eAAiB,SAAwBtE,GAClE,IAAImS,GAAiB,EAAI3R,EAAYiJ,sBAAsBzJ,EAAMuJ,iBAAiB,qBAClF,MAAO,CACH6I,cAAe3D,EAAmBzO,EAAMuJ,iBAAiB,oBACzD4I,eAAgBA,EAAevlB,OAASulB,EAAe,GAAK,KAC5DE,kBAAmBH,EAAuBlS,EAAMuJ,iBAAiB,0BAI5C,SAAgC9K,GACzD,OAAQA,GACJ,IAAK,SACD,OAAOkQ,EAAoBC,OAC/B,IAAK,UACL,QACI,OAAOD,EAAoBE,Y,kCCtMvCtjB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIsB,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf+T,EAAiB,EAAQ,MAEzB8Q,EAAc,EAAQ,MAI1B,IAAIC,EAAgB,WAChB,SAASA,EAAcC,EAAMpS,EAAQxF,IAHzC,SAAyB1M,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAIxGoB,CAAgBC,KAAMmkB,GAEtBnkB,KAAKokB,KAAOA,EACZpkB,KAAKgS,OAASA,EACdhS,KAAKwM,OAASA,EAWlB,OARA5N,EAAaulB,EAAe,KAAM,CAAC,CAC/B/kB,IAAK,eACL9B,MAAO,SAAsB+O,EAAM2F,GAC/B,IAAIoS,EAAO7M,EAAUlL,EAAKgY,KAAMrS,EAAOJ,MAAMyF,eAC7C,OAAO,IAAI8M,EAAcC,EAAMpS,GAAQ,EAAIkS,EAAYI,iBAAiBF,EAAMpS,EAAQ3F,QAIvF8X,EAjBS,GAoBpB9mB,EAAQ+D,QAAU+iB,EAGlB,IAAII,EAAa,2BAEbhN,EAAY,SAAmB6M,EAAM/Q,GACrC,OAAQA,GACJ,KAAKD,EAAeoR,eAAeC,UAC/B,OAAOL,EAAKxjB,cAChB,KAAKwS,EAAeoR,eAAeD,WAC/B,OAAOH,EAAKM,QAAQH,EAAYI,GACpC,KAAKvR,EAAeoR,eAAeI,UAC/B,OAAOR,EAAKS,cAChB,QACI,OAAOT,IAInB,SAASO,EAAWG,EAAGC,EAAIC,GACvB,OAAIF,EAAEtmB,OAAS,EACJumB,EAAKC,EAAGH,cAGZC,I,kCCvDX3nB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAI2nB,EAAyB,EAAQ,MAmFjCC,EAAe,SAAsBb,GACrC,OAAmB,IAAZA,EAAK,IAAwB,MAAZA,EAAK,IAA0B,IAAZA,EAAK,IAAwB,MAAZA,EAAK,IAuCjEc,EAAW,CAEX,2BAGI,IAAI7nB,EA9HU,SAAyB0P,GAG3C,GAAIA,EAASoY,YAAa,CACtB,IAAIC,EAAQrY,EAASoY,cACrB,GAAIC,EAAM9Y,sBAAuB,CAC7B,IAAI+Y,EAActY,EAASuY,cAAc,aACzCD,EAAY1T,MAAM5F,OAASwZ,QAC3BF,EAAY1T,MAAM+C,QAAU,QAC5B3H,EAASC,KAAKwY,YAAYH,GAE1BD,EAAMK,WAAWJ,GACjB,IAAIK,EAAcN,EAAM9Y,wBACpBqZ,EAAczlB,KAAK2d,MAAM6H,EAAY3Z,QAEzC,GADAgB,EAASC,KAAK4Y,YAAYP,GAbhB,MAcNM,EACA,OAAO,GAKnB,OAAO,EAyGSE,CAAgB9Y,UAE5B,OADA7P,OAAOC,eAAe+nB,EAAU,uBAAwB,CAAE7nB,MAAOA,IAC1DA,GAGX,0BAGI,IAAIA,EApEE,SAAiB0P,GAC3B,IAAIsN,EAAM,IAAIyL,MACVtL,EAASzN,EAASuY,cAAc,UAChCS,EAAMvL,EAAOwL,WAAW,MAC5B3L,EAAIE,IAAM,oEAEV,IACIwL,EAAIE,UAAU5L,EAAK,EAAG,GACtBG,EAAO0L,YACT,MAAOC,GACL,OAAO,EAEX,OAAO,EAwDSC,CAAQrZ,UAEpB,OADA7P,OAAOC,eAAe+nB,EAAU,sBAAuB,CAAE7nB,MAAOA,IACzDA,GAGX,6BAGI,OAAO,SAAUkd,GACb,IAAI8L,EAtHC,SAAoBtZ,EAAUwN,GAC3C,IAAIF,EAAM,IAAIyL,MACVtL,EAASzN,EAASuY,cAAc,UAChCS,EAAMvL,EAAOwL,WAAW,MAE5B,OAAO,IAAIM,SAAQ,SAAUC,GAEzBlM,EAAIE,IAAMA,EAEV,IAAIiM,EAAS,WACT,IACIT,EAAIE,UAAU5L,EAAK,EAAG,GACtBG,EAAO0L,YACT,MAAOC,GACL,OAAOI,GAAQ,GAGnB,OAAOA,GAAQ,IAGnBlM,EAAImM,OAASA,EACbnM,EAAIoM,QAAU,WACV,OAAOF,GAAQ,KAGE,IAAjBlM,EAAIqM,UACJC,YAAW,WACPH,MACD,QA0FUI,CAAW7Z,SAAUwN,GAIlC,OAHArd,OAAOC,eAAe+nB,EAAU,yBAA0B,CAAE7nB,MAAO,WAC3D,OAAOgpB,KAERA,IAIf,oCAGI,IAAIhpB,EAA8B,mBAAfI,MAAMopB,MAA+C,mBAAjBC,OAAOC,MArE9C,SAA2Bha,GAC/C,IAAIyN,EAASzN,EAASuY,cAAc,UAEpC9K,EAAO1O,MADI,IAEX0O,EAAOzO,OAFI,IAGX,IAAIga,EAAMvL,EAAOwL,WAAW,MAC5BD,EAAIiB,UAAY,iBAChBjB,EAAIkB,SAAS,EAAG,EALL,SAOX,IAAI5M,EAAM,IAAIyL,MACVoB,EAAgB1M,EAAO0L,YAC3B7L,EAAIE,IAAM2M,EACV,IAAIC,GAAM,EAAInC,EAAuBoC,wBAV1B,QAU8D,EAAG,EAAG/M,GAI/E,OAHA0L,EAAIiB,UAAY,MAChBjB,EAAIkB,SAAS,EAAG,EAZL,UAcJ,EAAIjC,EAAuBqC,mBAAmBF,GAAKG,MAAK,SAAUjN,GACrE0L,EAAIE,UAAU5L,EAAK,EAAG,GACtB,IAAI+J,EAAO2B,EAAIwB,aAAa,EAAG,EAhBxB,SAgBuCnD,KAC9C2B,EAAIiB,UAAY,MAChBjB,EAAIkB,SAAS,EAAG,EAlBT,SAoBP,IAAI7a,EAAOW,EAASuY,cAAc,OAIlC,OAHAlZ,EAAKuF,MAAMoL,gBAAkB,OAASmK,EAAgB,IACtD9a,EAAKuF,MAAM5F,OAAS8Q,QAEboI,EAAab,IAAQ,EAAIY,EAAuBqC,oBAAmB,EAAIrC,EAAuBoC,wBAxB9F,QAwBkI,EAAG,EAAGhb,IAASka,QAAQkB,QAAO,MACxKF,MAAK,SAAUjN,GAGd,OAFA0L,EAAIE,UAAU5L,EAAK,EAAG,GAEf4K,EAAac,EAAIwB,aAAa,EAAG,EA5BjC,SA4BgDnD,SACxDqD,OAAM,SAAUtB,GACf,OAAO,KAqC8EuB,CAAkB3a,UAAYuZ,QAAQC,SAAQ,GAEnI,OADArpB,OAAOC,eAAe+nB,EAAU,gCAAiC,CAAE7nB,MAAOA,IACnEA,GAGX,0BAGI,IAAIA,OAvGkC,KAA5B,IAAIyoB,OAAQ6B,YAyGtB,OADAzqB,OAAOC,eAAe+nB,EAAU,sBAAuB,CAAE7nB,MAAOA,IACzDA,GAGX,4BAGI,IAAIA,EA3G4C,iBAAtC,IAAIuqB,gBAAiBC,aA6G/B,OADA3qB,OAAOC,eAAe+nB,EAAU,wBAAyB,CAAE7nB,MAAOA,IAC3DA,GAGX,uBAGI,IAAIA,EAAQ,oBAAqB,IAAIuqB,eAErC,OADA1qB,OAAOC,eAAe+nB,EAAU,mBAAoB,CAAE7nB,MAAOA,IACtDA,IAIfD,EAAQ+D,QAAU+jB,G,kCC9LlBhoB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ6Z,oBAAsB7Z,EAAQ0qB,qBAAuB1qB,EAAQ2qB,gBAAkB3qB,EAAQ4qB,2BAAwB/pB,EAEvH,IAIgCqN,EAJ5B0G,EAAS,EAAQ,MAEjBC,GAE4B3G,EAFK0G,IAEgB1G,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAI0c,EAAwB5qB,EAAQ4qB,sBAAwB,CACxDC,MAAO,EACPC,OAAQ,EACRC,OAAQ,EACRC,OAAQ,EACRC,KAAM,GAGNN,EAAkB3qB,EAAQ2qB,gBAAkB,CAC5C7O,KAAM,MAGN4O,EAAuB1qB,EAAQ0qB,qBAAuB,CACtDQ,UAAW,EACXC,SAAU,EACVC,aAAc,EACdC,MAAO,GAGPC,EAAY,SAAmBC,GAC/B,OAAQA,GACJ,IAAK,YACD,OAAOb,EAAqBQ,UAChC,IAAK,WACD,OAAOR,EAAqBS,SAChC,IAAK,eACD,OAAOT,EAAqBU,aAEpC,OAAOV,EAAqBW,OAyBNrrB,EAAQ6Z,oBAAsB,SAA6BtF,GACjF,IAvB2DgX,EAuBvDC,EAtBS,UAD8CD,EAuBVhX,EAAMiX,mBAAqBjX,EAAMiX,mBAAqBjX,EAAMqF,gBArBlG,KAGJ2R,EAAKhK,MAAM,KAAKL,IAAIoK,GAmB3B,OAA2B,OAAvBE,EACOb,EAAgB7O,KAMpB,CACH0P,mBAAoBA,EACpBC,oBALsBlX,EAAMkX,oBAAsB,IAAI5W,EAAQ9Q,QAAQwQ,EAAMkX,qBAAuB,KAMnGC,oBA1BuB,SAAkCnX,GAC7D,OAAQA,GACJ,IAAK,SACD,OAAOqW,EAAsBE,OACjC,IAAK,SACD,OAAOF,EAAsBG,OACjC,IAAK,SACD,OAAOH,EAAsBI,OACjC,IAAK,OACD,OAAOJ,EAAsBK,KAErC,OAAOL,EAAsBC,MAUHc,CAAyBpX,EAAMmX,wB,kCCvE7D5rB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ+X,YAAc/X,EAAQ4rB,aAAe5rB,EAAQ6rB,kBAAehrB,EAEpE,IAIgCqN,EAJ5B0G,EAAS,EAAQ,MAEjBC,GAE4B3G,EAFK0G,IAEgB1G,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAI2d,EAAe7rB,EAAQ6rB,aAAe,CACtC/P,KAAM,EACN+O,MAAO,GAGPe,EAAe5rB,EAAQ4rB,aAAe,CACtCvL,IAAK,EACLD,MAAO,EACPE,OAAQ,EACRH,KAAM,GAGN2L,EAAQhsB,OAAOisB,KAAKH,GAAc1K,KAAI,SAAUtE,GAChD,OAAOA,EAAErZ,iBAWKvD,EAAQ+X,YAAc,SAAqBxD,GACzD,OAAOuX,EAAM5K,KAAI,SAAU8K,GACvB,IAAIC,EAAc,IAAIpX,EAAQ9Q,QAAQwQ,EAAMuJ,iBAAiB,UAAYkO,EAAO,WAC5EE,EAXW,SAA0B3X,GAC7C,OAAQA,GACJ,IAAK,OACD,OAAOsX,EAAa/P,KAE5B,OAAO+P,EAAahB,MAMEsB,CAAiB5X,EAAMuJ,iBAAiB,UAAYkO,EAAO,WACzE3c,EAAc2E,WAAWO,EAAMuJ,iBAAiB,UAAYkO,EAAO,WACvE,MAAO,CACHC,YAAaA,EACbC,YAAaA,EACb7c,YAAa4E,MAAM5E,GAAe,EAAIA,Q,kCC3ClDvP,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEQD,EAAQosB,aAAe,SAAsBC,GAI5D,IAHA,IAAIC,EAAa,GACblsB,EAAI,EACJe,EAASkrB,EAAIlrB,OACVf,EAAIe,GAAQ,CACf,IAAIlB,EAAQosB,EAAIE,WAAWnsB,KAC3B,GAAIH,GAAS,OAAUA,GAAS,OAAUG,EAAIe,EAAQ,CAClD,IAAIqrB,EAAQH,EAAIE,WAAWnsB,KACF,QAAZ,MAARosB,GACDF,EAAWprB,OAAe,KAARjB,IAAkB,KAAe,KAARusB,GAAiB,QAE5DF,EAAWprB,KAAKjB,GAChBG,UAGJksB,EAAWprB,KAAKjB,GAGxB,OAAOqsB,GAGStsB,EAAQysB,cAAgB,WACxC,GAAIC,OAAOD,cACP,OAAOC,OAAOD,cAAcja,MAAMka,OAAQC,WAG9C,IAAIxrB,EAASwrB,UAAUxrB,OACvB,IAAKA,EACD,MAAO,GAOX,IAJA,IAAIyrB,EAAY,GAEZlW,GAAS,EACTmW,EAAS,KACJnW,EAAQvV,GAAQ,CACrB,IAAI2rB,EAAYH,UAAUxrB,QAAUuV,OAAQ7V,EAAY8rB,UAAUjW,GAC9DoW,GAAa,MACbF,EAAU1rB,KAAK4rB,IAEfA,GAAa,MACbF,EAAU1rB,KAAyB,OAAnB4rB,GAAa,IAAcA,EAAY,KAAQ,SAE/DpW,EAAQ,IAAMvV,GAAUyrB,EAAUzrB,OAAS,SAC3C0rB,GAAUH,OAAOK,aAAava,MAAMka,OAAQE,GAC5CA,EAAUzrB,OAAS,GAG3B,OAAO0rB,GAOX,IAvDA,IAmDIG,EAAQ,mEAGRC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5D9sB,EAAI,EAAGA,EAAI4sB,EAAM7rB,OAAQf,IAC9B6sB,EAAOD,EAAMT,WAAWnsB,IAAMA,EAGrBJ,EAAQmtB,OAAS,SAAgBC,GAC1C,IAAIC,EAA+B,IAAhBD,EAAOjsB,OACtBmsB,EAAMF,EAAOjsB,OACbf,OAAI,EACJmtB,EAAI,EACJC,OAAW,EACXC,OAAW,EACXC,OAAW,EACXC,OAAW,EAEmB,MAA9BP,EAAOA,EAAOjsB,OAAS,KACvBksB,IACkC,MAA9BD,EAAOA,EAAOjsB,OAAS,IACvBksB,KAIR,IAAIO,EAAgC,oBAAhBC,aAAqD,oBAAfX,iBAAoE,IAA/BA,WAAW/qB,UAAU2rB,MAAwB,IAAID,YAAYR,GAAgB,IAAIhtB,MAAMgtB,GAClLU,EAAQ1tB,MAAMC,QAAQstB,GAAUA,EAAS,IAAIV,WAAWU,GAE5D,IAAKxtB,EAAI,EAAGA,EAAIktB,EAAKltB,GAAK,EACtBotB,EAAWP,EAAOG,EAAOb,WAAWnsB,IACpCqtB,EAAWR,EAAOG,EAAOb,WAAWnsB,EAAI,IACxCstB,EAAWT,EAAOG,EAAOb,WAAWnsB,EAAI,IACxCutB,EAAWV,EAAOG,EAAOb,WAAWnsB,EAAI,IAExC2tB,EAAMR,KAAOC,GAAY,EAAIC,GAAY,EACzCM,EAAMR,MAAmB,GAAXE,IAAkB,EAAIC,GAAY,EAChDK,EAAMR,MAAmB,EAAXG,IAAiB,EAAe,GAAXC,EAGvC,OAAOC,GAGW5tB,EAAQguB,gBAAkB,SAAyBJ,GAGrE,IAFA,IAAIzsB,EAASysB,EAAOzsB,OAChB4sB,EAAQ,GACHhtB,EAAK,EAAGA,EAAKI,EAAQJ,GAAM,EAChCgtB,EAAM7sB,KAAK0sB,EAAO7sB,EAAK,IAAM,EAAI6sB,EAAO7sB,IAE5C,OAAOgtB,GAGW/tB,EAAQiuB,gBAAkB,SAAyBL,GAGrE,IAFA,IAAIzsB,EAASysB,EAAOzsB,OAChB4sB,EAAQ,GACHG,EAAM,EAAGA,EAAM/sB,EAAQ+sB,GAAO,EACnCH,EAAM7sB,KAAK0sB,EAAOM,EAAM,IAAM,GAAKN,EAAOM,EAAM,IAAM,GAAKN,EAAOM,EAAM,IAAM,EAAIN,EAAOM,IAE7F,OAAOH,I,kCC/GXjuB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQmuB,kBAAoBnuB,EAAQouB,sBAAwBpuB,EAAQ8a,kBAAeja,EAEnF,IAAIiU,EAAQ,EAAQ,MAIhBuZ,EAAkBrgB,EAFD,EAAQ,OAMzBsgB,EAAkBtgB,EAFD,EAAQ,OAIzBuH,EAAa,EAAQ,MAErBgZ,EAAW,EAAQ,MAEvB,SAASvgB,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAGvF,IAEIsgB,EAAgB,CAAC,KAAM,KAAM,QAqF7BC,GAnFezuB,EAAQ8a,aAAe,SAAsBzG,GAC5D,IAAIM,EAASN,EAAUM,OACvB,IAAKA,EACD,OAAO,KAGX,EAAG,CAEC,IAD4D,IAA3C6Z,EAAcpV,QAAQzE,EAAOiC,SAE1C,OAAOjC,EAEXA,EAASA,EAAOA,aACXA,GAET,OAAON,EAAUM,QAGO3U,EAAQouB,sBAAwB,SAA+Bpf,EAAMqF,EAAWoC,GACxG,IAAIiC,EAAYrE,EAAUE,MAAMmE,UAEhC,GAAKA,EAAL,CAIA,IAAInE,EAAQvF,EAAKkI,cAAcD,YAAYI,iBAAiBrI,EAAM,MAC9D0f,EAAU1f,EAAKkI,cAAcgR,cAAc,sBAQ/C,QAPA,EAAIpT,EAAM4I,eAAenJ,EAAOma,GAEhCA,EAAQna,MAAMvB,SAAW,WACzB0b,EAAQna,MAAMoa,OAAS,OACvBD,EAAQna,MAAM+C,QAAU,QACxBoX,EAAQna,MAAMiE,cAAgB,SAEtBE,EAAUkO,mBACd,KAAKrR,EAAW2N,oBAAoBE,QAChCsL,EAAQna,MAAM/F,KAAO,OACrBkgB,EAAQna,MAAMqa,MAAQ5f,EAAKkI,cAAcD,YAAY4X,WAAaxa,EAAUlF,OAAOX,KAAO6F,EAAUE,MAAMyE,OAAO,GAAGrH,iBAAiB0C,EAAUlF,OAAOT,OAxC/I,EAwCuK,KAC9KggB,EAAQna,MAAMua,UAAY,QAC1B,MACJ,KAAKvZ,EAAW2N,oBAAoBC,OAChCuL,EAAQna,MAAM/F,KAAO6F,EAAUlF,OAAOX,KAAO6F,EAAUE,MAAMyE,OAAO,GAAGrH,iBAAiB0C,EAAUlF,OAAOT,OAAS,KAClHggB,EAAQna,MAAMqa,MAAQ,OACtBF,EAAQna,MAAMua,UAAY,OAIlC,IAAI/H,OAAO,EACPgI,EAAa1a,EAAUE,MAAMyE,OAAO,GAAGrH,iBAAiB0C,EAAUlF,OAAOT,OACzEsgB,EAAatW,EAAUgO,eAC3B,GAAIsI,EACA,GAA0B,QAAtBA,EAAW7N,OAAkB,CAC7B,IAAIhG,EAAQnM,EAAKkI,cAAcgR,cAAc,OAC7C/M,EAAMgC,IAAM6R,EAAW5N,KAAK,GAC5BsN,EAAQna,MAAM9F,IAAM4F,EAAUlF,OAAOV,IAAMsgB,EAAa,KACxDL,EAAQna,MAAM7F,MAAQ,OACtBggB,EAAQna,MAAM5F,OAAS,OACvB+f,EAAQtG,YAAYjN,OACjB,CACH,IAAIsE,EAAmD,GAA5CzL,WAAWK,EAAUE,MAAMC,KAAKC,UAC3Cia,EAAQna,MAAM9F,IAAM4F,EAAUlF,OAAOV,IAAMsgB,EAAa1a,EAAUlF,OAAOR,OAAS,IAAM8Q,EAAO,KAC/FiP,EAAQna,MAAM7F,MAAQ+Q,EAAO,KAC7BiP,EAAQna,MAAM5F,OAAS8Q,EAAO,KAC9BiP,EAAQna,MAAMoL,gBAAkBpL,EAAMmS,mBAEJ,iBAAxBrS,EAAU0G,YACxBgM,EAAO/X,EAAKkI,cAAc+X,eAAed,EAAkB9Z,EAAU0G,UAAWrC,EAAUiO,eAAe,IACzG+H,EAAQtG,YAAYrB,GACpB2H,EAAQna,MAAM9F,IAAM4F,EAAUlF,OAAOV,IAAMsgB,EAAa,MAI5D,IAAInf,EAAOZ,EAAKkI,cAActH,KAC9BA,EAAKwY,YAAYsG,GAEb3H,GACA1S,EAAUwC,WAAW3V,KAAKotB,EAAgBvqB,QAAQmrB,aAAanI,EAAM1S,IACrEzE,EAAK4Y,YAAYkG,IAGjBra,EAAUwC,WAAW3V,KAAK,IAAImtB,EAAgBtqB,QAAQ2qB,EAASra,EAAWoC,EAAgB,MAIhF,CACd0Y,SAAU,CAAC,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GAC9DC,OAAQ,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,OAG3EpL,EAAW,CACXmL,SAAU,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC1KC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAGxLzK,EAAS,CACTwK,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC5KC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG5M5K,EAAW,CACX2K,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACjLC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG7LC,EAAwB,SAA+BpvB,EAAO8C,EAAKgN,EAAKuf,EAASC,EAAUC,GAC3F,OAAIvvB,EAAQ8C,GAAO9C,EAAQ8P,EAChBoe,EAAkBluB,EAAOsvB,EAAUC,EAAOruB,OAAS,GAGvDmuB,EAAQH,SAASM,QAAO,SAAUC,EAAQC,EAASjZ,GACtD,KAAOzW,GAAS0vB,GACZ1vB,GAAS0vB,EACTD,GAAUJ,EAAQF,OAAO1Y,GAE7B,OAAOgZ,IACR,IAAMF,GAGTI,EAAuC,SAA8C3vB,EAAO4vB,EAAsBC,EAAWC,GAC7H,IAAIL,EAAS,GAEb,GACSI,GACD7vB,IAEJyvB,EAASK,EAAS9vB,GAASyvB,EAC3BzvB,GAAS4vB,QACJ5vB,EAAQ4vB,GAAwBA,GAEzC,OAAOH,GAGPM,EAA8B,SAAqC/vB,EAAOgwB,EAAqBC,EAAmBJ,EAAWN,GAC7H,IAAIK,EAAuBK,EAAoBD,EAAsB,EAErE,OAAQhwB,EAAQ,EAAI,IAAM,KAAO2vB,EAAqC9sB,KAAKqtB,IAAIlwB,GAAQ4vB,EAAsBC,GAAW,SAAUhD,GAC9H,OAAO,EAAIyB,EAAS9B,eAAe3pB,KAAKstB,MAAMtD,EAAY+C,GAAwBI,MACjFT,IAGLa,EAAgC,SAAuCpwB,EAAOqvB,GAC9E,IAAIE,EAAS7C,UAAUxrB,OAAS,QAAsBN,IAAjB8rB,UAAU,GAAmBA,UAAU,GAAK,KAE7EkD,EAAuBP,EAAQnuB,OACnC,OAAOyuB,EAAqC9sB,KAAKqtB,IAAIlwB,GAAQ4vB,GAAsB,GAAO,SAAU/C,GAChG,OAAOwC,EAAQxsB,KAAKstB,MAAMtD,EAAY+C,OACrCL,GAQLc,EAAmB,SAA0BrwB,EAAOswB,EAASC,EAAaC,EAAcjB,EAAQkB,GAChG,GAAIzwB,GAAS,MAAQA,EAAQ,KACzB,OAAOkuB,EAAkBluB,EAAOsV,EAAW0N,gBAAgBO,YAAagM,EAAOruB,OAAS,GAE5F,IAAIwvB,EAAM7tB,KAAKqtB,IAAIlwB,GACfyvB,EAASF,EAEb,GAAY,IAARmB,EACA,OAAOJ,EAAQ,GAAKb,EAGxB,IAAK,IAAIkB,EAAQ,EAAGD,EAAM,GAAKC,GAAS,EAAGA,IAAS,CAChD,IAAIC,EAAcF,EAAM,GAEJ,IAAhBE,IAAqB,EAAI/b,EAAM+G,UAAU6U,EAnBrC,IAmBqE,KAAXhB,EAC9DA,EAASa,EAAQM,GAAenB,EACzBmB,EAAc,GAAqB,IAAhBA,GAA+B,IAAVD,GAA+B,IAAhBC,GAA+B,IAAVD,IAAe,EAAI9b,EAAM+G,UAAU6U,EApBvG,IAoBuJ,IAAhBG,GAA+B,IAAVD,IAAe,EAAI9b,EAAM+G,UAAU6U,EAnB1L,IAmB+NzwB,EAAQ,KAAuB,IAAhB4wB,GAAqBD,EAAQ,IAAK,EAAI9b,EAAM+G,UAAU6U,EAlBrS,GAmBnBhB,EAASa,EAAQM,IAAgBD,EAAQ,EAAIJ,EAAYI,EAAQ,GAAK,IAAMlB,EACrD,IAAhBmB,GAAqBD,EAAQ,IACpClB,EAASc,EAAYI,EAAQ,GAAKlB,GAEtCiB,EAAM7tB,KAAKstB,MAAMO,EAAM,IAG3B,OAAQ1wB,EAAQ,EAAIwwB,EAAe,IAAMf,GAQzCvB,EAAoBnuB,EAAQmuB,kBAAoB,SAA2BluB,EAAO4T,EAAMid,GACxF,IAAIC,EAAgBD,EAAe,KAAO,GACtCE,EAAYF,EAAe,IAAM,GACjCG,EAAeH,EAAe,KAAO,GACzC,OAAQjd,GACJ,KAAK0B,EAAW0N,gBAAgBI,KAC5B,MAAO,IACX,KAAK9N,EAAW0N,gBAAgBJ,OAC5B,MAAO,IACX,KAAKtN,EAAW0N,gBAAgBK,OAC5B,MAAO,IACX,KAAK/N,EAAW0N,gBAAgBQ,qBAC5B,IAAIiM,EAASM,EAA4B/vB,EAAO,GAAI,IAAI,EAAM8wB,GAC9D,OAAOrB,EAAOvuB,OAAS,EAAI,IAAMuuB,EAASA,EAC9C,KAAKna,EAAW0N,gBAAgBO,YAC5B,OAAO6M,EAA8BpwB,EAAO,aAAc+wB,GAC9D,KAAKzb,EAAW0N,gBAAgBS,YAC5B,OAAO2L,EAAsBpvB,EAAO,EAAG,KAAMwuB,EAAalZ,EAAW0N,gBAAgBM,QAASwN,GAAextB,cACjH,KAAKgS,EAAW0N,gBAAgBU,YAC5B,OAAO0L,EAAsBpvB,EAAO,EAAG,KAAMwuB,EAAalZ,EAAW0N,gBAAgBM,QAASwN,GAClG,KAAKxb,EAAW0N,gBAAgBW,YAC5B,OAAOoM,EAA4B/vB,EAAO,IAAK,KAAK,EAAO8wB,GAC/D,KAAKxb,EAAW0N,gBAAgBY,YAC5B,OAAOmM,EAA4B/vB,EAAO,GAAI,KAAK,EAAO8wB,GAC9D,KAAKxb,EAAW0N,gBAAgBa,YAC5B,OAAOkM,EAA4B/vB,EAAO,GAAI,IAAI,EAAO8wB,GAC7D,KAAKxb,EAAW0N,gBAAgBc,aAC5B,OAAOiM,EAA4B/vB,EAAO,KAAM,MAAM,EAAM8wB,GAChE,KAAKxb,EAAW0N,gBAAgBe,SAChC,KAAKzO,EAAW0N,gBAAgBqD,eAC5B,OAAO+I,EAAsBpvB,EAAO,EAAG,KAAM+jB,EAAUzO,EAAW0N,gBAAgBM,QAASwN,GAC/F,KAAKxb,EAAW0N,gBAAgBuC,eAC5B,OAAO6J,EAAsBpvB,EAAO,EAAG,KAAM+jB,EAAUzO,EAAW0N,gBAAgBM,QAASwN,GAAextB,cAC9G,KAAKgS,EAAW0N,gBAAgBgB,QAC5B,OAAO+L,EAA4B/vB,EAAO,KAAM,MAAM,EAAM8wB,GAChE,KAAKxb,EAAW0N,gBAAgBiB,UAChC,KAAK3O,EAAW0N,gBAAgBkC,MAC5B,OAAO6K,EAA4B/vB,EAAO,KAAM,MAAM,EAAM8wB,GAChE,KAAKxb,EAAW0N,gBAAgBkB,mBAC5B,OAAOkM,EAA8BpwB,EAAO,eAAgB+wB,GAChE,KAAKzb,EAAW0N,gBAAgBmB,kBAC5B,OAAOiM,EAA8BpwB,EAAO,aAAc+wB,GAC9D,KAAKzb,EAAW0N,gBAAgBoB,gBAChC,KAAK9O,EAAW0N,gBAAgBoD,sBAC5B,OAAOiK,EAAiBrwB,EAAO,aAjDR,OAiDoD,IAAK+wB,EAAWE,IAC/F,KAAK3b,EAAW0N,gBAAgBmD,oBAC5B,OAAOkK,EAAiBrwB,EAAO,aAlDV,OAkDoD,IAAK+wB,EAAWG,IAC7F,KAAK5b,EAAW0N,gBAAgB8C,sBAC5B,OAAOuK,EAAiBrwB,EAAO,aArDR,OAqDoD,IAAK+wB,EAAWE,IAC/F,KAAK3b,EAAW0N,gBAAgB6C,oBAC5B,OAAOwK,EAAiBrwB,EAAO,aAtDV,OAsDoD,IAAK+wB,EAAWG,IAC7F,KAAK5b,EAAW0N,gBAAgB8B,kBAC5B,OAAOuL,EAAiBrwB,EAAO,aAAc,OAvDjC,OAuD4D+wB,EAAW,GACvF,KAAKzb,EAAW0N,gBAAgB6B,gBAC5B,OAAOwL,EAAiBrwB,EAAO,aAAc,OAzDjC,OAyD4D+wB,EAAWG,GACvF,KAAK5b,EAAW0N,gBAAgBmC,qBAC5B,OAAOkL,EAAiBrwB,EAAO,aAAc,OA1DnC,QA0D4DgxB,EAAcE,GACxF,KAAK5b,EAAW0N,gBAAgBqC,sBAC5B,OAAOgL,EAAiBrwB,EAAO,aAAc,OA5DnC,QA4D4DgxB,EAAc,GACxF,KAAK1b,EAAW0N,gBAAgBoC,oBAC5B,OAAOiL,EAAiBrwB,EAAO,aAAc,MA9DnC,QA8D2DgxB,EAAcE,GACvF,KAAK5b,EAAW0N,gBAAgBqB,WAC5B,OAAO0L,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBuB,SAC5B,OAAO6K,EAAsBpvB,EAAO,EAAG,MAAOukB,EAAUjP,EAAW0N,gBAAgBM,QAASwN,GAChG,KAAKxb,EAAW0N,gBAAgBwB,SAC5B,OAAOuL,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgByB,SAC5B,OAAOsL,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgB0B,OAC5B,OAAO0K,EAAsBpvB,EAAO,EAAG,MAAO0kB,EAAQpP,EAAW0N,gBAAgBM,QAASwN,GAC9F,KAAKxb,EAAW0N,gBAAgB2B,SAC5B,OAAOyL,EAA8BpwB,EAAO,oDAChD,KAAKsV,EAAW0N,gBAAgB4B,eAC5B,OAAOwL,EAA8BpwB,EAAO,mDAChD,KAAKsV,EAAW0N,gBAAgB+B,QAC5B,OAAOgL,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBgC,SAC5B,OAAOoL,EAA8BpwB,EAAO,mDAAoD+wB,GACpG,KAAKzb,EAAW0N,gBAAgBiC,eAC5B,OAAOmL,EAA8BpwB,EAAO,kDAAmD+wB,GACnG,KAAKzb,EAAW0N,gBAAgBsC,IAC5B,OAAOyK,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgByC,UAC5B,OAAOsK,EAA4B/vB,EAAO,KAAQ,MAAQ,EAAM8wB,GACpE,KAAKxb,EAAW0N,gBAAgB0C,QAC5B,OAAOqK,EAA4B/vB,EAAO,KAAQ,MAAQ,EAAM8wB,GACpE,KAAKxb,EAAW0N,gBAAgB2C,MAC5B,OAAOoK,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgB4C,QAC5B,OAAOmK,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgB+C,MAC5B,OAAOgK,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBgD,OAC5B,OAAO+J,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBiD,KAC5B,OAAO8J,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBkD,QAC5B,OAAO6J,EAA4B/vB,EAAO,KAAO,MAAO,EAAM8wB,GAClE,KAAKxb,EAAW0N,gBAAgBM,QAChC,QACI,OAAOyM,EAA4B/vB,EAAO,GAAI,IAAI,EAAM8wB,M,kCCxTpEjxB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIsB,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf8gB,EAAQ,EAAQ,MAEhBjN,EAAkB,EAAQ,MAI9B,IAAIub,EAAgB,SAAuBC,EAAUC,GACjD,IAAIC,EAAUzuB,KAAKiN,IAAIyC,MAAM,KAAM6e,EAASG,WAAWtQ,KAAI,SAAUuQ,GACjE,OAAOA,EAAUC,SAEjBC,EAAI,EAAI7uB,KAAKiN,IAAI,EAAGwhB,GACxBF,EAASG,WAAWjP,SAAQ,SAAUkP,GAClCH,EAAeM,aAAaD,EAAIF,EAAUC,KAAMD,EAAUtZ,MAAM0Z,gBAIpEC,EAAiB,WACjB,SAASA,EAAe1U,IAb5B,SAAyB3a,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAcxGoB,CAAgBC,KAAMmvB,GAEtBnvB,KAAKya,OAASA,GAAkBzN,SAASuY,cAAc,UACvDvlB,KAAKovB,eAAiB3U,EAgO1B,OA7NA7b,EAAauwB,EAAgB,CAAC,CAC1B/vB,IAAK,SACL9B,MAAO,SAAgB+xB,GACnBrvB,KAAKgmB,IAAMhmB,KAAKya,OAAOwL,WAAW,MAClCjmB,KAAKqvB,QAAUA,EACVrvB,KAAKovB,eACNpvB,KAAKya,OAAO1O,MAAQ5L,KAAKstB,MAAM4B,EAAQtjB,MAAQsjB,EAAQC,OACvDtvB,KAAKya,OAAOzO,OAAS7L,KAAKstB,MAAM4B,EAAQrjB,OAASqjB,EAAQC,OACzDtvB,KAAKya,OAAO7I,MAAM7F,MAAQsjB,EAAQtjB,MAAQ,KAC1C/L,KAAKya,OAAO7I,MAAM5F,OAASqjB,EAAQrjB,OAAS,MAGhDhM,KAAKgmB,IAAIsJ,MAAMtvB,KAAKqvB,QAAQC,MAAOtvB,KAAKqvB,QAAQC,OAChDtvB,KAAKgmB,IAAIuJ,WAAWF,EAAQ5jB,GAAI4jB,EAAQ3jB,GACxC1L,KAAKgmB,IAAIwJ,aAAe,SACxBH,EAAQI,OAAOC,IAAI,gCAAkCL,EAAQtjB,MAAQ,IAAMsjB,EAAQrjB,OAAS,OAASqjB,EAAQ5jB,EAAI,IAAM4jB,EAAQ3jB,EAAI,gBAAkB1L,KAAKqvB,QAAQC,SAEvK,CACClwB,IAAK,OACL9B,MAAO,SAAcqyB,EAAWC,GAC5B,IAAI5b,EAAQhU,KAER2vB,EAAUnxB,SACVwB,KAAKgmB,IAAI6J,OACTF,EAAU/P,SAAQ,SAAUnR,GACxBuF,EAAMvF,KAAKA,GACXuF,EAAMgS,IAAI5I,WAIlBwS,IAEID,EAAUnxB,QACVwB,KAAKgmB,IAAI8J,YAGlB,CACC1wB,IAAK,YACL9B,MAAO,SAAmBkb,EAAOwG,EAAQ+Q,GACrC/vB,KAAKgmB,IAAIE,UAAU1N,EAAOwG,EAAOnT,KAAMmT,EAAOlT,IAAKkT,EAAOjT,MAAOiT,EAAOhT,OAAQ+jB,EAAYlkB,KAAMkkB,EAAYjkB,IAAKikB,EAAYhkB,MAAOgkB,EAAY/jB,UAEvJ,CACC5M,IAAK,YACL9B,MAAO,SAAmBmR,EAAM+G,GAC5BxV,KAAKyO,KAAKA,GACVzO,KAAKgmB,IAAIiB,UAAYzR,EAAM0Z,WAC3BlvB,KAAKgmB,IAAIgK,SAEd,CACC5wB,IAAK,OACL9B,MAAO,SAAckY,GACjBxV,KAAKgmB,IAAIiB,UAAYzR,EAAM0Z,WAC3BlvB,KAAKgmB,IAAIgK,SAEd,CACC5wB,IAAK,YACL9B,MAAO,WACH,OAAOipB,QAAQC,QAAQxmB,KAAKya,UAEjC,CACCrb,IAAK,OACL9B,MAAO,SAAc2yB,GACjB,IAAIC,EAASlwB,KAEbA,KAAKgmB,IAAImK,YACLzyB,MAAMC,QAAQsyB,GACdA,EAAMrQ,SAAQ,SAAUwQ,EAAOrc,GAC3B,IAAIK,EAAQgc,EAAMlf,OAASiP,EAAMJ,KAAKC,OAASoQ,EAAQA,EAAMhc,MAC/C,IAAVL,EACAmc,EAAOlK,IAAIqK,OAAOjc,EAAM3I,EAAG2I,EAAM1I,GAEjCwkB,EAAOlK,IAAIsK,OAAOlc,EAAM3I,EAAG2I,EAAM1I,GAGjC0kB,EAAMlf,OAASiP,EAAMJ,KAAKE,cAC1BiQ,EAAOlK,IAAIuK,cAAcH,EAAMI,aAAa/kB,EAAG2kB,EAAMI,aAAa9kB,EAAG0kB,EAAMK,WAAWhlB,EAAG2kB,EAAMK,WAAW/kB,EAAG0kB,EAAMM,IAAIjlB,EAAG2kB,EAAMM,IAAIhlB,MAI5I1L,KAAKgmB,IAAI2K,IAAIV,EAAMxkB,EAAIwkB,EAAMW,OAAQX,EAAMvkB,EAAIukB,EAAMW,OAAQX,EAAMW,OAAQ,EAAa,EAAVzwB,KAAK0wB,IAAQ,GAG/F7wB,KAAKgmB,IAAI8K,cAEd,CACC1xB,IAAK,YACL9B,MAAO,SAAmBmO,EAAGC,EAAGK,EAAOC,EAAQwJ,GAC3CxV,KAAKgmB,IAAIiB,UAAYzR,EAAM0Z,WAC3BlvB,KAAKgmB,IAAIkB,SAASzb,EAAGC,EAAGK,EAAOC,KAEpC,CACC5M,IAAK,uBACL9B,MAAO,SAA8BkP,EAAQkiB,GACzC,IAAIqC,EAAiB/wB,KAAKgmB,IAAIgL,qBAAqBxkB,EAAOX,KAAO6iB,EAASuC,UAAUC,GAAI1kB,EAAOV,IAAM4iB,EAASuC,UAAUE,GAAI3kB,EAAOX,KAAO6iB,EAASuC,UAAUG,GAAI5kB,EAAOV,IAAM4iB,EAASuC,UAAUI,IAEjM5C,EAAcC,EAAUqC,GACxB/wB,KAAKgmB,IAAIiB,UAAY8J,EACrB/wB,KAAKgmB,IAAIkB,SAAS1a,EAAOX,KAAMW,EAAOV,IAAKU,EAAOT,MAAOS,EAAOR,UAErE,CACC5M,IAAK,uBACL9B,MAAO,SAA8BkP,EAAQkiB,GACzC,IAAI4C,EAAStxB,KAETyL,EAAIe,EAAOX,KAAO6iB,EAAS6C,OAAO9lB,EAClCC,EAAIc,EAAOV,IAAM4iB,EAAS6C,OAAO7lB,EAEjC8lB,EAAiBxxB,KAAKgmB,IAAIyL,qBAAqBhmB,EAAGC,EAAG,EAAGD,EAAGC,EAAGgjB,EAASkC,OAAOnlB,GAClF,GAAK+lB,EAOL,GAHA/C,EAAcC,EAAU8C,GACxBxxB,KAAKgmB,IAAIiB,UAAYuK,EAEjB9C,EAASkC,OAAOnlB,IAAMijB,EAASkC,OAAOllB,EAAG,CAEzC,IAAIgmB,EAAOllB,EAAOX,KAAO,GAAMW,EAAOT,MAClC4lB,EAAOnlB,EAAOV,IAAM,GAAMU,EAAOR,OACjCgjB,EAAIN,EAASkC,OAAOllB,EAAIgjB,EAASkC,OAAOnlB,EACxCmmB,EAAO,EAAI5C,EAEfhvB,KAAKuX,UAAUma,EAAMC,EAAM,CAAC,EAAG,EAAG,EAAG3C,EAAG,EAAG,IAAI,WAC3C,OAAOsC,EAAOtL,IAAIkB,SAAS1a,EAAOX,KAAM+lB,GAAQplB,EAAOV,IAAM6lB,GAAQA,EAAMnlB,EAAOT,MAAOS,EAAOR,OAAS4lB,WAG7G5xB,KAAKgmB,IAAIkB,SAAS1a,EAAOX,KAAMW,EAAOV,IAAKU,EAAOT,MAAOS,EAAOR,UAGzE,CACC5M,IAAK,eACL9B,MAAO,SAAsBmR,EAAM+J,EAAOqZ,EAAWC,EAASC,GAC1D/xB,KAAKyO,KAAKA,GACVzO,KAAKgmB,IAAIiB,UAAYjnB,KAAKgmB,IAAIgM,cAAchyB,KAAKiyB,YAAYzZ,EAAOqZ,GAAY,UAChF7xB,KAAKgmB,IAAIuJ,UAAUuC,EAASC,GAC5B/xB,KAAKgmB,IAAIgK,OACThwB,KAAKgmB,IAAIuJ,WAAWuC,GAAUC,KAEnC,CACC3yB,IAAK,iBACL9B,MAAO,SAAwB40B,EAAY1c,EAAO3D,EAAMoF,EAAgBkb,GACpE,IAAIC,EAASpyB,KAEbA,KAAKgmB,IAAInU,KAAO,CAACA,EAAKwgB,UAAWxgB,EAAKygB,YAAazgB,EAAK0gB,WAAY1gB,EAAKC,SAAUD,EAAK2gB,YAAYC,KAAK,KAEzGP,EAAWtS,SAAQ,SAAUwE,GAezB,GAdAgO,EAAOpM,IAAIiB,UAAYzR,EAAM0Z,WACzBiD,GAAe/N,EAAKA,KAAK/F,OAAO7f,OAChC2zB,EAAYhH,MAAM,GAAGxc,UAAUiR,SAAQ,SAAUzI,GAC7Cib,EAAOpM,IAAI0M,YAAcvb,EAAW3B,MAAM0Z,WAC1CkD,EAAOpM,IAAI2M,cAAgBxb,EAAW2a,QAAUM,EAAO/C,QAAQC,MAC/D8C,EAAOpM,IAAI4M,cAAgBzb,EAAW4a,QAAUK,EAAO/C,QAAQC,MAC/D8C,EAAOpM,IAAI6M,WAAa1b,EAAW2b,KAEnCV,EAAOpM,IAAI+M,SAAS3O,EAAKA,KAAMA,EAAK5X,OAAOX,KAAMuY,EAAK5X,OAAOV,IAAMsY,EAAK5X,OAAOR,WAGnFomB,EAAOpM,IAAI+M,SAAS3O,EAAKA,KAAMA,EAAK5X,OAAOX,KAAMuY,EAAK5X,OAAOV,IAAMsY,EAAK5X,OAAOR,QAG5D,OAAnBiL,EAAyB,CACzB,IAAI6R,EAAsB7R,EAAe6R,qBAAuBtT,EAChEyB,EAAe4R,mBAAmBjJ,SAAQ,SAAUiJ,GAChD,OAAQA,GACJ,KAAK3V,EAAgB6U,qBAAqBQ,UAItC,IACIyK,EADwBZ,EAAO/C,QAAQ4D,YAAYC,WAAWrhB,GAC7BmhB,SAErCZ,EAAOe,UAAU/O,EAAK5X,OAAOX,KAAM1L,KAAK2d,MAAMsG,EAAK5X,OAAOV,IAAMknB,GAAW5O,EAAK5X,OAAOT,MAAO,EAAG+c,GACjG,MACJ,KAAK5V,EAAgB6U,qBAAqBS,SACtC4J,EAAOe,UAAU/O,EAAK5X,OAAOX,KAAM1L,KAAK2d,MAAMsG,EAAK5X,OAAOV,KAAMsY,EAAK5X,OAAOT,MAAO,EAAG+c,GACtF,MACJ,KAAK5V,EAAgB6U,qBAAqBU,aAEtC,IACI2K,EADyBhB,EAAO/C,QAAQ4D,YAAYC,WAAWrhB,GAC/BuhB,OAEpChB,EAAOe,UAAU/O,EAAK5X,OAAOX,KAAM1L,KAAKkzB,KAAKjP,EAAK5X,OAAOV,IAAMsnB,GAAShP,EAAK5X,OAAOT,MAAO,EAAG+c,aAOvH,CACC1pB,IAAK,cACL9B,MAAO,SAAqBkb,EAAOsE,GAC/B,GAAItE,EAAMzM,QAAU+Q,EAAK/Q,OAASyM,EAAMxM,SAAW8Q,EAAK9Q,OACpD,OAAOwM,EAGX,IAAIiC,EAASza,KAAKya,OAAOlG,cAAcgR,cAAc,UAKrD,OAJA9K,EAAO1O,MAAQ+Q,EAAK/Q,MACpB0O,EAAOzO,OAAS8Q,EAAK9Q,OACXyO,EAAOwL,WAAW,MACxBC,UAAU1N,EAAO,EAAG,EAAGA,EAAMzM,MAAOyM,EAAMxM,OAAQ,EAAG,EAAG8Q,EAAK/Q,MAAO+Q,EAAK9Q,QACtEyO,IAEZ,CACCrb,IAAK,aACL9B,MAAO,SAAoBiZ,GACvBvW,KAAKgmB,IAAIsN,YAAc/c,IAE5B,CACCnX,IAAK,YACL9B,MAAO,SAAmBw0B,EAASC,EAASwB,EAAQ3D,GAChD5vB,KAAKgmB,IAAI6J,OACT7vB,KAAKgmB,IAAIuJ,UAAUuC,EAASC,GAC5B/xB,KAAKgmB,IAAIzO,UAAUgc,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IACjFvzB,KAAKgmB,IAAIuJ,WAAWuC,GAAUC,GAE9BnC,IAEA5vB,KAAKgmB,IAAI8J,cAIVX,EArOU,GAwOrB9xB,EAAQ+D,QAAU+tB,G,kCC9PlBhyB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIsB,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAInB,IAAIm0B,EAAS,WACT,SAASA,EAAOC,EAASC,EAAItf,IAHjC,SAAyBtU,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAIxGoB,CAAgBC,KAAMwzB,GAEtBxzB,KAAKyzB,QAA4B,oBAAX1M,QAA0B0M,EAChDzzB,KAAKoU,MAAQA,GAAgBuf,KAAKC,MAClC5zB,KAAK0zB,GAAKA,EAsCd,OAnCA90B,EAAa40B,EAAQ,CAAC,CAClBp0B,IAAK,QACL9B,MAAO,SAAeo2B,GAClB,OAAO,IAAIF,EAAOxzB,KAAKyzB,QAASC,EAAI1zB,KAAKoU,SAK9C,CACChV,IAAK,MACL9B,MAAO,WACH,GAAI0C,KAAKyzB,SAAW1M,OAAO8M,SAAW9M,OAAO8M,QAAQnE,IAAK,CACtD,IAAK,IAAIoE,EAAO9J,UAAUxrB,OAAQigB,EAAO/gB,MAAMo2B,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IACzEtV,EAAKsV,GAAQ/J,UAAU+J,GAG3BC,SAASx0B,UAAUy0B,KAAKC,KAAKnN,OAAO8M,QAAQnE,IAAK3I,OAAO8M,SAAShkB,MAAMkX,OAAO8M,QAAS,CAACF,KAAKC,MAAQ5zB,KAAKoU,MAAQ,KAAMpU,KAAK0zB,GAAK,gBAAkB1zB,KAAK0zB,GAAK,KAAO,gBAAgB5a,OAAO,GAAGqS,MAAM+I,KAAKzV,EAAM,QAMzN,CACCrf,IAAK,QACL9B,MAAO,WACH,GAAI0C,KAAKyzB,SAAW1M,OAAO8M,SAAW9M,OAAO8M,QAAQM,MAAO,CACxD,IAAK,IAAIC,EAAQpK,UAAUxrB,OAAQigB,EAAO/gB,MAAM02B,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC9E5V,EAAK4V,GAASrK,UAAUqK,GAG5BL,SAASx0B,UAAUy0B,KAAKC,KAAKnN,OAAO8M,QAAQM,MAAOpN,OAAO8M,SAAShkB,MAAMkX,OAAO8M,QAAS,CAACF,KAAKC,MAAQ5zB,KAAKoU,MAAQ,KAAMpU,KAAK0zB,GAAK,gBAAkB1zB,KAAK0zB,GAAK,KAAO,gBAAgB5a,OAAO,GAAGqS,MAAM+I,KAAKzV,EAAM,UAKvN+U,EA5CE,GA+Cbn2B,EAAQ+D,QAAUoyB,G,kCCvDlBr2B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ2Z,aAAe3Z,EAAQkgB,mBAAgBrf,EAE/C,IAIgCqN,EAJ5B+oB,EAAU,EAAQ,MAElBtY,GAE4BzQ,EAFM+oB,IAEe/oB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEnElO,EAAQkgB,cAAgB,CACxCG,IAAK,EACLD,MAAO,EACPE,OAAQ,EACRH,KAAM,GAJV,IAOI2L,EAAQ,CAAC,MAAO,QAAS,SAAU,QAEpB9rB,EAAQ2Z,aAAe,SAAsBpF,GAC5D,OAAOuX,EAAM5K,KAAI,SAAU8K,GACvB,OAAO,IAAIrN,EAAS5a,QAAQwQ,EAAMuJ,iBAAiB,WAAakO,S,kCCtBxElsB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIi3B,EAAgBl3B,EAAQk3B,cAAgB,CACxCC,OAAQ,EACRC,WAAY,GAGQp3B,EAAQyZ,kBAAoB,SAA2BN,GAC3E,OAAQA,GACJ,IAAK,aACD,OAAO+d,EAAcE,WACzB,IAAK,SACL,QACI,OAAOF,EAAcC,U,kCCdjCr3B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIya,EAAW1a,EAAQ0a,SAAW,CAC9BC,OAAQ,EACRqB,SAAU,EACVqb,SAAU,EACVC,MAAO,EACPC,OAAQ,GAGQv3B,EAAQyX,cAAgB,SAAuBzE,GAC/D,OAAQA,GACJ,IAAK,WACD,OAAO0H,EAASsB,SACpB,IAAK,WACD,OAAOtB,EAAS2c,SACpB,IAAK,QACD,OAAO3c,EAAS4c,MACpB,IAAK,SACD,OAAO5c,EAAS6c,OAGxB,OAAO7c,EAASC,S,kCCvBpB7a,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIknB,EAAiBnnB,EAAQmnB,eAAiB,CAC1CrL,KAAM,EACNsL,UAAW,EACXG,UAAW,EACXL,WAAY,GAGSlnB,EAAQia,mBAAqB,SAA4BD,GAC9E,OAAQA,GACJ,IAAK,YACD,OAAOmN,EAAeI,UAC1B,IAAK,YACD,OAAOJ,EAAeC,UAC1B,IAAK,aACD,OAAOD,EAAeD,WAG9B,OAAOC,EAAerL,O,kCCpB1Bhc,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQqb,oBAAsBrb,EAAQw3B,oBAAsBx3B,EAAQy3B,sBAAwBz3B,EAAQ03B,mBAAqB13B,EAAQiY,qBAAuBjY,EAAQ2X,iBAAmB3X,EAAQ8X,cAAgB9X,EAAQoY,iBAAcvX,EAEjO,IAEIytB,EAAkBtgB,EAFD,EAAQ,OAIzB+G,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAIlB2iB,EAAW3pB,EAFD,EAAQ,OAMlBD,EAAWC,EAFD,EAAQ,OAMlB6G,EAAU7G,EAFD,EAAQ,OAMjB2Q,EAAW3Q,EAFD,EAAQ,OAQlB8G,GAJU,EAAQ,MAEJ,EAAQ,MAEd,EAAQ,OAEpB,SAAS9G,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAErElO,EAAQoY,YAAc,IAAIvD,EAAQ9Q,QAAQ,CAAC,GAAI,GAAI,KAArE,IACI6zB,EAAqB,IAAI/iB,EAAQ9Q,QAAQ,CAAC,IAAK,IAAK,MACpD8zB,EAAyB,IAAIhjB,EAAQ9Q,QAAQ,CAAC,IAAK,IAAK,MACxD+zB,EAAe,CACfzoB,YAAa,EACb4c,YAAa2L,EACb1L,YAAalX,EAAQ6W,aAAahB,OAUlCkN,GARgB/3B,EAAQ8X,cAAgB,CAACggB,EAAcA,EAAcA,EAAcA,GAChE93B,EAAQ2X,iBAAmB,CAC9C+I,gBAAiBmX,EACjBlY,gBAAiB,GACjBiB,eAAgB7L,EAAYyJ,gBAAgBc,YAC5CU,iBAAkBjL,EAAYwJ,kBAAkBe,aAG1B,IAAIX,EAAS5a,QAAQ,QAC3Ci0B,EAA4B,CAACD,EAAqBA,GAClDE,EAA4B,CAACD,EAA2BA,EAA2BA,EAA2BA,GAE9GE,EAAyB,IAAIvZ,EAAS5a,QAAQ,OAC9Co0B,EAA+B,CAACD,EAAwBA,GACxDE,EAA+B,CAACD,EAA8BA,EAA8BA,EAA8BA,GAqC1HE,GAnCuBr4B,EAAQiY,qBAAuB,SAA8BjJ,GACpF,MAAqB,UAAdA,EAAK6E,KAAmBokB,EAA4BG,GAGtCp4B,EAAQ03B,mBAAqB,SAA4B1oB,EAAMqF,GACpF,GAAkB,UAAdrF,EAAK6E,MAAkC,aAAd7E,EAAK6E,MAC9B,GAAI7E,EAAKspB,QAAS,CACd,IAAI7Y,EAAO3c,KAAKC,IAAIsR,EAAUlF,OAAOT,MAAO2F,EAAUlF,OAAOR,QAC7D0F,EAAUwC,WAAW3V,KAAmB,aAAd8N,EAAK6E,KAAsB,CAAC,IAAI9F,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,OAAPiR,EAAgBpL,EAAUlF,OAAOV,IAAa,IAAPgR,GAAc,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,IAAPiR,EAAapL,EAAUlF,OAAOV,IAAa,MAAPgR,GAAgB,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,OAAPiR,EAAgBpL,EAAUlF,OAAOV,IAAa,OAAPgR,GAAiB,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,OAAPiR,EAAgBpL,EAAUlF,OAAOV,IAAa,MAAPgR,GAAgB,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,OAAPiR,EAAgBpL,EAAUlF,OAAOV,IAAa,IAAPgR,GAAc,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,IAAPiR,EAAapL,EAAUlF,OAAOV,IAAa,OAAPgR,GAAiB,IAAI1R,EAAShK,QAAQsQ,EAAUlF,OAAOX,KAAc,OAAPiR,EAAgBpL,EAAUlF,OAAOV,IAAa,IAAPgR,IAAgB,IAAIkY,EAAS5zB,QAAQsQ,EAAUlF,OAAOX,KAAOiR,EAAO,EAAGpL,EAAUlF,OAAOV,IAAMgR,EAAO,EAAGA,EAAO,UAG70B4Y,EAAkBE,EAAcvpB,GAAOA,EAAMqF,GAAW,IAIpCrU,EAAQy3B,sBAAwB,SAA+BzoB,EAAMqF,GAC7FgkB,EAAkBrpB,EAAK/O,MAAO+O,EAAMqF,GAAW,IAGzBrU,EAAQw3B,oBAAsB,SAA6BxoB,EAAMqF,GACvF,IAAImkB,EAASxpB,EAAKgjB,QAAQhjB,EAAKypB,eAAiB,GAChDJ,EAAkBG,GAASA,EAAOzR,MAAa,GAAI/X,EAAMqF,GAAW,IAG9CrU,EAAQqb,oBAAsB,SAA6BlM,GAQjF,OAPIA,EAAOT,MAAQS,EAAOR,QACtBQ,EAAOX,OAASW,EAAOT,MAAQS,EAAOR,QAAU,EAChDQ,EAAOT,MAAQS,EAAOR,QACfQ,EAAOT,MAAQS,EAAOR,SAC7BQ,EAAOV,MAAQU,EAAOR,OAASQ,EAAOT,OAAS,EAC/CS,EAAOR,OAASQ,EAAOT,OAEpBS,GAGa,SAA2BlP,EAAO+O,EAAMqF,EAAWqkB,GACvE,IAAI9oB,EAAOZ,EAAKkI,cAActH,KAC9B,GAAI3P,EAAMkB,OAAS,GAAKyO,EAAM,CAC1B,IAAI8e,EAAU1f,EAAKkI,cAAcgR,cAAc,uBAC/C,EAAIpT,EAAM4I,eAAe1O,EAAKkI,cAAcD,YAAYI,iBAAiBrI,EAAM,MAAO0f,GACtFA,EAAQna,MAAMvB,SAAW,WACzB0b,EAAQna,MAAM/F,KAAO6F,EAAUlF,OAAOX,KAAO,KAC7CkgB,EAAQna,MAAM9F,IAAM4F,EAAUlF,OAAOV,IAAM,KACtCiqB,IACDhK,EAAQna,MAAMokB,WAAa,UAE/B,IAAI5R,EAAO/X,EAAKkI,cAAc+X,eAAehvB,GAC7CyuB,EAAQtG,YAAYrB,GACpBnX,EAAKwY,YAAYsG,GACjBra,EAAUwC,WAAW3V,KAAKotB,EAAgBvqB,QAAQmrB,aAAanI,EAAM1S,IACrEzE,EAAK4Y,YAAYkG,MAIrB6J,EAAgB,SAAuBvpB,GACvC,IAAI/O,EAAsB,aAAd+O,EAAK6E,KAAsB,IAAIxT,MAAM2O,EAAK/O,MAAMkB,OAAS,GAAGi0B,KAAK,KAAYpmB,EAAK/O,MAE9F,OAAwB,IAAjBA,EAAMkB,OAAe6N,EAAK4pB,aAAe,GAAK34B,I,kCCtHzDH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQinB,gBAAkBjnB,EAAQ64B,gBAAah4B,EAE/C,IAUgCqN,EAV5BkI,EAAU,EAAQ,MAElBP,EAAkB,EAAQ,MAE1BijB,EAAW,EAAQ,MAEnBC,GAI4B7qB,EAJO4qB,IAIc5qB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAFnFqgB,EAAW,EAAQ,MAMvB,IAAIsK,EAAa74B,EAAQ64B,WAAa,SAASA,EAAW9R,EAAM5X,IAFhE,SAAyB1M,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAG5GoB,CAAgBC,KAAMk2B,GAEtBl2B,KAAKokB,KAAOA,EACZpkB,KAAKwM,OAASA,GAgCd6pB,GA7BkBh5B,EAAQinB,gBAAkB,SAAyBhnB,EAAO0U,EAAQ3F,GAWpF,IAVA,IACIiqB,EADiD,IAA/BtkB,EAAOJ,MAAMiE,eACF,EAAI+V,EAASnC,cAAcnsB,GAAOihB,KAAI,SAAU9gB,GAC7E,OAAO,EAAImuB,EAAS9B,eAAersB,OAClC,EAAImuB,EAAS2K,YAAYj5B,EAAO0U,GACjCxT,EAAS83B,EAAS93B,OAClB8V,EAAcjI,EAAKmqB,WAAanqB,EAAKmqB,WAAWjiB,cAAcD,YAAc,KAC5EpI,EAAUoI,EAAcA,EAAYE,YAAc,EAClDrI,EAAUmI,EAAcA,EAAYG,YAAc,EAClDyd,EAAa,GACbuE,EAAS,EACJh5B,EAAI,EAAGA,EAAIe,EAAQf,IAAK,CAC7B,IAAI2mB,EAAOkS,EAAS74B,GACpB,GAAIuU,EAAOJ,MAAMqF,iBAAmB/D,EAAgB8U,gBAAgB7O,MAAQiL,EAAK/F,OAAO7f,OAAS,EAC7F,GAAI43B,EAAUh1B,QAAQs1B,qBAClBxE,EAAW3zB,KAAK,IAAI23B,EAAW9R,EAAMuS,EAAetqB,EAAMoqB,EAAQrS,EAAK5lB,OAAQ0N,EAASC,SACrF,CACH,IAAIyqB,EAAkBvqB,EAAKwqB,UAAUzS,EAAK5lB,QAC1C0zB,EAAW3zB,KAAK,IAAI23B,EAAW9R,EAAMiS,EAAiBhqB,EAAMH,EAASC,KACrEE,EAAOuqB,OAEHR,EAAUh1B,QAAQs1B,uBAC1BrqB,EAAOA,EAAKwqB,UAAUzS,EAAK5lB,SAE/Bi4B,GAAUrS,EAAK5lB,OAEnB,OAAO0zB,GAGY,SAA0B7lB,EAAMH,EAASC,GAC5D,IAAI4f,EAAU1f,EAAKkI,cAAcgR,cAAc,sBAC/CwG,EAAQtG,YAAYpZ,EAAKyqB,WAAU,IACnC,IAAIN,EAAanqB,EAAKmqB,WACtB,GAAIA,EAAY,CACZA,EAAWO,aAAahL,EAAS1f,GACjC,IAAIG,GAAS,EAAIiH,EAAQvI,aAAa6gB,EAAS7f,EAASC,GAIxD,OAHI4f,EAAQiL,YACRR,EAAWO,aAAahL,EAAQiL,WAAYjL,GAEzCvf,EAEX,OAAO,IAAIiH,EAAQtI,OAAO,EAAG,EAAG,EAAG,KAGnCwrB,EAAiB,SAAwBtqB,EAAMoqB,EAAQj4B,EAAQ0N,EAASC,GACxE,IAAIkZ,EAAQhZ,EAAKkI,cAAc6Q,cAG/B,OAFAC,EAAM4R,SAAS5qB,EAAMoqB,GACrBpR,EAAM6R,OAAO7qB,EAAMoqB,EAASj4B,GACrBiV,EAAQtI,OAAOmB,eAAe+Y,EAAM9Y,wBAAyBL,EAASC,K,kCC1EjFhP,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAIsB,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAInB,IAAI83B,EAAwB,WACxB,SAASA,EAAsBC,IAHnC,SAAyBt3B,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAIxGoB,CAAgBC,KAAMm3B,GAEtBn3B,KAAKo3B,QAAUA,EA+BnB,OA5BAx4B,EAAau4B,EAAuB,CAAC,CACjC/3B,IAAK,SACL9B,MAAO,SAAgB+xB,GACnB,IAAIrb,EAAQhU,KAEZA,KAAKqvB,QAAUA,EACfrvB,KAAKya,OAASzN,SAASuY,cAAc,UACrCvlB,KAAKgmB,IAAMhmB,KAAKya,OAAOwL,WAAW,MAClCjmB,KAAKya,OAAO1O,MAAQ5L,KAAKstB,MAAM4B,EAAQtjB,OAASsjB,EAAQC,MACxDtvB,KAAKya,OAAOzO,OAAS7L,KAAKstB,MAAM4B,EAAQrjB,QAAUqjB,EAAQC,MAC1DtvB,KAAKya,OAAO7I,MAAM7F,MAAQsjB,EAAQtjB,MAAQ,KAC1C/L,KAAKya,OAAO7I,MAAM5F,OAASqjB,EAAQrjB,OAAS,KAE5CqjB,EAAQI,OAAOC,IAAI,uCAAyCL,EAAQtjB,MAAQ,IAAMsjB,EAAQrjB,OAAS,OAASqjB,EAAQ5jB,EAAI,IAAM4jB,EAAQ3jB,EAAI,gBAAkB2jB,EAAQC,OACpK,IAAIlI,EAAMC,EAAuBlnB,KAAKiN,IAAIiiB,EAAQgI,YAAahI,EAAQtjB,OAASsjB,EAAQC,MAAOnvB,KAAKiN,IAAIiiB,EAAQiI,aAAcjI,EAAQrjB,QAAUqjB,EAAQC,MAAOD,EAAQnjB,QAAUmjB,EAAQC,MAAOD,EAAQljB,QAAUkjB,EAAQC,MAAOtvB,KAAKo3B,SAEtO,OAAO9P,EAAkBF,GAAKG,MAAK,SAAUjN,GAOzC,OANI+U,EAAQtR,kBACR/J,EAAMgS,IAAIiB,UAAYoI,EAAQtR,gBAAgBmR,WAC9Clb,EAAMgS,IAAIkB,SAAS,EAAG,EAAGmI,EAAQtjB,MAAQsjB,EAAQC,MAAOD,EAAQrjB,OAASqjB,EAAQC,QAGrFtb,EAAMgS,IAAIE,UAAU5L,GAAM+U,EAAQ5jB,EAAI4jB,EAAQC,OAAQD,EAAQ3jB,EAAI2jB,EAAQC,OACnEtb,EAAMyG,cAKlB0c,EAnCiB,GAsC5B95B,EAAQ+D,QAAU+1B,EAClB,IAAI9P,EAAyBhqB,EAAQgqB,uBAAyB,SAAgCtb,EAAOC,EAAQP,EAAGC,EAAGW,GAC/G,IAAIkrB,EAAQ,6BACRnQ,EAAMpa,SAASwqB,gBAAgBD,EAAO,OACtCE,EAAgBzqB,SAASwqB,gBAAgBD,EAAO,iBAapD,OAZAnQ,EAAIsQ,eAAe,KAAM,QAAS3rB,GAClCqb,EAAIsQ,eAAe,KAAM,SAAU1rB,GAEnCyrB,EAAcC,eAAe,KAAM,QAAS,QAC5CD,EAAcC,eAAe,KAAM,SAAU,QAC7CD,EAAcC,eAAe,KAAM,IAAKjsB,GACxCgsB,EAAcC,eAAe,KAAM,IAAKhsB,GACxC+rB,EAAcC,eAAe,KAAM,4BAA6B,QAChEtQ,EAAI3B,YAAYgS,GAEhBA,EAAchS,YAAYpZ,GAEnB+a,GAGPE,EAAoBjqB,EAAQiqB,kBAAoB,SAA2BF,GAC3E,OAAO,IAAIb,SAAQ,SAAUC,EAASiB,GAClC,IAAInN,EAAM,IAAIyL,MACdzL,EAAImM,OAAS,WACT,OAAOD,EAAQlM,IAEnBA,EAAIoM,QAAUe,EAEdnN,EAAIE,IAAM,oCAAsCJ,oBAAmB,IAAIF,eAAgBG,kBAAkB+M,S,kCC1EjHjqB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQk5B,WAAal5B,EAAQysB,cAAgBzsB,EAAQosB,kBAAevrB,EAEpE,IAAIy5B,EAAgB,EAAQ,MAE5Bx6B,OAAOC,eAAeC,EAAS,eAAgB,CAC3C4B,YAAY,EACZ24B,IAAK,WACD,OAAOD,EAAclO,gBAG7BtsB,OAAOC,eAAeC,EAAS,gBAAiB,CAC5C4B,YAAY,EACZ24B,IAAK,WACD,OAAOD,EAAc7N,iBAI7B,IAMgCve,EAN5BuF,EAAiB,EAAQ,MAIzBiC,IAE4BxH,EAJauF,IAIQvF,EAAIC,WAFrC,EAAQ,OAIXnO,EAAQk5B,WAAa,SAAoB7M,EAAK1X,GAS3D,IARA,IAAI6lB,GAAU,EAAIF,EAAcG,aAAapO,EAAK,CAC9CvT,UAAWnE,EAAOJ,MAAMuE,UACxBwB,UAAW3F,EAAOJ,MAAMiF,eAAiB9D,EAAcwhB,cAAcE,WAAa,aAAeziB,EAAOJ,MAAM+F,YAG9GogB,EAAQ,GACRC,OAAK,IAEAA,EAAKH,EAAQx5B,QAAQC,MAC1By5B,EAAMx5B,KAAKy5B,EAAG16B,MAAM6tB,SAGxB,OAAO4M,I,kCCzCX56B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ46B,iBAAc/5B,EAEtB,IAAIU,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf8S,EAAQ,EAAQ,MAMF9U,EAAQ46B,YAAc,WACpC,SAASA,EAAYjrB,IALzB,SAAyBlN,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAMxGoB,CAAgBC,KAAMi4B,GAEtBj4B,KAAKk4B,MAAQ,GACbl4B,KAAKm4B,UAAYnrB,EAiErB,OA9DApO,EAAaq5B,EAAa,CAAC,CACvB74B,IAAK,gBACL9B,MAAO,SAAuBuU,GAC1B,IAAIH,EAAY1R,KAAKm4B,UAAU5S,cAAc,OACzCjL,EAAMta,KAAKm4B,UAAU5S,cAAc,OACnC6S,EAAOp4B,KAAKm4B,UAAU5S,cAAc,QAEpCtY,EAAOjN,KAAKm4B,UAAUlrB,KAC1B,IAAKA,EACD,MAAM,IAAIE,MAAqF,IAGnGuE,EAAUE,MAAM6F,WAAa,SAC7B/F,EAAUE,MAAM4gB,WAAa3gB,EAAK2gB,WAClC9gB,EAAUE,MAAME,SAAWD,EAAKC,SAChCJ,EAAUE,MAAMyE,OAAS,IACzB3E,EAAUE,MAAMjF,QAAU,IAE1BM,EAAKwY,YAAY/T,GAEjB4I,EAAIE,IAAMrI,EAAMiJ,YAChBd,EAAIvO,MAAQ,EACZuO,EAAItO,OAAS,EAEbsO,EAAI1I,MAAMyE,OAAS,IACnBiE,EAAI1I,MAAMjF,QAAU,IACpB2N,EAAI1I,MAAMymB,cAAgB,WAE1BD,EAAKxmB,MAAM4gB,WAAa3gB,EAAK2gB,WAC7B4F,EAAKxmB,MAAME,SAAWD,EAAKC,SAC3BsmB,EAAKxmB,MAAMyE,OAAS,IACpB+hB,EAAKxmB,MAAMjF,QAAU,IAErByrB,EAAK3S,YAAYzlB,KAAKm4B,UAAU7L,eA3C1B,gBA4CN5a,EAAU+T,YAAY2S,GACtB1mB,EAAU+T,YAAYnL,GACtB,IAAI0Y,EAAW1Y,EAAIge,UAAYF,EAAKE,UAAY,EAEhD5mB,EAAUmU,YAAYuS,GACtB1mB,EAAU+T,YAAYzlB,KAAKm4B,UAAU7L,eAjD/B,gBAmDN5a,EAAUE,MAAM2mB,WAAa,SAC7Bje,EAAI1I,MAAMymB,cAAgB,QAE1B,IAAIjF,EAAS9Y,EAAIge,UAAY5mB,EAAU4mB,UAAY,EAInD,OAFArrB,EAAK4Y,YAAYnU,GAEV,CAAEshB,SAAUA,EAAUI,OAAQA,KAE1C,CACCh0B,IAAK,aACL9B,MAAO,SAAoBuU,GACvB,IAAIzS,EAAMyS,EAAK2gB,WAAa,IAAM3gB,EAAKC,SAKvC,YAJwB5T,IAApB8B,KAAKk4B,MAAM94B,KACXY,KAAKk4B,MAAM94B,GAAOY,KAAKw4B,cAAc3mB,IAGlC7R,KAAKk4B,MAAM94B,OAInB64B,EAtE6B,I,kCCbxC96B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQo7B,WAAQv6B,EAEhB,IAIgCqN,EAJ5B4qB,EAAW,EAAQ,MAEnBC,GAE4B7qB,EAFO4qB,IAEc5qB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAE3ElO,EAAQo7B,MAAQ,SAAeje,EAAK6U,GAC5C,IAAKA,EAAQqJ,MACT,OAAOnS,QAAQkB,OAAoE,MAEvF,IAAIiR,EAAQrJ,EAAQqJ,MAEpB,OAAO,IAAInS,SAAQ,SAAUC,EAASiB,GAClC,IAAIK,EAAesO,EAAUh1B,QAAQu3B,kBAAoBvC,EAAUh1B,QAAQw3B,sBAAwB,OAAS,OACxGC,EAAMzC,EAAUh1B,QAAQu3B,iBAAmB,IAAI9Q,eAAmB,IAAIiR,eAiC1E,GAhCAD,EAAIpS,OAAS,WACT,GAAIoS,aAAehR,eACf,GAAmB,MAAfgR,EAAIE,OACJ,GAAqB,SAAjBjR,EACAtB,EAAQqS,EAAIG,cACT,CACH,IAAIC,EAAS,IAAIC,WAEjBD,EAAO3gB,iBAAiB,QAAQ,WAC5B,OAAOkO,EAAQyS,EAAO/O,WACvB,GAEH+O,EAAO3gB,iBAAiB,SAAS,SAAU8N,GACvC,OAAOqB,EAAOrB,MACf,GACH6S,EAAOE,cAAcN,EAAIG,eAG7BvR,EAAyI,SAG7IjB,EAAQqS,EAAIO,eAIpBP,EAAInS,QAAUe,EACdoR,EAAIQ,KAAK,MAAOX,EAAQ,QAAUte,mBAAmBI,GAAO,iBAAmBsN,GAE1D,SAAjBA,GAA2B+Q,aAAehR,iBAC1CgR,EAAI/Q,aAAeA,GAGnBuH,EAAQiK,aAAc,CACtB,IAAIC,EAAUlK,EAAQiK,aACtBT,EAAIU,QAAUA,EACdV,EAAIW,UAAY,WACZ,OAAO/R,EAAmH,KAIlIoR,EAAIY,Y,kCC5DZt8B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQq8B,mBAAgBx7B,EAExB,IAAIX,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAMllBg7B,GAFWtuB,EAFD,EAAQ,OAIJ,EAAQ,OAItBuuB,EAAavuB,EAFD,EAAQ,OAMpBwuB,EAA0BxuB,EAFD,EAAQ,OAMjC+qB,EAAY/qB,EAFD,EAAQ,OAInBoI,EAAU,EAAQ,MAElBqmB,EAAS,EAAQ,MAEjBC,EAAQ,EAAQ,MAEhB9nB,EAAS,EAAQ,MAEjBC,EAAU7G,EAAuB4G,GAErC,SAAS5G,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEnElO,EAAQq8B,cAAgB,SAASA,EAActC,EAAS/H,EAASI,GACjF,IAAIlb,EAAgB6iB,EAAQ7iB,cAExBylB,EAAe,IAAIvmB,EAAQtI,OAAOkkB,EAAQnjB,QAASmjB,EAAQljB,QAASkjB,EAAQgI,YAAahI,EAAQiI,cAGjG2C,EAA0B1lB,EAAcrH,gBAAkB,IAAIgF,EAAQ9Q,QAAQsT,iBAAiBH,EAAcrH,iBAAiB6Q,iBAAmB9L,EAAOvH,YACxJwvB,EAAsB3lB,EAActH,KAAO,IAAIiF,EAAQ9Q,QAAQsT,iBAAiBH,EAActH,MAAM8Q,iBAAmB9L,EAAOvH,YAE9HqT,EAAkBqZ,IAAY7iB,EAAcrH,gBAAkB+sB,EAAwBE,gBAAkBD,EAAoBC,gBAAkB9K,EAAQtR,gBAAkB,IAAI7L,EAAQ9Q,QAAQiuB,EAAQtR,iBAAmB,KAAOmc,EAAsBD,EAA0B5K,EAAQtR,gBAAkB,IAAI7L,EAAQ9Q,QAAQiuB,EAAQtR,iBAAmB,KAE3V,OAAQsR,EAAQ+K,uBAChBhE,EAAUh1B,QAAQi5B,8BAAgC9T,QAAQC,SAAQ,IAAQe,MAAK,SAAU+S,GACrF,OAAOA,GAAiCC,EAuBtC,IAAIT,EAAOU,eAAepD,EAAS/H,EAASI,GAAQ,EAAMiK,IAlB1Ce,YAAYlmB,GAAegT,MAAK,WAC1C,OAAOgT,EAAOzmB,eAAe4mB,WAC9BnT,MAAK,WAEJ,OADe,IAAIsS,EAAwBz4B,QAAQm5B,EAAOrtB,iBAC1CytB,OAAO,CACnB5c,gBAAiBA,EACjB0R,OAAQA,EACRH,MAAOD,EAAQC,MACf7jB,EAAG4jB,EAAQ5jB,EACXC,EAAG2jB,EAAQ3jB,EACXK,MAAOsjB,EAAQtjB,MACfC,OAAQqjB,EAAQrjB,OAChBqrB,YAAahI,EAAQgI,YACrBC,aAAcjI,EAAQiI,aACtBprB,QAASmjB,EAAQnjB,QACjBC,QAASkjB,EAAQljB,cAGiD,EAAI2tB,EAAOc,aAAarmB,EAAeylB,EAAc5C,EAAS/H,EAASI,EAAQiK,GAAenS,MAAK,SAAUrnB,GACvL,IAAIa,EAAQxD,EAAe2C,EAAM,GAC7BwR,EAAY3Q,EAAM,GAClB85B,EAAgB95B,EAAM,GACtB+S,EAAiB/S,EAAM,GAM3B,IAAI+5B,GAAQ,EAAInB,EAAYoB,YAAYF,EAAe/mB,EAAgB2b,GACnEuL,EAAiBH,EAActmB,cAMnC,OAJIwJ,IAAoB+c,EAAMppB,UAAUE,MAAMmD,WAAWgJ,kBACrD+c,EAAMppB,UAAUE,MAAMmD,WAAWgJ,gBAAkB9L,EAAOvH,aAGvDoJ,EAAe4mB,QAAQnT,MAAK,SAAU0T,GACzC,IAAIhI,EAAc,IAAI8G,EAAM9B,YAAY+C,GAKxC,IAAIE,EAAgB,CAChBnd,gBAAiBA,EACjBkV,YAAaA,EACbgI,WAAYA,EACZxL,OAAQA,EACRH,MAAOD,EAAQC,MACf7jB,EAAG4jB,EAAQ5jB,EACXC,EAAG2jB,EAAQ3jB,EACXK,MAAOsjB,EAAQtjB,MACfC,OAAQqjB,EAAQrjB,QAGpB,GAAItO,MAAMC,QAAQ0xB,EAAQvwB,QACtB,OAAOynB,QAAQ4U,IAAI9L,EAAQvwB,OAAOyf,KAAI,SAAUzf,GAE5C,OADe,IAAI86B,EAAWx4B,QAAQtC,EAAQo8B,GAC9BP,OAAOG,OAG3B,IACIrgB,EADW,IAAImf,EAAWx4B,QAAQiuB,EAAQvwB,OAAQo8B,GAChCP,OAAOG,GAS7B,OARgC,IAA5BzL,EAAQ+L,iBACJ1pB,EAAU8kB,YACV9kB,EAAU8kB,WAAW3Q,YAAYnU,GAMlC+I,QA1EW,IAAU8f,O,kCClDhDp9B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ09B,gBAAa78B,EAErB,IAEIm9B,EAAoBhwB,EAFD,EAAQ,OAM3BqgB,EAAkBrgB,EAFD,EAAQ,OAMzBsgB,EAAkBtgB,EAFD,EAAQ,OAIzBqI,EAAS,EAAQ,MAEjBC,EAAY,EAAQ,MAEpBf,EAAa,EAAQ,MAEzB,SAASvH,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEtElO,EAAQ09B,WAAa,SAAoB1uB,EAAMyH,EAAgB2b,GAK5E,IAAI1b,EAAQ,EAERrC,EAAY,IAAIga,EAAgBtqB,QAAQiL,EAAM,KAAMyH,EAAgBC,KACpE+mB,EAAQ,IAAIO,EAAkBj6B,QAAQsQ,EAAW,MAAM,GAQ3D,OANA4pB,EAAcjvB,EAAMqF,EAAWopB,EAAOhnB,EAHkCC,GASjE+mB,GAhBX,IAmBIS,EAAqB,CAAC,SAAU,OAAQ,QAAS,SAAU,KAAM,UAEjED,EAAgB,SAASA,EAAcjvB,EAAM2F,EAAQ8oB,EAAOhnB,EAAgBC,GAK5E,IAAK,IAAiCynB,EAA7BC,EAAYpvB,EAAK2qB,WAAsByE,EAAWA,EAAYD,EAAU,CAC7EA,EAAWC,EAAUC,YACrB,IAAIpnB,EAAcmnB,EAAUlnB,cAAcD,YAC1C,GAAImnB,aAAqBnnB,EAAYqnB,MAAQF,aAAqBE,MAAQrnB,EAAYtC,QAAUypB,aAAqBnnB,EAAYtC,OAAO2pB,KAChIF,EAAUpX,KAAKhG,OAAO7f,OAAS,GAC/BwT,EAAOkC,WAAW3V,KAAKotB,EAAgBvqB,QAAQmrB,aAAakP,EAAWzpB,SAExE,GAAIypB,aAAqBnnB,EAAYsnB,aAAeH,aAAqBG,aAAetnB,EAAYtC,QAAUypB,aAAqBnnB,EAAYtC,OAAO4pB,aACzJ,IAAwD,IAApDL,EAAmB9kB,QAAQglB,EAAUI,UAAkB,CACvD,IAAInqB,EAAY,IAAIga,EAAgBtqB,QAAQq6B,EAAWzpB,EAAQ8B,EAAgBC,KAC/E,GAAIrC,EAAUoqB,YAAa,CACG,UAAtBL,EAAUxnB,SAEV,EAAIP,EAAOqhB,oBAAoB0G,EAAW/pB,GACb,aAAtB+pB,EAAUxnB,SAEjB,EAAIP,EAAOohB,uBAAuB2G,EAAW/pB,GAChB,WAAtB+pB,EAAUxnB,SAEjB,EAAIP,EAAOmhB,qBAAqB4G,EAAW/pB,GACpCA,EAAUE,MAAMmE,WAAarE,EAAUE,MAAMmE,UAAUiO,gBAAkBpR,EAAW0N,gBAAgBnH,OAC3G,EAAIxF,EAAU8X,uBAAuBgQ,EAAW/pB,EAAWoC,GAG/D,IAAIioB,EAAiD,aAAtBN,EAAUxnB,QACrC+nB,EAA6BC,EAA2BvqB,EAAW+pB,GACvE,GAAIO,GAA8BE,EAAuBxqB,GAAY,CAGjE,IAAIyqB,EAAcH,GAA8BtqB,EAAU6H,eAAiBuhB,EAAMsB,+BAAiCtB,EAC9GuB,EAAa,IAAIhB,EAAkBj6B,QAAQsQ,EAAWyqB,EAAaH,GACvEG,EAAYG,SAAS/9B,KAAK89B,GACtBN,GACAT,EAAcG,EAAW/pB,EAAW2qB,EAAYvoB,EAAgBC,QAGpE+mB,EAAMyB,SAASh+B,KAAKmT,GAChBqqB,GACAT,EAAcG,EAAW/pB,EAAWopB,EAAOhnB,EAAgBC,UAKxE,GAAI0nB,aAAqBnnB,EAAYyF,eAAiB0hB,aAAqB1hB,eAAiBzF,EAAYtC,QAAUypB,aAAqBnnB,EAAYtC,OAAO+H,cAAe,CAC5K,IAAIyiB,EAAa,IAAI9Q,EAAgBtqB,QAAQq6B,EAAWzpB,EAAQ8B,EAAgBC,KAC5E0oB,EAA8BR,EAA2BO,EAAYf,GACzE,GAAIgB,GAA+BP,EAAuBM,GAAa,CAGnE,IAAIE,EAAeD,GAA+BD,EAAWjjB,eAAiBuhB,EAAMsB,+BAAiCtB,EACjH6B,EAAc,IAAItB,EAAkBj6B,QAAQo7B,EAAYE,EAAcD,GAC1EC,EAAaJ,SAAS/9B,KAAKo+B,QAE3B7B,EAAMyB,SAASh+B,KAAKi+B,MAMhCP,EAA6B,SAAoCvqB,EAAWrF,GAC5E,OAAOqF,EAAUqH,iBAAmBrH,EAAUkrB,0BAA4BlrB,EAAUE,MAAM2E,QAAU,GAAK7E,EAAUuG,iBAAmB4kB,EAA0BnrB,EAAWrF,IAG3K6vB,EAAyB,SAAgCxqB,GACzD,OAAOA,EAAU6H,gBAAkB7H,EAAUsH,cAG7C6jB,EAA4B,SAAmCnrB,EAAWrF,GAC1E,MAAyB,SAAlBA,EAAKwvB,UAAuBnqB,EAAUM,kBAAkB0Z,EAAgBtqB,SAAWsQ,EAAUM,OAAOJ,MAAMmD,WAAWgJ,gBAAgBoc,kB,kCCvHhJh9B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAQgCiO,EAR5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEfyR,EAAiB,EAAQ,OAMGvF,EAJauF,IAIQvF,EAAIC,WAFzC,EAAQ,MAMxB,IAAIsxB,EAAkB,WAClB,SAASA,EAAgBprB,EAAWM,EAAQgqB,IAHhD,SAAyBl8B,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAIxGoB,CAAgBC,KAAM88B,GAEtB98B,KAAK0R,UAAYA,EACjB1R,KAAKgS,OAASA,EACdhS,KAAKs8B,SAAW,GAChBt8B,KAAKu8B,SAAW,GAChBv8B,KAAKg8B,2BAA6BA,EAetC,OAZAp9B,EAAak+B,EAAiB,CAAC,CAC3B19B,IAAK,aACL9B,MAAO,WACH,OAAO0C,KAAKgS,OAAShS,KAAK0R,UAAUE,MAAM2E,QAAUvW,KAAKgS,OAAO+qB,aAAe/8B,KAAK0R,UAAUE,MAAM2E,UAEzG,CACCnX,IAAK,+BACL9B,MAAO,WACH,OAAQ0C,KAAKgS,QAAUhS,KAAKg8B,2BAA6Bh8B,KAAOA,KAAKgS,OAAOoqB,mCAI7EU,EAvBW,GA0BtBz/B,EAAQ+D,QAAU07B,G,kCC1ClB3/B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAYXD,EAAQ+D,QAPG,SAAS47B,EAAKjxB,EAAOC,IAFhC,SAAyBlM,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAG5GoB,CAAgBC,KAAMg9B,GAEtBh9B,KAAK+L,MAAQA,EACb/L,KAAKgM,OAASA,I,kCCVlB7O,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAQgCiO,EAR5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf8gB,EAAQ,EAAQ,MAEhB8c,EAAU,EAAQ,MAElB7xB,GAE4BG,EAFM0xB,IAEe1xB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAIvF,IAAI2xB,EAAO,SAAc/7B,EAAGD,EAAGi8B,GAC3B,OAAO,IAAI/xB,EAAShK,QAAQD,EAAEsK,GAAKvK,EAAEuK,EAAItK,EAAEsK,GAAK0xB,EAAGh8B,EAAEuK,GAAKxK,EAAEwK,EAAIvK,EAAEuK,GAAKyxB,IAGvEC,EAAc,WACd,SAASA,EAAYhpB,EAAOoc,EAAcC,EAAYC,IAP1D,SAAyB5wB,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAQxGoB,CAAgBC,KAAMo9B,GAEtBp9B,KAAKkR,KAAOiP,EAAMJ,KAAKE,aACvBjgB,KAAKoU,MAAQA,EACbpU,KAAKwwB,aAAeA,EACpBxwB,KAAKywB,WAAaA,EAClBzwB,KAAK0wB,IAAMA,EAqBf,OAlBA9xB,EAAaw+B,EAAa,CAAC,CACvBh+B,IAAK,YACL9B,MAAO,SAAmB6/B,EAAGE,GACzB,IAAIC,EAAKJ,EAAKl9B,KAAKoU,MAAOpU,KAAKwwB,aAAc2M,GACzCI,EAAKL,EAAKl9B,KAAKwwB,aAAcxwB,KAAKywB,WAAY0M,GAC9CK,EAAKN,EAAKl9B,KAAKywB,WAAYzwB,KAAK0wB,IAAKyM,GACrCM,EAAOP,EAAKI,EAAIC,EAAIJ,GACpBO,EAAOR,EAAKK,EAAIC,EAAIL,GACpBQ,EAAOT,EAAKO,EAAMC,EAAMP,GAC5B,OAAOE,EAAY,IAAID,EAAYp9B,KAAKoU,MAAOkpB,EAAIG,EAAME,GAAQ,IAAIP,EAAYO,EAAMD,EAAMF,EAAIx9B,KAAK0wB,OAE3G,CACCtxB,IAAK,UACL9B,MAAO,WACH,OAAO,IAAI8/B,EAAYp9B,KAAK0wB,IAAK1wB,KAAKywB,WAAYzwB,KAAKwwB,aAAcxwB,KAAKoU,WAI3EgpB,EA7BO,GAgClB//B,EAAQ+D,QAAUg8B,G,kCCpDlBjgC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQkY,uBAAoBrX,EAE5B,IAMgCqN,EAN5BhO,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllB21B,EAAU,EAAQ,MAElBtY,GAE4BzQ,EAFM+oB,IAEe/oB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAI4d,EAAQ,CAAC,WAAY,YAAa,eAAgB,eAE9B9rB,EAAQkY,kBAAoB,SAA2B3D,GAC3E,OAAOuX,EAAM5K,KAAI,SAAU8K,GACvB,IAEIuU,EAFQhsB,EAAMuJ,iBAAiB,UAAYkO,EAAO,WAEzBzK,MAAM,KAAKL,IAAIvC,EAAS5a,QAAQy8B,QACzDC,EAAoBvgC,EAAeqgC,EAAkB,GACrDG,EAAaD,EAAkB,GAC/BE,EAAWF,EAAkB,GAEjC,YAA2B,IAAbE,EAA2B,CAACD,EAAYA,GAAc,CAACA,EAAYC,Q,kCCxBzF7gC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI0Y,EAAU3Y,EAAQ2Y,QAAU,CAC5BmD,KAAM,EACN8kB,MAAO,EACPxkB,OAAQ,EACRykB,OAAQ,EACRC,KAAM,GACNC,UAAW,GACXC,MAAO,GACPC,KAAM,IACNC,KAAM,IACNC,KAAM,IACNC,QAAS,KACTxoB,UAAW,KACXyoB,gBAAiB,KACjBC,mBAAoB,KACpBC,mBAAoB,MACpBC,UAAW,MACXC,WAAY,MACZC,mBAAoB,GAAK,GACzBC,aAAc,GAAK,GACnBC,cAAe,GAAK,GACpBC,UAAW,GAAK,GAChBC,UAAW,GAAK,GAChBC,oBAAqB,GAAK,GAC1BC,oBAAqB,GAAK,GAC1BC,SAAU,GAAK,GACf5lB,aAAc,GAAK,GACnBG,iBAAkB,GAAK,GACvBC,aAAc,GAAK,GACnBH,YAAa,GAAK,GAClBC,YAAa,GAAK,IAoElB2lB,EAAgB,SAAuB1kB,EAAKlG,GAC5C,OAAOkG,EAlEa,SAA2BlG,GAC/C,OAAQA,GACJ,IAAK,QACD,OAAOqB,EAAQioB,MACnB,IAAK,SACD,OAAOjoB,EAAQyD,OACnB,IAAK,SACD,OAAOzD,EAAQkoB,OACnB,IAAK,OACD,OAAOloB,EAAQmoB,KACnB,IAAK,YACD,OAAOnoB,EAAQooB,UACnB,IAAK,QACD,OAAOpoB,EAAQqoB,MACnB,IAAK,OACD,OAAOroB,EAAQsoB,KACnB,IAAK,OACD,OAAOtoB,EAAQuoB,KACnB,IAAK,OACD,OAAOvoB,EAAQwoB,KACnB,IAAK,UACD,OAAOxoB,EAAQyoB,QACnB,IAAK,YACD,OAAOzoB,EAAQC,UACnB,IAAK,kBACD,OAAOD,EAAQ0oB,gBACnB,IAAK,qBACD,OAAO1oB,EAAQ2oB,mBACnB,IAAK,qBACD,OAAO3oB,EAAQ4oB,mBACnB,IAAK,YACD,OAAO5oB,EAAQ6oB,UACnB,IAAK,aACD,OAAO7oB,EAAQ8oB,WACnB,IAAK,qBACD,OAAO9oB,EAAQ+oB,mBACnB,IAAK,eACD,OAAO/oB,EAAQgpB,aACnB,IAAK,gBACD,OAAOhpB,EAAQipB,cACnB,IAAK,YACD,OAAOjpB,EAAQkpB,UACnB,IAAK,YACD,OAAOlpB,EAAQmpB,UACnB,IAAK,sBACD,OAAOnpB,EAAQopB,oBACnB,IAAK,sBACD,OAAOppB,EAAQqpB,oBACnB,IAAK,WACD,OAAOrpB,EAAQspB,SACnB,IAAK,eACD,OAAOtpB,EAAQ0D,aACnB,IAAK,mBACD,OAAO1D,EAAQ6D,iBACnB,IAAK,eACD,OAAO7D,EAAQ8D,aACnB,IAAK,cACD,OAAO9D,EAAQ2D,YACnB,IAAK,cACD,OAAO3D,EAAQ4D,YAGvB,OAAO5D,EAAQmD,KAIFqmB,CAAkB7qB,IAGhBtX,EAAQuX,aAAe,SAAsBD,GAC5D,OAAOA,EAAQiK,MAAM,KAAKkO,OAAOyS,EAAe,K,kCC1GpDpiC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIgc,EAAQjc,EAAQic,MAAQ,CACxBH,KAAM,EACNqE,KAAM,EACNC,MAAO,EACPgiB,aAAc,EACdC,WAAY,GAGIriC,EAAQsY,cAAgB,SAAuBD,GAC/D,OAAQA,GACJ,IAAK,OACD,OAAO4D,EAAMkE,KACjB,IAAK,QACD,OAAOlE,EAAMmE,MACjB,IAAK,eACD,OAAOnE,EAAMmmB,aACjB,IAAK,aACD,OAAOnmB,EAAMomB,WAErB,OAAOpmB,EAAMH,O,kCCtBjBhc,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAgBKD,EAAQuY,UAAY,SAAmBhE,GAOnD,MAAO,CACH4gB,WAPa5gB,EAAM4gB,WAQnB1gB,SAPWF,EAAME,SAQjBugB,UAPYzgB,EAAMygB,UAQlBC,YAPc1gB,EAAM0gB,YAQpBC,WAxBc,SAAyBoN,GAC3C,OAAQA,GACJ,IAAK,SACD,OAAO,IACX,IAAK,OACD,OAAO,IAGf,IAAIriC,EAAQgD,SAASq/B,EAAQ,IAC7B,OAAOruB,MAAMhU,GAAS,IAAMA,EAQXsiC,CAAgBhuB,EAAM2gB,e,kCCtB3Cp1B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEcD,EAAQyY,mBAAqB,SAA4BD,GAC9E,GAAsB,WAAlBA,EACA,OAAO,EAEX,IAAIvY,EAAQ+T,WAAWwE,GACvB,OAAOvE,MAAMhU,GAAS,EAAIA,I,kCCR9BH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIuiC,EAAaxiC,EAAQwiC,WAAa,CAClCrL,OAAQ,SACRsL,OAAQ,UAGSziC,EAAQ+Y,eAAiB,SAAwBuB,GAClE,OAAQA,GACJ,IAAK,SACD,OAAOkoB,EAAWC,OACtB,IAAK,SACL,QACI,OAAOD,EAAWrL,U,kCCd9Br3B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQiZ,iBAAcpY,EAEtB,IAIgCqN,EAJ5B+oB,EAAU,EAAQ,MAElBtY,GAE4BzQ,EAFM+oB,IAEe/oB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAI4d,EAAQ,CAAC,MAAO,QAAS,SAAU,QAErB9rB,EAAQiZ,YAAc,SAAqB1E,GACzD,OAAOuX,EAAM5K,KAAI,SAAU8K,GACvB,OAAO,IAAIrN,EAAS5a,QAAQwQ,EAAMuJ,iBAAiB,UAAYkO,S,kCCfvElsB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIqZ,EAAWtZ,EAAQsZ,SAAW,CAC9BkC,QAAS,EACTjC,OAAQ,EACRmpB,OAAQ,EACRzjB,KAAM,GAGUjf,EAAQqZ,cAAgB,SAAuBF,GAC/D,OAAQA,GACJ,IAAK,SACD,OAAOG,EAASC,OACpB,IAAK,SACD,OAAOD,EAASopB,OACpB,IAAK,OACD,OAAOppB,EAAS2F,KACpB,IAAK,UACL,QACI,OAAO3F,EAASkC,W,kCCpB5B1b,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ+Z,qBAAkBlZ,EAE1B,IAIgCqN,EAJ5B0G,EAAS,EAAQ,MAEjBC,GAE4B3G,EAFK0G,IAEgB1G,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAIy0B,EAAS,kBAES3iC,EAAQ+Z,gBAAkB,SAAyBD,GACrE,GAAmB,SAAfA,GAA+C,iBAAfA,EAChC,OAAO,KAmCX,IAhCA,IAAI8oB,EAAe,GACfC,GAAW,EACXzT,EAAS,GACT0T,EAAU,GACVC,EAAY,EACZ5qB,EAAQ,KAER6qB,EAAc,WACVJ,EAAazhC,SACT0hC,EACAzT,EAAOluB,KAAK8S,WAAW4uB,IAEvBzqB,EAAQ,IAAItD,EAAQ9Q,QAAQ6+B,IAGpCC,GAAW,EACXD,EAAe,IAGfK,EAAe,WACX7T,EAAOjuB,QAAoB,OAAVgX,GACjB2qB,EAAQ5hC,KAAK,CACTiX,MAAOA,EACPsc,QAASrF,EAAO,IAAM,EACtBsF,QAAStF,EAAO,IAAM,EACtBqG,KAAMrG,EAAO,IAAM,IAG3BA,EAAO8T,OAAO,EAAG9T,EAAOjuB,QACxBgX,EAAQ,MAGH/X,EAAI,EAAGA,EAAI0Z,EAAW3Y,OAAQf,IAAK,CACxC,IAAIoiB,EAAI1I,EAAW1Z,GACnB,OAAQoiB,GACJ,IAAK,IACDogB,GAAgBpgB,EAChBugB,IACA,MACJ,IAAK,IACDH,GAAgBpgB,EAChBugB,IACA,MACJ,IAAK,IACiB,IAAdA,GACAC,IACAC,KAEAL,GAAgBpgB,EAEpB,MACJ,IAAK,IACiB,IAAdugB,EACAC,IAEAJ,GAAgBpgB,EAEpB,MACJ,QACgC,IAAxBogB,EAAazhC,QAAgBwhC,EAAOlgB,KAAKD,KACzCqgB,GAAW,GAEfD,GAAgBpgB,GAO5B,OAHAwgB,IACAC,IAEuB,IAAnBH,EAAQ3hC,OACD,KAGJ2hC,I,kCC3FXhjC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQma,oBAAiBtZ,EAEzB,IAIgCqN,EAJ5B+oB,EAAU,EAAQ,MAElBtY,GAE4BzQ,EAFM+oB,IAEe/oB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAIi1B,EAAU,SAAiBvmB,GAC3B,OAAO5I,WAAW4I,EAAEoE,SAGpBoiB,EAAS,4BAuBTC,GArBiBrjC,EAAQma,eAAiB,SAAwB5F,GAClE,IAAI2F,EAAYopB,EAAqB/uB,EAAM2F,WAAa3F,EAAMgvB,iBAAmBhvB,EAAMivB,cAEvFjvB,EAAMkvB,aAENlvB,EAAMmvB,YACN,OAAkB,OAAdxpB,EACO,KAGJ,CACHA,UAAWA,EACXypB,gBAAiBN,EAAqB9uB,EAAMovB,iBAAmBpvB,EAAMqvB,uBAAyBrvB,EAAMsvB,oBAEpGtvB,EAAMuvB,mBAENvvB,EAAMwvB,oBAKa,SAA8BC,GACrD,GAAsB,iBAAXA,EAAqB,CAC5B,IAAI5vB,EAAI,IAAIuK,EAAS5a,QAAQ,KAC7B,MAAO,CAACqQ,EAAGA,GAEf,IAAIgb,EAAS4U,EAAOziB,MAAM,KAAKL,IAAIvC,EAAS5a,QAAQy8B,QACpD,MAAO,CAACpR,EAAO,GAAIA,EAAO,MAI1BkU,EAAuB,SAA8BppB,GACrD,GAAkB,SAAdA,GAA6C,iBAAdA,EAC/B,OAAO,KAGX,IAAIlX,EAAQkX,EAAUlX,MAAMogC,GAC5B,GAAIpgC,EAAO,CACP,GAAiB,WAAbA,EAAM,GAAiB,CACvB,IAAIkzB,EAASlzB,EAAM,GAAGue,MAAM,KAAKL,IAAIiiB,GACrC,MAAO,CAACjN,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAEtE,IAAI+N,EAAWjhC,EAAM,GAAGue,MAAM,KAAKL,IAAIiiB,GACvC,MAAO,CAACc,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,IAAKA,EAAS,KAG3F,OAAO,O,kCC/DXnkC,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI8b,EAAa/b,EAAQ+b,WAAa,CAClCP,QAAS,EACTjC,OAAQ,EACR2qB,SAAU,GAGQlkC,EAAQqa,gBAAkB,SAAyBD,GACrE,OAAQA,GACJ,IAAK,SACD,OAAO2B,EAAWxC,OACtB,IAAK,WACD,OAAOwC,EAAWmoB,SACtB,IAAK,UACL,QACI,OAAOnoB,EAAWP,W,kCCjB9B1b,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAIkkC,EAAankC,EAAQmkC,WAAa,CAClChN,OAAQ,SACRiN,UAAW,YACXC,SAAU,YAGOrkC,EAAQua,eAAiB,SAAwBD,GAClE,OAAQA,GACJ,IAAK,YACD,OAAO6pB,EAAWC,UACtB,IAAK,WACD,OAAOD,EAAWE,SACtB,IAAK,SACL,QACI,OAAOF,EAAWhN,U,kCCjB9Br3B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEOD,EAAQya,YAAc,SAAqBD,GACzD,IAAI2B,EAAkB,SAAX3B,EACX,MAAO,CACH2B,KAAMA,EACNmoB,MAAOnoB,EAAO,EAAIlZ,SAASuX,EAAQ,O,kCCP3C1a,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAGT,IAAI6U,EAAQ,EAAQ,MAEpBhV,OAAOC,eAAeC,EAAS,eAAgB,CAC7C4B,YAAY,EACZ24B,IAAK,WACH,OAAOzlB,EAAMsX,gBAGjBtsB,OAAOC,eAAeC,EAAS,gBAAiB,CAC9C4B,YAAY,EACZ24B,IAAK,WACH,OAAOzlB,EAAM2X,iBAIjB,IAAI8X,EAAa,EAAQ,MAEzBzkC,OAAOC,eAAeC,EAAS,cAAe,CAC5C4B,YAAY,EACZ24B,IAAK,WACH,OAAOgK,EAAW9J,gB,kCCxBtB36B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQy6B,YAAcz6B,EAAQwkC,yBAA2BxkC,EAAQykC,iBAAmBzkC,EAAQ0kC,6BAA+B1kC,EAAQ2kC,YAAc3kC,EAAQ4kC,cAAgB5kC,EAAQ6kC,kBAAoB7kC,EAAQ8kC,gBAAkB9kC,EAAQ+kC,QAAU/kC,EAAQglC,4BAAyBnkC,EAElR,IAYgCqN,EAZ5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf9B,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllB2jC,EAAQ,EAAQ,MAEhBC,EAAiB,EAAQ,MAEzBC,GAI4Bj3B,EAJag3B,IAIQh3B,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAFnF4G,EAAQ,EAAQ,MAQpB,IAAIkwB,EAAyBhlC,EAAQglC,uBAAyB,GAY1DI,EAAK,GAILC,EAAK,GAELC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GAiDL7B,GA9CU9kC,EAAQ+kC,QAAU,CAC5B6B,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLhC,GAAIA,EACJiC,IAjDM,GAkDNC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJ6B,GA/CK,GAgDL5B,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJsB,GA7CK,GA8CLrB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJmB,GA7CK,GA8CLlB,GAAIA,EACJmB,GA7CK,IAgDa9nC,EAAQ8kC,gBAAkB,KAC5CD,EAAoB7kC,EAAQ6kC,kBAAoB,IAChDD,EAAgB5kC,EAAQ4kC,cAAgB,IACxCD,EAAc3kC,EAAQ2kC,aAAc,EAAIM,EAAM8C,sBAAsB5C,EAAgBphC,SAEpFikC,EAAc,CAAC9B,EA5DV,IA6DL+B,EAAmB,CApGd,EACA,EACA,EAEA,GAiGLC,EAAQ,CAAC9C,EA9FJ,GA+FL+C,EAAiB,CAACnC,EAAID,GACtBqC,EAAcH,EAAiBxsB,OAAOysB,GACtCG,EAAwB,CAAC7B,EAAIC,EAAIC,EAAIL,EAAIC,GACzCgC,EAAS,CAAChD,EAAID,GAEdX,EAA+B1kC,EAAQ0kC,6BAA+B,SAAsCpY,GAC5G,IAAIxT,EAAY6T,UAAUxrB,OAAS,QAAsBN,IAAjB8rB,UAAU,GAAmBA,UAAU,GAAK,SAEhF4b,EAAQ,GACRC,EAAW,GACXC,EAAa,GAgEjB,OA/DAnc,EAAW/J,SAAQ,SAAUuK,EAAWpW,GACpC,IAAIgyB,EAAY/D,EAAYpK,IAAIzN,GAQhC,GAPI4b,EAAY1D,GACZyD,EAAWvnC,MAAK,GAChBwnC,GAAa1D,GAEbyD,EAAWvnC,MAAK,IAGoC,IAApD,CAAC,SAAU,OAAQ,SAASkY,QAAQN,KAEyB,IAAzD,CAAC,KAAQ,KAAQ,MAAQ,OAAQM,QAAQ0T,GAEzC,OADA0b,EAAStnC,KAAKwV,GACP6xB,EAAMrnC,KA9GpB,IAkHD,GA/HC,IA+HGwnC,GAxHF,KAwHsBA,EAAmB,CAEvC,GAAc,IAAVhyB,EAEA,OADA8xB,EAAStnC,KAAKwV,GACP6xB,EAAMrnC,KAAKglC,GAKtB,IAAIyC,EAAOJ,EAAM7xB,EAAQ,GACzB,OAAmC,IAA/B0xB,EAAYhvB,QAAQuvB,IACpBH,EAAStnC,KAAKsnC,EAAS9xB,EAAQ,IACxB6xB,EAAMrnC,KAAKynC,KAEtBH,EAAStnC,KAAKwV,GACP6xB,EAAMrnC,KAAKglC,IAKtB,OAFAsC,EAAStnC,KAAKwV,GAlHb,KAoHGgyB,EACOH,EAAMrnC,KAAmB,WAAd4X,EAAyB6sB,EAAKY,GAGhDmC,IAAc/B,GA1HjB,KA8HG+B,EAHOH,EAAMrnC,KAAKglC,GA7GrB,KAuHGwC,EACI5b,GAAa,QAAWA,GAAa,QAAWA,GAAa,QAAWA,GAAa,OAC9Eyb,EAAMrnC,KAAKqlC,GAEXgC,EAAMrnC,KAAKglC,QAI1BqC,EAAMrnC,KAAKwnC,MAGR,CAACF,EAAUD,EAAOE,IAGzBG,EAA6B,SAAoC9kC,EAAGD,EAAGglC,EAAcC,GACrF,IAAIC,EAAUD,EAAWD,GACzB,GAAIxoC,MAAMC,QAAQwD,IAA6B,IAAxBA,EAAEsV,QAAQ2vB,GAAkBjlC,IAAMilC,EAErD,IADA,IAAI3oC,EAAIyoC,EACDzoC,GAAK0oC,EAAW3nC,QAAQ,CAE3B,IAAIH,EAAO8nC,IADX1oC,GAGA,GAAIY,IAAS6C,EACT,OAAO,EAGX,GAAI7C,IAASokC,EACT,MAKZ,GAAI2D,IAAY3D,EAGZ,IAFA,IAAIrkC,EAAK8nC,EAEF9nC,EAAK,GAAG,CAEX,IAAI4nC,EAAOG,IADX/nC,GAGA,GAAIV,MAAMC,QAAQwD,IAA0B,IAArBA,EAAEsV,QAAQuvB,GAAe7kC,IAAM6kC,EAElD,IADA,IAAIK,EAAIH,EACDG,GAAKF,EAAW3nC,QAAQ,CAE3B,IAAI8nC,EAAQH,IADZE,GAGA,GAAIC,IAAUplC,EACV,OAAO,EAGX,GAAIolC,IAAU7D,EACV,MAKZ,GAAIuD,IAASvD,EACT,MAIZ,OAAO,GAGP8D,EAA4B,SAAmCL,EAAcC,GAE7E,IADA,IAAI1oC,EAAIyoC,EACDzoC,GAAK,GAAG,CACX,IAAIyT,EAAOi1B,EAAW1oC,GACtB,GAAIyT,IAASuxB,EAGT,OAAOvxB,EAFPzT,IAKR,OAAO,GAGP+oC,EAAoB,SAA2B7c,EAAYwc,EAAYN,EAAU9xB,EAAO0yB,GACxF,GAAwB,IAApBZ,EAAS9xB,GACT,OAAOmuB,EAGX,IAAIgE,EAAenyB,EAAQ,EAC3B,GAAIrW,MAAMC,QAAQ8oC,KAAsD,IAAlCA,EAAgBP,GAClD,OAAOhE,EAGX,IAAIwE,EAAcR,EAAe,EAC7BS,EAAaT,EAAe,EAC5BE,EAAUD,EAAWD,GAIrBU,EAASF,GAAe,EAAIP,EAAWO,GAAe,EACtDroC,EAAO8nC,EAAWQ,GAEtB,GAnQK,IAmQDP,GAlQC,IAkQiB/nC,EAClB,OAAO6jC,EAGX,IAA2C,IAAvCoD,EAAiB7uB,QAAQ2vB,GACzB,OAAOjE,EAIX,IAAwC,IAApCmD,EAAiB7uB,QAAQpY,GACzB,OAAO6jC,EAIX,IAA6B,IAAzBqD,EAAM9uB,QAAQpY,GACd,OAAO6jC,EAIX,GAhRK,IAgRDqE,EAA0BL,EAAcC,GACxC,OAAOlE,EAIX,GAlRM,KAkRFD,EAAYpK,IAAIjO,EAAWuc,MAA2B7nC,IAASulC,GAAMvlC,IAASmlC,GAAMnlC,IAASolC,GAC7F,OAAOvB,EAIX,GA3RK,IA2RDkE,GA3RC,IA2RiB/nC,EAClB,OAAO6jC,EAIX,GA9RK,IA8RDkE,EACA,OAAOlE,EAIX,IAAuC,IAAnC,CAACO,EAAIC,EAAIC,GAAIlsB,QAAQ2vB,IAnSpB,IAmSuC/nC,EACxC,OAAO6jC,EAIX,IAA4C,IAAxC,CAACU,EAAIC,EAAIC,EAAII,EAAII,GAAI7sB,QAAQpY,GAC7B,OAAO6jC,EAIX,GAAIqE,EAA0BL,EAAcC,KAAgBlD,EACxD,OAAOf,EAIX,GAAI+D,EAlSC,GAkS8BhD,EAAIiD,EAAcC,GACjD,OAAOjE,EAIX,GAAI+D,EAA2B,CAACrD,EAAIC,GAAKG,EAAIkD,EAAcC,GACvD,OAAOjE,EAIX,GAAI+D,EAxTC,MAwTkCC,EAAcC,GACjD,OAAOjE,EAIX,GAAIkE,IAAY3D,EACZ,OAAOR,EAIX,GAtTK,KAsTDmE,GAtTC,KAsTiB/nC,EAClB,OAAO6jC,EAIX,GAnUK,KAmUD7jC,GAnUC,KAmUc+nC,EACf,OAAOnE,EAIX,IAAoC,IAAhC,CAACS,EAAIC,EAAIK,GAAIvsB,QAAQpY,IA1UpB,KA0UoC+nC,EACrC,OAAOlE,EAIX,GAtTK,KAsTD0E,IAA8C,IAA7BjB,EAAOlvB,QAAQ2vB,GAChC,OAAOlE,EAIX,GAAIkE,IAAY9C,GA3TX,KA2TiBjlC,EAClB,OAAO6jC,EAIX,GAAI7jC,IAAS0kC,IAAuE,IAAjEsC,EAAYvsB,OAAOiqB,EAAID,EAAIK,EAAIS,EAAIJ,EAAIC,GAAIhtB,QAAQ2vB,GAClE,OAAOlE,EAIX,IAAmC,IAA/BmD,EAAY5uB,QAAQpY,IAAgB+nC,IAAYjD,IAAwC,IAAlCkC,EAAY5uB,QAAQ2vB,IAAmB/nC,IAAS8kC,EACtG,OAAOjB,EAIX,GAAIkE,IAAY/C,IAAsC,IAAhC,CAACO,EAAIJ,EAAIC,GAAIhtB,QAAQpY,KAAmD,IAAnC,CAACulC,EAAIJ,EAAIC,GAAIhtB,QAAQ2vB,IAAmB/nC,IAAS+kC,EACxG,OAAOlB,EAIX,IAAsC,IAAlCmD,EAAY5uB,QAAQ2vB,KAAqD,IAAlCZ,EAAe/uB,QAAQpY,KAAqD,IAArCmnC,EAAe/uB,QAAQ2vB,KAAkD,IAA/Bf,EAAY5uB,QAAQpY,GAC5I,OAAO6jC,EAIX,IAE+B,IAA/B,CAACmB,EAAID,GAAI3sB,QAAQ2vB,KAAoB/nC,IAAS8kC,IAAkC,IAA5B,CAACF,EAAIN,GAAIlsB,QAAQpY,IAAgB8nC,EAAWQ,EAAa,KAAOxD,KAErF,IAA/B,CAACF,EAAIN,GAAIlsB,QAAQ2vB,IAAmB/nC,IAAS8kC,GAE7CiD,IAAYjD,IAAsC,IAAhC,CAACA,EAAIG,EAAIJ,GAAIzsB,QAAQpY,GACnC,OAAO6jC,EAIX,IAA4C,IAAxC,CAACiB,EAAIG,EAAIJ,EAAIN,EAAIC,GAAIpsB,QAAQpY,GAE7B,IADA,IAAIwoC,EAAYX,EACTW,GAAa,GAAG,CACnB,IAAI31B,EAAOi1B,EAAWU,GACtB,GAAI31B,IAASiyB,EACT,OAAOjB,EACJ,IAAgC,IAA5B,CAACoB,EAAIJ,GAAIzsB,QAAQvF,GAGxB,MAFA21B,IAQZ,IAAgC,IAA5B,CAACxD,EAAID,GAAI3sB,QAAQpY,GAEjB,IADA,IAAIyoC,GAA4C,IAA/B,CAAClE,EAAIC,GAAIpsB,QAAQ2vB,GAAkBM,EAAcR,EAC3DY,GAAc,GAAG,CACpB,IAAIC,EAAQZ,EAAWW,GACvB,GAAIC,IAAU5D,EACV,OAAOjB,EACJ,IAAiC,IAA7B,CAACoB,EAAIJ,GAAIzsB,QAAQswB,GAGxB,MAFAD,IAQZ,GAAIjD,IAAOuC,IAA+C,IAApC,CAACvC,EAAIC,EAAIJ,EAAIC,GAAIltB,QAAQpY,KAA+C,IAA/B,CAACylC,EAAIJ,GAAIjtB,QAAQ2vB,KAA+C,IAA5B,CAACtC,EAAIC,GAAIttB,QAAQpY,KAA+C,IAA/B,CAAC0lC,EAAIJ,GAAIltB,QAAQ2vB,IAAmB/nC,IAAS0lC,EAC7K,OAAO7B,EAIX,IAAgD,IAA5CwD,EAAsBjvB,QAAQ2vB,KAA+C,IAA5B,CAACrD,EAAIK,GAAI3sB,QAAQpY,KAAyD,IAAzCqnC,EAAsBjvB,QAAQpY,IAAgB+nC,IAAY/C,EAC5I,OAAOnB,EAIX,IAAsC,IAAlCmD,EAAY5uB,QAAQ2vB,KAAkD,IAA/Bf,EAAY5uB,QAAQpY,GAC3D,OAAO6jC,EAIX,GAAIkE,IAAYlD,IAAqC,IAA/BmC,EAAY5uB,QAAQpY,GACtC,OAAO6jC,EAIX,IAAiD,IAA7CmD,EAAYvsB,OAAOqqB,GAAI1sB,QAAQ2vB,IAAmB/nC,IAAS4kC,IAAgD,IAA1CoC,EAAYvsB,OAAOqqB,GAAI1sB,QAAQpY,IAAgB+nC,IAAYvD,EAC5H,OAAOX,EAKX,GAlZK,KAkZDkE,GAlZC,KAkZiB/nC,EAAa,CAG/B,IAFA,IAAIZ,EAAIooC,EAASK,GACbc,EAAQ,EACLvpC,EAAI,GArZV,KAuZO0oC,IADJ1oC,IAEIupC,IAKR,GAAIA,EAAQ,GAAM,EACd,OAAO9E,EAKf,OAAIkE,IAAY5C,GAAMnlC,IAASolC,EACpBvB,EAGJD,GAsBPgF,GAnBmB5pC,EAAQykC,iBAAmB,SAA0BnY,EAAY5V,GAEpF,GAAc,IAAVA,EACA,OAAOmuB,EAIX,GAAInuB,GAAS4V,EAAWnrB,OACpB,OAAO2jC,EAGX,IAAI+E,EAAwBnF,EAA6BpY,GACrDwd,EAAyB5pC,EAAe2pC,EAAuB,GAC/DrB,EAAWsB,EAAuB,GAClChB,EAAagB,EAAuB,GAExC,OAAOX,EAAkB7c,EAAYwc,EAAYN,EAAU9xB,IAGrC,SAA6B4V,EAAY0F,GAC1DA,IACDA,EAAU,CAAElZ,UAAW,SAAUwB,UAAW,WAGhD,IAAIyvB,EAAyBrF,EAA6BpY,EAAY0F,EAAQlZ,WAC1EkxB,EAAyB9pC,EAAe6pC,EAAwB,GAChEvB,EAAWwB,EAAuB,GAClClB,EAAakB,EAAuB,GACpCC,EAAiBD,EAAuB,GAY5C,MAV0B,cAAtBhY,EAAQ1X,WAAmD,eAAtB0X,EAAQ1X,YAC7CwuB,EAAaA,EAAW5nB,KAAI,SAAUrN,GAClC,OAAuC,IAAhC,CAACiyB,EAAII,EAAIS,GAAIvtB,QAAQvF,GAAe0yB,EAAK1yB,MAQjD,CAAC20B,EAAUM,EAJ+B,aAAtB9W,EAAQ1X,UAA2B2vB,EAAe/oB,KAAI,SAAU+oB,EAAgB7pC,GACvG,OAAO6pC,GAAkB3d,EAAWlsB,IAAM,OAAUksB,EAAWlsB,IAAM,SACpE,QAsBL8pC,GAjB2BlqC,EAAQwkC,yBAA2B,SAAkCnY,EAAK2F,GACrG,IAAI1F,GAAa,EAAIxX,EAAMsX,cAAcC,GACrC8d,EAAStF,EAETuF,EAAuBR,EAAoBtd,EAAY0F,GACvDqY,EAAwBnqC,EAAekqC,EAAsB,GAC7D5B,EAAW6B,EAAsB,GACjCvB,EAAauB,EAAsB,GACnCC,EAAuBD,EAAsB,GAMjD,OAJA/d,EAAW/J,SAAQ,SAAUuK,EAAW1sB,GACpC+pC,IAAU,EAAIr1B,EAAM2X,eAAeK,IAAc1sB,GAAKksB,EAAWnrB,OAAS,EAAI2jC,EAAkBqE,EAAkB7c,EAAYwc,EAAYN,EAAUpoC,EAAI,EAAGkqC,OAGxJH,GAGC,WACR,SAASD,EAAM5d,EAAYxT,EAAW/B,EAAOsc,IAxhBjD,SAAyB5wB,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAyhBxGoB,CAAgBC,KAAMunC,GAEtBvnC,KAAK4nC,YAAcje,EACnB3pB,KAAK6nC,SAAW1xB,IAAcgsB,EAC9BniC,KAAKoU,MAAQA,EACbpU,KAAK0wB,IAAMA,EAUf,OAPA9xB,EAAa2oC,EAAO,CAAC,CACjBnoC,IAAK,QACL9B,MAAO,WACH,OAAO6U,EAAM2X,cAAcja,WAAM3R,EAtiB7C,SAA4BV,GAAO,GAAIE,MAAMC,QAAQH,GAAM,CAAE,IAAK,IAAIC,EAAI,EAAGqqC,EAAOpqC,MAAMF,EAAIgB,QAASf,EAAID,EAAIgB,OAAQf,IAAOqqC,EAAKrqC,GAAKD,EAAIC,GAAM,OAAOqqC,EAAe,OAAOpqC,MAAMopB,KAAKtpB,GAsiBlIuqC,CAAmB/nC,KAAK4nC,YAAYzc,MAAMnrB,KAAKoU,MAAOpU,KAAK0wB,WAIxG6W,EAjBC,IAoBMlqC,EAAQy6B,YAAc,SAAqBpO,EAAK2F,GAC9D,IAAI1F,GAAa,EAAIxX,EAAMsX,cAAcC,GAErCse,EAAwBf,EAAoBtd,EAAY0F,GACxD4Y,EAAwB1qC,EAAeyqC,EAAuB,GAC9DnC,EAAWoC,EAAsB,GACjC9B,EAAa8B,EAAsB,GACnCN,EAAuBM,EAAsB,GAE7CzpC,EAASmrB,EAAWnrB,OACpB0pC,EAAU,EACVC,EAAY,EAEhB,MAAO,CACH9pC,KAAM,WACF,GAAI8pC,GAAa3pC,EACb,MAAO,CAAEF,MAAM,GAGnB,IADA,IAAI6X,EAAY+rB,EACTiG,EAAY3pC,IAAW2X,EAAYqwB,EAAkB7c,EAAYwc,EAAYN,IAAYsC,EAAWR,MAA2BzF,IAEtI,GAAI/rB,IAAc+rB,GAAqBiG,IAAc3pC,EAAQ,CACzD,IAAIlB,EAAQ,IAAIiqC,EAAM5d,EAAYxT,EAAW+xB,EAASC,GAEtD,OADAD,EAAUC,EACH,CAAE7qC,MAAOA,EAAOgB,MAAM,GAGjC,MAAO,CAAEA,MAAM,O,kCC3lB3BnB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQ+qC,KAAO/qC,EAAQ+nC,qBAAuB/nC,EAAQgrC,oBAAsBhrC,EAAQirC,4BAA8BjrC,EAAQkrC,kCAAoClrC,EAAQmrC,sBAAwBnrC,EAAQorC,8BAAgCprC,EAAQqrC,8BAAgCrrC,EAAQsrC,0BAA4BtrC,EAAQurC,2BAA6BvrC,EAAQwrC,iBAAmBxrC,EAAQyrC,yBAA2BzrC,EAAQ0rC,2BAA6B1rC,EAAQ2rC,iBAAmB3rC,EAAQ4rC,mBAAqB5rC,EAAQ6rC,eAAiB7rC,EAAQ8rC,oBAAiBjrC,EAEpiB,IAAIU,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf8S,EAAQ,EAAQ,MAKpB,IAAIg3B,EAAiB9rC,EAAQ8rC,eAAiB,EAG1CD,EAAiB7rC,EAAQ6rC,eAAiB,GAQ1CD,EAAqB5rC,EAAQ4rC,mBAAqB,EAMlDD,EAAmB3rC,EAAQ2rC,iBAAmBE,EAAiBC,EAQ/DJ,EAA6B1rC,EAAQ0rC,2BAA6B,OAAWI,EAG7EL,EAA2BzrC,EAAQyrC,yBAA2B,GAAKK,EAEnEN,EAAmBxrC,EAAQwrC,iBAAmBC,EAA2B,EAEzEF,EAA6BvrC,EAAQurC,2BAA6B,MAASO,EAE3ER,EAA4BtrC,EAAQsrC,0BAA4BI,EAA6BH,EAK7FF,EAAgCrrC,EAAQqrC,8BAAgCC,EACxEF,EAAgCprC,EAAQorC,8BAAgC,GAaxED,EAAwBnrC,EAAQmrC,sBAAwBE,EAAgCD,EAMxFF,EAAoClrC,EAAQkrC,kCAAoC,OAAWW,EAG3FZ,EAA8BjrC,EAAQirC,4BAA8B,GAAKU,EAEzEX,EAAsBhrC,EAAQgrC,oBAAsBC,EAA8B,EAclFF,GAZuB/qC,EAAQ+nC,qBAAuB,SAA8B3a,GACpF,IAAIQ,GAAS,EAAI9Y,EAAMqY,QAAQC,GAC3B2e,EAAS1rC,MAAMC,QAAQstB,IAAU,EAAI9Y,EAAMmZ,iBAAiBL,GAAU,IAAIoe,YAAYpe,GACtFqe,EAAS5rC,MAAMC,QAAQstB,IAAU,EAAI9Y,EAAMkZ,iBAAiBJ,GAAU,IAAIse,YAAYte,GAGtFlX,EAAQu1B,EAAOne,MAAMqe,GAAkBJ,EAAO,GAAK,GACnD/kB,EAAqB,IAAd+kB,EAAO,GAAWE,EAAOne,OAHjB,GAGuCie,EAAO,IAAM,GAAKA,EAAOje,MAAMhrB,KAAKkzB,MAH3E,GAGgG+V,EAAO,IAAM,IAEhI,OAAO,IAAIhB,EAAKgB,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIr1B,EAAOsQ,IAG5DhnB,EAAQ+qC,KAAO,WACtB,SAASA,EAAKqB,EAAcC,EAAYC,EAAWC,EAAgB71B,EAAOsQ,IAlF9E,SAAyBvkB,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAmFxGoB,CAAgBC,KAAMooC,GAEtBpoC,KAAKypC,aAAeA,EACpBzpC,KAAK0pC,WAAaA,EAClB1pC,KAAK2pC,UAAYA,EACjB3pC,KAAK4pC,eAAiBA,EACtB5pC,KAAK+T,MAAQA,EACb/T,KAAKqkB,KAAOA,EAwDhB,OA7CAzlB,EAAawpC,EAAM,CAAC,CAChBhpC,IAAK,MACL9B,MAAO,SAAa6sB,GAChB,IAAI0f,OAAK,EACT,GAAI1f,GAAa,EAAG,CAChB,GAAIA,EAAY,OAAWA,EAAY,OAAWA,GAAa,MAM3D,OADA0f,IADAA,EAAK7pC,KAAK+T,MAAMoW,GAAagf,KACjBF,IAAuB9e,EAAY0e,GACxC7oC,KAAKqkB,KAAKwlB,GAGrB,GAAI1f,GAAa,MASb,OADA0f,IADAA,EAAK7pC,KAAK+T,MAAMg1B,GAA8B5e,EAAY,OAAUgf,MACxDF,IAAuB9e,EAAY0e,GACxC7oC,KAAKqkB,KAAKwlB,GAGrB,GAAI1f,EAAYnqB,KAAK2pC,UAOjB,OALAE,EAAKrB,EAAwBD,GAAqCpe,GAAa+e,GAC/EW,EAAK7pC,KAAK+T,MAAM81B,GAChBA,GAAM1f,GAAagf,EAAiBd,EAEpCwB,IADAA,EAAK7pC,KAAK+T,MAAM81B,KACJZ,IAAuB9e,EAAY0e,GACxC7oC,KAAKqkB,KAAKwlB,GAErB,GAAI1f,GAAa,QACb,OAAOnqB,KAAKqkB,KAAKrkB,KAAK4pC,gBAK9B,OAAO5pC,KAAK0pC,eAIbtB,EAjEe,K,kCC1F1B0B,EAAOzsC,QAAU,o8iD,kCCAjBF,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAAI6iB,EAAQ,EAAQ,MAwBpB9iB,EAAQ+D,QApBK,SAAS2oC,EAAOt+B,EAAGC,EAAGklB,IAFnC,SAAyB9wB,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAG5GoB,CAAgBC,KAAM+pC,GAEtB/pC,KAAKkR,KAAOiP,EAAMJ,KAAKG,OACvBlgB,KAAKyL,EAAIA,EACTzL,KAAK0L,EAAIA,EACT1L,KAAK4wB,OAASA,I,kCCdlBzzB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAGX,IAkBgCiO,EAlB5BhO,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEfoU,EAAU,EAAQ,MAIlBu2B,GAFQ,EAAQ,MAEJ,EAAQ,OAEpBC,EAAiB,EAAQ,MAEzBte,GAM4BpgB,EANa0+B,IAMQ1+B,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAJnF6G,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAMtB,IAAI63B,EAAW,WACX,SAASA,EAASprC,EAAQuwB,IAH9B,SAAyBvvB,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAIxGoB,CAAgBC,KAAMkqC,GAEtBlqC,KAAKlB,OAASA,EACdkB,KAAKqvB,QAAUA,EACfvwB,EAAO67B,OAAOtL,GA2OlB,OAxOAzwB,EAAasrC,EAAU,CAAC,CACpB9qC,IAAK,aACL9B,MAAO,SAAoBoU,GACnBA,EAAUoqB,cACV97B,KAAKmqC,+BAA+Bz4B,GACpC1R,KAAKoqC,kBAAkB14B,MAGhC,CACCtS,IAAK,oBACL9B,MAAO,SAA2BoU,GAC9B,IAAIsC,EAAQhU,KAER4vB,EAAW,WAYX,GAXIle,EAAUwC,WAAW1V,QACrBkT,EAAUwC,WAAW0L,SAAQ,SAAUyqB,GACnC,GAAIA,aAAiB1e,EAAgBvqB,QAAS,CAC1C,IAAIwQ,EAAQy4B,EAAMr4B,OAAOJ,MACzBoC,EAAMlV,OAAOwrC,eAAeD,EAAM79B,OAAQoF,EAAM4D,MAAO5D,EAAMC,KAAMD,EAAMqF,eAAgBrF,EAAMuF,iBAE/FnD,EAAMlV,OAAOyrC,UAAUF,EAAO34B,EAAUE,MAAM4D,UAKtD9D,EAAU8G,MAAO,CACjB,IAAIgyB,EAASx2B,EAAMqb,QAAQ4L,WAAWrD,IAAIlmB,EAAU8G,OACpD,GAAIgyB,EAAQ,CACR,IAAIC,GAAa,EAAIh3B,EAAQzI,qBAAqB0G,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMsD,QACzGw1B,EAAiC,iBAAjBF,EAAOz+B,OAAsBy+B,EAAOz+B,MAAQ,EAAIy+B,EAAOz+B,MAAQ0+B,EAAW1+B,MAC1F4+B,EAAmC,iBAAlBH,EAAOx+B,QAAuBw+B,EAAOx+B,OAAS,EAAIw+B,EAAOx+B,OAASy+B,EAAWz+B,OAC9F0+B,EAAS,GAAKC,EAAU,GACxB32B,EAAMlV,OAAOse,KAAK,EAAC,EAAI3J,EAAQ7I,yBAAyB8G,EAAU6G,gBAAgB,WAC9EvE,EAAMlV,OAAOonB,UAAUskB,EAAQ,IAAI/2B,EAAQtI,OAAO,EAAG,EAAGu/B,EAAQC,GAAUF,SAM1FG,EAAQl5B,EAAUkH,eAClBgyB,EAAMpsC,OACNwB,KAAKlB,OAAOse,KAAKwtB,EAAOhb,GAExBA,MAGT,CACCxwB,IAAK,iCACL9B,MAAO,SAAwCoU,GAC3C,IAAIwe,EAASlwB,KAET6qC,GAAkBn5B,EAAUE,MAAMmD,WAAWgJ,gBAAgBoc,iBAAmBzoB,EAAUE,MAAMmD,WAAWiI,gBAAgBxe,OAE3HssC,EAAuBp5B,EAAUE,MAAMsD,OAAO61B,MAAK,SAAU71B,GAC7D,OAAOA,EAAOqU,cAAgBlX,EAAQ6W,aAAa/P,OAASjE,EAAOoU,YAAY6Q,mBAG/EvK,EAAW,WACX,IAAIob,GAAyB,EAAI54B,EAAYqJ,iCAAiC/J,EAAU6G,aAAc7G,EAAUE,MAAMmD,WAAWkJ,gBAE7H4sB,GACA3a,EAAOpxB,OAAOse,KAAK,CAAC4tB,IAAyB,WACpCt5B,EAAUE,MAAMmD,WAAWgJ,gBAAgBoc,iBAC5CjK,EAAOpxB,OAAOkxB,KAAKte,EAAUE,MAAMmD,WAAWgJ,iBAGlDmS,EAAO+a,sBAAsBv5B,MAIrCA,EAAUE,MAAMsD,OAAO0K,SAAQ,SAAU1K,EAAQmU,GACzCnU,EAAOqU,cAAgBlX,EAAQ6W,aAAa/P,MAASjE,EAAOoU,YAAY6Q,iBACxEjK,EAAOgb,aAAah2B,EAAQmU,EAAM3X,EAAU6G,kBAKxD,GAAIsyB,GAAkBC,EAAsB,CACxC,IAAIF,EAAQl5B,EAAUM,OAASN,EAAUM,OAAO4G,eAAiB,GAC7DgyB,EAAMpsC,OACNwB,KAAKlB,OAAOse,KAAKwtB,EAAOhb,GAExBA,OAIb,CACCxwB,IAAK,wBACL9B,MAAO,SAA+BoU,GAClC,IAAI4f,EAAStxB,KAEb0R,EAAUE,MAAMmD,WAAWiI,gBAAgBmO,MAAM,GAAGxc,UAAUiR,SAAQ,SAAU5C,GACtC,QAAlCA,EAAgBgC,OAAOR,QAAoBxB,EAAgBgC,OAAOP,KAAKjgB,OACvE8yB,EAAO6Z,uBAAuBz5B,EAAWsL,GAClC,YAAY8C,KAAK9C,EAAgBgC,OAAOR,SAC/C8S,EAAO8Z,yBAAyB15B,EAAWsL,QAIxD,CACC5d,IAAK,yBACL9B,MAAO,SAAgCoU,EAAWqD,GAC9C,IAAIyD,EAAQxY,KAAKqvB,QAAQ4L,WAAWrD,IAAI7iB,EAAWiK,OAAOP,KAAK,IAC/D,GAAIjG,EAAO,CACP,IAAIoF,GAA4B,EAAIxL,EAAYoJ,oCAAoC9J,EAAUE,MAAMmD,WAAWsI,iBAAkB3L,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMsD,QACxLm2B,GAAsB,EAAIj5B,EAAYuJ,yBAAyB5G,EAAYyD,EAAOoF,GAClFvN,GAAW,EAAI+B,EAAYmJ,6BAA6BxG,EAAW1E,SAAUg7B,EAAqBztB,GAClGqS,GAAQ,EAAI7d,EAAYkJ,+BAA+BvG,EAAY1E,EAAUg7B,EAAqBztB,EAA2BlM,EAAUlF,QAEvI8+B,EAAWnrC,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAChE8/B,EAAWprC,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,GACnE1L,KAAKlB,OAAO0sC,aAAavb,EAAOzX,EAAO6yB,EAAqBC,EAAUC,MAG/E,CACCnsC,IAAK,2BACL9B,MAAO,SAAkCoU,EAAWqD,GAChD,IAAI6I,GAA4B,EAAIxL,EAAYoJ,oCAAoC9J,EAAUE,MAAMmD,WAAWsI,iBAAkB3L,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMsD,QACxLm2B,GAAsB,EAAIj5B,EAAYsJ,iCAAiC3G,EAAY6I,GACnFvN,GAAW,EAAI+B,EAAYmJ,6BAA6BxG,EAAW1E,SAAUg7B,EAAqBztB,GAClG6tB,EAAiB,IAAIh4B,EAAQtI,OAAOhL,KAAK2d,MAAMF,EAA0B/R,KAAOwE,EAAS5E,GAAItL,KAAK2d,MAAMF,EAA0B9R,IAAMuE,EAAS3E,GAAI2/B,EAAoBt/B,MAAOs/B,EAAoBr/B,QAEpM0iB,GAAW,EAAIsb,EAAU0B,eAAeh6B,EAAWqD,EAAWiK,OAAQysB,GAC1E,GAAI/c,EACA,OAAQA,EAASxd,MACb,KAAK84B,EAAU2B,cAAcC,gBAEzB5rC,KAAKlB,OAAO+sC,qBAAqBJ,EAAgB/c,GACjD,MACJ,KAAKsb,EAAU2B,cAAcG,gBAEzB9rC,KAAKlB,OAAOitC,qBAAqBN,EAAgB/c,MAKlE,CACCtvB,IAAK,eACL9B,MAAO,SAAsB4X,EAAQmU,EAAM2iB,GACvChsC,KAAKlB,OAAOyrC,WAAU,EAAI92B,EAAQ3I,oBAAoBkhC,EAAa3iB,GAAOnU,EAAOoU,eAEtF,CACClqB,IAAK,cACL9B,MAAO,SAAqBw9B,GACxB,IAAI1I,EAASpyB,KAEb,GAAI86B,EAAMppB,UAAUoqB,YAAa,CAC7B,IAAImQ,EAAWnR,EAAMiC,aACjBkP,IAAajsC,KAAKisC,WAClBjsC,KAAKlB,OAAOotC,WAAWpR,EAAMiC,cAC7B/8B,KAAKisC,SAAWA,GAGpB,IAAI54B,EAAaynB,EAAMppB,UAAUE,MAAM2F,UACpB,OAAflE,EACArT,KAAKlB,OAAOyY,UAAUujB,EAAMppB,UAAUlF,OAAOX,KAAOwH,EAAW2tB,gBAAgB,GAAG1jC,MAAOw9B,EAAMppB,UAAUlF,OAAOV,IAAMuH,EAAW2tB,gBAAgB,GAAG1jC,MAAO+V,EAAWkE,WAAW,WAC7K,OAAO6a,EAAO+Z,mBAAmBrR,MAGrC96B,KAAKmsC,mBAAmBrR,MAIrC,CACC17B,IAAK,qBACL9B,MAAO,SAA4Bw9B,GAC/B,IAAIsR,EAAwBC,EAAsBvR,GAC9CwR,EAAyB/uC,EAAe6uC,EAAuB,GAC/DG,EAAiBD,EAAuB,GACxCE,EAAyCF,EAAuB,GAChEG,EAAiBH,EAAuB,GACxCI,EAAsBJ,EAAuB,GAC7CK,EAA2BL,EAAuB,GAElDM,EAAoBC,EAAiB/R,GACrCgS,EAAqBvvC,EAAeqvC,EAAmB,GACvDG,EAAcD,EAAmB,GACjCE,EAAiBF,EAAmB,GAMxC9sC,KAAKmqC,+BAA+BrP,EAAMppB,WAE1C66B,EAAeU,KAAKC,GAActtB,QAAQ5f,KAAKmtC,YAAantC,MAE5DA,KAAKoqC,kBAAkBtP,EAAMppB,WAC7Bs7B,EAAeptB,QAAQ5f,KAAKotC,WAAYptC,MAKxC0sC,EAAoB9sB,QAAQ5f,KAAKmtC,YAAantC,MAE9C2sC,EAAyB/sB,QAAQ5f,KAAKmtC,YAAantC,MACnD+sC,EAAYntB,QAAQ5f,KAAKotC,WAAYptC,MAWrCwsC,EAAuC5sB,QAAQ5f,KAAKmtC,YAAantC,MAGjEysC,EAAeQ,KAAKC,GAActtB,QAAQ5f,KAAKmtC,YAAantC,QAEjE,CACCZ,IAAK,SACL9B,MAAO,SAAgBw9B,GAcnB,OAXI96B,KAAKqvB,QAAQtR,iBACb/d,KAAKlB,OAAOq0B,UAAUnzB,KAAKqvB,QAAQ5jB,EAAGzL,KAAKqvB,QAAQ3jB,EAAG1L,KAAKqvB,QAAQtjB,MAAO/L,KAAKqvB,QAAQrjB,OAAQhM,KAAKqvB,QAAQtR,iBAEhH/d,KAAKmtC,YAAYrS,GACJ96B,KAAKlB,OAAOuuC,gBAW1BnD,EAjPI,GAoPf7sC,EAAQ+D,QAAU8oC,EAGlB,IAAI2C,EAAmB,SAA0B/R,GAK7C,IAJA,IAAIiS,EAAc,GACdC,EAAiB,GAEjBxuC,EAASs8B,EAAMyB,SAAS/9B,OACnBf,EAAI,EAAGA,EAAIe,EAAQf,IAAK,CAC7B,IAAI4sC,EAAQvP,EAAMyB,SAAS9+B,GACvB4sC,EAAMiD,gBACNP,EAAYxuC,KAAK8rC,GAEjB2C,EAAezuC,KAAK8rC,GAG5B,MAAO,CAAC0C,EAAaC,IAGrBX,EAAwB,SAA+BvR,GAOvD,IANA,IAAIyR,EAAiB,GACjBC,EAAyC,GACzCC,EAAiB,GACjBC,EAAsB,GACtBC,EAA2B,GAC3BnuC,EAASs8B,EAAMwB,SAAS99B,OACnBf,EAAI,EAAGA,EAAIe,EAAQf,IAAK,CAC7B,IAAI4sC,EAAQvP,EAAMwB,SAAS7+B,GACvB4sC,EAAM34B,UAAU6H,gBAAkB8wB,EAAM34B,UAAUE,MAAM2E,QAAU,GAAK8zB,EAAM34B,UAAUuG,gBACnFoyB,EAAM34B,UAAUE,MAAMiG,OAAO8pB,MAAQ,EACrC4K,EAAehuC,KAAK8rC,GACbA,EAAM34B,UAAUE,MAAMiG,OAAO8pB,MAAQ,EAC5C8K,EAAeluC,KAAK8rC,GAEpBmC,EAAuCjuC,KAAK8rC,GAG5CA,EAAM34B,UAAUsH,aAChB0zB,EAAoBnuC,KAAK8rC,GAEzBsC,EAAyBpuC,KAAK8rC,GAI1C,MAAO,CAACkC,EAAgBC,EAAwCC,EAAgBC,EAAqBC,IAGrGO,EAAe,SAAsB/rC,EAAGD,GACxC,OAAIC,EAAEuQ,UAAUE,MAAMiG,OAAO8pB,MAAQzgC,EAAEwQ,UAAUE,MAAMiG,OAAO8pB,MACnD,EACAxgC,EAAEuQ,UAAUE,MAAMiG,OAAO8pB,MAAQzgC,EAAEwQ,UAAUE,MAAMiG,OAAO8pB,OACzD,EAGLxgC,EAAEuQ,UAAUqC,MAAQ7S,EAAEwQ,UAAUqC,MAAQ,GAAK,I,kCCpUxD5W,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQkwC,kCAAoClwC,EAAQquC,cAAgBruC,EAAQmwC,eAAiBnwC,EAAQowC,eAAiBpwC,EAAQqwC,sBAAwBrwC,EAAQsuC,mBAAgBztC,EAE9K,IAAIX,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAMllBgvC,GAFkBtiC,EAFD,EAAQ,OAIhB,EAAQ,OAIjB6G,EAAU7G,EAFD,EAAQ,OAIjBipB,EAAU,EAAQ,MAElBtY,EAAW3Q,EAAuBipB,GAElCniB,EAAQ,EAAQ,MAEpB,SAAS9G,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,SAASxL,EAAgBD,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAEhH,IAAIivC,EAAiB,8DACjBC,EAAoB,yCACpBC,EAAmB,gBACnBC,EAAoB,wDACpBC,EAA0B,yPAE1BrC,EAAgBtuC,EAAQsuC,cAAgB,CACxCC,gBAAiB,EACjBE,gBAAiB,GAGjB4B,EAAwBrwC,EAAQqwC,sBAAwB,CACxDxtB,OAAQ,EACR+tB,QAAS,GAGTC,EAAsB,CACtBriC,KAAM,IAAImQ,EAAS5a,QAAQ,MAC3B0K,IAAK,IAAIkQ,EAAS5a,QAAQ,MAC1BmwB,OAAQ,IAAIvV,EAAS5a,QAAQ,OAC7B6qB,MAAO,IAAIjQ,EAAS5a,QAAQ,QAC5B4qB,OAAQ,IAAIhQ,EAAS5a,QAAQ,SAG7BqsC,EAAiBpwC,EAAQowC,eAAiB,SAASA,EAAe5e,EAAYoC,GAC9ElxB,EAAgBC,KAAMytC,GAEtBztC,KAAKkR,KAAOy6B,EAAcC,gBAC1B5rC,KAAK6uB,WAAaA,EAClB7uB,KAAKixB,UAAYA,GAGjBuc,EAAiBnwC,EAAQmwC,eAAiB,SAASA,EAAe3e,EAAYsf,EAAO5c,EAAQX,GAC7F7wB,EAAgBC,KAAMwtC,GAEtBxtC,KAAKkR,KAAOy6B,EAAcG,gBAC1B9rC,KAAK6uB,WAAaA,EAClB7uB,KAAKmuC,MAAQA,EACbnuC,KAAKuxB,OAASA,EACdvxB,KAAK4wB,OAASA,GAoBdwd,GAjBgB/wC,EAAQquC,cAAgB,SAAuBh6B,EAAWxR,EAAMsM,GAChF,IAAIiS,EAAOve,EAAKue,KACZD,EAASte,EAAKse,OACdkB,EAASxf,EAAKwf,OAElB,MAAe,oBAAXlB,EACO6vB,EAAoB5vB,EAAMjS,IAAUkT,GACzB,aAAXlB,GAAqC,WAAZC,EAAK,GAE9B4vB,EAAoB,CAAC,aAAav1B,OAAOw1B,EAA4B7vB,EAAK0M,MAAM,KAAM3e,IAAUkT,GACrF,oBAAXlB,EACA+vB,EAAoB78B,EAAsB,aAAXgO,EAAwB6tB,EAAkC9uB,GAAQA,EAAMjS,GAC5F,aAAXgS,GAAqC,WAAZC,EAAK,GAC9B8vB,EAAoB78B,EAAW48B,EAA4Bf,EAAkC9uB,EAAK0M,MAAM,KAAM3e,QADlH,GAKW,SAAyBiS,EAAM+vB,EAAqBC,GAGtE,IAFA,IAAI5f,EAAa,GAERpxB,EAAI+wC,EAAqB/wC,EAAIghB,EAAKjgB,OAAQf,IAAK,CACpD,IAAIH,EAAQmhB,EAAKhhB,GACbixC,EAAaZ,EAAiBhuB,KAAKxiB,GACnCqxC,EAAiBrxC,EAAMsxC,YAAY,KACnCC,EAAS,IAAI38B,EAAQ9Q,QAAQstC,EAAapxC,EAAMuD,UAAU,EAAG8tC,GAAkBrxC,GAC/EwxC,EAAQJ,EAAa,IAAI1yB,EAAS5a,QAAQ9D,EAAMuD,UAAU8tC,EAAiB,IAAMlxC,IAAM+wC,EAAsB,IAAIxyB,EAAS5a,QAAQ,MAAQ3D,IAAMghB,EAAKjgB,OAAS,EAAI,IAAIwd,EAAS5a,QAAQ,QAAU,KACrMytB,EAAWtwB,KAAK,CAAEiX,MAAOq5B,EAAQ9f,KAAM+f,IAiB3C,IAdA,IAAIC,EAA2BlgB,EAAWtQ,KAAI,SAAUxd,GACpD,IAAIyU,EAAQzU,EAAMyU,MACduZ,EAAOhuB,EAAMguB,KAIjB,MAAO,CACHvZ,MAAOA,EAEPuZ,KAL8B,IAAf0f,EAAmB,EAAI1f,EAAOA,EAAK/f,iBAAiBy/B,GAAcA,EAAa,SASlGO,EAAoBD,EAAyB,GAAGhgB,KAC3C3wB,EAAK,EAAGA,EAAK2wC,EAAyBvwC,OAAQJ,IACnD,GAA0B,OAAtB4wC,EAA4B,CAC5B,IAAIC,EAASF,EAAyB3wC,GAAI2wB,KAC1C,GAAe,OAAXkgB,EAAiB,CAEjB,IADA,IAAI5I,EAAIjoC,EACoC,OAArC2wC,EAAyB1I,GAAGtX,MAC/BsX,IAKJ,IAHA,IAAI6I,EAAQ7I,EAAIjoC,EAAK,EAEjB+wC,GADgBJ,EAAyB1I,GAAGtX,KAChBigB,GAAqBE,EAC9C9wC,EAAKioC,EAAGjoC,IACX4wC,EAAoBD,EAAyB3wC,GAAI2wB,KAAOigB,EAAoBG,OAGhFH,EAAoBC,EAKhC,OAAOF,IAGPV,EAAsB,SAA6B5vB,EAAMjS,EAAQ4iC,GACjE,IAAIC,GAAQ,EAAI1B,EAAO2B,YAAY7wB,EAAK,IACpC8wB,EAAqB3B,EAAe9tB,KAAKrB,EAAK,IAC9C+wB,EAAgBD,GAAgC,OAAVF,GAAkBxB,EAAkB/tB,KAAKrB,EAAK,IACpFwS,EAAYue,EAA0B,OAAVH,EAAiBI,EAEjDL,EAAYC,EAAkB,GAAVlvC,KAAK0wB,GAAWwe,EAAO7iC,GAAU+iC,EAAqBG,EAAkBjxB,EAAK,GAAIjS,GAAUmjC,EAAqBlxB,EAAK,GAAIjS,GAAUijC,EAA2BtvC,KAAK0wB,GAAIrkB,GACvLgiC,EAAsBgB,EAAgB,EAAI,EAG1Cf,EAAatuC,KAAKC,KAAI,EAAI+R,EAAM2I,UAAU3a,KAAKqtB,IAAIyD,EAAUG,IAAMjxB,KAAKqtB,IAAIyD,EAAUC,IAAK/wB,KAAKqtB,IAAIyD,EAAUI,IAAMlxB,KAAKqtB,IAAIyD,EAAUE,KAAqB,EAAf3kB,EAAOT,MAA2B,EAAhBS,EAAOR,QAE1K,OAAO,IAAIyhC,EAAeW,EAAgB3vB,EAAM+vB,EAAqBC,GAAaxd,IAGlFsd,EAAsB,SAA6B78B,EAAW+M,EAAMjS,GACpE,IAAIsY,EAAIrG,EAAK,GAAGpe,MAAM2tC,GAClBG,EAAQrpB,IAAe,WAATA,EAAE,SACX5mB,IAAT4mB,EAAE,SAA6B5mB,IAAT4mB,EAAE,IACtB4oB,EAAsBxtB,OAASwtB,EAAsBO,QACnDrd,EAAS,GACTW,EAAS,GAETzM,SAEa5mB,IAAT4mB,EAAE,KACF8L,EAAOnlB,GAAI,EAAI6oB,EAAQ1jB,kCAAkCc,EAAWoT,EAAE,GAAIA,EAAE,IAAI9V,iBAAiBxC,EAAOT,aAG/F7N,IAAT4mB,EAAE,KACF8L,EAAOllB,GAAI,EAAI4oB,EAAQ1jB,kCAAkCc,EAAWoT,EAAE,GAAIA,EAAE,IAAI9V,iBAAiBxC,EAAOR,SAIxG8Y,EAAE,GACFyM,EAAO9lB,EAAIyiC,EAAoBppB,EAAE,GAAGlkB,oBACpB1C,IAAT4mB,EAAE,KACTyM,EAAO9lB,GAAI,EAAI6oB,EAAQ1jB,kCAAkCc,EAAWoT,EAAE,GAAIA,EAAE,KAG5EA,EAAE,IACFyM,EAAO7lB,EAAIwiC,EAAoBppB,EAAE,IAAIlkB,oBACpB1C,IAAV4mB,EAAE,MACTyM,EAAO7lB,GAAI,EAAI4oB,EAAQ1jB,kCAAkCc,EAAWoT,EAAE,IAAKA,EAAE,OAIrF,IAAI8qB,EAAiB,CACjBnkC,OAAgBvN,IAAbqzB,EAAO9lB,EAAkBe,EAAOT,MAAQ,EAAIwlB,EAAO9lB,EAAEuD,iBAAiBxC,EAAOT,OAChFL,OAAgBxN,IAAbqzB,EAAO7lB,EAAkBc,EAAOR,OAAS,EAAIulB,EAAO7lB,EAAEsD,iBAAiBxC,EAAOR,SAEjF6jC,EAAiBC,EAAgBhrB,GAAKA,EAAE,IAAM,kBAAmBqpB,EAAOyB,EAAgBhf,EAAQpkB,GAEpG,OAAO,IAAIghC,EAAeY,EAAgB3vB,EAAMqG,EAAI,EAAI,EAAG3kB,KAAKC,IAAIyvC,EAAepkC,EAAGokC,EAAenkC,IAAKyiC,EAAOyB,EAAgBC,IAGjIJ,EAA6B,SAAoCM,EAAQvjC,GACzE,IAAIT,EAAQS,EAAOT,MACfC,EAASQ,EAAOR,OAChBgkC,EAAqB,GAARjkC,EACbkkC,EAAuB,GAATjkC,EAEdkkC,GADa/vC,KAAKqtB,IAAIzhB,EAAQ5L,KAAKgwC,IAAIJ,IAAW5vC,KAAKqtB,IAAIxhB,EAAS7L,KAAKiwC,IAAIL,KAC7C,EAEhC3e,EAAK4e,EAAa7vC,KAAKgwC,IAAIJ,GAAUG,EACrC7e,EAAK4e,EAAc9vC,KAAKiwC,IAAIL,GAAUG,EAI1C,MAAO,CAAE9e,GAAIA,EAAIF,GAHRnlB,EAAQqlB,EAGQC,GAAIA,EAAIF,GAFxBnlB,EAASqlB,IAKlBgf,EAAgB,SAAuB7jC,GACvC,OAAOrM,KAAKmwC,KAAK9jC,EAAOT,MAAQ,IAAK,EAAIoG,EAAM2I,UAAUtO,EAAOT,MAAOS,EAAOR,QAAU,KAGxF0jC,EAAoB,SAA2BrmB,EAAM7c,GACrD,OAAQ6c,GACJ,IAAK,SACL,IAAK,SACD,OAAOomB,EAA2B,EAAGjjC,GACzC,IAAK,OACL,IAAK,WACD,OAAOijC,EAA2BtvC,KAAK0wB,GAAK,EAAGrkB,GACnD,IAAK,QACL,IAAK,UACD,OAAOijC,EAA2B,EAAItvC,KAAK0wB,GAAK,EAAGrkB,GACvD,IAAK,YACL,IAAK,YACL,IAAK,iBACL,IAAK,iBACD,OAAOijC,EAA2BtvC,KAAK0wB,GAAKwf,EAAc7jC,GAASA,GACvE,IAAK,WACL,IAAK,WACL,IAAK,kBACL,IAAK,kBACD,OAAOijC,EAA2BtvC,KAAK0wB,GAAKwf,EAAc7jC,GAASA,GACvE,IAAK,cACL,IAAK,cACL,IAAK,eACL,IAAK,eACD,OAAOijC,EAA2BY,EAAc7jC,GAASA,GAC7D,IAAK,eACL,IAAK,eACL,IAAK,cACL,IAAK,cACD,OAAOijC,EAA2B,EAAItvC,KAAK0wB,GAAKwf,EAAc7jC,GAASA,GAC3E,IAAK,MACL,IAAK,YACL,QACI,OAAOijC,EAA2BtvC,KAAK0wB,GAAIrkB,KAInDmjC,EAAuB,SAA8BN,EAAO7iC,GAC5D,IAAI+jC,EAAmBlB,EAAMzwB,MAAM,KAAKL,IAAIlN,YACxCm/B,EAAoBjzC,EAAegzC,EAAkB,GACrD1kC,EAAO2kC,EAAkB,GACzB1kC,EAAM0kC,EAAkB,GAExBC,EAAQ5kC,EAAO,IAAMW,EAAOT,OAASD,EAAM,IAAMU,EAAOR,QAE5D,OAAOyjC,EAA2BtvC,KAAKuwC,KAAKp/B,MAAMm/B,GAAS,EAAIA,GAAStwC,KAAK0wB,GAAK,EAAGrkB,IAGrFmkC,EAAa,SAAoBnkC,EAAQf,EAAGC,EAAGklC,GAI/C,MAHc,CAAC,CAAEnlC,EAAG,EAAGC,EAAG,GAAK,CAAED,EAAG,EAAGC,EAAGc,EAAOR,QAAU,CAAEP,EAAGe,EAAOT,MAAOL,EAAG,GAAK,CAAED,EAAGe,EAAOT,MAAOL,EAAGc,EAAOR,SAGpG8gB,QAAO,SAAU+jB,EAAMC,GAClC,IAAIC,GAAI,EAAI5+B,EAAM2I,UAAUrP,EAAIqlC,EAAOrlC,EAAGC,EAAIolC,EAAOplC,GACrD,OAAIklC,EAAUG,EAAIF,EAAKG,gBAAkBD,EAAIF,EAAKG,iBACvC,CACHC,cAAeH,EACfE,gBAAiBD,GAIlBF,IACR,CACCG,gBAAiBJ,EAAUM,KAAYA,IACvCD,cAAe,OAChBA,eAGHnB,EAAkB,SAAyBqB,EAAQhD,EAAO5c,EAAQX,EAAQpkB,GAC1E,IAAIf,EAAI8lB,EAAO9lB,EACXC,EAAI6lB,EAAO7lB,EACX0lC,EAAK,EACLC,EAAK,EAET,OAAQF,GACJ,IAAK,eAGGhD,IAAUT,EAAsBxtB,OAChCkxB,EAAKC,EAAKlxC,KAAKC,IAAID,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,OAAQ5L,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,SACtFmiC,IAAUT,EAAsBO,UACvCmD,EAAKjxC,KAAKC,IAAID,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,QAC/CslC,EAAKlxC,KAAKC,IAAID,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,UAEnD,MAEJ,IAAK,iBAGD,GAAImiC,IAAUT,EAAsBxtB,OAChCkxB,EAAKC,EAAKlxC,KAAKC,KAAI,EAAI+R,EAAM2I,UAAUrP,EAAGC,IAAI,EAAIyG,EAAM2I,UAAUrP,EAAGC,EAAIc,EAAOR,SAAS,EAAImG,EAAM2I,UAAUrP,EAAIe,EAAOT,MAAOL,IAAI,EAAIyG,EAAM2I,UAAUrP,EAAIe,EAAOT,MAAOL,EAAIc,EAAOR,cACjL,GAAImiC,IAAUT,EAAsBO,QAAS,CAEhD,IAAIpuB,EAAI1f,KAAKC,IAAID,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,SAAW7L,KAAKC,IAAID,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,QACnG+kC,EAASH,EAAWnkC,EAAQf,EAAGC,GAAG,GAEtC2lC,EAAKxxB,GADLuxB,GAAK,EAAIj/B,EAAM2I,UAAUg2B,EAAOrlC,EAAIA,GAAIqlC,EAAOplC,EAAIA,GAAKmU,IAG5D,MAEJ,IAAK,gBAEGsuB,IAAUT,EAAsBxtB,OAChCkxB,EAAKC,EAAKlxC,KAAKiN,IAAIjN,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,OAAQ5L,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,SACtFmiC,IAAUT,EAAsBO,UACvCmD,EAAKjxC,KAAKiN,IAAIjN,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,QAC/CslC,EAAKlxC,KAAKiN,IAAIjN,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,UAEnD,MAEJ,IAAK,kBAGD,GAAImiC,IAAUT,EAAsBxtB,OAChCkxB,EAAKC,EAAKlxC,KAAKiN,KAAI,EAAI+E,EAAM2I,UAAUrP,EAAGC,IAAI,EAAIyG,EAAM2I,UAAUrP,EAAGC,EAAIc,EAAOR,SAAS,EAAImG,EAAM2I,UAAUrP,EAAIe,EAAOT,MAAOL,IAAI,EAAIyG,EAAM2I,UAAUrP,EAAIe,EAAOT,MAAOL,EAAIc,EAAOR,cACjL,GAAImiC,IAAUT,EAAsBO,QAAS,CAEhD,IAAIqD,EAAKnxC,KAAKiN,IAAIjN,KAAKqtB,IAAI9hB,GAAIvL,KAAKqtB,IAAI9hB,EAAIc,EAAOR,SAAW7L,KAAKiN,IAAIjN,KAAKqtB,IAAI/hB,GAAItL,KAAKqtB,IAAI/hB,EAAIe,EAAOT,QACpGwlC,EAAUZ,EAAWnkC,EAAQf,EAAGC,GAAG,GAEvC2lC,EAAKC,GADLF,GAAK,EAAIj/B,EAAM2I,UAAUy2B,EAAQ9lC,EAAIA,GAAI8lC,EAAQ7lC,EAAIA,GAAK4lC,IAG9D,MAEJ,QAEIF,EAAKxgB,EAAOnlB,GAAK,EACjB4lC,OAAkBnzC,IAAb0yB,EAAOllB,EAAkBklB,EAAOllB,EAAI0lC,EAIjD,MAAO,CACH3lC,EAAG2lC,EACH1lC,EAAG2lC,IAIP9D,EAAoClwC,EAAQkwC,kCAAoC,SAA2C9uB,GAC3H,IAAI0vB,EAAQ,GACRvd,EAAS,GACTugB,EAAS,GACT9gC,EAAW,GACXmhC,EAAM,EAENz5B,EAAW,wFAEX05B,EAAS,4CAETC,EAAqBjzB,EAAK+yB,GAAKnxC,MAAM0X,GACrC25B,GACAF,IAGJ,IAAIG,EAAmBlzB,EAAK+yB,GAAKnxC,MARV,qGASnBsxC,IACAxD,EAAQwD,EAAiB,IAAM,GAEhB,aADfR,EAASQ,EAAiB,IAAM,IAE5BR,EAAS,eACS,UAAXA,IACPA,EAAS,mBAEbK,KAGJ,IAAII,EAAmBnzB,EAAK+yB,GAAKnxC,MAAMoxC,GACnCG,GACAJ,IAGJ,IAAIK,EAAmBpzB,EAAK+yB,GAAKnxC,MAAM0X,GACnC85B,GACAL,IAGJ,IAAIM,EAAiBrzB,EAAK+yB,GAAKnxC,MAAMoxC,GACjCK,GACAN,IAGJ,IAAIO,EAAgBF,GAAoBH,EACpCK,GAAiBA,EAAc,KAC/B1hC,EAAW0hC,EAAc,IAAM,QAAQjyB,KAAKiyB,EAAc,IAAM,KAAO,IACnEA,EAAc,KACd1hC,GAAY,IAAM0hC,EAAc,IAAM,QAAQjyB,KAAKiyB,EAAc,IAAM,KAAO,MAItF,IAAIC,EAAcF,GAAkBF,EAiBpC,OAhBII,IACAphB,EAASohB,EAAY,GAChBA,EAAY,KACbphB,GAAU,QAIdvgB,GAAa89B,GAAUvd,GAAWugB,IAClCvgB,EAASvgB,EACTA,EAAW,IAGXA,IACAA,EAAW,MAAQA,GAGhB,CAAC,CAAC89B,EAAOgD,EAAQvgB,EAAQvgB,GAAU4hC,QAAO,SAAUh4B,GACvD,QAASA,KACVwY,KAAK,MAAM3Z,OAAO2F,EAAK0M,MAAMqmB,KAGhClD,EAA8B,SAAqC7vB,GACnE,OAAOA,EAAKF,KAAI,SAAU/I,GACtB,OAAOA,EAAMnV,MAAM0tC,MAGtBxvB,KAAI,SAAU9M,EAAGsC,GACd,IAAKtC,EACD,OAAOgN,EAAK1K,GAGhB,OAAQtC,EAAE,IACN,IAAK,OACD,OAAOA,EAAE,GAAK,MAClB,IAAK,KACD,OAAOA,EAAE,GAAK,QAClB,IAAK,aACD,MAAa,MAATA,EAAE,GACKA,EAAE,GAAK,IAAMA,EAAE,GAEnBA,EAAE,GAAK,IAAyB,IAAnBJ,WAAWI,EAAE,IAAY,U,kCCzb7DtU,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEX,IAAI40C,EAAQ,uCAEK70C,EAAQiyC,WAAa,SAAoBD,GACtD,IAAIhvC,EAAQgvC,EAAMhvC,MAAM6xC,GAExB,GAAI7xC,EAAO,CACP,IAAI/C,EAAQ+T,WAAWhR,EAAM,IAC7B,OAAQA,EAAM,GAAGO,eACb,IAAK,MACD,OAAOT,KAAK0wB,GAAKvzB,EAAQ,IAC7B,IAAK,OACD,OAAO6C,KAAK0wB,GAAK,IAAMvzB,EAC3B,IAAK,MACD,OAAOA,EACX,IAAK,OACD,OAAiB,EAAV6C,KAAK0wB,GAASvzB,GAIjC,OAAO,O,kCCtBXH,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQu9B,YAAcv9B,EAAQm9B,oBAAiBt8B,EAE/C,IAAIX,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEfoU,EAAU,EAAQ,MAElB0+B,EAAS,EAAQ,MAIjBC,EAAmB/mC,EAFD,EAAQ,OAI1B8G,EAAQ,EAAQ,MAEhBC,EAAc,EAAQ,MAItBigC,EAAmBhnC,EAFD,EAAQ,OAI1BinC,EAAqB,EAAQ,MAEjC,SAASjnC,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAIvF,IAEIivB,EAAiBn9B,EAAQm9B,eAAiB,WAC1C,SAASA,EAAepD,EAAS/H,EAASI,EAAQ8iB,EAAYC,IALlE,SAAyB1yC,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAMxGoB,CAAgBC,KAAMw6B,GAEtBx6B,KAAKyyC,iBAAmBrb,EACxBp3B,KAAK0yC,iBAAmB,GACxB1yC,KAAK2yC,WAAaJ,EAClBvyC,KAAK4yC,aAAeL,EACpBvyC,KAAKyvB,OAASA,EACdzvB,KAAKqvB,QAAUA,EACfrvB,KAAKwyC,SAAWA,EAChBxyC,KAAK8T,eAAiB,IAAIs+B,EAAiBhxC,QAAQiuB,EAASI,EAAQ1I,QACpE/mB,KAAK6yC,kBAAoB,CACrBC,SAAU,GACVC,WAAY,GAGhB/yC,KAAKkN,gBAAkBlN,KAAK82B,UAAUM,EAAQ7iB,cAAcrH,iBAiPhE,OA9OAtO,EAAa47B,EAAgB,CAAC,CAC1Bp7B,IAAK,kBACL9B,MAAO,SAAyB+O,GAC5B,IAAI2H,EAAQhU,KAEZ,GAAIA,KAAK4yC,cAAgBvmC,EAAM,CAC3B,IAAIuF,EAAQvF,EAAKuF,MACjB2U,QAAQ4U,KAAI,EAAI/oB,EAAYiJ,sBAAsBzJ,EAAMoL,iBAAiBuB,KAAI,SAAUvB,GACnF,MAA+B,QAA3BA,EAAgBwB,OACTxK,EAAMF,eAAek/B,YAAYh2B,EAAgByB,KAAK,IAAI8I,MAAK,SAAUjN,GAC5E,OAAOA,GAA0B,iBAAZA,EAAIE,IAAmB,QAAUF,EAAIE,IAAM,KAAO,UACxEkN,OAAM,SAAUtB,GACX,KAKLG,QAAQC,QAAQ,GAAKxJ,EAAgB0C,OAAS1C,EAAgBwB,OAAS,IAAMxB,EAAgByB,KAAKgU,KAAK,KAAO,SACrHlL,MAAK,SAAU0rB,GACXA,EAAiBz0C,OAAS,IAE1BoT,EAAMmM,gBAAkB,IAE5BnM,EAAMoL,gBAAkBi2B,EAAiBxgB,KAAK,QAG9CpmB,aAAgB6mC,kBAChBlzC,KAAK8T,eAAek/B,YAAY3mC,EAAKmO,KAAK+M,MAAK,SAAUjN,GACrD,GAAIA,GAAOjO,aAAgB6mC,kBAAoB7mC,EAAKmqB,WAAY,CAC5D,IAAIA,EAAanqB,EAAKmqB,WAClB2c,GAAc,EAAIhhC,EAAM4I,eAAe1O,EAAKuF,MAAO0I,EAAIwc,WAAU,IACrEN,EAAWO,aAAaoc,EAAa9mC,OAE1Cqb,OAAM,SAAUtB,GACX,QAOrB,CACChnB,IAAK,cACL9B,MAAO,SAAqB0P,GACxB,IAAIkjB,EAASlwB,KAEb,OAAOumB,QAAQ4U,IAAIz9B,MAAMopB,KAAK9Z,EAASomC,aAAa70B,KAAI,SAAU80B,GAC9D,OAAIA,EAAMC,KACCtsB,MAAMqsB,EAAMC,MAAM/rB,MAAK,SAAUgsB,GACpC,OAAOA,EAAInvB,UACZmD,MAAK,SAAUnD,GACd,OAAOovB,EAA8BpvB,EAAMivB,EAAMC,SAClD5rB,OAAM,SAAUtB,GAIf,MAAO,MAGRqtB,EAAcJ,EAAOrmC,OAC5Bua,MAAK,SAAUmsB,GACf,OAAOA,EAAM5mB,QAAO,SAAU6mB,EAAK9hC,GAC/B,OAAO8hC,EAAI76B,OAAOjH,KACnB,OACJ0V,MAAK,SAAUmsB,GACd,OAAOntB,QAAQ4U,IAAIuY,EAAMn1B,KAAI,SAAU1M,GACnC,OAAOmV,MAAMnV,EAAK+hC,QAAQ,GAAGp5B,KAAK+M,MAAK,SAAUyR,GAC7C,OAAOA,EAAS6a,UACjBtsB,MAAK,SAAUssB,GACd,OAAO,IAAIttB,SAAQ,SAAUC,EAASiB,GAClC,IAAIwR,EAAS,IAAIC,WACjBD,EAAOvS,QAAUe,EACjBwR,EAAOxS,OAAS,WAEZ,IAAIyD,EAAS+O,EAAO/O,OACpB1D,EAAQ0D,IAEZ+O,EAAOE,cAAc0a,SAE1BtsB,MAAK,SAAUusB,GAEd,OADAjiC,EAAKkiC,SAAS74B,YAAY,MAAO,QAAU44B,EAAU,MAC9C,eAAiBjiC,EAAKkiC,SAASC,QAAU,cAGzDzsB,MAAK,SAAU0sB,GACd,IAAIriC,EAAQ5E,EAASuY,cAAc,SACnC3T,EAAMsiC,YAAcD,EAAQxhB,KAAK,MACjCvC,EAAOhjB,gBAAgBuY,YAAY7T,QAG5C,CACCxS,IAAK,qBACL9B,MAAO,SAA4B+O,GAC/B,IAAIilB,EAAStxB,KAEb,GAAIA,KAAK2yC,YAActmC,aAAgB8nC,kBAAmB,CACtD,IAAI75B,EAAMjO,EAAKkI,cAAcgR,cAAc,OAC3C,IAEI,OADAjL,EAAIE,IAAMnO,EAAK8Z,YACR7L,EACT,MAAO8L,GACD,GAMZ,GAAI/Z,aAAgB+nC,kBAAmB,CACnC,IAAIC,EAAahoC,EAAKyqB,WAAU,GAC5Bnc,EAAY25B,IAChBD,EAAWr6B,aAAa,uCAAwCW,GAEhE,IAAI45B,GAAe,EAAI9gC,EAAQvI,aAAamB,EAAM,EAAG,GACjDN,EAAQwoC,EAAaxoC,MACrBC,EAASuoC,EAAavoC,OAsC1B,OApCAhM,KAAK8T,eAAe0gC,MAAM75B,GAAa85B,EAAyBpoC,EAAMrM,KAAKqvB,SAAS9H,MAAK,SAAUra,GAC/F,OAAOokB,EAAOkhB,SAAStlC,EAAiB,CACpCwnC,MAAOpjB,EAAOjC,QAAQqlB,MACtBC,WAAYrjB,EAAOjC,QAAQslB,WAC3B52B,gBAAiB,UACjBtD,OAAQ,KACR6e,aAAchI,EAAOjC,QAAQiK,aAC7Bsb,QAAStjB,EAAOjC,QAAQulB,QACxBlc,MAAOpH,EAAOjC,QAAQqJ,MACtB0C,gBAAiB9J,EAAOjC,QAAQ+L,gBAChC9L,MAAOgC,EAAOjC,QAAQC,MACtB8K,uBAAwB9I,EAAOjC,QAAQ+K,uBACvCya,QAASvjB,EAAOjC,QAAQwlB,QACxB/1C,OAAQ,IAAIuzC,EAAiBjxC,QAC7B2K,MAAOA,EACPC,OAAQA,EACRP,EAAG,EACHC,EAAG,EACH2rB,YAAanqB,EAAgBqH,cAAcD,YAAY4X,WACvDoL,aAAcpqB,EAAgBqH,cAAcD,YAAYwgC,YACxD5oC,QAASgB,EAAgBqH,cAAcD,YAAYE,YACnDrI,QAASe,EAAgBqH,cAAcD,YAAYG,aACpD6c,EAAO7B,OAAO4a,MAAM1vB,OACxB4M,MAAK,SAAU9M,GACd,OAAO,IAAI8L,SAAQ,SAAUC,EAASiB,GAClC,IAAIstB,EAAe/nC,SAASuY,cAAc,OAC1CwvB,EAAatuB,OAAS,WAClB,OAAOD,EAAQ/L,IAEnBs6B,EAAaruB,QAAUe,EACvBstB,EAAav6B,IAAMC,EAAO0L,YACtBkuB,EAAW7d,YACX6d,EAAW7d,WAAWO,cAAa,EAAI5kB,EAAM4I,eAAe1O,EAAKkI,cAAcD,YAAYI,iBAAiBrI,GAAO0oC,GAAeV,SAIvIA,EAGX,GAAIhoC,aAAgB2oC,kBAAoB3oC,EAAKgnC,OAAShnC,EAAKgnC,MAAM4B,SAAU,CACvE,IAAIC,EAAM,GAAG/pB,MAAM+I,KAAK7nB,EAAKgnC,MAAM4B,SAAU,GAAGnoB,QAAO,SAAUooB,EAAKC,GAClE,OAAOD,EAAMC,EAAKnB,UACnB,IACCpiC,EAAQvF,EAAKyqB,WAAU,GAE3B,OADAllB,EAAMsiC,YAAcgB,EACbtjC,EAGX,OAAOvF,EAAKyqB,WAAU,KAE3B,CACC13B,IAAK,YACL9B,MAAO,SAAmB+O,GACtB,IAAI+oC,EAAQ/oC,EAAKgpC,WAAaC,KAAKC,UAAYvoC,SAASsf,eAAejgB,EAAKmpC,WAAax1C,KAAKy1C,mBAAmBppC,GAE7G0a,EAAS1a,EAAKkI,cAAcD,YAC5B1C,EAAQvF,aAAgB0a,EAAO6U,YAAc7U,EAAOrS,iBAAiBrI,GAAQ,KAC7EqpC,EAAcrpC,aAAgB0a,EAAO6U,YAAc7U,EAAOrS,iBAAiBrI,EAAM,WAAa,KAC9FspC,EAAatpC,aAAgB0a,EAAO6U,YAAc7U,EAAOrS,iBAAiBrI,EAAM,UAAY,KAE5FrM,KAAKyyC,mBAAqBpmC,GAAQ+oC,aAAiBruB,EAAO6U,cAC1D57B,KAAK41C,uBAAyBR,GAG9BA,aAAiBruB,EAAO8uB,iBACxBC,EAAuBV,GAM3B,IAHA,IAAItC,GAAW,EAAIR,EAAmByD,mBAAmBnkC,EAAO5R,KAAK6yC,mBACjEmD,GAAgB,EAAI1D,EAAmB2D,sBAAsB5pC,EAAMqpC,EAAa11C,KAAK6yC,mBAEhFxI,EAAQh+B,EAAK2qB,WAAYqT,EAAOA,EAAQA,EAAM3O,YAC/C2O,EAAMgL,WAAaC,KAAKY,eAAmC,WAAnB7L,EAAMxO,UAEjDwO,EAAMhyB,aApNA,4BAoN0E,mBAAhCrY,KAAKqvB,QAAQ8mB,gBAE7Dn2C,KAAKqvB,QAAQ8mB,eAAe9L,KACpBrqC,KAAK2yC,YAAiC,UAAnBtI,EAAMxO,UAC1BuZ,EAAM3vB,YAAYzlB,KAAK82B,UAAUuT,IAK7C,IAAI+L,GAAe,EAAI9D,EAAmB2D,sBAAsB5pC,EAAMspC,EAAY31C,KAAK6yC,mBAGvF,IAFA,EAAIP,EAAmB+D,aAAavD,EAAU9yC,KAAK6yC,mBAE/CxmC,aAAgB0a,EAAO6U,aAAewZ,aAAiBruB,EAAO6U,YAc9D,OAbI8Z,GACA11C,KAAKs2C,gBAAgBC,EAAoBlqC,EAAM+oC,EAAOM,EAAaM,EAAeQ,IAElFb,GACA31C,KAAKs2C,gBAAgBC,EAAoBlqC,EAAM+oC,EAAOO,EAAYS,EAAcK,KAEhF7kC,IAAS5R,KAAK2yC,YAAgBtmC,aAAgB+nC,oBAC9C,EAAIjiC,EAAM4I,eAAenJ,EAAOwjC,GAEpCp1C,KAAKs2C,gBAAgBlB,GACE,IAAnB/oC,EAAKqqC,WAAuC,IAApBrqC,EAAKsqC,YAC7B32C,KAAK0yC,iBAAiBn0C,KAAK,CAAC62C,EAAO/oC,EAAKsqC,WAAYtqC,EAAKqqC,YAErDrqC,EAAKwvB,UACT,IAAK,SACI77B,KAAK2yC,YACNiE,EAAoBvqC,EAAM+oC,GAE9B,MACJ,IAAK,WACL,IAAK,SACDA,EAAM93C,MAAQ+O,EAAK/O,MACnB,MACJ,IAAK,QACG+O,EAAKspB,SAELyf,EAAMp7B,aAAa,WAAW,GAK9C,OAAOo7B,MAIR5a,EAlQmC,GAqQ1CiZ,EAAgB,SAAuBJ,EAAOrmC,GAE9C,OAAQqmC,EAAM4B,SAAWv3C,MAAMopB,KAAKusB,EAAM4B,UAAY,IAAIhD,QAAO,SAAUkD,GACvE,OAAOA,EAAKjkC,OAAS2lC,QAAQC,kBAC9Bv4B,KAAI,SAAU42B,GAGb,IAFA,IAAI36B,GAAM,EAAIpI,EAAYiJ,sBAAsB85B,EAAKvjC,MAAMuJ,iBAAiB,QACxEy4B,EAAU,GACLn2C,EAAI,EAAGA,EAAI+c,EAAIhc,OAAQf,IAC5B,GAAsB,QAAlB+c,EAAI/c,GAAG+gB,QAAoBhE,EAAI/c,EAAI,IAA4B,WAAtB+c,EAAI/c,EAAI,GAAG+gB,OAAqB,CACzE,IAAIrd,EAAI6L,EAASuY,cAAc,KAC/BpkB,EAAEmyC,KAAO94B,EAAI/c,GAAGghB,KAAK,GACjBzR,EAASC,MACTD,EAASC,KAAKwY,YAAYtkB,GAG9B,IAAI0Q,EAAO,CACP2I,IAAKrZ,EAAEmyC,KACPyD,OAAQv8B,EAAI/c,EAAI,GAAGghB,KAAK,IAE5Bm1B,EAAQr1C,KAAKsT,GAIrB,MAAO,CAGH+hC,QAASA,EAAQ3B,QAAO,SAAUpgC,GAC9B,MAAQ,SAASiO,KAAKjO,EAAKklC,WAG/BhD,SAAUoB,EAAKvjC,UAEpBqgC,QAAO,SAAUpgC,GAChB,OAAOA,EAAK+hC,QAAQp1C,WAIxBg1C,EAAgC,SAAuCpvB,EAAM4yB,GAC7E,IAAIC,EAAMjqC,SAASkqC,eAAeC,mBAAmB,IACjDC,EAAOpqC,SAASuY,cAAc,QAElC6xB,EAAK9D,KAAO0D,EACZ,IAAIplC,EAAQ5E,SAASuY,cAAc,SAUnC,OARA3T,EAAMsiC,YAAc9vB,EAChB6yB,EAAII,MACJJ,EAAII,KAAK5xB,YAAY2xB,GAErBH,EAAIhqC,MACJgqC,EAAIhqC,KAAKwY,YAAY7T,GAGlBA,EAAMyhC,MAAQI,EAAc7hC,EAAMyhC,MAAO4D,GAAO,IASvDL,EAAsB,SAA6Bn8B,EAAQ68B,GAC3D,IACI,GAAIA,EAAc,CACdA,EAAavrC,MAAQ0O,EAAO1O,MAC5BurC,EAAatrC,OAASyO,EAAOzO,OAC7B,IAAIga,EAAMvL,EAAOwL,WAAW,MACxBsxB,EAAYD,EAAarxB,WAAW,MACpCD,EACAuxB,EAAUC,aAAaxxB,EAAIwB,aAAa,EAAG,EAAG/M,EAAO1O,MAAO0O,EAAOzO,QAAS,EAAG,GAE/EurC,EAAUrxB,UAAUzL,EAAQ,EAAG,IAGzC,MAAO2L,MAGTmwB,EAAsB,SAA6BlqC,EAAM+oC,EAAOxjC,EAAO6lC,EAAcC,GACrF,GAAK9lC,GAAUA,EAAM+lC,SAA6B,SAAlB/lC,EAAM+lC,SAAwC,qBAAlB/lC,EAAM+lC,SAAoD,SAAlB/lC,EAAM+C,QAA1G,CAIA,IAAIijC,EAA2BxC,EAAM7gC,cAAcgR,cAAc,4BAGjE,IAFA,EAAIpT,EAAM4I,eAAenJ,EAAOgmC,GAE5BH,EAEA,IADA,IAAI9sB,EAAM8sB,EAAaj5C,OACdf,EAAI,EAAGA,EAAIktB,EAAKltB,IAAK,CAC1B,IAAIwd,EAAOw8B,EAAah6C,GACxB,OAAQwd,EAAK/J,MACT,KAAKohC,EAAmBuF,yBAAyBC,MAC7C,IAAIx9B,EAAM86B,EAAM7gC,cAAcgR,cAAc,OAC5CjL,EAAIE,KAAM,EAAIpI,EAAYiJ,sBAAsB,OAASJ,EAAK3d,MAAQ,KAAK,GAAGmhB,KAAK,GACnFnE,EAAI1I,MAAM2E,QAAU,IACpBqhC,EAAyBnyB,YAAYnL,GACrC,MACJ,KAAKg4B,EAAmBuF,yBAAyBE,KAC7CH,EAAyBnyB,YAAY2vB,EAAM7gC,cAAc+X,eAAerR,EAAK3d,SAc7F,OARAs6C,EAAyBI,UAAYC,EAAmC,IAAMC,EAC9E9C,EAAM4C,WAAaN,IAAclB,EAAgB,IAAMyB,EAAmC,IAAMC,EAC5FR,IAAclB,EACdpB,EAAM+C,aAAaP,EAA0BxC,EAAMpe,YAEnDoe,EAAM3vB,YAAYmyB,GAGfA,IAIPpB,EAAgB,UAChBC,EAAe,SACfwB,EAAmC,wCACnCC,EAAkC,uCAIlCpC,EAAyB,SAAgC7oC,GACzDmrC,EAAanrC,EAAM,IAAMgrC,EAAmCzB,EAAzC,+EAAsG0B,EAAkCzB,EAH/H,qEAM5B2B,EAAe,SAAsBnrC,EAAMorC,GAC3C,IAAIzmC,EAAQ3E,EAAKsH,cAAcgR,cAAc,SAC7C3T,EAAM0mC,UAAYD,EAClBprC,EAAKwY,YAAY7T,IAGjB2mC,EAAW,SAAkBr4C,GAC7B,IAAIa,EAAQxD,EAAe2C,EAAM,GAC7Bk3B,EAAUr2B,EAAM,GAChB0K,EAAI1K,EAAM,GACV2K,EAAI3K,EAAM,GAEdq2B,EAAQuf,WAAalrC,EACrB2rB,EAAQsf,UAAYhrC,GAGpB4oC,EAAoB,WACpB,OAAOn0C,KAAKkzB,KAAKM,KAAKC,MAAwB,IAAhBzzB,KAAKq4C,UAAqBtpB,SAAS,KAGjEupB,EAAkB,oCAElBhE,EAA2B,SAAkCpoC,EAAMgjB,GACnE,IACI,OAAO9I,QAAQC,QAAQna,EAAKqsC,cAAc1rC,SAASE,iBACrD,MAAOkZ,GACL,OAAOiJ,EAAQqJ,OAAQ,EAAIyZ,EAAO1Z,OAAOpsB,EAAKmO,IAAK6U,GAAS9H,MAAK,SAAUoxB,GACvE,IAAIt4C,EAAQs4C,EAAKt4C,MAAMo4C,GACvB,OAAKp4C,EAIe,WAAbA,EAAM,GAAkB0mB,OAAO6xB,KAAKC,mBAAmBx4C,EAAM,KAAOw4C,mBAAmBx4C,EAAM,IAHzFkmB,QAAQkB,YAIpBF,MAAK,SAAUoxB,GACd,OAAOG,EAAsBzsC,EAAKkI,eAAe,EAAId,EAAQvI,aAAamB,EAAM,EAAG,IAAIkb,MAAK,SAAUwxB,GAClG,IACIC,EADcD,EAAqBL,cACP1rC,SAEhCgsC,EAAc3f,OACd2f,EAAcC,MAAMN,GACpB,IAAIO,EAAaC,EAAaJ,GAAsBxxB,MAAK,WACrD,OAAOyxB,EAAc9rC,mBAIzB,OADA8rC,EAAcI,QACPF,QAEV3yB,QAAQkB,WAIjBqxB,EAAwB,SAA+BvkC,EAAe/H,GACtE,IAAIusC,EAAuBxkC,EAAcgR,cAAc,UAYvD,OAVAwzB,EAAqBf,UAAY,wBACjCe,EAAqBnnC,MAAM6F,WAAa,SACxCshC,EAAqBnnC,MAAMvB,SAAW,QACtC0oC,EAAqBnnC,MAAM/F,KAAO,WAClCktC,EAAqBnnC,MAAM9F,IAAM,MACjCitC,EAAqBnnC,MAAMsD,OAAS,IACpC6jC,EAAqBhtC,MAAQS,EAAOT,MAAMmjB,WAC1C6pB,EAAqB/sC,OAASQ,EAAOR,OAAOkjB,WAC5C6pB,EAAqBM,UAAY,KACjCN,EAAqB/+B,aApcF,0BAociC,QAC/CzF,EAActH,MAInBsH,EAActH,KAAKwY,YAAYszB,GAExBxyB,QAAQC,QAAQuyB,IALZxyB,QAAQkB,OAA+G,KAQlI0xB,EAAe,SAAsBJ,GACrC,IAAIne,EAAcme,EAAqBL,cACnCM,EAAgBpe,EAAY5tB,SAEhC,OAAO,IAAIuZ,SAAQ,SAAUC,EAASiB,GAClCmT,EAAYnU,OAASsyB,EAAqBtyB,OAASuyB,EAAcM,mBAAqB,WAClF,IAAIC,EAAWC,aAAY,WACnBR,EAAc/rC,KAAKiH,WAAW1V,OAAS,GAAkC,aAA7Bw6C,EAAcS,aAC1DC,cAAcH,GACd/yB,EAAQuyB,MAEb,SAiDXY,GA5Cct8C,EAAQu9B,YAAc,SAAqBrmB,EAAe/H,EAAQimC,EAAkBpjB,EAASI,EAAQ+iB,GACnH,IAAIjY,EAAS,IAAIC,EAAeiY,EAAkBpjB,EAASI,GAAQ,EAAO+iB,GACtEtmC,EAAUqI,EAAcD,YAAYE,YACpCrI,EAAUoI,EAAcD,YAAYG,YAExC,OAAOqkC,EAAsBvkC,EAAe/H,GAAQ+a,MAAK,SAAUwxB,GAC/D,IAAIne,EAAcme,EAAqBL,cACnCM,EAAgBpe,EAAY5tB,SAM5BksC,EAAaC,EAAaJ,GAAsBxxB,MAAK,WACrDgT,EAAOmY,iBAAiB9yB,QAAQ24B,GAChC3d,EAAYgf,SAASptC,EAAOX,KAAMW,EAAOV,MACrC,sBAAsBgU,KAAK+5B,UAAUC,YAAelf,EAAYzuB,UAAYK,EAAOV,KAAO8uB,EAAY1uB,UAAYM,EAAOX,OACzHmtC,EAAc9rC,gBAAgB0E,MAAM9F,KAAOU,EAAOV,IAAM,KACxDktC,EAAc9rC,gBAAgB0E,MAAM/F,MAAQW,EAAOX,KAAO,KAC1DmtC,EAAc9rC,gBAAgB0E,MAAMvB,SAAW,YAGnD,IAAI6Z,EAAS3D,QAAQC,QAAQ,CAACuyB,EAAsBxe,EAAOqb,uBAAwBrb,EAAOzmB,iBAEtFimC,EAAU1qB,EAAQ0qB,QAEtB,OAAOxf,EAAOqb,kCAAkChb,EAAYgB,aAAerB,EAAOqb,kCAAkCrhC,EAAcD,YAAYsnB,aAAerB,EAAOqb,kCAAkCha,YAAiC,mBAAZme,EAAyBxzB,QAAQC,UAAUe,MAAK,WACvQ,OAAOwyB,EAAQf,MAChBzxB,MAAK,WACJ,OAAO2C,KACNA,EAAS3D,QAAQkB,OAA8H,OAUxJ,OAPAuxB,EAAc3f,OACd2f,EAAcC,MAAMU,EAAiB3sC,SAASgtC,SAAW,iBAlMxC,SAA4BzlC,EAAe9I,EAAGC,IAC/D6I,EAAcD,aAAgB7I,IAAM8I,EAAcD,YAAYE,aAAe9I,IAAM6I,EAAcD,YAAYG,aAC7GF,EAAcD,YAAYslC,SAASnuC,EAAGC,GAkMtCuuC,CAAmBxH,EAAiBl+B,cAAerI,EAASC,GAC5D6sC,EAAcjiB,aAAaiiB,EAAckB,UAAU3f,EAAOrtB,iBAAkB8rC,EAAc9rC,iBAC1F8rC,EAAcI,QAEPF,MAIQ,SAA0Bc,GAC7C,IAAItwB,EAAM,GAsBV,OArBIswB,IACAtwB,GAAO,aACHswB,EAAQG,OACRzwB,GAAOswB,EAAQG,MAGfH,EAAQI,iBACR1wB,GAAOswB,EAAQI,gBAGfJ,EAAQK,WACR3wB,GAAO,IAAMswB,EAAQK,SAAW,KAGhCL,EAAQM,WACR5wB,GAAO,IAAMswB,EAAQM,SAAW,KAGpC5wB,GAAO,KAGJA,K,kCChkBXvsB,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQk9C,mBAAgBr8C,EAExB,IAQgCqN,EAR5B3M,EAAe,WAAc,SAASC,EAAiBC,EAAQC,GAAS,IAAK,IAAItB,EAAI,EAAGA,EAAIsB,EAAMP,OAAQf,IAAK,CAAE,IAAIuB,EAAaD,EAAMtB,GAAIuB,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMhC,OAAOC,eAAe0B,EAAQE,EAAWI,IAAKJ,IAAiB,OAAO,SAAUK,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYT,EAAiBQ,EAAYG,UAAWF,GAAiBC,GAAaV,EAAiBQ,EAAaE,GAAqBF,GAA7gB,GAEf82B,EAAW,EAAQ,MAEnBC,GAI4B7qB,EAJO4qB,IAIc5qB,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAFnF4mC,EAAS,EAAQ,MAIrB,SAASpyC,EAAgBD,EAAUT,GAAe,KAAMS,aAAoBT,GAAgB,MAAM,IAAIV,UAAU,qCAEhH,IAAI67C,EAAiB,WACjB,SAASA,EAAenrB,EAASI,EAAQ1I,GACrChnB,EAAgBC,KAAMw6C,GAEtBx6C,KAAKqvB,QAAUA,EACfrvB,KAAKy6C,QAAU1zB,EACf/mB,KAAKqhC,OAASrhC,KAAK06C,UAAU3zB,EAAO4zB,SAASrH,MAC7CtzC,KAAKw0C,MAAQ,GACbx0C,KAAKyvB,OAASA,EACdzvB,KAAK46C,OAAS,EAgLlB,OA7KAh8C,EAAa47C,EAAgB,CAAC,CAC1Bp7C,IAAK,YACL9B,MAAO,SAAmBkd,GACtB,IAAIxG,EAAQhU,KAEZ,GAAIA,KAAK66C,mBAAmBrgC,GACxB,OAAOA,EAGX,IAAKsgC,EAAMtgC,IAAQ4b,EAAUh1B,QAAQ25C,oBAAqB,CACtD,IAAgC,IAA5B/6C,KAAKqvB,QAAQslB,YAAuBqG,EAAcxgC,IAAQxa,KAAKi7C,aAAazgC,GAC5E,OAAOxa,KAAKk7C,SAAS1gC,EAAKA,GAAK,GAC5B,IAAKxa,KAAKi7C,aAAazgC,GAAM,CAChC,GAAkC,iBAAvBxa,KAAKqvB,QAAQqJ,MAIpB,OAHA14B,KAAKw0C,MAAMh6B,IAAO,EAAI23B,EAAO1Z,OAAOje,EAAKxa,KAAKqvB,SAAS9H,MAAK,SAAU/M,GAClE,OAAO2gC,EAAW3gC,EAAKxG,EAAMqb,QAAQiK,cAAgB,MAElD9e,EACJ,IAA6B,IAAzBxa,KAAKqvB,QAAQwlB,SAAoBze,EAAUh1B,QAAQg6C,oBAC1D,OAAOp7C,KAAKk7C,SAAS1gC,EAAKA,GAAK,OAKhD,CACCpb,IAAK,cACL9B,MAAO,SAAqBkd,GACxB,IAAI0V,EAASlwB,KAEb,OAAIg7C,EAAcxgC,GACP2gC,EAAW3gC,EAAKxa,KAAKqvB,QAAQiK,cAAgB,GAEpDt5B,KAAK66C,mBAAmBrgC,GACjBxa,KAAKw0C,MAAMh6B,GAEjBxa,KAAKi7C,aAAazgC,IAAsC,iBAAvBxa,KAAKqvB,QAAQqJ,MAM5C14B,KAAKq7C,SAAS7gC,GALVxa,KAAKw0C,MAAMh6B,IAAO,EAAI23B,EAAO1Z,OAAOje,EAAKxa,KAAKqvB,SAAS9H,MAAK,SAAU/M,GACzE,OAAO2gC,EAAW3gC,EAAK0V,EAAOb,QAAQiK,cAAgB,QAMnE,CACCl6B,IAAK,WACL9B,MAAO,SAAkBkd,GACrB,IAAI8W,EAAStxB,KAoCb,OAlCAA,KAAKw0C,MAAMh6B,GAAO,IAAI+L,SAAQ,SAAUC,EAASiB,GAC7C,IAAIoR,EAAM,IAAIhR,eAoBd,GAnBAgR,EAAIygB,mBAAqB,WACrB,GAAuB,IAAnBzgB,EAAI4gB,WACJ,GAAmB,MAAf5gB,EAAIE,OACJtR,EAAO,yBAA2BjN,EAAI3Z,UAAU,EAAG,KAAO,qBAAuBg4B,EAAIE,YAClF,CACH,IAAIE,EAAS,IAAIC,WACjBD,EAAO3gB,iBAAiB,QAAQ,WAE5B,IAAI4R,EAAS+O,EAAO/O,OACpB1D,EAAQ0D,MACT,GACH+O,EAAO3gB,iBAAiB,SAAS,SAAU8N,GACvC,OAAOqB,EAAOrB,MACf,GACH6S,EAAOE,cAAcN,EAAIG,YAIrCH,EAAI/Q,aAAe,OACfwJ,EAAOjC,QAAQiK,aAAc,CAC7B,IAAIC,EAAUjI,EAAOjC,QAAQiK,aAC7BT,EAAIU,QAAUA,EACdV,EAAIW,UAAY,WACZ,OAAO/R,EAAmH,KAGlIoR,EAAIQ,KAAK,MAAO7e,GAAK,GACrBqe,EAAIY,UACLlS,MAAK,SAAU/M,GACd,OAAO2gC,EAAW3gC,EAAK8W,EAAOjC,QAAQiK,cAAgB,MAGnDt5B,KAAKw0C,MAAMh6B,KAEvB,CACCpb,IAAK,aACL9B,MAAO,SAAoB+O,GACvB,IAAIjN,EAAM2qB,OAAO/pB,KAAK46C,UAEtB,OADA56C,KAAKw0C,MAAMp1C,GAAOmnB,QAAQC,QAAQna,GAC3BjN,IAEZ,CACCA,IAAK,qBACL9B,MAAO,SAA4B8B,GAC/B,YAAkC,IAApBY,KAAKw0C,MAAMp1C,KAE9B,CACCA,IAAK,WACL9B,MAAO,SAAkB8B,EAAKob,EAAKq6B,GAC/B,IAAIziB,EAASpyB,KAMb,IAAIs7C,EAAmB,SAA0BC,GAC7C,OAAO,IAAIh1B,SAAQ,SAAUC,EAASiB,GAClC,IAAInN,EAAM,IAAIyL,MAiBd,GAhBAzL,EAAImM,OAAS,WACT,OAAOD,EAAQlM,IAGdihC,IAAsB1G,IACvBv6B,EAAIsN,YAAc,aAGtBtN,EAAIoM,QAAUe,EACdnN,EAAIE,IAAMA,GACW,IAAjBF,EAAIqM,UAEJC,YAAW,WACPJ,EAAQlM,KACT,KAEH8X,EAAO/C,QAAQiK,aAAc,CAC7B,IAAIC,EAAUnH,EAAO/C,QAAQiK,aAC7B1S,YAAW,WACP,OAAOa,EAAmH,MAC3H8R,QAOf,OAFAv5B,KAAKw0C,MAAMp1C,GAAOo8C,EAAoBhhC,KAASsgC,EAAMtgC,GACrD4b,EAAUh1B,QAAQq6C,uBAAuBjhC,GAAK+M,KAAK+zB,GAAoBA,GAAiB,GACjFl8C,IAEZ,CACCA,IAAK,eACL9B,MAAO,SAAsBo+C,GACzB,OAAO17C,KAAK06C,UAAUgB,KAAS17C,KAAKqhC,SAEzC,CACCjiC,IAAK,YACL9B,MAAO,SAAmBo+C,GACtB,IAAIC,EAAO37C,KAAK47C,QAAU57C,KAAK47C,MAAQ57C,KAAKy6C,QAAQztC,SAASuY,cAAc,MAG3E,OAFAo2B,EAAKrI,KAAOoI,EACZC,EAAKrI,KAAOqI,EAAKrI,KACVqI,EAAKE,SAAWF,EAAKG,SAAWH,EAAKI,OAEjD,CACC38C,IAAK,QACL9B,MAAO,WACH,IAAI0+C,EAASh8C,KAETopB,EAAOjsB,OAAOisB,KAAKppB,KAAKw0C,OACxB/nB,EAASrD,EAAK7K,KAAI,SAAUmL,GAC5B,OAAOsyB,EAAOxH,MAAM9qB,GAAKhC,OAAM,SAAUtB,GAIrC,OAAO,WAGf,OAAOG,QAAQ4U,IAAI1O,GAAQlF,MAAK,SAAU00B,GAItC,OAAO,IAAI1B,EAAcnxB,EAAM6yB,UAKpCzB,EAzLU,GA4LrBn9C,EAAQ+D,QAAUo5C,EAElB,IAAID,EAAgBl9C,EAAQk9C,cAAgB,WACxC,SAASA,EAAcnxB,EAAM8yB,GACzBn8C,EAAgBC,KAAMu6C,GAEtBv6C,KAAKm8C,MAAQ/yB,EACbppB,KAAKo8C,WAAaF,EAWtB,OARAt9C,EAAa27C,EAAe,CAAC,CACzBn7C,IAAK,MACL9B,MAAO,SAAa8B,GAChB,IAAI2U,EAAQ/T,KAAKm8C,MAAM1lC,QAAQrX,GAC/B,OAAkB,IAAX2U,EAAe,KAAO/T,KAAKo8C,WAAWroC,OAI9CwmC,EAhBiC,GAmBxC8B,EAAa,yBACbC,EAAgB,2BAChBC,EAAa,mBAEbvB,EAAgB,SAAuBxgC,GACvC,OAAO+hC,EAAWz8B,KAAKtF,IAEvBghC,EAAsB,SAA6BhhC,GACnD,OAAO8hC,EAAcx8B,KAAKtF,IAG1BsgC,EAAQ,SAAetgC,GACvB,MAAwC,QAAjCA,EAAIrJ,QAAQ,GAAGvQ,eAA2By7C,EAAWv8B,KAAKtF,IAGjE2gC,EAAa,SAAoB3gC,EAAK+e,GACtC,OAAO,IAAIhT,SAAQ,SAAUC,EAASiB,GAClC,IAAInN,EAAM,IAAIyL,MACdzL,EAAImM,OAAS,WACT,OAAOD,EAAQlM,IAEnBA,EAAIoM,QAAUe,EACdnN,EAAIE,IAAMA,GACW,IAAjBF,EAAIqM,UAEJC,YAAW,WACPJ,EAAQlM,KACT,KAEHif,GACA3S,YAAW,WACP,OAAOa,EAA+F,MACvG8R,Q,kCClQfp8B,OAAOC,eAAeC,EAAS,aAAc,CACzCC,OAAO,IAEXD,EAAQm/C,aAAen/C,EAAQ44C,qBAAuB54C,EAAQg5C,YAAch5C,EAAQ04C,kBAAoB14C,EAAQo/C,WAAap/C,EAAQw6C,8BAA2B35C,EAEhK,IAAIX,EAAgb,SAAUC,EAAKC,GAAK,GAAIC,MAAMC,QAAQH,GAAQ,OAAOA,EAAY,GAAII,OAAOC,YAAYV,OAAOK,GAAQ,OAAxf,SAAuBA,EAAKC,GAAK,IAAIK,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKZ,EAAII,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGb,QAAYG,GAAKK,EAAKU,SAAWf,GAA3DM,GAAK,IAAoE,MAAOU,GAAOT,GAAK,EAAMC,EAAKQ,EAAO,QAAU,KAAWV,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HY,CAAclB,EAAKC,GAAa,MAAM,IAAIkB,UAAU,yDAEllBgV,EAAY,EAAQ,MAEpBf,EAAa,EAAQ,MAErBilC,EAA2Bx6C,EAAQw6C,yBAA2B,CAC9DE,KAAM,EACND,MAAO,GAGP2E,EAAap/C,EAAQo/C,WAAa,CAClCC,OAAQ,EACRC,UAAW,EACXC,IAAK,EACLC,QAAS,EACTC,SAAU,EACVC,UAAW,EACXC,WAAY,GAoHZR,GAjHoBn/C,EAAQ04C,kBAAoB,SAA2BnkC,EAAOyS,GAClF,IAAKzS,IAAUA,EAAMqrC,cAAuC,SAAvBrrC,EAAMqrC,aACvC,MAAO,GAOX,IAJA,IAAIC,EAAe,GACfC,EAAgBvrC,EAAMqrC,aAAar+B,MAAM,WACzCw+B,EAAmBD,EAAc3+C,OAE5Bf,EAAI,EAAGA,EAAI2/C,EAAkB3/C,IAAK,CACvC,IAAI4/C,EAAwBF,EAAc1/C,GAAGmhB,MAAM,OAC/C0+B,EAAyB//C,EAAe8/C,EAAuB,GAC/DE,EAAcD,EAAuB,GACrC7T,EAAe6T,EAAuB,GAE1CJ,EAAa3+C,KAAKg/C,GAClB,IAAIC,EAAUn5B,EAAKyuB,SAASyK,GACvBC,IACDA,EAAUn5B,EAAKyuB,SAASyK,GAAe,IAE3CC,EAAQj/C,KAAK+B,SAASmpC,GAAgB,EAAG,KAG7C,OAAOyT,GAGO7/C,EAAQg5C,YAAc,SAAqB6G,EAAc74B,GAEvE,IADA,IAAIo5B,EAAcP,EAAa1+C,OACtBf,EAAI,EAAGA,EAAIggD,EAAahgD,IAC7B4mB,EAAKyuB,SAASoK,EAAaz/C,IAAIigD,OAIZrgD,EAAQ44C,qBAAuB,SAA8B5pC,EAAMuF,EAAOyS,GACjG,IAAKzS,IAAUA,EAAM+lC,SAA6B,SAAlB/lC,EAAM+lC,SAAwC,qBAAlB/lC,EAAM+lC,SAAoD,SAAlB/lC,EAAM+C,QACtG,OAAO,KAGX,IAAIgpC,EAASnB,EAAa5qC,EAAM+lC,SAE5BhtB,EAAMgzB,EAAOn/C,OACbi5C,EAAe,GACfx9B,EAAI,GAGJ2jC,EAAmBhsC,EAAMgsC,iBAC7B,GAAIA,GAAyC,SAArBA,EAA6B,CACjD,IAAIC,EAAwBD,EAAiBh/B,MAAM,OAC/Ck/B,EAAyBvgD,EAAesgD,EAAuB,GAC/DN,EAAcO,EAAuB,GACrCC,EAAiBD,EAAuB,GAExCN,EAAUn5B,EAAKyuB,SAASyK,GACxBC,IACAA,EAAQA,EAAQh/C,OAAS,SAAyBN,IAAnB6/C,EAA+B,EAAIz9C,SAASy9C,EAAgB,KAKnG,IAAK,IAAItgD,EAAI,EAAGA,EAAIktB,EAAKltB,IAAK,CAC1B,IAAIugD,EAAQL,EAAOlgD,GACnB,OAAQugD,EAAM9sC,MACV,KAAKurC,EAAWC,OACZziC,GAAK+jC,EAAM1gD,OAAS,GACpB,MAEJ,KAAKm/C,EAAWE,UACRtwC,aAAgBuvB,aAAeoiB,EAAM1gD,QACrC2c,GAAK5N,EAAKuO,aAAaojC,EAAM1gD,QAAU,IAE3C,MAEJ,KAAKm/C,EAAWI,QACZ,IAAIoB,EAAW55B,EAAKyuB,SAASkL,EAAM7D,MAAQ,IACvC8D,IACAhkC,GAAKikC,EAAmB,CAACD,EAASA,EAASz/C,OAAS,IAAK,GAAIw/C,EAAMjH,SAEvE,MAEJ,KAAK0F,EAAWK,SACZ,IAAIqB,EAAY95B,EAAKyuB,SAASkL,EAAM7D,MAAQ,IACxCgE,IACAlkC,GAAKikC,EAAmBC,EAAWH,EAAMI,KAAMJ,EAAMjH,SAEzD,MAEJ,KAAK0F,EAAWM,UACZ9iC,GAAKokC,EAASzsC,GAAO,EAAMyS,EAAK0uB,YAChC1uB,EAAK0uB,aACL,MAEJ,KAAK0J,EAAWO,WACZ34B,EAAK0uB,aACL94B,GAAKokC,EAASzsC,GAAO,EAAOyS,EAAK0uB,YACjC,MAEJ,KAAK0J,EAAWG,IACR3iC,IACAw9B,EAAal5C,KAAK,CAAE2S,KAAM2mC,EAAyBE,KAAMz6C,MAAO2c,IAChEA,EAAI,IAERw9B,EAAal5C,KAAK,CAAE2S,KAAM2mC,EAAyBC,MAAOx6C,MAAO0gD,EAAM1gD,OAAS,MAS5F,OAJI2c,GACAw9B,EAAal5C,KAAK,CAAE2S,KAAM2mC,EAAyBE,KAAMz6C,MAAO2c,IAG7Dw9B,GAGQp6C,EAAQm/C,aAAe,SAAsB7E,EAASnD,GACrE,GAAIA,GAASA,EAAMmD,GACf,OAAOnD,EAAMmD,GAajB,IAVA,IAAIgG,EAAS,GACThzB,EAAMgtB,EAAQn5C,OAEd8/C,GAAW,EACXC,GAAY,EACZC,GAAa,EACb90B,EAAM,GACN+0B,EAAe,GACfhgC,EAAO,GAEFhhB,EAAI,EAAGA,EAAIktB,EAAKltB,IAAK,CAC1B,IAAIoiB,EAAI83B,EAAQ+G,OAAOjhD,GAEvB,OAAQoiB,GACJ,IAAK,IACL,IAAK,IACG0+B,EACA70B,GAAO7J,GAEPy+B,GAAYA,EACPE,GAAeF,IAChBX,EAAOp/C,KAAK,CAAE2S,KAAMurC,EAAWC,OAAQp/C,MAAOosB,IAC9CA,EAAM,KAGd,MAEJ,IAAK,KACG60B,GACA70B,GAAO7J,EACP0+B,GAAY,GAEZA,GAAY,EAEhB,MAEJ,IAAK,IACGD,EACA50B,GAAO7J,GAEP2+B,GAAa,EACbC,EAAe/0B,EACfA,EAAM,GACNjL,EAAO,IAEX,MAEJ,IAAK,IACD,GAAI6/B,EACA50B,GAAO7J,OACJ,GAAI2+B,EAAY,CAKnB,OAJI90B,GACAjL,EAAKlgB,KAAKmrB,GAGN+0B,GACJ,IAAK,OACGhgC,EAAKjgB,OAAS,GACdm/C,EAAOp/C,KAAK,CAAE2S,KAAMurC,EAAWE,UAAWr/C,MAAOmhB,EAAK,KAE1D,MAEJ,IAAK,UACD,GAAIA,EAAKjgB,OAAS,EAAG,CACjB,IAAIg/C,EAAU,CACVtsC,KAAMurC,EAAWI,QACjB1C,KAAM17B,EAAK,IAEXA,EAAKjgB,OAAS,IACdg/C,EAAQzG,OAASt4B,EAAK,IAE1Bk/B,EAAOp/C,KAAKi/C,GAEhB,MAEJ,IAAK,WACD,GAAI/+B,EAAKjgB,OAAS,EAAG,CACjB,IAAImgD,EAAa,CACbztC,KAAMurC,EAAWK,SACjB3C,KAAM17B,EAAK,IAEXA,EAAKjgB,OAAS,IACdmgD,EAAWP,KAAO3/B,EAAK,IAEvBA,EAAKjgB,OAAS,IACdmgD,EAAW5H,OAASt4B,EAAK,IAE7Bk/B,EAAOp/C,KAAKogD,GAEhB,MAEJ,IAAK,MACGlgC,EAAKjgB,OAAS,GACdm/C,EAAOp/C,KAAK,CAAE2S,KAAMurC,EAAWG,IAAKt/C,MAAOmhB,EAAK,KAK5D+/B,GAAa,EACb90B,EAAM,GAEV,MAEJ,IAAK,IACG40B,EACA50B,GAAO7J,EACA2+B,IACP//B,EAAKlgB,KAAKmrB,GACVA,EAAM,IAEV,MAEJ,IAAK,IACL,IAAK,KACG40B,EACA50B,GAAO7J,EACA6J,IACPk1B,EAAcjB,EAAQj0B,GACtBA,EAAM,IAEV,MAEJ,QACIA,GAAO7J,EAGL,OAANA,IACA0+B,GAAY,GAYpB,OARI70B,GACAk1B,EAAcjB,EAAQj0B,GAGtB8qB,IACAA,EAAMmD,GAAWgG,GAGdA,IAGPiB,EAAgB,SAAuBjB,EAAQkB,GAC/C,OAAQA,GACJ,IAAK,aACDlB,EAAOp/C,KAAK,CAAE2S,KAAMurC,EAAWM,YAC/B,MACJ,IAAK,cACDY,EAAOp/C,KAAK,CAAE2S,KAAMurC,EAAWO,eAKvCqB,EAAW,SAAkBzsC,EAAOktC,EAAW/L,GAC/C,IAAIgM,EAASntC,EAAMmtC,OAASntC,EAAMmtC,OAAOngC,MAAM,OAAS,CAAC,OAAQ,QAC7D4yB,EAAmB,EAAbuB,EAOV,OANIvB,GAAOuN,EAAOvgD,SACdgzC,EAAMuN,EAAOvgD,OAAS,GAErBsgD,KACCtN,EAECuN,EAAOvN,GAAK9sB,QAAQ,eAAgB,KAG3Cw5B,EAAqB,SAA4BV,EAASY,EAAMrH,GAIhE,IAHA,IAAIpsB,EAAM6yB,EAAQh/C,OACd0rB,EAAS,GAEJzsB,EAAI,EAAGA,EAAIktB,EAAKltB,IACjBA,EAAI,IACJysB,GAAUk0B,GAAQ,IAEtBl0B,IAAU,EAAIvW,EAAU6X,mBAAmBgyB,EAAQ//C,IAAI,EAAImV,EAAWyN,oBAAoB02B,GAAU,YAAY,GAGpH,OAAO7sB,I,kCChUX,IAAI80B,EAAW7hD,OAAO8hD,QAAU,SAAUngD,GAAU,IAAK,IAAIrB,EAAI,EAAGA,EAAIusB,UAAUxrB,OAAQf,IAAK,CAAE,IAAIuhB,EAASgL,UAAUvsB,GAAI,IAAK,IAAI2B,KAAO4f,EAAc7hB,OAAOqC,UAAU0/C,eAAehrB,KAAKlV,EAAQ5f,KAAQN,EAAOM,GAAO4f,EAAO5f,IAAY,OAAON,GAInPuzC,EAAmBhnC,EAFD,EAAQ,OAM1B8zC,EAAW9zC,EAFD,EAAQ,OAIlB+zC,EAAU,EAAQ,MAElB3rC,EAAU,EAAQ,MAEtB,SAASpI,EAAuBE,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEnK,QAASmK,GAEvF,IAAI8zC,EAAc,SAAqBjoB,EAASkoB,GAC5C,IAAIC,EAASD,GAAQ,GACjB7vB,EAAS,IAAI0vB,EAAS/9C,QAAkC,kBAAnBm+C,EAAO3K,SAAwB2K,EAAO3K,SAC/EnlB,EAAOC,IAAI,oCAMX,IAAInb,EAAgB6iB,EAAQ7iB,cAC5B,IAAKA,EACD,OAAOgS,QAAQkB,OAAO,6CAE1B,IAAInT,EAAcC,EAAcD,YAE5BpI,EAAUoI,EAAYE,YACtBrI,EAAUmI,EAAYG,YAItBvU,EAFiC,SAApBk3B,EAAQnjB,SAA0C,SAApBmjB,EAAQnjB,SAE/B,EAAIR,EAAQ1I,mBAAmBwJ,IAAiB,EAAId,EAAQvI,aAAaksB,EAASlrB,EAASC,GAC/GJ,EAAQ7L,EAAK6L,MACbC,EAAS9L,EAAK8L,OACdH,EAAO3L,EAAK2L,KACZC,EAAM5L,EAAK4L,IAEX0zC,EAAiB,CACjB9K,OAAO,EACPC,YAAY,EACZ52B,gBAAiB,UACjBub,aAAc,KACdsb,SAAS,EACTlc,MAAO,KACP0C,iBAAiB,EACjBhB,wBAAwB,EACxB9K,MAAOhb,EAAYmrC,kBAAoB,EACvC3gD,OAAQ,IAAIuzC,EAAiBjxC,QAAQm+C,EAAO9kC,QAC5Co6B,SAAS,EACTppC,EAAGI,EACHH,EAAGI,EACHC,MAAO5L,KAAKkzB,KAAKtnB,GACjBC,OAAQ7L,KAAKkzB,KAAKrnB,GAClBqrB,YAAa/iB,EAAY4X,WACzBoL,aAAchjB,EAAYwgC,YAC1B5oC,QAASoI,EAAYE,YACrBrI,QAASmI,EAAYG,aAWzB,OARa,EAAI2qC,EAAQ1lB,eAAetC,EAAS4nB,EAAS,GAAIQ,EAAgBD,GAAS9vB,IAW3F4vB,EAAYlwB,eAAiBkjB,EAAiBjxC,QAE9C0oC,EAAOzsC,QAAUgiD", "file": "chunks/vendors.html2canvas.chunk.js", "sourcesContent": ["'use strict';\n\n// http://dev.w3.org/csswg/css-color/\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar HEX3 = /^#([a-f0-9]{3})$/i;\nvar hex3 = function hex3(value) {\n    var match = value.match(HEX3);\n    if (match) {\n        return [parseInt(match[1][0] + match[1][0], 16), parseInt(match[1][1] + match[1][1], 16), parseInt(match[1][2] + match[1][2], 16), null];\n    }\n    return false;\n};\n\nvar HEX6 = /^#([a-f0-9]{6})$/i;\nvar hex6 = function hex6(value) {\n    var match = value.match(HEX6);\n    if (match) {\n        return [parseInt(match[1].substring(0, 2), 16), parseInt(match[1].substring(2, 4), 16), parseInt(match[1].substring(4, 6), 16), null];\n    }\n    return false;\n};\n\nvar RGB = /^rgb\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*\\)$/;\nvar rgb = function rgb(value) {\n    var match = value.match(RGB);\n    if (match) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), null];\n    }\n    return false;\n};\n\nvar RGBA = /^rgba\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d?\\.?\\d+)\\s*\\)$/;\nvar rgba = function rgba(value) {\n    var match = value.match(RGBA);\n    if (match && match.length > 4) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), Number(match[4])];\n    }\n    return false;\n};\n\nvar fromArray = function fromArray(array) {\n    return [Math.min(array[0], 255), Math.min(array[1], 255), Math.min(array[2], 255), array.length > 3 ? array[3] : null];\n};\n\nvar namedColor = function namedColor(name) {\n    var color = NAMED_COLORS[name.toLowerCase()];\n    return color ? color : false;\n};\n\nvar Color = function () {\n    function Color(value) {\n        _classCallCheck(this, Color);\n\n        var _ref = Array.isArray(value) ? fromArray(value) : hex3(value) || rgb(value) || rgba(value) || namedColor(value) || hex6(value) || [0, 0, 0, null],\n            _ref2 = _slicedToArray(_ref, 4),\n            r = _ref2[0],\n            g = _ref2[1],\n            b = _ref2[2],\n            a = _ref2[3];\n\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n\n    _createClass(Color, [{\n        key: 'isTransparent',\n        value: function isTransparent() {\n            return this.a === 0;\n        }\n    }, {\n        key: 'toString',\n        value: function toString() {\n            return this.a !== null && this.a !== 1 ? 'rgba(' + this.r + ',' + this.g + ',' + this.b + ',' + this.a + ')' : 'rgb(' + this.r + ',' + this.g + ',' + this.b + ')';\n        }\n    }]);\n\n    return Color;\n}();\n\nexports.default = Color;\n\n\nvar NAMED_COLORS = {\n    transparent: [0, 0, 0, 0],\n    aliceblue: [240, 248, 255, null],\n    antiquewhite: [250, 235, 215, null],\n    aqua: [0, 255, 255, null],\n    aquamarine: [127, 255, 212, null],\n    azure: [240, 255, 255, null],\n    beige: [245, 245, 220, null],\n    bisque: [255, 228, 196, null],\n    black: [0, 0, 0, null],\n    blanchedalmond: [255, 235, 205, null],\n    blue: [0, 0, 255, null],\n    blueviolet: [138, 43, 226, null],\n    brown: [165, 42, 42, null],\n    burlywood: [222, 184, 135, null],\n    cadetblue: [95, 158, 160, null],\n    chartreuse: [127, 255, 0, null],\n    chocolate: [210, 105, 30, null],\n    coral: [255, 127, 80, null],\n    cornflowerblue: [100, 149, 237, null],\n    cornsilk: [255, 248, 220, null],\n    crimson: [220, 20, 60, null],\n    cyan: [0, 255, 255, null],\n    darkblue: [0, 0, 139, null],\n    darkcyan: [0, 139, 139, null],\n    darkgoldenrod: [184, 134, 11, null],\n    darkgray: [169, 169, 169, null],\n    darkgreen: [0, 100, 0, null],\n    darkgrey: [169, 169, 169, null],\n    darkkhaki: [189, 183, 107, null],\n    darkmagenta: [139, 0, 139, null],\n    darkolivegreen: [85, 107, 47, null],\n    darkorange: [255, 140, 0, null],\n    darkorchid: [153, 50, 204, null],\n    darkred: [139, 0, 0, null],\n    darksalmon: [233, 150, 122, null],\n    darkseagreen: [143, 188, 143, null],\n    darkslateblue: [72, 61, 139, null],\n    darkslategray: [47, 79, 79, null],\n    darkslategrey: [47, 79, 79, null],\n    darkturquoise: [0, 206, 209, null],\n    darkviolet: [148, 0, 211, null],\n    deeppink: [255, 20, 147, null],\n    deepskyblue: [0, 191, 255, null],\n    dimgray: [105, 105, 105, null],\n    dimgrey: [105, 105, 105, null],\n    dodgerblue: [30, 144, 255, null],\n    firebrick: [178, 34, 34, null],\n    floralwhite: [255, 250, 240, null],\n    forestgreen: [34, 139, 34, null],\n    fuchsia: [255, 0, 255, null],\n    gainsboro: [220, 220, 220, null],\n    ghostwhite: [248, 248, 255, null],\n    gold: [255, 215, 0, null],\n    goldenrod: [218, 165, 32, null],\n    gray: [128, 128, 128, null],\n    green: [0, 128, 0, null],\n    greenyellow: [173, 255, 47, null],\n    grey: [128, 128, 128, null],\n    honeydew: [240, 255, 240, null],\n    hotpink: [255, 105, 180, null],\n    indianred: [205, 92, 92, null],\n    indigo: [75, 0, 130, null],\n    ivory: [255, 255, 240, null],\n    khaki: [240, 230, 140, null],\n    lavender: [230, 230, 250, null],\n    lavenderblush: [255, 240, 245, null],\n    lawngreen: [124, 252, 0, null],\n    lemonchiffon: [255, 250, 205, null],\n    lightblue: [173, 216, 230, null],\n    lightcoral: [240, 128, 128, null],\n    lightcyan: [224, 255, 255, null],\n    lightgoldenrodyellow: [250, 250, 210, null],\n    lightgray: [211, 211, 211, null],\n    lightgreen: [144, 238, 144, null],\n    lightgrey: [211, 211, 211, null],\n    lightpink: [255, 182, 193, null],\n    lightsalmon: [255, 160, 122, null],\n    lightseagreen: [32, 178, 170, null],\n    lightskyblue: [135, 206, 250, null],\n    lightslategray: [119, 136, 153, null],\n    lightslategrey: [119, 136, 153, null],\n    lightsteelblue: [176, 196, 222, null],\n    lightyellow: [255, 255, 224, null],\n    lime: [0, 255, 0, null],\n    limegreen: [50, 205, 50, null],\n    linen: [250, 240, 230, null],\n    magenta: [255, 0, 255, null],\n    maroon: [128, 0, 0, null],\n    mediumaquamarine: [102, 205, 170, null],\n    mediumblue: [0, 0, 205, null],\n    mediumorchid: [186, 85, 211, null],\n    mediumpurple: [147, 112, 219, null],\n    mediumseagreen: [60, 179, 113, null],\n    mediumslateblue: [123, 104, 238, null],\n    mediumspringgreen: [0, 250, 154, null],\n    mediumturquoise: [72, 209, 204, null],\n    mediumvioletred: [199, 21, 133, null],\n    midnightblue: [25, 25, 112, null],\n    mintcream: [245, 255, 250, null],\n    mistyrose: [255, 228, 225, null],\n    moccasin: [255, 228, 181, null],\n    navajowhite: [255, 222, 173, null],\n    navy: [0, 0, 128, null],\n    oldlace: [253, 245, 230, null],\n    olive: [128, 128, 0, null],\n    olivedrab: [107, 142, 35, null],\n    orange: [255, 165, 0, null],\n    orangered: [255, 69, 0, null],\n    orchid: [218, 112, 214, null],\n    palegoldenrod: [238, 232, 170, null],\n    palegreen: [152, 251, 152, null],\n    paleturquoise: [175, 238, 238, null],\n    palevioletred: [219, 112, 147, null],\n    papayawhip: [255, 239, 213, null],\n    peachpuff: [255, 218, 185, null],\n    peru: [205, 133, 63, null],\n    pink: [255, 192, 203, null],\n    plum: [221, 160, 221, null],\n    powderblue: [176, 224, 230, null],\n    purple: [128, 0, 128, null],\n    rebeccapurple: [102, 51, 153, null],\n    red: [255, 0, 0, null],\n    rosybrown: [188, 143, 143, null],\n    royalblue: [65, 105, 225, null],\n    saddlebrown: [139, 69, 19, null],\n    salmon: [250, 128, 114, null],\n    sandybrown: [244, 164, 96, null],\n    seagreen: [46, 139, 87, null],\n    seashell: [255, 245, 238, null],\n    sienna: [160, 82, 45, null],\n    silver: [192, 192, 192, null],\n    skyblue: [135, 206, 235, null],\n    slateblue: [106, 90, 205, null],\n    slategray: [112, 128, 144, null],\n    slategrey: [112, 128, 144, null],\n    snow: [255, 250, 250, null],\n    springgreen: [0, 255, 127, null],\n    steelblue: [70, 130, 180, null],\n    tan: [210, 180, 140, null],\n    teal: [0, 128, 128, null],\n    thistle: [216, 191, 216, null],\n    tomato: [255, 99, 71, null],\n    turquoise: [64, 224, 208, null],\n    violet: [238, 130, 238, null],\n    wheat: [245, 222, 179, null],\n    white: [255, 255, 255, null],\n    whitesmoke: [245, 245, 245, null],\n    yellow: [255, 255, 0, null],\n    yellowgreen: [154, 205, 50, null]\n};\n\nvar TRANSPARENT = exports.TRANSPARENT = new Color([0, 0, 0, 0]);", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBoundCurves = exports.calculatePaddingBoxPath = exports.calculateBorderBoxPath = exports.parsePathForBorder = exports.parseDocumentSize = exports.calculateContentBox = exports.calculatePaddingBox = exports.parseBounds = exports.Bounds = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _BezierCurve = require('./drawing/BezierCurve');\n\nvar _BezierCurve2 = _interopRequireDefault(_BezierCurve);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TOP = 0;\nvar RIGHT = 1;\nvar BOTTOM = 2;\nvar LEFT = 3;\n\nvar H = 0;\nvar V = 1;\n\nvar Bounds = exports.Bounds = function () {\n    function Bounds(x, y, w, h) {\n        _classCallCheck(this, Bounds);\n\n        this.left = x;\n        this.top = y;\n        this.width = w;\n        this.height = h;\n    }\n\n    _createClass(Bounds, null, [{\n        key: 'fromClientRect',\n        value: function fromClientRect(clientRect, scrollX, scrollY) {\n            return new Bounds(clientRect.left + scrollX, clientRect.top + scrollY, clientRect.width, clientRect.height);\n        }\n    }]);\n\n    return Bounds;\n}();\n\nvar parseBounds = exports.parseBounds = function parseBounds(node, scrollX, scrollY) {\n    return Bounds.fromClientRect(node.getBoundingClientRect(), scrollX, scrollY);\n};\n\nvar calculatePaddingBox = exports.calculatePaddingBox = function calculatePaddingBox(bounds, borders) {\n    return new Bounds(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth));\n};\n\nvar calculateContentBox = exports.calculateContentBox = function calculateContentBox(bounds, padding, borders) {\n    // TODO support percentage paddings\n    var paddingTop = padding[TOP].value;\n    var paddingRight = padding[RIGHT].value;\n    var paddingBottom = padding[BOTTOM].value;\n    var paddingLeft = padding[LEFT].value;\n\n    return new Bounds(bounds.left + paddingLeft + borders[LEFT].borderWidth, bounds.top + paddingTop + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth + paddingLeft + paddingRight), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth + paddingTop + paddingBottom));\n};\n\nvar parseDocumentSize = exports.parseDocumentSize = function parseDocumentSize(document) {\n    var body = document.body;\n    var documentElement = document.documentElement;\n\n    if (!body || !documentElement) {\n        throw new Error(process.env.NODE_ENV !== 'production' ? 'Unable to get document size' : '');\n    }\n    var width = Math.max(Math.max(body.scrollWidth, documentElement.scrollWidth), Math.max(body.offsetWidth, documentElement.offsetWidth), Math.max(body.clientWidth, documentElement.clientWidth));\n\n    var height = Math.max(Math.max(body.scrollHeight, documentElement.scrollHeight), Math.max(body.offsetHeight, documentElement.offsetHeight), Math.max(body.clientHeight, documentElement.clientHeight));\n\n    return new Bounds(0, 0, width, height);\n};\n\nvar parsePathForBorder = exports.parsePathForBorder = function parsePathForBorder(curves, borderSide) {\n    switch (borderSide) {\n        case TOP:\n            return createPathFromCurves(curves.topLeftOuter, curves.topLeftInner, curves.topRightOuter, curves.topRightInner);\n        case RIGHT:\n            return createPathFromCurves(curves.topRightOuter, curves.topRightInner, curves.bottomRightOuter, curves.bottomRightInner);\n        case BOTTOM:\n            return createPathFromCurves(curves.bottomRightOuter, curves.bottomRightInner, curves.bottomLeftOuter, curves.bottomLeftInner);\n        case LEFT:\n        default:\n            return createPathFromCurves(curves.bottomLeftOuter, curves.bottomLeftInner, curves.topLeftOuter, curves.topLeftInner);\n    }\n};\n\nvar createPathFromCurves = function createPathFromCurves(outer1, inner1, outer2, inner2) {\n    var path = [];\n    if (outer1 instanceof _BezierCurve2.default) {\n        path.push(outer1.subdivide(0.5, false));\n    } else {\n        path.push(outer1);\n    }\n\n    if (outer2 instanceof _BezierCurve2.default) {\n        path.push(outer2.subdivide(0.5, true));\n    } else {\n        path.push(outer2);\n    }\n\n    if (inner2 instanceof _BezierCurve2.default) {\n        path.push(inner2.subdivide(0.5, true).reverse());\n    } else {\n        path.push(inner2);\n    }\n\n    if (inner1 instanceof _BezierCurve2.default) {\n        path.push(inner1.subdivide(0.5, false).reverse());\n    } else {\n        path.push(inner1);\n    }\n\n    return path;\n};\n\nvar calculateBorderBoxPath = exports.calculateBorderBoxPath = function calculateBorderBoxPath(curves) {\n    return [curves.topLeftOuter, curves.topRightOuter, curves.bottomRightOuter, curves.bottomLeftOuter];\n};\n\nvar calculatePaddingBoxPath = exports.calculatePaddingBoxPath = function calculatePaddingBoxPath(curves) {\n    return [curves.topLeftInner, curves.topRightInner, curves.bottomRightInner, curves.bottomLeftInner];\n};\n\nvar parseBoundCurves = exports.parseBoundCurves = function parseBoundCurves(bounds, borders, borderRadius) {\n    var tlh = borderRadius[CORNER.TOP_LEFT][H].getAbsoluteValue(bounds.width);\n    var tlv = borderRadius[CORNER.TOP_LEFT][V].getAbsoluteValue(bounds.height);\n    var trh = borderRadius[CORNER.TOP_RIGHT][H].getAbsoluteValue(bounds.width);\n    var trv = borderRadius[CORNER.TOP_RIGHT][V].getAbsoluteValue(bounds.height);\n    var brh = borderRadius[CORNER.BOTTOM_RIGHT][H].getAbsoluteValue(bounds.width);\n    var brv = borderRadius[CORNER.BOTTOM_RIGHT][V].getAbsoluteValue(bounds.height);\n    var blh = borderRadius[CORNER.BOTTOM_LEFT][H].getAbsoluteValue(bounds.width);\n    var blv = borderRadius[CORNER.BOTTOM_LEFT][V].getAbsoluteValue(bounds.height);\n\n    var factors = [];\n    factors.push((tlh + trh) / bounds.width);\n    factors.push((blh + brh) / bounds.width);\n    factors.push((tlv + blv) / bounds.height);\n    factors.push((trv + brv) / bounds.height);\n    var maxFactor = Math.max.apply(Math, factors);\n\n    if (maxFactor > 1) {\n        tlh /= maxFactor;\n        tlv /= maxFactor;\n        trh /= maxFactor;\n        trv /= maxFactor;\n        brh /= maxFactor;\n        brv /= maxFactor;\n        blh /= maxFactor;\n        blv /= maxFactor;\n    }\n\n    var topWidth = bounds.width - trh;\n    var rightHeight = bounds.height - brv;\n    var bottomWidth = bounds.width - brh;\n    var leftHeight = bounds.height - blv;\n\n    return {\n        topLeftOuter: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left, bounds.top, tlh, tlv, CORNER.TOP_LEFT) : new _Vector2.default(bounds.left, bounds.top),\n        topLeftInner: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, Math.max(0, tlh - borders[LEFT].borderWidth), Math.max(0, tlv - borders[TOP].borderWidth), CORNER.TOP_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        topRightOuter: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + topWidth, bounds.top, trh, trv, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top),\n        topRightInner: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + Math.min(topWidth, bounds.width + borders[LEFT].borderWidth), bounds.top + borders[TOP].borderWidth, topWidth > bounds.width + borders[LEFT].borderWidth ? 0 : trh - borders[LEFT].borderWidth, trv - borders[TOP].borderWidth, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        bottomRightOuter: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh, brv, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top + bounds.height),\n        bottomRightInner: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + Math.min(bottomWidth, bounds.width - borders[LEFT].borderWidth), bounds.top + Math.min(rightHeight, bounds.height + borders[TOP].borderWidth), Math.max(0, brh - borders[RIGHT].borderWidth), brv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth),\n        bottomLeftOuter: blh > 0 || blv > 0 ? getCurvePoints(bounds.left, bounds.top + leftHeight, blh, blv, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left, bounds.top + bounds.height),\n        bottomLeftInner: blh > 0 || blv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + leftHeight, Math.max(0, blh - borders[LEFT].borderWidth), blv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth)\n    };\n};\n\nvar CORNER = {\n    TOP_LEFT: 0,\n    TOP_RIGHT: 1,\n    BOTTOM_RIGHT: 2,\n    BOTTOM_LEFT: 3\n};\n\nvar getCurvePoints = function getCurvePoints(x, y, r1, r2, position) {\n    var kappa = 4 * ((Math.sqrt(2) - 1) / 3);\n    var ox = r1 * kappa; // control point offset horizontal\n    var oy = r2 * kappa; // control point offset vertical\n    var xm = x + r1; // x-middle\n    var ym = y + r2; // y-middle\n\n    switch (position) {\n        case CORNER.TOP_LEFT:\n            return new _BezierCurve2.default(new _Vector2.default(x, ym), new _Vector2.default(x, ym - oy), new _Vector2.default(xm - ox, y), new _Vector2.default(xm, y));\n        case CORNER.TOP_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(x, y), new _Vector2.default(x + ox, y), new _Vector2.default(xm, ym - oy), new _Vector2.default(xm, ym));\n        case CORNER.BOTTOM_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(xm, y), new _Vector2.default(xm, y + oy), new _Vector2.default(x + ox, ym), new _Vector2.default(x, ym));\n        case CORNER.BOTTOM_LEFT:\n        default:\n            return new _BezierCurve2.default(new _Vector2.default(xm, ym), new _Vector2.default(xm - ox, ym), new _Vector2.default(x, y + oy), new _Vector2.default(x, y));\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.calculateLengthFromValueWithUnit = exports.LENGTH_TYPE = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LENGTH_WITH_UNIT = /([\\d.]+)(px|r?em|%)/i;\n\nvar LENGTH_TYPE = exports.LENGTH_TYPE = {\n    PX: 0,\n    PERCENTAGE: 1\n};\n\nvar Length = function () {\n    function Length(value) {\n        _classCallCheck(this, Length);\n\n        this.type = value.substr(value.length - 1) === '%' ? LENGTH_TYPE.PERCENTAGE : LENGTH_TYPE.PX;\n        var parsedValue = parseFloat(value);\n        if (process.env.NODE_ENV !== 'production' && isNaN(parsedValue)) {\n            console.error('Invalid value given for Length: \"' + value + '\"');\n        }\n        this.value = isNaN(parsedValue) ? 0 : parsedValue;\n    }\n\n    _createClass(Length, [{\n        key: 'isPercentage',\n        value: function isPercentage() {\n            return this.type === LENGTH_TYPE.PERCENTAGE;\n        }\n    }, {\n        key: 'getAbsoluteValue',\n        value: function getAbsoluteValue(parentLength) {\n            return this.isPercentage() ? parentLength * (this.value / 100) : this.value;\n        }\n    }], [{\n        key: 'create',\n        value: function create(v) {\n            return new Length(v);\n        }\n    }]);\n\n    return Length;\n}();\n\nexports.default = Length;\n\n\nvar getRootFontSize = function getRootFontSize(container) {\n    var parent = container.parent;\n    return parent ? getRootFontSize(parent) : parseFloat(container.style.font.fontSize);\n};\n\nvar calculateLengthFromValueWithUnit = exports.calculateLengthFromValueWithUnit = function calculateLengthFromValueWithUnit(container, value, unit) {\n    switch (unit) {\n        case 'px':\n        case '%':\n            return new Length(value + unit);\n        case 'em':\n        case 'rem':\n            var length = new Length(value);\n            length.value *= unit === 'em' ? parseFloat(container.style.font.fontSize) : getRootFontSize(container);\n            return length;\n        default:\n            // TODO: handle correctly if unknown unit is used\n            return new Length('0');\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _borderRadius = require('./parsing/borderRadius');\n\nvar _display = require('./parsing/display');\n\nvar _float = require('./parsing/float');\n\nvar _font = require('./parsing/font');\n\nvar _letterSpacing = require('./parsing/letterSpacing');\n\nvar _lineBreak = require('./parsing/lineBreak');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _margin = require('./parsing/margin');\n\nvar _overflow = require('./parsing/overflow');\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nvar _padding = require('./parsing/padding');\n\nvar _position = require('./parsing/position');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _textShadow = require('./parsing/textShadow');\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _transform = require('./parsing/transform');\n\nvar _visibility = require('./parsing/visibility');\n\nvar _wordBreak = require('./parsing/word-break');\n\nvar _zIndex = require('./parsing/zIndex');\n\nvar _Bounds = require('./Bounds');\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar INPUT_TAGS = ['INPUT', 'TEXTAREA', 'SELECT'];\n\nvar NodeContainer = function () {\n    function NodeContainer(node, parent, resourceLoader, index) {\n        var _this = this;\n\n        _classCallCheck(this, NodeContainer);\n\n        this.parent = parent;\n        this.tagName = node.tagName;\n        this.index = index;\n        this.childNodes = [];\n        this.listItems = [];\n        if (typeof node.start === 'number') {\n            this.listStart = node.start;\n        }\n        var defaultView = node.ownerDocument.defaultView;\n        var scrollX = defaultView.pageXOffset;\n        var scrollY = defaultView.pageYOffset;\n        var style = defaultView.getComputedStyle(node, null);\n        var display = (0, _display.parseDisplay)(style.display);\n\n        var IS_INPUT = node.type === 'radio' || node.type === 'checkbox';\n\n        var position = (0, _position.parsePosition)(style.position);\n\n        this.style = {\n            background: IS_INPUT ? _Input.INPUT_BACKGROUND : (0, _background.parseBackground)(style, resourceLoader),\n            border: IS_INPUT ? _Input.INPUT_BORDERS : (0, _border.parseBorder)(style),\n            borderRadius: (node instanceof defaultView.HTMLInputElement || node instanceof HTMLInputElement) && IS_INPUT ? (0, _Input.getInputBorderRadius)(node) : (0, _borderRadius.parseBorderRadius)(style),\n            color: IS_INPUT ? _Input.INPUT_COLOR : new _Color2.default(style.color),\n            display: display,\n            float: (0, _float.parseCSSFloat)(style.float),\n            font: (0, _font.parseFont)(style),\n            letterSpacing: (0, _letterSpacing.parseLetterSpacing)(style.letterSpacing),\n            listStyle: display === _display.DISPLAY.LIST_ITEM ? (0, _listStyle.parseListStyle)(style) : null,\n            lineBreak: (0, _lineBreak.parseLineBreak)(style.lineBreak),\n            margin: (0, _margin.parseMargin)(style),\n            opacity: parseFloat(style.opacity),\n            overflow: INPUT_TAGS.indexOf(node.tagName) === -1 ? (0, _overflow.parseOverflow)(style.overflow) : _overflow.OVERFLOW.HIDDEN,\n            overflowWrap: (0, _overflowWrap.parseOverflowWrap)(style.overflowWrap ? style.overflowWrap : style.wordWrap),\n            padding: (0, _padding.parsePadding)(style),\n            position: position,\n            textDecoration: (0, _textDecoration.parseTextDecoration)(style),\n            textShadow: (0, _textShadow.parseTextShadow)(style.textShadow),\n            textTransform: (0, _textTransform.parseTextTransform)(style.textTransform),\n            transform: (0, _transform.parseTransform)(style),\n            visibility: (0, _visibility.parseVisibility)(style.visibility),\n            wordBreak: (0, _wordBreak.parseWordBreak)(style.wordBreak),\n            zIndex: (0, _zIndex.parseZIndex)(position !== _position.POSITION.STATIC ? style.zIndex : 'auto')\n        };\n\n        if (this.isTransformed()) {\n            // getBoundingClientRect provides values post-transform, we want them without the transformation\n            node.style.transform = 'matrix(1,0,0,1,0,0)';\n        }\n\n        if (display === _display.DISPLAY.LIST_ITEM) {\n            var listOwner = (0, _ListItem.getListOwner)(this);\n            if (listOwner) {\n                var listIndex = listOwner.listItems.length;\n                listOwner.listItems.push(this);\n                this.listIndex = node.hasAttribute('value') && typeof node.value === 'number' ? node.value : listIndex === 0 ? typeof listOwner.listStart === 'number' ? listOwner.listStart : 1 : listOwner.listItems[listIndex - 1].listIndex + 1;\n            }\n        }\n\n        // TODO move bound retrieval for all nodes to a later stage?\n        if (node.tagName === 'IMG') {\n            node.addEventListener('load', function () {\n                _this.bounds = (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n                _this.curvedBounds = (0, _Bounds.parseBoundCurves)(_this.bounds, _this.style.border, _this.style.borderRadius);\n            });\n        }\n        this.image = getImage(node, resourceLoader);\n        this.bounds = IS_INPUT ? (0, _Input.reformatInputBounds)((0, _Bounds.parseBounds)(node, scrollX, scrollY)) : (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n        this.curvedBounds = (0, _Bounds.parseBoundCurves)(this.bounds, this.style.border, this.style.borderRadius);\n\n        if (process.env.NODE_ENV !== 'production') {\n            this.name = '' + node.tagName.toLowerCase() + (node.id ? '#' + node.id : '') + node.className.toString().split(' ').map(function (s) {\n                return s.length ? '.' + s : '';\n            }).join('');\n        }\n    }\n\n    _createClass(NodeContainer, [{\n        key: 'getClipPaths',\n        value: function getClipPaths() {\n            var parentClips = this.parent ? this.parent.getClipPaths() : [];\n            var isClipped = this.style.overflow !== _overflow.OVERFLOW.VISIBLE;\n\n            return isClipped ? parentClips.concat([(0, _Bounds.calculatePaddingBoxPath)(this.curvedBounds)]) : parentClips;\n        }\n    }, {\n        key: 'isInFlow',\n        value: function isInFlow() {\n            return this.isRootElement() && !this.isFloating() && !this.isAbsolutelyPositioned();\n        }\n    }, {\n        key: 'isVisible',\n        value: function isVisible() {\n            return !(0, _Util.contains)(this.style.display, _display.DISPLAY.NONE) && this.style.opacity > 0 && this.style.visibility === _visibility.VISIBILITY.VISIBLE;\n        }\n    }, {\n        key: 'isAbsolutelyPositioned',\n        value: function isAbsolutelyPositioned() {\n            return this.style.position !== _position.POSITION.STATIC && this.style.position !== _position.POSITION.RELATIVE;\n        }\n    }, {\n        key: 'isPositioned',\n        value: function isPositioned() {\n            return this.style.position !== _position.POSITION.STATIC;\n        }\n    }, {\n        key: 'isFloating',\n        value: function isFloating() {\n            return this.style.float !== _float.FLOAT.NONE;\n        }\n    }, {\n        key: 'isRootElement',\n        value: function isRootElement() {\n            return this.parent === null;\n        }\n    }, {\n        key: 'isTransformed',\n        value: function isTransformed() {\n            return this.style.transform !== null;\n        }\n    }, {\n        key: 'isPositionedWithZIndex',\n        value: function isPositionedWithZIndex() {\n            return this.isPositioned() && !this.style.zIndex.auto;\n        }\n    }, {\n        key: 'isInlineLevel',\n        value: function isInlineLevel() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_FLEX) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_GRID) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_LIST_ITEM) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }, {\n        key: 'isInlineBlockOrInlineTable',\n        value: function isInlineBlockOrInlineTable() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }]);\n\n    return NodeContainer;\n}();\n\nexports.default = NodeContainer;\n\n\nvar getImage = function getImage(node, resourceLoader) {\n    if (node instanceof node.ownerDocument.defaultView.SVGSVGElement || node instanceof SVGSVGElement) {\n        var bounds = (0, _Bounds.parseBounds)(node, 0, 0);\n        node.setAttribute('width', bounds.width + 'px');\n        node.setAttribute('height', bounds.height + 'px');\n        var s = new XMLSerializer();\n        return resourceLoader.loadImage('data:image/svg+xml,' + encodeURIComponent(s.serializeToString(node)));\n    }\n    switch (node.tagName) {\n        case 'IMG':\n            // $FlowFixMe\n            var img = node;\n            return resourceLoader.loadImage(img.currentSrc || img.src);\n        case 'CANVAS':\n            // $FlowFixMe\n            var canvas = node;\n            return resourceLoader.loadCanvas(canvas);\n        case 'IFRAME':\n            var iframeKey = node.getAttribute('data-html2canvas-internal-iframe-key');\n            if (iframeKey) {\n                return iframeKey;\n            }\n            break;\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar contains = exports.contains = function contains(bit, value) {\n    return (bit & value) !== 0;\n};\n\nvar distance = exports.distance = function distance(a, b) {\n    return Math.sqrt(a * a + b * b);\n};\n\nvar copyCSSStyles = exports.copyCSSStyles = function copyCSSStyles(style, target) {\n    // Edge does not provide value for cssText\n    for (var i = style.length - 1; i >= 0; i--) {\n        var property = style.item(i);\n        // Safari shows pseudoelements if content is set\n        if (property !== 'content') {\n            target.style.setProperty(property, style.getPropertyValue(property));\n        }\n    }\n    return target;\n};\n\nvar SMALL_IMAGE = exports.SMALL_IMAGE = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBackgroundImage = exports.parseBackground = exports.calculateBackgroundRepeatPath = exports.calculateBackgroundPosition = exports.calculateBackgroungPositioningArea = exports.calculateBackgroungPaintingArea = exports.calculateGradientBackgroundSize = exports.calculateBackgroundSize = exports.BACKGROUND_ORIGIN = exports.BACKGROUND_CLIP = exports.BACKGROUND_SIZE = exports.BACKGROUND_REPEAT = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Size = require('../drawing/Size');\n\nvar _Size2 = _interopRequireDefault(_Size);\n\nvar _Vector = require('../drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Bounds = require('../Bounds');\n\nvar _padding = require('./padding');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar BACKGROUND_REPEAT = exports.BACKGROUND_REPEAT = {\n    REPEAT: 0,\n    NO_REPEAT: 1,\n    REPEAT_X: 2,\n    REPEAT_Y: 3\n};\n\nvar BACKGROUND_SIZE = exports.BACKGROUND_SIZE = {\n    AUTO: 0,\n    CONTAIN: 1,\n    COVER: 2,\n    LENGTH: 3\n};\n\nvar BACKGROUND_CLIP = exports.BACKGROUND_CLIP = {\n    BORDER_BOX: 0,\n    PADDING_BOX: 1,\n    CONTENT_BOX: 2\n};\n\nvar BACKGROUND_ORIGIN = exports.BACKGROUND_ORIGIN = BACKGROUND_CLIP;\n\nvar AUTO = 'auto';\n\nvar BackgroundSize = function BackgroundSize(size) {\n    _classCallCheck(this, BackgroundSize);\n\n    switch (size) {\n        case 'contain':\n            this.size = BACKGROUND_SIZE.CONTAIN;\n            break;\n        case 'cover':\n            this.size = BACKGROUND_SIZE.COVER;\n            break;\n        case 'auto':\n            this.size = BACKGROUND_SIZE.AUTO;\n            break;\n        default:\n            this.value = new _Length2.default(size);\n    }\n};\n\nvar calculateBackgroundSize = exports.calculateBackgroundSize = function calculateBackgroundSize(backgroundImage, image, bounds) {\n    var width = 0;\n    var height = 0;\n    var size = backgroundImage.size;\n    if (size[0].size === BACKGROUND_SIZE.CONTAIN || size[0].size === BACKGROUND_SIZE.COVER) {\n        var targetRatio = bounds.width / bounds.height;\n        var currentRatio = image.width / image.height;\n        return targetRatio < currentRatio !== (size[0].size === BACKGROUND_SIZE.COVER) ? new _Size2.default(bounds.width, bounds.width / currentRatio) : new _Size2.default(bounds.height * currentRatio, bounds.height);\n    }\n\n    if (size[0].value) {\n        width = size[0].value.getAbsoluteValue(bounds.width);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO && size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = image.height;\n    } else if (size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = width / image.width * image.height;\n    } else if (size[1].value) {\n        height = size[1].value.getAbsoluteValue(bounds.height);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO) {\n        width = height / image.height * image.width;\n    }\n\n    return new _Size2.default(width, height);\n};\n\nvar calculateGradientBackgroundSize = exports.calculateGradientBackgroundSize = function calculateGradientBackgroundSize(backgroundImage, bounds) {\n    var size = backgroundImage.size;\n    var width = size[0].value ? size[0].value.getAbsoluteValue(bounds.width) : bounds.width;\n    var height = size[1].value ? size[1].value.getAbsoluteValue(bounds.height) : size[0].value ? width : bounds.height;\n\n    return new _Size2.default(width, height);\n};\n\nvar AUTO_SIZE = new BackgroundSize(AUTO);\n\nvar calculateBackgroungPaintingArea = exports.calculateBackgroungPaintingArea = function calculateBackgroungPaintingArea(curves, clip) {\n    switch (clip) {\n        case BACKGROUND_CLIP.BORDER_BOX:\n            return (0, _Bounds.calculateBorderBoxPath)(curves);\n        case BACKGROUND_CLIP.PADDING_BOX:\n        default:\n            return (0, _Bounds.calculatePaddingBoxPath)(curves);\n    }\n};\n\nvar calculateBackgroungPositioningArea = exports.calculateBackgroungPositioningArea = function calculateBackgroungPositioningArea(backgroundOrigin, bounds, padding, border) {\n    var paddingBox = (0, _Bounds.calculatePaddingBox)(bounds, border);\n\n    switch (backgroundOrigin) {\n        case BACKGROUND_ORIGIN.BORDER_BOX:\n            return bounds;\n        case BACKGROUND_ORIGIN.CONTENT_BOX:\n            var paddingLeft = padding[_padding.PADDING_SIDES.LEFT].getAbsoluteValue(bounds.width);\n            var paddingRight = padding[_padding.PADDING_SIDES.RIGHT].getAbsoluteValue(bounds.width);\n            var paddingTop = padding[_padding.PADDING_SIDES.TOP].getAbsoluteValue(bounds.width);\n            var paddingBottom = padding[_padding.PADDING_SIDES.BOTTOM].getAbsoluteValue(bounds.width);\n            return new _Bounds.Bounds(paddingBox.left + paddingLeft, paddingBox.top + paddingTop, paddingBox.width - paddingLeft - paddingRight, paddingBox.height - paddingTop - paddingBottom);\n        case BACKGROUND_ORIGIN.PADDING_BOX:\n        default:\n            return paddingBox;\n    }\n};\n\nvar calculateBackgroundPosition = exports.calculateBackgroundPosition = function calculateBackgroundPosition(position, size, bounds) {\n    return new _Vector2.default(position[0].getAbsoluteValue(bounds.width - size.width), position[1].getAbsoluteValue(bounds.height - size.height));\n};\n\nvar calculateBackgroundRepeatPath = exports.calculateBackgroundRepeatPath = function calculateBackgroundRepeatPath(background, position, size, backgroundPositioningArea, bounds) {\n    var repeat = background.repeat;\n    switch (repeat) {\n        case BACKGROUND_REPEAT.REPEAT_X:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(size.height + backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left), Math.round(size.height + backgroundPositioningArea.top + position.y))];\n        case BACKGROUND_REPEAT.REPEAT_Y:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.height + bounds.top))];\n        case BACKGROUND_REPEAT.NO_REPEAT:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y + size.height)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y + size.height))];\n        default:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(bounds.left), Math.round(bounds.height + bounds.top))];\n    }\n};\n\nvar parseBackground = exports.parseBackground = function parseBackground(style, resourceLoader) {\n    return {\n        backgroundColor: new _Color2.default(style.backgroundColor),\n        backgroundImage: parseBackgroundImages(style, resourceLoader),\n        backgroundClip: parseBackgroundClip(style.backgroundClip),\n        backgroundOrigin: parseBackgroundOrigin(style.backgroundOrigin)\n    };\n};\n\nvar parseBackgroundClip = function parseBackgroundClip(backgroundClip) {\n    switch (backgroundClip) {\n        case 'padding-box':\n            return BACKGROUND_CLIP.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_CLIP.CONTENT_BOX;\n    }\n    return BACKGROUND_CLIP.BORDER_BOX;\n};\n\nvar parseBackgroundOrigin = function parseBackgroundOrigin(backgroundOrigin) {\n    switch (backgroundOrigin) {\n        case 'padding-box':\n            return BACKGROUND_ORIGIN.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_ORIGIN.CONTENT_BOX;\n    }\n    return BACKGROUND_ORIGIN.BORDER_BOX;\n};\n\nvar parseBackgroundRepeat = function parseBackgroundRepeat(backgroundRepeat) {\n    switch (backgroundRepeat.trim()) {\n        case 'no-repeat':\n            return BACKGROUND_REPEAT.NO_REPEAT;\n        case 'repeat-x':\n        case 'repeat no-repeat':\n            return BACKGROUND_REPEAT.REPEAT_X;\n        case 'repeat-y':\n        case 'no-repeat repeat':\n            return BACKGROUND_REPEAT.REPEAT_Y;\n        case 'repeat':\n            return BACKGROUND_REPEAT.REPEAT;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid background-repeat value \"' + backgroundRepeat + '\"');\n    }\n\n    return BACKGROUND_REPEAT.REPEAT;\n};\n\nvar parseBackgroundImages = function parseBackgroundImages(style, resourceLoader) {\n    var sources = parseBackgroundImage(style.backgroundImage).map(function (backgroundImage) {\n        if (backgroundImage.method === 'url') {\n            var key = resourceLoader.loadImage(backgroundImage.args[0]);\n            backgroundImage.args = key ? [key] : [];\n        }\n        return backgroundImage;\n    });\n    var positions = style.backgroundPosition.split(',');\n    var repeats = style.backgroundRepeat.split(',');\n    var sizes = style.backgroundSize.split(',');\n\n    return sources.map(function (source, index) {\n        var size = (sizes[index] || AUTO).trim().split(' ').map(parseBackgroundSize);\n        var position = (positions[index] || AUTO).trim().split(' ').map(parseBackgoundPosition);\n\n        return {\n            source: source,\n            repeat: parseBackgroundRepeat(typeof repeats[index] === 'string' ? repeats[index] : repeats[0]),\n            size: size.length < 2 ? [size[0], AUTO_SIZE] : [size[0], size[1]],\n            position: position.length < 2 ? [position[0], position[0]] : [position[0], position[1]]\n        };\n    });\n};\n\nvar parseBackgroundSize = function parseBackgroundSize(size) {\n    return size === 'auto' ? AUTO_SIZE : new BackgroundSize(size);\n};\n\nvar parseBackgoundPosition = function parseBackgoundPosition(position) {\n    switch (position) {\n        case 'bottom':\n        case 'right':\n            return new _Length2.default('100%');\n        case 'left':\n        case 'top':\n            return new _Length2.default('0%');\n        case 'auto':\n            return new _Length2.default('0');\n    }\n    return new _Length2.default(position);\n};\n\nvar parseBackgroundImage = exports.parseBackgroundImage = function parseBackgroundImage(image) {\n    var whitespace = /^\\s$/;\n    var results = [];\n\n    var args = [];\n    var method = '';\n    var quote = null;\n    var definition = '';\n    var mode = 0;\n    var numParen = 0;\n\n    var appendResult = function appendResult() {\n        var prefix = '';\n        if (method) {\n            if (definition.substr(0, 1) === '\"') {\n                definition = definition.substr(1, definition.length - 2);\n            }\n\n            if (definition) {\n                args.push(definition.trim());\n            }\n\n            var prefix_i = method.indexOf('-', 1) + 1;\n            if (method.substr(0, 1) === '-' && prefix_i > 0) {\n                prefix = method.substr(0, prefix_i).toLowerCase();\n                method = method.substr(prefix_i);\n            }\n            method = method.toLowerCase();\n            if (method !== 'none') {\n                results.push({\n                    prefix: prefix,\n                    method: method,\n                    args: args\n                });\n            }\n        }\n        args = [];\n        method = definition = '';\n    };\n\n    image.split('').forEach(function (c) {\n        if (mode === 0 && whitespace.test(c)) {\n            return;\n        }\n        switch (c) {\n            case '\"':\n                if (!quote) {\n                    quote = c;\n                } else if (quote === c) {\n                    quote = null;\n                }\n                break;\n            case '(':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    mode = 1;\n                    return;\n                } else {\n                    numParen++;\n                }\n                break;\n            case ')':\n                if (quote) {\n                    break;\n                } else if (mode === 1) {\n                    if (numParen === 0) {\n                        mode = 0;\n                        appendResult();\n                        return;\n                    } else {\n                        numParen--;\n                    }\n                }\n                break;\n\n            case ',':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    appendResult();\n                    return;\n                } else if (mode === 1) {\n                    if (numParen === 0 && !method.match(/^url$/i)) {\n                        args.push(definition.trim());\n                        definition = '';\n                        return;\n                    }\n                }\n                break;\n        }\n\n        if (mode === 0) {\n            method += c;\n        } else {\n            definition += c;\n        }\n    });\n\n    appendResult();\n    return results;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar PATH = exports.PATH = {\n    VECTOR: 0,\n    BEZIER_CURVE: 1,\n    CIRCLE: 2\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Vector = function Vector(x, y) {\n    _classCallCheck(this, Vector);\n\n    this.type = _Path.PATH.VECTOR;\n    this.x = x;\n    this.y = y;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Vector');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Vector');\n        }\n    }\n};\n\nexports.default = Vector;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseListStyle = exports.parseListStyleType = exports.LIST_STYLE_TYPE = exports.LIST_STYLE_POSITION = undefined;\n\nvar _background = require('./background');\n\nvar LIST_STYLE_POSITION = exports.LIST_STYLE_POSITION = {\n    INSIDE: 0,\n    OUTSIDE: 1\n};\n\nvar LIST_STYLE_TYPE = exports.LIST_STYLE_TYPE = {\n    NONE: -1,\n    DISC: 0,\n    CIRCLE: 1,\n    SQUARE: 2,\n    DECIMAL: 3,\n    CJK_DECIMAL: 4,\n    DECIMAL_LEADING_ZERO: 5,\n    LOWER_ROMAN: 6,\n    UPPER_ROMAN: 7,\n    LOWER_GREEK: 8,\n    LOWER_ALPHA: 9,\n    UPPER_ALPHA: 10,\n    ARABIC_INDIC: 11,\n    ARMENIAN: 12,\n    BENGALI: 13,\n    CAMBODIAN: 14,\n    <PERSON><PERSON><PERSON>_EARTHLY_BRANCH: 15,\n    <PERSON><PERSON><PERSON>_HEAVENLY_STEM: 16,\n    <PERSON><PERSON><PERSON>_IDEOGRAPHIC: 17,\n    DEVANAGARI: 18,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>_NUMERIC: 19,\n    GEORGIAN: 20,\n    GUJARATI: 21,\n    GURMUKHI: 22,\n    HEBREW: 22,\n    HIRAGANA: 23,\n    HIRAGANA_IROHA: 24,\n    JAPANESE_FORMAL: 25,\n    JAPANESE_INFORMAL: 26,\n    KANNADA: 27,\n    KATAKANA: 28,\n    KATAKANA_IROHA: 29,\n    KHMER: 30,\n    KOREAN_HANGUL_FORMAL: 31,\n    KOREAN_HANJA_FORMAL: 32,\n    KOREAN_HANJA_INFORMAL: 33,\n    LAO: 34,\n    LOWER_ARMENIAN: 35,\n    MALAYALAM: 36,\n    MONGOLIAN: 37,\n    MYANMAR: 38,\n    ORIYA: 39,\n    PERSIAN: 40,\n    SIMP_CHINESE_FORMAL: 41,\n    SIMP_CHINESE_INFORMAL: 42,\n    TAMIL: 43,\n    TELUGU: 44,\n    THAI: 45,\n    TIBETAN: 46,\n    TRAD_CHINESE_FORMAL: 47,\n    TRAD_CHINESE_INFORMAL: 48,\n    UPPER_ARMENIAN: 49,\n    DISCLOSURE_OPEN: 50,\n    DISCLOSURE_CLOSED: 51\n};\n\nvar parseListStyleType = exports.parseListStyleType = function parseListStyleType(type) {\n    switch (type) {\n        case 'disc':\n            return LIST_STYLE_TYPE.DISC;\n        case 'circle':\n            return LIST_STYLE_TYPE.CIRCLE;\n        case 'square':\n            return LIST_STYLE_TYPE.SQUARE;\n        case 'decimal':\n            return LIST_STYLE_TYPE.DECIMAL;\n        case 'cjk-decimal':\n            return LIST_STYLE_TYPE.CJK_DECIMAL;\n        case 'decimal-leading-zero':\n            return LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO;\n        case 'lower-roman':\n            return LIST_STYLE_TYPE.LOWER_ROMAN;\n        case 'upper-roman':\n            return LIST_STYLE_TYPE.UPPER_ROMAN;\n        case 'lower-greek':\n            return LIST_STYLE_TYPE.LOWER_GREEK;\n        case 'lower-alpha':\n            return LIST_STYLE_TYPE.LOWER_ALPHA;\n        case 'upper-alpha':\n            return LIST_STYLE_TYPE.UPPER_ALPHA;\n        case 'arabic-indic':\n            return LIST_STYLE_TYPE.ARABIC_INDIC;\n        case 'armenian':\n            return LIST_STYLE_TYPE.ARMENIAN;\n        case 'bengali':\n            return LIST_STYLE_TYPE.BENGALI;\n        case 'cambodian':\n            return LIST_STYLE_TYPE.CAMBODIAN;\n        case 'cjk-earthly-branch':\n            return LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH;\n        case 'cjk-heavenly-stem':\n            return LIST_STYLE_TYPE.CJK_HEAVENLY_STEM;\n        case 'cjk-ideographic':\n            return LIST_STYLE_TYPE.CJK_IDEOGRAPHIC;\n        case 'devanagari':\n            return LIST_STYLE_TYPE.DEVANAGARI;\n        case 'ethiopic-numeric':\n            return LIST_STYLE_TYPE.ETHIOPIC_NUMERIC;\n        case 'georgian':\n            return LIST_STYLE_TYPE.GEORGIAN;\n        case 'gujarati':\n            return LIST_STYLE_TYPE.GUJARATI;\n        case 'gurmukhi':\n            return LIST_STYLE_TYPE.GURMUKHI;\n        case 'hebrew':\n            return LIST_STYLE_TYPE.HEBREW;\n        case 'hiragana':\n            return LIST_STYLE_TYPE.HIRAGANA;\n        case 'hiragana-iroha':\n            return LIST_STYLE_TYPE.HIRAGANA_IROHA;\n        case 'japanese-formal':\n            return LIST_STYLE_TYPE.JAPANESE_FORMAL;\n        case 'japanese-informal':\n            return LIST_STYLE_TYPE.JAPANESE_INFORMAL;\n        case 'kannada':\n            return LIST_STYLE_TYPE.KANNADA;\n        case 'katakana':\n            return LIST_STYLE_TYPE.KATAKANA;\n        case 'katakana-iroha':\n            return LIST_STYLE_TYPE.KATAKANA_IROHA;\n        case 'khmer':\n            return LIST_STYLE_TYPE.KHMER;\n        case 'korean-hangul-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL;\n        case 'korean-hanja-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL;\n        case 'korean-hanja-informal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL;\n        case 'lao':\n            return LIST_STYLE_TYPE.LAO;\n        case 'lower-armenian':\n            return LIST_STYLE_TYPE.LOWER_ARMENIAN;\n        case 'malayalam':\n            return LIST_STYLE_TYPE.MALAYALAM;\n        case 'mongolian':\n            return LIST_STYLE_TYPE.MONGOLIAN;\n        case 'myanmar':\n            return LIST_STYLE_TYPE.MYANMAR;\n        case 'oriya':\n            return LIST_STYLE_TYPE.ORIYA;\n        case 'persian':\n            return LIST_STYLE_TYPE.PERSIAN;\n        case 'simp-chinese-formal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL;\n        case 'simp-chinese-informal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL;\n        case 'tamil':\n            return LIST_STYLE_TYPE.TAMIL;\n        case 'telugu':\n            return LIST_STYLE_TYPE.TELUGU;\n        case 'thai':\n            return LIST_STYLE_TYPE.THAI;\n        case 'tibetan':\n            return LIST_STYLE_TYPE.TIBETAN;\n        case 'trad-chinese-formal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL;\n        case 'trad-chinese-informal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL;\n        case 'upper-armenian':\n            return LIST_STYLE_TYPE.UPPER_ARMENIAN;\n        case 'disclosure-open':\n            return LIST_STYLE_TYPE.DISCLOSURE_OPEN;\n        case 'disclosure-closed':\n            return LIST_STYLE_TYPE.DISCLOSURE_CLOSED;\n        case 'none':\n        default:\n            return LIST_STYLE_TYPE.NONE;\n    }\n};\n\nvar parseListStyle = exports.parseListStyle = function parseListStyle(style) {\n    var listStyleImage = (0, _background.parseBackgroundImage)(style.getPropertyValue('list-style-image'));\n    return {\n        listStyleType: parseListStyleType(style.getPropertyValue('list-style-type')),\n        listStyleImage: listStyleImage.length ? listStyleImage[0] : null,\n        listStylePosition: parseListStylePosition(style.getPropertyValue('list-style-position'))\n    };\n};\n\nvar parseListStylePosition = function parseListStylePosition(position) {\n    switch (position) {\n        case 'inside':\n            return LIST_STYLE_POSITION.INSIDE;\n        case 'outside':\n        default:\n            return LIST_STYLE_POSITION.OUTSIDE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _TextBounds = require('./TextBounds');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextContainer = function () {\n    function TextContainer(text, parent, bounds) {\n        _classCallCheck(this, TextContainer);\n\n        this.text = text;\n        this.parent = parent;\n        this.bounds = bounds;\n    }\n\n    _createClass(TextContainer, null, [{\n        key: 'fromTextNode',\n        value: function fromTextNode(node, parent) {\n            var text = transform(node.data, parent.style.textTransform);\n            return new TextContainer(text, parent, (0, _TextBounds.parseTextBounds)(text, parent, node));\n        }\n    }]);\n\n    return TextContainer;\n}();\n\nexports.default = TextContainer;\n\n\nvar CAPITALIZE = /(^|\\s|:|-|\\(|\\))([a-z])/g;\n\nvar transform = function transform(text, _transform) {\n    switch (_transform) {\n        case _textTransform.TEXT_TRANSFORM.LOWERCASE:\n            return text.toLowerCase();\n        case _textTransform.TEXT_TRANSFORM.CAPITALIZE:\n            return text.replace(CAPITALIZE, capitalize);\n        case _textTransform.TEXT_TRANSFORM.UPPERCASE:\n            return text.toUpperCase();\n        default:\n            return text;\n    }\n};\n\nfunction capitalize(m, p1, p2) {\n    if (m.length > 0) {\n        return p1 + p2.toUpperCase();\n    }\n\n    return m;\n}", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar testRangeBounds = function testRangeBounds(document) {\n    var TEST_HEIGHT = 123;\n\n    if (document.createRange) {\n        var range = document.createRange();\n        if (range.getBoundingClientRect) {\n            var testElement = document.createElement('boundtest');\n            testElement.style.height = TEST_HEIGHT + 'px';\n            testElement.style.display = 'block';\n            document.body.appendChild(testElement);\n\n            range.selectNode(testElement);\n            var rangeBounds = range.getBoundingClientRect();\n            var rangeHeight = Math.round(rangeBounds.height);\n            document.body.removeChild(testElement);\n            if (rangeHeight === TEST_HEIGHT) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n};\n\n// iOS 10.3 taints canvas with base64 images unless crossOrigin = 'anonymous'\nvar testBase64 = function testBase64(document, src) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n\n    return new Promise(function (resolve) {\n        // Single pixel base64 image renders fine on iOS 10.3???\n        img.src = src;\n\n        var onload = function onload() {\n            try {\n                ctx.drawImage(img, 0, 0);\n                canvas.toDataURL();\n            } catch (e) {\n                return resolve(false);\n            }\n\n            return resolve(true);\n        };\n\n        img.onload = onload;\n        img.onerror = function () {\n            return resolve(false);\n        };\n\n        if (img.complete === true) {\n            setTimeout(function () {\n                onload();\n            }, 500);\n        }\n    });\n};\n\nvar testCORS = function testCORS() {\n    return typeof new Image().crossOrigin !== 'undefined';\n};\n\nvar testResponseType = function testResponseType() {\n    return typeof new XMLHttpRequest().responseType === 'string';\n};\n\nvar testSVG = function testSVG(document) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n    img.src = 'data:image/svg+xml,<svg xmlns=\\'http://www.w3.org/2000/svg\\'></svg>';\n\n    try {\n        ctx.drawImage(img, 0, 0);\n        canvas.toDataURL();\n    } catch (e) {\n        return false;\n    }\n    return true;\n};\n\nvar isGreenPixel = function isGreenPixel(data) {\n    return data[0] === 0 && data[1] === 255 && data[2] === 0 && data[3] === 255;\n};\n\nvar testForeignObject = function testForeignObject(document) {\n    var canvas = document.createElement('canvas');\n    var size = 100;\n    canvas.width = size;\n    canvas.height = size;\n    var ctx = canvas.getContext('2d');\n    ctx.fillStyle = 'rgb(0, 255, 0)';\n    ctx.fillRect(0, 0, size, size);\n\n    var img = new Image();\n    var greenImageSrc = canvas.toDataURL();\n    img.src = greenImageSrc;\n    var svg = (0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, img);\n    ctx.fillStyle = 'red';\n    ctx.fillRect(0, 0, size, size);\n\n    return (0, _ForeignObjectRenderer.loadSerializedSVG)(svg).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        var data = ctx.getImageData(0, 0, size, size).data;\n        ctx.fillStyle = 'red';\n        ctx.fillRect(0, 0, size, size);\n\n        var node = document.createElement('div');\n        node.style.backgroundImage = 'url(' + greenImageSrc + ')';\n        node.style.height = size + 'px';\n        // Firefox 55 does not render inline <img /> tags\n        return isGreenPixel(data) ? (0, _ForeignObjectRenderer.loadSerializedSVG)((0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, node)) : Promise.reject(false);\n    }).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        // Edge does not render background-images\n        return isGreenPixel(ctx.getImageData(0, 0, size, size).data);\n    }).catch(function (e) {\n        return false;\n    });\n};\n\nvar FEATURES = {\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RANGE_BOUNDS() {\n        'use strict';\n\n        var value = testRangeBounds(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_RANGE_BOUNDS', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_SVG_DRAWING() {\n        'use strict';\n\n        var value = testSVG(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_SVG_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_BASE64_DRAWING() {\n        'use strict';\n\n        return function (src) {\n            var _value = testBase64(document, src);\n            Object.defineProperty(FEATURES, 'SUPPORT_BASE64_DRAWING', { value: function value() {\n                    return _value;\n                } });\n            return _value;\n        };\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_FOREIGNOBJECT_DRAWING() {\n        'use strict';\n\n        var value = typeof Array.from === 'function' && typeof window.fetch === 'function' ? testForeignObject(document) : Promise.resolve(false);\n        Object.defineProperty(FEATURES, 'SUPPORT_FOREIGNOBJECT_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_IMAGES() {\n        'use strict';\n\n        var value = testCORS();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_IMAGES', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RESPONSE_TYPE() {\n        'use strict';\n\n        var value = testResponseType();\n        Object.defineProperty(FEATURES, 'SUPPORT_RESPONSE_TYPE', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_XHR() {\n        'use strict';\n\n        var value = 'withCredentials' in new XMLHttpRequest();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_XHR', { value: value });\n        return value;\n    }\n};\n\nexports.default = FEATURES;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextDecoration = exports.TEXT_DECORATION_LINE = exports.TEXT_DECORATION = exports.TEXT_DECORATION_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar TEXT_DECORATION_STYLE = exports.TEXT_DECORATION_STYLE = {\n    SOLID: 0,\n    DOUBLE: 1,\n    DOTTED: 2,\n    DASHED: 3,\n    WAVY: 4\n};\n\nvar TEXT_DECORATION = exports.TEXT_DECORATION = {\n    NONE: null\n};\n\nvar TEXT_DECORATION_LINE = exports.TEXT_DECORATION_LINE = {\n    UNDERLINE: 1,\n    OVERLINE: 2,\n    LINE_THROUGH: 3,\n    BLINK: 4\n};\n\nvar parseLine = function parseLine(line) {\n    switch (line) {\n        case 'underline':\n            return TEXT_DECORATION_LINE.UNDERLINE;\n        case 'overline':\n            return TEXT_DECORATION_LINE.OVERLINE;\n        case 'line-through':\n            return TEXT_DECORATION_LINE.LINE_THROUGH;\n    }\n    return TEXT_DECORATION_LINE.BLINK;\n};\n\nvar parseTextDecorationLine = function parseTextDecorationLine(line) {\n    if (line === 'none') {\n        return null;\n    }\n\n    return line.split(' ').map(parseLine);\n};\n\nvar parseTextDecorationStyle = function parseTextDecorationStyle(style) {\n    switch (style) {\n        case 'double':\n            return TEXT_DECORATION_STYLE.DOUBLE;\n        case 'dotted':\n            return TEXT_DECORATION_STYLE.DOTTED;\n        case 'dashed':\n            return TEXT_DECORATION_STYLE.DASHED;\n        case 'wavy':\n            return TEXT_DECORATION_STYLE.WAVY;\n    }\n    return TEXT_DECORATION_STYLE.SOLID;\n};\n\nvar parseTextDecoration = exports.parseTextDecoration = function parseTextDecoration(style) {\n    var textDecorationLine = parseTextDecorationLine(style.textDecorationLine ? style.textDecorationLine : style.textDecoration);\n    if (textDecorationLine === null) {\n        return TEXT_DECORATION.NONE;\n    }\n\n    var textDecorationColor = style.textDecorationColor ? new _Color2.default(style.textDecorationColor) : null;\n    var textDecorationStyle = parseTextDecorationStyle(style.textDecorationStyle);\n\n    return {\n        textDecorationLine: textDecorationLine,\n        textDecorationColor: textDecorationColor,\n        textDecorationStyle: textDecorationStyle\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorder = exports.BORDER_SIDES = exports.BORDER_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar BORDER_STYLE = exports.BORDER_STYLE = {\n    NONE: 0,\n    SOLID: 1\n};\n\nvar BORDER_SIDES = exports.BORDER_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = Object.keys(BORDER_SIDES).map(function (s) {\n    return s.toLowerCase();\n});\n\nvar parseBorderStyle = function parseBorderStyle(style) {\n    switch (style) {\n        case 'none':\n            return BORDER_STYLE.NONE;\n    }\n    return BORDER_STYLE.SOLID;\n};\n\nvar parseBorder = exports.parseBorder = function parseBorder(style) {\n    return SIDES.map(function (side) {\n        var borderColor = new _Color2.default(style.getPropertyValue('border-' + side + '-color'));\n        var borderStyle = parseBorderStyle(style.getPropertyValue('border-' + side + '-style'));\n        var borderWidth = parseFloat(style.getPropertyValue('border-' + side + '-width'));\n        return {\n            borderColor: borderColor,\n            borderStyle: borderStyle,\n            borderWidth: isNaN(borderWidth) ? 0 : borderWidth\n        };\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar toCodePoints = exports.toCodePoints = function toCodePoints(str) {\n    var codePoints = [];\n    var i = 0;\n    var length = str.length;\n    while (i < length) {\n        var value = str.charCodeAt(i++);\n        if (value >= 0xd800 && value <= 0xdbff && i < length) {\n            var extra = str.charCodeAt(i++);\n            if ((extra & 0xfc00) === 0xdc00) {\n                codePoints.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n            } else {\n                codePoints.push(value);\n                i--;\n            }\n        } else {\n            codePoints.push(value);\n        }\n    }\n    return codePoints;\n};\n\nvar fromCodePoint = exports.fromCodePoint = function fromCodePoint() {\n    if (String.fromCodePoint) {\n        return String.fromCodePoint.apply(String, arguments);\n    }\n\n    var length = arguments.length;\n    if (!length) {\n        return '';\n    }\n\n    var codeUnits = [];\n\n    var index = -1;\n    var result = '';\n    while (++index < length) {\n        var codePoint = arguments.length <= index ? undefined : arguments[index];\n        if (codePoint <= 0xffff) {\n            codeUnits.push(codePoint);\n        } else {\n            codePoint -= 0x10000;\n            codeUnits.push((codePoint >> 10) + 0xd800, codePoint % 0x400 + 0xdc00);\n        }\n        if (index + 1 === length || codeUnits.length > 0x4000) {\n            result += String.fromCharCode.apply(String, codeUnits);\n            codeUnits.length = 0;\n        }\n    }\n    return result;\n};\n\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nvar decode = exports.decode = function decode(base64) {\n    var bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i = void 0,\n        p = 0,\n        encoded1 = void 0,\n        encoded2 = void 0,\n        encoded3 = void 0,\n        encoded4 = void 0;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = typeof ArrayBuffer !== 'undefined' && typeof Uint8Array !== 'undefined' && typeof Uint8Array.prototype.slice !== 'undefined' ? new ArrayBuffer(bufferLength) : new Array(bufferLength);\n    var bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n\n    return buffer;\n};\n\nvar polyUint16Array = exports.polyUint16Array = function polyUint16Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i = 0; _i < length; _i += 2) {\n        bytes.push(buffer[_i + 1] << 8 | buffer[_i]);\n    }\n    return bytes;\n};\n\nvar polyUint32Array = exports.polyUint32Array = function polyUint32Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i2 = 0; _i2 < length; _i2 += 4) {\n        bytes.push(buffer[_i2 + 3] << 24 | buffer[_i2 + 2] << 16 | buffer[_i2 + 1] << 8 | buffer[_i2]);\n    }\n    return bytes;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.createCounterText = exports.inlineListItemElement = exports.getListOwner = undefined;\n\nvar _Util = require('./Util');\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Margin between the enumeration and the list item content\nvar MARGIN_RIGHT = 7;\n\nvar ancestorTypes = ['OL', 'UL', 'MENU'];\n\nvar getListOwner = exports.getListOwner = function getListOwner(container) {\n    var parent = container.parent;\n    if (!parent) {\n        return null;\n    }\n\n    do {\n        var isAncestor = ancestorTypes.indexOf(parent.tagName) !== -1;\n        if (isAncestor) {\n            return parent;\n        }\n        parent = parent.parent;\n    } while (parent);\n\n    return container.parent;\n};\n\nvar inlineListItemElement = exports.inlineListItemElement = function inlineListItemElement(node, container, resourceLoader) {\n    var listStyle = container.style.listStyle;\n\n    if (!listStyle) {\n        return;\n    }\n\n    var style = node.ownerDocument.defaultView.getComputedStyle(node, null);\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    (0, _Util.copyCSSStyles)(style, wrapper);\n\n    wrapper.style.position = 'absolute';\n    wrapper.style.bottom = 'auto';\n    wrapper.style.display = 'block';\n    wrapper.style.letterSpacing = 'normal';\n\n    switch (listStyle.listStylePosition) {\n        case _listStyle.LIST_STYLE_POSITION.OUTSIDE:\n            wrapper.style.left = 'auto';\n            wrapper.style.right = node.ownerDocument.defaultView.innerWidth - container.bounds.left - container.style.margin[1].getAbsoluteValue(container.bounds.width) + MARGIN_RIGHT + 'px';\n            wrapper.style.textAlign = 'right';\n            break;\n        case _listStyle.LIST_STYLE_POSITION.INSIDE:\n            wrapper.style.left = container.bounds.left - container.style.margin[3].getAbsoluteValue(container.bounds.width) + 'px';\n            wrapper.style.right = 'auto';\n            wrapper.style.textAlign = 'left';\n            break;\n    }\n\n    var text = void 0;\n    var MARGIN_TOP = container.style.margin[0].getAbsoluteValue(container.bounds.width);\n    var styleImage = listStyle.listStyleImage;\n    if (styleImage) {\n        if (styleImage.method === 'url') {\n            var image = node.ownerDocument.createElement('img');\n            image.src = styleImage.args[0];\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n            wrapper.style.width = 'auto';\n            wrapper.style.height = 'auto';\n            wrapper.appendChild(image);\n        } else {\n            var size = parseFloat(container.style.font.fontSize) * 0.5;\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + container.bounds.height - 1.5 * size + 'px';\n            wrapper.style.width = size + 'px';\n            wrapper.style.height = size + 'px';\n            wrapper.style.backgroundImage = style.listStyleImage;\n        }\n    } else if (typeof container.listIndex === 'number') {\n        text = node.ownerDocument.createTextNode(createCounterText(container.listIndex, listStyle.listStyleType, true));\n        wrapper.appendChild(text);\n        wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n    }\n\n    // $FlowFixMe\n    var body = node.ownerDocument.body;\n    body.appendChild(wrapper);\n\n    if (text) {\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    } else {\n        // $FlowFixMe\n        container.childNodes.push(new _NodeContainer2.default(wrapper, container, resourceLoader, 0));\n    }\n};\n\nvar ROMAN_UPPER = {\n    integers: [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1],\n    values: ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I']\n};\n\nvar ARMENIAN = {\n    integers: [9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['Ք', 'Փ', 'Ւ', 'Ց', 'Ր', 'Տ', 'Վ', 'Ս', 'Ռ', 'Ջ', 'Պ', 'Չ', 'Ո', 'Շ', 'Ն', 'Յ', 'Մ', 'Ճ', 'Ղ', 'Ձ', 'Հ', 'Կ', 'Ծ', 'Խ', 'Լ', 'Ի', 'Ժ', 'Թ', 'Ը', 'Է', 'Զ', 'Ե', 'Դ', 'Գ', 'Բ', 'Ա']\n};\n\nvar HEBREW = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 19, 18, 17, 16, 15, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['י׳', 'ט׳', 'ח׳', 'ז׳', 'ו׳', 'ה׳', 'ד׳', 'ג׳', 'ב׳', 'א׳', 'ת', 'ש', 'ר', 'ק', 'צ', 'פ', 'ע', 'ס', 'נ', 'מ', 'ל', 'כ', 'יט', 'יח', 'יז', 'טז', 'טו', 'י', 'ט', 'ח', 'ז', 'ו', 'ה', 'ד', 'ג', 'ב', 'א']\n};\n\nvar GEORGIAN = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['ჵ', 'ჰ', 'ჯ', 'ჴ', 'ხ', 'ჭ', 'წ', 'ძ', 'ც', 'ჩ', 'შ', 'ყ', 'ღ', 'ქ', 'ფ', 'ჳ', 'ტ', 'ს', 'რ', 'ჟ', 'პ', 'ო', 'ჲ', 'ნ', 'მ', 'ლ', 'კ', 'ი', 'თ', 'ჱ', 'ზ', 'ვ', 'ე', 'დ', 'გ', 'ბ', 'ა']\n};\n\nvar createAdditiveCounter = function createAdditiveCounter(value, min, max, symbols, fallback, suffix) {\n    if (value < min || value > max) {\n        return createCounterText(value, fallback, suffix.length > 0);\n    }\n\n    return symbols.integers.reduce(function (string, integer, index) {\n        while (value >= integer) {\n            value -= integer;\n            string += symbols.values[index];\n        }\n        return string;\n    }, '') + suffix;\n};\n\nvar createCounterStyleWithSymbolResolver = function createCounterStyleWithSymbolResolver(value, codePointRangeLength, isNumeric, resolver) {\n    var string = '';\n\n    do {\n        if (!isNumeric) {\n            value--;\n        }\n        string = resolver(value) + string;\n        value /= codePointRangeLength;\n    } while (value * codePointRangeLength >= codePointRangeLength);\n\n    return string;\n};\n\nvar createCounterStyleFromRange = function createCounterStyleFromRange(value, codePointRangeStart, codePointRangeEnd, isNumeric, suffix) {\n    var codePointRangeLength = codePointRangeEnd - codePointRangeStart + 1;\n\n    return (value < 0 ? '-' : '') + (createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, isNumeric, function (codePoint) {\n        return (0, _Unicode.fromCodePoint)(Math.floor(codePoint % codePointRangeLength) + codePointRangeStart);\n    }) + suffix);\n};\n\nvar createCounterStyleFromSymbols = function createCounterStyleFromSymbols(value, symbols) {\n    var suffix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '. ';\n\n    var codePointRangeLength = symbols.length;\n    return createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, false, function (codePoint) {\n        return symbols[Math.floor(codePoint % codePointRangeLength)];\n    }) + suffix;\n};\n\nvar CJK_ZEROS = 1 << 0;\nvar CJK_TEN_COEFFICIENTS = 1 << 1;\nvar CJK_TEN_HIGH_COEFFICIENTS = 1 << 2;\nvar CJK_HUNDRED_COEFFICIENTS = 1 << 3;\n\nvar createCJKCounter = function createCJKCounter(value, numbers, multipliers, negativeSign, suffix, flags) {\n    if (value < -9999 || value > 9999) {\n        return createCounterText(value, _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL, suffix.length > 0);\n    }\n    var tmp = Math.abs(value);\n    var string = suffix;\n\n    if (tmp === 0) {\n        return numbers[0] + string;\n    }\n\n    for (var digit = 0; tmp > 0 && digit <= 4; digit++) {\n        var coefficient = tmp % 10;\n\n        if (coefficient === 0 && (0, _Util.contains)(flags, CJK_ZEROS) && string !== '') {\n            string = numbers[coefficient] + string;\n        } else if (coefficient > 1 || coefficient === 1 && digit === 0 || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_COEFFICIENTS) || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_HIGH_COEFFICIENTS) && value > 100 || coefficient === 1 && digit > 1 && (0, _Util.contains)(flags, CJK_HUNDRED_COEFFICIENTS)) {\n            string = numbers[coefficient] + (digit > 0 ? multipliers[digit - 1] : '') + string;\n        } else if (coefficient === 1 && digit > 0) {\n            string = multipliers[digit - 1] + string;\n        }\n        tmp = Math.floor(tmp / 10);\n    }\n\n    return (value < 0 ? negativeSign : '') + string;\n};\n\nvar CHINESE_INFORMAL_MULTIPLIERS = '十百千萬';\nvar CHINESE_FORMAL_MULTIPLIERS = '拾佰仟萬';\nvar JAPANESE_NEGATIVE = 'マイナス';\nvar KOREAN_NEGATIVE = '마이너스 ';\n\nvar createCounterText = exports.createCounterText = function createCounterText(value, type, appendSuffix) {\n    var defaultSuffix = appendSuffix ? '. ' : '';\n    var cjkSuffix = appendSuffix ? '、' : '';\n    var koreanSuffix = appendSuffix ? ', ' : '';\n    switch (type) {\n        case _listStyle.LIST_STYLE_TYPE.DISC:\n            return '•';\n        case _listStyle.LIST_STYLE_TYPE.CIRCLE:\n            return '◦';\n        case _listStyle.LIST_STYLE_TYPE.SQUARE:\n            return '◾';\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO:\n            var string = createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n            return string.length < 4 ? '0' + string : string;\n        case _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL:\n            return createCounterStyleFromSymbols(value, '〇一二三四五六七八九', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_GREEK:\n            return createCounterStyleFromRange(value, 945, 969, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ALPHA:\n            return createCounterStyleFromRange(value, 97, 122, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ALPHA:\n            return createCounterStyleFromRange(value, 65, 90, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARABIC_INDIC:\n            return createCounterStyleFromRange(value, 1632, 1641, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARMENIAN:\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.BENGALI:\n            return createCounterStyleFromRange(value, 2534, 2543, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CAMBODIAN:\n        case _listStyle.LIST_STYLE_TYPE.KHMER:\n            return createCounterStyleFromRange(value, 6112, 6121, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH:\n            return createCounterStyleFromSymbols(value, '子丑寅卯辰巳午未申酉戌亥', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_HEAVENLY_STEM:\n            return createCounterStyleFromSymbols(value, '甲乙丙丁戊己庚辛壬癸', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_IDEOGRAPHIC:\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹貳參肆伍陸柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹贰叁肆伍陆柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_INFORMAL:\n            return createCJKCounter(value, '〇一二三四五六七八九', '十百千万', JAPANESE_NEGATIVE, cjkSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_FORMAL:\n            return createCJKCounter(value, '零壱弐参四伍六七八九', '拾百千万', JAPANESE_NEGATIVE, cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL:\n            return createCJKCounter(value, '영일이삼사오육칠팔구', '십백천만', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', '十百千萬', KOREAN_NEGATIVE, koreanSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL:\n            return createCJKCounter(value, '零壹貳參四五六七八九', '拾百千', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.DEVANAGARI:\n            return createCounterStyleFromRange(value, 0x966, 0x96f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GEORGIAN:\n            return createAdditiveCounter(value, 1, 19999, GEORGIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GUJARATI:\n            return createCounterStyleFromRange(value, 0xae6, 0xaef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GURMUKHI:\n            return createCounterStyleFromRange(value, 0xa66, 0xa6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HEBREW:\n            return createAdditiveCounter(value, 1, 10999, HEBREW, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA:\n            return createCounterStyleFromSymbols(value, 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん');\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす');\n        case _listStyle.LIST_STYLE_TYPE.KANNADA:\n            return createCounterStyleFromRange(value, 0xce6, 0xcef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA:\n            return createCounterStyleFromSymbols(value, 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LAO:\n            return createCounterStyleFromRange(value, 0xed0, 0xed9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MONGOLIAN:\n            return createCounterStyleFromRange(value, 0x1810, 0x1819, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MYANMAR:\n            return createCounterStyleFromRange(value, 0x1040, 0x1049, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ORIYA:\n            return createCounterStyleFromRange(value, 0xb66, 0xb6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.PERSIAN:\n            return createCounterStyleFromRange(value, 0x6f0, 0x6f9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TAMIL:\n            return createCounterStyleFromRange(value, 0xbe6, 0xbef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TELUGU:\n            return createCounterStyleFromRange(value, 0xc66, 0xc6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.THAI:\n            return createCounterStyleFromRange(value, 0xe50, 0xe59, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TIBETAN:\n            return createCounterStyleFromRange(value, 0xf20, 0xf29, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL:\n        default:\n            return createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('../drawing/Path');\n\nvar _textDecoration = require('../parsing/textDecoration');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar addColorStops = function addColorStops(gradient, canvasGradient) {\n    var maxStop = Math.max.apply(null, gradient.colorStops.map(function (colorStop) {\n        return colorStop.stop;\n    }));\n    var f = 1 / Math.max(1, maxStop);\n    gradient.colorStops.forEach(function (colorStop) {\n        canvasGradient.addColorStop(f * colorStop.stop, colorStop.color.toString());\n    });\n};\n\nvar CanvasRenderer = function () {\n    function CanvasRenderer(canvas) {\n        _classCallCheck(this, CanvasRenderer);\n\n        this.canvas = canvas ? canvas : document.createElement('canvas');\n        this.customCanvas = !!canvas;\n    }\n\n    _createClass(CanvasRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            this.ctx = this.canvas.getContext('2d');\n            this.options = options;\n            if (!this.customCanvas) {\n                this.canvas.width = Math.floor(options.width * options.scale);\n                this.canvas.height = Math.floor(options.height * options.scale);\n                this.canvas.style.width = options.width + 'px';\n                this.canvas.style.height = options.height + 'px';\n            }\n\n            this.ctx.scale(this.options.scale, this.options.scale);\n            this.ctx.translate(-options.x, -options.y);\n            this.ctx.textBaseline = 'bottom';\n            options.logger.log('Canvas renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + this.options.scale);\n        }\n    }, {\n        key: 'clip',\n        value: function clip(clipPaths, callback) {\n            var _this = this;\n\n            if (clipPaths.length) {\n                this.ctx.save();\n                clipPaths.forEach(function (path) {\n                    _this.path(path);\n                    _this.ctx.clip();\n                });\n            }\n\n            callback();\n\n            if (clipPaths.length) {\n                this.ctx.restore();\n            }\n        }\n    }, {\n        key: 'drawImage',\n        value: function drawImage(image, source, destination) {\n            this.ctx.drawImage(image, source.left, source.top, source.width, source.height, destination.left, destination.top, destination.width, destination.height);\n        }\n    }, {\n        key: 'drawShape',\n        value: function drawShape(path, color) {\n            this.path(path);\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'fill',\n        value: function fill(color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'getTarget',\n        value: function getTarget() {\n            return Promise.resolve(this.canvas);\n        }\n    }, {\n        key: 'path',\n        value: function path(_path) {\n            var _this2 = this;\n\n            this.ctx.beginPath();\n            if (Array.isArray(_path)) {\n                _path.forEach(function (point, index) {\n                    var start = point.type === _Path.PATH.VECTOR ? point : point.start;\n                    if (index === 0) {\n                        _this2.ctx.moveTo(start.x, start.y);\n                    } else {\n                        _this2.ctx.lineTo(start.x, start.y);\n                    }\n\n                    if (point.type === _Path.PATH.BEZIER_CURVE) {\n                        _this2.ctx.bezierCurveTo(point.startControl.x, point.startControl.y, point.endControl.x, point.endControl.y, point.end.x, point.end.y);\n                    }\n                });\n            } else {\n                this.ctx.arc(_path.x + _path.radius, _path.y + _path.radius, _path.radius, 0, Math.PI * 2, true);\n            }\n\n            this.ctx.closePath();\n        }\n    }, {\n        key: 'rectangle',\n        value: function rectangle(x, y, width, height, color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fillRect(x, y, width, height);\n        }\n    }, {\n        key: 'renderLinearGradient',\n        value: function renderLinearGradient(bounds, gradient) {\n            var linearGradient = this.ctx.createLinearGradient(bounds.left + gradient.direction.x1, bounds.top + gradient.direction.y1, bounds.left + gradient.direction.x0, bounds.top + gradient.direction.y0);\n\n            addColorStops(gradient, linearGradient);\n            this.ctx.fillStyle = linearGradient;\n            this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n        }\n    }, {\n        key: 'renderRadialGradient',\n        value: function renderRadialGradient(bounds, gradient) {\n            var _this3 = this;\n\n            var x = bounds.left + gradient.center.x;\n            var y = bounds.top + gradient.center.y;\n\n            var radialGradient = this.ctx.createRadialGradient(x, y, 0, x, y, gradient.radius.x);\n            if (!radialGradient) {\n                return;\n            }\n\n            addColorStops(gradient, radialGradient);\n            this.ctx.fillStyle = radialGradient;\n\n            if (gradient.radius.x !== gradient.radius.y) {\n                // transforms for elliptical radial gradient\n                var midX = bounds.left + 0.5 * bounds.width;\n                var midY = bounds.top + 0.5 * bounds.height;\n                var f = gradient.radius.y / gradient.radius.x;\n                var invF = 1 / f;\n\n                this.transform(midX, midY, [1, 0, 0, f, 0, 0], function () {\n                    return _this3.ctx.fillRect(bounds.left, invF * (bounds.top - midY) + midY, bounds.width, bounds.height * invF);\n                });\n            } else {\n                this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n            }\n        }\n    }, {\n        key: 'renderRepeat',\n        value: function renderRepeat(path, image, imageSize, offsetX, offsetY) {\n            this.path(path);\n            this.ctx.fillStyle = this.ctx.createPattern(this.resizeImage(image, imageSize), 'repeat');\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.fill();\n            this.ctx.translate(-offsetX, -offsetY);\n        }\n    }, {\n        key: 'renderTextNode',\n        value: function renderTextNode(textBounds, color, font, textDecoration, textShadows) {\n            var _this4 = this;\n\n            this.ctx.font = [font.fontStyle, font.fontVariant, font.fontWeight, font.fontSize, font.fontFamily].join(' ');\n\n            textBounds.forEach(function (text) {\n                _this4.ctx.fillStyle = color.toString();\n                if (textShadows && text.text.trim().length) {\n                    textShadows.slice(0).reverse().forEach(function (textShadow) {\n                        _this4.ctx.shadowColor = textShadow.color.toString();\n                        _this4.ctx.shadowOffsetX = textShadow.offsetX * _this4.options.scale;\n                        _this4.ctx.shadowOffsetY = textShadow.offsetY * _this4.options.scale;\n                        _this4.ctx.shadowBlur = textShadow.blur;\n\n                        _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                    });\n                } else {\n                    _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                }\n\n                if (textDecoration !== null) {\n                    var textDecorationColor = textDecoration.textDecorationColor || color;\n                    textDecoration.textDecorationLine.forEach(function (textDecorationLine) {\n                        switch (textDecorationLine) {\n                            case _textDecoration.TEXT_DECORATION_LINE.UNDERLINE:\n                                // Draws a line at the baseline of the font\n                                // TODO As some browsers display the line as more than 1px if the font-size is big,\n                                // need to take that into account both in position and size\n                                var _options$fontMetrics$ = _this4.options.fontMetrics.getMetrics(font),\n                                    baseline = _options$fontMetrics$.baseline;\n\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top + baseline), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.OVERLINE:\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.LINE_THROUGH:\n                                // TODO try and find exact position for line-through\n                                var _options$fontMetrics$2 = _this4.options.fontMetrics.getMetrics(font),\n                                    middle = _options$fontMetrics$2.middle;\n\n                                _this4.rectangle(text.bounds.left, Math.ceil(text.bounds.top + middle), text.bounds.width, 1, textDecorationColor);\n                                break;\n                        }\n                    });\n                }\n            });\n        }\n    }, {\n        key: 'resizeImage',\n        value: function resizeImage(image, size) {\n            if (image.width === size.width && image.height === size.height) {\n                return image;\n            }\n\n            var canvas = this.canvas.ownerDocument.createElement('canvas');\n            canvas.width = size.width;\n            canvas.height = size.height;\n            var ctx = canvas.getContext('2d');\n            ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, size.width, size.height);\n            return canvas;\n        }\n    }, {\n        key: 'setOpacity',\n        value: function setOpacity(opacity) {\n            this.ctx.globalAlpha = opacity;\n        }\n    }, {\n        key: 'transform',\n        value: function transform(offsetX, offsetY, matrix, callback) {\n            this.ctx.save();\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n            this.ctx.translate(-offsetX, -offsetY);\n\n            callback();\n\n            this.ctx.restore();\n        }\n    }]);\n\n    return CanvasRenderer;\n}();\n\nexports.default = CanvasRenderer;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Logger = function () {\n    function Logger(enabled, id, start) {\n        _classCallCheck(this, Logger);\n\n        this.enabled = typeof window !== 'undefined' && enabled;\n        this.start = start ? start : Date.now();\n        this.id = id;\n    }\n\n    _createClass(Logger, [{\n        key: 'child',\n        value: function child(id) {\n            return new Logger(this.enabled, id, this.start);\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'log',\n        value: function log() {\n            if (this.enabled && window.console && window.console.log) {\n                for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                    args[_key] = arguments[_key];\n                }\n\n                Function.prototype.bind.call(window.console.log, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'error',\n        value: function error() {\n            if (this.enabled && window.console && window.console.error) {\n                for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                    args[_key2] = arguments[_key2];\n                }\n\n                Function.prototype.bind.call(window.console.error, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n    }]);\n\n    return Logger;\n}();\n\nexports.default = Logger;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parsePadding = exports.PADDING_SIDES = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar PADDING_SIDES = exports.PADDING_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parsePadding = exports.parsePadding = function parsePadding(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('padding-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW_WRAP = exports.OVERFLOW_WRAP = {\n    NORMAL: 0,\n    BREAK_WORD: 1\n};\n\nvar parseOverflowWrap = exports.parseOverflowWrap = function parseOverflowWrap(overflow) {\n    switch (overflow) {\n        case 'break-word':\n            return OVERFLOW_WRAP.BREAK_WORD;\n        case 'normal':\n        default:\n            return OVERFLOW_WRAP.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar POSITION = exports.POSITION = {\n    STATIC: 0,\n    RELATIVE: 1,\n    ABSOLUTE: 2,\n    FIXED: 3,\n    STICKY: 4\n};\n\nvar parsePosition = exports.parsePosition = function parsePosition(position) {\n    switch (position) {\n        case 'relative':\n            return POSITION.RELATIVE;\n        case 'absolute':\n            return POSITION.ABSOLUTE;\n        case 'fixed':\n            return POSITION.FIXED;\n        case 'sticky':\n            return POSITION.STICKY;\n    }\n\n    return POSITION.STATIC;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar TEXT_TRANSFORM = exports.TEXT_TRANSFORM = {\n    NONE: 0,\n    LOWERCASE: 1,\n    UPPERCASE: 2,\n    CAPITALIZE: 3\n};\n\nvar parseTextTransform = exports.parseTextTransform = function parseTextTransform(textTransform) {\n    switch (textTransform) {\n        case 'uppercase':\n            return TEXT_TRANSFORM.UPPERCASE;\n        case 'lowercase':\n            return TEXT_TRANSFORM.LOWERCASE;\n        case 'capitalize':\n            return TEXT_TRANSFORM.CAPITALIZE;\n    }\n\n    return TEXT_TRANSFORM.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.reformatInputBounds = exports.inlineSelectElement = exports.inlineTextAreaElement = exports.inlineInputElement = exports.getInputBorderRadius = exports.INPUT_BACKGROUND = exports.INPUT_BORDERS = exports.INPUT_COLOR = undefined;\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _Circle = require('./drawing/Circle');\n\nvar _Circle2 = _interopRequireDefault(_Circle);\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Bounds = require('./Bounds');\n\nvar _TextBounds = require('./TextBounds');\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar INPUT_COLOR = exports.INPUT_COLOR = new _Color2.default([42, 42, 42]);\nvar INPUT_BORDER_COLOR = new _Color2.default([165, 165, 165]);\nvar INPUT_BACKGROUND_COLOR = new _Color2.default([222, 222, 222]);\nvar INPUT_BORDER = {\n    borderWidth: 1,\n    borderColor: INPUT_BORDER_COLOR,\n    borderStyle: _border.BORDER_STYLE.SOLID\n};\nvar INPUT_BORDERS = exports.INPUT_BORDERS = [INPUT_BORDER, INPUT_BORDER, INPUT_BORDER, INPUT_BORDER];\nvar INPUT_BACKGROUND = exports.INPUT_BACKGROUND = {\n    backgroundColor: INPUT_BACKGROUND_COLOR,\n    backgroundImage: [],\n    backgroundClip: _background.BACKGROUND_CLIP.PADDING_BOX,\n    backgroundOrigin: _background.BACKGROUND_ORIGIN.PADDING_BOX\n};\n\nvar RADIO_BORDER_RADIUS = new _Length2.default('50%');\nvar RADIO_BORDER_RADIUS_TUPLE = [RADIO_BORDER_RADIUS, RADIO_BORDER_RADIUS];\nvar INPUT_RADIO_BORDER_RADIUS = [RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE];\n\nvar CHECKBOX_BORDER_RADIUS = new _Length2.default('3px');\nvar CHECKBOX_BORDER_RADIUS_TUPLE = [CHECKBOX_BORDER_RADIUS, CHECKBOX_BORDER_RADIUS];\nvar INPUT_CHECKBOX_BORDER_RADIUS = [CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE];\n\nvar getInputBorderRadius = exports.getInputBorderRadius = function getInputBorderRadius(node) {\n    return node.type === 'radio' ? INPUT_RADIO_BORDER_RADIUS : INPUT_CHECKBOX_BORDER_RADIUS;\n};\n\nvar inlineInputElement = exports.inlineInputElement = function inlineInputElement(node, container) {\n    if (node.type === 'radio' || node.type === 'checkbox') {\n        if (node.checked) {\n            var size = Math.min(container.bounds.width, container.bounds.height);\n            container.childNodes.push(node.type === 'checkbox' ? [new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79), new _Vector2.default(container.bounds.left + size * 0.16, container.bounds.top + size * 0.5549), new _Vector2.default(container.bounds.left + size * 0.27347, container.bounds.top + size * 0.44071), new _Vector2.default(container.bounds.left + size * 0.39694, container.bounds.top + size * 0.5649), new _Vector2.default(container.bounds.left + size * 0.72983, container.bounds.top + size * 0.23), new _Vector2.default(container.bounds.left + size * 0.84, container.bounds.top + size * 0.34085), new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79)] : new _Circle2.default(container.bounds.left + size / 4, container.bounds.top + size / 4, size / 4));\n        }\n    } else {\n        inlineFormElement(getInputValue(node), node, container, false);\n    }\n};\n\nvar inlineTextAreaElement = exports.inlineTextAreaElement = function inlineTextAreaElement(node, container) {\n    inlineFormElement(node.value, node, container, true);\n};\n\nvar inlineSelectElement = exports.inlineSelectElement = function inlineSelectElement(node, container) {\n    var option = node.options[node.selectedIndex || 0];\n    inlineFormElement(option ? option.text || '' : '', node, container, false);\n};\n\nvar reformatInputBounds = exports.reformatInputBounds = function reformatInputBounds(bounds) {\n    if (bounds.width > bounds.height) {\n        bounds.left += (bounds.width - bounds.height) / 2;\n        bounds.width = bounds.height;\n    } else if (bounds.width < bounds.height) {\n        bounds.top += (bounds.height - bounds.width) / 2;\n        bounds.height = bounds.width;\n    }\n    return bounds;\n};\n\nvar inlineFormElement = function inlineFormElement(value, node, container, allowLinebreak) {\n    var body = node.ownerDocument.body;\n    if (value.length > 0 && body) {\n        var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n        (0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node, null), wrapper);\n        wrapper.style.position = 'absolute';\n        wrapper.style.left = container.bounds.left + 'px';\n        wrapper.style.top = container.bounds.top + 'px';\n        if (!allowLinebreak) {\n            wrapper.style.whiteSpace = 'nowrap';\n        }\n        var text = node.ownerDocument.createTextNode(value);\n        wrapper.appendChild(text);\n        body.appendChild(wrapper);\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    }\n};\n\nvar getInputValue = function getInputValue(node) {\n    var value = node.type === 'password' ? new Array(node.value.length + 1).join('\\u2022') : node.value;\n\n    return value.length === 0 ? node.placeholder || '' : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextBounds = exports.TextBounds = undefined;\n\nvar _Bounds = require('./Bounds');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextBounds = exports.TextBounds = function TextBounds(text, bounds) {\n    _classCallCheck(this, TextBounds);\n\n    this.text = text;\n    this.bounds = bounds;\n};\n\nvar parseTextBounds = exports.parseTextBounds = function parseTextBounds(value, parent, node) {\n    var letterRendering = parent.style.letterSpacing !== 0;\n    var textList = letterRendering ? (0, _Unicode.toCodePoints)(value).map(function (i) {\n        return (0, _Unicode.fromCodePoint)(i);\n    }) : (0, _Unicode.breakWords)(value, parent);\n    var length = textList.length;\n    var defaultView = node.parentNode ? node.parentNode.ownerDocument.defaultView : null;\n    var scrollX = defaultView ? defaultView.pageXOffset : 0;\n    var scrollY = defaultView ? defaultView.pageYOffset : 0;\n    var textBounds = [];\n    var offset = 0;\n    for (var i = 0; i < length; i++) {\n        var text = textList[i];\n        if (parent.style.textDecoration !== _textDecoration.TEXT_DECORATION.NONE || text.trim().length > 0) {\n            if (_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n                textBounds.push(new TextBounds(text, getRangeBounds(node, offset, text.length, scrollX, scrollY)));\n            } else {\n                var replacementNode = node.splitText(text.length);\n                textBounds.push(new TextBounds(text, getWrapperBounds(node, scrollX, scrollY)));\n                node = replacementNode;\n            }\n        } else if (!_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n            node = node.splitText(text.length);\n        }\n        offset += text.length;\n    }\n    return textBounds;\n};\n\nvar getWrapperBounds = function getWrapperBounds(node, scrollX, scrollY) {\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    wrapper.appendChild(node.cloneNode(true));\n    var parentNode = node.parentNode;\n    if (parentNode) {\n        parentNode.replaceChild(wrapper, node);\n        var bounds = (0, _Bounds.parseBounds)(wrapper, scrollX, scrollY);\n        if (wrapper.firstChild) {\n            parentNode.replaceChild(wrapper.firstChild, wrapper);\n        }\n        return bounds;\n    }\n    return new _Bounds.Bounds(0, 0, 0, 0);\n};\n\nvar getRangeBounds = function getRangeBounds(node, offset, length, scrollX, scrollY) {\n    var range = node.ownerDocument.createRange();\n    range.setStart(node, offset);\n    range.setEnd(node, offset + length);\n    return _Bounds.Bounds.fromClientRect(range.getBoundingClientRect(), scrollX, scrollY);\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ForeignObjectRenderer = function () {\n    function ForeignObjectRenderer(element) {\n        _classCallCheck(this, ForeignObjectRenderer);\n\n        this.element = element;\n    }\n\n    _createClass(ForeignObjectRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            var _this = this;\n\n            this.options = options;\n            this.canvas = document.createElement('canvas');\n            this.ctx = this.canvas.getContext('2d');\n            this.canvas.width = Math.floor(options.width) * options.scale;\n            this.canvas.height = Math.floor(options.height) * options.scale;\n            this.canvas.style.width = options.width + 'px';\n            this.canvas.style.height = options.height + 'px';\n\n            options.logger.log('ForeignObject renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + options.scale);\n            var svg = createForeignObjectSVG(Math.max(options.windowWidth, options.width) * options.scale, Math.max(options.windowHeight, options.height) * options.scale, options.scrollX * options.scale, options.scrollY * options.scale, this.element);\n\n            return loadSerializedSVG(svg).then(function (img) {\n                if (options.backgroundColor) {\n                    _this.ctx.fillStyle = options.backgroundColor.toString();\n                    _this.ctx.fillRect(0, 0, options.width * options.scale, options.height * options.scale);\n                }\n\n                _this.ctx.drawImage(img, -options.x * options.scale, -options.y * options.scale);\n                return _this.canvas;\n            });\n        }\n    }]);\n\n    return ForeignObjectRenderer;\n}();\n\nexports.default = ForeignObjectRenderer;\nvar createForeignObjectSVG = exports.createForeignObjectSVG = function createForeignObjectSVG(width, height, x, y, node) {\n    var xmlns = 'http://www.w3.org/2000/svg';\n    var svg = document.createElementNS(xmlns, 'svg');\n    var foreignObject = document.createElementNS(xmlns, 'foreignObject');\n    svg.setAttributeNS(null, 'width', width);\n    svg.setAttributeNS(null, 'height', height);\n\n    foreignObject.setAttributeNS(null, 'width', '100%');\n    foreignObject.setAttributeNS(null, 'height', '100%');\n    foreignObject.setAttributeNS(null, 'x', x);\n    foreignObject.setAttributeNS(null, 'y', y);\n    foreignObject.setAttributeNS(null, 'externalResourcesRequired', 'true');\n    svg.appendChild(foreignObject);\n\n    foreignObject.appendChild(node);\n\n    return svg;\n};\n\nvar loadSerializedSVG = exports.loadSerializedSVG = function loadSerializedSVG(svg) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n\n        img.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(new XMLSerializer().serializeToString(svg));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.breakWords = exports.fromCodePoint = exports.toCodePoints = undefined;\n\nvar _cssLineBreak = require('css-line-break');\n\nObject.defineProperty(exports, 'toCodePoints', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.toCodePoints;\n    }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.fromCodePoint;\n    }\n});\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar breakWords = exports.breakWords = function breakWords(str, parent) {\n    var breaker = (0, _cssLineBreak.LineBreaker)(str, {\n        lineBreak: parent.style.lineBreak,\n        wordBreak: parent.style.overflowWrap === _overflowWrap.OVERFLOW_WRAP.BREAK_WORD ? 'break-word' : parent.style.wordBreak\n    });\n\n    var words = [];\n    var bk = void 0;\n\n    while (!(bk = breaker.next()).done) {\n        words.push(bk.value.slice());\n    }\n\n    return words;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.FontMetrics = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SAMPLE_TEXT = 'Hidden Text';\n\nvar FontMetrics = exports.FontMetrics = function () {\n    function FontMetrics(document) {\n        _classCallCheck(this, FontMetrics);\n\n        this._data = {};\n        this._document = document;\n    }\n\n    _createClass(FontMetrics, [{\n        key: '_parseMetrics',\n        value: function _parseMetrics(font) {\n            var container = this._document.createElement('div');\n            var img = this._document.createElement('img');\n            var span = this._document.createElement('span');\n\n            var body = this._document.body;\n            if (!body) {\n                throw new Error(process.env.NODE_ENV !== 'production' ? 'No document found for font metrics' : '');\n            }\n\n            container.style.visibility = 'hidden';\n            container.style.fontFamily = font.fontFamily;\n            container.style.fontSize = font.fontSize;\n            container.style.margin = '0';\n            container.style.padding = '0';\n\n            body.appendChild(container);\n\n            img.src = _Util.SMALL_IMAGE;\n            img.width = 1;\n            img.height = 1;\n\n            img.style.margin = '0';\n            img.style.padding = '0';\n            img.style.verticalAlign = 'baseline';\n\n            span.style.fontFamily = font.fontFamily;\n            span.style.fontSize = font.fontSize;\n            span.style.margin = '0';\n            span.style.padding = '0';\n\n            span.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n            container.appendChild(span);\n            container.appendChild(img);\n            var baseline = img.offsetTop - span.offsetTop + 2;\n\n            container.removeChild(span);\n            container.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n\n            container.style.lineHeight = 'normal';\n            img.style.verticalAlign = 'super';\n\n            var middle = img.offsetTop - container.offsetTop + 2;\n\n            body.removeChild(container);\n\n            return { baseline: baseline, middle: middle };\n        }\n    }, {\n        key: 'getMetrics',\n        value: function getMetrics(font) {\n            var key = font.fontFamily + ' ' + font.fontSize;\n            if (this._data[key] === undefined) {\n                this._data[key] = this._parseMetrics(font);\n            }\n\n            return this._data[key];\n        }\n    }]);\n\n    return FontMetrics;\n}();", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Proxy = undefined;\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar Proxy = exports.Proxy = function Proxy(src, options) {\n    if (!options.proxy) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'No proxy defined' : null);\n    }\n    var proxy = options.proxy;\n\n    return new Promise(function (resolve, reject) {\n        var responseType = _Feature2.default.SUPPORT_CORS_XHR && _Feature2.default.SUPPORT_RESPONSE_TYPE ? 'blob' : 'text';\n        var xhr = _Feature2.default.SUPPORT_CORS_XHR ? new XMLHttpRequest() : new XDomainRequest();\n        xhr.onload = function () {\n            if (xhr instanceof XMLHttpRequest) {\n                if (xhr.status === 200) {\n                    if (responseType === 'text') {\n                        resolve(xhr.response);\n                    } else {\n                        var reader = new FileReader();\n                        // $FlowFixMe\n                        reader.addEventListener('load', function () {\n                            return resolve(reader.result);\n                        }, false);\n                        // $FlowFixMe\n                        reader.addEventListener('error', function (e) {\n                            return reject(e);\n                        }, false);\n                        reader.readAsDataURL(xhr.response);\n                    }\n                } else {\n                    reject(process.env.NODE_ENV !== 'production' ? 'Failed to proxy resource ' + src.substring(0, 256) + ' with status code ' + xhr.status : '');\n                }\n            } else {\n                resolve(xhr.responseText);\n            }\n        };\n\n        xhr.onerror = reject;\n        xhr.open('GET', proxy + '?url=' + encodeURIComponent(src) + '&responseType=' + responseType);\n\n        if (responseType !== 'text' && xhr instanceof XMLHttpRequest) {\n            xhr.responseType = responseType;\n        }\n\n        if (options.imageTimeout) {\n            var timeout = options.imageTimeout;\n            xhr.timeout = timeout;\n            xhr.ontimeout = function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) proxying ' + src.substring(0, 256) : '');\n            };\n        }\n\n        xhr.send();\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.renderElement = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _NodeParser = require('./NodeParser');\n\nvar _Renderer = require('./Renderer');\n\nvar _Renderer2 = _interopRequireDefault(_Renderer);\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar _ForeignObjectRenderer2 = _interopRequireDefault(_ForeignObjectRenderer);\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Bounds = require('./Bounds');\n\nvar _Clone = require('./Clone');\n\nvar _Font = require('./Font');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar renderElement = exports.renderElement = function renderElement(element, options, logger) {\n    var ownerDocument = element.ownerDocument;\n\n    var windowBounds = new _Bounds.Bounds(options.scrollX, options.scrollY, options.windowWidth, options.windowHeight);\n\n    // http://www.w3.org/TR/css3-background/#special-backgrounds\n    var documentBackgroundColor = ownerDocument.documentElement ? new _Color2.default(getComputedStyle(ownerDocument.documentElement).backgroundColor) : _Color.TRANSPARENT;\n    var bodyBackgroundColor = ownerDocument.body ? new _Color2.default(getComputedStyle(ownerDocument.body).backgroundColor) : _Color.TRANSPARENT;\n\n    var backgroundColor = element === ownerDocument.documentElement ? documentBackgroundColor.isTransparent() ? bodyBackgroundColor.isTransparent() ? options.backgroundColor ? new _Color2.default(options.backgroundColor) : null : bodyBackgroundColor : documentBackgroundColor : options.backgroundColor ? new _Color2.default(options.backgroundColor) : null;\n\n    return (options.foreignObjectRendering ? // $FlowFixMe\n    _Feature2.default.SUPPORT_FOREIGNOBJECT_DRAWING : Promise.resolve(false)).then(function (supportForeignObject) {\n        return supportForeignObject ? function (cloner) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using foreignObject rendering');\n            }\n\n            return cloner.inlineFonts(ownerDocument).then(function () {\n                return cloner.resourceLoader.ready();\n            }).then(function () {\n                var renderer = new _ForeignObjectRenderer2.default(cloner.documentElement);\n                return renderer.render({\n                    backgroundColor: backgroundColor,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height,\n                    windowWidth: options.windowWidth,\n                    windowHeight: options.windowHeight,\n                    scrollX: options.scrollX,\n                    scrollY: options.scrollY\n                });\n            });\n        }(new _Clone.DocumentCloner(element, options, logger, true, renderElement)) : (0, _Clone.cloneWindow)(ownerDocument, windowBounds, element, options, logger, renderElement).then(function (_ref) {\n            var _ref2 = _slicedToArray(_ref, 3),\n                container = _ref2[0],\n                clonedElement = _ref2[1],\n                resourceLoader = _ref2[2];\n\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using computed rendering');\n            }\n\n            var stack = (0, _NodeParser.NodeParser)(clonedElement, resourceLoader, logger);\n            var clonedDocument = clonedElement.ownerDocument;\n\n            if (backgroundColor === stack.container.style.background.backgroundColor) {\n                stack.container.style.background.backgroundColor = _Color.TRANSPARENT;\n            }\n\n            return resourceLoader.ready().then(function (imageStore) {\n                var fontMetrics = new _Font.FontMetrics(clonedDocument);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Starting renderer');\n                }\n\n                var renderOptions = {\n                    backgroundColor: backgroundColor,\n                    fontMetrics: fontMetrics,\n                    imageStore: imageStore,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height\n                };\n\n                if (Array.isArray(options.target)) {\n                    return Promise.all(options.target.map(function (target) {\n                        var renderer = new _Renderer2.default(target, renderOptions);\n                        return renderer.render(stack);\n                    }));\n                } else {\n                    var renderer = new _Renderer2.default(options.target, renderOptions);\n                    var canvas = renderer.render(stack);\n                    if (options.removeContainer === true) {\n                        if (container.parentNode) {\n                            container.parentNode.removeChild(container);\n                        } else if (process.env.NODE_ENV !== 'production') {\n                            logger.log('Cannot detach cloned iframe as it is not in the DOM anymore');\n                        }\n                    }\n\n                    return canvas;\n                }\n            });\n        });\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.NodeParser = undefined;\n\nvar _StackingContext = require('./StackingContext');\n\nvar _StackingContext2 = _interopRequireDefault(_StackingContext);\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NodeParser = exports.NodeParser = function NodeParser(node, resourceLoader, logger) {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Starting node parsing');\n    }\n\n    var index = 0;\n\n    var container = new _NodeContainer2.default(node, null, resourceLoader, index++);\n    var stack = new _StackingContext2.default(container, null, true);\n\n    parseNodeTree(node, container, stack, resourceLoader, index);\n\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished parsing node tree');\n    }\n\n    return stack;\n};\n\nvar IGNORED_NODE_NAMES = ['SCRIPT', 'HEAD', 'TITLE', 'OBJECT', 'BR', 'OPTION'];\n\nvar parseNodeTree = function parseNodeTree(node, parent, stack, resourceLoader, index) {\n    if (process.env.NODE_ENV !== 'production' && index > 50000) {\n        throw new Error('Recursion error while parsing node tree');\n    }\n\n    for (var childNode = node.firstChild, nextNode; childNode; childNode = nextNode) {\n        nextNode = childNode.nextSibling;\n        var defaultView = childNode.ownerDocument.defaultView;\n        if (childNode instanceof defaultView.Text || childNode instanceof Text || defaultView.parent && childNode instanceof defaultView.parent.Text) {\n            if (childNode.data.trim().length > 0) {\n                parent.childNodes.push(_TextContainer2.default.fromTextNode(childNode, parent));\n            }\n        } else if (childNode instanceof defaultView.HTMLElement || childNode instanceof HTMLElement || defaultView.parent && childNode instanceof defaultView.parent.HTMLElement) {\n            if (IGNORED_NODE_NAMES.indexOf(childNode.nodeName) === -1) {\n                var container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n                if (container.isVisible()) {\n                    if (childNode.tagName === 'INPUT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineInputElement)(childNode, container);\n                    } else if (childNode.tagName === 'TEXTAREA') {\n                        // $FlowFixMe\n                        (0, _Input.inlineTextAreaElement)(childNode, container);\n                    } else if (childNode.tagName === 'SELECT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineSelectElement)(childNode, container);\n                    } else if (container.style.listStyle && container.style.listStyle.listStyleType !== _listStyle.LIST_STYLE_TYPE.NONE) {\n                        (0, _ListItem.inlineListItemElement)(childNode, container, resourceLoader);\n                    }\n\n                    var SHOULD_TRAVERSE_CHILDREN = childNode.tagName !== 'TEXTAREA';\n                    var treatAsRealStackingContext = createsRealStackingContext(container, childNode);\n                    if (treatAsRealStackingContext || createsStackingContext(container)) {\n                        // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                        // which actually create a new stacking context should be considered part of the parent stacking context\n                        var parentStack = treatAsRealStackingContext || container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                        var childStack = new _StackingContext2.default(container, parentStack, treatAsRealStackingContext);\n                        parentStack.contexts.push(childStack);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, childStack, resourceLoader, index);\n                        }\n                    } else {\n                        stack.children.push(container);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, stack, resourceLoader, index);\n                        }\n                    }\n                }\n            }\n        } else if (childNode instanceof defaultView.SVGSVGElement || childNode instanceof SVGSVGElement || defaultView.parent && childNode instanceof defaultView.parent.SVGSVGElement) {\n            var _container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n            var _treatAsRealStackingContext = createsRealStackingContext(_container, childNode);\n            if (_treatAsRealStackingContext || createsStackingContext(_container)) {\n                // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                // which actually create a new stacking context should be considered part of the parent stacking context\n                var _parentStack = _treatAsRealStackingContext || _container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                var _childStack = new _StackingContext2.default(_container, _parentStack, _treatAsRealStackingContext);\n                _parentStack.contexts.push(_childStack);\n            } else {\n                stack.children.push(_container);\n            }\n        }\n    }\n};\n\nvar createsRealStackingContext = function createsRealStackingContext(container, node) {\n    return container.isRootElement() || container.isPositionedWithZIndex() || container.style.opacity < 1 || container.isTransformed() || isBodyWithTransparentRoot(container, node);\n};\n\nvar createsStackingContext = function createsStackingContext(container) {\n    return container.isPositioned() || container.isFloating();\n};\n\nvar isBodyWithTransparentRoot = function isBodyWithTransparentRoot(container, node) {\n    return node.nodeName === 'BODY' && container.parent instanceof _NodeContainer2.default && container.parent.style.background.backgroundColor.isTransparent();\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _position = require('./parsing/position');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar StackingContext = function () {\n    function StackingContext(container, parent, treatAsRealStackingContext) {\n        _classCallCheck(this, StackingContext);\n\n        this.container = container;\n        this.parent = parent;\n        this.contexts = [];\n        this.children = [];\n        this.treatAsRealStackingContext = treatAsRealStackingContext;\n    }\n\n    _createClass(StackingContext, [{\n        key: 'getOpacity',\n        value: function getOpacity() {\n            return this.parent ? this.container.style.opacity * this.parent.getOpacity() : this.container.style.opacity;\n        }\n    }, {\n        key: 'getRealParentStackingContext',\n        value: function getRealParentStackingContext() {\n            return !this.parent || this.treatAsRealStackingContext ? this : this.parent.getRealParentStackingContext();\n        }\n    }]);\n\n    return StackingContext;\n}();\n\nexports.default = StackingContext;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Size = function Size(width, height) {\n    _classCallCheck(this, Size);\n\n    this.width = width;\n    this.height = height;\n};\n\nexports.default = Size;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('./Path');\n\nvar _Vector = require('./Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar lerp = function lerp(a, b, t) {\n    return new _Vector2.default(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);\n};\n\nvar BezierCurve = function () {\n    function BezierCurve(start, startControl, endControl, end) {\n        _classCallCheck(this, BezierCurve);\n\n        this.type = _Path.PATH.BEZIER_CURVE;\n        this.start = start;\n        this.startControl = startControl;\n        this.endControl = endControl;\n        this.end = end;\n    }\n\n    _createClass(BezierCurve, [{\n        key: 'subdivide',\n        value: function subdivide(t, firstHalf) {\n            var ab = lerp(this.start, this.startControl, t);\n            var bc = lerp(this.startControl, this.endControl, t);\n            var cd = lerp(this.endControl, this.end, t);\n            var abbc = lerp(ab, bc, t);\n            var bccd = lerp(bc, cd, t);\n            var dest = lerp(abbc, bccd, t);\n            return firstHalf ? new BezierCurve(this.start, ab, abbc, dest) : new BezierCurve(dest, bccd, cd, this.end);\n        }\n    }, {\n        key: 'reverse',\n        value: function reverse() {\n            return new BezierCurve(this.end, this.endControl, this.startControl, this.start);\n        }\n    }]);\n\n    return BezierCurve;\n}();\n\nexports.default = BezierCurve;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorderRadius = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top-left', 'top-right', 'bottom-right', 'bottom-left'];\n\nvar parseBorderRadius = exports.parseBorderRadius = function parseBorderRadius(style) {\n    return SIDES.map(function (side) {\n        var value = style.getPropertyValue('border-' + side + '-radius');\n\n        var _value$split$map = value.split(' ').map(_Length2.default.create),\n            _value$split$map2 = _slicedToArray(_value$split$map, 2),\n            horizontal = _value$split$map2[0],\n            vertical = _value$split$map2[1];\n\n        return typeof vertical === 'undefined' ? [horizontal, horizontal] : [horizontal, vertical];\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar DISPLAY = exports.DISPLAY = {\n    NONE: 1 << 0,\n    BLOCK: 1 << 1,\n    INLINE: 1 << 2,\n    RUN_IN: 1 << 3,\n    FLOW: 1 << 4,\n    FLOW_ROOT: 1 << 5,\n    TABLE: 1 << 6,\n    FLEX: 1 << 7,\n    GRID: 1 << 8,\n    RUBY: 1 << 9,\n    SUBGRID: 1 << 10,\n    LIST_ITEM: 1 << 11,\n    TABLE_ROW_GROUP: 1 << 12,\n    TABLE_HEADER_GROUP: 1 << 13,\n    TABLE_FOOTER_GROUP: 1 << 14,\n    TABLE_ROW: 1 << 15,\n    TABLE_CELL: 1 << 16,\n    TABLE_COLUMN_GROUP: 1 << 17,\n    TABLE_COLUMN: 1 << 18,\n    TABLE_CAPTION: 1 << 19,\n    RUBY_BASE: 1 << 20,\n    RUBY_TEXT: 1 << 21,\n    RUBY_BASE_CONTAINER: 1 << 22,\n    RUBY_TEXT_CONTAINER: 1 << 23,\n    CONTENTS: 1 << 24,\n    INLINE_BLOCK: 1 << 25,\n    INLINE_LIST_ITEM: 1 << 26,\n    INLINE_TABLE: 1 << 27,\n    INLINE_FLEX: 1 << 28,\n    INLINE_GRID: 1 << 29\n};\n\nvar parseDisplayValue = function parseDisplayValue(display) {\n    switch (display) {\n        case 'block':\n            return DISPLAY.BLOCK;\n        case 'inline':\n            return DISPLAY.INLINE;\n        case 'run-in':\n            return DISPLAY.RUN_IN;\n        case 'flow':\n            return DISPLAY.FLOW;\n        case 'flow-root':\n            return DISPLAY.FLOW_ROOT;\n        case 'table':\n            return DISPLAY.TABLE;\n        case 'flex':\n            return DISPLAY.FLEX;\n        case 'grid':\n            return DISPLAY.GRID;\n        case 'ruby':\n            return DISPLAY.RUBY;\n        case 'subgrid':\n            return DISPLAY.SUBGRID;\n        case 'list-item':\n            return DISPLAY.LIST_ITEM;\n        case 'table-row-group':\n            return DISPLAY.TABLE_ROW_GROUP;\n        case 'table-header-group':\n            return DISPLAY.TABLE_HEADER_GROUP;\n        case 'table-footer-group':\n            return DISPLAY.TABLE_FOOTER_GROUP;\n        case 'table-row':\n            return DISPLAY.TABLE_ROW;\n        case 'table-cell':\n            return DISPLAY.TABLE_CELL;\n        case 'table-column-group':\n            return DISPLAY.TABLE_COLUMN_GROUP;\n        case 'table-column':\n            return DISPLAY.TABLE_COLUMN;\n        case 'table-caption':\n            return DISPLAY.TABLE_CAPTION;\n        case 'ruby-base':\n            return DISPLAY.RUBY_BASE;\n        case 'ruby-text':\n            return DISPLAY.RUBY_TEXT;\n        case 'ruby-base-container':\n            return DISPLAY.RUBY_BASE_CONTAINER;\n        case 'ruby-text-container':\n            return DISPLAY.RUBY_TEXT_CONTAINER;\n        case 'contents':\n            return DISPLAY.CONTENTS;\n        case 'inline-block':\n            return DISPLAY.INLINE_BLOCK;\n        case 'inline-list-item':\n            return DISPLAY.INLINE_LIST_ITEM;\n        case 'inline-table':\n            return DISPLAY.INLINE_TABLE;\n        case 'inline-flex':\n            return DISPLAY.INLINE_FLEX;\n        case 'inline-grid':\n            return DISPLAY.INLINE_GRID;\n    }\n\n    return DISPLAY.NONE;\n};\n\nvar setDisplayBit = function setDisplayBit(bit, display) {\n    return bit | parseDisplayValue(display);\n};\n\nvar parseDisplay = exports.parseDisplay = function parseDisplay(display) {\n    return display.split(' ').reduce(setDisplayBit, 0);\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar FLOAT = exports.FLOAT = {\n    NONE: 0,\n    LEFT: 1,\n    RIGHT: 2,\n    INLINE_START: 3,\n    INLINE_END: 4\n};\n\nvar parseCSSFloat = exports.parseCSSFloat = function parseCSSFloat(float) {\n    switch (float) {\n        case 'left':\n            return FLOAT.LEFT;\n        case 'right':\n            return FLOAT.RIGHT;\n        case 'inline-start':\n            return FLOAT.INLINE_START;\n        case 'inline-end':\n            return FLOAT.INLINE_END;\n    }\n    return FLOAT.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\n\nvar parseFontWeight = function parseFontWeight(weight) {\n    switch (weight) {\n        case 'normal':\n            return 400;\n        case 'bold':\n            return 700;\n    }\n\n    var value = parseInt(weight, 10);\n    return isNaN(value) ? 400 : value;\n};\n\nvar parseFont = exports.parseFont = function parseFont(style) {\n    var fontFamily = style.fontFamily;\n    var fontSize = style.fontSize;\n    var fontStyle = style.fontStyle;\n    var fontVariant = style.fontVariant;\n    var fontWeight = parseFontWeight(style.fontWeight);\n\n    return {\n        fontFamily: fontFamily,\n        fontSize: fontSize,\n        fontStyle: fontStyle,\n        fontVariant: fontVariant,\n        fontWeight: fontWeight\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseLetterSpacing = exports.parseLetterSpacing = function parseLetterSpacing(letterSpacing) {\n    if (letterSpacing === 'normal') {\n        return 0;\n    }\n    var value = parseFloat(letterSpacing);\n    return isNaN(value) ? 0 : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar LINE_BREAK = exports.LINE_BREAK = {\n    NORMAL: 'normal',\n    STRICT: 'strict'\n};\n\nvar parseLineBreak = exports.parseLineBreak = function parseLineBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'strict':\n            return LINE_BREAK.STRICT;\n        case 'normal':\n        default:\n            return LINE_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseMargin = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parseMargin = exports.parseMargin = function parseMargin(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('margin-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW = exports.OVERFLOW = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    SCROLL: 2,\n    AUTO: 3\n};\n\nvar parseOverflow = exports.parseOverflow = function parseOverflow(overflow) {\n    switch (overflow) {\n        case 'hidden':\n            return OVERFLOW.HIDDEN;\n        case 'scroll':\n            return OVERFLOW.SCROLL;\n        case 'auto':\n            return OVERFLOW.AUTO;\n        case 'visible':\n        default:\n            return OVERFLOW.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextShadow = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NUMBER = /^([+-]|\\d|\\.)$/i;\n\nvar parseTextShadow = exports.parseTextShadow = function parseTextShadow(textShadow) {\n    if (textShadow === 'none' || typeof textShadow !== 'string') {\n        return null;\n    }\n\n    var currentValue = '';\n    var isLength = false;\n    var values = [];\n    var shadows = [];\n    var numParens = 0;\n    var color = null;\n\n    var appendValue = function appendValue() {\n        if (currentValue.length) {\n            if (isLength) {\n                values.push(parseFloat(currentValue));\n            } else {\n                color = new _Color2.default(currentValue);\n            }\n        }\n        isLength = false;\n        currentValue = '';\n    };\n\n    var appendShadow = function appendShadow() {\n        if (values.length && color !== null) {\n            shadows.push({\n                color: color,\n                offsetX: values[0] || 0,\n                offsetY: values[1] || 0,\n                blur: values[2] || 0\n            });\n        }\n        values.splice(0, values.length);\n        color = null;\n    };\n\n    for (var i = 0; i < textShadow.length; i++) {\n        var c = textShadow[i];\n        switch (c) {\n            case '(':\n                currentValue += c;\n                numParens++;\n                break;\n            case ')':\n                currentValue += c;\n                numParens--;\n                break;\n            case ',':\n                if (numParens === 0) {\n                    appendValue();\n                    appendShadow();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            case ' ':\n                if (numParens === 0) {\n                    appendValue();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            default:\n                if (currentValue.length === 0 && NUMBER.test(c)) {\n                    isLength = true;\n                }\n                currentValue += c;\n        }\n    }\n\n    appendValue();\n    appendShadow();\n\n    if (shadows.length === 0) {\n        return null;\n    }\n\n    return shadows;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTransform = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar toFloat = function toFloat(s) {\n    return parseFloat(s.trim());\n};\n\nvar MATRIX = /(matrix|matrix3d)\\((.+)\\)/;\n\nvar parseTransform = exports.parseTransform = function parseTransform(style) {\n    var transform = parseTransformMatrix(style.transform || style.webkitTransform || style.mozTransform ||\n    // $FlowFixMe\n    style.msTransform ||\n    // $FlowFixMe\n    style.oTransform);\n    if (transform === null) {\n        return null;\n    }\n\n    return {\n        transform: transform,\n        transformOrigin: parseTransformOrigin(style.transformOrigin || style.webkitTransformOrigin || style.mozTransformOrigin ||\n        // $FlowFixMe\n        style.msTransformOrigin ||\n        // $FlowFixMe\n        style.oTransformOrigin)\n    };\n};\n\n// $FlowFixMe\nvar parseTransformOrigin = function parseTransformOrigin(origin) {\n    if (typeof origin !== 'string') {\n        var v = new _Length2.default('0');\n        return [v, v];\n    }\n    var values = origin.split(' ').map(_Length2.default.create);\n    return [values[0], values[1]];\n};\n\n// $FlowFixMe\nvar parseTransformMatrix = function parseTransformMatrix(transform) {\n    if (transform === 'none' || typeof transform !== 'string') {\n        return null;\n    }\n\n    var match = transform.match(MATRIX);\n    if (match) {\n        if (match[1] === 'matrix') {\n            var matrix = match[2].split(',').map(toFloat);\n            return [matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]];\n        } else {\n            var matrix3d = match[2].split(',').map(toFloat);\n            return [matrix3d[0], matrix3d[1], matrix3d[4], matrix3d[5], matrix3d[12], matrix3d[13]];\n        }\n    }\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar VISIBILITY = exports.VISIBILITY = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    COLLAPSE: 2\n};\n\nvar parseVisibility = exports.parseVisibility = function parseVisibility(visibility) {\n    switch (visibility) {\n        case 'hidden':\n            return VISIBILITY.HIDDEN;\n        case 'collapse':\n            return VISIBILITY.COLLAPSE;\n        case 'visible':\n        default:\n            return VISIBILITY.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar WORD_BREAK = exports.WORD_BREAK = {\n    NORMAL: 'normal',\n    BREAK_ALL: 'break-all',\n    KEEP_ALL: 'keep-all'\n};\n\nvar parseWordBreak = exports.parseWordBreak = function parseWordBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'break-all':\n            return WORD_BREAK.BREAK_ALL;\n        case 'keep-all':\n            return WORD_BREAK.KEEP_ALL;\n        case 'normal':\n        default:\n            return WORD_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseZIndex = exports.parseZIndex = function parseZIndex(zIndex) {\n    var auto = zIndex === 'auto';\n    return {\n        auto: auto,\n        order: auto ? 0 : parseInt(zIndex, 10)\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _Util = require('./Util');\n\nObject.defineProperty(exports, 'toCodePoints', {\n  enumerable: true,\n  get: function get() {\n    return _Util.toCodePoints;\n  }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n  enumerable: true,\n  get: function get() {\n    return _Util.fromCodePoint;\n  }\n});\n\nvar _LineBreak = require('./LineBreak');\n\nObject.defineProperty(exports, 'LineBreaker', {\n  enumerable: true,\n  get: function get() {\n    return _LineBreak.LineBreaker;\n  }\n});", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.LineBreaker = exports.inlineBreakOpportunities = exports.lineBreakAtIndex = exports.codePointsToCharacterClasses = exports.UnicodeTrie = exports.BREAK_ALLOWED = exports.BREAK_NOT_ALLOWED = exports.BREAK_MANDATORY = exports.classes = exports.LETTER_NUMBER_MODIFIER = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Trie = require('./Trie');\n\nvar _linebreakTrie = require('./linebreak-trie');\n\nvar _linebreakTrie2 = _interopRequireDefault(_linebreakTrie);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LETTER_NUMBER_MODIFIER = exports.LETTER_NUMBER_MODIFIER = 50;\n\n// Non-tailorable Line Breaking Classes\nvar BK = 1; //  Cause a line break (after)\nvar CR = 2; //  Cause a line break (after), except between CR and LF\nvar LF = 3; //  Cause a line break (after)\nvar CM = 4; //  Prohibit a line break between the character and the preceding character\nvar NL = 5; //  Cause a line break (after)\nvar SG = 6; //  Do not occur in well-formed text\nvar WJ = 7; //  Prohibit line breaks before and after\nvar ZW = 8; //  Provide a break opportunity\nvar GL = 9; //  Prohibit line breaks before and after\nvar SP = 10; // Enable indirect line breaks\nvar ZWJ = 11; // Prohibit line breaks within joiner sequences\n// Break Opportunities\nvar B2 = 12; //  Provide a line break opportunity before and after the character\nvar BA = 13; //  Generally provide a line break opportunity after the character\nvar BB = 14; //  Generally provide a line break opportunity before the character\nvar HY = 15; //  Provide a line break opportunity after the character, except in numeric context\nvar CB = 16; //   Provide a line break opportunity contingent on additional information\n// Characters Prohibiting Certain Breaks\nvar CL = 17; //  Prohibit line breaks before\nvar CP = 18; //  Prohibit line breaks before\nvar EX = 19; //  Prohibit line breaks before\nvar IN = 20; //  Allow only indirect line breaks between pairs\nvar NS = 21; //  Allow only indirect line breaks before\nvar OP = 22; //  Prohibit line breaks after\nvar QU = 23; //  Act like they are both opening and closing\n// Numeric Context\nvar IS = 24; //  Prevent breaks after any and before numeric\nvar NU = 25; //  Form numeric expressions for line breaking purposes\nvar PO = 26; //  Do not break following a numeric expression\nvar PR = 27; //  Do not break in front of a numeric expression\nvar SY = 28; //  Prevent a break before; and allow a break after\n// Other Characters\nvar AI = 29; //  Act like AL when the resolvedEAW is N; otherwise; act as ID\nvar AL = 30; //  Are alphabetic characters or symbols that are used with alphabetic characters\nvar CJ = 31; //  Treat as NS or ID for strict or normal breaking.\nvar EB = 32; //  Do not break from following Emoji Modifier\nvar EM = 33; //  Do not break from preceding Emoji Base\nvar H2 = 34; //  Form Korean syllable blocks\nvar H3 = 35; //  Form Korean syllable blocks\nvar HL = 36; //  Do not break around a following hyphen; otherwise act as Alphabetic\nvar ID = 37; //  Break before or after; except in some numeric context\nvar JL = 38; //  Form Korean syllable blocks\nvar JV = 39; //  Form Korean syllable blocks\nvar JT = 40; //  Form Korean syllable blocks\nvar RI = 41; //  Keep pairs together. For pairs; break before and after other classes\nvar SA = 42; //  Provide a line break opportunity contingent on additional, language-specific context analysis\nvar XX = 43; //  Have as yet unknown line breaking behavior or unassigned code positions\n\nvar classes = exports.classes = {\n    BK: BK,\n    CR: CR,\n    LF: LF,\n    CM: CM,\n    NL: NL,\n    SG: SG,\n    WJ: WJ,\n    ZW: ZW,\n    GL: GL,\n    SP: SP,\n    ZWJ: ZWJ,\n    B2: B2,\n    BA: BA,\n    BB: BB,\n    HY: HY,\n    CB: CB,\n    CL: CL,\n    CP: CP,\n    EX: EX,\n    IN: IN,\n    NS: NS,\n    OP: OP,\n    QU: QU,\n    IS: IS,\n    NU: NU,\n    PO: PO,\n    PR: PR,\n    SY: SY,\n    AI: AI,\n    AL: AL,\n    CJ: CJ,\n    EB: EB,\n    EM: EM,\n    H2: H2,\n    H3: H3,\n    HL: HL,\n    ID: ID,\n    JL: JL,\n    JV: JV,\n    JT: JT,\n    RI: RI,\n    SA: SA,\n    XX: XX\n};\n\nvar BREAK_MANDATORY = exports.BREAK_MANDATORY = '!';\nvar BREAK_NOT_ALLOWED = exports.BREAK_NOT_ALLOWED = '×';\nvar BREAK_ALLOWED = exports.BREAK_ALLOWED = '÷';\nvar UnicodeTrie = exports.UnicodeTrie = (0, _Trie.createTrieFromBase64)(_linebreakTrie2.default);\n\nvar ALPHABETICS = [AL, HL];\nvar HARD_LINE_BREAKS = [BK, CR, LF, NL];\nvar SPACE = [SP, ZW];\nvar PREFIX_POSTFIX = [PR, PO];\nvar LINE_BREAKS = HARD_LINE_BREAKS.concat(SPACE);\nvar KOREAN_SYLLABLE_BLOCK = [JL, JV, JT, H2, H3];\nvar HYPHEN = [HY, BA];\n\nvar codePointsToCharacterClasses = exports.codePointsToCharacterClasses = function codePointsToCharacterClasses(codePoints) {\n    var lineBreak = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'strict';\n\n    var types = [];\n    var indicies = [];\n    var categories = [];\n    codePoints.forEach(function (codePoint, index) {\n        var classType = UnicodeTrie.get(codePoint);\n        if (classType > LETTER_NUMBER_MODIFIER) {\n            categories.push(true);\n            classType -= LETTER_NUMBER_MODIFIER;\n        } else {\n            categories.push(false);\n        }\n\n        if (['normal', 'auto', 'loose'].indexOf(lineBreak) !== -1) {\n            // U+2010, – U+2013, 〜 U+301C, ゠ U+30A0\n            if ([0x2010, 0x2013, 0x301c, 0x30a0].indexOf(codePoint) !== -1) {\n                indicies.push(index);\n                return types.push(CB);\n            }\n        }\n\n        if (classType === CM || classType === ZWJ) {\n            // LB10 Treat any remaining combining mark or ZWJ as AL.\n            if (index === 0) {\n                indicies.push(index);\n                return types.push(AL);\n            }\n\n            // LB9 Do not break a combining character sequence; treat it as if it has the line breaking class of\n            // the base character in all of the following rules. Treat ZWJ as if it were CM.\n            var prev = types[index - 1];\n            if (LINE_BREAKS.indexOf(prev) === -1) {\n                indicies.push(indicies[index - 1]);\n                return types.push(prev);\n            }\n            indicies.push(index);\n            return types.push(AL);\n        }\n\n        indicies.push(index);\n\n        if (classType === CJ) {\n            return types.push(lineBreak === 'strict' ? NS : ID);\n        }\n\n        if (classType === SA) {\n            return types.push(AL);\n        }\n\n        if (classType === AI) {\n            return types.push(AL);\n        }\n\n        // For supplementary characters, a useful default is to treat characters in the range 10000..1FFFD as AL\n        // and characters in the ranges 20000..2FFFD and 30000..3FFFD as ID, until the implementation can be revised\n        // to take into account the actual line breaking properties for these characters.\n        if (classType === XX) {\n            if (codePoint >= 0x20000 && codePoint <= 0x2fffd || codePoint >= 0x30000 && codePoint <= 0x3fffd) {\n                return types.push(ID);\n            } else {\n                return types.push(AL);\n            }\n        }\n\n        types.push(classType);\n    });\n\n    return [indicies, types, categories];\n};\n\nvar isAdjacentWithSpaceIgnored = function isAdjacentWithSpaceIgnored(a, b, currentIndex, classTypes) {\n    var current = classTypes[currentIndex];\n    if (Array.isArray(a) ? a.indexOf(current) !== -1 : a === current) {\n        var i = currentIndex;\n        while (i <= classTypes.length) {\n            i++;\n            var next = classTypes[i];\n\n            if (next === b) {\n                return true;\n            }\n\n            if (next !== SP) {\n                break;\n            }\n        }\n    }\n\n    if (current === SP) {\n        var _i = currentIndex;\n\n        while (_i > 0) {\n            _i--;\n            var prev = classTypes[_i];\n\n            if (Array.isArray(a) ? a.indexOf(prev) !== -1 : a === prev) {\n                var n = currentIndex;\n                while (n <= classTypes.length) {\n                    n++;\n                    var _next = classTypes[n];\n\n                    if (_next === b) {\n                        return true;\n                    }\n\n                    if (_next !== SP) {\n                        break;\n                    }\n                }\n            }\n\n            if (prev !== SP) {\n                break;\n            }\n        }\n    }\n    return false;\n};\n\nvar previousNonSpaceClassType = function previousNonSpaceClassType(currentIndex, classTypes) {\n    var i = currentIndex;\n    while (i >= 0) {\n        var type = classTypes[i];\n        if (type === SP) {\n            i--;\n        } else {\n            return type;\n        }\n    }\n    return 0;\n};\n\nvar _lineBreakAtIndex = function _lineBreakAtIndex(codePoints, classTypes, indicies, index, forbiddenBreaks) {\n    if (indicies[index] === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var currentIndex = index - 1;\n    if (Array.isArray(forbiddenBreaks) && forbiddenBreaks[currentIndex] === true) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var beforeIndex = currentIndex - 1;\n    var afterIndex = currentIndex + 1;\n    var current = classTypes[currentIndex];\n\n    // LB4 Always break after hard line breaks.\n    // LB5 Treat CR followed by LF, as well as CR, LF, and NL as hard line breaks.\n    var before = beforeIndex >= 0 ? classTypes[beforeIndex] : 0;\n    var next = classTypes[afterIndex];\n\n    if (current === CR && next === LF) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    if (HARD_LINE_BREAKS.indexOf(current) !== -1) {\n        return BREAK_MANDATORY;\n    }\n\n    // LB6 Do not break before hard line breaks.\n    if (HARD_LINE_BREAKS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB7 Do not break before spaces or zero width space.\n    if (SPACE.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB8 Break before any character following a zero-width space, even if one or more spaces intervene.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === ZW) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB8a Do not break between a zero width joiner and an ideograph, emoji base or emoji modifier.\n    if (UnicodeTrie.get(codePoints[currentIndex]) === ZWJ && (next === ID || next === EB || next === EM)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB11 Do not break before or after Word joiner and related characters.\n    if (current === WJ || next === WJ) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12 Do not break after NBSP and related characters.\n    if (current === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12a Do not break before NBSP and related characters, except after spaces and hyphens.\n    if ([SP, BA, HY].indexOf(current) === -1 && next === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB13 Do not break before ‘]’ or ‘!’ or ‘;’ or ‘/’, even after spaces.\n    if ([CL, CP, EX, IS, SY].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB14 Do not break after ‘[’, even after spaces.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === OP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB15 Do not break within ‘”[’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(QU, OP, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB16 Do not break between closing punctuation and a nonstarter (lb=NS), even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored([CL, CP], NS, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB17 Do not break within ‘——’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(B2, B2, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB18 Break after spaces.\n    if (current === SP) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB19 Do not break before or after quotation marks, such as ‘ ” ’.\n    if (current === QU || next === QU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB20 Break before and after unresolved CB.\n    if (next === CB || current === CB) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB21 Do not break before hyphen-minus, other hyphens, fixed-width spaces, small kana, and other non-starters, or after acute accents.\n    if ([BA, HY, NS].indexOf(next) !== -1 || current === BB) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21a Don't break after Hebrew + Hyphen.\n    if (before === HL && HYPHEN.indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21b Don’t break between Solidus and Hebrew letters.\n    if (current === SY && next === HL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB22 Do not break between two ellipses, or between letters, numbers or exclamations and ellipsis.\n    if (next === IN && ALPHABETICS.concat(IN, EX, NU, ID, EB, EM).indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23 Do not break between digits and letters.\n    if (ALPHABETICS.indexOf(next) !== -1 && current === NU || ALPHABETICS.indexOf(current) !== -1 && next === NU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23a Do not break between numeric prefixes and ideographs, or between ideographs and numeric postfixes.\n    if (current === PR && [ID, EB, EM].indexOf(next) !== -1 || [ID, EB, EM].indexOf(current) !== -1 && next === PO) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB24 Do not break between numeric prefix/postfix and letters, or between letters and prefix/postfix.\n    if (ALPHABETICS.indexOf(current) !== -1 && PREFIX_POSTFIX.indexOf(next) !== -1 || PREFIX_POSTFIX.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB25 Do not break between the following pairs of classes relevant to numbers:\n    if (\n    // (PR | PO) × ( OP | HY )? NU\n    [PR, PO].indexOf(current) !== -1 && (next === NU || [OP, HY].indexOf(next) !== -1 && classTypes[afterIndex + 1] === NU) ||\n    // ( OP | HY ) × NU\n    [OP, HY].indexOf(current) !== -1 && next === NU ||\n    // NU ×\t(NU | SY | IS)\n    current === NU && [NU, SY, IS].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // NU (NU | SY | IS)* × (NU | SY | IS | CL | CP)\n    if ([NU, SY, IS, CL, CP].indexOf(next) !== -1) {\n        var prevIndex = currentIndex;\n        while (prevIndex >= 0) {\n            var type = classTypes[prevIndex];\n            if (type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(type) !== -1) {\n                prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // NU (NU | SY | IS)* (CL | CP)? × (PO | PR))\n    if ([PR, PO].indexOf(next) !== -1) {\n        var _prevIndex = [CL, CP].indexOf(current) !== -1 ? beforeIndex : currentIndex;\n        while (_prevIndex >= 0) {\n            var _type = classTypes[_prevIndex];\n            if (_type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(_type) !== -1) {\n                _prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // LB26 Do not break a Korean syllable.\n    if (JL === current && [JL, JV, H2, H3].indexOf(next) !== -1 || [JV, H2].indexOf(current) !== -1 && [JV, JT].indexOf(next) !== -1 || [JT, H3].indexOf(current) !== -1 && next === JT) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB27 Treat a Korean Syllable Block the same as ID.\n    if (KOREAN_SYLLABLE_BLOCK.indexOf(current) !== -1 && [IN, PO].indexOf(next) !== -1 || KOREAN_SYLLABLE_BLOCK.indexOf(next) !== -1 && current === PR) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB28 Do not break between alphabetics (“at”).\n    if (ALPHABETICS.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB29 Do not break between numeric punctuation and alphabetics (“e.g.”).\n    if (current === IS && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30 Do not break between letters, numbers, or ordinary symbols and opening or closing parentheses.\n    if (ALPHABETICS.concat(NU).indexOf(current) !== -1 && next === OP || ALPHABETICS.concat(NU).indexOf(next) !== -1 && current === CP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30a Break between two regional indicator symbols if and only if there are an even number of regional\n    // indicators preceding the position of the break.\n    if (current === RI && next === RI) {\n        var i = indicies[currentIndex];\n        var count = 1;\n        while (i > 0) {\n            i--;\n            if (classTypes[i] === RI) {\n                count++;\n            } else {\n                break;\n            }\n        }\n        if (count % 2 !== 0) {\n            return BREAK_NOT_ALLOWED;\n        }\n    }\n\n    // LB30b Do not break between an emoji base and an emoji modifier.\n    if (current === EB && next === EM) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    return BREAK_ALLOWED;\n};\n\nvar lineBreakAtIndex = exports.lineBreakAtIndex = function lineBreakAtIndex(codePoints, index) {\n    // LB2 Never break at the start of text.\n    if (index === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB3 Always break at the end of text.\n    if (index >= codePoints.length) {\n        return BREAK_MANDATORY;\n    }\n\n    var _codePointsToCharacte = codePointsToCharacterClasses(codePoints),\n        _codePointsToCharacte2 = _slicedToArray(_codePointsToCharacte, 2),\n        indicies = _codePointsToCharacte2[0],\n        classTypes = _codePointsToCharacte2[1];\n\n    return _lineBreakAtIndex(codePoints, classTypes, indicies, index);\n};\n\nvar cssFormattedClasses = function cssFormattedClasses(codePoints, options) {\n    if (!options) {\n        options = { lineBreak: 'normal', wordBreak: 'normal' };\n    }\n\n    var _codePointsToCharacte3 = codePointsToCharacterClasses(codePoints, options.lineBreak),\n        _codePointsToCharacte4 = _slicedToArray(_codePointsToCharacte3, 3),\n        indicies = _codePointsToCharacte4[0],\n        classTypes = _codePointsToCharacte4[1],\n        isLetterNumber = _codePointsToCharacte4[2];\n\n    if (options.wordBreak === 'break-all' || options.wordBreak === 'break-word') {\n        classTypes = classTypes.map(function (type) {\n            return [NU, AL, SA].indexOf(type) !== -1 ? ID : type;\n        });\n    }\n\n    var forbiddenBreakpoints = options.wordBreak === 'keep-all' ? isLetterNumber.map(function (isLetterNumber, i) {\n        return isLetterNumber && codePoints[i] >= 0x4e00 && codePoints[i] <= 0x9fff;\n    }) : null;\n\n    return [indicies, classTypes, forbiddenBreakpoints];\n};\n\nvar inlineBreakOpportunities = exports.inlineBreakOpportunities = function inlineBreakOpportunities(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n    var output = BREAK_NOT_ALLOWED;\n\n    var _cssFormattedClasses = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses2 = _slicedToArray(_cssFormattedClasses, 3),\n        indicies = _cssFormattedClasses2[0],\n        classTypes = _cssFormattedClasses2[1],\n        forbiddenBreakpoints = _cssFormattedClasses2[2];\n\n    codePoints.forEach(function (codePoint, i) {\n        output += (0, _Util.fromCodePoint)(codePoint) + (i >= codePoints.length - 1 ? BREAK_MANDATORY : _lineBreakAtIndex(codePoints, classTypes, indicies, i + 1, forbiddenBreakpoints));\n    });\n\n    return output;\n};\n\nvar Break = function () {\n    function Break(codePoints, lineBreak, start, end) {\n        _classCallCheck(this, Break);\n\n        this._codePoints = codePoints;\n        this.required = lineBreak === BREAK_MANDATORY;\n        this.start = start;\n        this.end = end;\n    }\n\n    _createClass(Break, [{\n        key: 'slice',\n        value: function slice() {\n            return _Util.fromCodePoint.apply(undefined, _toConsumableArray(this._codePoints.slice(this.start, this.end)));\n        }\n    }]);\n\n    return Break;\n}();\n\nvar LineBreaker = exports.LineBreaker = function LineBreaker(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n\n    var _cssFormattedClasses3 = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses4 = _slicedToArray(_cssFormattedClasses3, 3),\n        indicies = _cssFormattedClasses4[0],\n        classTypes = _cssFormattedClasses4[1],\n        forbiddenBreakpoints = _cssFormattedClasses4[2];\n\n    var length = codePoints.length;\n    var lastEnd = 0;\n    var nextIndex = 0;\n\n    return {\n        next: function next() {\n            if (nextIndex >= length) {\n                return { done: true };\n            }\n            var lineBreak = BREAK_NOT_ALLOWED;\n            while (nextIndex < length && (lineBreak = _lineBreakAtIndex(codePoints, classTypes, indicies, ++nextIndex, forbiddenBreakpoints)) === BREAK_NOT_ALLOWED) {}\n\n            if (lineBreak !== BREAK_NOT_ALLOWED || nextIndex === length) {\n                var value = new Break(codePoints, lineBreak, lastEnd, nextIndex);\n                lastEnd = nextIndex;\n                return { value: value, done: false };\n            }\n\n            return { done: true };\n        }\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Trie = exports.createTrieFromBase64 = exports.UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_DATA_MASK = exports.UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_SHIFT_1_2 = exports.UTRIE2_INDEX_SHIFT = exports.UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_2 = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/** Shift size for getting the index-2 table offset. */\nvar UTRIE2_SHIFT_2 = exports.UTRIE2_SHIFT_2 = 5;\n\n/** Shift size for getting the index-1 table offset. */\nvar UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_1 = 6 + 5;\n\n/**\n * Shift size for shifting left the index array values.\n * Increases possible data size with 16-bit index values at the cost\n * of compactability.\n * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n */\nvar UTRIE2_INDEX_SHIFT = exports.UTRIE2_INDEX_SHIFT = 2;\n\n/**\n * Difference between the two shift sizes,\n * for getting an index-1 offset from an index-2 offset. 6=11-5\n */\nvar UTRIE2_SHIFT_1_2 = exports.UTRIE2_SHIFT_1_2 = UTRIE2_SHIFT_1 - UTRIE2_SHIFT_2;\n\n/**\n * The part of the index-2 table for U+D800..U+DBFF stores values for\n * lead surrogate code _units_ not code _points_.\n * Values for lead surrogate code _points_ are indexed with this portion of the table.\n * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n */\nvar UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_LSCP_INDEX_2_OFFSET = 0x10000 >> UTRIE2_SHIFT_2;\n\n/** Number of entries in a data block. 32=0x20 */\nvar UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_DATA_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_2;\n/** Mask for getting the lower bits for the in-data-block offset. */\nvar UTRIE2_DATA_MASK = exports.UTRIE2_DATA_MASK = UTRIE2_DATA_BLOCK_LENGTH - 1;\n\nvar UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = 0x400 >> UTRIE2_SHIFT_2;\n/** Count the lengths of both BMP pieces. 2080=0x820 */\nvar UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_INDEX_2_BMP_LENGTH = UTRIE2_LSCP_INDEX_2_OFFSET + UTRIE2_LSCP_INDEX_2_LENGTH;\n/**\n * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n */\nvar UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\nvar UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n/**\n * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n * Variable length, for code points up to highStart, where the last single-value range starts.\n * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n *\n * The part of the index-2 table for supplementary code points starts\n * after this index-1 table.\n *\n * Both the index-1 table and the following part of the index-2 table\n * are omitted completely if there is only BMP data.\n */\nvar UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_INDEX_1_OFFSET = UTRIE2_UTF8_2B_INDEX_2_OFFSET + UTRIE2_UTF8_2B_INDEX_2_LENGTH;\n\n/**\n * Number of index-1 entries for the BMP. 32=0x20\n * This part of the index-1 table is omitted from the serialized form.\n */\nvar UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = 0x10000 >> UTRIE2_SHIFT_1;\n\n/** Number of entries in an index-2 block. 64=0x40 */\nvar UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_1_2;\n/** Mask for getting the lower bits for the in-index-2-block offset. */\nvar UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_MASK = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n\nvar createTrieFromBase64 = exports.createTrieFromBase64 = function createTrieFromBase64(base64) {\n    var buffer = (0, _Util.decode)(base64);\n    var view32 = Array.isArray(buffer) ? (0, _Util.polyUint32Array)(buffer) : new Uint32Array(buffer);\n    var view16 = Array.isArray(buffer) ? (0, _Util.polyUint16Array)(buffer) : new Uint16Array(buffer);\n    var headerLength = 24;\n\n    var index = view16.slice(headerLength / 2, view32[4] / 2);\n    var data = view32[5] === 2 ? view16.slice((headerLength + view32[4]) / 2) : view32.slice(Math.ceil((headerLength + view32[4]) / 4));\n\n    return new Trie(view32[0], view32[1], view32[2], view32[3], index, data);\n};\n\nvar Trie = exports.Trie = function () {\n    function Trie(initialValue, errorValue, highStart, highValueIndex, index, data) {\n        _classCallCheck(this, Trie);\n\n        this.initialValue = initialValue;\n        this.errorValue = errorValue;\n        this.highStart = highStart;\n        this.highValueIndex = highValueIndex;\n        this.index = index;\n        this.data = data;\n    }\n\n    /**\n     * Get the value for a code point as stored in the Trie.\n     *\n     * @param codePoint the code point\n     * @return the value\n     */\n\n\n    _createClass(Trie, [{\n        key: 'get',\n        value: function get(codePoint) {\n            var ix = void 0;\n            if (codePoint >= 0) {\n                if (codePoint < 0x0d800 || codePoint > 0x0dbff && codePoint <= 0x0ffff) {\n                    // Ordinary BMP code point, excluding leading surrogates.\n                    // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                    // 16 bit data is stored in the index array itself.\n                    ix = this.index[codePoint >> UTRIE2_SHIFT_2];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint <= 0xffff) {\n                    // Lead Surrogate Code Point.  A Separate index section is stored for\n                    // lead surrogate code units and code points.\n                    //   The main index has the code unit data.\n                    //   For this function, we need the code point data.\n                    // Note: this expression could be refactored for slightly improved efficiency, but\n                    //       surrogate code points will be so rare in practice that it's not worth it.\n                    ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET + (codePoint - 0xd800 >> UTRIE2_SHIFT_2)];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint < this.highStart) {\n                    // Supplemental code point, use two-level lookup.\n                    ix = UTRIE2_INDEX_1_OFFSET - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH + (codePoint >> UTRIE2_SHIFT_1);\n                    ix = this.index[ix];\n                    ix += codePoint >> UTRIE2_SHIFT_2 & UTRIE2_INDEX_2_MASK;\n                    ix = this.index[ix];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0x10ffff) {\n                    return this.data[this.highValueIndex];\n                }\n            }\n\n            // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n            return this.errorValue;\n        }\n    }]);\n\n    return Trie;\n}();", "'use strict';\n\nmodule.exports = '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';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Circle = function Circle(x, y, radius) {\n    _classCallCheck(this, Circle);\n\n    this.type = _Path.PATH.CIRCLE;\n    this.x = x;\n    this.y = y;\n    this.radius = radius;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Circle');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Circle');\n        }\n        if (isNaN(radius)) {\n            console.error('Invalid radius value given for Circle');\n        }\n    }\n};\n\nexports.default = Circle;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Font = require('./Font');\n\nvar _Gradient = require('./Gradient');\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Renderer = function () {\n    function Renderer(target, options) {\n        _classCallCheck(this, Renderer);\n\n        this.target = target;\n        this.options = options;\n        target.render(options);\n    }\n\n    _createClass(Renderer, [{\n        key: 'renderNode',\n        value: function renderNode(container) {\n            if (container.isVisible()) {\n                this.renderNodeBackgroundAndBorders(container);\n                this.renderNodeContent(container);\n            }\n        }\n    }, {\n        key: 'renderNodeContent',\n        value: function renderNodeContent(container) {\n            var _this = this;\n\n            var callback = function callback() {\n                if (container.childNodes.length) {\n                    container.childNodes.forEach(function (child) {\n                        if (child instanceof _TextContainer2.default) {\n                            var style = child.parent.style;\n                            _this.target.renderTextNode(child.bounds, style.color, style.font, style.textDecoration, style.textShadow);\n                        } else {\n                            _this.target.drawShape(child, container.style.color);\n                        }\n                    });\n                }\n\n                if (container.image) {\n                    var _image = _this.options.imageStore.get(container.image);\n                    if (_image) {\n                        var contentBox = (0, _Bounds.calculateContentBox)(container.bounds, container.style.padding, container.style.border);\n                        var _width = typeof _image.width === 'number' && _image.width > 0 ? _image.width : contentBox.width;\n                        var _height = typeof _image.height === 'number' && _image.height > 0 ? _image.height : contentBox.height;\n                        if (_width > 0 && _height > 0) {\n                            _this.target.clip([(0, _Bounds.calculatePaddingBoxPath)(container.curvedBounds)], function () {\n                                _this.target.drawImage(_image, new _Bounds.Bounds(0, 0, _width, _height), contentBox);\n                            });\n                        }\n                    }\n                }\n            };\n            var paths = container.getClipPaths();\n            if (paths.length) {\n                this.target.clip(paths, callback);\n            } else {\n                callback();\n            }\n        }\n    }, {\n        key: 'renderNodeBackgroundAndBorders',\n        value: function renderNodeBackgroundAndBorders(container) {\n            var _this2 = this;\n\n            var HAS_BACKGROUND = !container.style.background.backgroundColor.isTransparent() || container.style.background.backgroundImage.length;\n\n            var hasRenderableBorders = container.style.border.some(function (border) {\n                return border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent();\n            });\n\n            var callback = function callback() {\n                var backgroundPaintingArea = (0, _background.calculateBackgroungPaintingArea)(container.curvedBounds, container.style.background.backgroundClip);\n\n                if (HAS_BACKGROUND) {\n                    _this2.target.clip([backgroundPaintingArea], function () {\n                        if (!container.style.background.backgroundColor.isTransparent()) {\n                            _this2.target.fill(container.style.background.backgroundColor);\n                        }\n\n                        _this2.renderBackgroundImage(container);\n                    });\n                }\n\n                container.style.border.forEach(function (border, side) {\n                    if (border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent()) {\n                        _this2.renderBorder(border, side, container.curvedBounds);\n                    }\n                });\n            };\n\n            if (HAS_BACKGROUND || hasRenderableBorders) {\n                var paths = container.parent ? container.parent.getClipPaths() : [];\n                if (paths.length) {\n                    this.target.clip(paths, callback);\n                } else {\n                    callback();\n                }\n            }\n        }\n    }, {\n        key: 'renderBackgroundImage',\n        value: function renderBackgroundImage(container) {\n            var _this3 = this;\n\n            container.style.background.backgroundImage.slice(0).reverse().forEach(function (backgroundImage) {\n                if (backgroundImage.source.method === 'url' && backgroundImage.source.args.length) {\n                    _this3.renderBackgroundRepeat(container, backgroundImage);\n                } else if (/gradient/i.test(backgroundImage.source.method)) {\n                    _this3.renderBackgroundGradient(container, backgroundImage);\n                }\n            });\n        }\n    }, {\n        key: 'renderBackgroundRepeat',\n        value: function renderBackgroundRepeat(container, background) {\n            var image = this.options.imageStore.get(background.source.args[0]);\n            if (image) {\n                var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n                var backgroundImageSize = (0, _background.calculateBackgroundSize)(background, image, backgroundPositioningArea);\n                var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n                var _path = (0, _background.calculateBackgroundRepeatPath)(background, position, backgroundImageSize, backgroundPositioningArea, container.bounds);\n\n                var _offsetX = Math.round(backgroundPositioningArea.left + position.x);\n                var _offsetY = Math.round(backgroundPositioningArea.top + position.y);\n                this.target.renderRepeat(_path, image, backgroundImageSize, _offsetX, _offsetY);\n            }\n        }\n    }, {\n        key: 'renderBackgroundGradient',\n        value: function renderBackgroundGradient(container, background) {\n            var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n            var backgroundImageSize = (0, _background.calculateGradientBackgroundSize)(background, backgroundPositioningArea);\n            var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n            var gradientBounds = new _Bounds.Bounds(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y), backgroundImageSize.width, backgroundImageSize.height);\n\n            var gradient = (0, _Gradient.parseGradient)(container, background.source, gradientBounds);\n            if (gradient) {\n                switch (gradient.type) {\n                    case _Gradient.GRADIENT_TYPE.LINEAR_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderLinearGradient(gradientBounds, gradient);\n                        break;\n                    case _Gradient.GRADIENT_TYPE.RADIAL_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderRadialGradient(gradientBounds, gradient);\n                        break;\n                }\n            }\n        }\n    }, {\n        key: 'renderBorder',\n        value: function renderBorder(border, side, curvePoints) {\n            this.target.drawShape((0, _Bounds.parsePathForBorder)(curvePoints, side), border.borderColor);\n        }\n    }, {\n        key: 'renderStack',\n        value: function renderStack(stack) {\n            var _this4 = this;\n\n            if (stack.container.isVisible()) {\n                var _opacity = stack.getOpacity();\n                if (_opacity !== this._opacity) {\n                    this.target.setOpacity(stack.getOpacity());\n                    this._opacity = _opacity;\n                }\n\n                var _transform = stack.container.style.transform;\n                if (_transform !== null) {\n                    this.target.transform(stack.container.bounds.left + _transform.transformOrigin[0].value, stack.container.bounds.top + _transform.transformOrigin[1].value, _transform.transform, function () {\n                        return _this4.renderStackContent(stack);\n                    });\n                } else {\n                    this.renderStackContent(stack);\n                }\n            }\n        }\n    }, {\n        key: 'renderStackContent',\n        value: function renderStackContent(stack) {\n            var _splitStackingContext = splitStackingContexts(stack),\n                _splitStackingContext2 = _slicedToArray(_splitStackingContext, 5),\n                negativeZIndex = _splitStackingContext2[0],\n                zeroOrAutoZIndexOrTransformedOrOpacity = _splitStackingContext2[1],\n                positiveZIndex = _splitStackingContext2[2],\n                nonPositionedFloats = _splitStackingContext2[3],\n                nonPositionedInlineLevel = _splitStackingContext2[4];\n\n            var _splitDescendants = splitDescendants(stack),\n                _splitDescendants2 = _slicedToArray(_splitDescendants, 2),\n                inlineLevel = _splitDescendants2[0],\n                nonInlineLevel = _splitDescendants2[1];\n\n            // https://www.w3.org/TR/css-position-3/#painting-order\n            // 1. the background and borders of the element forming the stacking context.\n\n\n            this.renderNodeBackgroundAndBorders(stack.container);\n            // 2. the child stacking contexts with negative stack levels (most negative first).\n            negativeZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n            // 3. For all its in-flow, non-positioned, block-level descendants in tree order:\n            this.renderNodeContent(stack.container);\n            nonInlineLevel.forEach(this.renderNode, this);\n            // 4. All non-positioned floating descendants, in tree order. For each one of these,\n            // treat the element as if it created a new stacking context, but any positioned descendants and descendants\n            // which actually create a new stacking context should be considered part of the parent stacking context,\n            // not this new one.\n            nonPositionedFloats.forEach(this.renderStack, this);\n            // 5. the in-flow, inline-level, non-positioned descendants, including inline tables and inline blocks.\n            nonPositionedInlineLevel.forEach(this.renderStack, this);\n            inlineLevel.forEach(this.renderNode, this);\n            // 6. All positioned, opacity or transform descendants, in tree order that fall into the following categories:\n            //  All positioned descendants with 'z-index: auto' or 'z-index: 0', in tree order.\n            //  For those with 'z-index: auto', treat the element as if it created a new stacking context,\n            //  but any positioned descendants and descendants which actually create a new stacking context should be\n            //  considered part of the parent stacking context, not this new one. For those with 'z-index: 0',\n            //  treat the stacking context generated atomically.\n            //\n            //  All opacity descendants with opacity less than 1\n            //\n            //  All transform descendants with transform other than none\n            zeroOrAutoZIndexOrTransformedOrOpacity.forEach(this.renderStack, this);\n            // 7. Stacking contexts formed by positioned descendants with z-indices greater than or equal to 1 in z-index\n            // order (smallest first) then tree order.\n            positiveZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n        }\n    }, {\n        key: 'render',\n        value: function render(stack) {\n            var _this5 = this;\n\n            if (this.options.backgroundColor) {\n                this.target.rectangle(this.options.x, this.options.y, this.options.width, this.options.height, this.options.backgroundColor);\n            }\n            this.renderStack(stack);\n            var target = this.target.getTarget();\n            if (process.env.NODE_ENV !== 'production') {\n                return target.then(function (output) {\n                    _this5.options.logger.log('Render completed');\n                    return output;\n                });\n            }\n            return target;\n        }\n    }]);\n\n    return Renderer;\n}();\n\nexports.default = Renderer;\n\n\nvar splitDescendants = function splitDescendants(stack) {\n    var inlineLevel = [];\n    var nonInlineLevel = [];\n\n    var length = stack.children.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.children[i];\n        if (child.isInlineLevel()) {\n            inlineLevel.push(child);\n        } else {\n            nonInlineLevel.push(child);\n        }\n    }\n    return [inlineLevel, nonInlineLevel];\n};\n\nvar splitStackingContexts = function splitStackingContexts(stack) {\n    var negativeZIndex = [];\n    var zeroOrAutoZIndexOrTransformedOrOpacity = [];\n    var positiveZIndex = [];\n    var nonPositionedFloats = [];\n    var nonPositionedInlineLevel = [];\n    var length = stack.contexts.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.contexts[i];\n        if (child.container.isPositioned() || child.container.style.opacity < 1 || child.container.isTransformed()) {\n            if (child.container.style.zIndex.order < 0) {\n                negativeZIndex.push(child);\n            } else if (child.container.style.zIndex.order > 0) {\n                positiveZIndex.push(child);\n            } else {\n                zeroOrAutoZIndexOrTransformedOrOpacity.push(child);\n            }\n        } else {\n            if (child.container.isFloating()) {\n                nonPositionedFloats.push(child);\n            } else {\n                nonPositionedInlineLevel.push(child);\n            }\n        }\n    }\n    return [negativeZIndex, zeroOrAutoZIndexOrTransformedOrOpacity, positiveZIndex, nonPositionedFloats, nonPositionedInlineLevel];\n};\n\nvar sortByZIndex = function sortByZIndex(a, b) {\n    if (a.container.style.zIndex.order > b.container.style.zIndex.order) {\n        return 1;\n    } else if (a.container.style.zIndex.order < b.container.style.zIndex.order) {\n        return -1;\n    }\n\n    return a.container.index > b.container.index ? 1 : -1;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.transformWebkitRadialGradientArgs = exports.parseGradient = exports.RadialGradient = exports.LinearGradient = exports.RADIAL_GRADIENT_SHAPE = exports.GRADIENT_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _Angle = require('./Angle');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SIDE_OR_CORNER = /^(to )?(left|top|right|bottom)( (left|top|right|bottom))?$/i;\nvar PERCENTAGE_ANGLES = /^([+-]?\\d*\\.?\\d+)% ([+-]?\\d*\\.?\\d+)%$/i;\nvar ENDS_WITH_LENGTH = /(px)|%|( 0)$/i;\nvar FROM_TO_COLORSTOP = /^(from|to|color-stop)\\((?:([\\d.]+)(%)?,\\s*)?(.+?)\\)$/i;\nvar RADIAL_SHAPE_DEFINITION = /^\\s*(circle|ellipse)?\\s*((?:([\\d.]+)(px|r?em|%)\\s*(?:([\\d.]+)(px|r?em|%))?)|closest-side|closest-corner|farthest-side|farthest-corner)?\\s*(?:at\\s*(?:(left|center|right)|([\\d.]+)(px|r?em|%))\\s+(?:(top|center|bottom)|([\\d.]+)(px|r?em|%)))?(?:\\s|$)/i;\n\nvar GRADIENT_TYPE = exports.GRADIENT_TYPE = {\n    LINEAR_GRADIENT: 0,\n    RADIAL_GRADIENT: 1\n};\n\nvar RADIAL_GRADIENT_SHAPE = exports.RADIAL_GRADIENT_SHAPE = {\n    CIRCLE: 0,\n    ELLIPSE: 1\n};\n\nvar LENGTH_FOR_POSITION = {\n    left: new _Length2.default('0%'),\n    top: new _Length2.default('0%'),\n    center: new _Length2.default('50%'),\n    right: new _Length2.default('100%'),\n    bottom: new _Length2.default('100%')\n};\n\nvar LinearGradient = exports.LinearGradient = function LinearGradient(colorStops, direction) {\n    _classCallCheck(this, LinearGradient);\n\n    this.type = GRADIENT_TYPE.LINEAR_GRADIENT;\n    this.colorStops = colorStops;\n    this.direction = direction;\n};\n\nvar RadialGradient = exports.RadialGradient = function RadialGradient(colorStops, shape, center, radius) {\n    _classCallCheck(this, RadialGradient);\n\n    this.type = GRADIENT_TYPE.RADIAL_GRADIENT;\n    this.colorStops = colorStops;\n    this.shape = shape;\n    this.center = center;\n    this.radius = radius;\n};\n\nvar parseGradient = exports.parseGradient = function parseGradient(container, _ref, bounds) {\n    var args = _ref.args,\n        method = _ref.method,\n        prefix = _ref.prefix;\n\n    if (method === 'linear-gradient') {\n        return parseLinearGradient(args, bounds, !!prefix);\n    } else if (method === 'gradient' && args[0] === 'linear') {\n        // TODO handle correct angle\n        return parseLinearGradient(['to bottom'].concat(transformObsoleteColorStops(args.slice(3))), bounds, !!prefix);\n    } else if (method === 'radial-gradient') {\n        return parseRadialGradient(container, prefix === '-webkit-' ? transformWebkitRadialGradientArgs(args) : args, bounds);\n    } else if (method === 'gradient' && args[0] === 'radial') {\n        return parseRadialGradient(container, transformObsoleteColorStops(transformWebkitRadialGradientArgs(args.slice(1))), bounds);\n    }\n};\n\nvar parseColorStops = function parseColorStops(args, firstColorStopIndex, lineLength) {\n    var colorStops = [];\n\n    for (var i = firstColorStopIndex; i < args.length; i++) {\n        var value = args[i];\n        var HAS_LENGTH = ENDS_WITH_LENGTH.test(value);\n        var lastSpaceIndex = value.lastIndexOf(' ');\n        var _color = new _Color2.default(HAS_LENGTH ? value.substring(0, lastSpaceIndex) : value);\n        var _stop = HAS_LENGTH ? new _Length2.default(value.substring(lastSpaceIndex + 1)) : i === firstColorStopIndex ? new _Length2.default('0%') : i === args.length - 1 ? new _Length2.default('100%') : null;\n        colorStops.push({ color: _color, stop: _stop });\n    }\n\n    var absoluteValuedColorStops = colorStops.map(function (_ref2) {\n        var color = _ref2.color,\n            stop = _ref2.stop;\n\n        var absoluteStop = lineLength === 0 ? 0 : stop ? stop.getAbsoluteValue(lineLength) / lineLength : null;\n\n        return {\n            color: color,\n            // $FlowFixMe\n            stop: absoluteStop\n        };\n    });\n\n    var previousColorStop = absoluteValuedColorStops[0].stop;\n    for (var _i = 0; _i < absoluteValuedColorStops.length; _i++) {\n        if (previousColorStop !== null) {\n            var _stop2 = absoluteValuedColorStops[_i].stop;\n            if (_stop2 === null) {\n                var n = _i;\n                while (absoluteValuedColorStops[n].stop === null) {\n                    n++;\n                }\n                var steps = n - _i + 1;\n                var nextColorStep = absoluteValuedColorStops[n].stop;\n                var stepSize = (nextColorStep - previousColorStop) / steps;\n                for (; _i < n; _i++) {\n                    previousColorStop = absoluteValuedColorStops[_i].stop = previousColorStop + stepSize;\n                }\n            } else {\n                previousColorStop = _stop2;\n            }\n        }\n    }\n\n    return absoluteValuedColorStops;\n};\n\nvar parseLinearGradient = function parseLinearGradient(args, bounds, hasPrefix) {\n    var angle = (0, _Angle.parseAngle)(args[0]);\n    var HAS_SIDE_OR_CORNER = SIDE_OR_CORNER.test(args[0]);\n    var HAS_DIRECTION = HAS_SIDE_OR_CORNER || angle !== null || PERCENTAGE_ANGLES.test(args[0]);\n    var direction = HAS_DIRECTION ? angle !== null ? calculateGradientDirection(\n    // if there is a prefix, the 0° angle points due East (instead of North per W3C)\n    hasPrefix ? angle - Math.PI * 0.5 : angle, bounds) : HAS_SIDE_OR_CORNER ? parseSideOrCorner(args[0], bounds) : parsePercentageAngle(args[0], bounds) : calculateGradientDirection(Math.PI, bounds);\n    var firstColorStopIndex = HAS_DIRECTION ? 1 : 0;\n\n    // TODO: Fix some inaccuracy with color stops with px values\n    var lineLength = Math.min((0, _Util.distance)(Math.abs(direction.x0) + Math.abs(direction.x1), Math.abs(direction.y0) + Math.abs(direction.y1)), bounds.width * 2, bounds.height * 2);\n\n    return new LinearGradient(parseColorStops(args, firstColorStopIndex, lineLength), direction);\n};\n\nvar parseRadialGradient = function parseRadialGradient(container, args, bounds) {\n    var m = args[0].match(RADIAL_SHAPE_DEFINITION);\n    var shape = m && (m[1] === 'circle' || // explicit shape specification\n    m[3] !== undefined && m[5] === undefined) // only one radius coordinate\n    ? RADIAL_GRADIENT_SHAPE.CIRCLE : RADIAL_GRADIENT_SHAPE.ELLIPSE;\n    var radius = {};\n    var center = {};\n\n    if (m) {\n        // Radius\n        if (m[3] !== undefined) {\n            radius.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[3], m[4]).getAbsoluteValue(bounds.width);\n        }\n\n        if (m[5] !== undefined) {\n            radius.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[5], m[6]).getAbsoluteValue(bounds.height);\n        }\n\n        // Position\n        if (m[7]) {\n            center.x = LENGTH_FOR_POSITION[m[7].toLowerCase()];\n        } else if (m[8] !== undefined) {\n            center.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[8], m[9]);\n        }\n\n        if (m[10]) {\n            center.y = LENGTH_FOR_POSITION[m[10].toLowerCase()];\n        } else if (m[11] !== undefined) {\n            center.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[11], m[12]);\n        }\n    }\n\n    var gradientCenter = {\n        x: center.x === undefined ? bounds.width / 2 : center.x.getAbsoluteValue(bounds.width),\n        y: center.y === undefined ? bounds.height / 2 : center.y.getAbsoluteValue(bounds.height)\n    };\n    var gradientRadius = calculateRadius(m && m[2] || 'farthest-corner', shape, gradientCenter, radius, bounds);\n\n    return new RadialGradient(parseColorStops(args, m ? 1 : 0, Math.min(gradientRadius.x, gradientRadius.y)), shape, gradientCenter, gradientRadius);\n};\n\nvar calculateGradientDirection = function calculateGradientDirection(radian, bounds) {\n    var width = bounds.width;\n    var height = bounds.height;\n    var HALF_WIDTH = width * 0.5;\n    var HALF_HEIGHT = height * 0.5;\n    var lineLength = Math.abs(width * Math.sin(radian)) + Math.abs(height * Math.cos(radian));\n    var HALF_LINE_LENGTH = lineLength / 2;\n\n    var x0 = HALF_WIDTH + Math.sin(radian) * HALF_LINE_LENGTH;\n    var y0 = HALF_HEIGHT - Math.cos(radian) * HALF_LINE_LENGTH;\n    var x1 = width - x0;\n    var y1 = height - y0;\n\n    return { x0: x0, x1: x1, y0: y0, y1: y1 };\n};\n\nvar parseTopRight = function parseTopRight(bounds) {\n    return Math.acos(bounds.width / 2 / ((0, _Util.distance)(bounds.width, bounds.height) / 2));\n};\n\nvar parseSideOrCorner = function parseSideOrCorner(side, bounds) {\n    switch (side) {\n        case 'bottom':\n        case 'to top':\n            return calculateGradientDirection(0, bounds);\n        case 'left':\n        case 'to right':\n            return calculateGradientDirection(Math.PI / 2, bounds);\n        case 'right':\n        case 'to left':\n            return calculateGradientDirection(3 * Math.PI / 2, bounds);\n        case 'top right':\n        case 'right top':\n        case 'to bottom left':\n        case 'to left bottom':\n            return calculateGradientDirection(Math.PI + parseTopRight(bounds), bounds);\n        case 'top left':\n        case 'left top':\n        case 'to bottom right':\n        case 'to right bottom':\n            return calculateGradientDirection(Math.PI - parseTopRight(bounds), bounds);\n        case 'bottom left':\n        case 'left bottom':\n        case 'to top right':\n        case 'to right top':\n            return calculateGradientDirection(parseTopRight(bounds), bounds);\n        case 'bottom right':\n        case 'right bottom':\n        case 'to top left':\n        case 'to left top':\n            return calculateGradientDirection(2 * Math.PI - parseTopRight(bounds), bounds);\n        case 'top':\n        case 'to bottom':\n        default:\n            return calculateGradientDirection(Math.PI, bounds);\n    }\n};\n\nvar parsePercentageAngle = function parsePercentageAngle(angle, bounds) {\n    var _angle$split$map = angle.split(' ').map(parseFloat),\n        _angle$split$map2 = _slicedToArray(_angle$split$map, 2),\n        left = _angle$split$map2[0],\n        top = _angle$split$map2[1];\n\n    var ratio = left / 100 * bounds.width / (top / 100 * bounds.height);\n\n    return calculateGradientDirection(Math.atan(isNaN(ratio) ? 1 : ratio) + Math.PI / 2, bounds);\n};\n\nvar findCorner = function findCorner(bounds, x, y, closest) {\n    var corners = [{ x: 0, y: 0 }, { x: 0, y: bounds.height }, { x: bounds.width, y: 0 }, { x: bounds.width, y: bounds.height }];\n\n    // $FlowFixMe\n    return corners.reduce(function (stat, corner) {\n        var d = (0, _Util.distance)(x - corner.x, y - corner.y);\n        if (closest ? d < stat.optimumDistance : d > stat.optimumDistance) {\n            return {\n                optimumCorner: corner,\n                optimumDistance: d\n            };\n        }\n\n        return stat;\n    }, {\n        optimumDistance: closest ? Infinity : -Infinity,\n        optimumCorner: null\n    }).optimumCorner;\n};\n\nvar calculateRadius = function calculateRadius(extent, shape, center, radius, bounds) {\n    var x = center.x;\n    var y = center.y;\n    var rx = 0;\n    var ry = 0;\n\n    switch (extent) {\n        case 'closest-side':\n            // The ending shape is sized so that that it exactly meets the side of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, it exactly meets the closest side in each dimension.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.min(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'closest-corner':\n            // The ending shape is sized so that that it passes through the corner of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, the ending shape is given the same aspect-ratio it would have if closest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"closest-side\")\n                var c = Math.min(Math.abs(y), Math.abs(y - bounds.height)) / Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                var corner = findCorner(bounds, x, y, true);\n                rx = (0, _Util.distance)(corner.x - x, (corner.y - y) / c);\n                ry = c * rx;\n            }\n            break;\n\n        case 'farthest-side':\n            // Same as closest-side, except the ending shape is sized based on the farthest side(s)\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.max(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'farthest-corner':\n            // Same as closest-corner, except the ending shape is sized based on the farthest corner.\n            // If the shape is an ellipse, the ending shape is given the same aspect ratio it would have if farthest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"farthest-side\")\n                var _c = Math.max(Math.abs(y), Math.abs(y - bounds.height)) / Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                var _corner = findCorner(bounds, x, y, false);\n                rx = (0, _Util.distance)(_corner.x - x, (_corner.y - y) / _c);\n                ry = _c * rx;\n            }\n            break;\n\n        default:\n            // pixel or percentage values\n            rx = radius.x || 0;\n            ry = radius.y !== undefined ? radius.y : rx;\n            break;\n    }\n\n    return {\n        x: rx,\n        y: ry\n    };\n};\n\nvar transformWebkitRadialGradientArgs = exports.transformWebkitRadialGradientArgs = function transformWebkitRadialGradientArgs(args) {\n    var shape = '';\n    var radius = '';\n    var extent = '';\n    var position = '';\n    var idx = 0;\n\n    var POSITION = /^(left|center|right|\\d+(?:px|r?em|%)?)(?:\\s+(top|center|bottom|\\d+(?:px|r?em|%)?))?$/i;\n    var SHAPE_AND_EXTENT = /^(circle|ellipse)?\\s*(closest-side|closest-corner|farthest-side|farthest-corner|contain|cover)?$/i;\n    var RADIUS = /^\\d+(px|r?em|%)?(?:\\s+\\d+(px|r?em|%)?)?$/i;\n\n    var matchStartPosition = args[idx].match(POSITION);\n    if (matchStartPosition) {\n        idx++;\n    }\n\n    var matchShapeExtent = args[idx].match(SHAPE_AND_EXTENT);\n    if (matchShapeExtent) {\n        shape = matchShapeExtent[1] || '';\n        extent = matchShapeExtent[2] || '';\n        if (extent === 'contain') {\n            extent = 'closest-side';\n        } else if (extent === 'cover') {\n            extent = 'farthest-corner';\n        }\n        idx++;\n    }\n\n    var matchStartRadius = args[idx].match(RADIUS);\n    if (matchStartRadius) {\n        idx++;\n    }\n\n    var matchEndPosition = args[idx].match(POSITION);\n    if (matchEndPosition) {\n        idx++;\n    }\n\n    var matchEndRadius = args[idx].match(RADIUS);\n    if (matchEndRadius) {\n        idx++;\n    }\n\n    var matchPosition = matchEndPosition || matchStartPosition;\n    if (matchPosition && matchPosition[1]) {\n        position = matchPosition[1] + (/^\\d+$/.test(matchPosition[1]) ? 'px' : '');\n        if (matchPosition[2]) {\n            position += ' ' + matchPosition[2] + (/^\\d+$/.test(matchPosition[2]) ? 'px' : '');\n        }\n    }\n\n    var matchRadius = matchEndRadius || matchStartRadius;\n    if (matchRadius) {\n        radius = matchRadius[0];\n        if (!matchRadius[1]) {\n            radius += 'px';\n        }\n    }\n\n    if (position && !shape && !radius && !extent) {\n        radius = position;\n        position = '';\n    }\n\n    if (position) {\n        position = 'at ' + position;\n    }\n\n    return [[shape, extent, radius, position].filter(function (s) {\n        return !!s;\n    }).join(' ')].concat(args.slice(idx));\n};\n\nvar transformObsoleteColorStops = function transformObsoleteColorStops(args) {\n    return args.map(function (color) {\n        return color.match(FROM_TO_COLORSTOP);\n    })\n    // $FlowFixMe\n    .map(function (v, index) {\n        if (!v) {\n            return args[index];\n        }\n\n        switch (v[1]) {\n            case 'from':\n                return v[4] + ' 0%';\n            case 'to':\n                return v[4] + ' 100%';\n            case 'color-stop':\n                if (v[3] === '%') {\n                    return v[4] + ' ' + v[2];\n                }\n                return v[4] + ' ' + parseFloat(v[2]) * 100 + '%';\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar ANGLE = /([+-]?\\d*\\.?\\d+)(deg|grad|rad|turn)/i;\n\nvar parseAngle = exports.parseAngle = function parseAngle(angle) {\n    var match = angle.match(ANGLE);\n\n    if (match) {\n        var value = parseFloat(match[1]);\n        switch (match[2].toLowerCase()) {\n            case 'deg':\n                return Math.PI * value / 180;\n            case 'grad':\n                return Math.PI / 200 * value;\n            case 'rad':\n                return value;\n            case 'turn':\n                return Math.PI * 2 * value;\n        }\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.cloneWindow = exports.DocumentCloner = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Proxy = require('./Proxy');\n\nvar _ResourceLoader = require('./ResourceLoader');\n\nvar _ResourceLoader2 = _interopRequireDefault(_ResourceLoader);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _PseudoNodeContent = require('./PseudoNodeContent');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar IGNORE_ATTRIBUTE = 'data-html2canvas-ignore';\n\nvar DocumentCloner = exports.DocumentCloner = function () {\n    function DocumentCloner(element, options, logger, copyInline, renderer) {\n        _classCallCheck(this, DocumentCloner);\n\n        this.referenceElement = element;\n        this.scrolledElements = [];\n        this.copyStyles = copyInline;\n        this.inlineImages = copyInline;\n        this.logger = logger;\n        this.options = options;\n        this.renderer = renderer;\n        this.resourceLoader = new _ResourceLoader2.default(options, logger, window);\n        this.pseudoContentData = {\n            counters: {},\n            quoteDepth: 0\n        };\n        // $FlowFixMe\n        this.documentElement = this.cloneNode(element.ownerDocument.documentElement);\n    }\n\n    _createClass(DocumentCloner, [{\n        key: 'inlineAllImages',\n        value: function inlineAllImages(node) {\n            var _this = this;\n\n            if (this.inlineImages && node) {\n                var style = node.style;\n                Promise.all((0, _background.parseBackgroundImage)(style.backgroundImage).map(function (backgroundImage) {\n                    if (backgroundImage.method === 'url') {\n                        return _this.resourceLoader.inlineImage(backgroundImage.args[0]).then(function (img) {\n                            return img && typeof img.src === 'string' ? 'url(\"' + img.src + '\")' : 'none';\n                        }).catch(function (e) {\n                            if (process.env.NODE_ENV !== 'production') {\n                                _this.logger.log('Unable to load image', e);\n                            }\n                        });\n                    }\n                    return Promise.resolve('' + backgroundImage.prefix + backgroundImage.method + '(' + backgroundImage.args.join(',') + ')');\n                })).then(function (backgroundImages) {\n                    if (backgroundImages.length > 1) {\n                        // TODO Multiple backgrounds somehow broken in Chrome\n                        style.backgroundColor = '';\n                    }\n                    style.backgroundImage = backgroundImages.join(',');\n                });\n\n                if (node instanceof HTMLImageElement) {\n                    this.resourceLoader.inlineImage(node.src).then(function (img) {\n                        if (img && node instanceof HTMLImageElement && node.parentNode) {\n                            var parentNode = node.parentNode;\n                            var clonedChild = (0, _Util.copyCSSStyles)(node.style, img.cloneNode(false));\n                            parentNode.replaceChild(clonedChild, node);\n                        }\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this.logger.log('Unable to load image', e);\n                        }\n                    });\n                }\n            }\n        }\n    }, {\n        key: 'inlineFonts',\n        value: function inlineFonts(document) {\n            var _this2 = this;\n\n            return Promise.all(Array.from(document.styleSheets).map(function (sheet) {\n                if (sheet.href) {\n                    return fetch(sheet.href).then(function (res) {\n                        return res.text();\n                    }).then(function (text) {\n                        return createStyleSheetFontsFromText(text, sheet.href);\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this2.logger.log('Unable to load stylesheet', e);\n                        }\n                        return [];\n                    });\n                }\n                return getSheetFonts(sheet, document);\n            })).then(function (fonts) {\n                return fonts.reduce(function (acc, font) {\n                    return acc.concat(font);\n                }, []);\n            }).then(function (fonts) {\n                return Promise.all(fonts.map(function (font) {\n                    return fetch(font.formats[0].src).then(function (response) {\n                        return response.blob();\n                    }).then(function (blob) {\n                        return new Promise(function (resolve, reject) {\n                            var reader = new FileReader();\n                            reader.onerror = reject;\n                            reader.onload = function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            };\n                            reader.readAsDataURL(blob);\n                        });\n                    }).then(function (dataUri) {\n                        font.fontFace.setProperty('src', 'url(\"' + dataUri + '\")');\n                        return '@font-face {' + font.fontFace.cssText + ' ';\n                    });\n                }));\n            }).then(function (fontCss) {\n                var style = document.createElement('style');\n                style.textContent = fontCss.join('\\n');\n                _this2.documentElement.appendChild(style);\n            });\n        }\n    }, {\n        key: 'createElementClone',\n        value: function createElementClone(node) {\n            var _this3 = this;\n\n            if (this.copyStyles && node instanceof HTMLCanvasElement) {\n                var img = node.ownerDocument.createElement('img');\n                try {\n                    img.src = node.toDataURL();\n                    return img;\n                } catch (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        this.logger.log('Unable to clone canvas contents, canvas is tainted');\n                    }\n                }\n            }\n\n            if (node instanceof HTMLIFrameElement) {\n                var tempIframe = node.cloneNode(false);\n                var iframeKey = generateIframeKey();\n                tempIframe.setAttribute('data-html2canvas-internal-iframe-key', iframeKey);\n\n                var _parseBounds = (0, _Bounds.parseBounds)(node, 0, 0),\n                    width = _parseBounds.width,\n                    height = _parseBounds.height;\n\n                this.resourceLoader.cache[iframeKey] = getIframeDocumentElement(node, this.options).then(function (documentElement) {\n                    return _this3.renderer(documentElement, {\n                        async: _this3.options.async,\n                        allowTaint: _this3.options.allowTaint,\n                        backgroundColor: '#ffffff',\n                        canvas: null,\n                        imageTimeout: _this3.options.imageTimeout,\n                        logging: _this3.options.logging,\n                        proxy: _this3.options.proxy,\n                        removeContainer: _this3.options.removeContainer,\n                        scale: _this3.options.scale,\n                        foreignObjectRendering: _this3.options.foreignObjectRendering,\n                        useCORS: _this3.options.useCORS,\n                        target: new _CanvasRenderer2.default(),\n                        width: width,\n                        height: height,\n                        x: 0,\n                        y: 0,\n                        windowWidth: documentElement.ownerDocument.defaultView.innerWidth,\n                        windowHeight: documentElement.ownerDocument.defaultView.innerHeight,\n                        scrollX: documentElement.ownerDocument.defaultView.pageXOffset,\n                        scrollY: documentElement.ownerDocument.defaultView.pageYOffset\n                    }, _this3.logger.child(iframeKey));\n                }).then(function (canvas) {\n                    return new Promise(function (resolve, reject) {\n                        var iframeCanvas = document.createElement('img');\n                        iframeCanvas.onload = function () {\n                            return resolve(canvas);\n                        };\n                        iframeCanvas.onerror = reject;\n                        iframeCanvas.src = canvas.toDataURL();\n                        if (tempIframe.parentNode) {\n                            tempIframe.parentNode.replaceChild((0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node), iframeCanvas), tempIframe);\n                        }\n                    });\n                });\n                return tempIframe;\n            }\n\n            if (node instanceof HTMLStyleElement && node.sheet && node.sheet.cssRules) {\n                var css = [].slice.call(node.sheet.cssRules, 0).reduce(function (css, rule) {\n                    return css + rule.cssText;\n                }, '');\n                var style = node.cloneNode(false);\n                style.textContent = css;\n                return style;\n            }\n\n            return node.cloneNode(false);\n        }\n    }, {\n        key: 'cloneNode',\n        value: function cloneNode(node) {\n            var clone = node.nodeType === Node.TEXT_NODE ? document.createTextNode(node.nodeValue) : this.createElementClone(node);\n\n            var window = node.ownerDocument.defaultView;\n            var style = node instanceof window.HTMLElement ? window.getComputedStyle(node) : null;\n            var styleBefore = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':before') : null;\n            var styleAfter = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':after') : null;\n\n            if (this.referenceElement === node && clone instanceof window.HTMLElement) {\n                this.clonedReferenceElement = clone;\n            }\n\n            if (clone instanceof window.HTMLBodyElement) {\n                createPseudoHideStyles(clone);\n            }\n\n            var counters = (0, _PseudoNodeContent.parseCounterReset)(style, this.pseudoContentData);\n            var contentBefore = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleBefore, this.pseudoContentData);\n\n            for (var child = node.firstChild; child; child = child.nextSibling) {\n                if (child.nodeType !== Node.ELEMENT_NODE || child.nodeName !== 'SCRIPT' &&\n                // $FlowFixMe\n                !child.hasAttribute(IGNORE_ATTRIBUTE) && (typeof this.options.ignoreElements !== 'function' ||\n                // $FlowFixMe\n                !this.options.ignoreElements(child))) {\n                    if (!this.copyStyles || child.nodeName !== 'STYLE') {\n                        clone.appendChild(this.cloneNode(child));\n                    }\n                }\n            }\n\n            var contentAfter = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleAfter, this.pseudoContentData);\n            (0, _PseudoNodeContent.popCounters)(counters, this.pseudoContentData);\n\n            if (node instanceof window.HTMLElement && clone instanceof window.HTMLElement) {\n                if (styleBefore) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleBefore, contentBefore, PSEUDO_BEFORE));\n                }\n                if (styleAfter) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleAfter, contentAfter, PSEUDO_AFTER));\n                }\n                if (style && this.copyStyles && !(node instanceof HTMLIFrameElement)) {\n                    (0, _Util.copyCSSStyles)(style, clone);\n                }\n                this.inlineAllImages(clone);\n                if (node.scrollTop !== 0 || node.scrollLeft !== 0) {\n                    this.scrolledElements.push([clone, node.scrollLeft, node.scrollTop]);\n                }\n                switch (node.nodeName) {\n                    case 'CANVAS':\n                        if (!this.copyStyles) {\n                            cloneCanvasContents(node, clone);\n                        }\n                        break;\n                    case 'TEXTAREA':\n                    case 'SELECT':\n                        clone.value = node.value;\n                        break;\n                    case 'INPUT':\n                        if (node.checked) {\n                            // required for IE9 and 10\n                            clone.setAttribute('checked', true);\n                        }\n                        break;\n                }\n            }\n            return clone;\n        }\n    }]);\n\n    return DocumentCloner;\n}();\n\nvar getSheetFonts = function getSheetFonts(sheet, document) {\n    // $FlowFixMe\n    return (sheet.cssRules ? Array.from(sheet.cssRules) : []).filter(function (rule) {\n        return rule.type === CSSRule.FONT_FACE_RULE;\n    }).map(function (rule) {\n        var src = (0, _background.parseBackgroundImage)(rule.style.getPropertyValue('src'));\n        var formats = [];\n        for (var i = 0; i < src.length; i++) {\n            if (src[i].method === 'url' && src[i + 1] && src[i + 1].method === 'format') {\n                var a = document.createElement('a');\n                a.href = src[i].args[0];\n                if (document.body) {\n                    document.body.appendChild(a);\n                }\n\n                var font = {\n                    src: a.href,\n                    format: src[i + 1].args[0]\n                };\n                formats.push(font);\n            }\n        }\n\n        return {\n            // TODO select correct format for browser),\n\n            formats: formats.filter(function (font) {\n                return (/^woff/i.test(font.format)\n                );\n            }),\n            fontFace: rule.style\n        };\n    }).filter(function (font) {\n        return font.formats.length;\n    });\n};\n\nvar createStyleSheetFontsFromText = function createStyleSheetFontsFromText(text, baseHref) {\n    var doc = document.implementation.createHTMLDocument('');\n    var base = document.createElement('base');\n    // $FlowFixMe\n    base.href = baseHref;\n    var style = document.createElement('style');\n\n    style.textContent = text;\n    if (doc.head) {\n        doc.head.appendChild(base);\n    }\n    if (doc.body) {\n        doc.body.appendChild(style);\n    }\n\n    return style.sheet ? getSheetFonts(style.sheet, doc) : [];\n};\n\nvar restoreOwnerScroll = function restoreOwnerScroll(ownerDocument, x, y) {\n    if (ownerDocument.defaultView && (x !== ownerDocument.defaultView.pageXOffset || y !== ownerDocument.defaultView.pageYOffset)) {\n        ownerDocument.defaultView.scrollTo(x, y);\n    }\n};\n\nvar cloneCanvasContents = function cloneCanvasContents(canvas, clonedCanvas) {\n    try {\n        if (clonedCanvas) {\n            clonedCanvas.width = canvas.width;\n            clonedCanvas.height = canvas.height;\n            var ctx = canvas.getContext('2d');\n            var clonedCtx = clonedCanvas.getContext('2d');\n            if (ctx) {\n                clonedCtx.putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);\n            } else {\n                clonedCtx.drawImage(canvas, 0, 0);\n            }\n        }\n    } catch (e) {}\n};\n\nvar inlinePseudoElement = function inlinePseudoElement(node, clone, style, contentItems, pseudoElt) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return;\n    }\n\n    var anonymousReplacedElement = clone.ownerDocument.createElement('html2canvaspseudoelement');\n    (0, _Util.copyCSSStyles)(style, anonymousReplacedElement);\n\n    if (contentItems) {\n        var len = contentItems.length;\n        for (var i = 0; i < len; i++) {\n            var item = contentItems[i];\n            switch (item.type) {\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.IMAGE:\n                    var img = clone.ownerDocument.createElement('img');\n                    img.src = (0, _background.parseBackgroundImage)('url(' + item.value + ')')[0].args[0];\n                    img.style.opacity = '1';\n                    anonymousReplacedElement.appendChild(img);\n                    break;\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.TEXT:\n                    anonymousReplacedElement.appendChild(clone.ownerDocument.createTextNode(item.value));\n                    break;\n            }\n        }\n    }\n\n    anonymousReplacedElement.className = PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    clone.className += pseudoElt === PSEUDO_BEFORE ? ' ' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE : ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    if (pseudoElt === PSEUDO_BEFORE) {\n        clone.insertBefore(anonymousReplacedElement, clone.firstChild);\n    } else {\n        clone.appendChild(anonymousReplacedElement);\n    }\n\n    return anonymousReplacedElement;\n};\n\nvar URL_REGEXP = /^url\\((.+)\\)$/i;\nvar PSEUDO_BEFORE = ':before';\nvar PSEUDO_AFTER = ':after';\nvar PSEUDO_HIDE_ELEMENT_CLASS_BEFORE = '___html2canvas___pseudoelement_before';\nvar PSEUDO_HIDE_ELEMENT_CLASS_AFTER = '___html2canvas___pseudoelement_after';\n\nvar PSEUDO_HIDE_ELEMENT_STYLE = '{\\n    content: \"\" !important;\\n    display: none !important;\\n}';\n\nvar createPseudoHideStyles = function createPseudoHideStyles(body) {\n    createStyles(body, '.' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + PSEUDO_BEFORE + PSEUDO_HIDE_ELEMENT_STYLE + '\\n         .' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER + PSEUDO_AFTER + PSEUDO_HIDE_ELEMENT_STYLE);\n};\n\nvar createStyles = function createStyles(body, styles) {\n    var style = body.ownerDocument.createElement('style');\n    style.innerHTML = styles;\n    body.appendChild(style);\n};\n\nvar initNode = function initNode(_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n        element = _ref2[0],\n        x = _ref2[1],\n        y = _ref2[2];\n\n    element.scrollLeft = x;\n    element.scrollTop = y;\n};\n\nvar generateIframeKey = function generateIframeKey() {\n    return Math.ceil(Date.now() + Math.random() * 10000000).toString(16);\n};\n\nvar DATA_URI_REGEXP = /^data:text\\/(.+);(base64)?,(.*)$/i;\n\nvar getIframeDocumentElement = function getIframeDocumentElement(node, options) {\n    try {\n        return Promise.resolve(node.contentWindow.document.documentElement);\n    } catch (e) {\n        return options.proxy ? (0, _Proxy.Proxy)(node.src, options).then(function (html) {\n            var match = html.match(DATA_URI_REGEXP);\n            if (!match) {\n                return Promise.reject();\n            }\n\n            return match[2] === 'base64' ? window.atob(decodeURIComponent(match[3])) : decodeURIComponent(match[3]);\n        }).then(function (html) {\n            return createIframeContainer(node.ownerDocument, (0, _Bounds.parseBounds)(node, 0, 0)).then(function (cloneIframeContainer) {\n                var cloneWindow = cloneIframeContainer.contentWindow;\n                var documentClone = cloneWindow.document;\n\n                documentClone.open();\n                documentClone.write(html);\n                var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n                    return documentClone.documentElement;\n                });\n\n                documentClone.close();\n                return iframeLoad;\n            });\n        }) : Promise.reject();\n    }\n};\n\nvar createIframeContainer = function createIframeContainer(ownerDocument, bounds) {\n    var cloneIframeContainer = ownerDocument.createElement('iframe');\n\n    cloneIframeContainer.className = 'html2canvas-container';\n    cloneIframeContainer.style.visibility = 'hidden';\n    cloneIframeContainer.style.position = 'fixed';\n    cloneIframeContainer.style.left = '-10000px';\n    cloneIframeContainer.style.top = '0px';\n    cloneIframeContainer.style.border = '0';\n    cloneIframeContainer.width = bounds.width.toString();\n    cloneIframeContainer.height = bounds.height.toString();\n    cloneIframeContainer.scrolling = 'no'; // ios won't scroll without it\n    cloneIframeContainer.setAttribute(IGNORE_ATTRIBUTE, 'true');\n    if (!ownerDocument.body) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'Body element not found in Document that is getting rendered' : '');\n    }\n\n    ownerDocument.body.appendChild(cloneIframeContainer);\n\n    return Promise.resolve(cloneIframeContainer);\n};\n\nvar iframeLoader = function iframeLoader(cloneIframeContainer) {\n    var cloneWindow = cloneIframeContainer.contentWindow;\n    var documentClone = cloneWindow.document;\n\n    return new Promise(function (resolve, reject) {\n        cloneWindow.onload = cloneIframeContainer.onload = documentClone.onreadystatechange = function () {\n            var interval = setInterval(function () {\n                if (documentClone.body.childNodes.length > 0 && documentClone.readyState === 'complete') {\n                    clearInterval(interval);\n                    resolve(cloneIframeContainer);\n                }\n            }, 50);\n        };\n    });\n};\n\nvar cloneWindow = exports.cloneWindow = function cloneWindow(ownerDocument, bounds, referenceElement, options, logger, renderer) {\n    var cloner = new DocumentCloner(referenceElement, options, logger, false, renderer);\n    var scrollX = ownerDocument.defaultView.pageXOffset;\n    var scrollY = ownerDocument.defaultView.pageYOffset;\n\n    return createIframeContainer(ownerDocument, bounds).then(function (cloneIframeContainer) {\n        var cloneWindow = cloneIframeContainer.contentWindow;\n        var documentClone = cloneWindow.document;\n\n        /* Chrome doesn't detect relative background-images assigned in inline <style> sheets when fetched through getComputedStyle\n             if window url is about:blank, we can assign the url to current by writing onto the document\n             */\n\n        var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n            cloner.scrolledElements.forEach(initNode);\n            cloneWindow.scrollTo(bounds.left, bounds.top);\n            if (/(iPad|iPhone|iPod)/g.test(navigator.userAgent) && (cloneWindow.scrollY !== bounds.top || cloneWindow.scrollX !== bounds.left)) {\n                documentClone.documentElement.style.top = -bounds.top + 'px';\n                documentClone.documentElement.style.left = -bounds.left + 'px';\n                documentClone.documentElement.style.position = 'absolute';\n            }\n\n            var result = Promise.resolve([cloneIframeContainer, cloner.clonedReferenceElement, cloner.resourceLoader]);\n\n            var onclone = options.onclone;\n\n            return cloner.clonedReferenceElement instanceof cloneWindow.HTMLElement || cloner.clonedReferenceElement instanceof ownerDocument.defaultView.HTMLElement || cloner.clonedReferenceElement instanceof HTMLElement ? typeof onclone === 'function' ? Promise.resolve().then(function () {\n                return onclone(documentClone);\n            }).then(function () {\n                return result;\n            }) : result : Promise.reject(process.env.NODE_ENV !== 'production' ? 'Error finding the ' + referenceElement.nodeName + ' in the cloned document' : '');\n        });\n\n        documentClone.open();\n        documentClone.write(serializeDoctype(document.doctype) + '<html></html>');\n        // Chrome scrolls the parent document for some reason after the write to the cloned window???\n        restoreOwnerScroll(referenceElement.ownerDocument, scrollX, scrollY);\n        documentClone.replaceChild(documentClone.adoptNode(cloner.documentElement), documentClone.documentElement);\n        documentClone.close();\n\n        return iframeLoad;\n    });\n};\n\nvar serializeDoctype = function serializeDoctype(doctype) {\n    var str = '';\n    if (doctype) {\n        str += '<!DOCTYPE ';\n        if (doctype.name) {\n            str += doctype.name;\n        }\n\n        if (doctype.internalSubset) {\n            str += doctype.internalSubset;\n        }\n\n        if (doctype.publicId) {\n            str += '\"' + doctype.publicId + '\"';\n        }\n\n        if (doctype.systemId) {\n            str += '\"' + doctype.systemId + '\"';\n        }\n\n        str += '>';\n    }\n\n    return str;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.ResourceStore = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Proxy = require('./Proxy');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ResourceLoader = function () {\n    function ResourceLoader(options, logger, window) {\n        _classCallCheck(this, ResourceLoader);\n\n        this.options = options;\n        this._window = window;\n        this.origin = this.getOrigin(window.location.href);\n        this.cache = {};\n        this.logger = logger;\n        this._index = 0;\n    }\n\n    _createClass(ResourceLoader, [{\n        key: 'loadImage',\n        value: function loadImage(src) {\n            var _this = this;\n\n            if (this.hasResourceInCache(src)) {\n                return src;\n            }\n\n            if (!isSVG(src) || _Feature2.default.SUPPORT_SVG_DRAWING) {\n                if (this.options.allowTaint === true || isInlineImage(src) || this.isSameOrigin(src)) {\n                    return this.addImage(src, src, false);\n                } else if (!this.isSameOrigin(src)) {\n                    if (typeof this.options.proxy === 'string') {\n                        this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                            return _loadImage(src, _this.options.imageTimeout || 0);\n                        });\n                        return src;\n                    } else if (this.options.useCORS === true && _Feature2.default.SUPPORT_CORS_IMAGES) {\n                        return this.addImage(src, src, true);\n                    }\n                }\n            }\n        }\n    }, {\n        key: 'inlineImage',\n        value: function inlineImage(src) {\n            var _this2 = this;\n\n            if (isInlineImage(src)) {\n                return _loadImage(src, this.options.imageTimeout || 0);\n            }\n            if (this.hasResourceInCache(src)) {\n                return this.cache[src];\n            }\n            if (!this.isSameOrigin(src) && typeof this.options.proxy === 'string') {\n                return this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                    return _loadImage(src, _this2.options.imageTimeout || 0);\n                });\n            }\n\n            return this.xhrImage(src);\n        }\n    }, {\n        key: 'xhrImage',\n        value: function xhrImage(src) {\n            var _this3 = this;\n\n            this.cache[src] = new Promise(function (resolve, reject) {\n                var xhr = new XMLHttpRequest();\n                xhr.onreadystatechange = function () {\n                    if (xhr.readyState === 4) {\n                        if (xhr.status !== 200) {\n                            reject('Failed to fetch image ' + src.substring(0, 256) + ' with status code ' + xhr.status);\n                        } else {\n                            var reader = new FileReader();\n                            reader.addEventListener('load', function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            }, false);\n                            reader.addEventListener('error', function (e) {\n                                return reject(e);\n                            }, false);\n                            reader.readAsDataURL(xhr.response);\n                        }\n                    }\n                };\n                xhr.responseType = 'blob';\n                if (_this3.options.imageTimeout) {\n                    var timeout = _this3.options.imageTimeout;\n                    xhr.timeout = timeout;\n                    xhr.ontimeout = function () {\n                        return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                    };\n                }\n                xhr.open('GET', src, true);\n                xhr.send();\n            }).then(function (src) {\n                return _loadImage(src, _this3.options.imageTimeout || 0);\n            });\n\n            return this.cache[src];\n        }\n    }, {\n        key: 'loadCanvas',\n        value: function loadCanvas(node) {\n            var key = String(this._index++);\n            this.cache[key] = Promise.resolve(node);\n            return key;\n        }\n    }, {\n        key: 'hasResourceInCache',\n        value: function hasResourceInCache(key) {\n            return typeof this.cache[key] !== 'undefined';\n        }\n    }, {\n        key: 'addImage',\n        value: function addImage(key, src, useCORS) {\n            var _this4 = this;\n\n            if (process.env.NODE_ENV !== 'production') {\n                this.logger.log('Added image ' + key.substring(0, 256));\n            }\n\n            var imageLoadHandler = function imageLoadHandler(supportsDataImages) {\n                return new Promise(function (resolve, reject) {\n                    var img = new Image();\n                    img.onload = function () {\n                        return resolve(img);\n                    };\n                    //ios safari 10.3 taints canvas with data urls unless crossOrigin is set to anonymous\n                    if (!supportsDataImages || useCORS) {\n                        img.crossOrigin = 'anonymous';\n                    }\n\n                    img.onerror = reject;\n                    img.src = src;\n                    if (img.complete === true) {\n                        // Inline XML images may fail to parse, throwing an Error later on\n                        setTimeout(function () {\n                            resolve(img);\n                        }, 500);\n                    }\n                    if (_this4.options.imageTimeout) {\n                        var timeout = _this4.options.imageTimeout;\n                        setTimeout(function () {\n                            return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                        }, timeout);\n                    }\n                });\n            };\n\n            this.cache[key] = isInlineBase64Image(src) && !isSVG(src) ? // $FlowFixMe\n            _Feature2.default.SUPPORT_BASE64_DRAWING(src).then(imageLoadHandler) : imageLoadHandler(true);\n            return key;\n        }\n    }, {\n        key: 'isSameOrigin',\n        value: function isSameOrigin(url) {\n            return this.getOrigin(url) === this.origin;\n        }\n    }, {\n        key: 'getOrigin',\n        value: function getOrigin(url) {\n            var link = this._link || (this._link = this._window.document.createElement('a'));\n            link.href = url;\n            link.href = link.href; // IE9, LOL! - http://jsfiddle.net/niklasvh/2e48b/\n            return link.protocol + link.hostname + link.port;\n        }\n    }, {\n        key: 'ready',\n        value: function ready() {\n            var _this5 = this;\n\n            var keys = Object.keys(this.cache);\n            var values = keys.map(function (str) {\n                return _this5.cache[str].catch(function (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        _this5.logger.log('Unable to load image', e);\n                    }\n                    return null;\n                });\n            });\n            return Promise.all(values).then(function (images) {\n                if (process.env.NODE_ENV !== 'production') {\n                    _this5.logger.log('Finished loading ' + images.length + ' images', images);\n                }\n                return new ResourceStore(keys, images);\n            });\n        }\n    }]);\n\n    return ResourceLoader;\n}();\n\nexports.default = ResourceLoader;\n\nvar ResourceStore = exports.ResourceStore = function () {\n    function ResourceStore(keys, resources) {\n        _classCallCheck(this, ResourceStore);\n\n        this._keys = keys;\n        this._resources = resources;\n    }\n\n    _createClass(ResourceStore, [{\n        key: 'get',\n        value: function get(key) {\n            var index = this._keys.indexOf(key);\n            return index === -1 ? null : this._resources[index];\n        }\n    }]);\n\n    return ResourceStore;\n}();\n\nvar INLINE_SVG = /^data:image\\/svg\\+xml/i;\nvar INLINE_BASE64 = /^data:image\\/.*;base64,/i;\nvar INLINE_IMG = /^data:image\\/.*/i;\n\nvar isInlineImage = function isInlineImage(src) {\n    return INLINE_IMG.test(src);\n};\nvar isInlineBase64Image = function isInlineBase64Image(src) {\n    return INLINE_BASE64.test(src);\n};\n\nvar isSVG = function isSVG(src) {\n    return src.substr(-3).toLowerCase() === 'svg' || INLINE_SVG.test(src);\n};\n\nvar _loadImage = function _loadImage(src, timeout) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n        img.src = src;\n        if (img.complete === true) {\n            // Inline XML images may fail to parse, throwing an Error later on\n            setTimeout(function () {\n                resolve(img);\n            }, 500);\n        }\n        if (timeout) {\n            setTimeout(function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) loading image' : '');\n            }, timeout);\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseContent = exports.resolvePseudoContent = exports.popCounters = exports.parseCounterReset = exports.TOKEN_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar PSEUDO_CONTENT_ITEM_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = {\n    TEXT: 0,\n    IMAGE: 1\n};\n\nvar TOKEN_TYPE = exports.TOKEN_TYPE = {\n    STRING: 0,\n    ATTRIBUTE: 1,\n    URL: 2,\n    COUNTER: 3,\n    COUNTERS: 4,\n    OPENQUOTE: 5,\n    CLOSEQUOTE: 6\n};\n\nvar parseCounterReset = exports.parseCounterReset = function parseCounterReset(style, data) {\n    if (!style || !style.counterReset || style.counterReset === 'none') {\n        return [];\n    }\n\n    var counterNames = [];\n    var counterResets = style.counterReset.split(/\\s*,\\s*/);\n    var lenCounterResets = counterResets.length;\n\n    for (var i = 0; i < lenCounterResets; i++) {\n        var _counterResets$i$spli = counterResets[i].split(/\\s+/),\n            _counterResets$i$spli2 = _slicedToArray(_counterResets$i$spli, 2),\n            counterName = _counterResets$i$spli2[0],\n            initialValue = _counterResets$i$spli2[1];\n\n        counterNames.push(counterName);\n        var counter = data.counters[counterName];\n        if (!counter) {\n            counter = data.counters[counterName] = [];\n        }\n        counter.push(parseInt(initialValue || 0, 10));\n    }\n\n    return counterNames;\n};\n\nvar popCounters = exports.popCounters = function popCounters(counterNames, data) {\n    var lenCounters = counterNames.length;\n    for (var i = 0; i < lenCounters; i++) {\n        data.counters[counterNames[i]].pop();\n    }\n};\n\nvar resolvePseudoContent = exports.resolvePseudoContent = function resolvePseudoContent(node, style, data) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return null;\n    }\n\n    var tokens = parseContent(style.content);\n\n    var len = tokens.length;\n    var contentItems = [];\n    var s = '';\n\n    // increment the counter (if there is a \"counter-increment\" declaration)\n    var counterIncrement = style.counterIncrement;\n    if (counterIncrement && counterIncrement !== 'none') {\n        var _counterIncrement$spl = counterIncrement.split(/\\s+/),\n            _counterIncrement$spl2 = _slicedToArray(_counterIncrement$spl, 2),\n            counterName = _counterIncrement$spl2[0],\n            incrementValue = _counterIncrement$spl2[1];\n\n        var counter = data.counters[counterName];\n        if (counter) {\n            counter[counter.length - 1] += incrementValue === undefined ? 1 : parseInt(incrementValue, 10);\n        }\n    }\n\n    // build the content string\n    for (var i = 0; i < len; i++) {\n        var token = tokens[i];\n        switch (token.type) {\n            case TOKEN_TYPE.STRING:\n                s += token.value || '';\n                break;\n\n            case TOKEN_TYPE.ATTRIBUTE:\n                if (node instanceof HTMLElement && token.value) {\n                    s += node.getAttribute(token.value) || '';\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTER:\n                var _counter = data.counters[token.name || ''];\n                if (_counter) {\n                    s += formatCounterValue([_counter[_counter.length - 1]], '', token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTERS:\n                var _counters = data.counters[token.name || ''];\n                if (_counters) {\n                    s += formatCounterValue(_counters, token.glue, token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.OPENQUOTE:\n                s += getQuote(style, true, data.quoteDepth);\n                data.quoteDepth++;\n                break;\n\n            case TOKEN_TYPE.CLOSEQUOTE:\n                data.quoteDepth--;\n                s += getQuote(style, false, data.quoteDepth);\n                break;\n\n            case TOKEN_TYPE.URL:\n                if (s) {\n                    contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n                    s = '';\n                }\n                contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.IMAGE, value: token.value || '' });\n                break;\n        }\n    }\n\n    if (s) {\n        contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n    }\n\n    return contentItems;\n};\n\nvar parseContent = exports.parseContent = function parseContent(content, cache) {\n    if (cache && cache[content]) {\n        return cache[content];\n    }\n\n    var tokens = [];\n    var len = content.length;\n\n    var isString = false;\n    var isEscaped = false;\n    var isFunction = false;\n    var str = '';\n    var functionName = '';\n    var args = [];\n\n    for (var i = 0; i < len; i++) {\n        var c = content.charAt(i);\n\n        switch (c) {\n            case \"'\":\n            case '\"':\n                if (isEscaped) {\n                    str += c;\n                } else {\n                    isString = !isString;\n                    if (!isFunction && !isString) {\n                        tokens.push({ type: TOKEN_TYPE.STRING, value: str });\n                        str = '';\n                    }\n                }\n                break;\n\n            case '\\\\':\n                if (isEscaped) {\n                    str += c;\n                    isEscaped = false;\n                } else {\n                    isEscaped = true;\n                }\n                break;\n\n            case '(':\n                if (isString) {\n                    str += c;\n                } else {\n                    isFunction = true;\n                    functionName = str;\n                    str = '';\n                    args = [];\n                }\n                break;\n\n            case ')':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    if (str) {\n                        args.push(str);\n                    }\n\n                    switch (functionName) {\n                        case 'attr':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.ATTRIBUTE, value: args[0] });\n                            }\n                            break;\n\n                        case 'counter':\n                            if (args.length > 0) {\n                                var counter = {\n                                    type: TOKEN_TYPE.COUNTER,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    counter.format = args[1];\n                                }\n                                tokens.push(counter);\n                            }\n                            break;\n\n                        case 'counters':\n                            if (args.length > 0) {\n                                var _counters2 = {\n                                    type: TOKEN_TYPE.COUNTERS,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    _counters2.glue = args[1];\n                                }\n                                if (args.length > 2) {\n                                    _counters2.format = args[2];\n                                }\n                                tokens.push(_counters2);\n                            }\n                            break;\n\n                        case 'url':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.URL, value: args[0] });\n                            }\n                            break;\n                    }\n\n                    isFunction = false;\n                    str = '';\n                }\n                break;\n\n            case ',':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    args.push(str);\n                    str = '';\n                }\n                break;\n\n            case ' ':\n            case '\\t':\n                if (isString) {\n                    str += c;\n                } else if (str) {\n                    addOtherToken(tokens, str);\n                    str = '';\n                }\n                break;\n\n            default:\n                str += c;\n        }\n\n        if (c !== '\\\\') {\n            isEscaped = false;\n        }\n    }\n\n    if (str) {\n        addOtherToken(tokens, str);\n    }\n\n    if (cache) {\n        cache[content] = tokens;\n    }\n\n    return tokens;\n};\n\nvar addOtherToken = function addOtherToken(tokens, identifier) {\n    switch (identifier) {\n        case 'open-quote':\n            tokens.push({ type: TOKEN_TYPE.OPENQUOTE });\n            break;\n        case 'close-quote':\n            tokens.push({ type: TOKEN_TYPE.CLOSEQUOTE });\n            break;\n    }\n};\n\nvar getQuote = function getQuote(style, isOpening, quoteDepth) {\n    var quotes = style.quotes ? style.quotes.split(/\\s+/) : [\"'\\\"'\", \"'\\\"'\"];\n    var idx = quoteDepth * 2;\n    if (idx >= quotes.length) {\n        idx = quotes.length - 2;\n    }\n    if (!isOpening) {\n        ++idx;\n    }\n    return quotes[idx].replace(/^[\"']|[\"']$/g, '');\n};\n\nvar formatCounterValue = function formatCounterValue(counter, glue, format) {\n    var len = counter.length;\n    var result = '';\n\n    for (var i = 0; i < len; i++) {\n        if (i > 0) {\n            result += glue || '';\n        }\n        result += (0, _ListItem.createCounterText)(counter[i], (0, _listStyle.parseListStyleType)(format || 'decimal'), false);\n    }\n\n    return result;\n};", "'use strict';\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _Window = require('./Window');\n\nvar _Bounds = require('./Bounds');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar html2canvas = function html2canvas(element, conf) {\n    var config = conf || {};\n    var logger = new _Logger2.default(typeof config.logging === 'boolean' ? config.logging : true);\n    logger.log('html2canvas ' + \"$npm_package_version\");\n\n    if (process.env.NODE_ENV !== 'production' && typeof config.onrendered === 'function') {\n        logger.error('onrendered option is deprecated, html2canvas returns a Promise with the canvas as the value');\n    }\n\n    var ownerDocument = element.ownerDocument;\n    if (!ownerDocument) {\n        return Promise.reject('Provided element is not within a Document');\n    }\n    var defaultView = ownerDocument.defaultView;\n\n    var scrollX = defaultView.pageXOffset;\n    var scrollY = defaultView.pageYOffset;\n\n    var isDocument = element.tagName === 'HTML' || element.tagName === 'BODY';\n\n    var _ref = isDocument ? (0, _Bounds.parseDocumentSize)(ownerDocument) : (0, _Bounds.parseBounds)(element, scrollX, scrollY),\n        width = _ref.width,\n        height = _ref.height,\n        left = _ref.left,\n        top = _ref.top;\n\n    var defaultOptions = {\n        async: true,\n        allowTaint: false,\n        backgroundColor: '#ffffff',\n        imageTimeout: 15000,\n        logging: true,\n        proxy: null,\n        removeContainer: true,\n        foreignObjectRendering: false,\n        scale: defaultView.devicePixelRatio || 1,\n        target: new _CanvasRenderer2.default(config.canvas),\n        useCORS: false,\n        x: left,\n        y: top,\n        width: Math.ceil(width),\n        height: Math.ceil(height),\n        windowWidth: defaultView.innerWidth,\n        windowHeight: defaultView.innerHeight,\n        scrollX: defaultView.pageXOffset,\n        scrollY: defaultView.pageYOffset\n    };\n\n    var result = (0, _Window.renderElement)(element, _extends({}, defaultOptions, config), logger);\n\n    if (process.env.NODE_ENV !== 'production') {\n        return result.catch(function (e) {\n            logger.error(e);\n            throw e;\n        });\n    }\n    return result;\n};\n\nhtml2canvas.CanvasRenderer = _CanvasRenderer2.default;\n\nmodule.exports = html2canvas;"], "sourceRoot": ""}