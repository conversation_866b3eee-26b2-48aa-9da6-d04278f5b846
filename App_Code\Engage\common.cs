﻿using System;
using System.Data;
using System.Web;
using System.Data.SqlClient;
using System.Configuration;
using System.Text.RegularExpressions;
using System.Globalization;
using System.Net;
using System.IO;
using System.Web.Services.Description;
using System.CodeDom;
using System.CodeDom.Compiler;
using System.Reflection;
using System.Xml.Serialization;
using System.Threading;
using System.Collections;
using System.Web.UI;
using System.Text;
using System.Security.Cryptography;

namespace Engage
{ 
/// <summary>
/// 公用函式(V2.0 2012/03/28)
/// 公用函式(V2.1 2013/01/03)
/// </summary>
public class common : System.Web.UI.Page
{
    public SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();

    //繼承使用說明(請於web.config依據下列項目名稱定義參數)：
    //1.配合系統位於測試區或上線區，需設定參數testORonline，以識別FAQ環境位置。
    //2.Error Handing功能，須設定系統代碼參數sysCode。
    //3.資料庫連線名稱，須設定參數ConnString；ConnString為SSO的資料庫連線(各系統應該一致)
    //4.安全掃描時，須關閉信件發信功能，須設定參數CloseSendMail。
    //5.錯誤時導向的頁面，須設定參數ErrorPage。
    //6.設定是否提供多國語系功能，須設定參數Language(預設false)。注意：系統需有自己的使用者語言紀錄表(工號、語系)，才可使用為true。

    //PS.如有需調整或增加，請告知柏宏或奕嘉調整後重新發布(勿自行修改，否則可能會有版本覆蓋問題產生)。

    //1.FAQ系統專用(參數值：test or online)。
    private string testORonline = System.Web.Configuration.WebConfigurationManager.AppSettings["ServerSite"];

    //2.FAQ系統環境專用:向FAQ系統負責人(淑娟)申請取得的系統代碼，且必須為有效系統代碼才會記錄到FAQ。(若暫不寫入FAQ時，請給空字串。)
	//將SysCode改成使用"F2-28" Iso name, 因為目前的 OnError() 只接受 "28"，故過濾掉"F2-"
    private string sysCode = System.Web.Configuration.WebConfigurationManager.AppSettings["sysCode"].Replace("F2-","");

    //3.資料庫連線。
    //public SqlConnection ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.AppSettings["ConnString"]);//ConnString為SSO的資料庫連線(各系統應該一致)
    public SqlConnection ConnString = new SqlConnection(System.Web.Configuration.WebConfigurationManager.ConnectionStrings["ConnString"].ConnectionString);//ConnString為SSO的資料庫連線(各系統應該一致)

    //4.安全掃描時，須關閉信件通知功能(參數值：open or close)。
    private string CloseSendMail = System.Web.Configuration.WebConfigurationManager.AppSettings["CloseSendMail"];

    //5.錯誤時導向的頁面。
    private string errorPage = System.Web.Configuration.WebConfigurationManager.AppSettings["ErrorPage"];

    //6.設定是否提供多國語系功能(預設false)。注意：系統需有自己的使用者語言紀錄表(工號、語系)，才可使用為true。
    private bool useLanguage = Convert.ToBoolean(System.Web.Configuration.WebConfigurationManager.AppSettings["Language"]);
    private string useLanguageSqlQuery = System.Web.Configuration.WebConfigurationManager.AppSettings["LanguageSqlQuery"];

    public common()
    {
		//sso.GetEmpInfo();

		//if (useLanguage)
		//{
		//	//取得使用者慣用語系值(by各系統DD，請自行修改參數SQL語法，查詢回傳語系代碼，如:zh-TW)   
		//	GetProfileLanguage(useLanguageSqlQuery);
		//}
    }

	protected override void OnPreInit(EventArgs e)
	{
		sso.GetEmpInfo();

		if (useLanguage)
		{
			//取得使用者慣用語系值(by各系統DD，請自行修改參數SQL語法，查詢回傳語系代碼，如:zh-TW)   
			GetProfileLanguage(useLanguageSqlQuery);
		}
		base.OnPreInit(e);
	}

    #region 系統發生錯誤通知
    /// <summary>
    /// 系統發生錯誤通知(系統管理人、系統主管)
    /// </summary>
    /// <param name=""></param>
	protected override void OnError(EventArgs e)
	{
		try
		{
			string sysName = "", mailto = "", mailTo_str = "", mailCc_str = "", mailBcc_str = "";

			string mailFrom = "<EMAIL>";//此為統一標準不可修改        

			string aa = GetSystemDataOfFAQ().ToString();

			if (aa != "")
			{
				string[] arInfo = aa.Split(',');

				//系統名稱
				sysName = arInfo[1].ToString();

				//系統管理人
				if (arInfo[4].ToString() == "1")//判斷是否通知
				{
					mailto = GetEmpEmail(arInfo[2].ToString());
					if (mailto != "")
					{
						//收信方式
						if (arInfo[6].ToString() == "0")
						{
							mailTo_str += mailto + ",";
						}
						else if (arInfo[6].ToString() == "1")
						{
							mailCc_str += mailto + ",";
						}
						else
						{
							mailBcc_str += mailto + ",";
						}
					}
				}

				//系統主管
				if (arInfo[5].ToString() == "1")//判斷是否通知
				{
					mailto = GetEmpEmail(arInfo[3].ToString());
					if (mailto != "")
					{
						//收信方式
						if (arInfo[7].ToString() == "0")
						{
							mailTo_str += mailto + ",";
						}
						else if (arInfo[7].ToString() == "1")
						{
							mailCc_str += mailto + ",";
						}
						else
						{
							mailBcc_str += mailto + ",";
						}
					}
				}

				//mailCc_str += "<EMAIL>,<EMAIL>,";
				//mailCc_str += "<EMAIL>,";

				if (mailTo_str != "")
					mailTo_str = mailTo_str.Substring(0, mailTo_str.Length - 1);
				if (mailCc_str != "")
					mailCc_str = mailCc_str.Substring(0, mailCc_str.Length - 1);
				if (mailBcc_str != "")
					mailBcc_str = mailBcc_str.Substring(0, mailBcc_str.Length - 1);


				string[] mailTo = mailTo_str.Split(',');
				string[] mailCc = mailCc_str.Split(',');
				string[] mailBcc = mailBcc_str.Split(',');

				string mStrHostName = System.Net.Dns.GetHostName();

				System.Net.IPAddress[] server_ip = System.Net.Dns.GetHostAddresses(mStrHostName);

				string strIP = null;
				foreach (IPAddress serverip in server_ip)
				{
					if (serverip.ToString().Contains("140."))
						strIP += serverip.ToString();
				}

				if (strIP == null)
					strIP = "無";

				string mailSubject = sysName + "系統發生異常錯誤";
				string mailBody = "發生時間：" + DateTime.Now + "<BR>" +
								  "主機IP：" + strIP + "<BR>" +
								  "發生網頁：" + Page.Request.Url + "<BR>" +
								  "使用者：" + sso.empName + "(" + sso.empNo + ")" + "<BR>" +
								  "錯誤內容：" + Server.GetLastError().GetBaseException().ToString() + "<BR>";

				if (mailTo_str != "" || mailCc_str != "" || mailBcc_str != "")
				{
					//錯誤資訊log to FAQ 
					string ip = GetClientIP();
					saveErrorMsgToFAQ(testORonline, sysCode, sso.empNo, mailBody, ip);

					if (!Server.GetLastError().GetBaseException().ToString().Contains("System.Web.UI.ViewStateException: Invalid viewstate."))
					{
						sendMail(mailFrom, mailTo, mailCc, mailBcc, mailSubject, mailBody);
					}
				}
			}
		}
		catch (Exception ex)
		{
			throw new Exception("系統發生錯誤通知(common.OnError)發生錯誤，錯誤訊息：" + ex.Message);
		}
		finally
		{
			Response.Redirect(errorPage);
		}
	}
    #endregion

    #region 半形轉全形
    /// <summary>
    /// 半形轉全形
    /// </summary>
    /// <param name="word">字串</param>
    public string FilterSpecialChar(string word)
    {
        string p1 = "";
        p1 = word.Replace("|", "｜");
        p1 = p1.Replace("&", "＆");
        //p1 = p1.Replace(";", "；");
        p1 = p1.Replace("$", "＄");
        p1 = p1.Replace("%", "％");
        p1 = p1.Replace("@", "＠");
        p1 = p1.Replace("'", "’");
        p1 = p1.Replace("<", "＜");
        p1 = p1.Replace(">", "＞");
        p1 = p1.Replace("(", "（");
        p1 = p1.Replace(")", "）");
        p1 = p1.Replace("+", "＋");
        p1 = p1.Replace("#", "＃");
        p1 = p1.Replace(":", "：");
        p1 = p1.Replace("*", "＊");
        p1 = p1.Replace("?", "？");
        p1 = p1.Replace("\"", "”");
        p1 = p1.Replace("/", "／");
        p1 = p1.Replace("\\", "＼");
        p1 = p1.Replace("&lt", "＜");
        p1 = p1.Replace("&gt", "＞");
        //p1 = p1.Replace("--", "－－");
        //如果有連續兩個以上的"-"，則將所有的"-"變成全型"－"
        if (p1.IndexOf("--") > -1)
            p1 = p1.Replace("-", "－");

        return p1.Trim();
    }
    #endregion

    #region 全形轉半形
    /// <summary>
    /// 全形轉半形
    /// </summary>
    /// <param name="strOri"></param>
    /// <returns></returns>
    public string DoRetrunSpecialChar(string strOri)
    {
        if (strOri.Length > 0)
        {
            strOri = strOri.Replace("︱", "|");
            strOri = strOri.Replace("＆", "&");
            //strOri = strOri.Replace(";", "；");
            strOri = strOri.Replace("＄", "$");
            strOri = strOri.Replace("％", "%");
            strOri = strOri.Replace("＠", "@");
            strOri = strOri.Replace("’", "'");
            strOri = strOri.Replace("＜", "<");
            strOri = strOri.Replace("（", "(");
            strOri = strOri.Replace("＂", "\"");
            strOri = strOri.Replace("＞", ">");
            strOri = strOri.Replace("）", ")");
            strOri = strOri.Replace("＋", "+");
            strOri = strOri.Replace("＃", "#");
            strOri = strOri.Replace("ＣＲ", " CR ");
            strOri = strOri.Replace("ＬＦ", " LF ");
            strOri = strOri.Replace("＼", "\\");
            strOri = strOri.Replace("＆lt", "&lt");
            strOri = strOri.Replace("＆gt", "&gt");
            //strOri = strOri.Replace("－", "--");//如果有連續兩個以上的"-"，則將所有的"-"變成全型"－"
            strOri = strOri.Replace("－", "-");//如果有連續兩個以上的"-"，則將所有的"-"變成全型"－"

        }

        return strOri;
    }
    #endregion

    #region 去除特殊字元
    /// <summary>
    /// 去除特殊字元
    /// </summary>
    /// <param name="word">字串</param>
    public string clearn(string word)
    {
        string p1 = "";
        p1 = word.Replace("|", "");
        p1 = p1.Replace("&", "");
        p1 = p1.Replace(";", "");
        p1 = p1.Replace("$", "");
        p1 = p1.Replace("%", "");
        p1 = p1.Replace("@", "");
        p1 = p1.Replace("'", "");
        p1 = p1.Replace("<", "");
        p1 = p1.Replace("(", "");
        p1 = p1.Replace("\"", "");
        p1 = p1.Replace(">", "");
        p1 = p1.Replace(")", "");
        p1 = p1.Replace("+", "");
        p1 = p1.Replace("#", "");
        p1 = p1.Replace("CR", "");
        p1 = p1.Replace("LF", "");
        p1 = p1.Replace("\\", "");
        p1 = p1.Replace("&lt", "");
        p1 = p1.Replace("&gt", "");
        p1 = p1.Replace("--", "");
        return p1;
    }
    #endregion

    #region 轉換成YYYY/MM/DD格式
    /// <summary>
    /// 轉換成YYYY/MM/DD格式
    /// </summary>
    /// <param name="strInputDate">8碼日期字串</param>
    /// <returns></returns>
    public string DateToSlash(string strInputDate)
    {
        string str = string.Empty;
        strInputDate = strInputDate.Trim();
        if (strInputDate.Length != 8)
            strInputDate = "";

        if (strInputDate != "")
        {
            str = string.Format("{0}/{1}/{2}", strInputDate.Substring(0, 4), strInputDate.Substring(4, 2), strInputDate.Substring(6, 2));
        }

        return str;
    }
    #endregion

    #region 將過多的字轉成 Tip 格式
    /// <summary>
    /// 將過多的字轉成 Tip 格式(ex.只要顯示七個字...，滑鼠移到...時會出現Tip顯示所有內容)
    /// 1.請先確定頁面有引用 <script type="text/javascript" src="../js/jQueryTips.js"></script>
    /// 2.請先確定頁面有套用 <link href="../css/jQueryTips.css" rel="Stylesheet" type="text/css" />
    /// </summary>
    /// <param name="strOri">內容字串</param>
    /// <param name="iMax">欲顯示幾個字後用...代替</param>
    /// <returns></returns>
    public string TransMore(string strOri, int iMax)
    {
        string strResutl = string.Empty;
        StringBuilder sb = new StringBuilder();

        if (iMax <= 3)
            return strOri;

        if (strOri.Length > iMax)
        {
            sb.Append(strOri.Substring(0, iMax - 3));
            sb.Append(string.Format("<a href='#' class='ShowTips' title='{0}'>...</a>", strOri.Replace("\r\n", "<br>")));

        }
        else
            sb.Append(strOri);


        return sb.ToString();
    }
    #endregion

    #region 根據關鍵字，取得員工基本資料
    /// <summary>
    /// 根據關鍵字，取得員工基本資料
    /// </summary>
    /// <param name="keyword">工號或姓名</param>
    /// <returns>DataSet</returns>
    public DataSet GetEmpBasicData(string keyword)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select * from common..comper 
left join common..orgcod on com_orgcd=org_orgcd 
left join common..depcod on com_orgcd=dep_orgcd and com_deptcd=dep_deptcd
where com_depcd='N' and (com_empno like '%'+@keyword+'%' or com_cname like '%'+@keyword+'%' or com_ename like '%'+@keyword+'%')
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@keyword", keyword);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("根據關鍵字，取得員工基本資料(common.GetEmpBasicData)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取WebConfig變數
    public string GetConfigString(string strConfig)
    {
        return System.Web.Configuration.WebConfigurationManager.AppSettings[strConfig];
    }
    #endregion

	#region 取得FAQ的系統負責人、業務人員、相關人員
	/// <summary>
	/// 取得FAQ的系統負責人、業務人員、相關人員、主管(依身份用分號串接(每段又依-區隔為值為工號、姓名、是否收到通知))
	/// </summary>
	/// <returns></returns>
	public DataTable GetFAQSysOnwer()
	{
		DataTable dt = new DataTable();
		dt.Columns.Add("type", typeof(String));
		dt.Columns.Add("empno", typeof(String));
		dt.Columns.Add("cname", typeof(String));
		dt.Columns.Add("isGetMail", typeof(String));
		DataRow row;
		string str = GetSystemOwnerList().ToString();

		string[] arInfo = str.Split(';');
		for (int i = 0; i < arInfo.Length; i++)
		{
			string[] arInfo2 = arInfo[i].Split('-');

			row = dt.NewRow();
			if (i == 0)
				row["type"] = "sysOwner";
			if (i == 1)
				row["type"] = "buzOwner";
			if (i == 2)
				row["type"] = "other";
			if (i == 3)
				row["type"] = "boss";
			row["empno"] = arInfo2[0].ToString().Trim();
			row["cname"] = arInfo2[1].ToString().Trim();

			if (arInfo2[2].ToString().Trim() == "1")
				row["isGetMail"] = "Y";
			else
				row["isGetMail"] = "N";

			dt.Rows.Add(row);
		}

		return dt;
	}
	#endregion

    #region 取得員工角色
    /// <summary>
    /// 取得員工角色
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns>角色</returns>
    public string GetEmpRole(string empno)
    {
        try
        {
            string role = "";
            DataSet ds = new DataSet();

            string sql = @"
DECLARE @grpnm varchar(15)
exec [filter_GP] @empno,@grpnm OUTPUT
select @grpnm
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@empno", empno);

            ds = runParaCmdDS(sqlCmd);

            if (ds.Tables[0].Rows.Count > 0)
            {
                role = ds.Tables[0].Rows[0][0].ToString().Trim();
            }

            return role;
        }
        catch (Exception ex)
        {
            throw new Exception("取得員工姓名(common.GetEmpName)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得員工姓名
    /// <summary>
    /// 取得員工姓名
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns>姓名</returns>
    public string GetEmpName(string empno)
    {
        try
        {
            string name = "";
            DataSet ds = new DataSet();

            string sql = @"
select com_cname from common..comper left join common..orgcod on com_orgcd=org_orgcd 
where com_depcd='N' and com_empno=@empno
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@empno", empno);

            ds = runParaCmdDS(sqlCmd);

            if (ds.Tables[0].Rows.Count > 0)
            {
                name = ds.Tables[0].Rows[0][0].ToString();
            }

            return name;
        }
        catch (Exception ex)
        {
            throw new Exception("取得員工姓名(common.GetEmpName)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得員工信箱
    /// <summary>
    /// 取得員工信箱
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns>信箱地址</returns>
    public string GetEmpEmail(string empno)
    {
        string mailaddress = "";
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select com_mailadd from common..comper where com_depcd='N' and com_empno=@empno
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.Parameters.AddWithValue("@empno", empno);

            SqlDataAdapter oDataAdapter;
            oDataAdapter = new SqlDataAdapter(sqlCmd);
            oDataAdapter.Fill(ds, 0, 0, "Table1");
            oDataAdapter.Dispose();

            ConnString.Close();

            if (ds.Tables[0].Rows.Count > 0)
            {
                mailaddress = ds.Tables[0].Rows[0][0].ToString();
            }
        }
        catch (Exception ex)
        {
            throw new Exception("取得員工信箱(common.GetEmpEmail)發生錯誤，錯誤訊息：" + ex.Message);
        }
        return mailaddress;
    }
    #endregion

    #region 取得員工分機
    /// <summary>
    /// 取得員工信箱
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns>分機號碼</returns>
    public string GetEmptel(string empno)
    {
        string mailaddress = "";
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select com_telext from common..comper where com_depcd='N' and com_empno=@empno
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.Parameters.AddWithValue("@empno", empno);

            SqlDataAdapter oDataAdapter;
            oDataAdapter = new SqlDataAdapter(sqlCmd);
            oDataAdapter.Fill(ds, 0, 0, "Table1");
            oDataAdapter.Dispose();

            ConnString.Close();

            if (ds.Tables[0].Rows.Count > 0)
            {
                mailaddress = ds.Tables[0].Rows[0][0].ToString();
            }
        }
        catch (Exception ex)
        {
            throw new Exception("取得員工信箱(common.GetEmptel)發生錯誤，錯誤訊息：" + ex.Message);
        }
        return mailaddress;
    }
    #endregion

    #region 取得員工單位
    /// <summary>
    /// 取得員工單位
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns>單位名稱、代號、縮寫、簡稱</returns>
    public string GetEmpOrgcd(string empno)
    {
        string mailaddress = "";
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select org_orgcd,org_abbr_chnm1,org_abbr_chnm2,org_abbr_egnm from common..comper left join common..orgcod on com_orgcd=org_orgcd
where com_empno=@empno
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.Parameters.AddWithValue("@empno", empno);

            SqlDataAdapter oDataAdapter;
            oDataAdapter = new SqlDataAdapter(sqlCmd);
            oDataAdapter.Fill(ds, 0, 0, "Table1");
            oDataAdapter.Dispose();

            ConnString.Close();

            if (ds.Tables[0].Rows.Count > 0)
            {
                mailaddress = ds.Tables[0].Rows[0][0].ToString();
            }
        }
        catch (Exception ex)
        {
            throw new Exception("取得員工單位(common.GetEmpOrgcd)發生錯誤，錯誤訊息：" + ex.Message);
        }
        return mailaddress;
    }
    #endregion

    #region 取得員工的主管資料
    /// <summary>
    /// 取得員工的主管資料(最高主管只到組)
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns></returns>
    public DataSet GetEmpsBossData(string empno)
    {
        string sql = @"
declare @managerEmpno nvarchar(11)
select @managerEmpno=dep_manager_id from common..comper left join common..depcod on com_deptid=dep_deptid
where com_empno=@empno

select * from common..comper left join common..depcod on com_deptid=dep_deptid
where com_empno=@managerEmpno
";
        SqlCommand sqlCmd = new SqlCommand(sql);
        sqlCmd.CommandType = CommandType.Text;
        sqlCmd.Parameters.AddWithValue("@empno", empno);

        return runParaCmdDS(sqlCmd);
    }
    #endregion

    #region 取得組的主管資料
    /// <summary>
    /// 取得組的主管資料
    /// </summary>
    /// <param name="empno">工號</param>
    /// <returns></returns>
    public DataSet GetGroupBoss(string orgcd, string deptcd)
    {
        string sql = @"
select * from common..sec_mngr left join common..comper on sec_empno=com_empno
where sec_orgcd=@orgcd and sec_deptcd=@deptcd and sec_type='1'
";
        SqlCommand sqlCmd = new SqlCommand(sql);
        sqlCmd.CommandType = CommandType.Text;
        sqlCmd.Parameters.AddWithValue("@orgcd", orgcd);
        sqlCmd.Parameters.AddWithValue("@deptcd", deptcd);

        return runParaCmdDS(sqlCmd);
    }
    #endregion

    #region 取得單位資料(可帶出所有單位或帶出單一單位資料)
    /// <summary>
    /// 取得單位資料(可帶出所有單位或帶出單一單位資料)
    /// </summary>
    /// <param name="orgcd">單位代碼(查全部請給空字串)</param>
    /// <returns>DataSet</returns>
    public DataSet GetOrgBasicData(string orgcd)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
declare @sql varchar(1000)
set @sql='
select org_orgcd,org_abbr_chnm2,org_abbr_chnm1,org_abbr_egnm from common..orgcod where org_status=''A'' and org_orgcd<>00' 

if(@org_orgcd<>'')
	set @sql=@sql+'and org_orgcd='''+@org_orgcd+''' '

set @sql=@sql+'order by org_orgcd '

execute(@sql)
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@org_orgcd", orgcd);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("取得單位資料(common.GetOrgBasicData)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得部門資料(可帶出所有部門或帶出單一部門資料)
    /// <summary>
    /// 取得部門資料(可帶出所有部門或帶出單一部門資料)
    /// </summary>
    /// <param name="orgcd">單位代碼</param>
    /// <param name="deptcd">部門代碼</param>
    /// <returns>DataSet</returns>
    public DataSet GetDetpData(string orgcd, string deptcd)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
declare @sql varchar(1000)
set @sql='
select * , dep_deptcd + ''-'' + dep_deptname as dep_deptname1 from common..depcod  join common..orgcod on dep_orgcd=org_orgcd
where 1=1 ' 

if(@dep_orgcd<>'')
	set @sql=@sql+'and dep_orgcd='''+@dep_orgcd+''' '

if(@dep_deptcd<>'')
	set @sql=@sql+'and dep_deptcd='''+@dep_deptcd+''' '

set @sql=@sql+'order by dep_orgcd,dep_deptcd '

execute(@sql)
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@dep_orgcd", orgcd);
            sqlCmd.Parameters.AddWithValue("@dep_deptcd", deptcd);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("取得部門資料(common.GetDetpData)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得部門裡的員工
    /// <summary>
    /// 取得部門裡的員工
    /// </summary>
    /// <param name="orgcd">單位代號</param>
    /// <param name="deptcd">部門代號</param>
    /// <returns></returns>
    public DataSet GetDetpEmpno(string orgcd, string deptcd)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
declare @sql varchar(1000)
set @sql='select * from common..comper where com_orgcd='''+@dep_orgcd+''' and com_deptcd='''+@dep_deptcd+''' and com_depcd<>''Y'' order by com_cname '
execute(@sql)
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@dep_orgcd", orgcd);
            sqlCmd.Parameters.AddWithValue("@dep_deptcd", deptcd);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("取得部門裡的員工(common.GetDetpData)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得組長資料
    /// <summary>
    /// 取得組長資料
    /// </summary>
    /// <param name="orgcd"></param>
    /// <param name="deptcd"></param>
    /// <returns></returns>
    public DataSet GetGroupBossData(string orgcd, string deptcd)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select * from common..sec_mngr where sec_orgcd=@orgcd and sec_deptcd=(substring(@deptcd,1,2)+'000') and sec_type='1'
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@orgcd", orgcd);
            sqlCmd.Parameters.AddWithValue("@deptcd", deptcd);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("取得組長資料(common.GetGroupBossData)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 取得用戶端IP
    /// <summary>
    /// 取得用戶端IP
    /// </summary>
    /// <returns></returns>
    public string GetClientIP()
    {
        if (Request.ServerVariables["HTTP_X_FORWARDED_FOR"] == null)
        {
            return Request.ServerVariables["REMOTE_HOST"].ToString();
        }
        else
        {
            return Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
    }
    #endregion

    #region 輸入多筆姓名或工號組成的字串，查員工資料，並依據輸入順序排列回傳
    /// <summary>
    /// 輸入多筆姓名或工號組成的字串，查員工資料，並依據輸入順序排列回傳
    /// </summary>
    /// <param name="strWrod">輸入時的字串</param>
    /// <param name="symbols">串接的特殊符號：如分號、逗號...</param>
    /// <returns></returns>
    public DataSet GetMultipleEmpno(string strWrod, string symbols)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
declare @tempWord table (seq int identity(1,1) NOT NULL,empno nvarchar(11),cname nvarchar(30))

declare @finallyE nvarchar(4000)
declare @finallyC nvarchar(4000)
set @finallyE=''
set @finallyC=''

select * into #temp from fn_SplitWithIdNo(@strWrod, @symbols)

declare @word nvarchar(50)
declare @count int
declare @i int
set @i=1

select @count=count(*) from #temp

while (@i<=@count)
begin		

	select @word=rtrim(Items) from #temp where id_num=@i

	insert @tempWord
	select com_empno,rtrim(com_cname) from common..comper
	where com_depcd='N' and (com_empno like upper(@word)+'%' or com_cname like '%'+@word+'%') and com_type<>'ZZZ'
	
	
	set @i=@i+1
end


select @finallyE=(@finallyE+empno+@symbols),@finallyC=(@finallyC+cname+@symbols) from @tempWord 

drop table #temp

if(@finallyE<>'')
    set @finallyE=substring(@finallyE,1,len(@finallyE)-1)
if(@finallyC<>'')
    set @finallyC=substring(@finallyC,1,len(@finallyC)-1)

select empno=@finallyE,cname=@finallyC
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@strWrod", strWrod);
            sqlCmd.Parameters.AddWithValue("@symbols", symbols);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("輸入多筆關鍵字，查員工資料，並依據輸入順序排列回傳(common.GetMultipleEmpno)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 依據輸入字串查出同名同姓清單
    /// <summary>
    /// 依據輸入字串查出同名同姓清單
    /// </summary>
    /// <param name="strCname">輸入的關鍵字字串</param>
    /// <returns></returns>
    public DataSet GetMultEmpnoList(string strCname)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
SELECT * into #temp FROM fn_SplitWithIdNo(@strCname, ';') 

declare @temp table(com_empno nvarchar(11),com_cname nvarchar(50),org_abbr_chnm2 nvarchar(30),dep_deptname nvarchar(30),com_telext nvarchar(50))

declare @emp nvarchar(30)
declare @count int
declare @i int
set @i=1
select @count=count(*) from #temp

while (@i<=@count)
begin	
	select @emp=Items FROM #temp where id_num=@i
	
	insert @temp
	select rtrim(com_empno),rtrim(com_cname),rtrim(org_abbr_chnm2),rtrim(dep_abbrnm),com_telext
	from common..comper left join common..orgcod on com_orgcd=org_orgcd left join common..depcod on org_orgcd=dep_orgcd and com_deptcd=dep_deptcd
	where com_depcd='N' and (com_empno like UPPER(@emp)+'%' or com_cname like '%'+@emp+'%') and com_type<>'ZZZ'

	set @i=@i+1
end

drop table #temp

select distinct * from @temp order by com_cname
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@strCname", strCname);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("依據輸入字串查出同名同姓清單(common.GetMultEmpnoList)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 帶出計畫代號資訊(pro020)
    /// <summary>
    /// 帶出計畫代號資訊(pro020)
    /// </summary>
    /// <param name="keyword">計畫代號</param>
    /// <returns></returns>
    public DataSet GetProjNo(string projno)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
select * from common..pro020
where upper(p20_pojno) = upper(@projno)
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@projno", projno);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("根據關鍵字查出計畫代號(common.GetProjNo)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 帶出計畫代號資訊(prs020)
    /// <summary>
    /// 帶出計畫代號資訊(prs020)
    /// </summary>
    /// <param name="choose">0:單位 1:10%自由創新計畫 2:不限單位</param>
    /// <param name="org">單位</param>
    /// <param name="dept">部門</param>
    /// <param name="projno">計畫代號</param>
    /// <returns></returns>
    public DataSet GetProjNo(string choose, string org, string dept, string projno)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
if(@choose='0')
    begin
		select REPLACE(common.dbo.prs020.s20_pojcname, '''', '’') AS s20_pojcname,org_cname=org_abbr_chnm2,'' as ipright_name ,* 
		from common..prs020,common..orgcod --,tech_ipright 
		where s20_orgcd=@org  and ( lower(s20_pjdept) like '%'+lower(@dept)+'%' and lower(s20_pojno) like '%'+lower(@projno)+'%') 
				and s20_orgcd=org_orgcd --and s20_ipright = ipright_value 
		order by s20_bgyear desc,s20_pojno desc
    end
    else if(@choose='1')
    begin    
		select REPLACE(common.dbo.prs020.s20_pojcname, '''', '’') AS s20_pojcname,org_cname=org_abbr_chnm2,'' as ipright_name,* 
		from common..prs020,common..orgcod--,tech_ipright 
		where s20_orgcd=org_orgcd --and s20_ipright = ipright_value 
		and lower(s20_pojno) like '%'+lower(@projno)+'%' and substring(s20_pojno,2,1)='1' and substring(s20_pojno,3,1)='0' and substring(s20_pojno,4,1)='1' and substring(s20_pojno,5,1)='Q' and substring(s20_pojno,6,1)='V' and substring(s20_pojno,7,1)='3'
		order by s20_bgyear desc,s20_pojno desc
    end
    else
    begin    
		select REPLACE(common.dbo.prs020.s20_pojcname, '''', '’') AS s20_pojcname,org_cname=org_abbr_chnm2,'' as ipright_name  ,* 
		from common..prs020,common..orgcod--,tech_ipright 
		where lower(s20_pojno) like '%'+lower(@projno)+'%' 
				and s20_orgcd=org_orgcd --and s20_ipright = ipright_value 
		order by s20_bgyear desc,s20_pojno desc
    end
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@choose", choose);
            sqlCmd.Parameters.AddWithValue("@org", org);
            sqlCmd.Parameters.AddWithValue("@dept", dept);
            sqlCmd.Parameters.AddWithValue("@projno", projno);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("根據關鍵字查出計畫代號(common.GetProjNo)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 查詢計畫代號清單
    /// <summary>
    /// 查詢計畫代號清單
    /// </summary>
    /// <param name="orgcd"></param>
    /// <param name="keyword"></param>
    /// <returns></returns>
    public DataSet GetProjList(string orgcd, string keyword)
    {
        DataSet ds = new DataSet();

        string sql = @"
declare @sql varchar(1000)

set @sql='select * from common..pro020 where 1=1 '

if(@orgcd<>'')
    set @sql=@sql+' and p20_orgcd='''+@orgcd+''' '
if(@keyword<>'')
    set @sql=@sql+' and (upper(p20_pojno) like ''%'+upper(@keyword)+'%'' or p20_pojcname like ''%'+@keyword+'%'' or rtrim(p20_nminchrg) like ''%'+@keyword+'%'' or p20_noinchrg like ''%'+@keyword+'%'')'

execute(@sql)
";

        SqlCommand sqlCmd = new SqlCommand(sql);
        sqlCmd.CommandType = CommandType.Text;
        sqlCmd.Parameters.AddWithValue("@orgcd", orgcd);
        sqlCmd.Parameters.AddWithValue("@keyword", keyword);

        return runParaCmdDS(sqlCmd);
    }
    #endregion

    #region 工號字串轉換姓名字串回傳
    /// <summary>
    /// 工號字串轉換姓名字串
    /// </summary>
    /// <param name="strEmpno">工號字串(分號串接)</param>
    /// <returns>回傳工號字串及姓名字串</returns>
    public DataSet GetEmpnoString(string strEmpno, string symbol)
    {
        try
        {
            DataSet ds = new DataSet();

            string sql = @"
declare @empno nvarchar(1000)
set @empno=''
declare @cname nvarchar(1000)
set @cname=''

select @empno=@empno+rtrim(com_empno)+@symbol,@cname=@cname+rtrim(com_cname)+@symbol from fn_SplitWithIdNo(@strEmpno, @symbol) left join common..comper on Items=com_empno

if(@empno<>'')
set @empno=substring(@empno,1,len(@empno)-1)
if(@cname<>'')
set @cname=substring(@cname,1,len(@cname)-1)
select empno=@empno,cname=@cname
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@strEmpno", strEmpno);
            sqlCmd.Parameters.AddWithValue("@symbol", symbol);

            return runParaCmdDS(sqlCmd);
        }
        catch (Exception ex)
        {
            throw new Exception("根據關鍵字查出計畫代號(common.GetProjNo)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 過濾重複性資料
    /// <summary>
    /// 過濾重複性資料
    /// </summary>
    /// <param name="mailToStr"></param>
    /// <returns></returns>
    public string filterRepeat(string str, string symbol)
    {
        try
        {
            string sql = @"
select Items into #temp from fn_SplitWithIdNo(@str, @symbol)

select distinct * into #temp2 from #temp

declare @newStr nvarchar(1000)
set @newStr=''
select @newStr=@newStr+Items+',' from #temp2

drop table #temp,#temp2

select @newStr
";
            SqlCommand sqlCmd = new SqlCommand(sql, ConnString);
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.Parameters.AddWithValue("@str", str);
            sqlCmd.Parameters.AddWithValue("@symbol", symbol);

            return runScalar(sqlCmd).ToString();
        }
        catch (Exception ex)
        {
            throw new Exception("過濾重複性資料(common.filterRepeat)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 發送信件
    /// <summary>
    /// 發送信件
    /// </summary>
    /// <param name="strFROM">寄件者</param>
    /// <param name="strTO">收件者(string[])</param>
    /// <param name="strCC">副件者(string[])</param>
    /// <param name="strBCC">密件者(string[])</param>
    /// <param name="strSubject">主旨</param>
    /// <param name="strBody">信件內容</param>
    /// <returns>bool</returns>
    public bool sendMail(string strFROM, string[] strTO, string[] strCC, string[] strBCC, string strSubject, string strBody)
    {
        if (CloseSendMail == "open")
        {
            try
            {
                try
                {
                    // 1. 使用 WebClient 下載 WSDL 信息。
                    WebClient web = new WebClient();
                    Stream stream = web.OpenRead("http://itriap5.itri.org.tw/ws_rece/MailService.asmx?WSDL");

                    // 2. 創建和格式化 WSDL 文檔。
                    ServiceDescription description = ServiceDescription.Read(stream);

                    // 3. 創建客戶端代理代理類。
                    ServiceDescriptionImporter importer = new ServiceDescriptionImporter();

                    importer.ProtocolName = "Soap"; // 指定訪問協議。
                    importer.Style = ServiceDescriptionImportStyle.Client; // 生成客户端代理。
                    importer.CodeGenerationOptions = CodeGenerationOptions.GenerateProperties | CodeGenerationOptions.GenerateNewAsync;

                    importer.AddServiceDescription(description, null, null); // 添加 WSDL 文檔。

                    // 4. 使用 CodeDom 编译客户端代理类。
                    CodeNamespace nmspace = new CodeNamespace(); // 為代理類添加命名空間，缺省為全局空間。
                    CodeCompileUnit unit = new CodeCompileUnit();
                    unit.Namespaces.Add(nmspace);

                    ServiceDescriptionImportWarnings warning = importer.Import(nmspace, unit);
                    CodeDomProvider provider = CodeDomProvider.CreateProvider("CSharp");

                    CompilerParameters parameter = new CompilerParameters();
                    parameter.GenerateExecutable = false;
                    parameter.GenerateInMemory = true;
                    parameter.ReferencedAssemblies.Add("System.dll");
                    parameter.ReferencedAssemblies.Add("System.XML.dll");
                    parameter.ReferencedAssemblies.Add("System.Web.Services.dll");
                    parameter.ReferencedAssemblies.Add("System.Data.dll");

                    CompilerResults result = provider.CompileAssemblyFromDom(parameter, unit);

                    // 5. 使用 WebClient 下載 WSDL 信息。
                    if (!result.Errors.HasErrors)
                    {
                        Assembly asm = result.CompiledAssembly;
                        Type t = asm.GetType("MailService"); // 如果在前面為代理類添加了命名空間，此處需要將命名空間添加到類型前面。

                        object o = Activator.CreateInstance(t);
                        MethodInfo method = t.GetMethod("simple_mail");

                        object[] tArgs = new object[6];//參數列表 
                        tArgs[0] = strFROM;
                        tArgs[1] = strTO;
                        tArgs[2] = strCC;
                        tArgs[3] = strBCC;
                        tArgs[4] = strSubject;
                        tArgs[5] = strBody;

                        method.Invoke(o, tArgs);
                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("發送信件(common.sendMail)發生錯誤，錯誤訊息：" + ex.Message);
            }
        }
        else
        {
            return true;
        }
    }
    #endregion

    #region 檢查工號格式
    /// <summary>
    /// 檢查工號格式(錯誤時回傳訊息；否則，回傳空值。)
    /// </summary>
    /// <param name="strIn">檢查工號</param>
    /// <returns>錯誤時回傳訊息；否則，回傳空值。</returns>
    /// <remarks></remarks>
    public string IsValidEmpno(string strIn)
    {
        string msg = "";
        if (strIn.Length != 6)
        {
            msg = "請輸入6位數工號!";
        }
        else
        {
            if (Regex.IsMatch(strIn, @"([A-Z]|[a-z]|[0-9])\d{5}"))
            {
                msg = "";
            }
            else
            {
                msg = "請輸入正確格式工號!";
            }
        }
        return msg;
    }
    #endregion

    #region 檢查e-mail格式
    /// <summary>
    /// 檢查e-mail格式
    /// </summary>
    /// <param name="strIn">信箱地址</param>
    /// <returns>bool</returns>
    /// <remarks></remarks>
    public bool IsValidEmail(string strIn)
    {
        // Return true if strIn is in valid e-mail format.只能輸入這些字元
        return Regex.IsMatch(strIn, @"^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$");
    }
    #endregion

    #region 檢查有效日期
    /// <summary>
    /// 檢查有效日期(錯誤時回傳訊息；否則，回傳空值。)
    /// </summary>
    /// <param name="inputDate">日期</param>
    /// <returns>錯誤時回傳訊息；否則，回傳空值。</returns>
    public string IsDateFormat(string inputDate)
    {
        try
        {
            string msg = "";
            if (inputDate != "")
            {
                if (inputDate.Length < 8 || inputDate.Length > 10)
                {
                    msg = "請輸入yyyy/MM/dd或yyyyMMdd的日期格式!";
                }
                else
                {
                    try
                    {
                        string[] DateTimeList = { "yyyy/M/d tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd HH:mm:ss", 
                                              "yyyy/MM/dd",
                                              "yyyyMMdd"
                                            };
                        DateTime dt = DateTime.ParseExact(inputDate, DateTimeList, CultureInfo.InvariantCulture, DateTimeStyles.AllowWhiteSpaces);
                    }
                    catch
                    {
                        msg = "請輸入有效日期!";
                    }
                }
            }

            return msg;

        }
        catch (Exception ex)
        {
            throw new Exception("檢查日期(common.IsDateFormat)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 檢查起迄有效日期
    /// <summary>
    /// 檢查起迄有效日期(錯誤時回傳訊息；否則，回傳空值。)
    /// </summary>
    /// <param name="inputSDate">開始日期</param>
    /// /// <param name="inputEDate">結束日期</param>
    /// <returns>錯誤時回傳訊息；否則，回傳空值。</returns>
    public string IsDateFormat(string inputSDate, string inputEDate)
    {
        try
        {
            string msg = "";
            DateTime sdt = new DateTime();
            DateTime edt = new DateTime();
            if (inputSDate != "")
            {
                if (inputSDate.Length < 8 || inputSDate.Length > 10)
                {
                    msg = "開始日期，請輸入yyyy/MM/dd或yyyyMMdd的日期格式!\n";
                }
                else
                {
                    try
                    {
                        string[] DateTimeList = { "yyyy/M/d tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd HH:mm:ss", 
                                              "yyyy/MM/dd",
                                              "yyyyMMdd"
                                            };
                        sdt = DateTime.ParseExact(inputSDate, DateTimeList, CultureInfo.InvariantCulture, DateTimeStyles.AllowWhiteSpaces);
                    }
                    catch
                    {
                        msg = "開始日期，請輸入有效日期!\n";
                    }
                }
            }

            if (inputEDate != "")
            {
                if (inputEDate.Length < 8 || inputEDate.Length > 10)
                {
                    msg += "結束日期，請輸入yyyy/MM/dd或yyyyMMdd的日期格式!";
                }
                else
                {
                    try
                    {
                        string[] DateTimeList = { "yyyy/M/d tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd tt hh:mm:ss", 
                                              "yyyy/MM/dd HH:mm:ss", 
                                              "yyyy/MM/dd",
                                              "yyyyMMdd"
                                            };
                        edt = DateTime.ParseExact(inputEDate, DateTimeList, CultureInfo.InvariantCulture, DateTimeStyles.AllowWhiteSpaces);
                    }
                    catch
                    {
                        msg += "結束日期，請輸入有效日期!";
                    }
                }
            }

            if (inputSDate != "" && inputEDate != "" && msg == "")
            {
                if (edt > sdt)
                {
                    msg = "開始時間不可大於或等於結束時間!";
                }
            }

            return msg;

        }
        catch (Exception ex)
        {
            throw new Exception("檢查起迄有效日期(common.IsDateFormat)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 檢查起迄有效日期時間
    /// <summary>
    /// 檢查起迄有效日期時間(錯誤時回傳訊息；否則，回傳空值。)
    /// </summary>
    /// <param name="inputSDateTime">開始日期時間</param>
    /// /// <param name="inputEDateTime">結束日期時間</param>
    /// <returns>錯誤時回傳訊息；否則，回傳空值。</returns>
    public string IsDateFormat(DateTime inputSDateTime, DateTime inputEDateTime)
    {
        try
        {
            string msg = "";
            if (inputEDateTime > inputSDateTime)
            {
                msg = "開始時間不可大於或等於結束時間!";
            }

            return msg;
        }
        catch (Exception ex)
        {
            throw new Exception("檢查起迄有效日期時間(common.IsDateFormat)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

    #region 檢查是否為數值
    /// <summary>
    /// 檢查是否為數值
    /// </summary>
    /// <param name="str_Integer">字串</param>
    public bool IsInteger(string str_Integer)
    {
        if (str_Integer.Length == 0)
        {
            return false;
        }
        else
        {
            //return Regex.IsMatch(str_Integer, @"^\d+$");
            return Regex.IsMatch(str_Integer, @"^[-]?\d+\d*$");
        }
    }
    #endregion

    #region 檢查是否為浮點數
    /// <summary>
    /// 檢查是否為浮點數
    /// </summary>
    /// <param name="str_Integer">字串</param>
    public bool IsDecimal(string str_IsDecimal)
    {


        bool flag;
        if (str_IsDecimal.Length == 0)
        {
            flag = false;
        }
        else
        {
            flag = Regex.IsMatch(str_IsDecimal, @"^([-+]?[0-9]*\.?[0-9]+)$");
        }
        return flag;
    }
    #endregion

    #region 檢查有效身份證字號
    /// <summary>
    /// 檢查有效身份證字號
    /// </summary>
    /// <param name="strIn"></param>
    /// <returns></returns>
    public bool IsIdNumber(string strIn)
    {
        return Regex.IsMatch(strIn, @"([A-Z]|[a-z])\d{9}");
    }
    #endregion

    #region 檢查是否為科學符號
    /// <summary>
    /// 檢查是否為科學符號
    /// </summary>
    /// <param name="str_IsScientific"></param>
    /// <returns></returns>
    public bool IsScientific(string str_IsScientific)
    {
        if (str_IsScientific.Length == 0)
        {
            return false;
        }
        else
        {
            string left = str_IsScientific.Substring(0, str_IsScientific.IndexOf("E"));
            string right = "";
            if (str_IsScientific.IndexOf("-") != -1)
            {
                right = str_IsScientific.Substring(str_IsScientific.IndexOf("-") + 1, str_IsScientific.Length - str_IsScientific.IndexOf("-") - 1);

                if (!IsDecimal(left) || !IsInteger(right) || str_IsScientific.IndexOf("E") == -1 || str_IsScientific.IndexOf("-") == -1)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                right = str_IsScientific.Substring(str_IsScientific.IndexOf("+") + 1, str_IsScientific.Length - str_IsScientific.IndexOf("+") - 1);

                if (!IsDecimal(left) || !IsInteger(right) || str_IsScientific.IndexOf("E") == -1 || str_IsScientific.IndexOf("+") == -1)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }


        }
    }
    #endregion

    #region 檢查GUID格式是否為正確
    /// <summary>
    /// 檢查GUID格式是否為正確
    /// </summary>
    /// <param name="cal_id"></param>
    /// <returns></returns>
    public bool Check_calendarID(string cal_id)
    {
        Match match = null;
        Regex regx = new Regex(@"^\w{8}\-\w{4}\-\w{4}\-\w{4}\-\w{12}$");
        match = regx.Match(cal_id.Trim());
        if (match.Success)
            return true;
        else
            return false;

    }
    #endregion

    #region 移除HTML Tags
    /// <summary>
    /// 移除HTML Tags
    /// </summary>
    /// <param name="HtmlSource"></param>
    /// <returns></returns>
    public static string RemoveHTMLTags(string HtmlSource)
    {
        string PureText = System.Text.RegularExpressions.Regex.Replace(Regex.Replace(HtmlSource.ToString(), "(?is)<.+?>", "").ToString(), "(?is)&.+?;", "").ToString();
        return PureText;
    }
    #endregion

    #region 檢查是否有特殊符號
    /// <summary>
    /// 檢查是否有特殊符號
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public bool IsContainSymbol(string str)
    {
        return Regex.IsMatch(str, @"([\#\$\%\^\&\*\+\?\|\']|--)");
    }
    #endregion

    #region 資料加密解密
    /// <summary>
    /// 資料加密解密
    /// </summary>
    public class Security
    {
        string _QueryStringKey = "abcdefgh"; //URL傳輸參數加密Key 
        string _PassWordKey = "hgfedcba"; //PassWord加密Key 

        public Security()
        {
            // 
            // TODO: 在此處添加構造函數邏輯 
            // 
        }

        /// <summary>
        /// 加密URL傳輸的字符串
        /// </summary>
        /// <param name="QueryString"></param>
        /// <returns></returns>
        public string EncryptQueryString(string QueryString)
        {
            return Encrypt(QueryString, _QueryStringKey);
        }

        /// <summary>
        /// 解密URL傳輸的字符串
        /// </summary>
        /// <param name="QueryString"></param>
        /// <returns></returns>
        public string DecryptQueryString(string QueryString)
        {
            return Decrypt(QueryString, _QueryStringKey);
        }

        /// <summary>
        /// 加密帳號口令 
        /// </summary>
        /// <param name="PassWord"></param>
        /// <returns></returns>
        public string EncryptPassWord(string PassWord)
        {
            return Encrypt(PassWord, _PassWordKey);
        }

        /// <summary>
        /// 解密帳號口令 
        /// </summary>
        /// <param name="PassWord"></param>
        /// <returns></returns>
        public string DecryptPassWord(string PassWord)
        {
            return Decrypt(PassWord, _PassWordKey);
        }

        /// <summary>
        /// DEC 加密過程 
        /// </summary>
        /// <param name="pToEncrypt"></param>
        /// <param name="sKey"></param>
        /// <returns></returns>
        public string Encrypt(string pToEncrypt, string sKey)
        {
            DESCryptoServiceProvider des = new DESCryptoServiceProvider(); //把字符串放到byte數組中 

            byte[] inputByteArray = Encoding.Default.GetBytes(pToEncrypt);
            //byte[] inputByteArray=Encoding.Unicode.GetBytes(pToEncrypt); 

            des.Key = ASCIIEncoding.ASCII.GetBytes(sKey); //建立加密對象的密鑰和偏移量 
            des.IV = ASCIIEncoding.ASCII.GetBytes(sKey); //原文使用ASCIIEncoding.ASCII方法的GetBytes方法 
            MemoryStream ms = new MemoryStream(); //使得輸入密碼必須輸入英文文本 
            CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write);

            cs.Write(inputByteArray, 0, inputByteArray.Length);
            cs.FlushFinalBlock();

            StringBuilder ret = new StringBuilder();
            foreach (byte b in ms.ToArray())
            {
                ret.AppendFormat("{0:X2}", b);
            }
            ret.ToString();
            return ret.ToString();
        }

        /// <summary>
        /// DEC 解密過程
        /// </summary>
        /// <param name="pToDecrypt"></param>
        /// <param name="sKey"></param>
        /// <returns></returns>
        public string Decrypt(string pToDecrypt, string sKey)
        {
            DESCryptoServiceProvider des = new DESCryptoServiceProvider();

            byte[] inputByteArray = new byte[pToDecrypt.Length / 2];
            for (int x = 0; x < pToDecrypt.Length / 2; x++)
            {
                int i = (Convert.ToInt32(pToDecrypt.Substring(x * 2, 2), 16));
                inputByteArray[x] = (byte)i;
            }

            des.Key = ASCIIEncoding.ASCII.GetBytes(sKey); //建立加密對象的密鑰和偏移量，此值重要，不能修改 
            des.IV = ASCIIEncoding.ASCII.GetBytes(sKey);
            MemoryStream ms = new MemoryStream();
            CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write);

            cs.Write(inputByteArray, 0, inputByteArray.Length);
            cs.FlushFinalBlock();

            StringBuilder ret = new StringBuilder(); //建立StringBuild對象，CreateDecrypt使用的是流對象，必須把解密後的文本變成流對像 

            return System.Text.Encoding.Default.GetString(ms.ToArray());
        }

        /// <summary>
        /// 檢查己加密的字符串是否與原文相同
        /// </summary>
        /// <param name="EnString"></param>
        /// <param name="FoString"></param>
        /// <param name="Mode"></param>
        /// <returns></returns>
        public bool ValidateString(string EnString, string FoString, int Mode)
        {
            switch (Mode)
            {
                default:
                case 1:
                    if (Decrypt(EnString, _QueryStringKey) == FoString.ToString())
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                case 2:
                    if (Decrypt(EnString, _PassWordKey) == FoString.ToString())
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
            }
        }
    }
    #endregion

    #region 彈出訊息
    /// <summary>
    /// 彈出訊息(如果使用UpdatePanel時，須將button加入PostBackTrigger才有效)
    /// </summary>
    /// <param name="strIn">要顯示的訊息</param>
    public void alertMessage(UpdatePanel ID, string strIn)
    {
        ClientScript.RegisterStartupScript(ID.GetType(), "ErrorMessage", "<script>alert('" + strIn + "')</script>");
    }

    /// <summary>
    /// 彈出訊息
    /// </summary>
    /// <param name="strIn">要顯示的訊息</param>
    public void alertMessage(string strIn)
    {
        ClientScript.RegisterStartupScript(this.GetType(), "ErrorMessage2", "<script>alert('" + strIn + "')</script>");
    }

    /// <summary>
    /// 彈出訊息
    /// </summary>
    /// <param name="strIn">要顯示的訊息</param>
    public void alertManagerMessage(UpdatePanel updatePanel, string strIn)
    {
        string script = "<script language='javascript'>window.alert('" + strIn + "');</script>";
        ScriptManager.RegisterClientScriptBlock(updatePanel, this.GetType(), "送出", script, false);
    }
    #endregion

    #region 紀錄錯誤訊息至FAQ系統資料庫
    /// <summary>
    /// 紀錄錯誤訊息至FAQ系統資料庫
    /// </summary>
    /// <param name="sysCode">系統代號(洽詢FAQ提供)</param>
    /// <param name="userEmpno">使用者工號</param>
    /// <param name="errMsg">錯誤訊息</param>
    /// <param name="testOrOnline">使用位於測區或上線區：off->測試 on->上線</param>
    /// <param name="ip">IP位址</param>
    private void saveErrorMsgToFAQ(string testOrOnline, string sysCode, string userEmpno, string errMsg, string ip)
    {
        try
        {
            // 1. 使用 WebClient 下載 WSDL 信息。
            WebClient web = new WebClient();
            Stream stream = web.OpenRead("http://itriap5.itri.org.tw/ws_sf000/FAQWebService.asmx?WSDL");

            // 2. 創建和格式化 WSDL 文檔。
            ServiceDescription description = ServiceDescription.Read(stream);

            // 3. 創建客戶端代理代理類。
            ServiceDescriptionImporter importer = new ServiceDescriptionImporter();

            importer.ProtocolName = "Soap"; // 指定訪問協議。
            importer.Style = ServiceDescriptionImportStyle.Client; // 生成客户端代理。
            importer.CodeGenerationOptions = CodeGenerationOptions.GenerateProperties | CodeGenerationOptions.GenerateNewAsync;

            importer.AddServiceDescription(description, null, null); // 添加 WSDL 文檔。

            // 4. 使用 CodeDom 编译客户端代理类。
            CodeNamespace nmspace = new CodeNamespace(); // 為代理類添加命名空間，缺省為全局空間。
            CodeCompileUnit unit = new CodeCompileUnit();
            unit.Namespaces.Add(nmspace);

            ServiceDescriptionImportWarnings warning = importer.Import(nmspace, unit);
            CodeDomProvider provider = CodeDomProvider.CreateProvider("CSharp");

            CompilerParameters parameter = new CompilerParameters();
            parameter.GenerateExecutable = false;
            parameter.GenerateInMemory = true;
            parameter.ReferencedAssemblies.Add("System.dll");
            parameter.ReferencedAssemblies.Add("System.XML.dll");
            parameter.ReferencedAssemblies.Add("System.Web.Services.dll");
            parameter.ReferencedAssemblies.Add("System.Data.dll");

            CompilerResults result = provider.CompileAssemblyFromDom(parameter, unit);

            // 5. 使用 WebClient 下載 WSDL 信息。
            if (!result.Errors.HasErrors)
            {
                Assembly asm = result.CompiledAssembly;
                Type t = asm.GetType("FAQWebService"); // 如果在前面為代理類添加了命名空間，此處需要將命名空間添加到類型前面。

                object o = Activator.CreateInstance(t);
                MethodInfo method = t.GetMethod("SaveErrorMsgToFAQ2");

                object[] tArgs = new object[5];//參數列表 
                tArgs[0] = testORonline;
                tArgs[1] = sysCode;
                tArgs[2] = userEmpno;
                tArgs[3] = errMsg;
                tArgs[4] = ip;

                method.Invoke(o, tArgs);
            }
        }
        catch (Exception ex)
        {
            throw new Exception("紀錄錯誤訊息至FAQ系統資料庫(common.saveErrorMsgToFAQ)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion



    #region 查FAQ各系統相關資訊，如系統名稱、收件人
    /// <summary>
    /// 查FAQ各系統相關資訊，如系統名稱、收件人
    /// </summary>
    /// <returns>object</returns>
    private object GetSystemDataOfFAQ()
    {
        try
        {
            // 1. 使用 WebClient 下載 WSDL 信息。
            WebClient web = new WebClient();
            Stream stream = web.OpenRead("http://itriap5.itri.org.tw/ws_sf000/FAQWebService.asmx?WSDL");

            // 2. 創建和格式化 WSDL 文檔。
            ServiceDescription description = ServiceDescription.Read(stream);

            // 3. 創建客戶端代理代理類。
            ServiceDescriptionImporter importer = new ServiceDescriptionImporter();

            importer.ProtocolName = "Soap"; // 指定訪問協議。
            importer.Style = ServiceDescriptionImportStyle.Client; // 生成客户端代理。
            importer.CodeGenerationOptions = CodeGenerationOptions.GenerateProperties | CodeGenerationOptions.GenerateNewAsync;

            importer.AddServiceDescription(description, null, null); // 添加 WSDL 文檔。

            // 4. 使用 CodeDom 编译客户端代理类。
            CodeNamespace nmspace = new CodeNamespace(); // 為代理類添加命名空間，缺省為全局空間。
            CodeCompileUnit unit = new CodeCompileUnit();
            unit.Namespaces.Add(nmspace);

            ServiceDescriptionImportWarnings warning = importer.Import(nmspace, unit);
            CodeDomProvider provider = CodeDomProvider.CreateProvider("CSharp");

            CompilerParameters parameter = new CompilerParameters();
            parameter.GenerateExecutable = false;
            parameter.GenerateInMemory = true;
            parameter.ReferencedAssemblies.Add("System.dll");
            parameter.ReferencedAssemblies.Add("System.XML.dll");
            parameter.ReferencedAssemblies.Add("System.Web.Services.dll");
            parameter.ReferencedAssemblies.Add("System.Data.dll");

            CompilerResults result = provider.CompileAssemblyFromDom(parameter, unit);

            // 5. 使用 WebClient 下載 WSDL 信息。
            //if (!result.Errors.HasErrors)
            //{
            Assembly asm = result.CompiledAssembly;
            Type t = asm.GetType("FAQWebService"); // 如果在前面為代理類添加了命名空間，此處需要將命名空間添加到類型前面。

            object o = Activator.CreateInstance(t);
            MethodInfo method = t.GetMethod("GetSystemDataOfFAQ");

            object[] tArgs = new object[2];//參數列表 
            tArgs[0] = testORonline;
            tArgs[1] = sysCode;

            return method.Invoke(o, tArgs);
            //}
        }
        catch (Exception ex)
        {
            throw new Exception("查FAQ各系統相關資訊(common.GetSystemDataOfFAQ)發生錯誤，錯誤訊息：" + ex.Message);
        }
    }
    #endregion

	#region 查FAQ系統的系統負責人、業務負責人、相關人員、主管
	/// <summary>
	/// 查FAQ系統的系統負責人、業務負責人、相關人員、主管
	/// </summary>
	/// <returns>object</returns>
	protected object GetSystemOwnerList()
	{
		try
		{
			// 1. 使用 WebClient 下載 WSDL 信息。
			WebClient web = new WebClient();
			Stream stream;
			stream = web.OpenRead("http://itriap5.itri.org.tw/ws_sf000/FAQWebService.asmx?WSDL");

			// 2. 創建和格式化 WSDL 文檔。
			ServiceDescription description = ServiceDescription.Read(stream);

			// 3. 創建客戶端代理代理類。
			ServiceDescriptionImporter importer = new ServiceDescriptionImporter();

			importer.ProtocolName = "Soap"; // 指定訪問協議。
			importer.Style = ServiceDescriptionImportStyle.Client; // 生成客户端代理。
			importer.CodeGenerationOptions = CodeGenerationOptions.GenerateProperties | CodeGenerationOptions.GenerateNewAsync;

			importer.AddServiceDescription(description, null, null); // 添加 WSDL 文檔。

			// 4. 使用 CodeDom 编译客户端代理类。
			CodeNamespace nmspace = new CodeNamespace(); // 為代理類添加命名空間，缺省為全局空間。
			CodeCompileUnit unit = new CodeCompileUnit();
			unit.Namespaces.Add(nmspace);

			ServiceDescriptionImportWarnings warning = importer.Import(nmspace, unit);
			CodeDomProvider provider = CodeDomProvider.CreateProvider("CSharp");

			CompilerParameters parameter = new CompilerParameters();
			parameter.GenerateExecutable = false;
			parameter.GenerateInMemory = true;
			parameter.ReferencedAssemblies.Add("System.dll");
			parameter.ReferencedAssemblies.Add("System.XML.dll");
			parameter.ReferencedAssemblies.Add("System.Web.Services.dll");
			parameter.ReferencedAssemblies.Add("System.Data.dll");

			CompilerResults result = provider.CompileAssemblyFromDom(parameter, unit);

			// 5. 使用 WebClient 下載 WSDL 信息。
			//if (!result.Errors.HasErrors)
			//{
			Assembly asm = result.CompiledAssembly;
			Type t = asm.GetType("FAQWebService"); // 如果在前面為代理類添加了命名空間，此處需要將命名空間添加到類型前面。

			object o = Activator.CreateInstance(t);
			MethodInfo method = t.GetMethod("GetSystemOwnerList");

			object[] tArgs = new object[2];//參數列表 
			tArgs[0] = testORonline;
			tArgs[1] = sysCode;

			return method.Invoke(o, tArgs);
			//}
		}
		catch (Exception ex)
		{
			throw new Exception("查FAQ系統的系統負責人、業務負責人、相關人員、主管(GetSystemOwnerList)發生錯誤，錯誤訊息：" + ex.Message);
		}
	}
	#endregion

    #region 取得使用者慣用語系值(by各系統DD)
    /// <summary>
    /// 取得使用者慣用語系值
    /// </summary>
    /// <param name="sql">查詢各系統DD的語言table的SQL語法</param>
    private void GetProfileLanguage(string sql)
    {
        DataSet ds = new DataSet();

        SqlCommand sqlCmd = new SqlCommand(sql);
        sqlCmd.CommandType = CommandType.Text;
        sqlCmd.Parameters.AddWithValue("@empno", sso.empNo);

        ds = runParaCmdDS(sqlCmd);

        String selectedLanguage = "";
        if (ds.Tables[0].Rows.Count > 0)
        {
            selectedLanguage = ds.Tables[0].Rows[0][0].ToString();
            if (selectedLanguage == "") selectedLanguage = "zh-TW";
        }
        else
        {
            selectedLanguage = "zh-TW";
        }

        Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(selectedLanguage);
        Thread.CurrentThread.CurrentUICulture = new CultureInfo(selectedLanguage);

        base.InitializeCulture();
    }
    #endregion

    #region 執行資料庫的方法(由Data.cs整入)(含特殊字元處理：半形轉全形處理)
    #region 轉換成Int32
    /// <summary>
    /// 轉換成Int32
    /// </summary>
    /// <param name="str_Integer">要轉換的數字字串</param>
    private static Int32 ToInt32(string str_Integer)
    {
        return Convert.ToInt32(str_Integer);
    }
    #endregion

    #region 宣告參數型態
    /// <summary>
    /// 宣告變數型態
    /// </summary>
    /// <param name="sqlPara">SqlParameter</param>
    /// <param name="dr">DataRow</param>
    private static void setPara(SqlParameter sqlPara, DataRow dr)
    {
        string strLocation = "Data::SetPara";
        sqlPara.ParameterName = dr["COLUMN_NAME"].ToString();
        switch (dr["TYPE_NAME"].ToString())
        {
            case "bigint":
                sqlPara.SqlDbType = SqlDbType.BigInt;
                break;
            case "binary":
                sqlPara.SqlDbType = SqlDbType.Binary;
                break;
            case "char":
                sqlPara.SqlDbType = SqlDbType.Char;
                sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                break;
            case "datetime":
                sqlPara.SqlDbType = SqlDbType.DateTime;
                break;
            case "decimal":
                sqlPara.SqlDbType = SqlDbType.Decimal;
                break;
            case "float":
                sqlPara.SqlDbType = SqlDbType.Float;
                break;
            case "int":
                sqlPara.SqlDbType = SqlDbType.Int;
                break;
            case "nchar":
                sqlPara.SqlDbType = SqlDbType.NChar;
                sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                break;
            case "ntext":
                sqlPara.SqlDbType = SqlDbType.NText;
                break;
            case "nvarchar":
                sqlPara.SqlDbType = SqlDbType.NVarChar;
                sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                break;
            case "smallint":
                sqlPara.SqlDbType = SqlDbType.SmallInt;
                break;
            case "text":
                sqlPara.SqlDbType = SqlDbType.Text;
                break;
            case "tinyint":
                sqlPara.SqlDbType = SqlDbType.TinyInt;
                break;
            case "varbinary":
                sqlPara.SqlDbType = SqlDbType.VarBinary;
                break;
            case "varchar":
                sqlPara.SqlDbType = SqlDbType.VarChar;
                sqlPara.Size = ToInt32(dr["LENGTH"].ToString());
                break;
            default:
                Exception exErr = new Exception("系統未設定此型別 " + dr["info_name"] + ", 請通知系統人員");
                exErr.Source = strLocation;
                throw exErr;
        }

        if (ToInt32(dr["COLUMN_TYPE"].ToString()) == 2)
            sqlPara.Direction = ParameterDirection.Output;
    }
    #endregion

    #region 利用此函式來執行SQL statement, 並回傳單一值
    /// <summary>
    /// 利用此函式來執行SQL statement, 並回傳單一值		 
    /// </summary>
    /// <param name="sqlCmd">SqlCommand</param>
    /// <returns>return object</returns>
    public Object runScalar(SqlCommand sqlCmd)
    {
        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref sqlCmd);

        string strLocation = "Data::RunScalar";
        Object obj = null;
        sqlCmd.Connection = ConnString;
        sqlCmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();

        try
        {
            ConnString.Open();
            obj = sqlCmd.ExecuteScalar();
        }
        catch (Exception err)
        {
            err.Source = strLocation;
            throw err;
        }
        finally
        {
            ConnString.Close();
        }

        return obj;
    }
    #endregion

    #region 利用此函式來執行stored procedure, 並回傳單一值
    /// <summary>
    /// 利用此函式來執行stored procedure, 並回傳單一值		 
    /// </summary>		
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <returns>return object</returns>
    public object runScalar(string pSPName)
    {
        string strLocation = "Data::RunScalar()";
        object objResult = null;
        SqlCommand cmd = new SqlCommand();
        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;
        // execute SQL 

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        try
        {
            ConnString.Open();
            objResult = cmd.ExecuteScalar();
        }
        catch (Exception err)
        {
            err.Source = strLocation;
            throw err;
        }
        finally
        {
            ConnString.Close();
        }

        return objResult;
    }
    #endregion

    #region 利用此函式來執行stored procedure, 並回傳單一值
    /// <summary>
    /// 利用此函式來執行stored procedure, 並回傳單一值		 
    /// </summary>		
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">參數</param>
    /// <returns>return object</returns>
    public object runScalar(string pSPName, Object[] pParas)
    {
        string strLocation = "Data::RunScalar()";
        object objResult = null;

        SqlCommand cmd = new SqlCommand();
        SqlParameter[] paras;

        //設定Parameters
        paras = getParameters(pSPName, pParas);

        if (paras.Length != pParas.Length)
            throw new Exception("參數個數不一致!!");

        // Add Parameter into SqlCommand.SqlParameter
        for (int i = 0; i < paras.Length; i++)
        {
            cmd.Parameters.Add(paras[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        try
        {
            ConnString.Open();
            objResult = cmd.ExecuteScalar();
        }
        catch (Exception err)
        {
            err.Source = strLocation;
            throw err;
        }
        finally
        {
            ConnString.Close();
        }

        // Release Resource 
        if (paras != null)
            cmd.Parameters.Clear();

        ConnString.Close();

        return objResult;
    }
    #endregion

    #region 利用此函式來執行SQL statement,回傳DataView
    /// <summary>
    /// 利用此函式來執行SQL statement		 
    /// </summary>
    /// <param name="sqlCmd">SQL statement</param>
    /// <returns>return DataView</returns>
    public DataView runParaCmd(SqlCommand sqlCmd)
    {
        sqlCmd.Connection = ConnString;
        sqlCmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref sqlCmd);

        SqlDataAdapter cmdSQL = new SqlDataAdapter(sqlCmd);
        DataSet ds = new DataSet();
        cmdSQL.Fill(ds, "myTable");

        ConnString.Close();

        return ds.Tables["myTable"].DefaultView;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataView
    /// <summary>
    /// 利用此函式來執行stored procedure		 
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">參數</param>
    /// <returns>return DataView</returns>
    public DataView runSp(string pSPName, SqlParameter[] pParas)
    {
        SqlCommand cmd = new SqlCommand();

        // Add Parameter into SqlCommand.SqlParameter
        for (int i = 0; i < pParas.Length; i++)
        {
            cmd.Parameters.Add(pParas[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet myds = new DataSet();
        cmdSQL.Fill(myds, "myTable");

        // Release Resource 
        if (pParas != null)
            cmd.Parameters.Clear();

        ConnString.Close();

        return myds.Tables["myTable"].DefaultView;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataView
    /// <summary>
    /// 利用此函式來執行stored procedure		 
    /// </summary>		
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">參數</param>
    /// <returns>return DataView</returns>
    public DataView runSp(string pSPName, Object[] pParas)
    {
        string strLocation = "Data::RunSp";
        SqlCommand cmd = new SqlCommand();
        SqlParameter[] paras;

        //to get Parameters
        paras = getParameters(pSPName, pParas);

        //若參數個數不一致, 則丟出Exception
        if (paras.Length != pParas.Length)
        {
            Exception exErr = new Exception("參數個數不一致!!");
            exErr.Source = strLocation;
            throw exErr;
        }

        // Add Parameter into SqlCommand.SqlParameter		
        for (int i = 0; i < paras.Length; i++)
        {
            cmd.Parameters.Add(paras[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet myds = new DataSet();
        cmdSQL.Fill(myds, "myTable");

        // Release Resource 
        if (paras != null)
            cmd.Parameters.Clear();

        ConnString.Close();

        return myds.Tables["myTable"].DefaultView;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataView
    /// <summary>
    /// 利用此函式來執行stored procedure		 
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <returns>return DataView</returns>
    public DataView runSp(string pSPName)
    {
        SqlCommand cmd = new SqlCommand();

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet myds = new DataSet();
        cmdSQL.Fill(myds, "myTable");

        ConnString.Close();

        return myds.Tables["myTable"].DefaultView;
    }
    #endregion

    #region 利用此函式來執行SQL statement,回傳DataSet
    /// <summary>
    /// 利用此函式來執行SQL statement,回傳DataSet	 
    /// </summary>
    /// <param name="sqlCmd">SQL statement</param>
    /// <returns>return DataSet</returns>
    public DataSet runParaCmdDS(SqlCommand sqlCmd)
    {
        sqlCmd.Connection = ConnString;
        sqlCmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref sqlCmd);

        SqlDataAdapter cmdSQL = new SqlDataAdapter(sqlCmd);
        DataSet ds = new DataSet();
        cmdSQL.Fill(ds, "myTable");

        ConnString.Close();

        return ds;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataSet
    /// <summary>
    /// 利用此函式來執行stored procedure,回傳DataSet		 
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">參數</param>
    /// <returns>return DataSet</returns>
    public DataSet runSpDS(string pSPName, SqlParameter[] pParas)
    {
        SqlCommand cmd = new SqlCommand();

        // Add Parameter into SqlCommand.SqlParameter
        for (int i = 0; i < pParas.Length; i++)
        {
            cmd.Parameters.Add(pParas[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet ds = new DataSet();
        cmdSQL.Fill(ds, "myTable");

        // Release Resource 
        if (pParas != null)
            cmd.Parameters.Clear();

        ConnString.Close();

        return ds;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataSet
    /// <summary>
    /// 利用此函式來執行stored procedure,回傳DataSet		 
    /// </summary>		
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">參數</param>
    /// <returns>return DataSet</returns>
    public DataSet runSpDS(string pSPName, Object[] pParas)
    {
        string strLocation = "Data::RunSp";
        SqlCommand cmd = new SqlCommand();
        SqlParameter[] paras;

        //to get Parameters
        paras = getParameters(pSPName, pParas);

        //若參數個數不一致, 則丟出Exception
        if (paras.Length != pParas.Length)
        {
            Exception exErr = new Exception("參數個數不一致!!");
            exErr.Source = strLocation;
            throw exErr;
        }

        // Add Parameter into SqlCommand.SqlParameter		
        for (int i = 0; i < paras.Length; i++)
        {
            cmd.Parameters.Add(paras[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet ds = new DataSet();
        cmdSQL.Fill(ds, "myTable");

        // Release Resource 
        if (paras != null)
            cmd.Parameters.Clear();

        ConnString.Close();

        return ds;
    }
    #endregion

    #region 利用此函式來執行stored procedure,回傳DataSet
    /// <summary>
    /// 利用此函式來執行stored procedure,回傳DataSet		 
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <returns>return DataSet</returns>
    public DataSet runSpDS(string pSPName)
    {
        SqlCommand cmd = new SqlCommand();

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL and return a dataview
        SqlDataAdapter cmdSQL = new SqlDataAdapter(cmd);
        DataSet ds = new DataSet();
        cmdSQL.Fill(ds, "myTable");

        ConnString.Close();

        return ds;
    }
    #endregion

    #region 利用此函式來執行SQL statement(無傳回值)
    /// <summary>
    /// 利用此函式來執行SQL statement(無傳回值)		 
    /// </summary>
    /// <param name="sqlCmd">SQL statement</param>
    public void runParaCmd1(SqlCommand sqlCmd)
    {
        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref sqlCmd);

        sqlCmd.Connection = ConnString;
        sqlCmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        ConnString.Open();
        sqlCmd.ExecuteNonQuery();
        ConnString.Close();
    }

    #endregion

    #region 利用此函式來執行stored procedure(無傳回值)
    /// <summary>
    /// 利用此函式來執行stored procedure(無回傳值)		 
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pPara">參數</param>
    public void runSp1(string pSPName, SqlParameter[] pParas)
    {
        SqlCommand cmd = new SqlCommand();

        // Add Parameter into SqlCommand.SqlParameter
        for (int i = 0; i < pParas.Length; i++)
        {
            cmd.Parameters.Add(pParas[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL 
        ConnString.Open();

        cmd.ExecuteNonQuery();

        ConnString.Close();

        // Release Resource 
        if (pParas != null)
            cmd.Parameters.Clear();
    }
    #endregion

    #region 利用此函式來執行stored procedure(無回傳值)
    /// <summary>
    /// 利用此函式來執行stored procedure(無回傳值)
    /// </summary>		
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">Object[]參數</param>
    public void runSp1(string pSPName, Object[] pParas)
    {
        string strLocation = "Data::RunSp1";
        SqlCommand cmd = new SqlCommand();
        SqlParameter[] paras;

        //設定Parameters
        paras = getParameters(pSPName, pParas);

        if (paras.Length != pParas.Length)
        {
            Exception exErr = new Exception("參數個數不一致!!");
            exErr.Source = strLocation;
            throw exErr;
        }

        // Add Parameter into SqlCommand.SqlParameter
        for (int i = 0; i < paras.Length; i++)
        {
            cmd.Parameters.Add(paras[i]);
        }

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL 
        ConnString.Open();

        cmd.ExecuteNonQuery();

        ConnString.Close();

        // Release Resource 
        if (paras != null)
            cmd.Parameters.Clear();
    }
    #endregion

    #region 利用此函式來執行stored procedure(無回傳值)
    /// <summary>
    /// 利用此函式來執行stored procedure(無回傳值)
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    public void runSp1(string pSPName)
    {
        SqlCommand cmd = new SqlCommand();

        // set properties of SqlCommand
        cmd.Connection = ConnString;
        cmd.CommandTimeout = GetSQLCommandTimeOutSecondsInWebConfig();
        cmd.CommandText = pSPName;
        cmd.CommandType = CommandType.StoredProcedure;

        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);

        // execute SQL 
        ConnString.Open();

        cmd.ExecuteNonQuery();

        ConnString.Close();
    }
    #endregion

    #region 利用此函式來取得Stored Procedured的參數
    /// <summary>
    /// 利用此函式來取得Stored Procedured的參數
    /// </summary>
    /// <param name="pSPName">Stored Procedure名稱</param>
    /// <param name="pParas">Parameter Values</param>
    /// <returns>return SqlParameter[]</returns>	
    public SqlParameter[] getParameters(string pSPName, object[] pParas)
    {
        string strLocation = "Data::GetParameters";
        SqlParameter[] Paras = new SqlParameter[pParas.Length];
        DataView dvMain;

        SqlParameter[] myParas = new SqlParameter[1];
        myParas[0] = new SqlParameter("@procedure_name", SqlDbType.NVarChar, 128);
        myParas[0].Value = pSPName;

        dvMain = runSp("sp_sproc_columns", myParas);

        //判斷parameters的參數個數是否正確
        if ((dvMain.Count - 1) != pParas.Length)
        {
            Exception exErr = new Exception("參數個數不一致!!");
            exErr.Source = strLocation;
            throw exErr;
        }

        for (int i = 0; i < pParas.Length; i++)
        {
            Paras[i] = new SqlParameter();
            setPara(Paras[i], dvMain.Table.Rows[i + 1]);
            Paras[i].Value = pParas[i];
        }

        //Release Resource
        dvMain = null;
        myParas = null;

        return Paras;
    }

    #endregion

    #region 獲取Web.Config裡，執行資料庫SQL指令，等待命令執行的時間(以秒為單位)。

    /// <summary> 
    /// 獲取Web.Config裡，執行資料庫SQL指令，等待命令執行的時間(以秒為單位)。
    /// </summary>
    public static int GetSQLCommandTimeOutSecondsInWebConfig()
    {
        if (System.Web.Configuration.WebConfigurationManager.AppSettings["SQLCommandTimeOut"] == null)
            return 30;

        return System.Convert.ToInt32(System.Web.Configuration.WebConfigurationManager.AppSettings["SQLCommandTimeOut"]);
    }

    #endregion
    #endregion

    #region 執行資料庫的方法(由SQLHelper.cs整入)(含特殊字元處理：半形轉全形處理)
    #region private utility methods & constructors
    // Since this class provides only static methods, make the default constructor private to prevent 
    // instances from being created with "new SqlHelper()"
    //	private SqlHelper() {}

    /// <summary>
    /// This method is used to attach array of SqlParameters to a SqlCommand.
    /// 
    /// This method will assign a value of DbNull to any parameter with a direction of
    /// InputOutput and a value of null.  
    /// 
    /// This behavior will prevent default values from being used, but
    /// this will be the less common case than an intended pure output parameter (derived as InputOutput)
    /// where the user provided no input value.
    /// </summary>
    /// <param name="command">The command to which the parameters will be added</param>
    /// <param name="commandParameters">An array of SqlParameters to be added to command</param>
    private static void AttachParameters(SqlCommand command, SqlParameter[] commandParameters)
    {
        if (command == null) throw new ArgumentNullException("command");
        if (commandParameters != null)
        {
            foreach (SqlParameter p in commandParameters)
            {
                if (p != null)
                {
                    // Check for derived output value with no value assigned
                    if ((p.Direction == ParameterDirection.InputOutput ||
                        p.Direction == ParameterDirection.Input) &&
                        (p.Value == null))
                    {
                        p.Value = DBNull.Value;
                    }
                    command.Parameters.Add(p);
                }
            }
        }
    }

    /// <summary>
    /// This method assigns dataRow column values to an array of SqlParameters
    /// </summary>
    /// <param name="commandParameters">Array of SqlParameters to be assigned values</param>
    /// <param name="dataRow">The dataRow used to hold the stored procedure's parameter values</param>
    private static void AssignParameterValues(SqlParameter[] commandParameters, DataRow dataRow)
    {
        if ((commandParameters == null) || (dataRow == null))
        {
            // Do nothing if we get no data
            return;
        }

        int i = 0;
        // Set the parameters values
        foreach (SqlParameter commandParameter in commandParameters)
        {
            // Check the parameter name
            if (commandParameter.ParameterName == null ||
                commandParameter.ParameterName.Length <= 1)
                throw new Exception(
                    string.Format(
                    "Please provide a valid parameter name on the parameter #{0}, the ParameterName property has the following value: '{1}'.",
                    i, commandParameter.ParameterName));
            if (dataRow.Table.Columns.IndexOf(commandParameter.ParameterName.Substring(1)) != -1)
                commandParameter.Value = dataRow[commandParameter.ParameterName.Substring(1)];
            i++;
        }
    }

    /// <summary>
    /// This method assigns an array of values to an array of SqlParameters
    /// </summary>
    /// <param name="commandParameters">Array of SqlParameters to be assigned values</param>
    /// <param name="parameterValues">Array of objects holding the values to be assigned</param>
    private static void AssignParameterValues(SqlParameter[] commandParameters, object[] parameterValues)
    {
        if ((commandParameters == null) || (parameterValues == null))
        {
            // Do nothing if we get no data
            return;
        }

        // We must have the same number of values as we pave parameters to put them in
        if (commandParameters.Length != parameterValues.Length)
        {
            throw new ArgumentException("Parameter count does not match Parameter Value count.");
        }

        // Iterate through the SqlParameters, assigning the values from the corresponding position in the 
        // value array
        for (int i = 0, j = commandParameters.Length; i < j; i++)
        {
            // If the current array value derives from IDbDataParameter, then assign its Value property
            if (parameterValues[i] is IDbDataParameter)
            {
                IDbDataParameter paramInstance = (IDbDataParameter)parameterValues[i];
                if (paramInstance.Value == null)
                {
                    commandParameters[i].Value = DBNull.Value;
                }
                else
                {
                    commandParameters[i].Value = paramInstance.Value;
                }
            }
            else if (parameterValues[i] == null)
            {
                commandParameters[i].Value = DBNull.Value;
            }
            else
            {
                commandParameters[i].Value = parameterValues[i];
            }
        }
    }

    /// <summary>
    /// This method opens (if necessary) and assigns a connection, transaction, command type and parameters 
    /// to the provided command
    /// </summary>
    /// <param name="command">The SqlCommand to be prepared</param>
    /// <param name="connection">A valid SqlConnection, on which to execute this command</param>
    /// <param name="transaction">A valid SqlTransaction, or 'null'</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">An array of SqlParameters to be associated with the command or 'null' if no parameters are required</param>
    /// <param name="mustCloseConnection"><c>true</c> if the connection was opened by the method, otherwose is false.</param>
    private static void PrepareCommand(SqlCommand command, SqlConnection connection, SqlTransaction transaction, CommandType commandType, string commandText, SqlParameter[] commandParameters, out bool mustCloseConnection)
    {
        if (command == null) throw new ArgumentNullException("command");
        if (commandText == null || commandText.Length == 0) throw new ArgumentNullException("commandText");

        // If the provided connection is not open, we will open it
        if (connection.State != ConnectionState.Open)
        {
            mustCloseConnection = true;
            connection.Open();
        }
        else
        {
            mustCloseConnection = false;
        }

        // Associate the connection with the command
        command.Connection = connection;

        // Set the command text (stored procedure name or SQL statement)
        command.CommandText = commandText;

        // If we were provided a transaction, assign it
        if (transaction != null)
        {
            if (transaction.Connection == null) throw new ArgumentException("The transaction was rollbacked or commited, please provide an open transaction.", "transaction");
            command.Transaction = transaction;
        }

        // Set the command type
        command.CommandType = commandType;

        // Attach the command parameters if they are provided
        if (commandParameters != null)
        {
            AttachParameters(command, commandParameters);
        }
        return;
    }

    #endregion private utility methods & constructors

    // Hashtable to store cached parameters
    private static Hashtable parmCache = Hashtable.Synchronized(new Hashtable());

    #region Execute a SqlCommand (that returns no resultset) against the database specified in the connection string
    /// <summary>
    /// Execute a SqlCommand (that returns no resultset) against the database specified in the connection string 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  int result = ExecuteNonQuery(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="connectionString">a valid connection string for a SqlConnection</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>an int representing the number of rows affected by the command</returns>
    public static int ExecuteNonQuery(string connString, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {
        SqlCommand cmd = new SqlCommand();
        using (SqlConnection conn = new SqlConnection(connString))
        {
            PrepareCommand(cmd, conn, null, cmdType, cmdText, cmdParms);
            //取代可能的攻擊字眼
            ReplaceMaliciousParametersSqlCommand(ref cmd);
            int val = cmd.ExecuteNonQuery();
            cmd.Parameters.Clear();

            conn.Close();

            return val;
        }
    }
    #endregion

    #region Execute a SqlCommand (that returns no resultset) against an existing database connection
    /// <summary>
    /// Execute a SqlCommand (that returns no resultset) against an existing database connection 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  int result = ExecuteNonQuery(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="conn">an existing database connection</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>an int representing the number of rows affected by the command</returns>
    public static int ExecuteNonQuery(SqlConnection conn, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {

        SqlCommand cmd = new SqlCommand();

        PrepareCommand(cmd, conn, null, cmdType, cmdText, cmdParms);
        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);
        int val = cmd.ExecuteNonQuery();
        cmd.Parameters.Clear();

        conn.Close();

        return val;
    }
    #endregion

    #region Execute a SqlCommand (that returns no resultset) using an existing SQL Transaction
    /// <summary>
    /// Execute a SqlCommand (that returns no resultset) using an existing SQL Transaction 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  int result = ExecuteNonQuery(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="trans">an existing sql transaction</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>an int representing the number of rows affected by the command</returns>
    public static int ExecuteNonQuery(SqlTransaction trans, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {
        SqlCommand cmd = new SqlCommand();
        PrepareCommand(cmd, trans.Connection, trans, cmdType, cmdText, cmdParms);
        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);
        int val = cmd.ExecuteNonQuery();
        cmd.Parameters.Clear();
        trans.Connection.Close();
        return val;
    }
    #endregion

    #region Execute a SqlCommand that returns a resultset against the database specified in the connection string
    /// <summary>
    /// Execute a SqlCommand that returns a resultset against the database specified in the connection string 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  SqlDataReader r = ExecuteReader(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="connectionString">a valid connection string for a SqlConnection</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>A SqlDataReader containing the results</returns>
    public static SqlDataReader ExecuteReader(string connString, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {
        SqlCommand cmd = new SqlCommand();
        SqlConnection conn = new SqlConnection(connString);

        // we use a try/catch here because if the method throws an exception we want to 
        // close the connection throw code, because no datareader will exist, hence the 
        // commandBehaviour.CloseConnection will not work
        try
        {
            PrepareCommand(cmd, conn, null, cmdType, cmdText, cmdParms);
            //取代可能的攻擊字眼
            ReplaceMaliciousParametersSqlCommand(ref cmd);
            SqlDataReader rdr = cmd.ExecuteReader(CommandBehavior.CloseConnection);
            cmd.Parameters.Clear();
            return rdr;
        }
        catch
        {
            conn.Close();
            throw;
        }
        finally
        {
            conn.Close();
        }
    }
    #endregion

    #region Execute a SqlCommand that returns the first column of the first record against the database specified in the connection string using the provided parameters.
    /// <summary>
    /// Execute a SqlCommand that returns the first column of the first record against the database specified in the connection string 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  Object obj = ExecuteScalar(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="connectionString">a valid connection string for a SqlConnection</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>An object that should be converted to the expected type using Convert.To{Type}</returns>
    public static object ExecuteScalar(string connString, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {
        SqlCommand cmd = new SqlCommand();

        using (SqlConnection conn = new SqlConnection(connString))
        {
            PrepareCommand(cmd, conn, null, cmdType, cmdText, cmdParms);
            //取代可能的攻擊字眼
            ReplaceMaliciousParametersSqlCommand(ref cmd);
            object val = cmd.ExecuteScalar();
            cmd.Parameters.Clear();
            conn.Close();
            return val;
        }
    }
    #endregion

    #region Execute a SqlCommand that returns the first column of the first record against an existing database connection using the provided parameters.
    /// <summary>
    /// Execute a SqlCommand that returns the first column of the first record against an existing database connection 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  Object obj = ExecuteScalar(connString, CommandType.StoredProcedure, "PublishOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="conn">an existing database connection</param>
    /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">the stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
    /// <returns>An object that should be converted to the expected type using Convert.To{Type}</returns>
    public static object ExecuteScalar(SqlConnection conn, CommandType cmdType, string cmdText, params SqlParameter[] cmdParms)
    {

        SqlCommand cmd = new SqlCommand();

        PrepareCommand(cmd, conn, null, cmdType, cmdText, cmdParms);
        //取代可能的攻擊字眼
        ReplaceMaliciousParametersSqlCommand(ref cmd);
        object val = cmd.ExecuteScalar();

        cmd.Parameters.Clear();
        conn.Close();
        return val;
    }
    #endregion

    #region add parameter array to the cache
    /// <summary>
    /// add parameter array to the cache
    /// </summary>
    /// <param name="cacheKey">Key to the parameter cache</param>
    /// <param name="cmdParms">an array of SqlParamters to be cached</param>
    public static void CacheParameters(string cacheKey, params SqlParameter[] cmdParms)
    {
        parmCache[cacheKey] = cmdParms;
    }
    #endregion

    #region Retrieve cached parameters
    /// <summary>
    /// Retrieve cached parameters
    /// </summary>
    /// <param name="cacheKey">key used to lookup parameters</param>
    /// <returns>Cached SqlParamters array</returns>
    public static SqlParameter[] GetCachedParameters(string cacheKey)
    {
        SqlParameter[] cachedParms = (SqlParameter[])parmCache[cacheKey];

        if (cachedParms == null)
            return null;

        SqlParameter[] clonedParms = new SqlParameter[cachedParms.Length];

        for (int i = 0, j = cachedParms.Length; i < j; i++)
            clonedParms[i] = (SqlParameter)((ICloneable)cachedParms[i]).Clone();

        return clonedParms;
    }
    #endregion

    #region Prepare a command for execution
    /// <summary>
    /// Prepare a command for execution
    /// </summary>
    /// <param name="cmd">SqlCommand object</param>
    /// <param name="conn">SqlConnection object</param>
    /// <param name="trans">SqlTransaction object</param>
    /// <param name="cmdType">Cmd type e.g. stored procedure or text</param>
    /// <param name="cmdText">Command text, e.g. Select * from Products</param>
    /// <param name="cmdParms">SqlParameters to use in the command</param>
    private static void PrepareCommand(SqlCommand cmd, SqlConnection conn, SqlTransaction trans, CommandType cmdType, string cmdText, SqlParameter[] cmdParms)
    {

        if (conn.State != ConnectionState.Open)
            conn.Open();

        cmd.Connection = conn;
        cmd.CommandText = cmdText;

        if (trans != null)
            cmd.Transaction = trans;

        cmd.CommandType = cmdType;

        if (cmdParms != null)
        {
            foreach (SqlParameter parm in cmdParms)
                cmd.Parameters.Add(parm);
        }
    }
    #endregion

    #region  SqlParam
    /// <summary>
    /// Make input param.
    /// </summary>
    /// <param name="ParamName">Name of param.</param>
    /// <param name="DbType">Param type.</param>
    /// <param name="Size">Param size.</param>
    /// <param name="Value">Param value.</param>
    /// <returns>New parameter.</returns>
    public static SqlParameter MakeInParam(string ParamName, SqlDbType DbType, int Size, object Value)
    {
        return MakeParam(ParamName, DbType, Size, ParameterDirection.Input, Value);
    }

    /// <summary>
    /// Make input param.
    /// </summary>
    /// <param name="ParamName">Name of param.</param>
    /// <param name="DbType">Param type.</param>
    /// <param name="Size">Param size.</param>
    /// <returns>New parameter.</returns>
    public static SqlParameter MakeOutParam(string ParamName, SqlDbType DbType, int Size)
    {
        return MakeParam(ParamName, DbType, Size, ParameterDirection.Output, null);
    }

    /// <summary>
    /// Make stored procedure param.
    /// </summary>
    /// <param name="ParamName">Name of param.</param>
    /// <param name="DbType">Param type.</param>
    /// <param name="Size">Param size.</param>
    /// <param name="Direction">Parm direction.</param>
    /// <param name="Value">Param value.</param>
    /// <returns>New parameter.</returns>
    public static SqlParameter MakeParam(string ParamName, SqlDbType DbType, Int32 Size,
        ParameterDirection Direction, object Value)
    {
        SqlParameter param;

        if (Size > 0)
            param = new SqlParameter(ParamName, DbType, Size);
        else
            param = new SqlParameter(ParamName, DbType);

        param.Direction = Direction;
        if (!(Direction == ParameterDirection.Output && Value == null))
            param.Value = Value;

        return param;
    }

    #endregion

    #region ExecuteDataset

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset and takes no parameters) against the database specified in 
    /// the connection string. 
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(connString, CommandType.StoredProcedure, "GetOrders");
    /// </remarks>
    /// <param name="connectionString">A valid connection string for a SqlConnection</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(string connectionString, CommandType commandType, string commandText)
    {
        // Pass through the call providing null for the set of SqlParameters
        return ExecuteDataset(connectionString, commandType, commandText, (SqlParameter[])null);
    }

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset) against the database specified in the connection string 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(connString, CommandType.StoredProcedure, "GetOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="connectionString">A valid connection string for a SqlConnection</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">An array of SqlParamters used to execute the command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        if (connectionString == null || connectionString.Length == 0) throw new ArgumentNullException("connectionString");

        // Create & open a SqlConnection, and dispose of it after we are done
        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            connection.Open();

            // Call the overload that takes a connection in place of the connection string
            return ExecuteDataset(connection, commandType, commandText, commandParameters);
        }
    }

    /// <summary>
    /// Execute a stored procedure via a SqlCommand (that returns a resultset) against the database specified in 
    /// the connection string using the provided parameter values.  This method will query the database to discover the parameters for the 
    /// stored procedure (the first time each stored procedure is called), and assign the values based on parameter order.
    /// </summary>
    /// <remarks>
    /// This method provides no access to output parameters or the stored procedure's return value parameter.
    /// 
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(connString, "GetOrders", 24, 36);
    /// </remarks>
    /// <param name="connectionString">A valid connection string for a SqlConnection</param>
    /// <param name="spName">The name of the stored procedure</param>
    /// <param name="parameterValues">An array of objects to be assigned as the input values of the stored procedure</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(string connectionString, string spName, params object[] parameterValues)
    {
        if (connectionString == null || connectionString.Length == 0) throw new ArgumentNullException("connectionString");
        if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

        // If we receive parameter values, we need to figure out where they go
        if ((parameterValues != null) && (parameterValues.Length > 0))
        {
            // Pull the parameters for this stored procedure from the parameter cache (or discover them & populate the cache)
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);

            // Assign the provided values to these parameters based on parameter order
            AssignParameterValues(commandParameters, parameterValues);

            // Call the overload that takes an array of SqlParameters
            return ExecuteDataset(connectionString, CommandType.StoredProcedure, spName, commandParameters);
        }
        else
        {
            // Otherwise we can just call the SP without params
            return ExecuteDataset(connectionString, CommandType.StoredProcedure, spName);
        }
    }

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset and takes no parameters) against the provided SqlConnection. 
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(conn, CommandType.StoredProcedure, "GetOrders");
    /// </remarks>
    /// <param name="connection">A valid SqlConnection</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlConnection connection, CommandType commandType, string commandText)
    {
        // Pass through the call providing null for the set of SqlParameters
        return ExecuteDataset(connection, commandType, commandText, (SqlParameter[])null);
    }

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset) against the specified SqlConnection 
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(conn, CommandType.StoredProcedure, "GetOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="connection">A valid SqlConnection</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">An array of SqlParamters used to execute the command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlConnection connection, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        if (connection == null) throw new ArgumentNullException("connection");

        // Create a command and prepare it for execution
        SqlCommand cmd = new SqlCommand();
        bool mustCloseConnection = false;
        PrepareCommand(cmd, connection, (SqlTransaction)null, commandType, commandText, commandParameters, out mustCloseConnection);

        // Create the DataAdapter & DataSet
        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
        {
            DataSet ds = new DataSet();

            // Fill the DataSet using default values for DataTable names, etc
            da.Fill(ds);

            // Detach the SqlParameters from the command object, so they can be used again
            cmd.Parameters.Clear();

            if (mustCloseConnection)
                connection.Close();

            // Return the dataset
            return ds;
        }
    }

    /// <summary>
    /// Execute a stored procedure via a SqlCommand (that returns a resultset) against the specified SqlConnection 
    /// using the provided parameter values.  This method will query the database to discover the parameters for the 
    /// stored procedure (the first time each stored procedure is called), and assign the values based on parameter order.
    /// </summary>
    /// <remarks>
    /// This method provides no access to output parameters or the stored procedure's return value parameter.
    /// 
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(conn, "GetOrders", 24, 36);
    /// </remarks>
    /// <param name="connection">A valid SqlConnection</param>
    /// <param name="spName">The name of the stored procedure</param>
    /// <param name="parameterValues">An array of objects to be assigned as the input values of the stored procedure</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlConnection connection, string spName, params object[] parameterValues)
    {
        if (connection == null) throw new ArgumentNullException("connection");
        if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

        // If we receive parameter values, we need to figure out where they go
        if ((parameterValues != null) && (parameterValues.Length > 0))
        {
            // Pull the parameters for this stored procedure from the parameter cache (or discover them & populate the cache)
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connection, spName);

            // Assign the provided values to these parameters based on parameter order
            AssignParameterValues(commandParameters, parameterValues);

            // Call the overload that takes an array of SqlParameters
            return ExecuteDataset(connection, CommandType.StoredProcedure, spName, commandParameters);
        }
        else
        {
            // Otherwise we can just call the SP without params
            return ExecuteDataset(connection, CommandType.StoredProcedure, spName);
        }
    }

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset and takes no parameters) against the provided SqlTransaction. 
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(trans, CommandType.StoredProcedure, "GetOrders");
    /// </remarks>
    /// <param name="transaction">A valid SqlTransaction</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlTransaction transaction, CommandType commandType, string commandText)
    {
        // Pass through the call providing null for the set of SqlParameters
        return ExecuteDataset(transaction, commandType, commandText, (SqlParameter[])null);
    }

    /// <summary>
    /// Execute a SqlCommand (that returns a resultset) against the specified SqlTransaction
    /// using the provided parameters.
    /// </summary>
    /// <remarks>
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(trans, CommandType.StoredProcedure, "GetOrders", new SqlParameter("@prodid", 24));
    /// </remarks>
    /// <param name="transaction">A valid SqlTransaction</param>
    /// <param name="commandType">The CommandType (stored procedure, text, etc.)</param>
    /// <param name="commandText">The stored procedure name or T-SQL command</param>
    /// <param name="commandParameters">An array of SqlParamters used to execute the command</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlTransaction transaction, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        if (transaction == null) throw new ArgumentNullException("transaction");
        if (transaction != null && transaction.Connection == null) throw new ArgumentException("The transaction was rollbacked or commited, please provide an open transaction.", "transaction");

        // Create a command and prepare it for execution
        SqlCommand cmd = new SqlCommand();
        bool mustCloseConnection = false;
        PrepareCommand(cmd, transaction.Connection, transaction, commandType, commandText, commandParameters, out mustCloseConnection);

        // Create the DataAdapter & DataSet
        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
        {
            DataSet ds = new DataSet();

            // Fill the DataSet using default values for DataTable names, etc
            da.Fill(ds);

            // Detach the SqlParameters from the command object, so they can be used again
            cmd.Parameters.Clear();

            // Return the dataset
            return ds;
        }
    }

    /// <summary>
    /// Execute a stored procedure via a SqlCommand (that returns a resultset) against the specified 
    /// SqlTransaction using the provided parameter values.  This method will query the database to discover the parameters for the 
    /// stored procedure (the first time each stored procedure is called), and assign the values based on parameter order.
    /// </summary>
    /// <remarks>
    /// This method provides no access to output parameters or the stored procedure's return value parameter.
    /// 
    /// e.g.:  
    ///  DataSet ds = ExecuteDataset(trans, "GetOrders", 24, 36);
    /// </remarks>
    /// <param name="transaction">A valid SqlTransaction</param>
    /// <param name="spName">The name of the stored procedure</param>
    /// <param name="parameterValues">An array of objects to be assigned as the input values of the stored procedure</param>
    /// <returns>A dataset containing the resultset generated by the command</returns>
    public static DataSet ExecuteDataset(SqlTransaction transaction, string spName, params object[] parameterValues)
    {
        if (transaction == null) throw new ArgumentNullException("transaction");
        if (transaction != null && transaction.Connection == null) throw new ArgumentException("The transaction was rollbacked or commited, please provide an open transaction.", "transaction");
        if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

        // If we receive parameter values, we need to figure out where they go
        if ((parameterValues != null) && (parameterValues.Length > 0))
        {
            // Pull the parameters for this stored procedure from the parameter cache (or discover them & populate the cache)
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(transaction.Connection, spName);

            // Assign the provided values to these parameters based on parameter order
            AssignParameterValues(commandParameters, parameterValues);

            // Call the overload that takes an array of SqlParameters
            return ExecuteDataset(transaction, CommandType.StoredProcedure, spName, commandParameters);
        }
        else
        {
            // Otherwise we can just call the SP without params
            return ExecuteDataset(transaction, CommandType.StoredProcedure, spName);
        }
    }

    #endregion ExecuteDataset

    public sealed class SqlHelperParameterCache
    {
        #region private methods, variables, and constructors

        //Since this class provides only static methods, make the default constructor private to prevent 
        //instances from being created with "new SqlHelperParameterCache()"
        private SqlHelperParameterCache() { }

        private static Hashtable paramCache = Hashtable.Synchronized(new Hashtable());

        /// <summary>
        /// Resolve at run time the appropriate set of SqlParameters for a stored procedure
        /// </summary>
        /// <param name="connection">A valid SqlConnection object</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <param name="includeReturnValueParameter">Whether or not to include their return value parameter</param>
        /// <returns>The parameter array discovered.</returns>
        private static SqlParameter[] DiscoverSpParameterSet(SqlConnection connection, string spName, bool includeReturnValueParameter)
        {
            if (connection == null) throw new ArgumentNullException("connection");
            if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

            SqlCommand cmd = new SqlCommand(spName, connection);
            cmd.CommandType = CommandType.StoredProcedure;

            connection.Open();
            SqlCommandBuilder.DeriveParameters(cmd);
            connection.Close();

            if (!includeReturnValueParameter)
            {
                cmd.Parameters.RemoveAt(0);
            }

            SqlParameter[] discoveredParameters = new SqlParameter[cmd.Parameters.Count];

            cmd.Parameters.CopyTo(discoveredParameters, 0);

            // Init the parameters with a DBNull value
            foreach (SqlParameter discoveredParameter in discoveredParameters)
            {
                discoveredParameter.Value = DBNull.Value;
            }
            return discoveredParameters;
        }

        /// <summary>
        /// Deep copy of cached SqlParameter array
        /// </summary>
        /// <param name="originalParameters"></param>
        /// <returns></returns>
        private static SqlParameter[] CloneParameters(SqlParameter[] originalParameters)
        {
            SqlParameter[] clonedParameters = new SqlParameter[originalParameters.Length];

            for (int i = 0, j = originalParameters.Length; i < j; i++)
            {
                clonedParameters[i] = (SqlParameter)((ICloneable)originalParameters[i]).Clone();
            }

            return clonedParameters;
        }

        #endregion private methods, variables, and constructors

        #region caching functions

        /// <summary>
        /// Add parameter array to the cache
        /// </summary>
        /// <param name="connectionString">A valid connection string for a SqlConnection</param>
        /// <param name="commandText">The stored procedure name or T-SQL command</param>
        /// <param name="commandParameters">An array of SqlParamters to be cached</param>
        public static void CacheParameterSet(string connectionString, string commandText, params SqlParameter[] commandParameters)
        {
            if (connectionString == null || connectionString.Length == 0) throw new ArgumentNullException("connectionString");
            if (commandText == null || commandText.Length == 0) throw new ArgumentNullException("commandText");

            string hashKey = connectionString + ":" + commandText;

            paramCache[hashKey] = commandParameters;
        }

        /// <summary>
        /// Retrieve a parameter array from the cache
        /// </summary>
        /// <param name="connectionString">A valid connection string for a SqlConnection</param>
        /// <param name="commandText">The stored procedure name or T-SQL command</param>
        /// <returns>An array of SqlParamters</returns>
        public static SqlParameter[] GetCachedParameterSet(string connectionString, string commandText)
        {
            if (connectionString == null || connectionString.Length == 0) throw new ArgumentNullException("connectionString");
            if (commandText == null || commandText.Length == 0) throw new ArgumentNullException("commandText");

            string hashKey = connectionString + ":" + commandText;

            SqlParameter[] cachedParameters = paramCache[hashKey] as SqlParameter[];
            if (cachedParameters == null)
            {
                return null;
            }
            else
            {
                return CloneParameters(cachedParameters);
            }
        }

        #endregion caching functions

        #region Parameter Discovery Functions

        /// <summary>
        /// Retrieves the set of SqlParameters appropriate for the stored procedure
        /// </summary>
        /// <remarks>
        /// This method will query the database for this information, and then store it in a cache for future requests.
        /// </remarks>
        /// <param name="connectionString">A valid connection string for a SqlConnection</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <returns>An array of SqlParameters</returns>
        public static SqlParameter[] GetSpParameterSet(string connectionString, string spName)
        {
            return GetSpParameterSet(connectionString, spName, false);
        }

        /// <summary>
        /// Retrieves the set of SqlParameters appropriate for the stored procedure
        /// </summary>
        /// <remarks>
        /// This method will query the database for this information, and then store it in a cache for future requests.
        /// </remarks>
        /// <param name="connectionString">A valid connection string for a SqlConnection</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <param name="includeReturnValueParameter">A bool value indicating whether the return value parameter should be included in the results</param>
        /// <returns>An array of SqlParameters</returns>
        public static SqlParameter[] GetSpParameterSet(string connectionString, string spName, bool includeReturnValueParameter)
        {
            if (connectionString == null || connectionString.Length == 0) throw new ArgumentNullException("connectionString");
            if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                return GetSpParameterSetInternal(connection, spName, includeReturnValueParameter);
            }
        }

        /// <summary>
        /// Retrieves the set of SqlParameters appropriate for the stored procedure
        /// </summary>
        /// <remarks>
        /// This method will query the database for this information, and then store it in a cache for future requests.
        /// </remarks>
        /// <param name="connection">A valid SqlConnection object</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <returns>An array of SqlParameters</returns>
        internal static SqlParameter[] GetSpParameterSet(SqlConnection connection, string spName)
        {
            return GetSpParameterSet(connection, spName, false);
        }

        /// <summary>
        /// Retrieves the set of SqlParameters appropriate for the stored procedure
        /// </summary>
        /// <remarks>
        /// This method will query the database for this information, and then store it in a cache for future requests.
        /// </remarks>
        /// <param name="connection">A valid SqlConnection object</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <param name="includeReturnValueParameter">A bool value indicating whether the return value parameter should be included in the results</param>
        /// <returns>An array of SqlParameters</returns>
        internal static SqlParameter[] GetSpParameterSet(SqlConnection connection, string spName, bool includeReturnValueParameter)
        {
            if (connection == null) throw new ArgumentNullException("connection");
            using (SqlConnection clonedConnection = (SqlConnection)((ICloneable)connection).Clone())
            {
                return GetSpParameterSetInternal(clonedConnection, spName, includeReturnValueParameter);
            }
        }

        /// <summary>
        /// Retrieves the set of SqlParameters appropriate for the stored procedure
        /// </summary>
        /// <param name="connection">A valid SqlConnection object</param>
        /// <param name="spName">The name of the stored procedure</param>
        /// <param name="includeReturnValueParameter">A bool value indicating whether the return value parameter should be included in the results</param>
        /// <returns>An array of SqlParameters</returns>
        private static SqlParameter[] GetSpParameterSetInternal(SqlConnection connection, string spName, bool includeReturnValueParameter)
        {
            if (connection == null) throw new ArgumentNullException("connection");
            if (spName == null || spName.Length == 0) throw new ArgumentNullException("spName");

            string hashKey = connection.ConnectionString + ":" + spName + (includeReturnValueParameter ? ":include ReturnValue Parameter" : "");

            SqlParameter[] cachedParameters;

            cachedParameters = paramCache[hashKey] as SqlParameter[];
            if (cachedParameters == null)
            {
                SqlParameter[] spParameters = DiscoverSpParameterSet(connection, spName, includeReturnValueParameter);
                paramCache[hashKey] = spParameters;
                cachedParameters = spParameters;
            }

            return CloneParameters(cachedParameters);
        }

        #endregion Parameter Discovery Functions

    }
    #endregion

    #region 處理 SqlCommand 惡意攻擊符號
    /// <summary>
    /// 處理 SqlCommand 惡意攻擊符號
    /// </summary>
    /// <param name="sc"></param>
    private static void ReplaceMaliciousParametersSqlCommand(ref SqlCommand sc)
    {
        foreach (System.Data.SqlClient.SqlParameter p in sc.Parameters)
        {
            if (p.Value != null)
            {
                p.Value = RemoveHTMLTags(DoReplace(p.Value.ToString()));
            }
        }
    }
    /// <summary>
    /// 目前可通過掃描的項目
    /// </summary>
    /// <param name="strOri">參數</param>
    /// <returns></returns>
    private static string DoReplace(string strOri)
    {
        if (strOri.Length > 0)
        {
            strOri = strOri.Replace("|", "︱");
            strOri = strOri.Replace("&", "＆");
            //strOri = strOri.Replace(";", "；");
            strOri = strOri.Replace("$", "＄");
            strOri = strOri.Replace("%", "％");
            strOri = strOri.Replace("@", "＠");
            strOri = strOri.Replace("'", "’");
            strOri = strOri.Replace("<", "＜");
            //strOri = strOri.Replace("(", "（");
            //strOri = strOri.Replace("\"", "＂");
            strOri = strOri.Replace(">", "＞");
            //strOri = strOri.Replace(")", "）");
            strOri = strOri.Replace("+", "＋");
            strOri = strOri.Replace("#", "＃");
            //strOri = strOri.Replace(" CR ", "ＣＲ");
            // strOri = strOri.Replace(" LF ", "ＬＦ");
            //strOri = strOri.Replace("\\", "＼");
            //strOri = strOri.Replace("&lt", "＆lt");
            // strOri = strOri.Replace("&gt", "＆gt");

            //如果有連續兩個以上的"-"，則將所有的"-"變成全型"－"
            if (strOri.IndexOf("--") > -1)
                strOri = strOri.Replace("-", "－");

        }

        return strOri;
    }
    #endregion

}
}