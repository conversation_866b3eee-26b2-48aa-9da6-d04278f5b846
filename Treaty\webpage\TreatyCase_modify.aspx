﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_modify.aspx.cs" Inherits="TreatyCase_modify" ValidateRequest="false" %>

<%@ Register Assembly="SmartGridView" Namespace="GridView3Probe" TagPrefix="cc1" %>

<%@ Register Src="../userControl/Header.ascx" TagPrefix="uc1" TagName="Header" %>
<%@ Register Src="../userControl/Foot.ascx" TagPrefix="uc2" TagName="Foot" %>


<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <link href="../Scripts/cluetip/jquery.cluetip.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.scrollbox.js"></script>
    <!-- 跑馬燈 -->
    <script type="text/javascript" src="../Scripts/navigation.js"></script>
    <!-- 下拉選單 -->
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>

    <script src="../Scripts/jquery.defaultvalue-1.0.js"></script>
    <script type="text/javascript" src="../Scripts/cluetip/jquery.cluetip.min.js"></script>
    <link rel="stylesheet" href="../Scripts/template.css" type="text/css" />
    <link rel="stylesheet" href="../Scripts/validationEngine.jquery.css" type="text/css" />
    <script type="text/javascript" src="../Scripts/languages/jquery.validationEngine-zh_TW.js" charset="utf-8"> </script>
    <script type="text/javascript" src="../Scripts/jquery.validationEngine.js" charset="utf-8"></script>
    <script type="text/javascript" src="../Scripts/autosize.min.js"></script>

    <script src="../Scripts/tinymce/tinymce.min.js"></script>
    <script type="text/javascript">
        var SystemCode = "<%=System.Configuration.ConfigurationManager.AppSettings["sysCode"].ToString()%>";
        var cust_url = "../subap";//"<%=System.Configuration.ConfigurationManager.AppSettings["customer_url"].ToString()%>";
        var ret_url = escape("../subap/colorbox_close.aspx");
        function reflash_topic(tag, arg) {
            __doPostBack(tag, arg);
        }
        function showDialog() {
            jQuery('#popup').dialog({
                modal: true,
                title: 'Meaasge',
                resizable: false,
                width: 'auto',
                autoOpen: false,
                open: function () {
                    jQuery("button, input[type=submit]").button();
                    secondarySiteDisplay();
                    if (jQuery('#ptsiid').length != 0) {
                        jQuery('#ptsiid').focus();
                        jQuery('#popup').dialog().width(jQuery('#popup').width());
                    } else {
                        document.forms.editform.firstname.focus();
                    }
                }
            });
        }

        function ViewEnLarge(obj, newPageUrl) {
            jQuery('#popup').load(newPageUrl, showDialog);
            alert($('#' + obj).val());
        }
        $(function () { $('a.iterm_dymanic').cluetip({ width: '830px', showTitle: false, ajaxCache: false }); });
        $(function () { $('a.iterm_dymanic_caseInfo').cluetip({ width: '480px', showTitle: false, arrows: true, ajaxCache: false }); });
        $(function () { $('a.iterm_dymanic_company').cluetip({ width: '830px', activation: 'click', sticky: true, closePosition: 'title', arrows: true, closeText: '<img src="../Scripts/cluetip/images/cross.png"  alt="close" />' }); });
        $(function () { $('a.iterm_dymanic_historyRecord').cluetip({ width: '600px', showTitle: false, arrows: true, ajaxCache: false }); });

        function newGuid() {//用來產生GUID亂數
            var guid = "";
            for (var i = 1; i <= 32; i++) {
                var n = Math.floor(Math.random() * 16.0).toString(16);
                guid += n;
                if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                    guid += "-";
            }
            return guid;
        }
        function viewCase(seno) {
            var url = './TreatyCase_view.aspx?seno=' + seno;
            window.open(url, 'companyInfo', config = 'height=600px,width=950px,resizable=yes,scrollbars=yes');
        }
        function find_customer2() {
            var Commonkey = newGuid();
            $(".ajax_mesg").colorbox({
                href: cust_url + '/Qry_customer_cb.aspx?systemcode=' + SystemCode + '&Commonkey=' + Commonkey + '&keyword=&url=' + ret_url
                , iframe: true, width: "650px", height: "550px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    var strURL = cust_url + '/ret_customer.aspx?Commonkey=' + Commonkey + "&keyword=";
                    $.getJSON(strURL + '&callback=?', jsonp_callbackcustomer);

                }
            });
        }
        function jsonp_callbackcustomer(data) {
            switch (data.c_compcname) {
                case "error0":
                    alert("尋找不到相關訊息 !");
                    break;
                case "danger":
                    alert("您查詢的關鍵字包含有危險攻擊字眼 !");
                    break;
                default:
                    if ($("#h_compno").val() == "")
                        $("#h_compno").val(data.c_compidno);
                    else
                        $("#h_compno").val($("#h_compno").val() + "," + data.c_compidno);
                    reflash_topic("company_renew", 0);
                    break;
            }
        }
        function Add_Inspect(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_assignInspect.aspx?seno=" + seno
                , title: '新增審查人'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Inspect_renew", 0);
                }
            });
        }
        function chk_int(theobj) {
            if (isNaN(theobj.value)) {
                alert(theobj.value + ' 不是數值');
                theobj.value = 0;
            }
        }
        function ipbshare_check(object) {//智權歸屬共用裡的本院、客戶自動比率調整
            chk_int(object);
            if (object.value > 100 || object.value < 0) {
                alert('所輸入的數字格式必須大於等於0，且小於等於100');
                object.value = '';
                return false;
            }
            var total = 100;
            if (object.id == 'txt_ipbi_percent') {//使用者觸發了本院
                $('#txt_ipbc_percent').val(total - $('#txt_ipbi_percent').val());
            }
            else {//使用者觸發了客戶
                $('#txt_ipbi_percent').val(total - $('#txt_ipbc_percent').val());
            }
            $('#rb_ipb_coparcenary').prop('checked', true);//自動將智權歸屬的共有選項勾選
            $('#txt_ipb_other_desc').val("");//智權歸屬-其他值清空
        }
        function ipbOther() {//如果有變更智權歸屬裡的其他描述，則自動勾選其他
            $('#rb_ipb_other').prop('checked', true);
            $('#txt_ipbi_percent').val("");//智權歸屬-共有-本院的值清空
            $('#txt_ipbc_percent').val("");//智權歸屬-共有-客戶的值清空
        }
        function duty_plan() {//如果有變更責任範圍裡的計畫經費，則自動勾選計畫經費
            $('#rb_duty_plain').attr('checked', true);
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_capital() {//如果有變更責任範圍裡的最高賠償金額，則自動勾選自動賠償金額
            $('#rb_duty_capital').attr('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_other_desc').val('');//其他的值清空
        }
        function duty_other() {//如果有變更責任範圍裡的其他，則自動勾選勾選其他
            $('#rb_duty_other').prop('checked', true);
            $('#txt_duty_plain_budget').val('');//計畫經費的值清空
            $('#txt_duty_capitalsum').val('');//最高賠償金額的值清空
        }
        function treaty_fileup(contno, seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileUp.aspx?contno=" + contno + "&seno=" + seno
                , title: '檔案上傳'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function CompanyInfo(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }

        function CompanyCASE(contno) {
            $(".ajax_mesg_comp").colorbox({
                href: "./TreatyCase_CompanyInfo.aspx?compno=" + contno
                , title: '客戶相關契約資料'
                , iframe: true, width: "900px", height: "450px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }

        function file_modify(fid) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_FileModify.aspx?fid=" + fid
                , title: '上傳檔案資料維護'
                , iframe: true, width: "700px", height: "320px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("file_renew", 0);
                }
            });
        }
        function treaty_defert(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_Defer.aspx?seno=" + seno
                , title: '議約展延'
                , iframe: true, width: "750px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("Defer_renew", 0);
                }
            });
        }
        function treaty_cop(seno) {
            $(".ajax_mesg").colorbox({
                href: "./TreatyCase_cop.aspx?seno=" + seno
                , title: '協同法務 新增/維護'
                , iframe: true, width: "400px", height: "380px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                    reflash_topic("cop_renew", 0);
                }
            });
        }

        function find_dictionary() {
            $(".ajax_dictionary").colorbox({
                href: "./TreatyCase_dictionary.aspx"
                , title: '常用詞庫'
                , iframe: true, width: "810px", height: "600px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {

                }
            });
        }


        function DeleteCase() {
            alert("案件已刪除!");
            location.replace("./TreatyApply.aspx");
        }
        function SendInspect(seno) {
            alert("案件已送審!");
            location.replace("./TreatyCase_view.aspx?seno=" + seno);
        }
        function EndCase(seno) {
            alert("案件已發結案通知!");
            location.replace("./TreatyCase_view.aspx?seno=" + seno);
        }
        function autogrow(textarea) {
            var adjustedHeight = textarea.clientHeight;
            adjustedHeight = Math.max(textarea.scrollHeight, adjustedHeight);
            if (adjustedHeight > textarea.clientHeight) {
                textarea.style.height = adjustedHeight + 'px';
            }
        }

        function openFileCompare(sender) {

            //window.open('../FileCompare/doccompare/index2.aspx?' + sender.getAttribute('key'));
            window.open('../FileCompare/doccompare/index2.aspx?seno=<%=ViewState["seno"].ToString()%>');
        }
        function doc_attach(doc_no) {
            window.open("https://docfile.itri.org.tw/contract/contractdetail?formid=" + doc_no, "dx", "fullscreen=no,resizable=no,scrollbars, channelmode=no,directories=no, status=no,toolbar=no,menubar=no,location=no,height=300,width=450", null);
        }


        function WordCheck() {
            window.open('https://itriap9.itri.org.tw/projassist/WebPage/Index.aspx');
        }
        function showCompInfoDialog(Compno) {
            var newopen = window.open('https://arpt.itri.org.tw/CustomerRiskDetails.aspx?CustNo=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }
        function showCompInfo(Compno) {
            var newopen = window.open('https://cust.itri.org.tw/mgrcust_custdata.aspx?comp_idno=' + Compno + '&tab=digi0', 'base_c', 'scrollbars,Width=700,Height=500,left=125,top=125,resizable=yes');
        }

        function Valuation_history(seno) {
            $(".ajax_vmesg").colorbox({
                href: "./TreatyCase_valuation_view.aspx?seno=" + seno
                , title: '計價歷程'
                , iframe: true, width: "850px", height: "650px", transition: "none", opacity: "0.5", overlayClose: false
                , onClosed: function () {
                    $('html, body').css('overflow', '');
                }
            });
        }
    </script>
    <style type="text/css">
        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        ​
        .empty {
            color: #aaa;
        }

        a:link {
            background-color: transparent;
            text-decoration: none;
            color: blue;
        }

        a:hover {
            background-color: transparent;
            text-decoration: underline;
            color: red;
        }

        a:active {
            background-color: transparent;
            text-decoration: underline;
            color: green;
        }
        .auto-style1 {
            width: 132px;
        }
    </style>
</head>
<body>
    <form id="Form1" method="post" runat="server">

        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperHeader fixwidth">
                    <uc1:Header ID="Header1" runat="server" />
                </div>
                <!-- WrapperHeader -->
                <div class="WrapperMain">
                    <div class="fixwidth">
                        <div class="twocol underlineT1">
                            <div class="left">
                                <asp:Literal ID="lb_Subtitle" runat="server" />
                            </div>
                            <div class="right font-light">
                                <asp:Image ID="Image1" runat="server" ImageUrl="../images/CONFIDENTIAL.png" Width="70px" Height="25px" />
                            </div>
                        </div>
                        <div class="twocol margin10TB">
                            <div class="left"><span class="font-red">*表示為必填欄位</span> </div>
                            <div class="right">
                                <span class="font-normal font-size3 font-bold">
                                    <asp:LinkButton ID="LB_request" runat="server" OnClick="LB_request_Click"><img src="../images/icon-1301.gif" border="0"/>檢視申請單資訊</asp:LinkButton>
                                    <asp:LinkButton ID="btnEngage" runat="server" OnClick="btnEngage_Click"><img src="../images/icon-1301.gif" />檢視洽案資訊</asp:LinkButton>&nbsp;&nbsp;
                  <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_tratycase_info" runat="server">案件紀錄</asp:Literal>
                                </span>
                            </div>
                        </div>
                        <div class="tabsubmenublock">
                            <span class="gentable font-normal">

                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">
                                                <asp:Label ID="Label1" runat="server">案號</asp:Label>
                                            </div>
                                        </td>
                                        <td class="width35">
                                            <asp:Label ID="txtComplexNo" runat="server" Text=""></asp:Label>
                                            (舊案號:
                                            <asp:Label ID="txtOldContno" runat="server"></asp:Label>)
                              <asp:Literal ID="LT_擬約幫手" runat="server" Text=''></asp:Literal>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約語文</div>
                                        </td>
                                        <td class="width40">
                                            <asp:RadioButton ID="rb_language_chiness" runat="server" Text="中文" GroupName="ContractLang" />
                                            <asp:RadioButton ID="rb_language_english" runat="server" Text="英文" GroupName="ContractLang" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>需求單位及部門</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txtOrgAbbrName" runat="server"></asp:Label>&nbsp;
                                            <asp:Label ID="txt_req_dept" runat="server"></asp:Label>
                                        </td>
                                        <td align="right">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>單位承辦人</div>
                                        </td>
                                        <td>
                                            <asp:Label ID="txt_promoter_name" runat="server" Width="95px"></asp:Label>
                                            &nbsp;
                       分機 &nbsp;
                                            <asp:Label ID="txtTel" runat="server" Width="110px"></asp:Label>&nbsp;
                       <asp:HiddenField ID="txt_promoter_empno" runat="server" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon"><span class="font-red">*</span>契約名稱</div>
                                            <br />
                                            <asp:LinkButton ID="LK_公文文號" runat="server"><img src="../images/icon-1301.gif" />檢視公文資訊</asp:LinkButton>

                                        </td>
                                        <td colspan="3">
                                            <asp:TextBox ID="txt_name" runat="server" Width="580px" Height="30px" class="text-input"></asp:TextBox>
                                            <div style="float: right; vertical-align: top">
                                                <asp:Literal ID="LT_計價" runat="server"></asp:Literal>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="auto-style1">
                                            <div class="font-title titlebackicon">是否為急件需求</div>
                                        </td>
                                        <td colspan="3">
                                            <asp:CheckBox ID="CB_急件" runat="server" />
                                            <asp:TextBox ID="TB_急件原因" runat="server" Width="580px" TextMode="MultiLine" Height="20px"></asp:TextBox>
                                            <div style="float: right; vertical-align: top">
                                                <asp:Literal ID="LT_計價版件次" runat="server"></asp:Literal>
                                            </div>
                                        </td>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon"><span class="font-red">*</span>簽約<br>對象</div>
                                            </td>
                                            <td colspan="3">
                                                <!-- 簽約對象 -->
                                                <div class="twocol margin5TB">
                                                    <div class="left"></div>
                                                    <asp:Button ID="BT_Customer" runat="server" class="ajax_mesg genbtnS" Text="新增" /><br />
                                                    <span class="stripeMe">
                                                        <cc1:SmartGridView ID="SGV_company" runat="server" AutoGenerateColumns="False" CellPadding="4" Width="90%" GridLines="None" OnRowCommand="SGV_company_RowCommand" OnRowDataBound="SGV_company_RowDataBound">
                                                            <FooterStyle Font-Bold="True" ForeColor="Black" />
                                                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Left" />
                                                            <SelectedRowStyle BackColor="#E2DED6" Font-Bold="True" ForeColor="#333333" />
                                                            <HeaderStyle BackColor="#5d7b9d" Font-Bold="True" ForeColor="Black" />
                                                            <AlternatingRowStyle CssClass="TRowEven" />
                                                            <Columns>
                                                                <asp:TemplateField HeaderText="功能">
                                                                    <HeaderStyle Width="5px" ForeColor="Black"></HeaderStyle>
                                                                    <ItemStyle HorizontalAlign="Center" Width="5px"></ItemStyle>
                                                                    <ItemTemplate>
                                                                        <asp:LinkButton ID="LB_del" runat="server" CommandName="UserDelete" CommandArgument='<%# Eval("comp_idno") %>' ForeColor="Red">刪<br />除</asp:LinkButton>
                                                                    </ItemTemplate>
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="跳票">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_退換票" runat="server" Text='<%# Eval("tmp_退換票").ToString()  %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="逾資本額½">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_資本額" runat="server" Text='<%#  Eval("tmp_資本額").ToString()  %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="資產遭查封">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_資產遭查封" runat="server" Text='<%# Eval("tmp_資產遭查封").ToString() %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="抽換票">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_抽換票" runat="server" Text='<%#  Eval("tmp_抽換票").ToString() %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="其他風險">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_其他風險" runat="server" Text='<%#  Eval("tmp_其他風險").ToString() %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="含陸資">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_含陸資" runat="server" Text='<%#  Eval("陸資").ToString() %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle Width="10px" ForeColor="Red" Font-Size="14px" HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="廠商編號">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_company" runat="server" Text='<%#  Eval("comp_idno").ToString() %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="65px" />
                                                                    <ItemStyle HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="廠商中文名稱<hr>廠商英文名稱">
                                                                    <ItemTemplate>
                                                                        <asp:LinkButton ID="LB_廠商中文名稱" runat="server" Compno='<%# Eval("comp_idno")%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="LB_廠商編號_Click"></asp:LinkButton><hr />
                                                                        <asp:Label ID="LB_廠商英文名稱" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_ename").ToString()) %>'></asp:Label>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="400px" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="廠商<br>國別">
                                                                    <ItemTemplate>
                                                                        <asp:Label ID="LB_國別" runat="server" Text='<%#  Server.HtmlEncode(Eval("comp_country_name").ToString()) %>'></asp:Label>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="40px" />
                                                                    <ItemStyle HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="利益<br>迴避">
                                                                    <ItemTemplate>
                                                                        <asp:Label ID="LB_利益迴避" runat="server" Text='<%#  Server.HtmlEncode(Eval("利益迴避").ToString()) %>'></asp:Label>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="10px" />
                                                                    <ItemStyle HorizontalAlign="Center" />
                                                                </asp:TemplateField>
                                                            <asp:TemplateField   HeaderText="風險客戶">
                                                                <HeaderStyle Width="10px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center"  />
                                                                 <ItemTemplate>
                                                                    <asp:ImageButton ID="IB_廠商中文名稱" runat="server" Compno='<%#System.Web.HttpUtility.HtmlEncode(Eval("comp_idno").ToString())%>' Text='<%#  Server.HtmlEncode(Eval("comp_cname").ToString()) %>' OnClick="IBx_廠商編號_Click" ImageUrl="../images/icon-lookdetail.png"></asp:ImageButton>
                                                                 </ItemTemplate>                                                      
                                                            </asp:TemplateField>
                                                            </Columns>
                                                            <EmptyDataTemplate>
                                                                <!--當找不到資料時則顯示「無資料」-->
                                                                <asp:Label ID="Label2" runat="server" ForeColor="Red" Text="無客戶資料，請新增!"></asp:Label>
                                                            </EmptyDataTemplate>
                                                            <FooterStyle BackColor="White" />
                                                            <PagerStyle BackColor="#EEEEEE" HorizontalAlign="Left" VerticalAlign="Bottom" Font-Size="Small" />
                                                        </cc1:SmartGridView>
                                                        <%-- <asp:SqlDataSource ID="SDS_company" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" /> --%>
                                                        <asp:HiddenField ID="h_compno" runat="server" />
                                                    </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">
                                                    案件<br />
                                                    性質<br />
                                                    <span style="float: right; vertical-align: top">
                                                        <asp:CheckBox ID="CB_常用版本" runat="server" Text="常用版本" Font-Bold="True" ForeColor="Blue" />
                                                    </span>
                                            </td>
                                            <td colspan="3" class="lineheight03">
                                                <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                                    <ContentTemplate>
                                                        <asp:CheckBox ID="cb_conttype_b0" runat="server" Text="技術服務" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                        <asp:CheckBox ID="cb_conttype_b1" runat="server" Text="合作開發" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                        <asp:CheckBox ID="cb_conttype_d4" runat="server" Text="技術授權" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                        <asp:CheckBox ID="cb_conttype_d5" runat="server" Text="專利授權" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                        <asp:CheckBox ID="cb_conttype_d7" runat="server" Text="專利讓與" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" Font-Bold="True" ForeColor="#0060A4" />
                                                        <asp:CheckBox ID="CB_技術讓與" runat="server" Text="技術讓與" class="font-title titlebackicon" Font-Bold="True" ForeColor="#0060A4" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                        <asp:CheckBox ID="cb_conttype_ns" runat="server" Text="新創事業(洽案)" AutoPostBack="True" OnCheckedChanged="cb_conttype_CheckedChanged" />
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                                <div class="font-title titlebackicon">
                                                    <b>
                                                        <asp:CheckBox ID="CB_技術授權" runat="server" Text="專屬技術授權" OnCheckedChanged="cb_conttype_CheckedChanged" AutoPostBack="True" />
                                                        <asp:CheckBox ID="CB_專利授權" runat="server" Text="專屬專利授權" OnCheckedChanged="cb_conttype_CheckedChanged" AutoPostBack="True" />
                                                        <asp:CheckBox ID="CB_技術與專利授權" runat="server" Text="專屬技術與專利授權" OnCheckedChanged="cb_conttype_CheckedChanged" AutoPostBack="True" />
                                                        <asp:CheckBox ID="CB_全球" runat="server" Text="全球" Font-Bold="True" ForeColor="#0060A4" Visible="false" />
                                                        <asp:CheckBox ID="CB_陸港澳" runat="server" Text="陸港澳" Font-Bold="True" ForeColor="#0060A4" Visible="false" />
                                                        <asp:CheckBox ID="CB_特定區域" runat="server" Text="境外實施" Font-Bold="True" ForeColor="#0060A4" />
                                                        <asp:CheckBox ID="CB_韓國" runat="server" Text="韓國" Font-Bold="True" ForeColor="#0060A4" Visible="false" />
                                                        <asp:Label ID="LB_計價提示" runat="server" Text="(如果有計價需求，請至 【契約性質:是否計價】 設定計價!!)" Font-Bold="True" ForeColor="Red" BackColor="#66FFFF" Font-Size="Small" Visible="false"></asp:Label>
                                                    </b>
                                                </div>
                                                <asp:CheckBox ID="cb_conttype_rb" runat="server" Text="標案" />
                                                <asp:CheckBox ID="cb_conttype_m" runat="server" Text="保密契約" />
                                                <asp:CheckBox ID="cb_conttype_c" runat="server" Text="工服" />
                                                <asp:RadioButton ID="rb_conttype_uo" runat="server" Text="國外支出(無收入)" />
                                                <asp:RadioButton ID="rb_conttype_ui" runat="server" Text="國內支出(無收入)" />
                                                <asp:RadioButton ID="rb_conttype_bd" runat="server" Text="新創事業" GroupName="ConttypeT" />
                                                <asp:RadioButton ID="rb_conttype_other" runat="server" Text="其他" GroupName="ConttypeT" />
                                                <asp:Label ID="txt_class_other_desc" runat="server" Enabled="false"></asp:Label>
                                                <span id="spanContractEdit" runat="server" visible="false">
                                                    <br />
                                                    <font color="#ff0000">契約修訂:
						        <asp:radiobuttonlist id="rbl_amend" runat="server" repeatlayout="Flow" repeatdirection="Horizontal" Visible="false">
							        <asp:listitem value="1" selected="True">展延</asp:listitem>
							        <asp:listitem value="2">中止</asp:listitem>
							        <asp:listitem value="3">其他</asp:listitem>
						        </asp:radiobuttonlist><asp:Label id="LB_amend" runat="server" Text="" ></asp:Label>
                                <asp:Label id="txtamend_other_desc" runat="server" width="400px"></asp:Label></font>
                                                </span>
                                                <span style="color: red">
                                                    <br />
                                                    (若為收入性質契約，請至洽案管理進行相關案件登錄及申請契約需求！)</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon"><span class="font-red">*</span>契約<br />
                                                    性質</div>
                                            </td>
                                            <td>
                                                <asp:Label ID="lb_Amend_Show" runat="server" ForeColor="Red" Visible="false">(修約)</asp:Label>
                                                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                                    <ContentTemplate>
                                                        <span class="gentable font-normal">
                                                            <asp:DropDownList ID="ddlContType" runat="server" AppendDataBoundItems="True" DataTextField="subtype_desc" DataValueField="code_subtype" Width="165px">
                                                                <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                                            </asp:DropDownList>
                                                        </span>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                                <%-- <asp:SqlDataSource ID="SDS_ContType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"  />--%>
                                                <font color="blue"><B> 是否計價:<asp:DropDownList ID="DDL_計價" runat="server" OnSelectedIndexChanged="DDL_計價_SelectedIndexChanged" AutoPostBack="true" >
                                <asp:ListItem Value="">無</asp:ListItem>
                                <asp:ListItem Value="Y">是</asp:ListItem>
                                <asp:ListItem Value="C">取消</asp:ListItem>
                            </asp:DropDownList>
                         ，計價說明:</B></font>
                                                <asp:TextBox runat="server" Text="" ID="TB_計價說明" Width="124px"></asp:TextBox>
                                            </td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">契約預估金額</div>
                                            </td>
                                            <td>
                                                <asp:DropDownList ID="ddlContMoneyType" runat="server" Width="100px" DataTextField="subtype_desc" DataValueField="code_subtype" />
                                                <%--                    <asp:SqlDataSource ID="SDS_ContMoneyType" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" SelectCommand="SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  " />--%>
                                                <asp:TextBox ID="txtContMoney" runat="server" class="inputex inputsizeS  text-input" />
                                                &nbsp;元 /匯率: 
                    <asp:TextBox ID="TB_money_rate" runat="server" class="inputsizeS text-input" Width="70px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">
                                                    契約<br />
                                                    期間<br />
                                                    (預定)
                                                </div>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txt_contsdate" runat="server" class="pickdate inputex inputsizeS  text-input" />&nbsp;至&nbsp;
                                                <asp:TextBox ID="txt_contedate" runat="server" class="pickdate inputex inputsizeS  text-input"></asp:TextBox>
                                            </td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">
                                                    契約內對於成果<br>
                                                有特殊限制者</td>
                                            <td>
                                                <div class="font-title titlebackicon">
                                                    <asp:CheckBox ID="CB_成果有特殊限制者" runat="server" Text="" AutoPostBack="true" OnCheckedChanged="CB_成果有特殊限制者_CheckedChanged" />
                                                </div>
                                                <asp:TextBox ID="TB_成果有特殊限制者_說明" runat="server" Height="38px" TextMode="MultiLine" Width="300px" Visible="false"></asp:TextBox>
                                            </td>

                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">專利維護費用<img src="../images/icon_tips.png" class="itemhint" title="契約中有約定，廠商須分攤專利維護費者。" /></div>
                                            </td>
                                            <td>
                                                <div class="font-title titlebackicon">
                                                    <asp:CheckBox ID="CB_特殊費用負擔" runat="server" Text="" AutoPostBack="true" OnCheckedChanged="CB_特殊費用負擔_CheckedChanged" />
                                                </div>
                                                <asp:TextBox ID="TB_特殊費用負擔原因" runat="server" Height="38px" TextMode="MultiLine" Width="300px"></asp:TextBox>
                                            </td>
                                            <asp:PlaceHolder ID="PL_CoPromoter" runat="server" Visible="false">
                                                <td align="right">
                                                    <div class="font-title titlebackicon">協同承辦人</div>
                                                </td>
                                                <td>
                                                    <asp:Label ID="txt_px_name" runat="server" Text="" />
                                                    <asp:HiddenField ID="h_px_empno" runat="server" />
                                                    <asp:Label ID="LB_adm" runat="server" Text=""></asp:Label>
                                                    君 
                    <asp:PlaceHolder ID="PH_rb_adm" runat="server" Visible="false">&
                            <asp:RadioButton ID="rb_adm_yes" runat="server" Text="同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                        <asp:RadioButton ID="rb_adm_no" runat="server" Text="不同意" GroupName="rb_adm" Visible="false"></asp:RadioButton>
                        <font style="color: red; font-weight: bold;"> 『同意貴單位所指定之業務窗口: <span style="color: red"><asp:Label id="LB_adm_text" runat="server" ForeColor="Red"></asp:Label></span> 君，與您有相同之權限』</font>
                    </asp:PlaceHolder>
                                                </td>
                                            </asp:PlaceHolder>
                                        </tr>
                                    <asp:PlaceHolder ID="PL_關鍵技術項目" runat="server">
                                    <tr>
                                        <td align="right" class="auto-style1"> <div class="font-title titlebackicon">是否涉及國家核心關鍵技術項目</div></td>
                                        <td colspan="3">
                                            <div class="font-title titlebackicon">
                                                <font color='red'><b>是</b></font>／列管迄日:<b><asp:Label ID="LB_核心關鍵技術_列管迄日" runat="server" Text=""></asp:Label></b><br />
                                                列管之關鍵技術項目(請說明)：<br />
                                                <asp:TextBox ID="TB_核心關鍵技術_說明" runat="server" Height="50px" TextMode="MultiLine" Width="600px" Enabled="false"></asp:TextBox>
                                            </div>
                                        </td>
                                    </tr>
                                    </asp:PlaceHolder>

                                        <asp:PlaceHolder ID="Plh_Dynax_sRC" runat="server"></asp:PlaceHolder>
                                        <%--            <asp:SqlDataSource ID="SDS_sRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">其他<br />
                                                    需求</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:CheckBox ID="rb_other_1" runat="server" Text="1.本案契約與前案號　" />&nbsp;<asp:TextBox ID="txt_otherrequire_contno" class="inputsizeS" runat="server" />&nbsp;之契約相同,承辦法務同仁為&nbsp;<asp:TextBox ID="TB_otherrequire_handle_name" class="inputsizeS" runat="server" Width="110px" /><br />
                                                <asp:CheckBox ID="rb_other_2" runat="server" Text="2.本案前已與法務同仁" />&nbsp;<asp:TextBox ID="txt_otherrequire_asked_name" class="inputsizeS" runat="server" />&nbsp;討論,請分案予前述法務同仁<br />
                                                <asp:CheckBox ID="rb_other_3" runat="server" Text="3.本案請法務同仁僅提供法律原則意見,毌庸修改契約文字" /><br />
                                                <asp:CheckBox ID="rb_other_4" runat="server" Text="4.請法務同仁僅提供本院常用契約(草稿)供參考" /><br />
                                                <asp:CheckBox ID="rb_other_T" runat="server" Text="5.其他。" />
                                                <asp:TextBox ID="txt_otherrequire_desc" runat="server" Width="530px" TextMode="MultiLine" Height="60px" onkeyup="autogrow(this);"></asp:TextBox>
                                                <%--<asp:SqlDataSource ID="SDS_oRC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">附件<br />
                                                    資料</div>
                                            </td>
                                            <td align="left" colspan="3">
                                                <asp:Button class="ajax_mesg genbtnS" ID="BT_FileUp" runat="server" Text="檔案上傳"></asp:Button>
                                                <input id="BT_OpenFileCompare" class="ajax_mesg genbtnS" onclick="openFileCompare(this)" runat="server" type="button" value="文件版本比對" />
                                                <input id="BT_OpenWordCheck" class="genbtnS" onclick="WordCheck()" runat="server" type="button" value="文件錯別字檢查" />
                                                <span class="stripeMe">
                                                    <asp:GridView ID="gv_doc_file" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_doc_file_RowCommand" OnRowDataBound="gv_doc_file_RowDataBound" Width="85%">
                                                        <Columns>
                                                            <asp:TemplateField HeaderText="功能">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_tcdf_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tcdf_no").ToString()) %>' Visible="false"></asp:Label>
                                                                    <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tcdf_no") %>' ForeColor="Red">刪除</asp:LinkButton><br />
                                                                    <asp:LinkButton ID="LB_edit" runat="server" class="ajax_mesg" CommandName="xEdit" CommandArgument='<%# Eval("tcdf_no") %>'>維護</asp:LinkButton>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="40px" HorizontalAlign="Center" ForeColor="Black" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="附件名稱">
                                                                <ItemTemplate>
                                                                    <asp:LinkButton ID="LinkButton1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>'> </asp:LinkButton>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="150px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Left" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="修改概要">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="300px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Left" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="審查">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_inspect" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_inspect").ToString()) %>'></asp:Label>
                                                                    <asp:Label ID="LB_tcdf_type" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_type").ToString()) %>' Visible="false"></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="30px" HorizontalAlign="Center"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>

                                                            <asp:TemplateField HeaderText="常用<br>版本">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_3" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_flag_desc").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="40px" HorizontalAlign="Center"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="上傳者">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_4" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="60px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                            <asp:TemplateField HeaderText="上傳日期">
                                                                <ItemTemplate>
                                                                    <asp:Label ID="LB_1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_date","{0:yyyy/MM/dd}").ToString()) %>'></asp:Label>
                                                                </ItemTemplate>
                                                                <HeaderStyle Width="100px"></HeaderStyle>
                                                                <ItemStyle HorizontalAlign="Center" />
                                                            </asp:TemplateField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%-- <asp:SqlDataSource ID="SDS_gv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                        <asp:PlaceHolder ID="PL_Inspect" runat="server">
                                            <tr valign="top">
                                                <td align="right">
                                                    <div class="font-title titlebackicon">審查<br />資訊</div>
                                                </td>
                                                <td width="450">
                                                    <asp:CheckBox ID="CB_NotInspect" runat="server" Text="文件不須審查" Enabled="false" Visible="false" />
                                                    <asp:Button ID="BT_AddInspect" runat="server" Text="新增審查人" class="ajax_mesg genbtnS" />
                                                    <asp:Button ID="BT_SendInspect" runat="server" Text="送出審查" class="genbtnS" OnClick="BT_SendInspect_Click" Visible="false" />
                                                    <span class="stripeMe">
                                                        <asp:GridView ID="GV_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="GV_Inspect_RowDataBound" OnRowCommand="GV_Inspect_RowCommand">
                                                            <Columns>
                                                                <asp:TemplateField HeaderText="功能">
                                                                    <ItemTemplate>
                                                                        <asp:Label ID="LB_tci_no" runat="server" Text='<%#  Server.HtmlEncode(Eval("tci_no").ToString()) %>' Visible="false"></asp:Label>
                                                                        <asp:LinkButton ID="LB_del" runat="server" CommandName="xDelete" CommandArgument='<%# Eval("tci_no") %>' ForeColor="Red">刪除</asp:LinkButton>
                                                                    </ItemTemplate>
                                                                    <HeaderStyle Width="50px" HorizontalAlign="Center" ForeColor="Black" />
                                                                </asp:TemplateField>
                                                                <asp:TemplateField HeaderText="順序">
                                                                    <ItemTemplate>
                                                                        <asp:Label ID="Lb_order" runat="server" Text='<%#  Server.HtmlEncode(Eval("tci_order").ToString()) %>'></asp:Label>
                                                                    </ItemTemplate>
                                                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                                </asp:TemplateField>
                                                                <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                    <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                </asp:BoundField>
                                                                <asp:BoundField DataField="tci_inspect_desc" HeaderText="簽核意見">
                                                                    <ItemStyle Width="200px" />
                                                                </asp:BoundField>
                                                                <asp:TemplateField HeaderText="簽核狀態">
                                                                    <ItemTemplate>
                                                                        <asp:Literal ID="LB_Istatus" runat="server" Text='<%#  Server.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                    </ItemTemplate>
                                                                    <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                                </asp:TemplateField>
                                                                <asp:BoundField DataField="tci_inspect_time" HeaderText="簽核日期">
                                                                    <ItemStyle HorizontalAlign="Left" Width="180px" />
                                                                </asp:BoundField>
                                                            </Columns>
                                                            <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                            <PagerSettings Position="Bottom" />
                                                            <PagerStyle HorizontalAlign="Left" />
                                                        </asp:GridView>
                                                        <%-- <asp:SqlDataSource ID="SDS_Inspect" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>
                    <asp:SqlDataSource ID="SDS_Inspect_count" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                    </span>
                                                </td>
                                                <asp:PlaceHolder ID="PL_Inspect_計價" runat="server" Visible="false">
                                                    <td align="right">
                                                        <div class="font-title titlebackicon">計價資訊</div>
                                                    </td>
                                                    <td align="left">
                                                        <table border="0" cellspacing="0" cellpadding="0" width="400px">
                                                            <tr>
                                                                <td colspan="2" with="100%" hight="20">承辦人：
                                                                    <asp:Label ID="LB_承辦人" runat="server"></asp:Label>
                                                                    <div style="float: right; margin-top: -10px">
                                                                        計價進度:
                                        <asp:DropDownList ID="DDL_計價進度" runat="server" Enabled="false">
                                            <asp:ListItem Value="0">計價承辦 </asp:ListItem>
                                            <asp:ListItem Value="1">草稿  </asp:ListItem>
                                            <asp:ListItem Value="3">審查  </asp:ListItem>
                                            <asp:ListItem Value="Z">完成  </asp:ListItem>
                                            <asp:ListItem Value="C">取消 </asp:ListItem>
                                            <asp:ListItem Value="U">不同意 </asp:ListItem>
                                        </asp:DropDownList>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="40%">參考價：
                                                                    <asp:Label ID="TB_參考價" runat="server"></asp:Label>
                                                                    元</td>
                                                                <td width="60%">，盡職調查結果: 
                                                <asp:DropDownList ID="DDL_盡職調查結果" runat="server" Enabled="false">
                                                    <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                                    <asp:ListItem Value="1">綠</asp:ListItem>
                                                    <asp:ListItem Value="5">黃</asp:ListItem>
                                                    <asp:ListItem Value="9">紅</asp:ListItem>
                                                </asp:DropDownList>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="40%">底價：
                                                                    <asp:Label ID="TB_底價" runat="server"></asp:Label>
                                                                    元</td>
                                                                <td width="60%">，<asp:CheckBox ID="CB_底價_無" runat="server" Text="無　說明：" Enabled="false" />
                                                                    <asp:TextBox ID="TB_底價_無_說明" runat="server" Width="180px" Height="30px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="40%">第三方鑑價：
                                                                    <asp:Label ID="TB_第三方鑑價" runat="server"></asp:Label>
                                                                    元</td>
                                                                <td width="60%">，<asp:CheckBox ID="CB_第三方鑑價_無" runat="server" Text="無　說明：" Enabled="false" />
                                                                    <asp:TextBox ID="TB_第三方鑑價_無_說明" runat="server" Width="180px" Height="30px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="2" with="100%">其他說明：<br />
                                                                    <asp:TextBox ID="TB_其他說明" runat="server" Width="400px" Height="40px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td_right" colspan="2">
                                                                    <span class="stripeMe">
                                                                        <asp:GridView ID="gv_vdoc_file" BorderWidth="0px" CellPadding="0" Width="97%" runat="server" AutoGenerateColumns="False" OnRowCommand="gv_vdoc_file_RowCommand" OnRowDataBound="gv_vdoc_file_RowDataBound">
                                                                            <Columns>
                                                                                <asp:TemplateField HeaderText="附件名稱">
                                                                                    <ItemTemplate>
                                                                                        <asp:LinkButton ID="LinkButton1" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>'> </asp:LinkButton>
                                                                                    </ItemTemplate>
                                                                                    <HeaderStyle Width="250px"></HeaderStyle>
                                                                                    <ItemStyle HorizontalAlign="Left" />
                                                                                </asp:TemplateField>
                                                                                <asp:TemplateField HeaderText="說明">
                                                                                    <ItemTemplate>
                                                                                        <asp:Label ID="LB_2" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_filetxt").ToString()) %>'></asp:Label>
                                                                                    </ItemTemplate>
                                                                                    <HeaderStyle Width="100px"></HeaderStyle>
                                                                                    <ItemStyle HorizontalAlign="Left" />
                                                                                </asp:TemplateField>
                                                                                <asp:TemplateField HeaderText="上傳資訊">
                                                                                    <ItemTemplate>
                                                                                        <asp:Label ID="LB_4" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_up_empname").ToString()) %>'></asp:Label><br>
                                                                                        <asp:Label ID="LB_1" runat="server" Text='<%# Server.HtmlEncode(Eval("時間").ToString()) %>' DataFormatString="{0:yyyy/MM/dd}" HtmlEncode="false"></asp:Label>
                                                                                    </ItemTemplate>
                                                                                    <HeaderStyle Width="70px"></HeaderStyle>
                                                                                    <ItemStyle HorizontalAlign="Center" />
                                                                                </asp:TemplateField>
                                                                            </Columns>
                                                                            <EmptyDataTemplate>無上傳資料 </EmptyDataTemplate>
                                                                            <PagerSettings Position="Bottom" />
                                                                            <PagerStyle HorizontalAlign="Left" />
                                                                        </asp:GridView>
                                                                        <%--  <asp:SqlDataSource ID="SDS_vgv_file" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td_right" colspan="2">
                                                                    <div style="float: right">
                                                                        <asp:PlaceHolder ID="PH_計價歷程" runat="server" Visible="false">
                                                                            <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_計價歷程" runat="server">計價歷程</asp:Literal>
                                                                        </asp:PlaceHolder>
                                                                    </div>
                                                                    <span class="stripeMe">
                                                                        <asp:GridView ID="GV_Inspect_value" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="GV_Inspect_value_RowDataBound" OnRowCommand="GV_Inspect_value_RowCommand" Width="450px">
                                                                            <Columns>
                                                                                <asp:BoundField DataField="tci_order" HeaderText="順序">
                                                                                    <ItemStyle HorizontalAlign="Center" Width="20px" />
                                                                                </asp:BoundField>
                                                                                <asp:BoundField DataField="tci_empname" HeaderText="審查人">
                                                                                    <ItemStyle HorizontalAlign="Center" Width="80px" />
                                                                                </asp:BoundField>
                                                                                <asp:BoundField DataField="tci_審查意見" HeaderText="簽核意見">
                                                                                    <ItemStyle HorizontalAlign="Left" Width="100px" />
                                                                                </asp:BoundField>
                                                                                <asp:TemplateField HeaderText="簽核<br>狀態">
                                                                                    <ItemTemplate>
                                                                                        <asp:Literal ID="LB_Istatus" runat="server" Text='<%#  Server.HtmlEncode(Eval("tci_flag").ToString()) %>'></asp:Literal>
                                                                                    </ItemTemplate>
                                                                                    <ItemStyle HorizontalAlign="Center" Width="100px" />
                                                                                </asp:TemplateField>
                                                                                <asp:BoundField DataField="short_inspect_time" HeaderText="簽核日期">
                                                                                    <ItemStyle HorizontalAlign="center" Width="100px" />
                                                                                </asp:BoundField>
                                                                            </Columns>
                                                                            <EmptyDataTemplate>不需送審! </EmptyDataTemplate>
                                                                            <PagerSettings Position="Bottom" />
                                                                            <PagerStyle HorizontalAlign="Left" />
                                                                        </asp:GridView>
                                                                        <%--<asp:SqlDataSource ID="SDS_Inspect_value" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </asp:PlaceHolder>
                                            </tr>
                                        </asp:PlaceHolder>
                                        <asp:PlaceHolder ID="PL_tc_manage_note" runat="server">
                                            <tr>
                                                <td align="right">
                                                    <div><a href='#' onclick='find_dictionary();' class="ajax_dictionary"><b>【常用詞】</b></a></div>
                                                    <div class="font-title titlebackicon">法務承辦人意見彙整</div>
                                                </td>
                                                <td class="lineheight03" colspan="3">
                                                    <asp:TextBox ID="txt_betsum" runat="server" Width="580px" TextMode="MultiLine" Height="30px"></asp:TextBox>
                                                    <asp:Image ID="Image2" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" />
                                                </td>
                                            </tr>
                                        </asp:PlaceHolder>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">預估完成日展延</div>
                                            </td>
                                            <td align="left" colspan="3">
                                                <div class="left">
                                                    <asp:Button ID="BT_defert" runat="server" Text="新增展延" class="ajax_mesg genbtnS" />
                                                </div>
                                                <span class="stripeMe">
                                                    <asp:GridView ID="GV_Defer" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False">
                                                        <Columns>
                                                            <asp:BoundField DataField="tcd_defer_date" HeaderText="展延後預估完成日">
                                                                <ItemStyle HorizontalAlign="Center" Width="150px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_keyin_date" HeaderText="提出展延日">
                                                                <ItemStyle HorizontalAlign="Center" Width="120px" />
                                                            </asp:BoundField>
                                                            <asp:BoundField DataField="tcd_desc" HeaderText="展延原因">
                                                                <ItemStyle Width="550px" HorizontalAlign="Left" />
                                                            </asp:BoundField>
                                                        </Columns>
                                                        <EmptyDataTemplate>無展延! </EmptyDataTemplate>
                                                        <PagerSettings Position="Bottom" />
                                                        <PagerStyle HorizontalAlign="Left" />
                                                    </asp:GridView>
                                                    <%--<asp:SqlDataSource ID="SDS_Defer" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" class="auto-style1">
                                                <div class="font-title titlebackicon">法務<br />
                                                    備註</div>
                                            </td>
                                            <td class="lineheight03" colspan="3">
                                                <asp:TextBox ID="txtManageNote" runat="server" Width="89%" TextMode="MultiLine" Height="60px"></asp:TextBox>
                                                <asp:Image ID="Image3" runat="server" ImageUrl="../images/icon-lookdetail.png" class="help_manage_note" />
                                            </td>
                                        </tr>
                                </table>
                            </span>
                        </div>
                        <!-- tabsubmenublock -->
                        <div class="twocol margin10TB">
                            <div class="right">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="td_left" colspan="2">
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="bt_reject" Text="退件" OnClick="bt_reject_Click" Visible="false" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="bt_cancle" Text="需求取消" OnClick="bt_cancle_Click" />
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="btnDelete" Text="刪除" Visible="False" OnClick="btnDelete_Click" />
                                        </td>
                                        <td class="td_right" colspan="3">
                                            <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_infoHandel" runat="server">歷次承辦人資訊</asp:Literal>
                                            &nbsp;&nbsp;
                        <img src="../images/icon-1301.gif" /><asp:Literal ID="LT_historyRecord" runat="server">歷次修改紀錄</asp:Literal><a href="#dialog06" class="inlineS"></a>
                                            <asp:Button runat="server" class="ajax_mesg genbtnS" ID="BT_view" Text="回上一頁" OnClick="BT_view_Click" />
                                            <asp:Button class="genbtnS" ID="BT_End" runat="server" Text="結案通知" OnClick="BT_End_Click"></asp:Button>
                                            <asp:Button class="genbtnS" ID="BT_save" runat="server" Text="存檔" OnClick="BT_save_Click"></asp:Button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="accordionblock">
                            <div class="tabsubmenublock">
                                <span class="gentable font-normal">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">分案主管</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_assign_name" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">送件/分案日期</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_send_date" runat="server"></asp:Literal>
                                                / 
                                                <asp:Literal ID="lb_assign_date" runat="server"></asp:Literal></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">法務承辦人</div>
                                            </td>
                                            <td align="left">
                                                <asp:Literal ID="lb_handle_name" runat="server"></asp:Literal>
                                                |
                                                <asp:Literal ID="lb_handle_empno" runat="server"></asp:Literal>
                                                |
                                                <asp:Literal ID="lb_handle_ext" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">進度</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="LT_L_Degree" runat="server"></asp:Literal><asp:DropDownList ID="DDL_Degree" runat="server" DataTextField="subtype_desc" DataValueField="code_subtype" Visible="false"></asp:DropDownList><%--<asp:SqlDataSource ID="SDS_DDL_degree" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>"></asp:SqlDataSource>--%></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">協同法務承辦人</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_cop" runat="server"></asp:Literal><%--<asp:SqlDataSource ID="SDS_cop" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%><asp:Button class="genbtnS ajax_mesg" ID="BT_cop" runat="server" Text="維護協同承辦人"></asp:Button></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">處理天數</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_process_date" runat="server"></asp:Literal></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">產出文件數</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_contract_count" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">預估/需求結件日</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_expect_close_date" runat="server"></asp:Literal>
                                                / 
                                                <asp:Literal ID="lb_case_closedate" runat="server"></asp:Literal></td>
                                        </tr>
                                        <tr>
                                            <td align="right">
                                                <div class="font-title titlebackicon">修改人</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_modify_emp_name" runat="server"></asp:Literal>
                                                |
                                                <asp:Literal ID="lb_modify_emp_no" runat="server"></asp:Literal></td>
                                            <td align="right">
                                                <div class="font-title titlebackicon">修改日期</div>
                                            </td>
                                            <td>
                                                <asp:Literal ID="lb_modify_date" runat="server"></asp:Literal></td>
                                        </tr>
                                    </table>
                                </span>
                            </div>
                            <!-- tabsubmenublock -->
                        </div>
                        <!-- accordionblock -->
                    </div>
                    <!-- fixwidth -->
                    <br />
                </div>
                <!-- WrapperMain -->
            </div>
            <!-- WrapperContent -->
        </div>
        <!-- WrapperBody -->

        <uc2:Foot runat="server" ID="Foot" />

        <%--<asp:SqlDataSource ID="SDS_NR" runat="server"  ConnectionString="<%$ ConnectionStrings:CS_treaty %>"/>
<asp:SqlDataSource ID="SDS_log" runat="server"  ConnectionString="<%$ ConnectionStrings:CS_treaty %>"/>
<asp:SqlDataSource ID="SDS_auth" runat="server"  ConnectionString="<%$ ConnectionStrings:CS_treaty %>"/>--%>
        <script type="text/javascript">
            tinymce.init({
                selector: '#txt_betsum',
                width: "800",
                height: "500",
                language: 'zh_TW',
                //font_formats: "新細明體=Microsoft JhengHei;細明體=PMingLiU;標楷體=MingLiU;微軟正黑體=微軟正黑體 ;Arial=arial,helvetica,sans-serif;",| fontselect 
                fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 24pt 36pt",
                content_css: 'css/content.css',
                toolbar: "insertfile undo redo | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | forecolor backcolor | fontsizeselect",
                statusbar: false,
                plugins: [' code', 'textcolor'],
            });

            $(function () {
                $(".pickdate").datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yy/mm/dd',
                    monthNamesShort: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
                    showButtonPanel: true,
                    closeText: '關閉',
                    currentText: '移至今天'

                });

                // hack to add clear button
                // 增加清除按鈕 -Start (Ref. http://bugs.jqueryui.com/ticket/3999)
                //wrap up the redraw function with our new shiz
                var dpFunc = $.datepicker._generateHTML; //record the original
                $.datepicker._generateHTML = function (inst) {
                    var thishtml = $(dpFunc.call($.datepicker, inst)); //call the original
                    thishtml = $('<div />').append(thishtml); //add a wrapper div for jQuery context
                    //locate the button panel and add our button - with a custom css class.
                    $('.ui-datepicker-buttonpane', thishtml).append(
                        $('<button class="\
				        ui-datepicker-clear ui-state-default ui-priority-primary ui-corner-all\
				        "\>清除</button>'
                        ).click(function () {
                            inst.input.attr('');
                            inst.input.attr('value', '');
                            inst.input.datepicker('hide');
                        })
                    );
                    thishtml = thishtml.children(); //remove the wrapper div
                    return thishtml; //assume okay to return a jQuery
                };
                // 增加清除按鈕 -End				
            });

            $(document).ready(function () {
                jQuery('#Form1').validationEngine({});
                $('#txtContMoney').defaultValue('0');
                $('#txt_name').defaultValue('請輸入契約名稱');
                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                $(".inputhint").tooltip({
                    position: { my: "left+10 bottom+40", at: "left bottom " },
                    tooltipClass: "custom-tooltip-styling",
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500, height: 300,
                    autoOpen: false,
                    show: { duration: 300 },
                    hide: { duration: 300 }
                });

                //$('textarea.expand').focus(function () {
                //    $(this).animate({ height: "+=50" }, 400);
                //});
                //$(".help_txtSignReason").mouseover(function () {
                //$(".help_txtSignReason").removeAttr("title");
                //$(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                //$(".help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                //});
                //$("#txtSignReason").change(function () {
                //    $(".help_txtSignReason").removeAttr("title");
                //    $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                //    $(".help_txtSignReason").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                //});
                //$(".help_ip_apply").mouseover(function () {
                //$(".help_ip_apply").removeAttr("title");
                //$(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                //$(".help_ip_apply").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                //});
                //$(".help_income_divvy").mouseover(function () {
                //$(".help_income_divvy").removeAttr("title");
                //$(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                //$(".help_income_divvy").cluetip({ width: "500px", splitTitle: "\n", showTitle: false });
                //});

                $(".help_txtSignReason").attr("title", $("#txtSignReason").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_ip_apply").attr("title", $("#txt_ip_apply").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_income_divvy").attr("title", $("#txt_income_divvy").val());
                $(".help_txtSignReason").attr("class", "itemhint");
                $(".help_otherrequire_desc").attr("title", $("#txt_otherrequire_desc").val());
                $(".help_otherrequire_desc").attr("class", "itemhint");
                $(".help_manage_note").attr("title", $("#txt_manage_note").val());
                $(".help_manage_note").attr("class", "itemhint");

                $(".itemhint").tooltip({
                    track: true,
                    position: { my: "left+15 center", at: "right center" },
                    //讓tooltips內可以放置HTML CODE
                    content: function () { return $(this).prop('title'); }
                });
                //說明dialog
                $("#pagehow01").dialog({
                    modal: true,
                    position: ["center", 100],
                    width: 500,
                    height: 300,
                    autoOpen: false,
                    show: { duration: 300 },
                    hide: { duration: 300 }
                });

            });
        //autosize(document.querySelectorAll('textarea'));
        </script>
        <script type="text/javascript" src="../Scripts/autoheight.js"></script>
    </form>
</body>
</html>
