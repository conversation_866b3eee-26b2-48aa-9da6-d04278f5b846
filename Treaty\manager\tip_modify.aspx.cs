﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_manager_tip_modify : System.Web.UI.Page
{

    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    RemoveCheckMax oRCM = new RemoveCheckMax();
    protected void Page_Load(object sender, EventArgs e)
    {

        if (!IsPostBack)
        {
            if (Request.QueryString["tid"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["tid"].ToString()) || (Request.QueryString["tid"].Length > 5))
                    Response.Redirect("../danger.aspx");
                ViewState["tid"] = Request.QueryString["tid"].ToString();
            }
            if (Request.QueryString["mod"] != null)
            {
                if ((Request.QueryString["mod"].Length > 8))
                    Response.Redirect("../danger.aspx");
                ViewState["mod"] = Request.QueryString["mod"].ToString();

                if (Request.QueryString["mod"] == "Modify")
                {
                    BindComp();
                    BindFileList();
                }
            }
        }
    }

    private void BindComp()
    {
        DataTable dt = new DataTable();
        try
        {
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                sqlConn.Open();
                SqlCommand oCmd = new SqlCommand();
                oCmd.Connection = sqlConn;
                StringBuilder sb = new StringBuilder();
                sb.Append(@"esp_treaty_TechCase_tip");
                oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(ViewState["tid"].ToString()));
                oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("View"));
                oCmd.CommandText = sb.ToString();
                oCmd.CommandType = CommandType.StoredProcedure;
                oCmd.CommandTimeout = 0;

                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                oda.Fill(dt);
            }
        }
        catch (Exception ex)
        {
            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

            oRCM.ErrorExceptionDataToDB(logMail);
        }
        System.Data.DataView dv_search = dt.DefaultView;// (DataView)SDS_search.Select(new DataSourceSelectArguments());
        if (dv_search.Count >= 1)
        {
            DDL_type.SelectedValue = dv_search[0]["tip_type"].ToString();
            DDL_type_SelectedIndexChanged(DDL_type, EventArgs.Empty);
            DDL_code.SelectedValue = dv_search[0]["tip_xid"].ToString();
            TB_title.Text = Server.HtmlEncode(dv_search[0]["tip_title"].ToString());
            TB_content.Text = Server.HtmlDecode(Server.HtmlEncode(dv_search[0]["tip_content"].ToString()));
            LB_id.Text = Server.HtmlEncode(dv_search[0]["tip_id"].ToString());
        }
    }


    protected void BT_save_Click(object sender, EventArgs e)
    {
        if (DDL_code.SelectedValue.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");
        if (TB_title.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");
        if (TB_content.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");

        if (ViewState["mod"].ToString() == "Modify")
        {
            try
            {
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_treaty_TechCase_tip");
                    oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(LB_id.Text));
                    oCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(DDL_type.SelectedValue));
                    oCmd.Parameters.AddWithValue("@tip_xid", oRCM.SQLInjectionReplaceAll(DDL_code.SelectedValue));
                    oCmd.Parameters.AddWithValue("@tip_title", oRCM.SQLInjectionReplaceAll(TB_title.Text));
                    oCmd.Parameters.AddWithValue("@tipContent", oRCM.SQLInjectionReplaceAll(TB_content.Text));
                    oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("Modify"));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;
                    oCmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
        else
        {
            try
            {
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    sqlConn.Open();
                    SqlCommand oCmd = new SqlCommand();
                    oCmd.Connection = sqlConn;
                    StringBuilder sb = new StringBuilder();
                    sb.Append(@"esp_treaty_TechCase_tip");
                    oCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(""));
                    oCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(DDL_type.SelectedValue));
                    oCmd.Parameters.AddWithValue("@tip_title", oRCM.SQLInjectionReplaceAll(TB_title.Text));
                    oCmd.Parameters.AddWithValue("@tipContent", oRCM.SQLInjectionReplaceAll(TB_content.Text));
                    oCmd.Parameters.AddWithValue("@mod", oRCM.SQLInjectionReplaceAll("Add"));
                    oCmd.CommandText = sb.ToString();
                    oCmd.CommandType = CommandType.StoredProcedure;
                    oCmd.CommandTimeout = 0;
                    oCmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString, Request, Response, ex);

                oRCM.ErrorExceptionDataToDB(logMail);
            }
        }
        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

    }

    protected void btnFileUpload_Click(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        string path = oRCM.GetValidPathPart(FilePathString, "tip");
        path = oRCM.GetValidPathPart(path, DateTime.Now.Year.ToString());

        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);

        if (FileUpload1.PostedFile.ContentLength > 0)
        {
            string upFileName = FileUpload1.FileName.Replace("&", "＆").Replace("­", "－");
            string str_FileName = Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "").Replace("--", "－－") +
                                    Path.GetExtension(upFileName);

            FileUpload1.SaveAs(path.Replace("/", "").Replace("..", "") + oRCM.SQLInjectionReplaceAll(str_FileName));

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@mod", "file_Add");
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(ViewState["tid"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tdf_xid", "");
                sqlCmd.Parameters.AddWithValue("@tdf_type", "");
                sqlCmd.Parameters.AddWithValue("@tdf_inspect", "");
                sqlCmd.Parameters.AddWithValue("@tdf_filename", oRCM.SQLInjectionReplaceAll(upFileName.Replace("--", "－－")));
                sqlCmd.Parameters.AddWithValue("@tdf_filetxt", "");
                sqlCmd.Parameters.AddWithValue("@tdf_filetype", "");
                sqlCmd.Parameters.AddWithValue("@tdf_url", oRCM.SQLInjectionReplaceAll(path.Replace("/", "").Replace("..", "") + str_FileName));
                sqlCmd.Parameters.AddWithValue("@tdf_up_flag", "");

                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('檔案上傳成功');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindFileList();
        }

    }

    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tdf_url"].ToString().Trim();
                str_filename = dv[0]["tdf_filename"].ToString().Trim();
            }


            //File_Del(e.CommandArgument.ToString());


            BindFileList();
        }

        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";

            DataTable dt = File_View(e.CommandArgument.ToString());
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tdf_url"].ToString().Trim().Replace("/", "").Replace("..", "");
                str_filename = dv[0]["tdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {

                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                Response.WriteFile(Server.HtmlEncode(str_file_url));
                Response.Flush();
                Response.End();
            }
        }
    }

    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            LinkButton lb_del = (LinkButton)e.Row.FindControl("lnkbtn_Del");

            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return confirm('確定要刪除 ?');");
                //Label lb_tcdf_no = (Label)e.Row.FindControl("lbl_tcdf_no");
                //LinkButton lb_edit = (LinkButton)e.Row.FindControl("lnkbtn_Edit");
                //lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");      
            }
        }
    }

    private void BindFileList()
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@tipID", oRCM.SQLInjectionReplaceAll(ViewState["tid"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mod", "file_list");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    private DataTable File_View(string tdf_id)
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mod", "file_view");
            sqlCmd.Parameters.AddWithValue("@tdf_id", oRCM.SQLInjectionReplaceAll(tdf_id));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        return dt;
    }

    protected void lnkbtn_Del_Click(object sender, EventArgs e)
    {
        #region --- modify ---
        string tdf_id = ((LinkButton)sender).CommandArgument;
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@mod", "file_Del");
            sqlCmd.Parameters.AddWithValue("@tdf_id", oRCM.SQLInjectionReplaceAll(tdf_id));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    protected void DDL_type_SelectedIndexChanged(object sender, EventArgs e)
    {
        TB_class.Text = DDL_type.SelectedValue;

        BindCodeList();

        TB_class.Visible = string.IsNullOrEmpty(TB_class.Text);
    }

    private void BindCodeList()
    {
        DataTable dt = new DataTable();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_tip";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tipType", oRCM.SQLInjectionReplaceAll(TB_class.Text));
            sqlCmd.Parameters.AddWithValue("@mod", "code_list");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                DDL_code.DataSource = dt;
                DDL_code.DataValueField = "tcs_code";
                DDL_code.DataTextField = "tcs_codeName";
                DDL_code.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }


        #endregion
    }

    protected void TB_class_TextChanged(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(TB_class.Text) == false)
        {
            BindCodeList();
        }
    }
}
