﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Comp_EmployeeMultiSelect_EmployeeMultiSelect : System.Web.UI.UserControl
{
    protected void Page_Load(object sender, EventArgs e)
    {
        
    }
    //開窗選取
    protected void img_search_Click(object sender, ImageClickEventArgs e)
    {
        //open window參數說明
        //tbxid : 存放姓名字串的TextBox(MultiLine)的ID
        //hfid : 存放工號字串的HiddenField 的ID
        //hfValue : 存放工號字串的HiddenField的Value
        ////string script = "<script language='javascript'>";
        ////string root = Request.ApplicationPath.ToString();
        ////script += " var w =window.open('" + root + "/Comp/EmployeeMultiSelect/EmployeeWindow.aspx?tbxid=" + Server.UrlEncode(txa_cname_string.ClientID) + "&hfid=" + Server.UrlEncode(hf_empno_string.ClientID) + "&hfValue=" + Server.UrlEncode(hf_empno_string.Value) + "','empno', 'top=50,left=250,width=800,height=610,center=yes,resizable=yes,status=no,location=no,scrollbars=yes');";
        ////script += "w.focus();";
        ////script += "</script>";
        ////ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "openwin", script, false);
    }

    //設定取得員工代號字串
    public string empnolist
    {
        get 
        {
            return TB_empno_string.Text;
        }
    }

    //設定取得員工姓名字串
    public string cnamelist
    {
        get
        {
            return TB_MtName.Text;
        }
    }

   
}