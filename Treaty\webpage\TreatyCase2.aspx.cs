﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.WebControls;
public partial class TreatyCase2 : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.Image1));
        if (Request["seno"] != null)
        {
            if ((Request["seno"].Length == 0) || (Request["seno"].Length > 8))
                Response.Redirect("../danger.aspx");
            if (!IsNumber(Request["seno"].ToString()))
                Response.Redirect("../danger.aspx");
            if (!IsNumber(Request["newver"].ToString()))
                Response.Redirect("../danger.aspx");
        }
        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            BT_Customer.Attributes.Add("onclick", "find_customer2();");
            ViewState["NewType"] = "1";
            txt_promoter_name.Attributes.Add("onChange", string.Format("Find_empno_kw('{0}',1);", txt_promoter_empno.ClientID));
            string strCaseNo = "";
            if (Request["contno"] != null)
            {
                if (Request["contno"].Length > 15)
                    Response.Redirect("../danger.aspx");
                if (!IsNatural_Number(Request["contno"].ToString()))
                    Response.Redirect("../danger.aspx");
                strCaseNo = Request["contno"].ToString();
            }
            else
            {

                if (Request.ServerVariables["HTTP_VIA"] != null)
                {
                    ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
                }
                else
                {
                    ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
                }
            }
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            x_dept.Attributes.Add("readOnly", "readonly");
            lb_keyin_emp_no.Text = Server.HtmlEncode(ssoUser.empNo);       //建檔人工號
            lb_keyin_emp_name.Text = Server.HtmlEncode(ssoUser.empName);   //建檔人名稱
            lb_keyin_tel.Text = Server.HtmlEncode(ssoUser.empTelext);      //建檔人分機
            lb_keyin_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期
            txtOrgAbbrName.Text = Server.HtmlEncode(ssoUser.empOrgname);
            x_dept.Text = Server.HtmlEncode(ssoUser.empDeptcd);
            txt_req_dept.Value = Server.HtmlEncode(ssoUser.empOrgcd + ssoUser.empDeptcd);
            txt_promoter_name.Text = Server.HtmlEncode(ssoUser.empName);
            txt_promoter_empno.Value = Server.HtmlEncode(ssoUser.empNo);
            txtTel.Text = Server.HtmlEncode(ssoUser.empTelext);
            ViewState["tr_class"] = "L";
        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
    }

    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    private void DoSaveDraft(string tr_status)
    {
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = "esp_TreatyCase2_modify";
        ViewState["eb_orgcd"] = txt_req_dept.Value.Substring(0, 2);//取得組織單位
                                                                   //SDS_NR.SelectParameters.Add("tc2_seno", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_year", (DateTime.Now).Year.ToString());
                                                                   //SDS_NR.SelectParameters.Add("tc2_orgcd", txt_req_dept.Value.Substring(0, 2));
                                                                   //SDS_NR.SelectParameters.Add("tc2_class", "L");
                                                                   //SDS_NR.SelectParameters.Add("tc2_sn", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_old_contno", txtOldContno.Text.Trim()); //將舊案的流水號存入
                                                                   //SDS_NR.SelectParameters.Add("tc2_compidno_all", h_compno.Value);
                                                                   //SDS_NR.SelectParameters.Add("tc2_req_dept",   txt_req_dept.Value.Trim());
                                                                   //SDS_NR.SelectParameters.Add("tc2_accept_date", txt_accept_date.Text.Trim());
                                                                   //SDS_NR.SelectParameters.Add("tc2_promoter_no", txt_promoter_empno.Value);//承辦人姓名
                                                                   //SDS_NR.SelectParameters.Add("tc2_promoter_name", txt_promoter_name.Text.Trim());
                                                                   //SDS_NR.SelectParameters.Add("tc2_name", txt_name.Text.Trim());//洽案(契約)名稱
                                                                   //SDS_NR.SelectParameters.Add("tc2_heads","");
                                                                   //SDS_NR.SelectParameters.Add("tc2_compaddr", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_manage_note","");
                                                                   //SDS_NR.SelectParameters.Add("tc2_tort_impeach", ""); 
                                                                   //SDS_NR.SelectParameters.Add("tc2_bugdet","");
                                                                   //SDS_NR.SelectParameters.Add("tc2_breviary","");
                                                                   //SDS_NR.SelectParameters.Add("tc2_prosecution_date", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_litigation_type", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_litigation_name", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_litigation_target", "");
                                                                   //SDS_NR.SelectParameters.Add("tc2_keyin_emp_no", lb_keyin_emp_no.Text.Trim());    // 建檔工號
                                                                   //SDS_NR.SelectParameters.Add("tc2_keyin_emp_name", lb_keyin_emp_name.Text.Trim());// 建檔人
                                                                   //SDS_NR.SelectParameters.Add("tc2_keyin_date", lb_keyin_date.Text.Trim());        // 建檔日期
                                                                   //SDS_NR.SelectParameters.Add("tc2_keyin_emp_tel","");
                                                                   //SDS_NR.SelectParameters.Add("tc2_modify_emp_no", lb_modify_emp_no.Text.Trim());  // 修改工號
                                                                   //SDS_NR.SelectParameters.Add("tc2_modify_emp_name", lb_modify_emp_name.Text.Trim());// 修改人
                                                                   //SDS_NR.SelectParameters.Add("tc2_modify_date", lb_modify_date.Text.Trim());      // 修改日期
                                                                   //SDS_NR.SelectParameters.Add("tc2_degree", "5");
                                                                   //SDS_NR.SelectParameters.Add("Modifymode", "Insert");  
                                                                   //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                                                                   //{
                                                                   //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                                                                   //}

        //SDS_NR.DataBind();

        //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("tc2_seno", "");
            sqlCmd.Parameters.AddWithValue("tc2_year", (DateTime.Now).Year.ToString());
            sqlCmd.Parameters.AddWithValue("tc2_orgcd", oRCM.SQLInjectionReplaceAll(txt_req_dept.Value.Substring(0, 2)));
            sqlCmd.Parameters.AddWithValue("tc2_class", "L");
            sqlCmd.Parameters.AddWithValue("tc2_sn", "");
            sqlCmd.Parameters.AddWithValue("tc2_old_contno", oRCM.SQLInjectionReplaceAll(txtOldContno.Text.Trim())); //將舊案的流水號存入
            sqlCmd.Parameters.AddWithValue("tc2_compidno_all", oRCM.SQLInjectionReplaceAll(h_compno.Value));
            sqlCmd.Parameters.AddWithValue("tc2_req_dept", oRCM.SQLInjectionReplaceAll(txt_req_dept.Value.Trim()));
            sqlCmd.Parameters.AddWithValue("tc2_accept_date", oRCM.SQLInjectionReplaceAll(txt_accept_date.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("tc2_promoter_no", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
            sqlCmd.Parameters.AddWithValue("tc2_promoter_name", oRCM.SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("tc2_name", oRCM.SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
            sqlCmd.Parameters.AddWithValue("tc2_heads", "");
            sqlCmd.Parameters.AddWithValue("tc2_compaddr", "");
            sqlCmd.Parameters.AddWithValue("tc2_manage_note", "");
            sqlCmd.Parameters.AddWithValue("tc2_tort_impeach", "");
            sqlCmd.Parameters.AddWithValue("tc2_bugdet", "");
            sqlCmd.Parameters.AddWithValue("tc2_breviary", "");
            sqlCmd.Parameters.AddWithValue("tc2_prosecution_date", "");
            sqlCmd.Parameters.AddWithValue("tc2_litigation_type", "");
            sqlCmd.Parameters.AddWithValue("tc2_litigation_name", "");
            sqlCmd.Parameters.AddWithValue("tc2_litigation_target", "");
            sqlCmd.Parameters.AddWithValue("tc2_keyin_emp_no", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
            sqlCmd.Parameters.AddWithValue("tc2_keyin_emp_name", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
            sqlCmd.Parameters.AddWithValue("tc2_keyin_date", oRCM.SQLInjectionReplaceAll(lb_keyin_date.Text.Trim()));        // 建檔日期
            sqlCmd.Parameters.AddWithValue("tc2_keyin_emp_tel", "");
            sqlCmd.Parameters.AddWithValue("tc2_modify_emp_no", oRCM.SQLInjectionReplaceAll(lb_modify_emp_no.Text.Trim()));  // 修改工號
            sqlCmd.Parameters.AddWithValue("tc2_modify_emp_name", oRCM.SQLInjectionReplaceAll(lb_modify_emp_name.Text.Trim()));// 修改人
            sqlCmd.Parameters.AddWithValue("tc2_modify_date", oRCM.SQLInjectionReplaceAll(lb_modify_date.Text.Trim()));      // 修改日期
            sqlCmd.Parameters.AddWithValue("tc2_degree", "5");
            sqlCmd.Parameters.AddWithValue("Modifymode", "Insert");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_actno = dt.DefaultView;
        if (dv_actno.Count >= 1)
        {
            ViewState["seno"] = Server.HtmlEncode(dv_actno[0][0].ToString());
            Response.Redirect("./TreatyCase2_modify.aspx?seno=" + ViewState["seno"].ToString());
        }
    }

    protected void btnSendApply_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";

        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_empno').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_empno').click(function () { $('#txt_promoter_empno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (txt_name.Text == "")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name.Text').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name.Text", script_alert);
        }
        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";

        if (str_danger == "1")
            Response.Redirect("../danger.aspx");

        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            DoSaveDraft("3");
            Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "送出申請單", "", "", "treaty\\TreatyCase2.aspx");
            string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyCase2_modify.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_TreatyCase2_log";
        //SDS_log.InsertParameters.Add("seno", xID);
        //SDS_log.InsertParameters.Add("empno", ssoUser.empNo);
        //SDS_log.InsertParameters.Add("empName", ssoUser.empName.Trim());
        //SDS_log.InsertParameters.Add("txtResult", txtResult);
        //SDS_log.InsertParameters.Add("txt_meno", txtMeno);
        //SDS_log.InsertParameters.Add("xIP", xIP);
        //SDS_log.InsertParameters.Add("xApp", xApp);
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindData_Customer()
    {
        //this.SDS_company.SelectParameters.Clear();
        //this.SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //this.SDS_company.SelectCommand = "esp_TreatyCase2_MultiCustomer_List";

        //this.SDS_company.SelectParameters.Add("customers" , this.h_compno.Value.ToString());
        //this.SDS_company.DataBind();
        //SGV_company.DataBind();
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_MultiCustomer_List";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    //private string GetMultiCustomerFromScreen()
    //{
    //    string strCustomer = string.Empty;
    //    Control ctl = null;
    //    string strIDno = string.Empty;
    //    string strHeads = string.Empty;
    //    StringBuilder sb = new StringBuilder();
    //    foreach (DataGridItem it in SGV_company.Columns)
    //    {
    //        ctl = it.FindControl("TB_comp_chairman");
    //        if (ctl != null)
    //        {
    //            TextBox tB_comp_chairman = (TextBox)ctl;
    //            strHeads = tB_comp_chairman.Text;
    //            strIDno = it.Cells[1].Text.Trim();
    //            if (strIDno.Length > 0)
    //                sb.Append(string.Format("{0}㊣", strHeads));
    //        }
    //    }
    //    strCustomer = sb.ToString();
    //    if (strCustomer.Length > 0)
    //        strCustomer = strCustomer.Substring(0, strCustomer.Length - 1);
    //    return strCustomer;
    //} 
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + Server.HtmlEncode(LB.Text.ToString()) + "\");' >" + Server.HtmlEncode(LB.Text.ToString()) + "</a>";

    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace("," + e.CommandArgument, "");
            BindData_Customer();

        }
    }
}