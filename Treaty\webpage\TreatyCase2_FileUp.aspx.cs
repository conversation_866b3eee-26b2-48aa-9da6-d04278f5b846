﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TreatyCase2_FileUp : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:<PERSON>(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            //ViewState["contno"] = "201430N0002C-01";
            //ViewState["seno"] = "2604";
            if (Request.QueryString["contno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["contno"]) || (Request.QueryString["contno"].Length > 15))
                    Response.Redirect("../danger.aspx");
                ViewState["contno"] = Request.QueryString["contno"].ToString();
            }

            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
            }
            //SDS_file.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'11' ";
            //SDS_file.DataBind();


        }
    }

    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {

        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = SQLInjectionReplaceAll("esp_TreatyCase2_log");
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplaceAll(xID) );
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo) );
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplaceAll(ssoUser.empName.Trim()) );
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplaceAll(txtResult) );
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplaceAll(txtMeno) );
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplaceAll(xIP) );
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplaceAll(xApp) );
        //SDS_log.Insert();

        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_log";

            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(xIP));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));


            try
            {
                sqlConn.Open();

                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (txt_filetxt.Text.ToUpper().IndexOf("SCRIPT") >= 0)
            Response.Redirect("../danger.aspx");

        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        string FilePathString = System.Web.Configuration.WebConfigurationManager.AppSettings["FilePathString"];
        string path = string.Format("{0}\\{1}\\", FilePathString, ViewState["contno"].ToString().Substring(0, 4));
        string strPreRandom = Path.GetRandomFileName().Substring(0, 5);
        if (Request.ServerVariables["HTTP_VIA"] != null)
        {
            ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        else
        {
            ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
        }
        if (FU_up.PostedFile.ContentLength > 0)
        {
            string upFileName = FU_up.FileName.Replace("&", "＆").Replace("­", "－");
            //if (!Directory.Exists(path.Replace("/", "").Replace("..", "")))
            //{
            //    Directory.CreateDirectory(path.Replace("/", "").Replace("..", ""));
            //}
            oRCM.GetValidPathPart(FilePathString, ViewState["contno"].ToString().Substring(0, 4));
            string str_FileName = "\\DP_" + ViewState["seno"].ToString() + "_" + strPreRandom + "_" +
                                    Path.GetFileNameWithoutExtension(upFileName).Replace("/", "").Replace(".....", "").Replace("....", "").Replace("...", "").Replace("..", "") +
                                    Path.GetExtension(upFileName);

            FU_up.SaveAs(Server.HtmlEncode(path.Replace("/", "").Replace("..", "") + str_FileName));
            //System.Web.UI.WebControls.SqlDataSource SqlDataSource1 = new System.Web.UI.WebControls.SqlDataSource();
            //SqlDataSource1.ConnectionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SqlDataSource1.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SqlDataSource1.InsertCommand = SQLInjectionReplaceAll("esp_TreatyCase2_file_modify");
            //SqlDataSource1.InsertParameters.Add("req_id", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()) );
            //SqlDataSource1.InsertParameters.Add("fd_name", oRCM.SQLInjectionReplaceAll(upFileName) );
            //SqlDataSource1.InsertParameters.Add("filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text) );
            //SqlDataSource1.InsertParameters.Add("file_url", oRCM.SQLInjectionReplaceAll(path + str_FileName) );
            //SqlDataSource1.InsertParameters.Add("empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            //SqlDataSource1.InsertParameters.Add("mode", "Ins");
            //SqlDataSource1.InsertParameters.Add("fid","");
            //SqlDataSource1.Insert();

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyCase2_file_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@fd_name", oRCM.SQLInjectionReplaceAll(upFileName));
                sqlCmd.Parameters.AddWithValue("@filetxt", oRCM.SQLInjectionReplaceAll(txt_filetxt.Text));
                sqlCmd.Parameters.AddWithValue("@file_url", oRCM.SQLInjectionReplaceAll(path + str_FileName));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                sqlCmd.Parameters.AddWithValue("@mode", "Ins");
                sqlCmd.Parameters.AddWithValue("@fid", "");


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            Treaty_log(Server.HtmlEncode(ViewState["seno"].ToString()), "附件上傳", "", "", "FU_up.FileName");

            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

        }

    }
}