﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TechCase_Inspect_his.aspx.cs" Inherits="Treaty_webpage_TechCase_Inspect_his" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>案件審查</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />

    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("審查完成!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <br />
        <span class="stripeMe">

            <asp:GridView ID="gv_Inspect" BorderWidth="0px" CellPadding="0" runat="server" AutoGenerateColumns="False" OnRowDataBound="gv_Inspect_RowDataBound" Width="700px">
                <Columns>
                    <asp:BoundField DataField="tt_order" HeaderText="順序">
                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tt_empname" HeaderText="審查人">
                        <ItemStyle HorizontalAlign="Center" Width="100px" />
                    </asp:BoundField>
                    <asp:BoundField DataField="tt_inspect_desc" HeaderText="簽核意見">
                        <ItemStyle Width="350px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="簽核狀態">
                        <ItemTemplate>
                            <asp:Literal ID="lbl_Istatus" runat="server" Text='<%#System.Web.HttpUtility.HtmlEncode(Eval("tt_flag").ToString()) %>'></asp:Literal>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Center" Width="80px" />
                    </asp:TemplateField>
                    <asp:BoundField DataField="short_inspect_time" HeaderText="簽核日期">
                        <ItemStyle HorizontalAlign="center" Width="100px" />
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="修改建議">
                        <ItemTemplate>
                            <asp:GridView ID="GV_File" runat="server" AutoGenerateColumns="False" Width="100%" OnRowCommand="GV_File_RowCommand">
                                <Columns>
                                    <asp:TemplateField HeaderText="附件名稱">
                                        <ItemTemplate>
                                            <asp:LinkButton ID="lnkbtn_附件名稱" runat="server" Text='<%# Server.HtmlEncode(Eval("tcdf_doc").ToString()) %>' CommandName="xDownload" CommandArgument='<%# Eval("tcdf_no") %>'> </asp:LinkButton>
                                        </ItemTemplate>
                                        <HeaderStyle Width="550px"></HeaderStyle>
                                        <ItemStyle HorizontalAlign="Left" />
                                    </asp:TemplateField>
                                </Columns>
                            </asp:GridView>
                        </ItemTemplate>
                        <ItemStyle HorizontalAlign="Center" Width="80px" />
                    </asp:TemplateField>
                </Columns>
                <EmptyDataTemplate>無歷史資料</EmptyDataTemplate>
                <PagerSettings Position="Bottom" />
                <PagerStyle HorizontalAlign="Left" />
            </asp:GridView>
        </span>
    </form>
</body>
</html>
