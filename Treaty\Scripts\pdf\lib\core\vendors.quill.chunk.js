/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[3],{317:function(ia,y,e){(function(e){(function(e,y){ia.exports=y()})("undefined"!==typeof self?self:this,function(){return function(e){function x(da){if(y[da])return y[da].exports;var ba=y[da]={i:da,l:!1,exports:{}};e[da].call(ba.exports,ba,ba.exports,x);ba.l=!0;return ba.exports}var y={};x.m=e;x.c=y;x.d=function(e,ba,w){x.o(e,ba)||Object.defineProperty(e,ba,{configurable:!1,enumerable:!0,get:w})};x.n=function(e){var ba=e&&e.__esModule?
function(){return e["default"]}:function(){return e};x.d(ba,"a",ba);return ba};x.o=function(e,x){return Object.prototype.hasOwnProperty.call(e,x)};x.p="";return x(x.s=109)}([function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});e=ea(17);var x=ea(18),ba=ea(19),w=ea(45),z=ea(46),r=ea(47),h=ea(48),f=ea(49),n=ea(12),ca=ea(32),aa=ea(33),ha=ea(31);ea=ea(1);y.default={Scope:ea.Scope,create:ea.create,find:ea.find,query:ea.query,register:ea.register,Container:e.default,Format:x.default,Leaf:ba.default,
Embed:h.default,Scroll:w.default,Block:r.default,Inline:z.default,Text:f.default,Attributor:{Attribute:n.default,Class:ca.default,Style:aa.default,Store:ha.default}}},function(e,y){function x(f,e){void 0===e&&(e=!1);return null==f?null:null!=f[y.DATA_KEY]?f[y.DATA_KEY].blot:e?x(f.parentNode,e):null}function da(e,w){void 0===w&&(w=ca.ANY);if("string"===typeof e)var x=n[e]||r[e];else if(e instanceof Text||e.nodeType===Node.TEXT_NODE)x=n.text;else if("number"===typeof e)e&ca.LEVEL&ca.BLOCK?x=n.block:
e&ca.LEVEL&ca.INLINE&&(x=n.inline);else if(e instanceof HTMLElement){var aa=(e.getAttribute("class")||"").split(/\s+/),z;for(z in aa)if(x=h[aa[z]])break;x=x||f[e.tagName]}return null==x?null:w&ca.LEVEL&x.scope&&w&ca.TYPE&x.scope?x:null}function ba(){for(var e=[],w=0;w<arguments.length;w++)e[w]=arguments[w];if(1<e.length)return e.map(function(f){return ba(f)});var x=e[0];if("string"!==typeof x.blotName&&"string"!==typeof x.attrName)throw new z("Invalid definition");if("abstract"===x.blotName)throw new z("Cannot register abstract class");
n[x.blotName||x.attrName]=x;"string"===typeof x.keyName?r[x.keyName]=x:(null!=x.className&&(h[x.className]=x),null!=x.tagName&&(Array.isArray(x.tagName)?x.tagName=x.tagName.map(function(f){return f.toUpperCase()}):x.tagName=x.tagName.toUpperCase(),(Array.isArray(x.tagName)?x.tagName:[x.tagName]).forEach(function(e){if(null==f[e]||null==x.className)f[e]=x})));return x}var w=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,e){f.__proto__=e}||function(f,
e){for(var h in e)e.hasOwnProperty(h)&&(f[h]=e[h])};return function(e,h){function n(){this.constructor=e}f(e,h);e.prototype=null===h?Object.create(h):(n.prototype=h.prototype,new n)}}();Object.defineProperty(y,"__esModule",{value:!0});var z=function(f){function e(e){e="[Parchment] "+e;var h=f.call(this,e)||this;h.message=e;h.name=h.constructor.name;return h}w(e,f);return e}(Error);y.ParchmentError=z;var r={},h={},f={},n={};y.DATA_KEY="__blot";var ca;(function(f){f[f.TYPE=3]="TYPE";f[f.LEVEL=12]="LEVEL";
f[f.ATTRIBUTE=13]="ATTRIBUTE";f[f.BLOT=14]="BLOT";f[f.INLINE=7]="INLINE";f[f.BLOCK=11]="BLOCK";f[f.BLOCK_BLOT=10]="BLOCK_BLOT";f[f.INLINE_BLOT=6]="INLINE_BLOT";f[f.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE";f[f.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE";f[f.ANY=15]="ANY"})(ca=y.Scope||(y.Scope={}));y.create=function(f,e){var h=da(f);if(null==h)throw new z("Unable to create "+f+" blot");f=f instanceof Node||f.nodeType===Node.TEXT_NODE?f:h.create(e);return new h(f,e)};y.find=x;y.query=da;y.register=ba},function(e,
y,ea){function x(f){Array.isArray(f)?this.ops=f:null!=f&&Array.isArray(f.ops)?this.ops=f.ops:this.ops=[]}var ba=ea(51),w=ea(11),z=ea(3),r=ea(20),h=String.fromCharCode(0);x.prototype.insert=function(f,e){var h={};if(0===f.length)return this;h.insert=f;null!=e&&"object"===typeof e&&0<Object.keys(e).length&&(h.attributes=e);return this.push(h)};x.prototype["delete"]=function(f){return 0>=f?this:this.push({"delete":f})};x.prototype.retain=function(f,e){if(0>=f)return this;f={retain:f};null!=e&&"object"===
typeof e&&0<Object.keys(e).length&&(f.attributes=e);return this.push(f)};x.prototype.push=function(f){var e=this.ops.length,h=this.ops[e-1];f=z(!0,{},f);if("object"===typeof h){if("number"===typeof f["delete"]&&"number"===typeof h["delete"])return this.ops[e-1]={"delete":h["delete"]+f["delete"]},this;if("number"===typeof h["delete"]&&null!=f.insert&&(--e,h=this.ops[e-1],"object"!==typeof h))return this.ops.unshift(f),this;if(w(f.attributes,h.attributes)){if("string"===typeof f.insert&&"string"===
typeof h.insert)return this.ops[e-1]={insert:h.insert+f.insert},"object"===typeof f.attributes&&(this.ops[e-1].attributes=f.attributes),this;if("number"===typeof f.retain&&"number"===typeof h.retain)return this.ops[e-1]={retain:h.retain+f.retain},"object"===typeof f.attributes&&(this.ops[e-1].attributes=f.attributes),this}}e===this.ops.length?this.ops.push(f):this.ops.splice(e,0,f);return this};x.prototype.chop=function(){var f=this.ops[this.ops.length-1];f&&f.retain&&!f.attributes&&this.ops.pop();
return this};x.prototype.filter=function(f){return this.ops.filter(f)};x.prototype.forEach=function(f){this.ops.forEach(f)};x.prototype.map=function(f){return this.ops.map(f)};x.prototype.partition=function(f){var e=[],h=[];this.forEach(function(n){(f(n)?e:h).push(n)});return[e,h]};x.prototype.reduce=function(f,e){return this.ops.reduce(f,e)};x.prototype.changeLength=function(){return this.reduce(function(f,e){return e.insert?f+r.length(e):e.delete?f-e.delete:f},0)};x.prototype.length=function(){return this.reduce(function(f,
e){return f+r.length(e)},0)};x.prototype.slice=function(f,e){f=f||0;"number"!==typeof e&&(e=Infinity);for(var h=[],n=r.iterator(this.ops),w=0;w<e&&n.hasNext();){if(w<f)var z=n.next(f-w);else z=n.next(e-w),h.push(z);w+=r.length(z)}return new x(h)};x.prototype.compose=function(f){var e=r.iterator(this.ops);f=r.iterator(f.ops);var h=[],aa=f.peek();if(null!=aa&&"number"===typeof aa.retain&&null==aa.attributes){for(var z=aa.retain;"insert"===e.peekType()&&e.peekLength()<=z;)z-=e.peekLength(),h.push(e.next());
0<aa.retain-z&&f.next(aa.retain-z)}for(h=new x(h);e.hasNext()||f.hasNext();)if("insert"===f.peekType())h.push(f.next());else if("delete"===e.peekType())h.push(e.next());else{z=Math.min(e.peekLength(),f.peekLength());var y=e.next(z),ba=f.next(z);if("number"===typeof ba.retain){aa={};"number"===typeof y.retain?aa.retain=z:aa.insert=y.insert;if(z=r.attributes.compose(y.attributes,ba.attributes,"number"===typeof y.retain))aa.attributes=z;h.push(aa);if(!f.hasNext()&&w(h.ops[h.ops.length-1],aa))return e=
new x(e.rest()),h.concat(e).chop()}else"number"===typeof ba["delete"]&&"number"===typeof y.retain&&h.push(ba)}return h.chop()};x.prototype.concat=function(f){var e=new x(this.ops.slice());0<f.ops.length&&(e.push(f.ops[0]),e.ops=e.ops.concat(f.ops.slice(1)));return e};x.prototype.diff=function(f,e){if(this.ops===f.ops)return new x;var n=[this,f].map(function(e){return e.map(function(n){if(null!=n.insert)return"string"===typeof n.insert?n.insert:h;throw Error("diff() called "+(e===f?"on":"with")+" non-document");
}).join("")}),aa=new x;e=ba(n[0],n[1],e);var z=r.iterator(this.ops),y=r.iterator(f.ops);e.forEach(function(f){for(var e=f[1].length;0<e;){var h=0;switch(f[0]){case ba.INSERT:h=Math.min(y.peekLength(),e);aa.push(y.next(h));break;case ba.DELETE:h=Math.min(e,z.peekLength());z.next(h);aa["delete"](h);break;case ba.EQUAL:h=Math.min(z.peekLength(),y.peekLength(),e);var n=z.next(h),x=y.next(h);if(w(n.insert,x.insert))aa.retain(h,r.attributes.diff(n.attributes,x.attributes));else aa.push(x)["delete"](h)}e-=
h}});return aa.chop()};x.prototype.eachLine=function(f,e){e=e||"\n";for(var h=r.iterator(this.ops),n=new x,w=0;h.hasNext();){if("insert"!==h.peekType())return;var z=h.peek(),y=r.length(z)-h.peekLength();z="string"===typeof z.insert?z.insert.indexOf(e,y)-y:-1;if(0>z)n.push(h.next());else if(0<z)n.push(h.next(z));else{if(!1===f(n,h.next(1).attributes||{},w))return;w+=1;n=new x}}0<n.length()&&f(n,{},w)};x.prototype.transform=function(f,e){e=!!e;if("number"===typeof f)return this.transformPosition(f,
e);var h=r.iterator(this.ops);f=r.iterator(f.ops);for(var n=new x;h.hasNext()||f.hasNext();)if("insert"!==h.peekType()||!e&&"insert"===f.peekType())if("insert"===f.peekType())n.push(f.next());else{var w=Math.min(h.peekLength(),f.peekLength()),z=h.next(w),y=f.next(w);z["delete"]||(y["delete"]?n.push(y):n.retain(w,r.attributes.transform(z.attributes,y.attributes,e)))}else n.retain(r.length(h.next()));return n.chop()};x.prototype.transformPosition=function(f,e){e=!!e;for(var h=r.iterator(this.ops),n=
0;h.hasNext()&&n<=f;){var w=h.peekLength(),x=h.peekType();h.next();"delete"===x?f-=Math.min(w,f-n):("insert"===x&&(n<f||!e)&&(f+=w),n+=w)}return f};e.exports=x},function(e){function x(f,e){if("__proto__"===e){if(!w.call(f,e))return;if(h)return h(f,e).value}return f[e]}function y(f,e){r&&"__proto__"===e.name?r(f,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):f[e.name]=e.newValue}function da(f){if(!f||"[object Object]"!==z.call(f))return!1;var e=w.call(f,"constructor"),h=f.constructor&&
f.constructor.prototype&&w.call(f.constructor.prototype,"isPrototypeOf");if(f.constructor&&!e&&!h)return!1;for(var r in f);return"undefined"===typeof r||w.call(f,r)}function ba(f){return"function"===typeof Array.isArray?Array.isArray(f):"[object Array]"===z.call(f)}var w=Object.prototype.hasOwnProperty,z=Object.prototype.toString,r=Object.defineProperty,h=Object.getOwnPropertyDescriptor;e.exports=function n(){var e,h,r=arguments[0],w=1,z=arguments.length,ea=!1;"boolean"===typeof r&&(ea=r,r=arguments[1]||
{},w=2);if(null==r||"object"!==typeof r&&"function"!==typeof r)r={};for(;w<z;++w){var ha=arguments[w];if(null!=ha)for(e in ha){var fa=x(r,e);var ia=x(ha,e);r!==ia&&(ea&&ia&&(da(ia)||(h=ba(ia)))?(h?(h=!1,fa=fa&&ba(fa)?fa:[]):fa=fa&&da(fa)?fa:{},y(r,{name:e,newValue:n(ea,fa,ia)})):"undefined"!==typeof ia&&y(r,{name:e,newValue:ia}))}}return r}},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e){if(!(f instanceof e))throw new TypeError("Cannot call a class as a function");
}function w(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}function r(f){var e=
1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(null==f)return e;"function"===typeof f.formats&&(e=(0,n.default)(e,f.formats()));return null==f.parent||"scroll"==f.parent.blotName||f.parent.statics.scope!==f.statics.scope?e:r(f.parent,e)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.BlockEmbed=y.bubbleFormats=void 0;var h=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,
n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}(),f=function ra(f,e,h){null===f&&(f=Function.prototype);var n=Object.getOwnPropertyDescriptor(f,e);if(void 0===n){if(f=Object.getPrototypeOf(f),null!==f)return ra(f,e,h)}else{if("value"in n)return n.value;e=n.get;return void 0===e?void 0:e.call(h)}};e=ea(3);var n=x(e);e=ea(2);var ca=x(e);e=ea(0);var aa=x(e);e=ea(16);var ha=x(e);e=ea(6);e=x(e);ea=ea(7);ea=x(ea);var fa=function(e){function r(){ba(this,r);return w(this,(r.__proto__||
Object.getPrototypeOf(r)).apply(this,arguments))}z(r,e);h(r,[{key:"attach",value:function(){f(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"attach",this).call(this);this.attributes=new aa.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new ca.default).insert(this.value(),(0,n.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(f,e){f=aa.default.query(f,aa.default.Scope.BLOCK_ATTRIBUTE);null!=f&&this.attributes.attribute(f,e)}},
{key:"formatAt",value:function(f,e,h,n){this.format(h,n)}},{key:"insertAt",value:function(e,h,n){"string"===typeof h&&h.endsWith("\n")?(n=aa.default.create(ia.blotName),this.parent.insertBefore(n,0===e?this:this.next),n.insertAt(0,h.slice(0,-1))):f(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"insertAt",this).call(this,e,h,n)}}]);return r}(aa.default.Embed);fa.scope=aa.default.Scope.BLOCK_BLOT;var ia=function(e){function n(f){ba(this,n);f=w(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,
f));f.cache={};return f}z(n,e);h(n,[{key:"delta",value:function(){null==this.cache.delta&&(this.cache.delta=this.descendants(aa.default.Leaf).reduce(function(f,e){return 0===e.length()?f:f.insert(e.value(),r(e))},new ca.default).insert("\n",r(this)));return this.cache.delta}},{key:"deleteAt",value:function(e,h){f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"deleteAt",this).call(this,e,h);this.cache={}}},{key:"formatAt",value:function(e,h,r,w){0>=h||(aa.default.query(r,aa.default.Scope.BLOCK)?
e+h===this.length()&&this.format(r,w):f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"formatAt",this).call(this,e,Math.min(h,this.length()-e-1),r,w),this.cache={})}},{key:"insertAt",value:function(e,h,r){if(null!=r)return f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"insertAt",this).call(this,e,h,r);if(0!==h.length){h=h.split("\n");r=h.shift();0<r.length&&(e<this.length()-1||null==this.children.tail?f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"insertAt",
this).call(this,Math.min(e,this.length()-1),r):this.children.tail.insertAt(this.children.tail.length(),r),this.cache={});var w=this;h.reduce(function(f,e){w=w.split(f,!0);w.insertAt(0,e);return e.length},e+r.length)}}},{key:"insertBefore",value:function(e,h){var r=this.children.head;f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"insertBefore",this).call(this,e,h);r instanceof ha.default&&r.remove();this.cache={}}},{key:"length",value:function(){null==this.cache.length&&(this.cache.length=
f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"length",this).call(this)+1);return this.cache.length}},{key:"moveChildren",value:function(e,h){f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"moveChildren",this).call(this,e,h);this.cache={}}},{key:"optimize",value:function(e){f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"optimize",this).call(this,e);this.cache={}}},{key:"path",value:function(e){return f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),
"path",this).call(this,e,!0)}},{key:"removeChild",value:function(e){f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"removeChild",this).call(this,e);this.cache={}}},{key:"split",value:function(e){var h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1;if(h&&(0===e||e>=this.length()-1)){h=this.clone();if(0===e)return this.parent.insertBefore(h,this),this;this.parent.insertBefore(h,this.next);return h}h=f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"split",this).call(this,
e,h);this.cache={};return h}}]);return n}(aa.default.Block);ia.blotName="block";ia.tagName="P";ia.defaultChild="break";ia.allowedChildren=[e.default,aa.default.Embed,ea.default];y.bubbleFormats=r;y.BlockEmbed=fa;y.default=ia},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e,h){e in f?Object.defineProperty(f,e,{value:h,enumerable:!0,configurable:!0,writable:!0}):f[e]=h;return f}function w(f,e){e=(0,za.default)(!0,{container:f,modules:{clipboard:!0,keyboard:!0,history:!0}},
e);if(e.theme&&e.theme!==oa.DEFAULTS.theme){if(e.theme=oa.import("themes/"+e.theme),null==e.theme)throw Error("Invalid theme "+e.theme+". Did you register it?");}else e.theme=Da.default;f=(0,za.default)(!0,{},e.theme.DEFAULTS);[f,e].forEach(function(f){f.modules=f.modules||{};Object.keys(f.modules).forEach(function(e){!0===f.modules[e]&&(f.modules[e]={})})});var h=Object.keys(f.modules).concat(Object.keys(e.modules)).reduce(function(f,e){var h=oa.import("modules/"+e);null==h?sa.error("Cannot load "+
e+" module. Are you sure you registered it?"):f[e]=h.DEFAULTS||{};return f},{});null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar});e=(0,za.default)(!0,{},oa.DEFAULTS,{modules:h},f,e);["bounds","container","scrollingContainer"].forEach(function(f){"string"===typeof e[f]&&(e[f]=document.querySelector(e[f]))});e.modules=Object.keys(e.modules).reduce(function(f,h){e.modules[h]&&(f[h]=e.modules[h]);return f},{});return e}function z(f,
e,n,r){if(this.options.strict&&!this.isEnabled()&&e===fa.default.sources.USER)return new aa.default;var w=null==n?null:this.getSelection(),x=this.editor.delta;f=f();null!=w&&(!0===n&&(n=w.index),null==r?w=h(w,f,e):0!==r&&(w=h(w,n,r,e)),this.setSelection(w,fa.default.sources.SILENT));if(0<f.length()){var z;n=[fa.default.events.TEXT_CHANGE,f,x,e];(z=this.emitter).emit.apply(z,[fa.default.events.EDITOR_CHANGE].concat(n));if(e!==fa.default.sources.SILENT){var ca;(ca=this.emitter).emit.apply(ca,n)}}return f}
function r(e,h,n,r,w){var x={};"number"===typeof e.index&&"number"===typeof e.length?("number"!==typeof h?(w=r,r=n,n=h,h=e.length):h=e.length,e=e.index):"number"!==typeof h&&(w=r,r=n,n=h,h=0);"object"===("undefined"===typeof n?"undefined":f(n))?(x=n,w=r):"string"===typeof n&&(null!=r?x[n]=r:w=n);w=w||fa.default.sources.API;return[e,h,x,w]}function h(f,e,h,r){if(null==f)return null;var w=void 0,x=void 0;e instanceof aa.default?(w=[f.index,f.index+f.length].map(function(f){return e.transformPosition(f,
r!==fa.default.sources.USER)}),f=n(w,2),w=f[0],x=f[1]):(w=[f.index,f.index+f.length].map(function(f){return f<e||f===e&&r===fa.default.sources.USER?f:0<=h?f+h:Math.max(e,f+h)}),f=n(w,2),w=f[0],x=f[1]);return new la.Range(w,x-w)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.overload=y.expandConfig=void 0;var f="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?
"symbol":typeof f},n=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==e);n=!0);}catch(Ba){r=!0,w=Ba}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ca=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=
n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}();ea(50);e=ea(2);var aa=x(e);e=ea(14);var ha=x(e);e=ea(8);var fa=x(e);e=ea(9);e=x(e);var ia=ea(0),xa=x(ia),la=ea(15),na=x(la);ia=ea(3);var za=x(ia);ia=ea(10);var ra=x(ia);ea=ea(34);var Da=x(ea),sa=(0,ra.default)("quill"),oa=function(){function f(e){var h=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(this instanceof
f))throw new TypeError("Cannot call a class as a function");this.options=w(e,n);this.container=this.options.container;if(null==this.container)return sa.error("Invalid Quill container",e);this.options.debug&&f.debug(this.options.debug);n=this.container.innerHTML.trim();this.container.classList.add("ql-container");this.container.innerHTML="";this.container.__quill=this;this.root=this.addContainer("ql-editor");this.root.classList.add("ql-blank");this.root.setAttribute("data-gramm",!1);this.scrollingContainer=
this.options.scrollingContainer||this.root;this.emitter=new fa.default;this.scroll=xa.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats});this.editor=new ha.default(this.scroll);this.selection=new na.default(this.scroll,this.emitter);this.theme=new this.options.theme(this,this.options);this.keyboard=this.theme.addModule("keyboard");this.clipboard=this.theme.addModule("clipboard");this.history=this.theme.addModule("history");this.theme.init();this.emitter.on(fa.default.events.EDITOR_CHANGE,
function(f){f===fa.default.events.TEXT_CHANGE&&h.root.classList.toggle("ql-blank",h.editor.isBlank())});this.emitter.on(fa.default.events.SCROLL_UPDATE,function(f,e){var n=h.selection.lastRange,r=n&&0===n.length?n.index:void 0;z.call(h,function(){return h.editor.update(null,e,r)},f)});n=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>");this.setContents(n);this.history.clear();this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder);
this.options.readOnly&&this.disable()}ca(f,null,[{key:"debug",value:function(f){!0===f&&(f="log");ra.default.level(f)}},{key:"find",value:function(f){return f.__quill||xa.default.find(f)}},{key:"import",value:function(f){null==this.imports[f]&&sa.error("Cannot import "+f+". Are you sure it was registered?");return this.imports[f]}},{key:"register",value:function(f,e){var h=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:!1;"string"!==typeof f?(n=f.attrName||f.blotName,"string"===typeof n?
this.register("formats/"+n,f,e):Object.keys(f).forEach(function(n){h.register(n,f[n],e)})):(null==this.imports[f]||n||sa.warn("Overwriting "+f+" with",e),this.imports[f]=e,(f.startsWith("blots/")||f.startsWith("formats/"))&&"abstract"!==e.blotName?xa.default.register(e):f.startsWith("modules")&&"function"===typeof e.register&&e.register())}}]);ca(f,[{key:"addContainer",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if("string"===typeof f){var h=f;f=document.createElement("div");
f.classList.add(h)}this.container.insertBefore(f,e);return f}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(f,e,h){var w=this;h=r(f,e,h);h=n(h,4);f=h[0];e=h[1];h=h[3];return z.call(this,function(){return w.editor.deleteText(f,e)},h,f,-1*e)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:!0;this.scroll.enable(f);this.container.classList.toggle("ql-disabled",
!f)}},{key:"focus",value:function(){var f=this.scrollingContainer.scrollTop;this.selection.focus();this.scrollingContainer.scrollTop=f;this.scrollIntoView()}},{key:"format",value:function(f,e){var h=this;return z.call(this,function(){var n=h.getSelection(!0),r=new aa.default;if(null==n)return r;if(xa.default.query(f,xa.default.Scope.BLOCK))r=h.editor.formatLine(n.index,n.length,ba({},f,e));else{if(0===n.length)return h.selection.format(f,e),r;r=h.editor.formatText(n.index,n.length,ba({},f,e))}h.setSelection(n,
fa.default.sources.SILENT);return r},2<arguments.length&&void 0!==arguments[2]?arguments[2]:fa.default.sources.API)}},{key:"formatLine",value:function(f,e,h,w,x){var aa=this,ca=void 0;h=r(f,e,h,w,x);h=n(h,4);f=h[0];e=h[1];ca=h[2];x=h[3];return z.call(this,function(){return aa.editor.formatLine(f,e,ca)},x,f,0)}},{key:"formatText",value:function(f,e,h,w,x){var aa=this,ca=void 0;h=r(f,e,h,w,x);h=n(h,4);f=h[0];e=h[1];ca=h[2];x=h[3];return z.call(this,function(){return aa.editor.formatText(f,e,ca)},x,
f,0)}},{key:"getBounds",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;e="number"===typeof f?this.selection.getBounds(f,e):this.selection.getBounds(f.index,f.length);var h=this.container.getBoundingClientRect();return{bottom:e.bottom-h.top,height:e.height,left:e.left-h.left,right:e.right-h.left,top:e.top-h.top,width:e.width}}},{key:"getContents",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?
arguments[1]:this.getLength()-f;f=r(f,e);e=n(f,2);f=e[0];e=e[1];return this.editor.getContents(f,e)}},{key:"getFormat",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return"number"===typeof f?this.editor.getFormat(f,e):this.editor.getFormat(f.index,f.length)}},{key:"getIndex",value:function(f){return f.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},
{key:"getLeaf",value:function(f){return this.scroll.leaf(f)}},{key:"getLine",value:function(f){return this.scroll.line(f)}},{key:"getLines",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!==typeof f?this.scroll.lines(f.index,f.length):this.scroll.lines(f,e)}},{key:"getModule",value:function(f){return this.theme.modules[f]}},{key:"getSelection",value:function(){0<arguments.length&&
void 0!==arguments[0]&&arguments[0]&&this.focus();this.update();return this.selection.getRange()[0]}},{key:"getText",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.getLength()-f;f=r(f,e);e=n(f,2);f=e[0];e=e[1];return this.editor.getText(f,e)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(e,h,n){var r=this;return z.call(this,function(){return r.editor.insertEmbed(e,
h,n)},3<arguments.length&&void 0!==arguments[3]?arguments[3]:f.sources.API,e)}},{key:"insertText",value:function(f,e,h,w,x){var aa=this,ca=void 0;h=r(f,0,h,w,x);h=n(h,4);f=h[0];ca=h[2];x=h[3];return z.call(this,function(){return aa.editor.insertText(f,e,ca)},x,f,e.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,
arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(f,e,h){this.clipboard.dangerouslyPasteHTML(f,e,h)}},{key:"removeFormat",value:function(f,e,h){var w=this;h=r(f,e,h);h=n(h,4);f=h[0];e=h[1];h=h[3];return z.call(this,function(){return w.editor.removeFormat(f,e)},h,f)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(f){var e=this;return z.call(this,
function(){f=new aa.default(f);var h=e.getLength();h=e.editor.deleteText(0,h);var n=e.editor.applyDelta(f),r=n.ops[n.ops.length-1];null!=r&&"string"===typeof r.insert&&"\n"===r.insert[r.insert.length-1]&&(e.editor.deleteText(e.getLength()-1,1),n.delete(1));return h.compose(n)},1<arguments.length&&void 0!==arguments[1]?arguments[1]:fa.default.sources.API)}},{key:"setSelection",value:function(e,h,w){null==e?this.selection.setRange(null,h||f.sources.API):(e=r(e,h,w),w=n(e,4),e=w[0],h=w[1],w=w[3],this.selection.setRange(new la.Range(e,
h),w),w!==fa.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer))}},{key:"setText",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:fa.default.sources.API,h=(new aa.default).insert(f);return this.setContents(h,e)}},{key:"update",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:fa.default.sources.USER,e=this.scroll.update(f);this.selection.update(f);return e}},{key:"updateContents",value:function(f){var e=this,h=
1<arguments.length&&void 0!==arguments[1]?arguments[1]:fa.default.sources.API;return z.call(this,function(){f=new aa.default(f);return e.editor.applyDelta(f,h)},h,!0)}}]);return f}();oa.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"};oa.events=fa.default.events;oa.sources=fa.default.sources;oa.version="1.3.7";oa.imports={delta:aa.default,parchment:xa.default,"core/module":e.default,"core/theme":Da.default};y.expandConfig=
w;y.overload=r;y.default=oa},function(e,y,ea){function x(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||
!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),w=function ca(e,f,n){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};e=(e=ea(7))&&e.__esModule?e:{default:e};var z=(ea=ea(0))&&ea.__esModule?ea:{default:ea};ea=function(e){function f(){if(!(this instanceof
f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(f,e);ba(f,[{key:"formatAt",value:function(e,h,x,y){0>f.compare(this.statics.blotName,x)&&z.default.query(x,z.default.Scope.BLOT)?(e=this.isolate(e,h),y&&e.wrap(x,y)):w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),
"formatAt",this).call(this,e,h,x,y)}},{key:"optimize",value:function(e){w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"optimize",this).call(this,e);this.parent instanceof f&&0<f.compare(this.statics.blotName,this.parent.statics.blotName)&&(e=this.parent.isolate(this.offset(),this.length()),this.moveChildren(e),e.wrap(this))}}],[{key:"compare",value:function(e,h){var n=f.order.indexOf(e),w=f.order.indexOf(h);return 0<=n||0<=w?n-w:e===h?0:e<h?-1:1}}]);return f}(z.default.Inline);ea.allowedChildren=
[ea,z.default.Embed,e.default];ea.order="cursor inline underline strike italic bold script link code".split(" ");y.default=ea},function(e,y,ea){function x(e,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);e.prototype=Object.create(w&&w.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(e,w):e.__proto__=w)}Object.defineProperty(y,"__esModule",{value:!0});
e=function(e){function w(){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);return w}(function(e){return e&&e.__esModule?e:{default:e}}(ea(0)).default.Text);y.default=e},function(e,y,ea){function x(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+
typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),w=function ca(e,
f,n){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};e=(e=ea(54))&&e.__esModule?e:{default:e};ea=(ea=ea(10))&&ea.__esModule?ea:{default:ea};var z=(0,ea.default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(e){document.addEventListener(e,function(){for(var f=arguments.length,e=Array(f),
h=0;h<f;h++)e[h]=arguments[h];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(f){if(f.__quill&&f.__quill.emitter){var h;(h=f.__quill.emitter).handleDOM.apply(h,e)}})})});ea=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).call(this);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");e=!e||"object"!==typeof e&&"function"!==typeof e?
this:e;e.listeners={};e.on("error",z.error);return e}x(f,e);ba(f,[{key:"emit",value:function(){z.log.apply(z,arguments);w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(f){for(var e=arguments.length,h=Array(1<e?e-1:0),n=1;n<e;n++)h[n-1]=arguments[n];(this.listeners[f.type]||[]).forEach(function(e){var n=e.node;e=e.handler;(f.target===n||n.contains(f.target))&&e.apply(void 0,[f].concat(h))})}},{key:"listenDOM",value:function(f,
e,h){this.listeners[f]||(this.listeners[f]=[]);this.listeners[f].push({node:e,handler:h})}}]);return f}(e.default);ea.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"};ea.sources={API:"api",SILENT:"silent",USER:"user"};y.default=ea},function(e,y){Object.defineProperty(y,"__esModule",{value:!0});e=function ba(e){var w=1<arguments.length&&void 0!==
arguments[1]?arguments[1]:{};if(!(this instanceof ba))throw new TypeError("Cannot call a class as a function");this.quill=e;this.options=w};e.DEFAULTS={};y.default=e},function(e,y){function x(e){if(ba.indexOf(e)<=ba.indexOf(w)){for(var r,h=arguments.length,f=Array(1<h?h-1:0),n=1;n<h;n++)f[n-1]=arguments[n];(r=console)[e].apply(r,f)}}function da(e){return ba.reduce(function(r,h){r[h]=x.bind(console,h,e);return r},{})}Object.defineProperty(y,"__esModule",{value:!0});var ba=["error","warn","log","info"],
w="warn";x.level=da.level=function(e){w=e};y.default=da},function(e,y,ea){function x(f){return!f||"object"!==typeof f||"number"!==typeof f.length||"function"!==typeof f.copy||"function"!==typeof f.slice||0<f.length&&"number"!==typeof f[0]?!1:!0}function ba(f,e,ca){var n;if(null===f||void 0===f||null===e||void 0===e||f.prototype!==e.prototype)return!1;if(r(f)){if(!r(e))return!1;f=w.call(f);e=w.call(e);return h(f,e,ca)}if(x(f)){if(!x(e)||f.length!==e.length)return!1;for(n=0;n<f.length;n++)if(f[n]!==
e[n])return!1;return!0}try{var y=z(f);var ba=z(e)}catch(pa){return!1}if(y.length!=ba.length)return!1;y.sort();ba.sort();for(n=y.length-1;0<=n;n--)if(y[n]!=ba[n])return!1;for(n=y.length-1;0<=n;n--)if(ba=y[n],!h(f[ba],e[ba],ca))return!1;return typeof f===typeof e}var w=Array.prototype.slice,z=ea(52),r=ea(53),h=e.exports=function(f,e,h){h||(h={});return f===e?!0:f instanceof Date&&e instanceof Date?f.getTime()===e.getTime():!f||!e||"object"!=typeof f&&"object"!=typeof e?h.strict?f===e:f==e:ba(f,e,h)}},
function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});var x=ea(1);e=function(){function e(e,z,r){void 0===r&&(r={});this.attrName=e;this.keyName=z;e=x.Scope.TYPE&x.Scope.ATTRIBUTE;this.scope=null!=r.scope?r.scope&x.Scope.LEVEL|e:x.Scope.ATTRIBUTE;null!=r.whitelist&&(this.whitelist=r.whitelist)}e.keys=function(e){return[].map.call(e.attributes,function(e){return e.name})};e.prototype.add=function(e,x){if(!this.canAdd(e,x))return!1;e.setAttribute(this.keyName,x);return!0};e.prototype.canAdd=
function(e,z){return null==x.query(e,x.Scope.BLOT&(this.scope|x.Scope.TYPE))?!1:null==this.whitelist?!0:"string"===typeof z?-1<this.whitelist.indexOf(z.replace(/["']/g,"")):-1<this.whitelist.indexOf(z)};e.prototype.remove=function(e){e.removeAttribute(this.keyName)};e.prototype.value=function(e){var w=e.getAttribute(this.keyName);return this.canAdd(e,w)&&w?w:""};return e}();y.default=e},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e){if(!(f instanceof e))throw new TypeError("Cannot call a class as a function");
}function w(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(y,
"__esModule",{value:!0});y.default=y.Code=void 0;var r=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==e);n=!0);}catch(sa){r=!0,w=sa}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function(){function f(f,e){for(var h=0;h<
e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}(),f=function na(f,e,h){null===f&&(f=Function.prototype);var n=Object.getOwnPropertyDescriptor(f,e);if(void 0===n){if(f=Object.getPrototypeOf(f),null!==f)return na(f,e,h)}else{if("value"in n)return n.value;e=n.get;return void 0===e?void 0:e.call(h)}};e=ea(2);var n=x(e);e=ea(0);var ca=x(e);e=ea(4);e=x(e);
var aa=ea(6);aa=x(aa);ea=ea(7);var ha=x(ea);ea=function(f){function e(){ba(this,e);return w(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}z(e,f);return e}(aa.default);ea.blotName="code";ea.tagName="CODE";e=function(e){function x(){ba(this,x);return w(this,(x.__proto__||Object.getPrototypeOf(x)).apply(this,arguments))}z(x,e);h(x,[{key:"delta",value:function(){var f=this,e=this.domNode.textContent;e.endsWith("\n")&&(e=e.slice(0,-1));return e.split("\n").reduce(function(e,h){return e.insert(h).insert("\n",
f.formats())},new n.default)}},{key:"format",value:function(e,h){if(e!==this.statics.blotName||!h){var n=this.descendant(ha.default,this.length()-1);n=r(n,1)[0];null!=n&&n.deleteAt(n.length()-1,1);f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"format",this).call(this,e,h)}}},{key:"formatAt",value:function(f,e,h,n){if(0!==e&&null!=ca.default.query(h,ca.default.Scope.BLOCK)&&(h!==this.statics.blotName||n!==this.statics.formats(this.domNode))){var r=this.newlineIndex(f);if(!(0>r||r>=f+
e)){var w=this.newlineIndex(f,!0)+1;r=r-w+1;var aa=this.isolate(w,r),z=aa.next;aa.format(h,n);z instanceof x&&z.formatAt(0,f-w+e-r,h,n)}}}},{key:"insertAt",value:function(f,e,h){null==h&&(f=this.descendant(ha.default,f),f=r(f,2),f[0].insertAt(f[1],e))}},{key:"length",value:function(){var f=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?f:f+1}},{key:"newlineIndex",value:function(f){if(1<arguments.length&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,
f).lastIndexOf("\n");var e=this.domNode.textContent.slice(f).indexOf("\n");return-1<e?f+e:-1}},{key:"optimize",value:function(e){this.domNode.textContent.endsWith("\n")||this.appendChild(ca.default.create("text","\n"));f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"optimize",this).call(this,e);var h=this.next;null!=h&&h.prev===this&&h.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===h.statics.formats(h.domNode)&&(h.optimize(e),h.moveChildren(this),h.remove())}},
{key:"replace",value:function(e){f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"replace",this).call(this,e);[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(f){var e=ca.default.find(f);null==e?f.parentNode.removeChild(f):e instanceof ca.default.Embed?e.remove():e.unwrap()})}}],[{key:"create",value:function(e){e=f(x.__proto__||Object.getPrototypeOf(x),"create",this).call(this,e);e.setAttribute("spellcheck",!1);return e}},{key:"formats",value:function(){return!0}}]);
return x}(e.default);e.blotName="code-block";e.tagName="PRE";e.TAB="  ";y.Code=ea;y.default=e},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e,h){e in f?Object.defineProperty(f,e,{value:h,enumerable:!0,configurable:!0,writable:!0}):f[e]=h;return f}function w(f,e){return Object.keys(e).reduce(function(h,n){if(null==f[n])return h;e[n]===f[n]?h[n]=e[n]:Array.isArray(e[n])?0>e[n].indexOf(f[n])&&(h[n]=e[n].concat([f[n]])):h[n]=[e[n],f[n]];return h},{})}function z(f){return f.reduce(function(f,
e){if(1===e.insert){var h=(0,na.default)(e.attributes);delete h.image;return f.insert({image:e.attributes.image},h)}null==e.attributes||!0!==e.attributes.list&&!0!==e.attributes.bullet||(e=(0,na.default)(e),e.attributes.list?e.attributes.list="ordered":(e.attributes.list="bullet",delete e.attributes.bullet));return"string"===typeof e.insert?(h=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),f.insert(h,e.attributes)):f.push(e)},new n.default)}Object.defineProperty(y,"__esModule",{value:!0});var r=
"function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},h=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==e);n=!0);}catch(Ea){r=!0,w=Ea}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;
}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),f=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}();e=ea(2);var n=x(e);e=ea(20);var ca=x(e);e=ea(0);var aa=x(e);e=ea(13);var ha=x(e);e=ea(24);var fa=x(e),ia=ea(4),xa=x(ia);e=ea(16);var la=x(e);e=ea(21);var na=x(e);e=
ea(11);var za=x(e);ea=ea(3);var ra=x(ea),Da=/^[ -~]*$/;ea=function(){function e(f){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.scroll=f;this.delta=this.getDelta()}f(e,[{key:"applyDelta",value:function(f){var e=this,n=!1;this.scroll.update();var w=this.scroll.length();this.scroll.batchStart();f=z(f);f.reduce(function(f,x){var z=x.retain||x.delete||x.insert.length||1,y=x.attributes||{};if(null!=x.insert){if("string"===typeof x.insert){x=x.insert;x.endsWith("\n")&&
n&&(n=!1,x=x.slice(0,-1));f>=w&&!x.endsWith("\n")&&(n=!0);e.scroll.insertAt(f,x);x=e.scroll.line(f);x=h(x,2);var ba=x[0],da=x[1];x=(0,ra.default)({},(0,ia.bubbleFormats)(ba));ba instanceof xa.default&&(ba=ba.descendant(aa.default.Leaf,da),ba=h(ba,1)[0],x=(0,ra.default)(x,(0,ia.bubbleFormats)(ba)));y=ca.default.attributes.diff(x,y)||{}}else if("object"===r(x.insert)){ba=Object.keys(x.insert)[0];if(null==ba)return f;e.scroll.insertAt(f,ba,x.insert[ba])}w+=z}Object.keys(y).forEach(function(h){e.scroll.formatAt(f,
z,h,y[h])});return f+z},0);f.reduce(function(f,h){return"number"===typeof h.delete?(e.scroll.deleteAt(f,h.delete),f):f+(h.retain||h.insert.length||1)},0);this.scroll.batchEnd();return this.update(f)}},{key:"deleteText",value:function(f,e){this.scroll.deleteAt(f,e);return this.update((new n.default).retain(f).delete(e))}},{key:"formatLine",value:function(f,e){var h=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update();Object.keys(r).forEach(function(n){if(null==h.scroll.whitelist||
h.scroll.whitelist[n]){var w=h.scroll.lines(f,Math.max(e,1)),x=e;w.forEach(function(e){var w=e.length();if(e instanceof ha.default){var aa=f-e.offset(h.scroll),z=e.newlineIndex(aa+x)-aa+1;e.formatAt(aa,z,n,r[n])}else e.format(n,r[n]);x-=w})}});this.scroll.optimize();return this.update((new n.default).retain(f).retain(e,(0,na.default)(r)))}},{key:"formatText",value:function(f,e){var h=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Object.keys(r).forEach(function(n){h.scroll.formatAt(f,
e,n,r[n])});return this.update((new n.default).retain(f).retain(e,(0,na.default)(r)))}},{key:"getContents",value:function(f,e){return this.delta.slice(f,f+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(f,e){return f.concat(e.delta())},new n.default)}},{key:"getFormat",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=[],r=[];0===e?this.scroll.path(f).forEach(function(f){f=h(f,1)[0];f instanceof xa.default?n.push(f):f instanceof aa.default.Leaf&&
r.push(f)}):(n=this.scroll.lines(f,e),r=this.scroll.descendants(aa.default.Leaf,f,e));e=[n,r].map(function(f){if(0===f.length)return{};for(var e=(0,ia.bubbleFormats)(f.shift());0<Object.keys(e).length;){var h=f.shift();if(null==h)break;e=w((0,ia.bubbleFormats)(h),e)}return e});return ra.default.apply(ra.default,e)}},{key:"getText",value:function(f,e){return this.getContents(f,e).filter(function(f){return"string"===typeof f.insert}).map(function(f){return f.insert}).join("")}},{key:"insertEmbed",value:function(f,
e,h){this.scroll.insertAt(f,e,h);return this.update((new n.default).retain(f).insert(ba({},e,h)))}},{key:"insertText",value:function(f,e){var h=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n");this.scroll.insertAt(f,e);Object.keys(r).forEach(function(n){h.scroll.formatAt(f,e.length,n,r[n])});return this.update((new n.default).retain(f).insert(e,(0,na.default)(r)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;
if(1<this.scroll.children.length)return!1;var f=this.scroll.children.head;return f.statics.blotName!==xa.default.blotName||1<f.children.length?!1:f.children.head instanceof la.default}},{key:"removeFormat",value:function(f,e){var r=this.getText(f,e),w=this.scroll.line(f+e),x=h(w,2);w=x[0];x=x[1];var aa=0,z=new n.default;null!=w&&(aa=w instanceof ha.default?w.newlineIndex(x)-x+1:w.length()-x,z=w.delta().slice(x,x+aa-1).insert("\n"));e=this.getContents(f,e+aa).diff((new n.default).insert(r).concat(z));
f=(new n.default).retain(f).concat(e);return this.applyDelta(f)}},{key:"update",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0,r=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(Da)&&aa.default.find(e[0].target)){var w=aa.default.find(e[0].target),x=(0,ia.bubbleFormats)(w),z=w.offset(this.scroll);e=e[0].oldValue.replace(fa.default.CONTENTS,"");e=(new n.default).insert(e);
w=(new n.default).insert(w.value());f=(new n.default).retain(z).concat(e.diff(w,h)).reduce(function(f,e){return e.insert?f.insert(e.insert,x):f.push(e)},new n.default);this.delta=r.compose(f)}else this.delta=this.getDelta(),f&&(0,za.default)(r.compose(f),this.delta)||(f=r.diff(this.delta,h));return f}}]);return e}();y.default=ea},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f){if(Array.isArray(f)){for(var e=0,h=Array(f.length);e<f.length;e++)h[e]=f[e];return h}return Array.from(f)}
function w(f,e){if(!(f instanceof e))throw new TypeError("Cannot call a class as a function");}function z(f,e){try{e.parentNode}catch(la){return!1}e instanceof Text&&(e=e.parentNode);return f.contains(e)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.Range=void 0;var r=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==
e);n=!0);}catch(oa){r=!0,w=oa}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}();e=ea(0);var f=x(e);e=ea(21);var n=x(e);e=ea(11);var ca=x(e);e=ea(8);
var aa=x(e);ea=ea(10);ea=x(ea);var ha=(0,ea.default)("quill:selection"),fa=function la(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;w(this,la);this.index=f;this.length=e};ea=function(){function e(h,n){var r=this;w(this,e);this.emitter=n;this.scroll=h;this.mouseDown=this.composing=!1;this.root=this.scroll.domNode;this.cursor=f.default.create("cursor",this);this.lastRange=this.savedRange=new fa(0,0);this.handleComposition();this.handleDragging();this.emitter.listenDOM("selectionchange",
document,function(){r.mouseDown||setTimeout(r.update.bind(r,aa.default.sources.USER),1)});this.emitter.on(aa.default.events.EDITOR_CHANGE,function(f,e){f===aa.default.events.TEXT_CHANGE&&0<e.length()&&r.update(aa.default.sources.SILENT)});this.emitter.on(aa.default.events.SCROLL_BEFORE_UPDATE,function(){if(r.hasFocus()){var f=r.getNativeRange();if(null!=f&&f.start.node!==r.cursor.textNode)r.emitter.once(aa.default.events.SCROLL_UPDATE,function(){try{r.setNativeRange(f.start.node,f.start.offset,f.end.node,
f.end.offset)}catch(Da){}})}});this.emitter.on(aa.default.events.SCROLL_OPTIMIZE,function(f,e){e.range&&(f=e.range,r.setNativeRange(f.startNode,f.startOffset,f.endNode,f.endOffset))});this.update(aa.default.sources.SILENT)}h(e,[{key:"handleComposition",value:function(){var f=this;this.root.addEventListener("compositionstart",function(){f.composing=!0});this.root.addEventListener("compositionend",function(){f.composing=!1;if(f.cursor.parent){var e=f.cursor.restore();e&&setTimeout(function(){f.setNativeRange(e.startNode,
e.startOffset,e.endNode,e.endOffset)},1)}})}},{key:"handleDragging",value:function(){var f=this;this.emitter.listenDOM("mousedown",document.body,function(){f.mouseDown=!0});this.emitter.listenDOM("mouseup",document.body,function(){f.mouseDown=!1;f.update(aa.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(e,h){if(null==this.scroll.whitelist||this.scroll.whitelist[e]){this.scroll.update();var n=
this.getNativeRange();if(null!=n&&n.native.collapsed&&!f.default.query(e,f.default.Scope.BLOCK)){if(n.start.node!==this.cursor.textNode){var r=f.default.find(n.start.node,!1);if(null==r)return;r instanceof f.default.Leaf?(n=r.split(n.start.offset),r.parent.insertBefore(this.cursor,n)):r.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(e,h);this.scroll.optimize();this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length);this.update()}}}},{key:"getBounds",
value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,h=this.scroll.length();f=Math.min(f,h-1);e=Math.min(f+e,h-1)-f;h=this.scroll.leaf(f);var n=r(h,2);h=n[0];var w=n[1];if(null==h)return null;n=h.position(w,!0);n=r(n,2);var x=n[0];w=n[1];n=document.createRange();if(0<e){n.setStart(x,w);e=this.scroll.leaf(f+e);e=r(e,2);h=e[0];w=e[1];if(null==h)return null;e=h.position(w,!0);e=r(e,2);x=e[0];w=e[1];n.setEnd(x,w);return n.getBoundingClientRect()}e="left";x instanceof Text?
(w<x.data.length?(n.setStart(x,w),n.setEnd(x,w+1)):(n.setStart(x,w-1),n.setEnd(x,w),e="right"),h=n.getBoundingClientRect()):(h=h.domNode.getBoundingClientRect(),0<w&&(e="right"));return{bottom:h.top+h.height,height:h.height,left:h[e],right:h[e],top:h.top,width:0}}},{key:"getNativeRange",value:function(){var f=document.getSelection();if(null==f||0>=f.rangeCount)return null;f=f.getRangeAt(0);if(null==f)return null;f=this.normalizeNative(f);ha.info("getNativeRange",f);return f}},{key:"getRange",value:function(){var f=
this.getNativeRange();return null==f?[null,null]:[this.normalizedToRange(f),f]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(e){var h=this,n=[[e.start.node,e.start.offset]];e.native.collapsed||n.push([e.end.node,e.end.offset]);n=n.map(function(e){var n=r(e,2);e=n[0];n=n[1];var w=f.default.find(e,!0),x=w.offset(h.scroll);return 0===n?x:w instanceof f.default.Container?x+w.length():x+w.index(e,n)});e=Math.min(Math.max.apply(Math,
ba(n)),this.scroll.length()-1);n=Math.min.apply(Math,[e].concat(ba(n)));return new fa(n,e-n)}},{key:"normalizeNative",value:function(f){if(!z(this.root,f.startContainer)||!f.collapsed&&!z(this.root,f.endContainer))return null;f={start:{node:f.startContainer,offset:f.startOffset},end:{node:f.endContainer,offset:f.endOffset},native:f};[f.start,f.end].forEach(function(f){for(var e=f.node,h=f.offset;!(e instanceof Text)&&0<e.childNodes.length;)if(e.childNodes.length>h)e=e.childNodes[h],h=0;else if(e.childNodes.length===
h)e=e.lastChild,h=e instanceof Text?e.data.length:e.childNodes.length+1;else break;f.node=e;f.offset=h});return f}},{key:"rangeToNative",value:function(f){var e=this;f=f.collapsed?[f.index]:[f.index,f.index+f.length];var h=[],n=this.scroll.length();f.forEach(function(f,w){f=Math.min(n-1,f);f=e.scroll.leaf(f);var x=r(f,2);f=x[1];w=x[0].position(f,0!==w);f=r(w,2);w=f[0];f=f[1];h.push(w,f)});2>h.length&&(h=h.concat(h));return h}},{key:"scrollIntoView",value:function(f){var e=this.lastRange;if(null!=
e){var h=this.getBounds(e.index,e.length);if(null!=h){var n=this.scroll.length()-1,w=this.scroll.line(Math.min(e.index,n)),x=w=r(w,1)[0];0<e.length&&(e=this.scroll.line(Math.min(e.index+e.length,n)),x=r(e,1)[0]);null!=w&&null!=x&&(e=f.getBoundingClientRect(),h.top<e.top?f.scrollTop-=e.top-h.top:h.bottom>e.bottom&&(f.scrollTop+=h.bottom-e.bottom))}}}},{key:"setNativeRange",value:function(f,e){var h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:f,n=3<arguments.length&&void 0!==arguments[3]?
arguments[3]:e,r=4<arguments.length&&void 0!==arguments[4]?arguments[4]:!1;ha.info("setNativeRange",f,e,h,n);if(null==f||null!=this.root.parentNode&&null!=f.parentNode&&null!=h.parentNode){var w=document.getSelection();if(null!=w)if(null!=f){this.hasFocus()||this.root.focus();var x=(this.getNativeRange()||{}).native;if(null==x||r||f!==x.startContainer||e!==x.startOffset||h!==x.endContainer||n!==x.endOffset)"BR"==f.tagName&&(e=[].indexOf.call(f.parentNode.childNodes,f),f=f.parentNode),"BR"==h.tagName&&
(n=[].indexOf.call(h.parentNode.childNodes,h),h=h.parentNode),r=document.createRange(),r.setStart(f,e),r.setEnd(h,n),w.removeAllRanges(),w.addRange(r)}else w.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1,h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:aa.default.sources.API;"string"===typeof e&&(h=e,e=!1);ha.info("setRange",f);if(null!=f){var n=this.rangeToNative(f);this.setNativeRange.apply(this,
ba(n).concat([e]))}else this.setNativeRange(null);this.update(h)}},{key:"update",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:aa.default.sources.USER,e=this.lastRange,h=this.getRange();h=r(h,2);var w=h[1];this.lastRange=h[0];null!=this.lastRange&&(this.savedRange=this.lastRange);if(!(0,ca.default)(e,this.lastRange)){var x;!this.composing&&null!=w&&w.native.collapsed&&w.start.node!==this.cursor.textNode&&this.cursor.restore();e=[aa.default.events.SELECTION_CHANGE,(0,
n.default)(this.lastRange),(0,n.default)(e),f];(x=this.emitter).emit.apply(x,[aa.default.events.EDITOR_CHANGE].concat(e));if(f!==aa.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,e)}}}}]);return e}();y.Range=fa;y.default=ea},function(e,y,ea){function x(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});
r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(r,h,f){h&&e(r.prototype,h);f&&e(r,f);return r}}(),w=function n(e,h,f){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,h);if(void 0===r){if(e=Object.getPrototypeOf(e),
null!==e)return n(e,h,f)}else{if("value"in r)return r.value;h=r.get;return void 0===h?void 0:h.call(f)}};e=function(e){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var f=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?this:f}x(h,e);ba(h,[{key:"insertInto",value:function(f,e){0===f.children.length?
w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertInto",this).call(this,f,e):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]);return h}(function(e){return e&&e.__esModule?e:{default:e}}(ea(0)).default.Embed);e.blotName="break";e.tagName="BR";y.default=e},function(e,y,ea){function x(e){var h=z.find(e);if(null==h)try{h=z.create(e)}catch(f){h=z.create(z.Scope.INLINE),[].slice.call(e.childNodes).forEach(function(f){h.domNode.appendChild(f)}),
e.parentNode&&e.parentNode.replaceChild(h.domNode,e),h.attach()}return h}var ba=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(h,f){function n(){this.constructor=h}e(h,f);h.prototype=null===f?Object.create(f):(n.prototype=f.prototype,new n)}}();Object.defineProperty(y,"__esModule",{value:!0});var w=ea(44);e=ea(30);var z=ea(1);ea=function(e){function h(f){f=
e.call(this,f)||this;f.build();return f}ba(h,e);h.prototype.appendChild=function(f){this.insertBefore(f)};h.prototype.attach=function(){e.prototype.attach.call(this);this.children.forEach(function(f){f.attach()})};h.prototype.build=function(){var f=this;this.children=new w.default;[].slice.call(this.domNode.childNodes).reverse().forEach(function(e){try{var h=x(e);f.insertBefore(h,f.children.head||void 0)}catch(aa){if(!(aa instanceof z.ParchmentError))throw aa;}})};h.prototype.deleteAt=function(f,
e){if(0===f&&e===this.length())return this.remove();this.children.forEachAt(f,e,function(f,e,h){f.deleteAt(e,h)})};h.prototype.descendant=function(f,e){var n=this.children.find(e);e=n[0];n=n[1];return null==f.blotName&&f(e)||null!=f.blotName&&e instanceof f?[e,n]:e instanceof h?e.descendant(f,n):[null,-1]};h.prototype.descendants=function(f,e,r){void 0===e&&(e=0);void 0===r&&(r=Number.MAX_VALUE);var n=[],w=r;this.children.forEachAt(e,r,function(e,r,x){(null==f.blotName&&f(e)||null!=f.blotName&&e instanceof
f)&&n.push(e);e instanceof h&&(n=n.concat(e.descendants(f,r,w)));w-=x});return n};h.prototype.detach=function(){this.children.forEach(function(f){f.detach()});e.prototype.detach.call(this)};h.prototype.formatAt=function(f,e,h,r){this.children.forEachAt(f,e,function(f,e,n){f.formatAt(e,n,h,r)})};h.prototype.insertAt=function(f,e,h){var n=this.children.find(f);f=n[0];n=n[1];f?f.insertAt(n,e,h):(e=null==h?z.create("text",e):z.create(e,h),this.appendChild(e))};h.prototype.insertBefore=function(f,e){if(null!=
this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(e){return f instanceof e}))throw new z.ParchmentError("Cannot insert "+f.statics.blotName+" into "+this.statics.blotName);f.insertInto(this,e)};h.prototype.length=function(){return this.children.reduce(function(f,e){return f+e.length()},0)};h.prototype.moveChildren=function(f,e){this.children.forEach(function(h){f.insertBefore(h,e)})};h.prototype.optimize=function(f){e.prototype.optimize.call(this,f);if(0===this.children.length)if(null!=
this.statics.defaultChild){var h=z.create(this.statics.defaultChild);this.appendChild(h);h.optimize(f)}else this.remove()};h.prototype.path=function(f,e){void 0===e&&(e=!1);var n=this.children.find(f,e),r=n[0];n=n[1];f=[[this,f]];if(r instanceof h)return f.concat(r.path(n,e));null!=r&&f.push([r,n]);return f};h.prototype.removeChild=function(f){this.children.remove(f)};h.prototype.replace=function(f){f instanceof h&&f.moveChildren(this);e.prototype.replace.call(this,f)};h.prototype.split=function(f,
e){void 0===e&&(e=!1);if(!e){if(0===f)return this;if(f===this.length())return this.next}var h=this.clone();this.parent.insertBefore(h,this.next);this.children.forEachAt(f,this.length(),function(f,n){f=f.split(n,e);h.appendChild(f)});return h};h.prototype.unwrap=function(){this.moveChildren(this.parent,this.next);this.remove()};h.prototype.update=function(f){var e=this,h=[],r=[];f.forEach(function(f){f.target===e.domNode&&"childList"===f.type&&(h.push.apply(h,f.addedNodes),r.push.apply(r,f.removedNodes))});
r.forEach(function(f){null!=f.parentNode&&"IFRAME"!==f.tagName&&document.body.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_CONTAINED_BY||(f=z.find(f),null!=f&&(null!=f.domNode.parentNode&&f.domNode.parentNode!==e.domNode||f.detach()))});h.filter(function(f){return f.parentNode==e.domNode}).sort(function(f,e){return f===e?0:f.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(f){var h=null;null!=f.nextSibling&&(h=z.find(f.nextSibling));f=x(f);if(f.next!=h||null==
f.next)null!=f.parent&&f.parent.removeChild(e),e.insertBefore(f,h||void 0)})};return h}(e.default);y.default=ea},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(h,f){function n(){this.constructor=h}e(h,f);h.prototype=null===f?Object.create(f):(n.prototype=f.prototype,new n)}}();Object.defineProperty(y,"__esModule",{value:!0});
var ba=ea(12),w=ea(31);e=ea(17);var z=ea(1);ea=function(e){function h(f){f=e.call(this,f)||this;f.attributes=new w.default(f.domNode);return f}x(h,e);h.formats=function(f){if("string"===typeof this.tagName)return!0;if(Array.isArray(this.tagName))return f.tagName.toLowerCase()};h.prototype.format=function(f,e){var h=z.query(f);h instanceof ba.default?this.attributes.attribute(h,e):e&&(null==h||f===this.statics.blotName&&this.formats()[f]===e||this.replaceWith(f,e))};h.prototype.formats=function(){var f=
this.attributes.values(),e=this.statics.formats(this.domNode);null!=e&&(f[this.statics.blotName]=e);return f};h.prototype.replaceWith=function(f,h){f=e.prototype.replaceWith.call(this,f,h);this.attributes.copy(f);return f};h.prototype.update=function(f,h){var n=this;e.prototype.update.call(this,f,h);f.some(function(f){return f.target===n.domNode&&"attributes"===f.type})&&this.attributes.build()};h.prototype.wrap=function(f,n){f=e.prototype.wrap.call(this,f,n);f instanceof h&&f.statics.scope===this.statics.scope&&
this.attributes.move(f);return f};return h}(e.default);y.default=ea},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var h in r)r.hasOwnProperty(h)&&(e[h]=r[h])};return function(w,r){function h(){this.constructor=w}e(w,r);w.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(y,"__esModule",{value:!0});e=ea(30);var ba=ea(1);ea=function(e){function w(){return null!==
e&&e.apply(this,arguments)||this}x(w,e);w.value=function(){return!0};w.prototype.index=function(e,h){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(h,1):-1};w.prototype.position=function(e){var h=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);0<e&&(h+=1);return[this.parent.domNode,h]};w.prototype.value=function(){var e;return e={},e[this.statics.blotName]=this.statics.value(this.domNode)||!0,e};w.scope=ba.Scope.INLINE_BLOT;
return w}(e.default);y.default=ea},function(e,y,ea){function x(e){this.ops=e;this.offset=this.index=0}var ba=ea(11),w=ea(3),z={attributes:{compose:function(e,h,f){"object"!==typeof e&&(e={});"object"!==typeof h&&(h={});var n=w(!0,{},h);f||(n=Object.keys(n).reduce(function(f,e){null!=n[e]&&(f[e]=n[e]);return f},{}));for(var r in e)void 0!==e[r]&&void 0===h[r]&&(n[r]=e[r]);return 0<Object.keys(n).length?n:void 0},diff:function(e,h){"object"!==typeof e&&(e={});"object"!==typeof h&&(h={});var f=Object.keys(e).concat(Object.keys(h)).reduce(function(f,
r){ba(e[r],h[r])||(f[r]=void 0===h[r]?null:h[r]);return f},{});return 0<Object.keys(f).length?f:void 0},transform:function(e,h,f){if("object"!==typeof e)return h;if("object"===typeof h){if(!f)return h;f=Object.keys(h).reduce(function(f,r){void 0===e[r]&&(f[r]=h[r]);return f},{});return 0<Object.keys(f).length?f:void 0}}},iterator:function(e){return new x(e)},length:function(e){return"number"===typeof e["delete"]?e["delete"]:"number"===typeof e.retain?e.retain:"string"===typeof e.insert?e.insert.length:
1}};x.prototype.hasNext=function(){return Infinity>this.peekLength()};x.prototype.next=function(e){e||(e=Infinity);var h=this.ops[this.index];if(h){var f=this.offset,n=z.length(h);e>=n-f?(e=n-f,this.index+=1,this.offset=0):this.offset+=e;if("number"===typeof h["delete"])return{"delete":e};n={};h.attributes&&(n.attributes=h.attributes);"number"===typeof h.retain?n.retain=e:n.insert="string"===typeof h.insert?h.insert.substr(f,e):h.insert;return n}return{retain:Infinity}};x.prototype.peek=function(){return this.ops[this.index]};
x.prototype.peekLength=function(){return this.ops[this.index]?z.length(this.ops[this.index])-this.offset:Infinity};x.prototype.peekType=function(){if(this.ops[this.index]){if("number"===typeof this.ops[this.index]["delete"])return"delete";if("number"!==typeof this.ops[this.index].retain)return"insert"}return"retain"};x.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var e=this.offset,h=this.index,f=this.next(),n=this.ops.slice(this.index);this.offset=
e;this.index=h;return[f].concat(n)}return[]};e.exports=z},function(x){var y=function(){function x(f,e){return null!=e&&f instanceof e}function y(f,n,ca,aa,ba){function da(f,ca){if(null===f)return null;if(0===ca||"object"!=typeof f)return f;if(x(f,z))var ja=new z;else if(x(f,r))ja=new r;else if(x(f,h))ja=new h(function(e,h){f.then(function(f){e(da(f,ca-1))},function(f){h(da(f,ca-1))})});else if(y.__isArray(f))ja=[];else if(y.__isRegExp(f))ja=new RegExp(f.source,w(f)),f.lastIndex&&(ja.lastIndex=f.lastIndex);
else if(y.__isDate(f))ja=new Date(f.getTime());else{if(fa&&e.isBuffer(f))return ja=e.allocUnsafe?e.allocUnsafe(f.length):new e(f.length),f.copy(ja),ja;if(x(f,Error))ja=Object.create(f);else if("undefined"==typeof aa){var ka=Object.getPrototypeOf(f);ja=Object.create(ka)}else ja=Object.create(aa),ka=aa}if(n){var ia=ea.indexOf(f);if(-1!=ia)return ha[ia];ea.push(f);ha.push(ja)}x(f,z)&&f.forEach(function(f,e){e=da(e,ca-1);f=da(f,ca-1);ja.set(e,f)});x(f,r)&&f.forEach(function(f){f=da(f,ca-1);ja.add(f)});
for(var la in f){var ma;ka&&(ma=Object.getOwnPropertyDescriptor(ka,la));ma&&null==ma.set||(ja[la]=da(f[la],ca-1))}if(Object.getOwnPropertySymbols)for(ia=Object.getOwnPropertySymbols(f),la=0;la<ia.length;la++)if(ma=ia[la],ka=Object.getOwnPropertyDescriptor(f,ma),!ka||ka.enumerable||ba)ja[ma]=da(f[ma],ca-1),ka.enumerable||Object.defineProperty(ja,ma,{enumerable:!1});if(ba)for(ia=Object.getOwnPropertyNames(f),la=0;la<ia.length;la++)ma=ia[la],ka=Object.getOwnPropertyDescriptor(f,ma),ka&&ka.enumerable||
(ja[ma]=da(f[ma],ca-1),Object.defineProperty(ja,ma,{enumerable:!1}));return ja}"object"===typeof n&&(ca=n.depth,aa=n.prototype,ba=n.includeNonEnumerable,n=n.circular);var ea=[],ha=[],fa="undefined"!=typeof e;"undefined"==typeof n&&(n=!0);"undefined"==typeof ca&&(ca=Infinity);return da(f,ca)}function ba(f){return Object.prototype.toString.call(f)}function w(f){var e="";f.global&&(e+="g");f.ignoreCase&&(e+="i");f.multiline&&(e+="m");return e}try{var z=Map}catch(f){z=function(){}}try{var r=Set}catch(f){r=
function(){}}try{var h=Promise}catch(f){h=function(){}}y.clonePrototype=function(f){function e(){}if(null===f)return null;e.prototype=f;return new e};y.__objToStr=ba;y.__isDate=function(f){return"object"===typeof f&&"[object Date]"===ba(f)};y.__isArray=function(f){return"object"===typeof f&&"[object Array]"===ba(f)};y.__isRegExp=function(f){return"object"===typeof f&&"[object RegExp]"===ba(f)};y.__getRegExpFlags=w;return y}();"object"===typeof x&&x.exports&&(x.exports=y)},function(e,y,ea){function x(f){return f&&
f.__esModule?f:{default:f}}function ba(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function w(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=
e)}function z(f){return f instanceof ha.default||f instanceof aa.BlockEmbed}Object.defineProperty(y,"__esModule",{value:!0});var r=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==e);n=!0);}catch(ma){r=!0,w=ma}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");
}}(),h=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}(),f=function ra(f,e,h){null===f&&(f=Function.prototype);var n=Object.getOwnPropertyDescriptor(f,e);if(void 0===n){if(f=Object.getPrototypeOf(f),null!==f)return ra(f,e,h)}else{if("value"in n)return n.value;e=n.get;return void 0===e?void 0:e.call(h)}};e=ea(0);
var n=x(e);e=ea(8);var ca=x(e),aa=ea(4),ha=x(aa);e=ea(16);var fa=x(e);e=ea(13);var ia=x(e);ea=ea(25);ea=x(ea);e=function(e){function x(f,e){if(!(this instanceof x))throw new TypeError("Cannot call a class as a function");f=ba(this,(x.__proto__||Object.getPrototypeOf(x)).call(this,f));f.emitter=e.emitter;Array.isArray(e.whitelist)&&(f.whitelist=e.whitelist.reduce(function(f,e){f[e]=!0;return f},{}));f.domNode.addEventListener("DOMNodeInserted",function(){});f.optimize();f.enable();return f}w(x,e);
h(x,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1;this.optimize()}},{key:"deleteAt",value:function(e,h){var n=this.line(e),w=r(n,2);n=w[0];var z=w[1];w=this.line(e+h);w=r(w,1)[0];f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"deleteAt",this).call(this,e,h);if(null!=w&&n!==w&&0<z){if(n instanceof aa.BlockEmbed||w instanceof aa.BlockEmbed){this.optimize();return}if(n instanceof ia.default){if(e=n.newlineIndex(n.length(),!0),-1<e&&(n=
n.split(e+1),n===w)){this.optimize();return}}else w instanceof ia.default&&(e=w.newlineIndex(0),-1<e&&w.split(e+1));n.moveChildren(w,w.children.head instanceof fa.default?null:w.children.head);n.remove()}this.optimize()}},{key:"enable",value:function(){this.domNode.setAttribute("contenteditable",0<arguments.length&&void 0!==arguments[0]?arguments[0]:!0)}},{key:"formatAt",value:function(e,h,n,r){if(null==this.whitelist||this.whitelist[n])f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),
"formatAt",this).call(this,e,h,n,r),this.optimize()}},{key:"insertAt",value:function(e,h,r){if(null==r||null==this.whitelist||this.whitelist[h])e>=this.length()?null==r||null==n.default.query(h,n.default.Scope.BLOCK)?(e=n.default.create(this.statics.defaultChild),this.appendChild(e),null==r&&h.endsWith("\n")&&(h=h.slice(0,-1)),e.insertAt(0,h,r)):(h=n.default.create(h,r),this.appendChild(h)):f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"insertAt",this).call(this,e,h,r),this.optimize()}},
{key:"insertBefore",value:function(e,h){if(e.statics.scope===n.default.Scope.INLINE_BLOT){var r=n.default.create(this.statics.defaultChild);r.appendChild(e);e=r}f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"insertBefore",this).call(this,e,h)}},{key:"leaf",value:function(f){return this.path(f).pop()||[null,-1]}},{key:"line",value:function(f){return f===this.length()?this.line(f-1):this.descendant(z,f)}},{key:"lines",value:function(){return function oa(f,e,h){var r=[],w=h;f.children.forEachAt(e,
h,function(f,e,h){z(f)?r.push(f):f instanceof n.default.Container&&(r=r.concat(oa(f,e,w)));w-=h});return r}(this,0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],h=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"optimize",this).call(this,
e,h),0<e.length&&this.emitter.emit(ca.default.events.SCROLL_OPTIMIZE,e,h))}},{key:"path",value:function(e){return f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"path",this).call(this,e).slice(1)}},{key:"update",value:function(e){if(!0!==this.batch){var h=ca.default.sources.USER;"string"===typeof e&&(h=e);Array.isArray(e)||(e=this.observer.takeRecords());0<e.length&&this.emitter.emit(ca.default.events.SCROLL_BEFORE_UPDATE,h,e);f(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),
"update",this).call(this,e.concat([]));0<e.length&&this.emitter.emit(ca.default.events.SCROLL_UPDATE,h,e)}}}]);return x}(n.default.Scroll);e.blotName="scroll";e.className="ql-editor";e.tagName="DIV";e.defaultChild="block";e.allowedChildren=[ha.default,aa.BlockEmbed,ea.default];y.default=e},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e,h){e in f?Object.defineProperty(f,e,{value:h,enumerable:!0,configurable:!0,writable:!0}):f[e]=h;return f}function w(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}function r(f,e){var h,n=f===ua.keys.LEFT?"prefix":"suffix";return h={key:f,shiftKey:e,altKey:null},ba(h,n,/^$/),ba(h,"handler",function(h){var n=
h.index;f===ua.keys.RIGHT&&(n+=h.length+1);n=this.quill.getLeaf(n);if(!(xa(n,1)[0]instanceof oa.default.Embed))return!0;f===ua.keys.LEFT?e?this.quill.setSelection(h.index-1,h.length+1,ma.default.sources.USER):this.quill.setSelection(h.index-1,ma.default.sources.USER):e?this.quill.setSelection(h.index,h.length+1,ma.default.sources.USER):this.quill.setSelection(h.index+h.length+1,ma.default.sources.USER);return!1}),h}function h(f,e){if(!(0===f.index||1>=this.quill.getLength())){var h=this.quill.getLine(f.index),
n=xa(h,1)[0];h={};if(0===e.offset){var r=this.quill.getLine(f.index-1);r=xa(r,1)[0];null!=r&&1<r.length()&&(h=n.formats(),n=this.quill.getFormat(f.index-1,1),h=sa.default.attributes.diff(h,n)||{})}e=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;this.quill.deleteText(f.index-e,e,ma.default.sources.USER);0<Object.keys(h).length&&this.quill.formatLine(f.index-e,e,h,ma.default.sources.USER);this.quill.focus()}}function f(f,e){var h=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(f.index>=
this.quill.getLength()-h)){var n={},r=0,w=this.quill.getLine(f.index);w=xa(w,1)[0];e.offset>=w.length()-1&&(e=this.quill.getLine(f.index+1),e=xa(e,1)[0])&&(n=w.formats(),r=this.quill.getFormat(f.index,1),n=sa.default.attributes.diff(n,r)||{},r=e.length());this.quill.deleteText(f.index,h,ma.default.sources.USER);0<Object.keys(n).length&&this.quill.formatLine(f.index+r-1,h,n,ma.default.sources.USER)}}function n(f){var e=this.quill.getLines(f),h={};1<e.length&&(h=e[0].formats(),e=e[e.length-1].formats(),
h=sa.default.attributes.diff(e,h)||{});this.quill.deleteText(f,ma.default.sources.USER);0<Object.keys(h).length&&this.quill.formatLine(f.index,1,h,ma.default.sources.USER);this.quill.setSelection(f.index,ma.default.sources.SILENT);this.quill.focus()}function ca(f,e){var h=this;0<f.length&&this.quill.scroll.deleteAt(f.index,f.length);var n=Object.keys(e.format).reduce(function(f,h){oa.default.query(h,oa.default.Scope.BLOCK)&&!Array.isArray(e.format[h])&&(f[h]=e.format[h]);return f},{});this.quill.insertText(f.index,
"\n",n,ma.default.sources.USER);this.quill.setSelection(f.index+1,ma.default.sources.SILENT);this.quill.focus();Object.keys(e.format).forEach(function(f){null==n[f]&&(Array.isArray(e.format[f])||"link"!==f&&h.quill.format(f,e.format[f],ma.default.sources.USER))})}function aa(f){return{key:ua.keys.TAB,shiftKey:!f,format:{"code-block":!0},handler:function(e){var h=oa.default.query("code-block"),n=e.index,r=e.length;e=this.quill.scroll.descendant(h,n);e=xa(e,2);var w=e[0],x=e[1];if(null!=w){e=this.quill.getIndex(w);
var aa=w.newlineIndex(x,!0)+1;e=w.newlineIndex(e+x+r);e=w.domNode.textContent.slice(aa,e).split("\n");x=0;e.forEach(function(e,z){f?(w.insertAt(aa+x,h.TAB),x+=h.TAB.length,0===z?n+=h.TAB.length:r+=h.TAB.length):e.startsWith(h.TAB)&&(w.deleteAt(aa+x,h.TAB.length),x-=h.TAB.length,0===z?n-=h.TAB.length:r-=h.TAB.length);x+=e.length+1});this.quill.update(ma.default.sources.USER);this.quill.setSelection(n,r,ma.default.sources.SILENT)}}}}function ha(f){return{key:f[0].toUpperCase(),shortKey:!0,handler:function(e,
h){this.quill.format(f,!h.format[f],ma.default.sources.USER)}}}function fa(f){if("string"===typeof f||"number"===typeof f)return fa({key:f});"object"===("undefined"===typeof f?"undefined":ia(f))&&(f=(0,na.default)(f,!1));if("string"===typeof f.key)if(null!=ua.keys[f.key.toUpperCase()])f.key=ua.keys[f.key.toUpperCase()];else if(1===f.key.length)f.key=f.key.toUpperCase().charCodeAt(0);else return null;f.shortKey&&(f[Fa]=f.shortKey,delete f.shortKey);return f}Object.defineProperty(y,"__esModule",{value:!0});
y.SHORTKEY=y.default=void 0;var ia="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},xa=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),aa;!(n=(aa=x.next()).done)&&(h.push(aa.value),!e||h.length!==e);n=!0);}catch(wa){r=!0,w=wa}finally{try{if(!n&&
x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),la=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}();e=ea(21);var na=x(e);e=ea(11);var za=x(e);e=ea(3);var ra=x(e);e=ea(2);var Da=x(e);e=ea(20);var sa=x(e);e=ea(0);var oa=
x(e);e=ea(5);var ma=x(e);e=ea(10);e=x(e);ea=ea(9);ea=x(ea);var va=(0,e.default)("quill:keyboard"),Fa=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",ua=function(e){function r(e,x){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function");var aa=w(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,e,x));aa.bindings={};Object.keys(aa.options.bindings).forEach(function(f){("list autofill"!==f||null==e.scroll.whitelist||e.scroll.whitelist.list)&&aa.options.bindings[f]&&
aa.addBinding(aa.options.bindings[f])});aa.addBinding({key:r.keys.ENTER,shiftKey:null},ca);aa.addBinding({key:r.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){});/Firefox/i.test(navigator.userAgent)?(aa.addBinding({key:r.keys.BACKSPACE},{collapsed:!0},h),aa.addBinding({key:r.keys.DELETE},{collapsed:!0},f)):(aa.addBinding({key:r.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},h),aa.addBinding({key:r.keys.DELETE},{collapsed:!0,suffix:/^.?$/},f));aa.addBinding({key:r.keys.BACKSPACE},{collapsed:!1},
n);aa.addBinding({key:r.keys.DELETE},{collapsed:!1},n);aa.addBinding({key:r.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},h);aa.listen();return aa}z(r,e);la(r,null,[{key:"match",value:function(f,e){e=fa(e);return["altKey","ctrlKey","metaKey","shiftKey"].some(function(h){return!!e[h]!==f[h]&&null!==e[h]})?!1:e.key===(f.which||f.keyCode)}}]);la(r,[{key:"addBinding",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},h=2<arguments.length&&
void 0!==arguments[2]?arguments[2]:{},n=fa(f);if(null==n||null==n.key)return va.warn("Attempted to add invalid keyboard binding",n);"function"===typeof e&&(e={handler:e});"function"===typeof h&&(h={handler:h});n=(0,ra.default)(n,e,h);this.bindings[n.key]=this.bindings[n.key]||[];this.bindings[n.key].push(n)}},{key:"listen",value:function(){var f=this;this.quill.root.addEventListener("keydown",function(e){if(!e.defaultPrevented){var h=(f.bindings[e.which||e.keyCode]||[]).filter(function(f){return r.match(e,
f)});if(0!==h.length){var n=f.quill.getSelection();if(null!=n&&f.quill.hasFocus()){var w=f.quill.getLine(n.index),x=xa(w,2);w=x[0];x=x[1];var aa=f.quill.getLeaf(n.index),z=xa(aa,2);aa=z[0];z=z[1];var y=0===n.length?[aa,z]:f.quill.getLeaf(n.index+n.length),ca=xa(y,2);y=ca[0];ca=ca[1];aa=aa instanceof oa.default.Text?aa.value().slice(0,z):"";z=y instanceof oa.default.Text?y.value().slice(ca):"";var ba={collapsed:0===n.length,empty:0===n.length&&1>=w.length(),format:f.quill.getFormat(n),offset:x,prefix:aa,
suffix:z};h.some(function(e){if(null!=e.collapsed&&e.collapsed!==ba.collapsed||null!=e.empty&&e.empty!==ba.empty||null!=e.offset&&e.offset!==ba.offset)return!1;if(Array.isArray(e.format)){if(e.format.every(function(f){return null==ba.format[f]}))return!1}else if("object"===ia(e.format)&&!Object.keys(e.format).every(function(f){return!0===e.format[f]?null!=ba.format[f]:!1===e.format[f]?null==ba.format[f]:(0,za.default)(e.format[f],ba.format[f])}))return!1;return null!=e.prefix&&!e.prefix.test(ba.prefix)||
null!=e.suffix&&!e.suffix.test(ba.suffix)?!1:!0!==e.handler.call(f,n,ba)})&&e.preventDefault()}}}})}}]);return r}(ea.default);ua.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46};ua.DEFAULTS={bindings:{bold:ha("bold"),italic:ha("italic"),underline:ha("underline"),indent:{key:ua.keys.TAB,format:["blockquote","indent","list"],handler:function(f,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","+1",ma.default.sources.USER)}},outdent:{key:ua.keys.TAB,
shiftKey:!0,format:["blockquote","indent","list"],handler:function(f,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","-1",ma.default.sources.USER)}},"outdent backspace":{key:ua.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(f,e){null!=e.format.indent?this.quill.format("indent","-1",ma.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,ma.default.sources.USER)}},"indent code-block":aa(!0),
"outdent code-block":aa(!1),"remove tab":{key:ua.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(f){this.quill.deleteText(f.index-1,1,ma.default.sources.USER)}},tab:{key:ua.keys.TAB,handler:function(f){this.quill.history.cutoff();var e=(new Da.default).retain(f.index).delete(f.length).insert("\t");this.quill.updateContents(e,ma.default.sources.USER);this.quill.history.cutoff();this.quill.setSelection(f.index+1,ma.default.sources.SILENT)}},"list empty enter":{key:ua.keys.ENTER,collapsed:!0,
format:["list"],empty:!0,handler:function(f,e){this.quill.format("list",!1,ma.default.sources.USER);e.format.indent&&this.quill.format("indent",!1,ma.default.sources.USER)}},"checklist enter":{key:ua.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(f){var e=this.quill.getLine(f.index),h=xa(e,2);e=h[0];h=h[1];var n=(0,ra.default)({},e.formats(),{list:"checked"});e=(new Da.default).retain(f.index).insert("\n",n).retain(e.length()-h-1).retain(1,{list:"unchecked"});this.quill.updateContents(e,
ma.default.sources.USER);this.quill.setSelection(f.index+1,ma.default.sources.SILENT);this.quill.scrollIntoView()}},"header enter":{key:ua.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(f,e){var h=this.quill.getLine(f.index),n=xa(h,2);h=n[0];n=n[1];e=(new Da.default).retain(f.index).insert("\n",e.format).retain(h.length()-n-1).retain(1,{header:null});this.quill.updateContents(e,ma.default.sources.USER);this.quill.setSelection(f.index+1,ma.default.sources.SILENT);this.quill.scrollIntoView()}},
"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(f,e){var h=e.prefix.length,n=this.quill.getLine(f.index),r=xa(n,2);n=r[0];r=r[1];if(r>h)return!0;switch(e.prefix.trim()){case "[]":case "[ ]":e="unchecked";break;case "[x]":e="checked";break;case "-":case "*":e="bullet";break;default:e="ordered"}this.quill.insertText(f.index," ",ma.default.sources.USER);this.quill.history.cutoff();e=(new Da.default).retain(f.index-r).delete(h+1).retain(n.length()-
2-r).retain(1,{list:e});this.quill.updateContents(e,ma.default.sources.USER);this.quill.history.cutoff();this.quill.setSelection(f.index-h,ma.default.sources.SILENT)}},"code exit":{key:ua.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(f){var e=this.quill.getLine(f.index),h=xa(e,2);e=h[0];h=h[1];f=(new Da.default).retain(f.index+e.length()-h-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(f,ma.default.sources.USER)}},"embed left":r(ua.keys.LEFT,
!1),"embed left shift":r(ua.keys.LEFT,!0),"embed right":r(ua.keys.RIGHT,!1),"embed right shift":r(ua.keys.RIGHT,!0)}};y.default=ua;y.SHORTKEY=Fa},function(e,y,ea){function x(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(y,
"__esModule",{value:!0});var ba=function(){return function(f,e){if(Array.isArray(f))return f;if(Symbol.iterator in Object(f)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=f[Symbol.iterator](),z;!(n=(z=x.next()).done)&&(h.push(z.value),!e||h.length!==e);n=!0);}catch(la){r=!0,w=la}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),w=function ja(e,h,r){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,
h);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ja(e,h,r)}else{if("value"in n)return n.value;h=n.get;return void 0===h?void 0:h.call(r)}},z=function(){function e(e,h){for(var n=0;n<h.length;n++){var r=h[n];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,n,r){n&&e(h.prototype,n);r&&e(h,r);return h}}(),r=(e=ea(0))&&e.__esModule?e:{default:e},h=(ea=ea(7))&&ea.__esModule?ea:{default:ea};ea=function(e){function n(e,
h){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");e=(n.__proto__||Object.getPrototypeOf(n)).call(this,e);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");e=!e||"object"!==typeof e&&"function"!==typeof e?this:e;e.selection=h;e.textNode=document.createTextNode(n.CONTENTS);e.domNode.appendChild(e.textNode);e._length=0;return e}x(n,e);z(n,null,[{key:"value",value:function(){}}]);z(n,[{key:"detach",value:function(){null!=this.parent&&
this.parent.removeChild(this)}},{key:"format",value:function(e,h){if(0!==this._length)return w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"format",this).call(this,e,h);for(var x=this,aa=0;null!=x&&x.statics.scope!==r.default.Scope.BLOCK_BLOT;)aa+=x.offset(x.parent),x=x.parent;null!=x&&(this._length=n.CONTENTS.length,x.optimize(),x.formatAt(aa,n.CONTENTS.length,e,h),this._length=0)}},{key:"index",value:function(e,h){return e===this.textNode?0:w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),
"index",this).call(this,e,h)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"remove",this).call(this);this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var e=this.textNode,w=this.selection.getNativeRange(),x=void 0,z=void 0,y=void 0;null!=w&&w.start.node===e&&w.end.node===
e&&(y=[e,w.start.offset,w.end.offset],x=y[0],z=y[1],y=y[2]);for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);this.textNode.data!==n.CONTENTS&&(e=this.textNode.data.split(n.CONTENTS).join(""),this.next instanceof h.default?(x=this.next.domNode,this.next.insertAt(0,e),this.textNode.data=n.CONTENTS):(this.textNode.data=e,this.parent.insertBefore(r.default.create(this.textNode),this),this.textNode=document.createTextNode(n.CONTENTS),
this.domNode.appendChild(this.textNode)));this.remove();if(null!=z)return z=[z,y].map(function(e){return Math.max(0,Math.min(x.data.length,e-1))}),y=ba(z,2),z=y[0],y=y[1],{startNode:x,startOffset:z,endNode:x,endOffset:y}}}},{key:"update",value:function(e,h){var n=this;e.some(function(e){return"characterData"===e.type&&e.target===n.textNode})&&(e=this.restore())&&(h.range=e)}},{key:"value",value:function(){return""}}]);return n}(r.default.Embed);ea.blotName="cursor";ea.className="ql-cursor";ea.tagName=
"span";ea.CONTENTS="\ufeff";y.default=ea},function(e,y,ea){function x(e,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+typeof x);e.prototype=Object.create(x&&x.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(e,x):e.__proto__=x)}Object.defineProperty(y,"__esModule",{value:!0});e=(e=ea(0))&&e.__esModule?e:{default:e};var ba=(ea=ea(4))&&ea.__esModule?ea:{default:ea};
e=function(e){function w(){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);return w}(e.default.Container);e.allowedChildren=[ba.default,ea.BlockEmbed,e];y.default=e},function(e,y,ea){function x(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+
typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});y.ColorStyle=y.ColorClass=y.ColorAttributor=void 0;var ba=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,
f);n&&e(h,n);return h}}(),w=function ca(e,f,n){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}},z=function(e){return e&&e.__esModule?e:{default:e}}(ea(0));e=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);
if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(f,e);ba(f,[{key:"value",value:function(e){e=w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"value",this).call(this,e);if(!e.startsWith("rgb("))return e;e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"");return"#"+e.split(",").map(function(f){return("00"+parseInt(f).toString(16)).slice(-2)}).join("")}}]);return f}(z.default.Attributor.Style);
ea=new z.default.Attributor.Class("color","ql-color",{scope:z.default.Scope.INLINE});z=new e("color","color",{scope:z.default.Scope.INLINE});y.ColorAttributor=e;y.ColorClass=ea;y.ColorStyle=z},function(e,y,ea){function x(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,
h):e.__proto__=h)}function ba(e,h){var f=document.createElement("a");f.href=e;e=f.href.slice(0,f.href.indexOf(":"));return-1<h.indexOf(e)}Object.defineProperty(y,"__esModule",{value:!0});y.sanitize=y.default=void 0;var w=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),z=function ca(e,f,n){null===e&&(e=Function.prototype);
var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};e=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==
typeof e?this:e}x(f,e);w(f,[{key:"format",value:function(e,h){if(e!==this.statics.blotName||!h)return z(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"format",this).call(this,e,h);h=this.constructor.sanitize(h);this.domNode.setAttribute("href",h)}}],[{key:"create",value:function(e){var h=z(f.__proto__||Object.getPrototypeOf(f),"create",this).call(this,e);e=this.sanitize(e);h.setAttribute("href",e);h.setAttribute("rel","noopener noreferrer");h.setAttribute("target","_blank");return h}},
{key:"formats",value:function(f){return f.getAttribute("href")}},{key:"sanitize",value:function(f){return ba(f,this.PROTOCOL_WHITELIST)?f:this.SANITIZED_URL}}]);return f}(function(e){return e&&e.__esModule?e:{default:e}}(ea(6)).default);e.blotName="link";e.tagName="A";e.SANITIZED_URL="about:blank";e.PROTOCOL_WHITELIST=["http","https","mailto","tel"];y.default=e;y.sanitize=ba},function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});var x="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?
function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ba=function(){function e(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(f,h,r){h&&e(f.prototype,h);r&&e(f,r);return f}}(),w=(e=ea(23))&&e.__esModule?e:{default:e},z=(ea=ea(107))&&ea.__esModule?ea:{default:ea},r=0;ea=function(){function e(f){var h=
this;if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.select=f;this.container=document.createElement("span");this.buildPicker();this.select.style.display="none";this.select.parentNode.insertBefore(this.container,this.select);this.label.addEventListener("mousedown",function(){h.togglePicker()});this.label.addEventListener("keydown",function(f){switch(f.keyCode){case w.default.keys.ENTER:h.togglePicker();break;case w.default.keys.ESCAPE:h.escape(),f.preventDefault()}});
this.select.addEventListener("change",this.update.bind(this))}ba(e,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded");var f=this.label;f.setAttribute("aria-expanded","true"!==f.getAttribute("aria-expanded"));f=this.options;f.setAttribute("aria-hidden","true"!==f.getAttribute("aria-hidden"))}},{key:"buildItem",value:function(f){var e=this,h=document.createElement("span");h.tabIndex="0";h.setAttribute("role","button");h.classList.add("ql-picker-item");f.hasAttribute("value")&&
h.setAttribute("data-value",f.getAttribute("value"));f.textContent&&h.setAttribute("data-label",f.textContent);h.addEventListener("click",function(){e.selectItem(h,!0)});h.addEventListener("keydown",function(f){switch(f.keyCode){case w.default.keys.ENTER:e.selectItem(h,!0);f.preventDefault();break;case w.default.keys.ESCAPE:e.escape(),f.preventDefault()}});return h}},{key:"buildLabel",value:function(){var f=document.createElement("span");f.classList.add("ql-picker-label");f.innerHTML=z.default;f.tabIndex=
"0";f.setAttribute("role","button");f.setAttribute("aria-expanded","false");this.container.appendChild(f);return f}},{key:"buildOptions",value:function(){var f=this,e=document.createElement("span");e.classList.add("ql-picker-options");e.setAttribute("aria-hidden","true");e.tabIndex="-1";e.id="ql-picker-options-"+r;r+=1;this.label.setAttribute("aria-controls",e.id);this.options=e;[].slice.call(this.select.options).forEach(function(h){var n=f.buildItem(h);e.appendChild(n);!0===h.selected&&f.selectItem(n)});
this.container.appendChild(e)}},{key:"buildPicker",value:function(){var f=this;[].slice.call(this.select.attributes).forEach(function(e){f.container.setAttribute(e.name,e.value)});this.container.classList.add("ql-picker");this.label=this.buildLabel();this.buildOptions()}},{key:"escape",value:function(){var f=this;this.close();setTimeout(function(){return f.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded");this.label.setAttribute("aria-expanded","false");
this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(f){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!1,h=this.container.querySelector(".ql-selected");f!==h&&(null!=h&&h.classList.remove("ql-selected"),null!=f&&(f.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(f.parentNode.children,f),f.hasAttribute("data-value")?this.label.setAttribute("data-value",f.getAttribute("data-value")):this.label.removeAttribute("data-value"),f.hasAttribute("data-label")?
this.label.setAttribute("data-label",f.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&("function"===typeof Event?this.select.dispatchEvent(new Event("change")):"object"===("undefined"===typeof Event?"undefined":x(Event))&&(e=document.createEvent("Event"),e.initEvent("change",!0,!0),this.select.dispatchEvent(e)),this.close())))}},{key:"update",value:function(){var f=void 0;if(-1<this.select.selectedIndex){var e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];
f=this.select.options[this.select.selectedIndex];this.selectItem(e)}else this.selectItem(null);this.label.classList.toggle("ql-active",null!=f&&f!==this.select.querySelector("option[selected]"))}}]);return e}();y.default=ea},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}Object.defineProperty(y,"__esModule",{value:!0});e=ea(0);e=x(e);var ba=ea(5);ba=x(ba);var w=ea(4),z=x(w),r=ea(16);r=x(r);var h=ea(25);h=x(h);var f=ea(24);f=x(f);var n=ea(35);n=x(n);var ca=ea(6);ca=x(ca);var aa=
ea(22);aa=x(aa);var ha=ea(7);ha=x(ha);var fa=ea(55);fa=x(fa);var ia=ea(42);ia=x(ia);ea=ea(23);ba.default.register({"blots/block":z.default,"blots/block/embed":w.BlockEmbed,"blots/break":r.default,"blots/container":h.default,"blots/cursor":f.default,"blots/embed":n.default,"blots/inline":ca.default,"blots/scroll":aa.default,"blots/text":ha.default,"modules/clipboard":fa.default,"modules/history":ia.default,"modules/keyboard":x(ea).default});e.default.register(z.default,r.default,f.default,ca.default,
aa.default,ha.default);y.default=ba.default},function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});var x=ea(1);e=function(){function e(e){this.domNode=e;this.domNode[x.DATA_KEY]={blot:this}}Object.defineProperty(e.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0});e.create=function(e){if(null==this.tagName)throw new x.ParchmentError("Blot definition missing tagName");Array.isArray(this.tagName)?("string"===typeof e&&(e=e.toUpperCase(),parseInt(e).toString()===
e&&(e=parseInt(e))),e="number"===typeof e?document.createElement(this.tagName[e-1]):-1<this.tagName.indexOf(e)?document.createElement(e):document.createElement(this.tagName[0])):e=document.createElement(this.tagName);this.className&&e.classList.add(this.className);return e};e.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)};e.prototype.clone=function(){var e=this.domNode.cloneNode(!1);return x.create(e)};e.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this);
delete this.domNode[x.DATA_KEY]};e.prototype.deleteAt=function(e,x){this.isolate(e,x).remove()};e.prototype.formatAt=function(e,z,r,h){e=this.isolate(e,z);null!=x.query(r,x.Scope.BLOT)&&h?e.wrap(r,h):null!=x.query(r,x.Scope.ATTRIBUTE)&&(z=x.create(this.statics.scope),e.wrap(z),z.format(r,h))};e.prototype.insertAt=function(e,z,r){z=null==r?x.create("text",z):x.create(z,r);e=this.split(e);this.parent.insertBefore(z,e)};e.prototype.insertInto=function(e,x){void 0===x&&(x=null);null!=this.parent&&this.parent.children.remove(this);
var r=null;e.children.insertBefore(this,x);null!=x&&(r=x.domNode);this.domNode.parentNode==e.domNode&&this.domNode.nextSibling==r||e.domNode.insertBefore(this.domNode,r);this.parent=e;this.attach()};e.prototype.isolate=function(e,x){e=this.split(e);e.split(x);return e};e.prototype.length=function(){return 1};e.prototype.offset=function(e){void 0===e&&(e=this.parent);return null==this.parent||this==e?0:this.parent.children.offset(this)+this.parent.offset(e)};e.prototype.optimize=function(){null!=this.domNode[x.DATA_KEY]&&
delete this.domNode[x.DATA_KEY].mutations};e.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode);this.detach()};e.prototype.replace=function(e){null!=e.parent&&(e.parent.insertBefore(this,e.next),e.remove())};e.prototype.replaceWith=function(e,z){e="string"===typeof e?x.create(e,z):e;e.replace(this);return e};e.prototype.split=function(e){return 0===e?this:this.next};e.prototype.update=function(){};e.prototype.wrap=function(e,z){e="string"===
typeof e?x.create(e,z):e;null!=this.parent&&this.parent.insertBefore(e,this.next);e.appendChild(this);return e};e.blotName="abstract";return e}();y.default=e},function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});var x=ea(12),ba=ea(32),w=ea(33),z=ea(1);e=function(){function e(e){this.attributes={};this.domNode=e;this.build()}e.prototype.attribute=function(e,f){f?e.add(this.domNode,f)&&(null!=e.value(this.domNode)?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),
delete this.attributes[e.attrName])};e.prototype.build=function(){var e=this;this.attributes={};var f=x.default.keys(this.domNode),n=ba.default.keys(this.domNode),r=w.default.keys(this.domNode);f.concat(n).concat(r).forEach(function(f){f=z.query(f,z.Scope.ATTRIBUTE);f instanceof x.default&&(e.attributes[f.attrName]=f)})};e.prototype.copy=function(e){var f=this;Object.keys(this.attributes).forEach(function(h){var n=f.attributes[h].value(f.domNode);e.format(h,n)})};e.prototype.move=function(e){var f=
this;this.copy(e);Object.keys(this.attributes).forEach(function(e){f.attributes[e].remove(f.domNode)});this.attributes={}};e.prototype.values=function(){var e=this;return Object.keys(this.attributes).reduce(function(f,h){f[h]=e.attributes[h].value(e.domNode);return f},{})};return e}();y.default=e},function(e,y,ea){function x(e,x){return(e.getAttribute("class")||"").split(/\s+/).filter(function(e){return 0===e.indexOf(x+"-")})}var ba=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof
Array&&function(e,r){e.__proto__=r}||function(e,r){for(var h in r)r.hasOwnProperty(h)&&(e[h]=r[h])};return function(w,r){function h(){this.constructor=w}e(w,r);w.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){return null!==e&&e.apply(this,arguments)||this}ba(w,e);w.keys=function(e){return(e.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})};w.prototype.add=
function(e,h){if(!this.canAdd(e,h))return!1;this.remove(e);e.classList.add(this.keyName+"-"+h);return!0};w.prototype.remove=function(e){x(e,this.keyName).forEach(function(h){e.classList.remove(h)});0===e.classList.length&&e.removeAttribute("class")};w.prototype.value=function(e){var h=(x(e,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(e,h)?h:""};return w}(ea(12).default);y.default=e},function(e,y,ea){function x(e){e=e.split("-");var w=e.slice(1).map(function(e){return e[0].toUpperCase()+
e.slice(1)}).join("");return e[0]+w}var ba=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var h in r)r.hasOwnProperty(h)&&(e[h]=r[h])};return function(w,r){function h(){this.constructor=w}e(w,r);w.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){return null!==e&&e.apply(this,arguments)||this}ba(w,e);w.keys=
function(e){return(e.getAttribute("style")||"").split(";").map(function(e){return e.split(":")[0].trim()})};w.prototype.add=function(e,h){if(!this.canAdd(e,h))return!1;e.style[x(this.keyName)]=h;return!0};w.prototype.remove=function(e){e.style[x(this.keyName)]="";e.getAttribute("style")||e.removeAttribute("style")};w.prototype.value=function(e){var h=e.style[x(this.keyName)];return this.canAdd(e,h)?h:""};return w}(ea(12).default);y.default=e},function(e,y){Object.defineProperty(y,"__esModule",{value:!0});
var x=function(){function e(e,w){for(var x=0;x<w.length;x++){var r=w[x];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(x,w,z){w&&e(x.prototype,w);z&&e(x,z);return x}}();e=function(){function e(x,w){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.quill=x;this.options=w;this.modules={}}x(e,[{key:"init",value:function(){var e=this;Object.keys(this.options.modules).forEach(function(w){null==
e.modules[w]&&e.addModule(w)})}},{key:"addModule",value:function(e){var w=this.quill.constructor.import("modules/"+e);this.modules[e]=new w(this.quill,this.options.modules[e]||{});return this.modules[e]}}]);return e}();e.DEFAULTS={modules:{}};e.themes={"default":e};y.default=e},function(e,y,ea){function x(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function ba(f,e){if("function"!==typeof e&&
null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}Object.defineProperty(y,"__esModule",{value:!0});var w=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,
n)}}return function(e,h,r){h&&f(e.prototype,h);r&&f(e,r);return e}}(),z=function ja(e,h,r){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,h);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ja(e,h,r)}else{if("value"in n)return n.value;h=n.get;return void 0===h?void 0:h.call(r)}},r=(e=ea(0))&&e.__esModule?e:{default:e},h=(ea=ea(7))&&ea.__esModule?ea:{default:ea};ea=function(e){function n(e){if(!(this instanceof n))throw new TypeError("Cannot call a class as a function");
var h=x(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e));h.contentNode=document.createElement("span");h.contentNode.setAttribute("contenteditable",!1);[].slice.call(h.domNode.childNodes).forEach(function(e){h.contentNode.appendChild(e)});h.leftGuard=document.createTextNode("\ufeff");h.rightGuard=document.createTextNode("\ufeff");h.domNode.appendChild(h.leftGuard);h.domNode.appendChild(h.contentNode);h.domNode.appendChild(h.rightGuard);return h}ba(n,e);w(n,[{key:"index",value:function(e,
h){return e===this.leftGuard?0:e===this.rightGuard?1:z(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"index",this).call(this,e,h)}},{key:"restore",value:function(e){var n=void 0,w=e.data.split("\ufeff").join("");e===this.leftGuard?this.prev instanceof h.default?(n=this.prev.length(),this.prev.insertAt(n,w),n={startNode:this.prev.domNode,startOffset:n+w.length}):(n=document.createTextNode(w),this.parent.insertBefore(r.default.create(n),this),n={startNode:n,startOffset:w.length}):e===this.rightGuard&&
(this.next instanceof h.default?(this.next.insertAt(0,w),n={startNode:this.next.domNode,startOffset:w.length}):(n=document.createTextNode(w),this.parent.insertBefore(r.default.create(n),this.next),n={startNode:n,startOffset:w.length}));e.data="\ufeff";return n}},{key:"update",value:function(e,h){var n=this;e.forEach(function(e){"characterData"!==e.type||e.target!==n.leftGuard&&e.target!==n.rightGuard||!(e=n.restore(e.target))||(h.range=e)})}}]);return n}(r.default.Embed);y.default=ea},function(e,
y,ea){Object.defineProperty(y,"__esModule",{value:!0});y.AlignStyle=y.AlignClass=y.AlignAttribute=void 0;var x=(e=ea(0))&&e.__esModule?e:{default:e};var ba={scope:x.default.Scope.BLOCK,whitelist:["right","center","justify"]};e=new x.default.Attributor.Attribute("align","align",ba);ea=new x.default.Attributor.Class("align","ql-align",ba);x=new x.default.Attributor.Style("align","text-align",ba);y.AlignAttribute=e;y.AlignClass=ea;y.AlignStyle=x},function(e,y,ea){Object.defineProperty(y,"__esModule",
{value:!0});y.BackgroundStyle=y.BackgroundClass=void 0;e=(e=ea(0))&&e.__esModule?e:{default:e};var x=ea(26);ea=new e.default.Attributor.Class("background","ql-bg",{scope:e.default.Scope.INLINE});e=new x.ColorAttributor("background","background-color",{scope:e.default.Scope.INLINE});y.BackgroundClass=ea;y.BackgroundStyle=e},function(e,y,ea){Object.defineProperty(y,"__esModule",{value:!0});y.DirectionStyle=y.DirectionClass=y.DirectionAttribute=void 0;var x=(e=ea(0))&&e.__esModule?e:{default:e};var ba=
{scope:x.default.Scope.BLOCK,whitelist:["rtl"]};e=new x.default.Attributor.Attribute("direction","dir",ba);ea=new x.default.Attributor.Class("direction","ql-direction",ba);x=new x.default.Attributor.Style("direction","direction",ba);y.DirectionAttribute=e;y.DirectionClass=ea;y.DirectionStyle=x},function(e,y,ea){function x(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,
enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});y.FontClass=y.FontStyle=void 0;var ba=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),w=function ca(e,f,n){null===e&&(e=Function.prototype);var h=
Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};ea=function(e){return e&&e.__esModule?e:{default:e}}(ea(0));var z={scope:ea.default.Scope.INLINE,whitelist:["serif","monospace"]};e=new ea.default.Attributor.Class("font","ql-font",z);ea=new (function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||
Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(f,e);ba(f,[{key:"value",value:function(e){return w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"value",this).call(this,e).replace(/["']/g,"")}}]);return f}(ea.default.Attributor.Style))("font","font-family",z);y.FontStyle=ea;y.FontClass=e},function(e,y,ea){Object.defineProperty(y,"__esModule",
{value:!0});y.SizeStyle=y.SizeClass=void 0;ea=(e=ea(0))&&e.__esModule?e:{default:e};e=new ea.default.Attributor.Class("size","ql-size",{scope:ea.default.Scope.INLINE,whitelist:["small","large","huge"]});ea=new ea.default.Attributor.Style("size","font-size",{scope:ea.default.Scope.INLINE,whitelist:["10px","18px","32px"]});y.SizeClass=e;y.SizeStyle=ea},function(e,y,ea){e.exports={align:{"":ea(76),center:ea(77),right:ea(78),justify:ea(79)},background:ea(80),blockquote:ea(81),bold:ea(82),clean:ea(83),
code:ea(58),"code-block":ea(58),color:ea(84),direction:{"":ea(85),rtl:ea(86)},"float":{center:ea(87),full:ea(88),left:ea(89),right:ea(90)},formula:ea(91),header:{1:ea(92),2:ea(93)},italic:ea(94),image:ea(95),indent:{"+1":ea(96),"-1":ea(97)},link:ea(98),list:{ordered:ea(99),bullet:ea(100),check:ea(101)},script:{sub:ea(102),"super":ea(103)},strike:ea(104),underline:ea(105),video:ea(106)}},function(e,y,ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function w(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}function z(e){e=e.ops[e.ops.length-1];return null==e?!1:null!=e.insert?"string"===typeof e.insert&&e.insert.endsWith("\n"):null!=
e.attributes?Object.keys(e.attributes).some(function(e){return null!=f.default.query(e,f.default.Scope.BLOCK)}):!1}function r(f){var e=f.reduce(function(f,e){return f+=e.delete||0},0);e=f.length()-e;z(f)&&--e;return e}Object.defineProperty(y,"__esModule",{value:!0});y.getLastChangeIndex=y.default=void 0;var h=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,
h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}();e=ea(0);var f=x(e);e=ea(5);var n=x(e);ea=ea(9);ea=function(f){function e(f,h){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");var r=ba(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,f,h));r.lastRecorded=0;r.ignoreChange=!1;r.clear();r.quill.on(n.default.events.EDITOR_CHANGE,function(f,e,h,w){f!==n.default.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&w!==n.default.sources.USER?r.transform(e):r.record(e,
h))});r.quill.keyboard.addBinding({key:"Z",shortKey:!0},r.undo.bind(r));r.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},r.redo.bind(r));/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"Y",shortKey:!0},r.redo.bind(r));return r}w(e,f);h(e,[{key:"change",value:function(f,e){if(0!==this.stack[f].length){var h=this.stack[f].pop();this.stack[e].push(h);this.lastRecorded=0;this.ignoreChange=!0;this.quill.updateContents(h[f],n.default.sources.USER);this.ignoreChange=!1;f=r(h[f]);
this.quill.setSelection(f)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(f,e){if(0!==f.ops.length){this.stack.redo=[];e=this.quill.getContents().diff(e);var h=Date.now();this.lastRecorded+this.options.delay>h&&0<this.stack.undo.length?(h=this.stack.undo.pop(),e=e.compose(h.undo),f=h.redo.compose(f)):this.lastRecorded=h;this.stack.undo.push({redo:f,undo:e});this.stack.undo.length>this.options.maxStack&&
this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(f){this.stack.undo.forEach(function(e){e.undo=f.transform(e.undo,!0);e.redo=f.transform(e.redo,!0)});this.stack.redo.forEach(function(e){e.undo=f.transform(e.undo,!0);e.redo=f.transform(e.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]);return e}(x(ea).default);ea.DEFAULTS={delay:1E3,maxStack:100,userOnly:!1};y.default=ea;y.getLastChangeIndex=r},function(e,y,
ea){function x(f){return f&&f.__esModule?f:{default:f}}function ba(f,e){if(!(f instanceof e))throw new TypeError("Cannot call a class as a function");}function w(f,e){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?f:e}function z(f,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);f.prototype=Object.create(e&&e.prototype,{constructor:{value:f,
enumerable:!1,writable:!0,configurable:!0}});e&&(Object.setPrototypeOf?Object.setPrototypeOf(f,e):f.__proto__=e)}function r(f,e){var h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:!1;e.forEach(function(e){var n=document.createElement("option");e===h?n.setAttribute("selected","selected"):n.setAttribute("value",e);f.appendChild(n)})}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.BaseTooltip=void 0;var h=function(){function f(f,e){for(var h=0;h<e.length;h++){var n=e[h];n.enumerable=
n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(f,n.key,n)}}return function(e,h,n){h&&f(e.prototype,h);n&&f(e,n);return e}}(),f=function ua(f,e,h){null===f&&(f=Function.prototype);var n=Object.getOwnPropertyDescriptor(f,e);if(void 0===n){if(f=Object.getPrototypeOf(f),null!==f)return ua(f,e,h)}else{if("value"in n)return n.value;e=n.get;return void 0===e?void 0:e.call(h)}};e=ea(3);e=x(e);var n=ea(2),ca=x(n);n=ea(8);var aa=x(n);n=ea(23);var ha=x(n);n=ea(34);n=x(n);
var fa=ea(59),ia=x(fa);fa=ea(60);var xa=x(fa);fa=ea(28);var la=x(fa);ea=ea(61);fa=x(ea);var na=[!1,"center","right","justify"],za="#000000 #e60000 #ff9900 #ffff00 #008a00 #0066cc #9933ff #ffffff #facccc #ffebcc #ffffcc #cce8cc #cce0f5 #ebd6ff #bbbbbb #f06666 #ffc266 #ffff66 #66b966 #66a3e0 #c285ff #888888 #a10000 #b26b00 #b2b200 #006100 #0047b2 #6b24b2 #444444 #5c0000 #663d00 #666600 #003700 #002966 #3d1466".split(" "),ra=[!1,"serif","monospace"],Da=["1","2","3",!1],sa=["small",!1,"large","huge"];
ea=function(e){function n(f,e){ba(this,n);var h=w(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,f,e));f.emitter.listenDOM("click",document.body,function Ga(e){if(!document.body.contains(f.root))return document.body.removeEventListener("click",Ga);null==h.tooltip||h.tooltip.root.contains(e.target)||document.activeElement===h.tooltip.textbox||h.quill.hasFocus()||h.tooltip.hide();null!=h.pickers&&h.pickers.forEach(function(f){f.container.contains(e.target)||f.close()})});return h}z(n,e);h(n,
[{key:"addModule",value:function(e){var h=f(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"addModule",this).call(this,e);"toolbar"===e&&this.extendToolbar(h);return h}},{key:"buildButtons",value:function(f,e){f.forEach(function(f){(f.getAttribute("class")||"").split(/\s+/).forEach(function(h){if(h.startsWith("ql-")&&(h=h.slice(3),null!=e[h]))if("direction"===h)f.innerHTML=e[h][""]+e[h].rtl;else if("string"===typeof e[h])f.innerHTML=e[h];else{var n=f.value||"";null!=n&&e[h][n]&&(f.innerHTML=
e[h][n])}})})}},{key:"buildPickers",value:function(f,e){var h=this;this.pickers=f.map(function(f){if(f.classList.contains("ql-align"))return null==f.querySelector("option")&&r(f,na),new xa.default(f,e.align);if(f.classList.contains("ql-background")||f.classList.contains("ql-color")){var h=f.classList.contains("ql-background")?"background":"color";null==f.querySelector("option")&&r(f,za,"background"===h?"#ffffff":"#000000");return new ia.default(f,e[h])}null==f.querySelector("option")&&(f.classList.contains("ql-font")?
r(f,ra):f.classList.contains("ql-header")?r(f,Da):f.classList.contains("ql-size")&&r(f,sa));return new la.default(f)});this.quill.on(aa.default.events.EDITOR_CHANGE,function(){h.pickers.forEach(function(f){f.update()})})}}]);return n}(n.default);ea.DEFAULTS=(0,e.default)(!0,{},n.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var f=this,e=this.container.querySelector("input.ql-image[type=file]");null==e&&(e=document.createElement("input"),
e.setAttribute("type","file"),e.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),e.classList.add("ql-image"),e.addEventListener("change",function(){if(null!=e.files&&null!=e.files[0]){var h=new FileReader;h.onload=function(h){var n=f.quill.getSelection(!0);f.quill.updateContents((new ca.default).retain(n.index).delete(n.length).insert({image:h.target.result}),aa.default.sources.USER);f.quill.setSelection(n.index+1,aa.default.sources.SILENT);e.value=""};h.readAsDataURL(e.files[0])}}),
this.container.appendChild(e));e.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});e=function(f){function e(f,h){ba(this,e);f=w(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,f,h));f.textbox=f.root.querySelector('input[type="text"]');f.listen();return f}z(e,f);h(e,[{key:"listen",value:function(){var f=this;this.textbox.addEventListener("keydown",function(e){ha.default.match(e,"enter")?(f.save(),e.preventDefault()):ha.default.match(e,"escape")&&(f.cancel(),e.preventDefault())})}},
{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var f=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"link",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden");this.root.classList.add("ql-editing");null!=e?this.textbox.value=e:f!==this.root.getAttribute("data-mode")&&(this.textbox.value="");this.position(this.quill.getBounds(this.quill.selection.savedRange));this.textbox.select();this.textbox.setAttribute("placeholder",
this.textbox.getAttribute("data-"+f)||"");this.root.setAttribute("data-mode",f)}},{key:"restoreFocus",value:function(){var f=this.quill.scrollingContainer.scrollTop;this.quill.focus();this.quill.scrollingContainer.scrollTop=f}},{key:"save",value:function(){var f=this.textbox.value;switch(this.root.getAttribute("data-mode")){case "link":var e=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",f,aa.default.sources.USER),delete this.linkRange):(this.restoreFocus(),
this.quill.format("link",f,aa.default.sources.USER));this.quill.root.scrollTop=e;break;case "video":f=(e=f.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||f.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?(e[1]||"https")+"://www.youtube.com/embed/"+e[2]+"?showinfo=0":(e=f.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(e[1]||"https")+"://player.vimeo.com/video/"+e[2]+"/":f;case "formula":f&&(e=this.quill.getSelection(!0),null!=
e&&(e=e.index+e.length,this.quill.insertEmbed(e,this.root.getAttribute("data-mode"),f,aa.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(e+1," ",aa.default.sources.USER),this.quill.setSelection(e+2,aa.default.sources.USER)))}this.textbox.value="";this.hide()}}]);return e}(fa.default);y.BaseTooltip=e;y.default=ea},function(e,y){Object.defineProperty(y,"__esModule",{value:!0});e=function(){function e(){this.head=this.tail=null;this.length=0}e.prototype.append=
function(){for(var e=[],x=0;x<arguments.length;x++)e[x]=arguments[x];this.insertBefore(e[0],null);1<e.length&&this.append.apply(this,e.slice(1))};e.prototype.contains=function(e){for(var x,w=this.iterator();x=w();)if(x===e)return!0;return!1};e.prototype.insertBefore=function(e,x){e&&(e.next=x,null!=x?(e.prev=x.prev,null!=x.prev&&(x.prev.next=e),x.prev=e,x===this.head&&(this.head=e)):null!=this.tail?(this.tail.next=e,e.prev=this.tail,this.tail=e):(e.prev=null,this.head=this.tail=e),this.length+=1)};
e.prototype.offset=function(e){for(var x=0,w=this.head;null!=w;){if(w===e)return x;x+=w.length();w=w.next}return-1};e.prototype.remove=function(e){this.contains(e)&&(null!=e.prev&&(e.prev.next=e.next),null!=e.next&&(e.next.prev=e.prev),e===this.head&&(this.head=e.next),e===this.tail&&(this.tail=e.prev),--this.length)};e.prototype.iterator=function(e){void 0===e&&(e=this.head);return function(){var x=e;null!=e&&(e=e.next);return x}};e.prototype.find=function(e,x){void 0===x&&(x=!1);for(var w,z=this.iterator();w=
z();){var r=w.length();if(e<r||x&&e===r&&(null==w.next||0!==w.next.length()))return[w,e];e-=r}return[null,0]};e.prototype.forEach=function(e){for(var x,w=this.iterator();x=w();)e(x)};e.prototype.forEachAt=function(e,x,w){if(!(0>=x))for(var z=this.find(e),r=e-z[1],h=this.iterator(z[0]);(z=h())&&r<e+x;){var f=z.length();e>r?w(z,e-r,Math.min(x,r+f-e)):w(z,0,Math.min(f,e+x-r));r+=f}};e.prototype.map=function(e){return this.reduce(function(x,w){x.push(e(w));return x},[])};e.prototype.reduce=function(e,
x){for(var w,z=this.iterator();w=z();)x=e(x,w);return x};return e}();y.default=e},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,f){e.__proto__=f}||function(e,f){for(var h in f)f.hasOwnProperty(h)&&(e[h]=f[h])};return function(h,f){function n(){this.constructor=h}e(h,f);h.prototype=null===f?Object.create(f):(n.prototype=f.prototype,new n)}}();Object.defineProperty(y,"__esModule",{value:!0});var ba=ea(17),w=ea(1),z={attributes:!0,
characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0};e=function(e){function h(f){var h=e.call(this,f)||this;h.scroll=h;h.observer=new MutationObserver(function(f){h.update(f)});h.observer.observe(h.domNode,z);h.attach();return h}x(h,e);h.prototype.detach=function(){e.prototype.detach.call(this);this.observer.disconnect()};h.prototype.deleteAt=function(f,h){this.update();0===f&&h===this.length()?this.children.forEach(function(f){f.remove()}):e.prototype.deleteAt.call(this,f,h)};h.prototype.formatAt=
function(f,h,r,w){this.update();e.prototype.formatAt.call(this,f,h,r,w)};h.prototype.insertAt=function(f,h,r){this.update();e.prototype.insertAt.call(this,f,h,r)};h.prototype.optimize=function(f,h){function n(f){null!=f.domNode[w.DATA_KEY]&&null!=f.domNode[w.DATA_KEY].mutations&&(f instanceof ba.default&&f.children.forEach(n),f.optimize(h))}function r(f,e){void 0===e&&(e=!0);null!=f&&f!==x&&null!=f.domNode.parentNode&&(null==f.domNode[w.DATA_KEY].mutations&&(f.domNode[w.DATA_KEY].mutations=[]),e&&
r(f.parent))}var x=this;void 0===f&&(f=[]);void 0===h&&(h={});e.prototype.optimize.call(this,h);for(var z=[].slice.call(this.observer.takeRecords());0<z.length;)f.push(z.pop());for(var y=f,ea=0;0<y.length;ea+=1){if(100<=ea)throw Error("[Parchment] Maximum optimize iterations reached");y.forEach(function(f){var e=w.find(f.target,!0);null!=e&&(e.domNode===f.target&&("childList"===f.type?(r(w.find(f.previousSibling,!1)),[].forEach.call(f.addedNodes,function(f){f=w.find(f,!1);r(f,!1);f instanceof ba.default&&
f.children.forEach(function(f){r(f,!1)})})):"attributes"===f.type&&r(e.prev)),r(e))});this.children.forEach(n);y=[].slice.call(this.observer.takeRecords());for(z=y.slice();0<z.length;)f.push(z.pop())}};h.prototype.update=function(f,h){var n=this;void 0===h&&(h={});f=f||this.observer.takeRecords();f.map(function(f){var e=w.find(f.target,!0);if(null==e)return null;if(null==e.domNode[w.DATA_KEY].mutations)return e.domNode[w.DATA_KEY].mutations=[f],e;e.domNode[w.DATA_KEY].mutations.push(f);return null}).forEach(function(f){null!=
f&&f!==n&&null!=f.domNode[w.DATA_KEY]&&f.update(f.domNode[w.DATA_KEY].mutations||[],h)});null!=this.domNode[w.DATA_KEY].mutations&&e.prototype.update.call(this,this.domNode[w.DATA_KEY].mutations,h);this.optimize(f,h)};h.blotName="scroll";h.defaultChild="block";h.scope=w.Scope.BLOCK_BLOT;h.tagName="DIV";return h}(ba.default);y.default=e},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,h){e.__proto__=h}||function(e,h){for(var f in h)h.hasOwnProperty(f)&&
(e[f]=h[f])};return function(r,h){function f(){this.constructor=r}e(r,h);r.prototype=null===h?Object.create(h):(f.prototype=h.prototype,new f)}}();Object.defineProperty(y,"__esModule",{value:!0});var ba=ea(18),w=ea(1);e=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}x(r,e);r.formats=function(h){if(h.tagName!==r.tagName)return e.formats.call(this,h)};r.prototype.format=function(h,f){var n=this;h!==this.statics.blotName||f?e.prototype.format.call(this,h,f):(this.children.forEach(function(f){f instanceof
ba.default||(f=f.wrap(r.blotName,!0));n.attributes.copy(f)}),this.unwrap())};r.prototype.formatAt=function(h,f,n,r){null!=this.formats()[n]||w.query(n,w.Scope.ATTRIBUTE)?this.isolate(h,f).format(n,r):e.prototype.formatAt.call(this,h,f,n,r)};r.prototype.optimize=function(h){e.prototype.optimize.call(this,h);h=this.formats();if(0===Object.keys(h).length)return this.unwrap();var f=this.next,n;if(n=f instanceof r&&f.prev===this)a:if(n=f.formats(),Object.keys(h).length!==Object.keys(n).length)n=!1;else{for(var w in h)if(h[w]!==
n[w]){n=!1;break a}n=!0}n&&(f.moveChildren(this),f.remove())};r.blotName="inline";r.scope=w.Scope.INLINE_BLOT;r.tagName="SPAN";return r}(ba.default);y.default=e},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var h in r)r.hasOwnProperty(h)&&(e[h]=r[h])};return function(w,r){function h(){this.constructor=w}e(w,r);w.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();
Object.defineProperty(y,"__esModule",{value:!0});e=ea(18);var ba=ea(1);ea=function(e){function w(){return null!==e&&e.apply(this,arguments)||this}x(w,e);w.formats=function(r){var h=ba.query(w.blotName).tagName;if(r.tagName!==h)return e.formats.call(this,r)};w.prototype.format=function(r,h){null!=ba.query(r,ba.Scope.BLOCK)&&(r!==this.statics.blotName||h?e.prototype.format.call(this,r,h):this.replaceWith(w.blotName))};w.prototype.formatAt=function(r,h,f,n){null!=ba.query(f,ba.Scope.BLOCK)?this.format(f,
n):e.prototype.formatAt.call(this,r,h,f,n)};w.prototype.insertAt=function(r,h,f){null==f||null!=ba.query(h,ba.Scope.INLINE)?e.prototype.insertAt.call(this,r,h,f):(r=this.split(r),h=ba.create(h,f),r.parent.insertBefore(h,r))};w.prototype.update=function(r,h){navigator.userAgent.match(/Trident/)?this.build():e.prototype.update.call(this,r,h)};w.blotName="block";w.scope=ba.Scope.BLOCK_BLOT;w.tagName="P";return w}(e.default);y.default=ea},function(e,y,ea){var x=this&&this.__extends||function(){var e=
Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,x){e.__proto__=x}||function(e,x){for(var r in x)x.hasOwnProperty(r)&&(e[r]=x[r])};return function(w,x){function r(){this.constructor=w}e(w,x);w.prototype=null===x?Object.create(x):(r.prototype=x.prototype,new r)}}();Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){return null!==e&&e.apply(this,arguments)||this}x(w,e);w.formats=function(){};w.prototype.format=function(w,r){e.prototype.formatAt.call(this,0,
this.length(),w,r)};w.prototype.formatAt=function(w,r,h,f){0===w&&r===this.length()?this.format(h,f):e.prototype.formatAt.call(this,w,r,h,f)};w.prototype.formats=function(){return this.statics.formats(this.domNode)};return w}(ea(19).default);y.default=e},function(e,y,ea){var x=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var h in r)r.hasOwnProperty(h)&&(e[h]=r[h])};return function(w,r){function h(){this.constructor=
w}e(w,r);w.prototype=null===r?Object.create(r):(h.prototype=r.prototype,new h)}}();Object.defineProperty(y,"__esModule",{value:!0});e=ea(19);var ba=ea(1);ea=function(e){function w(r){r=e.call(this,r)||this;r.text=r.statics.value(r.domNode);return r}x(w,e);w.create=function(e){return document.createTextNode(e)};w.value=function(e){e=e.data;e.normalize&&(e=e.normalize());return e};w.prototype.deleteAt=function(e,h){this.domNode.data=this.text=this.text.slice(0,e)+this.text.slice(e+h)};w.prototype.index=
function(e,h){return this.domNode===e?h:-1};w.prototype.insertAt=function(r,h,f){null==f?(this.text=this.text.slice(0,r)+h+this.text.slice(r),this.domNode.data=this.text):e.prototype.insertAt.call(this,r,h,f)};w.prototype.length=function(){return this.text.length};w.prototype.optimize=function(r){e.prototype.optimize.call(this,r);this.text=this.statics.value(this.domNode);0===this.text.length?this.remove():this.next instanceof w&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),
this.next.remove())};w.prototype.position=function(e){return[this.domNode,e]};w.prototype.split=function(e,h){void 0===h&&(h=!1);if(!h){if(0===e)return this;if(e===this.length())return this.next}e=ba.create(this.domNode.splitText(e));this.parent.insertBefore(e,this.next);this.text=this.statics.value(this.domNode);return e};w.prototype.update=function(e){var h=this;e.some(function(f){return"characterData"===f.type&&f.target===h.domNode})&&(this.text=this.statics.value(this.domNode))};w.prototype.value=
function(){return this.text};w.blotName="text";w.scope=ba.Scope.INLINE_BLOT;return w}(e.default);y.default=ea},function(){var e=document.createElement("div");e.classList.toggle("test-class",!1);if(e.classList.contains("test-class")){var y=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,x){return 1<arguments.length&&!this.contains(e)===!x?x:y.call(this,e)}}String.prototype.startsWith||(String.prototype.startsWith=function(e,x){return this.substr(x||0,e.length)===e});String.prototype.endsWith||
(String.prototype.endsWith=function(e,x){var y=this.toString();if("number"!==typeof x||!isFinite(x)||Math.floor(x)!==x||x>y.length)x=y.length;x-=e.length;e=y.indexOf(e,x);return-1!==e&&e===x});Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(e,x){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!==typeof e)throw new TypeError("predicate must be a function");for(var y=Object(this),w=y.length>>>0,z,r=0;r<w;r++)if(z=
y[r],e.call(x,z,r,y))return z}});document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1);document.execCommand("autoUrlDetect",!1,!1)})},function(e){function x(e,n,x){if(e==n)return e?[[0,e]]:[];if(0>x||e.length<x)x=null;var aa=ba(e,n),z=e.substring(0,aa);e=e.substring(aa);n=n.substring(aa);aa=w(e,n);var ca=e.substring(e.length-aa);e=e.substring(0,e.length-aa);n=n.substring(0,n.length-aa);e=y(e,n);z&&e.unshift([0,z]);ca&&e.push([0,ca]);r(e);null!=
x&&(e=h(e,x));return e=f(e)}function y(f,e){if(!f)return[[1,e]];if(!e)return[[-1,f]];var h=f.length>e.length?f:e;var n=f.length>e.length?e:f,r=h.indexOf(n);if(-1!=r)return h=[[1,h.substring(0,r)],[0,n],[1,h.substring(r+n.length)]],f.length>e.length&&(h[0][0]=h[2][0]=-1),h;if(1==n.length)return[[-1,f],[1,e]];if(h=z(f,e))return e=h[1],n=h[3],f=h[4],h=x(h[0],h[2]),e=x(e,n),h.concat([[0,f]],e);a:{h=f.length;n=e.length;r=Math.ceil((h+n)/2);for(var w=2*r,aa=Array(w),y=Array(w),ba=0;ba<w;ba++)aa[ba]=-1,
y[ba]=-1;aa[r+1]=0;y[r+1]=0;ba=h-n;for(var ca=0!=ba%2,ea=0,ha=0,fa=0,ia=0,va=0;va<r;va++){for(var Fa=-va+ea;Fa<=va-ha;Fa+=2){var ua=r+Fa;var Ca=Fa==-va||Fa!=va&&aa[ua-1]<aa[ua+1]?aa[ua+1]:aa[ua-1]+1;for(var Aa=Ca-Fa;Ca<h&&Aa<n&&f.charAt(Ca)==e.charAt(Aa);)Ca++,Aa++;aa[ua]=Ca;if(Ca>h)ha+=2;else if(Aa>n)ea+=2;else if(ca&&(ua=r+ba-Fa,0<=ua&&ua<w&&-1!=y[ua])){var Ea=h-y[ua];if(Ca>=Ea){f=da(f,e,Ca,Aa);break a}}}for(Fa=-va+fa;Fa<=va-ia;Fa+=2){ua=r+Fa;Ea=Fa==-va||Fa!=va&&y[ua-1]<y[ua+1]?y[ua+1]:y[ua-1]+
1;for(Ca=Ea-Fa;Ea<h&&Ca<n&&f.charAt(h-Ea-1)==e.charAt(n-Ca-1);)Ea++,Ca++;y[ua]=Ea;if(Ea>h)ia+=2;else if(Ca>n)fa+=2;else if(!ca&&(ua=r+ba-Fa,0<=ua&&ua<w&&-1!=aa[ua]&&(Ca=aa[ua],Aa=r+Ca-ua,Ea=h-Ea,Ca>=Ea))){f=da(f,e,Ca,Aa);break a}}}f=[[-1,f],[1,e]]}return f}function da(f,e,h,n){var r=f.substring(h),w=e.substring(n);f=x(f.substring(0,h),e.substring(0,n));r=x(r,w);return f.concat(r)}function ba(f,e){if(!f||!e||f.charAt(0)!=e.charAt(0))return 0;for(var h=0,n=Math.min(f.length,e.length),r=n,w=0;h<r;)f.substring(w,
r)==e.substring(w,r)?w=h=r:n=r,r=Math.floor((n-h)/2+h);return r}function w(f,e){if(!f||!e||f.charAt(f.length-1)!=e.charAt(e.length-1))return 0;for(var h=0,n=Math.min(f.length,e.length),r=n,w=0;h<r;)f.substring(f.length-r,f.length-w)==e.substring(e.length-r,e.length-w)?w=h=r:n=r,r=Math.floor((n-h)/2+h);return r}function z(f,e){function h(f,e,h){for(var n=f.substring(h,h+Math.floor(f.length/4)),r=-1,x="",aa,z,y,ca;-1!=(r=e.indexOf(n,r+1));){var ea=ba(f.substring(h),e.substring(r)),da=w(f.substring(0,
h),e.substring(0,r));x.length<da+ea&&(x=e.substring(r-da,r)+e.substring(r,r+ea),aa=f.substring(0,h-da),z=f.substring(h+ea),y=e.substring(0,r-da),ca=e.substring(r+ea))}return 2*x.length>=f.length?[aa,z,y,ca,x]:null}var n=f.length>e.length?f:e,r=f.length>e.length?e:f;if(4>n.length||2*r.length<n.length)return null;var x=h(n,r,Math.ceil(n.length/4));n=h(n,r,Math.ceil(n.length/2));if(x||n)x=n?x?x[4].length>n[4].length?x:n:n:x;else return null;f.length>e.length?(f=x[0],e=x[1],n=x[2],r=x[3]):(n=x[0],r=x[1],
f=x[2],e=x[3]);return[f,e,n,r,x[4]]}function r(f){f.push([0,""]);for(var e=0,h=0,n=0,x="",z="",y;e<f.length;)switch(f[e][0]){case 1:n++;z+=f[e][1];e++;break;case -1:h++;x+=f[e][1];e++;break;case 0:1<h+n?(0!==h&&0!==n&&(y=ba(z,x),0!==y&&(0<e-h-n&&0==f[e-h-n-1][0]?f[e-h-n-1][1]+=z.substring(0,y):(f.splice(0,0,[0,z.substring(0,y)]),e++),z=z.substring(y),x=x.substring(y)),y=w(z,x),0!==y&&(f[e][1]=z.substring(z.length-y)+f[e][1],z=z.substring(0,z.length-y),x=x.substring(0,x.length-y))),0===h?f.splice(e-
n,h+n,[1,z]):0===n?f.splice(e-h,h+n,[-1,x]):f.splice(e-h-n,h+n,[-1,x],[1,z]),e=e-h-n+(h?1:0)+(n?1:0)+1):0!==e&&0==f[e-1][0]?(f[e-1][1]+=f[e][1],f.splice(e,1)):e++,h=n=0,z=x=""}""===f[f.length-1][1]&&f.pop();h=!1;for(e=1;e<f.length-1;)0==f[e-1][0]&&0==f[e+1][0]&&(f[e][1].substring(f[e][1].length-f[e-1][1].length)==f[e-1][1]?(f[e][1]=f[e-1][1]+f[e][1].substring(0,f[e][1].length-f[e-1][1].length),f[e+1][1]=f[e-1][1]+f[e+1][1],f.splice(e-1,1),h=!0):f[e][1].substring(0,f[e+1][1].length)==f[e+1][1]&&(f[e-
1][1]+=f[e+1][1],f[e][1]=f[e][1].substring(f[e+1][1].length)+f[e+1][1],f.splice(e+1,1),h=!0)),e++;h&&r(f)}function h(f,e){a:{var h=f;if(0===e)var r=[0,h];else{var w=0;for(r=0;r<h.length;r++){var x=h[r];if(-1===x[0]||0===x[0]){var z=w+x[1].length;if(e===z){r=[r+1,h];break a}if(e<z){h=h.slice();w=e-w;e=[x[0],x[1].slice(0,w)];x=[x[0],x[1].slice(w)];h.splice(r,1,e,x);r=[r+1,h];break a}w=z}}throw Error("cursor_pos is out of bounds!");}}h=r[1];r=r[0];e=h[r];x=h[r+1];return null==e||0!==e[0]?f:null!=x&&
e[1]+x[1]===x[1]+e[1]?(h.splice(r,2,x,e),n(h,r,2)):null!=x&&0===x[1].indexOf(e[1])?(h.splice(r,2,[x[0],e[1]],[0,e[1]]),f=x[1].slice(e[1].length),0<f.length&&h.splice(r+2,0,[x[0],f]),n(h,r,3)):f}function f(f){function e(f){return 55296<=f.charCodeAt(f.length-1)&&56319>=f.charCodeAt(f.length-1)}function h(f){return 56320<=f.charCodeAt(0)&&57343>=f.charCodeAt(0)}for(var n=!1,r=2;r<f.length;r+=1)0===f[r-2][0]&&e(f[r-2][1])&&-1===f[r-1][0]&&h(f[r-1][1])&&1===f[r][0]&&h(f[r][1])&&(n=!0,f[r-1][1]=f[r-2][1].slice(-1)+
f[r-1][1],f[r][1]=f[r-2][1].slice(-1)+f[r][1],f[r-2][1]=f[r-2][1].slice(0,-1));if(!n)return f;n=[];for(r=0;r<f.length;r+=1)0<f[r][1].length&&n.push(f[r]);return n}function n(f,e,h){for(h=e+h-1;0<=h&&h>=e-1;h--)if(h+1<f.length){var n=f[h],r=f[h+1];n[0]===r[1]&&f.splice(h,2,[n[0],n[1]+r[1]])}return f}x.INSERT=1;x.DELETE=-1;x.EQUAL=0;e.exports=x},function(e,y){function x(e){var x=[],w;for(w in e)x.push(w);return x}y=e.exports="function"===typeof Object.keys?Object.keys:x;y.shim=x},function(e,y){function x(e){return"[object Arguments]"==
Object.prototype.toString.call(e)}function da(e){return e&&"object"==typeof e&&"number"==typeof e.length&&Object.prototype.hasOwnProperty.call(e,"callee")&&!Object.prototype.propertyIsEnumerable.call(e,"callee")||!1}y="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();y=e.exports=y?x:da;y.supported=x;y.unsupported=da},function(e){function x(){}function y(e,r,h){this.fn=e;this.context=r;this.once=h||!1}function da(){this._events=new x;this._eventsCount=0}var ba=Object.prototype.hasOwnProperty,
w="~";Object.create&&(x.prototype=Object.create(null),(new x).__proto__||(w=!1));da.prototype.eventNames=function(){var e=[],r,h;if(0===this._eventsCount)return e;for(h in r=this._events)ba.call(r,h)&&e.push(w?h.slice(1):h);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(r)):e};da.prototype.listeners=function(e,r){e=this._events[w?w+e:e];if(r)return!!e;if(!e)return[];if(e.fn)return[e.fn];r=0;for(var h=e.length,f=Array(h);r<h;r++)f[r]=e[r].fn;return f};da.prototype.emit=function(e,
r,h,f,n,x){var z=w?w+e:e;if(!this._events[z])return!1;z=this._events[z];var y=arguments.length,ba;if(z.fn){z.once&&this.removeListener(e,z.fn,void 0,!0);switch(y){case 1:return z.fn.call(z.context),!0;case 2:return z.fn.call(z.context,r),!0;case 3:return z.fn.call(z.context,r,h),!0;case 4:return z.fn.call(z.context,r,h,f),!0;case 5:return z.fn.call(z.context,r,h,f,n),!0;case 6:return z.fn.call(z.context,r,h,f,n,x),!0}var ca=1;for(ba=Array(y-1);ca<y;ca++)ba[ca-1]=arguments[ca];z.fn.apply(z.context,
ba)}else{var ea=z.length;for(ca=0;ca<ea;ca++)switch(z[ca].once&&this.removeListener(e,z[ca].fn,void 0,!0),y){case 1:z[ca].fn.call(z[ca].context);break;case 2:z[ca].fn.call(z[ca].context,r);break;case 3:z[ca].fn.call(z[ca].context,r,h);break;case 4:z[ca].fn.call(z[ca].context,r,h,f);break;default:if(!ba){var da=1;for(ba=Array(y-1);da<y;da++)ba[da-1]=arguments[da]}z[ca].fn.apply(z[ca].context,ba)}}return!0};da.prototype.on=function(e,r,h){r=new y(r,h||this);e=w?w+e:e;this._events[e]?this._events[e].fn?
this._events[e]=[this._events[e],r]:this._events[e].push(r):(this._events[e]=r,this._eventsCount++);return this};da.prototype.once=function(e,r,h){r=new y(r,h||this,!0);e=w?w+e:e;this._events[e]?this._events[e].fn?this._events[e]=[this._events[e],r]:this._events[e].push(r):(this._events[e]=r,this._eventsCount++);return this};da.prototype.removeListener=function(e,r,h,f){e=w?w+e:e;if(!this._events[e])return this;if(!r)return 0===--this._eventsCount?this._events=new x:delete this._events[e],this;var n=
this._events[e];if(n.fn)n.fn!==r||f&&!n.once||h&&n.context!==h||(0===--this._eventsCount?this._events=new x:delete this._events[e]);else{for(var z=0,aa=[],y=n.length;z<y;z++)(n[z].fn!==r||f&&!n[z].once||h&&n[z].context!==h)&&aa.push(n[z]);aa.length?this._events[e]=1===aa.length?aa[0]:aa:0===--this._eventsCount?this._events=new x:delete this._events[e]}return this};da.prototype.removeAllListeners=function(e){e?(e=w?w+e:e,this._events[e]&&(0===--this._eventsCount?this._events=new x:delete this._events[e])):
(this._events=new x,this._eventsCount=0);return this};da.prototype.off=da.prototype.removeListener;da.prototype.addListener=da.prototype.on;da.prototype.setMaxListeners=function(){return this};da.prefixed=w;da.EventEmitter=da;"undefined"!==typeof e&&(e.exports=da)},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f,h){f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}function r(e,f,h){return"object"===("undefined"===typeof f?"undefined":na(f))?Object.keys(f).reduce(function(e,h){return r(e,h,f[h])},
e):e.reduce(function(e,n){return n.attributes&&n.attributes[f]?e.push(n):e.insert(n.insert,(0,Da.default)({},ba({},f,h),n.attributes))},new sa.default)}function h(e){return e.nodeType!==Node.ELEMENT_NODE?{}:e["__ql-computed-style"]||(e["__ql-computed-style"]=window.getComputedStyle(e))}function f(e,f){for(var h="",n=e.ops.length-1;0<=n&&h.length<f.length;--n){var r=e.ops[n];if("string"!==typeof r.insert)break;h=r.insert+h}return h.slice(-1*f.length)===f}function n(e){if(0===e.childNodes.length)return!1;
e=h(e);return-1<["block","list-item"].indexOf(e.display)}function ca(e,f,h){return e.nodeType===e.TEXT_NODE?h.reduce(function(f,h){return h(e,f)},new sa.default):e.nodeType===e.ELEMENT_NODE?[].reduce.call(e.childNodes||[],function(n,r){var w=ca(r,f,h);r.nodeType===e.ELEMENT_NODE&&(w=f.reduce(function(e,f){return f(r,e)},w),w=(r["__ql-matcher"]||[]).reduce(function(e,f){return f(r,e)},w));return n.concat(w)},new sa.default):new sa.default}function aa(e,f,h){return r(h,e,!0)}function fa(e,f){var h=
oa.default.Attributor.Attribute.keys(e),n=oa.default.Attributor.Class.keys(e),w=oa.default.Attributor.Style.keys(e),x={};h.concat(n).concat(w).forEach(function(f){var h=oa.default.query(f,oa.default.Scope.ATTRIBUTE);if(null!=h&&(x[h.attrName]=h.value(e),x[h.attrName]))return;h=Ja[f];null==h||h.attrName!==f&&h.keyName!==f||(x[h.attrName]=h.value(e)||void 0);h=ya[f];null==h||h.attrName!==f&&h.keyName!==f||(h=ya[f],x[h.attrName]=h.value(e)||void 0)});0<Object.keys(x).length&&(f=r(f,x));return f}function ha(e,
f){var h=oa.default.query(e);if(null==h)return f;if(h.prototype instanceof oa.default.Embed){var n={},w=h.value(e);null!=w&&(n[h.blotName]=w,f=(new sa.default).insert(n,h.formats(e)))}else"function"===typeof h.formats&&(f=r(f,h.blotName,h.formats(e)));return f}function ia(e,h){f(h,"\n")||(n(e)||0<h.length()&&e.nextSibling&&n(e.nextSibling))&&h.insert("\n");return h}function xa(e,r){if(n(e)&&null!=e.nextElementSibling&&!f(r,"\n\n")){var w=e.offsetHeight+parseFloat(h(e).marginTop)+parseFloat(h(e).marginBottom);
e.nextElementSibling.offsetTop>e.offsetTop*****w&&r.insert("\n")}return r}function la(e,f){var r=e.data;if("O:P"===e.parentNode.tagName)return f.insert(r.trim());if(0===r.trim().length&&e.parentNode.classList.contains("ql-clipboard"))return f;if(!h(e.parentNode).whiteSpace.startsWith("pre")){var w=function(e,f){f=f.replace(/[^\u00a0]/g,"");return 1>f.length&&e?" ":f};r=r.replace(/\r\n/g," ").replace(/\n/g," ");r=r.replace(/\s\s+/g,w.bind(w,!0));if(null==e.previousSibling&&n(e.parentNode)||null!=e.previousSibling&&
n(e.previousSibling))r=r.replace(/^\s+/,w.bind(w,!1));if(null==e.nextSibling&&n(e.parentNode)||null!=e.nextSibling&&n(e.nextSibling))r=r.replace(/\s+$/,w.bind(w,!1))}return f.insert(r)}Object.defineProperty(y,"__esModule",{value:!0});y.matchText=y.matchSpacing=y.matchNewline=y.matchBlot=y.matchAttributor=y.default=void 0;var na="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?
"symbol":typeof e},za=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),z;!(n=(z=x.next()).done)&&(h.push(z.value),!f||h.length!==f);n=!0);}catch(Qa){r=!0,w=Qa}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ra=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=
n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();e=ea(3);var Da=x(e);e=ea(2);var sa=x(e);e=ea(0);var oa=x(e);e=ea(5);var ma=x(e);e=ea(10);e=x(e);var va=ea(9);va=x(va);var Fa=ea(36),ua=ea(37),Ca=ea(13),Aa=x(Ca);Ca=ea(26);var Ea=ea(38),Ga=ea(39);ea=ea(40);var Ba=(0,e.default)("quill:clipboard"),Ia=[[Node.TEXT_NODE,la],[Node.TEXT_NODE,ia],["br",function(e,h){f(h,"\n")||h.insert("\n");return h}],
[Node.ELEMENT_NODE,ia],[Node.ELEMENT_NODE,ha],[Node.ELEMENT_NODE,xa],[Node.ELEMENT_NODE,fa],[Node.ELEMENT_NODE,function(e,f){var n={},w=e.style||{};w.fontStyle&&"italic"===h(e).fontStyle&&(n.italic=!0);w.fontWeight&&(h(e).fontWeight.startsWith("bold")||700<=parseInt(h(e).fontWeight))&&(n.bold=!0);0<Object.keys(n).length&&(f=r(f,n));0<parseFloat(w.textIndent||0)&&(f=(new sa.default).insert("\t").concat(f));return f}],["li",function(e,h){var n=oa.default.query(e);if(null==n||"list-item"!==n.blotName||
!f(h,"\n"))return h;n=-1;for(e=e.parentNode;!e.classList.contains("ql-clipboard");)"list"===(oa.default.query(e)||{}).blotName&&(n+=1),e=e.parentNode;return 0>=n?h:h.compose((new sa.default).retain(h.length()-1).retain(1,{indent:n}))}],["b",aa.bind(aa,"bold")],["i",aa.bind(aa,"italic")],["style",function(){return new sa.default}]],Ja=[Fa.AlignAttribute,Ea.DirectionAttribute].reduce(function(e,f){e[f.keyName]=f;return e},{}),ya=[Fa.AlignStyle,ua.BackgroundStyle,Ca.ColorStyle,Ea.DirectionStyle,Ga.FontStyle,
ea.SizeStyle].reduce(function(e,f){e[f.keyName]=f;return e},{});ea=function(e){function h(e,f){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var n=w(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,e,f));n.quill.root.addEventListener("paste",n.onPaste.bind(n));n.container=n.quill.addContainer("ql-clipboard");n.container.setAttribute("contenteditable",!0);n.container.setAttribute("tabindex",-1);n.matchers=[];Ia.concat(n.options.matchers).forEach(function(e){e=
za(e,2);var h=e[1];(f.matchVisual||h!==xa)&&n.addMatcher(e[0],h)});return n}z(h,e);ra(h,[{key:"addMatcher",value:function(e,f){this.matchers.push([e,f])}},{key:"convert",value:function(e){if("string"===typeof e)return this.container.innerHTML=e.replace(/>\r?\n +</g,"><"),this.convert();e=this.quill.getFormat(this.quill.selection.savedRange.index);if(e[Aa.default.blotName]){var h=this.container.innerText;this.container.innerHTML="";return(new sa.default).insert(h,ba({},Aa.default.blotName,e[Aa.default.blotName]))}e=
this.prepareMatching();e=za(e,2);e=ca(this.container,e[0],e[1]);f(e,"\n")&&null==e.ops[e.ops.length-1].attributes&&(e=e.compose((new sa.default).retain(e.length()-1).delete(1)));Ba.log("convert",this.container.innerHTML,e);this.container.innerHTML="";return e}},{key:"dangerouslyPasteHTML",value:function(e,f){var h=2<arguments.length&&void 0!==arguments[2]?arguments[2]:ma.default.sources.API;if("string"===typeof e)this.quill.setContents(this.convert(e),f),this.quill.setSelection(0,ma.default.sources.SILENT);
else{var n=this.convert(f);this.quill.updateContents((new sa.default).retain(e).concat(n),h);this.quill.setSelection(e+n.length(),ma.default.sources.SILENT)}}},{key:"onPaste",value:function(e){var f=this;if(!e.defaultPrevented&&this.quill.isEnabled()){var h=this.quill.getSelection(),n=(new sa.default).retain(h.index),r=this.quill.scrollingContainer.scrollTop;this.container.focus();this.quill.selection.update(ma.default.sources.SILENT);setTimeout(function(){n=n.concat(f.convert()).delete(h.length);
f.quill.updateContents(n,ma.default.sources.USER);f.quill.setSelection(n.length()-h.length,ma.default.sources.SILENT);f.quill.scrollingContainer.scrollTop=r;f.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var e=this,f=[],h=[];this.matchers.forEach(function(n){n=za(n,2);var r=n[0],w=n[1];switch(r){case Node.TEXT_NODE:h.push(w);break;case Node.ELEMENT_NODE:f.push(w);break;default:[].forEach.call(e.container.querySelectorAll(r),function(e){e["__ql-matcher"]=e["__ql-matcher"]||[];e["__ql-matcher"].push(w)})}});
return[f,h]}}]);return h}(va.default);ea.DEFAULTS={matchers:[],matchVisual:!0};y.default=ea;y.matchAttributor=fa;y.matchBlot=ha;y.matchNewline=ia;y.matchSpacing=xa;y.matchText=la},function(e,y,ea){function x(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=
r)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(r,h,f){h&&e(r.prototype,h);f&&e(r,f);return r}}(),w=function n(e,h,f){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,h);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return n(e,h,f)}else{if("value"in r)return r.value;
h=r.get;return void 0===h?void 0:h.call(f)}};e=function(e){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var e=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(h,e);ba(h,[{key:"optimize",value:function(e){w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,
e);this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return w(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]);return h}(function(e){return e&&e.__esModule?e:{default:e}}(ea(6)).default);e.blotName="bold";e.tagName=["STRONG","B"];y.default=e},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f,h){f in e?Object.defineProperty(e,f,{value:h,
enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,
f):e.__proto__=f)}function r(e,f,h){var n=document.createElement("button");n.setAttribute("type","button");n.classList.add("ql-"+f);null!=h&&(n.value=h);e.appendChild(n)}function h(e,h){Array.isArray(h[0])||(h=[h]);h.forEach(function(h){var n=document.createElement("span");n.classList.add("ql-formats");h.forEach(function(e){if("string"===typeof e)r(n,e);else{var h=Object.keys(e)[0];e=e[h];Array.isArray(e)?f(n,h,e):r(n,h,e)}});e.appendChild(n)})}function f(e,f,h){var n=document.createElement("select");
n.classList.add("ql-"+f);h.forEach(function(e){var f=document.createElement("option");!1!==e?f.setAttribute("value",e):f.setAttribute("selected","selected");n.appendChild(f)});e.appendChild(n)}Object.defineProperty(y,"__esModule",{value:!0});y.addControls=y.default=void 0;var n=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),z;!(n=(z=x.next()).done)&&(h.push(z.value),!f||h.length!==f);n=!0);
}catch(ma){r=!0,w=ma}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),ca=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();e=ea(2);var aa=x(e);e=ea(0);var fa=x(e);e=ea(5);var ha=x(e);e=ea(10);e=x(e);
ea=ea(9);ea=x(ea);var ia=(0,e.default)("quill:toolbar");ea=function(e){function f(e,r){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var x=w(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,r));Array.isArray(x.options.container)?(r=document.createElement("div"),h(r,x.options.container),e.container.parentNode.insertBefore(r,e.container),x.container=r):x.container="string"===typeof x.options.container?document.querySelector(x.options.container):x.options.container;
if(!(x.container instanceof HTMLElement)){var z;return z=ia.error("Container required for toolbar",x.options),w(x,z)}x.container.classList.add("ql-toolbar");x.controls=[];x.handlers={};Object.keys(x.options.handlers).forEach(function(e){x.addHandler(e,x.options.handlers[e])});[].forEach.call(x.container.querySelectorAll("button, select"),function(e){x.attach(e)});x.quill.on(ha.default.events.EDITOR_CHANGE,function(e,f){e===ha.default.events.SELECTION_CHANGE&&x.update(f)});x.quill.on(ha.default.events.SCROLL_OPTIMIZE,
function(){var e=x.quill.selection.getRange();e=n(e,1)[0];x.update(e)});return x}z(f,e);ca(f,[{key:"addHandler",value:function(e,f){this.handlers[e]=f}},{key:"attach",value:function(e){var f=this,h=[].find.call(e.classList,function(e){return 0===e.indexOf("ql-")});if(h){h=h.slice(3);"BUTTON"===e.tagName&&e.setAttribute("type","button");if(null==this.handlers[h]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[h]){ia.warn("ignoring attaching to disabled format",h,e);return}if(null==
fa.default.query(h)){ia.warn("ignoring attaching to nonexistent format",h,e);return}}e.addEventListener("SELECT"===e.tagName?"change":"click",function(r){if("SELECT"===e.tagName){if(0>e.selectedIndex)return;var w=e.options[e.selectedIndex];w=w.hasAttribute("selected")?!1:w.value||!1}else w=e.classList.contains("ql-active")?!1:e.value||!e.hasAttribute("value"),r.preventDefault();f.quill.focus();r=f.quill.selection.getRange();r=n(r,1)[0];if(null!=f.handlers[h])f.handlers[h].call(f,w);else if(fa.default.query(h).prototype instanceof
fa.default.Embed){w=prompt("Enter "+h);if(!w)return;f.quill.updateContents((new aa.default).retain(r.index).delete(r.length).insert(ba({},h,w)),ha.default.sources.USER)}else f.quill.format(h,w,ha.default.sources.USER);f.update(r)});this.controls.push([h,e])}}},{key:"update",value:function(e){var f=null==e?{}:this.quill.getFormat(e);this.controls.forEach(function(h){h=n(h,2);var r=h[0];h=h[1];if("SELECT"===h.tagName){var w=void 0;null==e?w=null:null==f[r]?w=h.querySelector("option[selected]"):Array.isArray(f[r])||
(r=f[r],"string"===typeof r&&(r=r.replace(/"/g,'\\"')),w=h.querySelector('option[value="'+r+'"]'));null==w?(h.value="",h.selectedIndex=-1):w.selected=!0}else null==e?h.classList.remove("ql-active"):h.hasAttribute("value")?(r=f[r]===h.getAttribute("value")||null!=f[r]&&f[r].toString()===h.getAttribute("value")||null==f[r]&&!h.getAttribute("value"),h.classList.toggle("ql-active",r)):h.classList.toggle("ql-active",null!=f[r])})}}]);return f}(ea.default);ea.DEFAULTS={};ea.DEFAULTS={container:null,handlers:{clean:function(){var e=
this,f=this.quill.getSelection();null!=f&&(0==f.length?(f=this.quill.getFormat(),Object.keys(f).forEach(function(f){null!=fa.default.query(f,fa.default.Scope.INLINE)&&e.quill.format(f,!1)})):this.quill.removeFormat(f,ha.default.sources.USER))},direction:function(e){var f=this.quill.getFormat().align;"rtl"===e&&null==f?this.quill.format("align","right",ha.default.sources.USER):e||"right"!==f||this.quill.format("align",!1,ha.default.sources.USER);this.quill.format("direction",e,ha.default.sources.USER)},
indent:function(e){var f=this.quill.getFormat(this.quill.getSelection()),h=parseInt(f.indent||0);if("+1"===e||"-1"===e)e="+1"===e?1:-1,"rtl"===f.direction&&(e*=-1),this.quill.format("indent",h+e,ha.default.sources.USER)},link:function(e){!0===e&&(e=prompt("Enter link URL:"));this.quill.format("link",e,ha.default.sources.USER)},list:function(e){var f=this.quill.getFormat(this.quill.getSelection());"check"===e?"checked"===f.list||"unchecked"===f.list?this.quill.format("list",!1,ha.default.sources.USER):
this.quill.format("list","unchecked",ha.default.sources.USER):this.quill.format("list",e,ha.default.sources.USER)}}};y.default=ea;y.addControls=h},function(e){e.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(e,y,ea){function x(e,h){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
return!h||"object"!==typeof h&&"function"!==typeof h?e:h}function ba(e,h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});var w=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=
r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),z=function ca(e,f,n){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};e=function(e){function f(e,h){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");
e=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e));e.label.innerHTML=h;e.container.classList.add("ql-color-picker");[].slice.call(e.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(e){e.classList.add("ql-primary")});return e}ba(f,e);w(f,[{key:"buildItem",value:function(e){var h=z(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"buildItem",this).call(this,e);h.style.backgroundColor=e.getAttribute("value")||"";return h}},{key:"selectItem",value:function(e,h){z(f.prototype.__proto__||
Object.getPrototypeOf(f.prototype),"selectItem",this).call(this,e,h);h=this.label.querySelector(".ql-color-label");e=e?e.getAttribute("data-value")||"":"";h&&("line"===h.tagName?h.style.stroke=e:h.style.fill=e)}}]);return f}(function(e){return e&&e.__esModule?e:{default:e}}(ea(28)).default);y.default=e},function(e,y,ea){function x(e,h){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!h||"object"!==typeof h&&"function"!==typeof h?e:h}function ba(e,
h){if("function"!==typeof h&&null!==h)throw new TypeError("Super expression must either be null or a function, not "+typeof h);e.prototype=Object.create(h&&h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});h&&(Object.setPrototypeOf?Object.setPrototypeOf(e,h):e.__proto__=h)}Object.defineProperty(y,"__esModule",{value:!0});var w=function(){function e(e,f){for(var h=0;h<f.length;h++){var r=f[h];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);
Object.defineProperty(e,r.key,r)}}return function(h,f,n){f&&e(h.prototype,f);n&&e(h,n);return h}}(),z=function ca(e,f,n){null===e&&(e=Function.prototype);var h=Object.getOwnPropertyDescriptor(e,f);if(void 0===h){if(e=Object.getPrototypeOf(e),null!==e)return ca(e,f,n)}else{if("value"in h)return h.value;f=h.get;return void 0===f?void 0:f.call(n)}};e=function(e){function f(e,h){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");e=x(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,
e));e.container.classList.add("ql-icon-picker");[].forEach.call(e.container.querySelectorAll(".ql-picker-item"),function(e){e.innerHTML=h[e.getAttribute("data-value")||""]});e.defaultItem=e.container.querySelector(".ql-selected");e.selectItem(e.defaultItem);return e}ba(f,e);w(f,[{key:"selectItem",value:function(e,h){z(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"selectItem",this).call(this,e,h);e=e||this.defaultItem;this.label.innerHTML=e.innerHTML}}]);return f}(function(e){return e&&
e.__esModule?e:{default:e}}(ea(28)).default);y.default=e},function(e,y){Object.defineProperty(y,"__esModule",{value:!0});var x=function(){function e(e,w){for(var x=0;x<w.length;x++){var r=w[x];r.enumerable=r.enumerable||!1;r.configurable=!0;"value"in r&&(r.writable=!0);Object.defineProperty(e,r.key,r)}}return function(x,w,z){w&&e(x.prototype,w);z&&e(x,z);return x}}();e=function(){function e(x,w){var z=this;if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.quill=
x;this.boundsContainer=w||document.body;this.root=x.addContainer("ql-tooltip");this.root.innerHTML=this.constructor.TEMPLATE;this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){z.root.style.marginTop=-1*z.quill.root.scrollTop+"px"});this.hide()}x(e,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(e){var w=e.left+e.width/2-this.root.offsetWidth/2,x=e.bottom+this.quill.root.scrollTop;this.root.style.left=
w+"px";this.root.style.top=x+"px";this.root.classList.remove("ql-flip");var r=this.boundsContainer.getBoundingClientRect(),h=this.root.getBoundingClientRect(),f=0;h.right>r.right&&(f=r.right-h.right,this.root.style.left=w+f+"px");h.left<r.left&&(f=r.left-h.left,this.root.style.left=w+f+"px");h.bottom>r.bottom&&(this.root.style.top=x-(e.bottom-e.top+(h.bottom-h.top))+"px",this.root.classList.add("ql-flip"));return f}},{key:"show",value:function(){this.root.classList.remove("ql-editing");this.root.classList.remove("ql-hidden")}}]);
return e}();y.default=e},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=
Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});var r=function(){return function(e,f){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){var h=[],n=!0,r=!1,w=void 0;try{for(var x=e[Symbol.iterator](),z;!(n=(z=x.next()).done)&&(h.push(z.value),!f||h.length!==f);n=!0);}catch(ua){r=!0,w=ua}finally{try{if(!n&&x["return"])x["return"]()}finally{if(r)throw w;
}}return h}throw new TypeError("Invalid attempt to destructure non-iterable instance");}}(),h=function oa(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return oa(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}},f=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,
n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();e=ea(3);e=x(e);var n=ea(8),ca=x(n);n=ea(43);var aa=x(n),fa=ea(27),ha=x(fa),ia=ea(15);ea=ea(41);var xa=x(ea),la=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];ea=function(e){function h(e,f){ba(this,h);null!=f.modules.toolbar&&null==f.modules.toolbar.container&&(f.modules.toolbar.container=la);e=w(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,e,f));e.quill.container.classList.add("ql-snow");
return e}z(h,e);f(h,[{key:"extendToolbar",value:function(e){e.container.classList.add("ql-snow");this.buildButtons([].slice.call(e.container.querySelectorAll("button")),xa.default);this.buildPickers([].slice.call(e.container.querySelectorAll("select")),xa.default);this.tooltip=new na(this.quill,this.options.bounds);e.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(f,h){e.handlers.link.call(e,!h.format.link)})}}]);return h}(aa.default);ea.DEFAULTS=
(0,e.default)(!0,{},aa.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(e){e?(e=this.quill.getSelection(),null!=e&&0!=e.length&&(e=this.quill.getText(e),/^\S+@\S+\.\S+$/.test(e)&&0!==e.indexOf("mailto:")&&(e="mailto:"+e),this.quill.theme.tooltip.edit("link",e))):this.quill.format("link",!1)}}}}});var na=function(e){function n(e,f){ba(this,n);e=w(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,f));e.preview=e.root.querySelector("a.ql-preview");return e}z(n,e);f(n,[{key:"listen",
value:function(){var e=this;h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"listen",this).call(this);this.root.querySelector("a.ql-action").addEventListener("click",function(f){e.root.classList.contains("ql-editing")?e.save():e.edit("link",e.preview.textContent);f.preventDefault()});this.root.querySelector("a.ql-remove").addEventListener("click",function(f){if(null!=e.linkRange){var h=e.linkRange;e.restoreFocus();e.quill.formatText(h,"link",!1,ca.default.sources.USER);delete e.linkRange}f.preventDefault();
e.hide()});this.quill.on(ca.default.events.SELECTION_CHANGE,function(f,h,n){if(null!=f){if(0===f.length&&n===ca.default.sources.USER){if(h=e.quill.scroll.descendant(ha.default,f.index),n=r(h,2),h=n[0],n=n[1],null!=h){e.linkRange=new ia.Range(f.index-n,h.length());f=ha.default.formats(h.domNode);e.preview.textContent=f;e.preview.setAttribute("href",f);e.show();e.position(e.quill.getBounds(e.linkRange));return}}else delete e.linkRange;e.hide()}})}},{key:"show",value:function(){h(n.prototype.__proto__||
Object.getPrototypeOf(n.prototype),"show",this).call(this);this.root.removeAttribute("data-mode")}}]);return n}(n.BaseTooltip);na.TEMPLATE='<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-action"></a><a class="ql-remove"></a>';y.default=ea},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(y,"__esModule",{value:!0});
e=ea(29);e=x(e);var ba=ea(36),w=ea(38),z=ea(64),r=ea(65);r=x(r);var h=ea(66);h=x(h);var f=ea(67),n=x(f),ca=ea(37),aa=ea(26),fa=ea(39),ha=ea(40),ia=ea(56);ia=x(ia);var xa=ea(68);xa=x(xa);var la=ea(27);la=x(la);var na=ea(69);na=x(na);var za=ea(70);za=x(za);var ra=ea(71);ra=x(ra);var Da=ea(72);Da=x(Da);var sa=ea(73);sa=x(sa);var oa=ea(13),ma=x(oa),va=ea(74);va=x(va);var Fa=ea(75);Fa=x(Fa);var ua=ea(57);ua=x(ua);var Ca=ea(41);Ca=x(Ca);var Aa=ea(28);Aa=x(Aa);var Ea=ea(59);Ea=x(Ea);var Ga=ea(60);Ga=x(Ga);
var Ba=ea(61);Ba=x(Ba);var Ia=ea(108);Ia=x(Ia);ea=ea(62);ea=x(ea);e.default.register({"attributors/attribute/direction":w.DirectionAttribute,"attributors/class/align":ba.AlignClass,"attributors/class/background":ca.BackgroundClass,"attributors/class/color":aa.ColorClass,"attributors/class/direction":w.DirectionClass,"attributors/class/font":fa.FontClass,"attributors/class/size":ha.SizeClass,"attributors/style/align":ba.AlignStyle,"attributors/style/background":ca.BackgroundStyle,"attributors/style/color":aa.ColorStyle,
"attributors/style/direction":w.DirectionStyle,"attributors/style/font":fa.FontStyle,"attributors/style/size":ha.SizeStyle},!0);e.default.register({"formats/align":ba.AlignClass,"formats/direction":w.DirectionClass,"formats/indent":z.IndentClass,"formats/background":ca.BackgroundStyle,"formats/color":aa.ColorStyle,"formats/font":fa.FontClass,"formats/size":ha.SizeClass,"formats/blockquote":r.default,"formats/code-block":ma.default,"formats/header":h.default,"formats/list":n.default,"formats/bold":ia.default,
"formats/code":oa.Code,"formats/italic":xa.default,"formats/link":la.default,"formats/script":na.default,"formats/strike":za.default,"formats/underline":ra.default,"formats/image":Da.default,"formats/video":sa.default,"formats/list/item":f.ListItem,"modules/formula":va.default,"modules/syntax":Fa.default,"modules/toolbar":ua.default,"themes/bubble":Ia.default,"themes/snow":ea.default,"ui/icons":Ca.default,"ui/picker":Aa.default,"ui/icon-picker":Ga.default,"ui/color-picker":Ea.default,"ui/tooltip":Ba.default},
!0);y.default=e.default},function(e,y,ea){function x(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}Object.defineProperty(y,"__esModule",{value:!0});y.IndentClass=void 0;var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];
n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(r,h,f){h&&e(r.prototype,h);f&&e(r,f);return r}}(),w=function n(e,h,f){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,h);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return n(e,h,f)}else{if("value"in r)return r.value;h=r.get;return void 0===h?void 0:h.call(f)}};e=function(e){return e&&e.__esModule?e:{default:e}}(ea(0));e=new (function(e){function h(){if(!(this instanceof
h))throw new TypeError("Cannot call a class as a function");var e=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(h,e);ba(h,[{key:"add",value:function(e,n){if("+1"===n||"-1"===n){var f=this.value(e)||0;n="+1"===n?f+1:f-1}return 0===n?(this.remove(e),!0):w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"add",this).call(this,
e,n)}},{key:"canAdd",value:function(e,n){return w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"canAdd",this).call(this,e,n)||w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"canAdd",this).call(this,e,parseInt(n))}},{key:"value",value:function(e){return parseInt(w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"value",this).call(this,e))||void 0}}]);return h}(e.default.Attributor.Class))("indent","ql-indent",{scope:e.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,
8]});y.IndentClass=e},function(e,y,ea){function x(e,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);e.prototype=Object.create(w&&w.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(e,w):e.__proto__=w)}Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");
var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);return w}(function(e){return e&&e.__esModule?e:{default:e}}(ea(4)).default);e.blotName="blockquote";e.tagName="blockquote";y.default=e},function(e,y,ea){function x(e,x){if("function"!==typeof x&&null!==x)throw new TypeError("Super expression must either be null or a function, not "+
typeof x);e.prototype=Object.create(x&&x.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});x&&(Object.setPrototypeOf?Object.setPrototypeOf(e,x):e.__proto__=x)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,r){for(var h=0;h<r.length;h++){var f=r[h];f.enumerable=f.enumerable||!1;f.configurable=!0;"value"in f&&(f.writable=!0);Object.defineProperty(e,f.key,f)}}return function(w,r,h){r&&e(w.prototype,r);h&&e(w,h);return w}}();e=function(e){function w(){if(!(this instanceof
w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);ba(w,null,[{key:"formats",value:function(e){return this.tagName.indexOf(e.tagName)+1}}]);return w}(function(e){return e&&e.__esModule?e:{default:e}}(ea(4)).default);e.blotName="header";e.tagName="H1 H2 H3 H4 H5 H6".split(" ");
y.default=e},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&
f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.ListItem=void 0;var r=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),h=function xa(e,f,h){null===
e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return xa(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};e=ea(0);var f=x(e);e=ea(4);e=x(e);ea=ea(25);ea=x(ea);var n=function(e){function n(){ba(this,n);return w(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}z(n,e);r(n,[{key:"format",value:function(e,r){e!==ca.blotName||r?h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),
"format",this).call(this,e,r):this.replaceWith(f.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(e,f){this.parent.isolate(this.offset(this.parent),this.length());if(e===this.parent.statics.blotName)return this.parent.replaceWith(e,f),this;this.parent.unwrap();return h(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),
"replaceWith",this).call(this,e,f)}}],[{key:"formats",value:function(e){return e.tagName===this.tagName?void 0:h(n.__proto__||Object.getPrototypeOf(n),"formats",this).call(this,e)}}]);return n}(e.default);n.blotName="list-item";n.tagName="LI";var ca=function(e){function x(e){function h(h){if(h.target.parentNode===e){var r=n.statics.formats(e);h=f.default.find(h.target);"checked"===r?h.format("list","unchecked"):"unchecked"===r&&h.format("list","checked")}}ba(this,x);var n=w(this,(x.__proto__||Object.getPrototypeOf(x)).call(this,
e));e.addEventListener("touchstart",h);e.addEventListener("mousedown",h);return n}z(x,e);r(x,null,[{key:"create",value:function(e){var f="ordered"===e?"OL":"UL";f=h(x.__proto__||Object.getPrototypeOf(x),"create",this).call(this,f);"checked"!==e&&"unchecked"!==e||f.setAttribute("data-checked","checked"===e);return f}},{key:"formats",value:function(e){if("OL"===e.tagName)return"ordered";if("UL"===e.tagName)return e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":
"bullet"}}]);r(x,[{key:"format",value:function(e,f){0<this.children.length&&this.children.tail.format(e,f)}},{key:"formats",value:function(){var e={},f=this.statics.blotName,h=this.statics.formats(this.domNode);f in e?Object.defineProperty(e,f,{value:h,enumerable:!0,configurable:!0,writable:!0}):e[f]=h;return e}},{key:"insertBefore",value:function(e,f){e instanceof n?h(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"insertBefore",this).call(this,e,f):(f=null==f?this.length():f.offset(this),
f=this.split(f),f.parent.insertBefore(e,f))}},{key:"optimize",value:function(e){h(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"optimize",this).call(this,e);e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&e.domNode.tagName===this.domNode.tagName&&e.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(e.moveChildren(this),e.remove())}},{key:"replace",value:function(e){if(e.statics.blotName!==this.statics.blotName){var n=f.default.create(this.statics.defaultChild);
e.moveChildren(n);this.appendChild(n)}h(x.prototype.__proto__||Object.getPrototypeOf(x.prototype),"replace",this).call(this,e)}}]);return x}(ea.default);ca.blotName="list";ca.scope=f.default.Scope.BLOCK_BLOT;ca.tagName=["OL","UL"];ca.defaultChild="list-item";ca.allowedChildren=[n];y.ListItem=n;y.default=ca},function(e,y,ea){function x(e,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);e.prototype=Object.create(w&&w.prototype,
{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(e,w):e.__proto__=w)}Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?
this:e}x(w,e);return w}(function(e){return e&&e.__esModule?e:{default:e}}(ea(56)).default);e.blotName="italic";e.tagName=["EM","I"];y.default=e},function(e,y,ea){function x(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}Object.defineProperty(y,
"__esModule",{value:!0});var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(r,h,f){h&&e(r.prototype,h);f&&e(r,f);return r}}(),w=function n(e,h,f){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,h);if(void 0===r){if(e=Object.getPrototypeOf(e),null!==e)return n(e,h,f)}else{if("value"in r)return r.value;h=r.get;return void 0===h?
void 0:h.call(f)}};e=function(e){function h(){if(!(this instanceof h))throw new TypeError("Cannot call a class as a function");var e=(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(h,e);ba(h,null,[{key:"create",value:function(e){return"super"===e?document.createElement("sup"):"sub"===e?document.createElement("sub"):w(h.__proto__||
Object.getPrototypeOf(h),"create",this).call(this,e)}},{key:"formats",value:function(e){if("SUB"===e.tagName)return"sub";if("SUP"===e.tagName)return"super"}}]);return h}(function(e){return e&&e.__esModule?e:{default:e}}(ea(6)).default);e.blotName="script";e.tagName=["SUB","SUP"];y.default=e},function(e,y,ea){function x(e,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);e.prototype=Object.create(w&&w.prototype,{constructor:{value:e,
enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(e,w):e.__proto__=w)}Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);return w}(function(e){return e&&
e.__esModule?e:{default:e}}(ea(6)).default);e.blotName="strike";e.tagName="S";y.default=e},function(e,y,ea){function x(e,w){if("function"!==typeof w&&null!==w)throw new TypeError("Super expression must either be null or a function, not "+typeof w);e.prototype=Object.create(w&&w.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});w&&(Object.setPrototypeOf?Object.setPrototypeOf(e,w):e.__proto__=w)}Object.defineProperty(y,"__esModule",{value:!0});e=function(e){function w(){if(!(this instanceof
w))throw new TypeError("Cannot call a class as a function");var e=(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(w,e);return w}(function(e){return e&&e.__esModule?e:{default:e}}(ea(6)).default);e.blotName="underline";e.tagName="U";y.default=e},function(e,y,ea){function x(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+
typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,r){h&&e(f.prototype,h);r&&e(f,r);return f}}(),w=function aa(e,
n,r){null===e&&(e=Function.prototype);var f=Object.getOwnPropertyDescriptor(e,n);if(void 0===f){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,n,r)}else{if("value"in f)return f.value;n=f.get;return void 0===n?void 0:n.call(r)}};e=function(e){return e&&e.__esModule?e:{default:e}}(ea(0));var z=ea(27),r=["alt","height","width"];ea=function(e){function f(){if(!(this instanceof f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);
if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(f,e);ba(f,[{key:"format",value:function(e,n){-1<r.indexOf(e)?n?this.domNode.setAttribute(e,n):this.domNode.removeAttribute(e):w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"format",this).call(this,e,n)}}],[{key:"create",value:function(e){var n=w(f.__proto__||Object.getPrototypeOf(f),"create",this).call(this,e);"string"===typeof e&&
n.setAttribute("src",this.sanitize(e));return n}},{key:"formats",value:function(e){return r.reduce(function(f,n){e.hasAttribute(n)&&(f[n]=e.getAttribute(n));return f},{})}},{key:"match",value:function(e){return/\.(jpe?g|gif|png)$/.test(e)||/^data:image\/.+;base64/.test(e)}},{key:"sanitize",value:function(e){return(0,z.sanitize)(e,["http","https","data"])?e:"//:0"}},{key:"value",value:function(e){return e.getAttribute("src")}}]);return f}(e.default.Embed);ea.blotName="image";ea.tagName="IMG";y.default=
ea},function(e,y,ea){function x(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});var ba=function(){function e(e,h){for(var f=0;f<h.length;f++){var n=h[f];n.enumerable=n.enumerable||!1;n.configurable=
!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,r){h&&e(f.prototype,h);r&&e(f,r);return f}}(),w=function aa(e,n,r){null===e&&(e=Function.prototype);var f=Object.getOwnPropertyDescriptor(e,n);if(void 0===f){if(e=Object.getPrototypeOf(e),null!==e)return aa(e,n,r)}else{if("value"in f)return f.value;n=f.get;return void 0===n?void 0:n.call(r)}};e=ea(4);var z=function(e){return e&&e.__esModule?e:{default:e}}(ea(27)),r=["height","width"];ea=function(e){function f(){if(!(this instanceof
f))throw new TypeError("Cannot call a class as a function");var e=(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments);if(!this)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?this:e}x(f,e);ba(f,[{key:"format",value:function(e,n){-1<r.indexOf(e)?n?this.domNode.setAttribute(e,n):this.domNode.removeAttribute(e):w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"format",this).call(this,e,n)}}],
[{key:"create",value:function(e){var n=w(f.__proto__||Object.getPrototypeOf(f),"create",this).call(this,e);n.setAttribute("frameborder","0");n.setAttribute("allowfullscreen",!0);n.setAttribute("src",this.sanitize(e));return n}},{key:"formats",value:function(e){return r.reduce(function(f,n){e.hasAttribute(n)&&(f[n]=e.getAttribute(n));return f},{})}},{key:"sanitize",value:function(e){return z.default.sanitize(e)}},{key:"value",value:function(e){return e.getAttribute("src")}}]);return f}(e.BlockEmbed);
ea.blotName="video";ea.className="ql-video";ea.tagName="IFRAME";y.default=ea},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+
typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.FormulaBlot=void 0;var r=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);
return f}}(),h=function xa(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return xa(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};e=ea(35);e=x(e);var f=ea(5),n=x(f);ea=ea(9);ea=x(ea);var ca=function(e){function f(){ba(this,f);return w(this,(f.__proto__||Object.getPrototypeOf(f)).apply(this,arguments))}z(f,e);r(f,null,[{key:"create",value:function(e){var n=h(f.__proto__||
Object.getPrototypeOf(f),"create",this).call(this,e);"string"===typeof e&&(window.katex.render(e,n,{throwOnError:!1,errorColor:"#f00"}),n.setAttribute("data-value",e));return n}},{key:"value",value:function(e){return e.getAttribute("data-value")}}]);return f}(e.default);ca.blotName="formula";ca.className="ql-formula";ca.tagName="SPAN";ea=function(e){function f(){ba(this,f);var e=w(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));if(null==window.katex)throw Error("Formula module requires KaTeX.");
return e}z(f,e);r(f,null,[{key:"register",value:function(){n.default.register(ca,!0)}}]);return f}(ea.default);y.FormulaBlot=ca;y.default=ea},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==typeof f?e:f}function z(e,f){if("function"!==
typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.CodeToken=y.CodeBlock=void 0;var r=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in
n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}(),h=function la(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return la(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}};e=ea(0);e=x(e);var f=ea(5),n=x(f);f=ea(9);f=x(f);ea=ea(13);var ca=function(e){function f(){ba(this,f);return w(this,(f.__proto__||Object.getPrototypeOf(f)).apply(this,
arguments))}z(f,e);r(f,[{key:"replaceWith",value:function(e){this.domNode.textContent=this.domNode.textContent;this.attach();h(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"replaceWith",this).call(this,e)}},{key:"highlight",value:function(e){var f=this.domNode.textContent;if(this.cachedText!==f){if(0<f.trim().length||null==this.cachedText)this.domNode.innerHTML=e(f),this.domNode.normalize(),this.attach();this.cachedText=f}}}]);return f}(x(ea).default);ca.className="ql-syntax";var aa=
new e.default.Attributor.Class("token","hljs",{scope:e.default.Scope.INLINE});ea=function(e){function f(e,h){ba(this,f);var r=w(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));if("function"!==typeof r.options.highlight)throw Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var x=null;r.quill.on(n.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(x);x=setTimeout(function(){r.highlight();x=null},r.options.interval)});r.highlight();
return r}z(f,e);r(f,null,[{key:"register",value:function(){n.default.register(aa,!0);n.default.register(ca,!0)}}]);r(f,[{key:"highlight",value:function(){var e=this;if(!this.quill.selection.composing){this.quill.update(n.default.sources.USER);var f=this.quill.getSelection();this.quill.scroll.descendants(ca).forEach(function(f){f.highlight(e.options.highlight)});this.quill.update(n.default.sources.SILENT);null!=f&&this.quill.setSelection(f,n.default.sources.SILENT)}}}]);return f}(f.default);ea.DEFAULTS=
{highlight:function(){return null==window.hljs?null:function(e){return window.hljs.highlightAuto(e).value}}(),interval:1E3};y.CodeBlock=ca;y.CodeToken=aa;y.default=ea},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},
function(e){e.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(e){e.exports=
'<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},
function(e){e.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},
function(e){e.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(e){e.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(e){e.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},
function(e){e.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(e,y,ea){function x(e){return e&&e.__esModule?e:{default:e}}function ba(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function");}function w(e,f){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!f||"object"!==typeof f&&"function"!==
typeof f?e:f}function z(e,f){if("function"!==typeof f&&null!==f)throw new TypeError("Super expression must either be null or a function, not "+typeof f);e.prototype=Object.create(f&&f.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}});f&&(Object.setPrototypeOf?Object.setPrototypeOf(e,f):e.__proto__=f)}Object.defineProperty(y,"__esModule",{value:!0});y.default=y.BubbleTooltip=void 0;var r=function ra(e,f,h){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,
f);if(void 0===n){if(e=Object.getPrototypeOf(e),null!==e)return ra(e,f,h)}else{if("value"in n)return n.value;f=n.get;return void 0===f?void 0:f.call(h)}},h=function(){function e(e,f){for(var h=0;h<f.length;h++){var n=f[h];n.enumerable=n.enumerable||!1;n.configurable=!0;"value"in n&&(n.writable=!0);Object.defineProperty(e,n.key,n)}}return function(f,h,n){h&&e(f.prototype,h);n&&e(f,n);return f}}();e=ea(3);e=x(e);var f=ea(8),n=x(f);f=ea(43);var ca=x(f),aa=ea(15);ea=ea(41);var fa=x(ea),ha=[["bold","italic",
"link"],[{header:1},{header:2},"blockquote"]];ea=function(e){function f(e,h){ba(this,f);null!=h.modules.toolbar&&null==h.modules.toolbar.container&&(h.modules.toolbar.container=ha);e=w(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));e.quill.container.classList.add("ql-bubble");return e}z(f,e);h(f,[{key:"extendToolbar",value:function(e){this.tooltip=new ia(this.quill,this.options.bounds);this.tooltip.root.appendChild(e.container);this.buildButtons([].slice.call(e.container.querySelectorAll("button")),
fa.default);this.buildPickers([].slice.call(e.container.querySelectorAll("select")),fa.default)}}]);return f}(ca.default);ea.DEFAULTS=(0,e.default)(!0,{},ca.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(e){e?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var ia=function(e){function f(e,h){ba(this,f);var r=w(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,h));r.quill.on(n.default.events.EDITOR_CHANGE,function(e,f,h,w){e===n.default.events.SELECTION_CHANGE&&
(null!=f&&0<f.length&&w===n.default.sources.USER?(r.show(),r.root.style.left="0px",r.root.style.width="",r.root.style.width=r.root.offsetWidth+"px",e=r.quill.getLines(f.index,f.length),1===e.length?r.position(r.quill.getBounds(f)):(h=e[e.length-1],e=r.quill.getIndex(h),f=Math.min(h.length()-1,f.index+f.length-e),f=r.quill.getBounds(new aa.Range(e,f)),r.position(f))):document.activeElement!==r.textbox&&r.quill.hasFocus()&&r.hide())});return r}z(f,e);h(f,[{key:"listen",value:function(){var e=this;r(f.prototype.__proto__||
Object.getPrototypeOf(f.prototype),"listen",this).call(this);this.root.querySelector(".ql-close").addEventListener("click",function(){e.root.classList.remove("ql-editing")});this.quill.on(n.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!e.root.classList.contains("ql-hidden")){var f=e.quill.getSelection();null!=f&&e.position(e.quill.getBounds(f))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(e){e=r(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),
"position",this).call(this,e);var h=this.root.querySelector(".ql-tooltip-arrow");h.style.marginLeft="";if(0===e)return e;h.style.marginLeft=-1*e-h.offsetWidth/2+"px"}}]);return f}(f.BaseTooltip);ia.TEMPLATE='<span class="ql-tooltip-arrow"></span><div class="ql-tooltip-editor"><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-close"></a></div>';y.BubbleTooltip=ia;y.default=ea},function(e,y,ea){e.exports=ea(63)}])["default"]})}).call(this,e(406).Buffer)}}]);}).call(this || window)
