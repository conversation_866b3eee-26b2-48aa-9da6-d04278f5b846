﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;

public partial class Treaty_webpage_TreatyCase2_CourtAdd : System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            if (Request.QueryString["seno"] != null)
            {
                if (!IsNumber(Request.QueryString["seno"]) || (Request.QueryString["seno"].Length == 0) || (Request.QueryString["seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request.QueryString["seno"].ToString();
                SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
                ssoUser.GetEmpInfo();
                //SDS_auth.SelectParameters.Clear();
                //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_auth.SelectCommand = "esp_TreatyCase2_Auth";
                //SDS_auth.SelectParameters.Add("seno", ViewState["seno"].ToString());
                //SDS_auth.SelectParameters.Add("empno", ssoUser.empNo);
                //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                //{
                //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_auth.DataBind();
                //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyCase2_Auth";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv_auth = dt.DefaultView;
                if (dv_auth.Count >= 1)
                {
                    string str_auth = dv_auth[0][0].ToString();
                    if ((str_auth == "X") || (str_auth == "R"))
                        Response.Redirect("../NoAuthRight.aspx");
                }
            }
            else
                Response.Redirect("../NoAuthRight.aspx");
        }
        Bindcourt();
    }

    private void Bindcourt()
    {
        //SDS_court.SelectCommand = "exec esp_treaty_codetable_query_by_group  '' ,'22' ";
        //SDS_court.DataBind();
        //DDL_court.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            sqlCmd.Parameters.AddWithValue("@code_group", "");
            sqlCmd.Parameters.AddWithValue("@code_type", "22");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_court.DataSource = dt;
                DDL_court.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

    }

    protected void BT_Save_Click(object sender, EventArgs e)
    {
        if (TB_year.Text == "")
        {
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('年度 必須填寫 !');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }
        else
        {
            if (TB_year.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_word.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_no.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_stock.Text.ToUpper().IndexOf("SCRIPT") >= 0)
                Response.Redirect("../danger.aspx");
            if (TB_year.Text.Length > 3)
                Response.Redirect("../danger.aspx");
            if (TB_word.Text.Length > 4)
                Response.Redirect("../danger.aspx");
            if (TB_no.Text.Length > 5)
                Response.Redirect("../danger.aspx");
            if (TB_stock.Text.Length > 2)
                Response.Redirect("../danger.aspx");

            //SDS_SC.SelectParameters.Clear();
            //this.SDS_SC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_SC.InsertCommand = "insert treaty_case2_court(tcp_seno,tcp_court,tcp_year,tcp_word,tcp_no,tcp_stock ) values(@tcp_seno,@tcp_court,@tcp_year,@tcp_word,@tcp_no,@tcp_stock )";
            //this.SDS_SC.InsertParameters.Add("tcp_seno", ViewState["seno"].ToString());
            //this.SDS_SC.InsertParameters.Add("tcp_court", DDL_court.SelectedValue);
            //this.SDS_SC.InsertParameters.Add("tcp_year", TB_year.Text);
            //this.SDS_SC.InsertParameters.Add("tcp_word", TB_word.Text);
            //this.SDS_SC.InsertParameters.Add("tcp_no", TB_no.Text);
            //this.SDS_SC.InsertParameters.Add("tcp_stock", TB_stock.Text);
            //this.SDS_SC.Insert();

            #region --- insert ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"insert treaty_case2_court(tcp_seno,tcp_court,tcp_year,tcp_word,tcp_no,tcp_stock ) values(@tcp_seno,@tcp_court,@tcp_year,@tcp_word,@tcp_no,@tcp_stock )";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tcp_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tcp_court", oRCM.SQLInjectionReplaceAll(DDL_court.SelectedValue));
                sqlCmd.Parameters.AddWithValue("@tcp_year", oRCM.SQLInjectionReplaceAll(TB_year.Text));
                sqlCmd.Parameters.AddWithValue("@tcp_word", oRCM.SQLInjectionReplaceAll(TB_word.Text));
                sqlCmd.Parameters.AddWithValue("@tcp_no", oRCM.SQLInjectionReplaceAll(TB_no.Text));
                sqlCmd.Parameters.AddWithValue("@tcp_stock", oRCM.SQLInjectionReplaceAll(TB_stock.Text));


                try
                {
                    sqlConn.Open();

                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion

            StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
        }

    }
}