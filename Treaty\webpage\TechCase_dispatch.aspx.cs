﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_dispatch : Treaty.common
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();

    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;

        if (!IsPostBack)
        {
            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }

            if (Request.QueryString["Role2"] != null)
            {
                ViewState["Role2"] = Request.QueryString["Role2"].ToString();
                Bind_Handle();
            }
        }
    }

    protected void btn_Save_Click(object sender, EventArgs e)
    {
        DoSave();

        StringBuilder script = new StringBuilder("<script type='text/javascript'> close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }

    private void Bind_Handle()
    {
        DataTable dt = new DataTable();
        #region --- query --- 
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            if (ViewState["Role2"].ToString() == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "handle_X_init");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "handle_x_init");

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
                ddl_Handle.DataSource = dt;
                ddl_Handle.DataBind();
                ddl_Handle.Items.Insert(0, new ListItem(" ", ""));
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void DoSave()
    {
        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            if (ViewState["Role2"].ToString() == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "assign_X_handle");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "assign_x_handle");

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@handle_empno", oRCM.SQLInjectionReplaceAll(ddl_Handle.SelectedValue));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
}