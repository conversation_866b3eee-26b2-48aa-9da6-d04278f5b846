﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;

public partial class TreatyApply_modify : Treaty.common   //System.Web.UI.Page
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {
            case "N":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視NDA資訊";
                break;
            //case "U":
            //    strResult = "<img src='../images/icon-1301.gif' />檢視國外契約資訊";
            //    break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif'  border='0'/>檢視標案資訊";
                break;
            case "F":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
                break;
            case "C":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
                break;
            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion
    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            case "U":
                bResult = true;
                break;
            //case "R":
            //    bResult = true;
            //    break;
            case "C":
                bResult = true;
                break;
            case "F":
                bResult = true;
                break;
            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion
    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    protected void btnEngage_Click(object sender, EventArgs e)
    {
        string strCaseNo = oRCM.RemoveXss(txtComplexNo.Text.Trim());
        string strCaseNo_C = oRCM.RemoveXss(txtOldContno.Text.Trim());
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
        string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();

        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案
                strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                //strWinOpen = string.Format("{0}/nda/NDA_readonly.aspx?kind=NDA&seqsn=&contno={1}&style=2&readstyle=2", strNDA_Path, strCaseNo.Substring(0, 12));
                strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
                break;
            case "A": // 國外契約
                //strWinOpen = string.Format(@"window.open('{0}');	return false;", strWinOpen);
                strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                break;
            case "F": // 國內契約  
                strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
                break;
            case "C": // 工服
                strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                break;

        }
        script = @" <script> window.open('" + Server.HtmlEncode(strWinOpen) + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
    }
    #endregion
    private string 匯率
    {
        get
        {
            string str_字串 = TB_money_rate.Text.Trim().Replace(".", "");
            if (str_字串 == "") return "";

            if (Regex.IsMatch(str_字串, "^[0-9]*$") == false)
                Response.Redirect("../danger.aspx");
            return TB_money_rate.Text.Trim();
        }
    }
    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }
    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;
        BT_Customer.Attributes.Add("onclick", "find_customer2();");
        //txt_px_name.Attributes.Add("onChange", "Find_empno_kw('txt_px_name',2);");
        txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
        btnFilesUpload2.Attributes.Add("onclick", "treaty_fileup();");
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));
        if (!IsPostBack)
        {

            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            if (Request["seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"].ToString(), out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
                ViewState["IsEdit"] = true;
            }
            if (ViewState["seno"] == null)
                Response.Redirect("../danger.aspx");

            BindContMoneyType();
            BindData();
            BindData_file();
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            x_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            txtTel.Attributes.Add("readOnly", "readonly");
            btnDelete2.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            if (ViewState["tr_class"].ToString() != "T") //其他議約需求
            {
                rb_conttype_other.Enabled = false;
                rb_conttype_bd.Enabled = false;
            }
            cb_conttype_b0.Enabled = false;
            cb_conttype_b1.Enabled = false;
            cb_conttype_d4.Enabled = false;
            cb_conttype_d5.Enabled = false;
            cb_conttype_d7.Enabled = false;
            cb_conttype_rb.Enabled = false;
            cb_conttype_m.Enabled = false;
            cb_conttype_ns.Enabled = false;
            rb_conttype_uo.Enabled = false;
            rb_conttype_ui.Enabled = false;
            cb_conttype_c.Enabled = false;
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            HttpContext.Current.Response.Cache.SetNoServerCaching();
            HttpContext.Current.Response.Cache.SetNoStore();
            if (ViewState["tr_class"].ToString() == "T")
            {
                BindData_業務窗口();
            }

        }
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (Request.Params.Get("__EVENTTARGET") == "ADM_other")
        {
            BindData_業務窗口();
        }
        if (Request.Params.Get("__EVENTTARGET") == "seno_ECP")
        {
            if (h_ECP_success.Value == "Y")
            {
                string script = "<script language='javascript'>alert('申請單送出簽核成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
    }
    private void BindContMoneyType()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT code_subtype, subtype_desc FROM treaty_code_table  WHERE code_type = '20' and enable = '1' order by display_order  ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContMoneyType.DataSource = dt;
                ddlContMoneyType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    private void BindData_業務窗口()
    {

        //SDS_ADM_業務.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_ADM_業務.SelectParameters.Clear();
        //SDS_ADM_業務.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_ADM_業務.SelectCommand = "select rtrim(com_cname) cname ,com_empno from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =( select com_orgcd from common..comper where com_empno=@業務窗口))";
        //SDS_ADM_業務.SelectParameters.Add("業務窗口", SQLInjectionReplaceAll(txt_promoter_empno.Value));
        //System.Data.DataView dv_業務 = (DataView)SDS_ADM_業務.Select(new DataSourceSelectArguments());


        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select rtrim(com_cname) cname ,com_empno from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org =( select com_orgcd from common..comper where com_empno=@業務窗口))";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@業務窗口", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_業務 = dt.DefaultView;
        if ((dv_業務.Count >= 1) && (ViewState["tr_class"].ToString() == "T"))
        {
            PL_amd.Visible = true;

            LB_adm_text.Text = Server.HtmlEncode(dv_業務[0]["cname"].ToString());
            LB_adm_業務_empno.Text = Server.HtmlEncode(dv_業務[0]["com_empno"].ToString());
        }
        else
        {
            PL_amd.Visible = false;
            rb_adm_no.Checked = true;
        }
    }

    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = SQLInjectionReplaceAll("esp_treaty_MultiCustomer_List_by_NOs");
        //SDS_company.SelectParameters.Add("customers", SQLInjectionReplaceAll(this.h_compno.Value.ToString()));
        //SDS_company.DataBind();
        //SGV_company.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_MultiCustomer_List_by_NOs";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(h_compno.Value.ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    public void BindData()
    {
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommand = " select * ,(select com_telext  from common..comper where com_empno=tr_keyin_emp_no) keyinTel ,(select com_telext  from common..comper where com_empno=tr_keyin_emp_no) modefyTel from treaty_requisition where  tr_seno = @sn ";
        //SDS_NR.SelectParameters.Add("sn", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.DataBind();
        //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * ,(select com_telext  from common..comper where com_empno=tr_keyin_emp_no) keyinTel ,(select com_telext  from common..comper where com_empno=tr_keyin_emp_no) modefyTel from treaty_requisition where  tr_seno = @sn ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);


            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            string str_tr_year = dv[0]["tr_year"].ToString().Trim();
            string str_tr_orgcd = dv[0]["tr_orgcd"].ToString().Trim();
            string str_tr_class = dv[0]["tr_class"].ToString().Trim();
            ViewState["tr_class"] = str_tr_class;
            string str_tr_sn = dv[0]["tr_sn"].ToString().Trim();
            string str_tr_ver = dv[0]["tr_ver"].ToString().Trim();
            string str_tr_seqsn = dv[0]["tr_seqsn"].ToString().Trim();
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;
            GetEngageNDAText(str_actcontno);
            btnEngage.Visible = GetEngageNDAVisible(str_actcontno);
            if (str_tr_class == "C")
            {
                txtOldContno.Text = Server.HtmlEncode(dv[0]["tr_old_contno"].ToString().Trim());
                txtOldContno.Attributes.Add("class", "TB_ReadOnly");
            }
            switch (dv[0]["tr_language"].ToString().Trim())
            {
                // case "0": rb_language_other.Checked = true; break;
                case "1": rb_language_chiness.Checked = true; break;
                case "2": rb_language_english.Checked = true; break;
            }

            #region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + oRCM.SQLInjectionReplaceAll(dv[0]["tr_promoter_no"].ToString().Trim()) + "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(dv[0]["tr_promoter_no"].ToString().Trim()));
                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt = new DataTable();
                    sqlDA.Fill(dt);


                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv_emp = dt.DefaultView;
            if (dv_emp.Count >= 1)
            {
                txt_req_dept.Value = Server.HtmlEncode(dv_emp[0]["com_deptid"].ToString().Trim());
                x_dept.Text = Server.HtmlEncode(dv_emp[0]["com_deptid"].ToString().Trim().Substring(2, 5));
                txt_promoter_name.Text = Server.HtmlEncode(dv_emp[0]["com_cname"].ToString().Trim());
                txt_promoter_empno.Value = Server.HtmlEncode(dv_emp[0]["com_empno"].ToString().Trim());
                txtTel.Text = Server.HtmlEncode(dv_emp[0]["com_telext"].ToString().Trim());
                txtOrgAbbrName.Text = Server.HtmlEncode(dv_emp[0]["orgName"].ToString().Trim());
                ViewState["com_orgcd"] = Server.HtmlEncode(dv_emp[0]["com_orgcd"].ToString().Trim());
            }
            txt_name.Text = oRCM.HtmlRead(dv[0]["tr_name"].ToString().Trim());//洽案（契約名稱）
            #endregion
            #region 客戶
            h_compno.Value = dv[0]["tr_compidno_all"].ToString().Trim().Replace(" ", "").Replace("㊣", ",");//簽約對象(多)
            BindData_Customer();
            #endregion
            #region 案件性質
            switch (dv[0]["tr_class"].ToString().Trim())
            {
                case "A":
                    #region A
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_uo.Enabled = true;
                    rb_conttype_uo.Checked = true;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;
                case "C":
                    #region C
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    cb_conttype_c.Checked = true;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;

                case "M":
                    #region M
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    cb_conttype_m.Checked = true;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    if (dv[0]["tr_case_flag"].ToString().Trim() == "1")
                        lb_standar_flag.Visible = false;
                    #endregion
                    break;
                case "N":
                    #region N
                    if (dv[0]["tr_conttype_b0"].ToString().Trim() == "1")
                        cb_conttype_b0.Checked = true;

                    if (dv[0]["tr_conttype_b1"].ToString().Trim() == "1")
                        cb_conttype_b1.Checked = true;
                    else
                        cb_conttype_b1.Checked = false;
                    if (dv[0]["tr_conttype_d4"].ToString().Trim() == "1")
                        cb_conttype_d4.Checked = true;

                    if (dv[0]["tr_conttype_d5"].ToString().Trim() == "1")
                        cb_conttype_d5.Checked = true;
                    else
                        cb_conttype_d5.Checked = false;
                    if (dv[0]["tr_conttype_d7"].ToString().Trim() == "1")
                        cb_conttype_d7.Checked = true;

                    if (dv[0]["tr_conttype_ns"].ToString().Trim() == "1")
                        cb_conttype_ns.Checked = true;

                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    if (dv[0]["tr_amend"].ToString().Trim().Trim() != "0")
                    {
                        lb_Amend_Show.Visible = true;
                        spanContractEdit.Visible = true;
                        rbl_amend.SelectedValue = dv[0]["tr_amend"].ToString().Trim().Trim();
                        txtamend_other_desc.Text = Server.HtmlEncode(dv[0]["tr_amend_other_desc"].ToString().Trim().Trim());
                    }
                    #endregion
                    break;
                case "R":
                    #region R
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = true;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;
                case "S":
                    #region S
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_bd.Checked = true;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;


                case "T":
                    #region T
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = true;
                    rb_conttype_other.Checked = true;
                    txt_class_other_desc.Enabled = true;
                    btnDelete2.Visible = true;
                    txt_class_other_desc.Text = Server.HtmlEncode(dv[0]["tr_class_other_desc"].ToString().Trim());
                    PL_CoPromoter.Visible = true;
                    h_px_empno.Value = dv[0]["tr_promoter_no_other"].ToString().Trim();
                    if (h_px_empno.Value != "")
                    {
                        //SDS_emp.SelectCommand = " select rtrim(com_cname) com_cname from common..comper where com_empno ='" + oRCM.SQLInjectionReplaceAll(h_px_empno.Value) + "'  ";
                        //SDS_emp.DataBind();
                        //System.Data.DataView dv_px = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
                        #region --- query ---

                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.Text;

                            sqlCmd.CommandText = @"select rtrim(com_cname) com_cname from common..comper where com_empno =@empno";

                            // --- 避免匯出查詢過久而當掉 --- //
                            sqlCmd.CommandTimeout = 0;

                            sqlCmd.Parameters.Clear();
                            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(h_px_empno.Value));


                            try
                            {

                                sqlConn.Open();
                                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                                dt = new DataTable();
                                sqlDA.Fill(dt);


                            }
                            catch (Exception ex)
                            {
                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );

                                oRCM.ErrorExceptionDataToDB(logMail);

                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }

                        #endregion
                        DataView dv_px = dt.DefaultView;
                        if (dv_px.Count >= 1)
                        {
                            txt_px_name.Text = Server.HtmlEncode(dv_px[0]["com_cname"].ToString().Trim());
                        }
                    }
                    //SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT top 1  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = '" + oRCM.SQLInjectionReplaceAll(ViewState["com_orgcd"].ToString()) + "' )";
                    //SDS_emp.DataBind();
                    //System.Data.DataView dv_oA = (DataView)SDS_emp.Select(new DataSourceSelectArguments());

                    #region --- query ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;

                        sqlCmd.CommandText = @"select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT top 1  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = @org )";

                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;

                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@org", oRCM.SQLInjectionReplaceAll(ViewState["com_orgcd"].ToString()));

                        try
                        {

                            sqlConn.Open();
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dt = new DataTable();
                            sqlDA.Fill(dt);


                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);

                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }

                    #endregion
                    DataView dv_oA = dt.DefaultView;
                    if (dv_oA.Count >= 1)
                    {
                        LB_adm_text.Text = Server.HtmlEncode(dv_oA[0]["cname"].ToString().Trim());
                    }
                    if (dv[0]["tr_org_adm"].ToString().Trim() != "")
                    {
                        rb_adm_yes.Checked = true;
                        rb_adm_no.Checked = false;
                    }

                    else
                        rb_adm_no.Checked = true;

                    #endregion
                    break;

                case "F":
                    #region A
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    rb_conttype_ui.Enabled = true;
                    rb_conttype_ui.Checked = true;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;

            }
            BindContType(dv[0]["tr_conttype"].ToString().Trim());
            #endregion
            #region 契約預估金額
            ddlContMoneyType.SelectedValue = IIf(dv[0]["tr_money_type"].ToString().Trim() == "", "TWD", dv[0]["tr_money_type"].ToString().Trim());
            txtContMoney.Text = Server.HtmlEncode(dv[0]["tr_money"].ToString().Trim());
            TB_money_rate.Text = Server.HtmlEncode(dv[0]["tr_money_rate"].ToString().Trim());

            #endregion
            #region 契約期間
            txt_contsdate.Text = Server.HtmlEncode(dv[0]["tr_contsdate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tr_contsdate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");
            txt_contedate.Text = Server.HtmlEncode(dv[0]["tr_contedate"].ToString().Trim().Length > 0 ? DateTime.ParseExact(dv[0]["tr_contedate"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "");
            #endregion
            ViewState["ver"] = dv[0]["tr_sRC_ver"].ToString().Trim();
            #region 其他需求
            if (dv[0]["tr_otherrequire_ver"].ToString().Trim() == "")
                ViewState["tr_oRC_ver"] = 2;
            else
                ViewState["tr_oRC_ver"] = dv[0]["tr_otherrequire_ver"].ToString().Trim();
            Bind_oRC(ViewState["seno"].ToString(), ViewState["tr_oRC_ver"].ToString().Trim());
            #endregion
            lb_keyin_emp_no.Text = Server.HtmlEncode(dv[0]["tr_keyin_emp_no"].ToString().Trim());
            lb_keyin_emp_name.Text = Server.HtmlEncode(dv[0]["tr_keyin_emp_name"].ToString().Trim());
            lb_keyin_tel.Text = Server.HtmlEncode(GetEmptel(dv[0]["tr_keyin_emp_no"].ToString().Trim()));
            lb_keyin_date.Text = Server.HtmlEncode(DateTime.ParseExact(dv[0]["tr_keyin_date"].ToString().Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd"));
            lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tr_modify_emp_no"].ToString().Trim());
            lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tr_modify_emp_name"].ToString().Trim());
            lb_modify_tel.Text = Server.HtmlEncode(GetEmptel(dv[0]["tr_modify_emp_no"].ToString().Trim()));
            lb_modify_date.Text = DateTime.Now.ToString("yyyy/MM/dd"); //建檔日期
            lb_send_date.Text = "";
            if (dv[0]["tr_急件"].ToString().Trim() == "1")
            {
                CB_急件.Checked = true;
                TB_急件原因.Text = Server.HtmlDecode(Server.HtmlEncode(dv[0]["tr_急件原因"].ToString()));
            }
        }
    }

    private void BindData_file()
    {
        //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = "esp_TreatyApply_files";
        //SDS_gv_file.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyApply_files";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        if (cb_conttype_b0.Checked)
            strCondition += "B0,";

        if (cb_conttype_b1.Checked)
            strCondition += "B1,";

        if (cb_conttype_d4.Checked)
            strCondition += "D4,";

        if (cb_conttype_d5.Checked)
            strCondition += "D5,";

        if (cb_conttype_d7.Checked)
            strCondition += "D7,";
        if (cb_conttype_ns.Checked)
            strCondition += "NS,";

        if (cb_conttype_rb.Checked)
            strCondition += "RB,";
        if (cb_conttype_m.Checked)
            strCondition += "ND,";
        if (rb_conttype_bd.Checked)
            strCondition += "UN,";
        if (rb_conttype_uo.Checked)
            strCondition += "A0,";
        if (rb_conttype_bd.Checked)
            strCondition += "BD,";
        if (rb_conttype_other.Checked)
            strCondition += "OT,";
        if (rb_conttype_ui.Checked)
            strCondition += "F7,FA,F9,F3,FC,";
        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);

        #endregion
        //SDS_ContType.SelectCommand = "exec esp_treaty_codetable_query_by_group  '" + SQLInjectionReplaceAll(strCondition) + "' ,'10' ";
        //SDS_ContType.DataBind();
        //ddlContType.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_codetable_query_by_group";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@code_group", oRCM.SQLInjectionReplaceAll(strCondition));
            sqlCmd.Parameters.AddWithValue("@code_type", "10");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
        }
        #endregion
    }
    private void Bind_oRC(string str_seno, string str_ver)
    {
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.SelectParameters.Clear();
        //SDS_oRC.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.SelectCommand = " select * from treaty_requisition_oRC where tr_seno=@seno and troRC_ver=@ver";
        //SDS_oRC.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_oRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(str_ver));
        //for (int i = 0; i < this.SDS_oRC.SelectParameters.Count; i++)
        //{
        //    SDS_oRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_oRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_oRC.Select(new DataSourceSelectArguments());


        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"select * from treaty_requisition_oRC where tr_seno=@seno and troRC_ver=@ver";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                switch (dvR[i]["troRC_val"].ToString())
                {
                    case "1":
                        rb_other_1.Checked = true;
                        txt_otherrequire_contno.Text = Server.HtmlEncode(dvR[i]["troRC_desc1"].ToString());
                        TB_otherrequire_handle_name.Text = Server.HtmlEncode(dvR[i]["troRC_desc2"].ToString());
                        break;
                    case "2":
                        rb_other_2.Checked = true;
                        txt_otherrequire_asked_name.Text = Server.HtmlEncode(dvR[i]["troRC_desc1"].ToString());
                        break;
                    case "3":
                        rb_other_3.Checked = true;
                        break;
                    case "4":
                        rb_other_4.Checked = true;
                        break;
                    case "T":
                        rb_other_T.Checked = true;
                        txt_otherrequire_desc.Text = Server.HtmlEncode(dvR[i]["troRC_desc1"].ToString());
                        break;
                }
            }
        }
    }
    #region Save
    private void DoSaveDraft(string str_status)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.SelectParameters.Clear();
        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.SelectCommand = SQLInjectionReplaceAll("esp_TreatyApply_modify");
        //SDS_NR.SelectParameters.Add("tr_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.SelectParameters.Add("tr_year", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
        //SDS_NR.SelectParameters.Add("tr_orgcd", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
        //SDS_NR.SelectParameters.Add("tr_class", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
        //SDS_NR.SelectParameters.Add("tr_sn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
        //SDS_NR.SelectParameters.Add("tr_ver", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
        //SDS_NR.SelectParameters.Add("tr_seqsn", SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
        //SDS_NR.SelectParameters.Add("tr_status", SQLInjectionReplaceAll(str_status)); //
        //SDS_NR.SelectParameters.Add("tr_old_contno", txtOldContno.Text.Trim()); //將舊案的流水號存入
        //SDS_NR.SelectParameters.Add("tr_req_dept", SQLInjectionReplaceAll(txt_req_dept.Value.Trim()));
        //SDS_NR.SelectParameters.Add("tr_promoter_name", SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
        //SDS_NR.SelectParameters.Add("tr_promoter_no", SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
        //SDS_NR.SelectParameters.Add("tr_name", SQLInjectionReplaceAll(txt_name.Text.Trim()));//洽案(契約)名稱
        //SDS_NR.SelectParameters.Add("tr_compidno", SQLInjectionReplaceAll(""));
        //SDS_NR.SelectParameters.Add("tr_compname", SQLInjectionReplaceAll(""));
        //SDS_NR.SelectParameters.Add("tr_compidno_all", SQLInjectionReplaceAll(h_compno.Value.Replace(",", "㊣")));
        //SDS_NR.SelectParameters.Add("tr_compname_all", SQLInjectionReplaceAll(""));
        ////if (rb_language_other.Checked)
        ////    SDS_NR.SelectParameters.Add("tr_language", "0");//契約語文-其他
        //if (rb_language_chiness.Checked)
        //    SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("1"));//契約語文-中文
        //if (rb_language_english.Checked)
        //    SDS_NR.SelectParameters.Add("tr_language", SQLInjectionReplaceAll("2"));//契約語文-英文
        //#region 案件性質
        //SDS_NR.SelectParameters.Add("tr_conttype_b0", SQLInjectionReplaceAll(cb_conttype_b0.Checked ? "1" : "0"));//技術服務
        //SDS_NR.SelectParameters.Add("tr_conttype_b1", SQLInjectionReplaceAll(cb_conttype_b1.Checked ? "1" : "0"));//合作開發
        //SDS_NR.SelectParameters.Add("tr_conttype_d4", SQLInjectionReplaceAll(cb_conttype_d4.Checked ? "1" : "0"));//技術授權
        //SDS_NR.SelectParameters.Add("tr_conttype_d5", SQLInjectionReplaceAll(cb_conttype_d5.Checked ? "1" : "0"));//專利授權
        //SDS_NR.SelectParameters.Add("tr_conttype_d7", SQLInjectionReplaceAll(cb_conttype_d7.Checked ? "1" : "0"));//專利讓與
        //SDS_NR.SelectParameters.Add("tr_conttype_ns", SQLInjectionReplaceAll(cb_conttype_ns.Checked ? "1" : "0"));//新創事業(洽案)
        //SDS_NR.SelectParameters.Add("tr_conttype_rb", SQLInjectionReplaceAll(cb_conttype_rb.Checked ? "1" : "0"));//標案 
        //SDS_NR.SelectParameters.Add("tr_conttype_uo", SQLInjectionReplaceAll(rb_conttype_uo.Checked ? "1" : "0"));//國外支出(無收入) 
        //SDS_NR.SelectParameters.Add("tr_conttype_ui", SQLInjectionReplaceAll(rb_conttype_ui.Checked ? "1" : "0"));//國外支出(無收入) 
        //SDS_NR.SelectParameters.Add("tr_class_other_desc", txt_class_other_desc.Text.Trim()); ////案件類別-其他  描述
        //#endregion
        //#region 契約修訂
        //if (spanContractEdit.Visible)
        //{//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
        //    SDS_NR.SelectParameters.Add("tr_amend", SQLInjectionReplaceAll(rbl_amend.SelectedValue));
        //    SDS_NR.SelectParameters.Add("tr_amend_other_desc", SQLInjectionReplaceAll(txtamend_other_desc.Text));
        //}
        //else
        //{
        //    SDS_NR.SelectParameters.Add("tr_amend", SQLInjectionReplaceAll("0"));
        //    SDS_NR.SelectParameters.Add("tr_amend_other_desc", SQLInjectionReplaceAll(""));
        //}
        //#endregion
        //SDS_NR.SelectParameters.Add("tr_contsdate", SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
        //SDS_NR.SelectParameters.Add("tr_contedate", SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
        //if (txtComplexNo.Text.Substring(6, 1) == "T")
        //{
        //    SDS_NR.SelectParameters.Add("tr_promoter_no_other", h_px_empno.Value);
        //    if (rb_adm_yes.Checked)
        //        SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll("1"));
        //    if (rb_adm_no.Checked)
        //        SDS_NR.SelectParameters.Add("tr_org_adm", SQLInjectionReplaceAll(""));
        //}
        //else
        //{
        //    SDS_NR.SelectParameters.Add("tr_promoter_no_other", SQLInjectionReplaceAll(""));
        //    SDS_NR.SelectParameters.Add("tr_org_adm", "");
        //}
        //SDS_NR.SelectParameters.Add("tr_otherrequire_ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString())); //其他需求
        //SDS_NR.SelectParameters.Add("tr_keyin_emp_no", SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
        //SDS_NR.SelectParameters.Add("tr_keyin_emp_name", SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
        //SDS_NR.SelectParameters.Add("tr_keyin_date", SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
        //SDS_NR.SelectParameters.Add("tr_modify_emp_no", SQLInjectionReplaceAll(ssoUser.empNo));  // 修改工號
        //SDS_NR.SelectParameters.Add("tr_modify_emp_name", SQLInjectionReplaceAll(ssoUser.empName.Trim()));// 修改人
        //SDS_NR.SelectParameters.Add("tr_modify_date", SQLInjectionReplaceAll(DateTime.Now.ToString("yyyyMMdd")));      // 修改日期
        //SDS_NR.SelectParameters.Add("tr_file_flag", SQLInjectionReplaceAll("0"));//附件狀態
        //SDS_NR.SelectParameters.Add("tr_conttype", SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
        //SDS_NR.SelectParameters.Add("tr_money_type", SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
        //if (txtContMoney.Text.Trim() == "")
        //    SDS_NR.SelectParameters.Add("tr_money", SQLInjectionReplaceAll("0"));
        //else
        //    SDS_NR.SelectParameters.Add("tr_money", SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
        //SDS_NR.SelectParameters.Add("tr_money_rate", SQLInjectionReplaceAll(匯率));

        //SDS_NR.SelectParameters.Add("NewType", SQLInjectionReplaceAll("1")); // 草稿
        //SDS_NR.SelectParameters.Add("急件", SQLInjectionReplaceAll(CB_急件.Checked == true ? "1" : "0"));
        //SDS_NR.SelectParameters.Add("急件原因", SQLInjectionReplaceAll(TB_急件原因.Text));
        //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
        //{
        //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_NR.DataBind();
        //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
        //if (dv_actno.Count >= 1)
        //{
        //}


        DataTable dt = new DataTable();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyApply_modify";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tr_year", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(0, 4)));
            sqlCmd.Parameters.AddWithValue("@tr_orgcd", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(4, 2)));
            sqlCmd.Parameters.AddWithValue("@tr_class", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(6, 1)));
            sqlCmd.Parameters.AddWithValue("@tr_sn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(7, 4)));
            sqlCmd.Parameters.AddWithValue("@tr_ver", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(11, 1)));  //案件編號(版次)   
            sqlCmd.Parameters.AddWithValue("@tr_seqsn", oRCM.SQLInjectionReplaceAll(txtComplexNo.Text.Substring(13, 2)));//案件編號(件次)
            sqlCmd.Parameters.AddWithValue("@tr_status", oRCM.SQLInjectionReplaceAll(str_status)); //
            sqlCmd.Parameters.AddWithValue("@tr_old_contno", txtOldContno.Text.Trim()); //將舊案的流水號存入
            sqlCmd.Parameters.AddWithValue("@tr_req_dept", oRCM.SQLInjectionReplaceAll(txt_req_dept.Value.Trim()));
            sqlCmd.Parameters.AddWithValue("@tr_promoter_name", oRCM.SQLInjectionReplaceAll(txt_promoter_name.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@tr_promoter_no", oRCM.SQLInjectionReplaceAll(txt_promoter_empno.Value));//承辦人姓名
            sqlCmd.Parameters.AddWithValue("@tr_name", oRCM.SQLInjectionReplaceAll(oRCM.HtmlWrite(txt_name.Text.Trim())));//洽案(契約)名稱
            sqlCmd.Parameters.AddWithValue("@tr_compidno", "");
            sqlCmd.Parameters.AddWithValue("@tr_compname", "");
            sqlCmd.Parameters.AddWithValue("@tr_compidno_all", oRCM.SQLInjectionReplaceAll(h_compno.Value.Replace(",", "㊣")));
            sqlCmd.Parameters.AddWithValue("@tr_compname_all", "");
            //if (rb_language_other.Checked)
            //    sqlCmd.Parameters.AddWithValue("tr_language", "0");//契約語文-其他
            if (rb_language_chiness.Checked)
                sqlCmd.Parameters.AddWithValue("@tr_language", "1");//契約語文-中文
            if (rb_language_english.Checked)
                sqlCmd.Parameters.AddWithValue("@tr_language", "2");//契約語文-英文
            #region 案件性質
            sqlCmd.Parameters.AddWithValue("@tr_conttype_b0", oRCM.SQLInjectionReplaceAll(cb_conttype_b0.Checked ? "1" : "0"));//技術服務
            sqlCmd.Parameters.AddWithValue("@tr_conttype_b1", oRCM.SQLInjectionReplaceAll(cb_conttype_b1.Checked ? "1" : "0"));//合作開發
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d4", oRCM.SQLInjectionReplaceAll(cb_conttype_d4.Checked ? "1" : "0"));//技術授權
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d5", oRCM.SQLInjectionReplaceAll(cb_conttype_d5.Checked ? "1" : "0"));//專利授權
            sqlCmd.Parameters.AddWithValue("@tr_conttype_d7", oRCM.SQLInjectionReplaceAll(cb_conttype_d7.Checked ? "1" : "0"));//專利讓與
            sqlCmd.Parameters.AddWithValue("@tr_conttype_ns", oRCM.SQLInjectionReplaceAll(cb_conttype_ns.Checked ? "1" : "0"));//新創事業(洽案)
            sqlCmd.Parameters.AddWithValue("@tr_conttype_rb", oRCM.SQLInjectionReplaceAll(cb_conttype_rb.Checked ? "1" : "0"));//標案 
            sqlCmd.Parameters.AddWithValue("@tr_conttype_uo", oRCM.SQLInjectionReplaceAll(rb_conttype_uo.Checked ? "1" : "0"));//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tr_conttype_ui", oRCM.SQLInjectionReplaceAll(rb_conttype_ui.Checked ? "1" : "0"));//國外支出(無收入) 
            sqlCmd.Parameters.AddWithValue("@tr_class_other_desc", txt_class_other_desc.Text.Trim()); ////案件類別-其他  描述
            #endregion
            #region 契約修訂
            if (spanContractEdit.Visible)
            {//如果契約修訂有打開，則更新契約修訂的資料，否則不更動
                sqlCmd.Parameters.AddWithValue("@tr_amend", oRCM.SQLInjectionReplaceAll(rbl_amend.SelectedValue));
                sqlCmd.Parameters.AddWithValue("@tr_amend_other_desc", oRCM.SQLInjectionReplaceAll(txtamend_other_desc.Text));
            }
            else
            {
                sqlCmd.Parameters.AddWithValue("@tr_amend", "0");
                sqlCmd.Parameters.AddWithValue("@tr_amend_other_desc", "");
            }
            #endregion
            sqlCmd.Parameters.AddWithValue("@tr_contsdate", oRCM.SQLInjectionReplaceAll(txt_contsdate.Text.Trim().Replace("/", "")));  //契約期間(起)
            sqlCmd.Parameters.AddWithValue("@tr_contedate", oRCM.SQLInjectionReplaceAll(txt_contedate.Text.Trim().Replace("/", "")));  //契約期間(迄)
            if (txtComplexNo.Text.Substring(6, 1) == "T")
            {
                sqlCmd.Parameters.AddWithValue("@tr_promoter_no_other", oRCM.SQLInjectionReplaceAll(h_px_empno.Value));
                if (rb_adm_yes.Checked)
                    sqlCmd.Parameters.AddWithValue("@tr_org_adm", "1");
                if (rb_adm_no.Checked)
                    sqlCmd.Parameters.AddWithValue("@tr_org_adm", "");
            }
            else
            {
                sqlCmd.Parameters.AddWithValue("@tr_promoter_no_other", "");
                sqlCmd.Parameters.AddWithValue("@tr_org_adm", "");
            }
            sqlCmd.Parameters.AddWithValue("@tr_otherrequire_ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString())); //其他需求
            sqlCmd.Parameters.AddWithValue("@tr_keyin_emp_no", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_no.Text.Trim()));    // 建檔工號
            sqlCmd.Parameters.AddWithValue("@tr_keyin_emp_name", oRCM.SQLInjectionReplaceAll(lb_keyin_emp_name.Text.Trim()));// 建檔人
            sqlCmd.Parameters.AddWithValue("@tr_keyin_date", oRCM.SQLInjectionReplaceAll(lb_keyin_date.Text.Trim().Replace("/", "")));        // 建檔日期
            sqlCmd.Parameters.AddWithValue("@tr_modify_emp_no", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));  // 修改工號
            sqlCmd.Parameters.AddWithValue("@tr_modify_emp_name", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));// 修改人
            sqlCmd.Parameters.AddWithValue("@tr_modify_date", oRCM.SQLInjectionReplaceAll(DateTime.Now.ToString("yyyyMMdd")));      // 修改日期
            sqlCmd.Parameters.AddWithValue("@tr_file_flag", "0");//附件狀態
            sqlCmd.Parameters.AddWithValue("@tr_conttype", oRCM.SQLInjectionReplaceAll(ddlContType.SelectedValue.ToString().Trim())); //契約性質
            sqlCmd.Parameters.AddWithValue("@tr_money_type", oRCM.SQLInjectionReplaceAll(ddlContMoneyType.SelectedValue.ToString().Trim()));//契約預估金額
            if (txtContMoney.Text.Trim() == "")
                sqlCmd.Parameters.AddWithValue("@tr_money", "0");
            else
                sqlCmd.Parameters.AddWithValue("@tr_money", oRCM.SQLInjectionReplaceAll(txtContMoney.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@tr_money_rate", oRCM.SQLInjectionReplaceAll(匯率));

            sqlCmd.Parameters.AddWithValue("@NewType", "1"); // 草稿
            sqlCmd.Parameters.AddWithValue("@急件", oRCM.SQLInjectionReplaceAll(CB_急件.Checked == true ? "1" : "0"));
            sqlCmd.Parameters.AddWithValue("@急件原因", oRCM.SQLInjectionReplaceAll(TB_急件原因.Text));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_actno = dt.DefaultView;

        #region 其它需求
        //this.SDS_oRC.DeleteParameters.Clear();
        //this.SDS_oRC.DeleteCommandType = SqlDataSourceCommandType.Text;
        //this.SDS_oRC.DeleteCommand = " delete treaty_requisition_oRC where tr_seno =@seno";
        //this.SDS_oRC.DeleteParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //this.SDS_oRC.Delete();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = "delete treaty_requisition_oRC where tr_seno =@seno";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        if (rb_other_1.Checked == true)
        {
            //this.SDS_oRC.InsertParameters.Clear();
            //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
            //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("1"));
            //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_contno.Text));
            //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(TB_otherrequire_handle_name.Text));
            //this.SDS_oRC.Insert();
            doInsert("1");

        }
        if (rb_other_2.Checked == true)
        {
            //this.SDS_oRC.InsertParameters.Clear();
            //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
            //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("2"));
            //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_asked_name.Text));
            //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.Insert();
            doInsert("2");
        }
        if (rb_other_3.Checked == true)
        {
            //this.SDS_oRC.InsertParameters.Clear();
            //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
            //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("3"));
            //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.Insert();
            doInsert("3");
        }
        if (rb_other_4.Checked == true)
        {
            //this.SDS_oRC.InsertParameters.Clear();
            //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
            //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("4"));
            //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.Insert();
            doInsert("4");
        }
        if (rb_other_T.Checked == true)
        {
            //this.SDS_oRC.InsertParameters.Clear();
            //this.SDS_oRC.InsertCommandType = SqlDataSourceCommandType.Text;
            //this.SDS_oRC.InsertCommand = " insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";
            //this.SDS_oRC.InsertParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("ver", SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            //this.SDS_oRC.InsertParameters.Add("val", SQLInjectionReplaceAll("T"));
            //this.SDS_oRC.InsertParameters.Add("desc1", SQLInjectionReplaceAll(txt_otherrequire_desc.Text));
            //this.SDS_oRC.InsertParameters.Add("desc2", SQLInjectionReplaceAll(""));
            //this.SDS_oRC.Insert();
            doInsert("T");
        }
        #endregion
    }
    #endregion

    public void doInsert(string type)
    {
        string stype = type;
        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"insert treaty_requisition_oRC (tr_seno,troRC_ver,troRC_val,troRC_desc1,troRC_desc2) values( @seno,@ver,@val,@desc1,@desc2) ";

            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["tr_oRC_ver"].ToString()));
            sqlCmd.Parameters.AddWithValue("@val", oRCM.SQLInjectionReplaceAll(stype));
            if (type == "1")
            {
                sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_contno.Text));
                sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(TB_otherrequire_handle_name.Text));
            }
            if (type == "2")
            {
                sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_asked_name.Text));
                sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(""));
            }
            if (type == "3")
            {
                sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(""));
                sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(""));
            }
            if (type == "4")
            {
                sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(""));
                sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(""));
            }
            if (type == "T")
            {
                sqlCmd.Parameters.AddWithValue("@desc1", oRCM.SQLInjectionReplaceAll(txt_otherrequire_desc.Text));
                sqlCmd.Parameters.AddWithValue("@desc2", oRCM.SQLInjectionReplaceAll(""));
            }

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }

    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    protected void btnSaveDraft_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_empno').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_empno').click(function () { $('#txt_promoter_empno').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }

        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (txt_name.Text == "")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name.Text').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name.Text", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須輸入','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "BT_Customer", script_alert);
        }
        if (ddlContType.SelectedValue == "")
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }

        if (((txt_contsdate.Text != "")) && ((txt_contedate.Text != "")))
        {
            if ((!CheckDateTimeType(txt_contsdate.Text)) && (!CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }
        if (txtComplexNo.Text.Trim() != "")
        {
            if ((!IsNatural_Number(txtComplexNo.Text.Substring(0, 4))))
                str_danger = "1";
        }
        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((x_dept.Text.Trim().Length > 8) || (!IsNatural_Number(x_dept.Text.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((!IsNatural_Number(h_compno.Value.Replace(",", "").Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
            str_danger = "1";
        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";
        if (txt_req_dept.Value.Length > 7)
            str_danger = "1";
        if (txt_req_dept.Value.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_contno.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (TB_otherrequire_handle_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_asked_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";

        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        else
        {
            if (ddlContMoneyType.SelectedValue == "TWD")
            {
                if (!(匯率 == "1" || 匯率 == "1.00000"))
                {
                    str_error += "★幣別是新台幣,匯率只能是1\\n ";
                    string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別是新台幣,匯率只能是1','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
                }
            }
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }

        if (TB_急件原因.Text.Trim() != "" && CB_急件.Checked == false)
        {
            str_error += "★請勾選急件原因\\n ";
            string script_alert = "<script language='javascript'> $('#CB_急件').validationEngine('showPrompt', '★請勾選急件原因','','',true); $('#CB_急件').click(function () { $('#CB_急件').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "CB_急件", script_alert);
        }


        if (str_danger == "1")
            Response.Redirect("../danger.aspx");


        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            DoSaveDraft("2");
            Treaty_log(ViewState["seno"].ToString(), "暫存草稿", "", "", "treaty\\TreatyApply_modify.aspx");
            string script = "<script language='javascript'>alert('暫存草稿成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
        }
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        HttpContext.Current.Response.Cache.SetNoServerCaching();
        HttpContext.Current.Response.Cache.SetNoStore();
    }
    protected void btnSendApply_Click(object sender, EventArgs e)
    {
        string str_error = "";
        string str_danger = "0";
        if ((txt_name.Text == "") || (txt_name.Text == "請輸入契約名稱"))
        {
            str_error += "★請輸入契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name", script_alert);
        }
        if (txt_promoter_empno.Value == "")
        {
            str_error += "★單位承辦人 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_promoter_name').validationEngine('showPrompt', '★單位承辦人 必須輸入','','',true); $('#txt_promoter_name').click(function () { $('#txt_promoter_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_promoter_empno", script_alert);
        }
        if (!((rb_language_chiness.Checked) || (rb_language_english.Checked)))
        {
            str_error += "★契約語文 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#rb_language_chiness').validationEngine('showPrompt', '★契約語文 必須挑選','','',true); $('#rb_language_chiness').click(function () { $('#rb_language_chiness').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "rb_language_chiness", script_alert);
        }
        if (txt_name.Text == "")
        {
            str_error += "★契約名稱 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#txt_name.Text').validationEngine('showPrompt', '★契約名稱 必須輸入','','',true); $('#txt_name').click(function () { $('#txt_name').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txt_name.Text", script_alert);
        }
        if (h_compno.Value == "")
        {
            str_error += "★簽約對象 必須輸入\\n ";
            string script_alert = "<script language='javascript'> $('#BT_Customer').validationEngine('showPrompt', '★簽約對象 必須輸入','','',true); $('#BT_Customer').click(function () { $('#BT_Customer').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "BT_Customer", script_alert);
        }
        if (ddlContType.SelectedValue == "")
        {
            str_error += "★契約性質 必須挑選\\n ";
            string script_alert = "<script language='javascript'> $('#ddlContType').validationEngine('showPrompt', '★契約性質 必須挑選','','',true); $('#ddlContType').click(function () { $('#ddlContType').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "ddlContType", script_alert);
        }
        else
        {
            if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
                str_danger = "1";
        }
        if (((txt_contsdate.Text != "")) && ((txt_contedate.Text != "")))
        {
            if ((!CheckDateTimeType(txt_contsdate.Text)) && (!CheckDateTimeType(txt_contedate.Text)))
            {
                //DateTime dt1 = DateTime.Parse(txt_contsdate.Text);
                //DateTime dt2 = DateTime.Parse(txt_contedate.Text);
                int dt1 = int.Parse(txt_contsdate.Text.Replace("/", ""));
                int dt2 = int.Parse(txt_contedate.Text.Replace("/", ""));
                if (dt1 > dt2)
                    str_error += "★契約期間異常 (起日 > 訖日) \\n ";
            }
        }
        if (txtComplexNo.Text.Trim() != "")
        {
            if ((!IsNatural_Number(txtComplexNo.Text.Substring(0, 4))))
                str_danger = "1";
        }
        if ((txt_req_dept.Value.Trim().Length > 8) || (!IsNatural_Number(txt_req_dept.Value.Trim())))
            str_danger = "1";
        if ((txt_promoter_empno.Value.Length > 7) || (!IsNatural_Number(txt_promoter_empno.Value.Trim())))
            str_danger = "1";
        if ((ddlContType.SelectedValue.Length > 3) || (!IsNatural_Number(ddlContType.SelectedValue.Trim())))
            str_danger = "1";
        if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
            str_danger = "1";
        if ((!IsNatural_Number(h_compno.Value.Replace(",", "").Trim())))
            str_danger = "1";
        if (txt_req_dept.Value.Length > 7)
            str_danger = "1";

        if (ddlContMoneyType.SelectedValue != "TWD" && (匯率 == "1" || 匯率 == "1.00000" || 匯率 == ""))
        {
            str_error += "★幣別不是新台幣,須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別不是新台幣,須填寫匯率','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
        }
        if (匯率 == "")
        {
            str_error += "★須填寫匯率\\n ";
            string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★匯率須填寫','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate1", script_alert);
        }
        else
        {
            if (ddlContMoneyType.SelectedValue == "TWD")
            {
                if (!(匯率 == "1" || 匯率 == "1.00000"))
                {
                    str_error += "★幣別是新台幣,匯率只能是1\\n ";
                    string script_alert = "<script language='javascript'> $('#TB_money_rate').validationEngine('showPrompt', '★幣別是新台幣,匯率只能是1','','',true); $('#TB_money_rate').click(function () { $('#TB_money_rate').validationEngine('hide'); })</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "TB_money_rate", script_alert);
                }
            }
            if ((ddlContMoneyType.SelectedValue.Length > 4) || (!IsNatural_Number(ddlContMoneyType.SelectedValue.Trim())))
                str_danger = "1";
        }

        if (txtContMoney.Text.Trim() == "")
        {
            str_error += "★契約預估金額必填! 只能填寫數字\\n ";
            string script_alert = "<script language='javascript'> $('#txtContMoney').validationEngine('showPrompt', '★契約預估金額必填 只能填寫數字(沒有請填0)','','',true); $('#txtContMoney').click(function () { $('#txtContMoney').validationEngine('hide'); })</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "txtContMoney", script_alert);
        }

        if (txt_req_dept.Value.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_contno.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (TB_otherrequire_handle_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_asked_name.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";
        if (txt_otherrequire_desc.Text.ToUpper().IndexOf("SCRIPT") >= 0) str_danger = "1";

        if ((txtContMoney.Text.Trim().Length > 20) || (!IsNumber(txtContMoney.Text)))
        {
            str_danger = "1";
        }
        //else
        //{
        //    if (txtContMoney.Text.Trim() == "")
        //        txtContMoney.Text = "0";
        //}

        if (str_error != "")
        {
            //string script_alert = "<script language='javascript'>alert(\"" + str_error + "\") ;</script>";
            //ClientScript.RegisterStartupScript(this.GetType(), "n1", script_alert);
        }
        else
        {
            if (str_danger == "1")
                Response.Redirect("../danger.aspx");
            if (txtComplexNo.Text.Substring(11, 4) == "A-01")
            {
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommand = " select tr_status  from treaty_requisition where  tr_seno = @sn ";
                //SDS_NR.SelectParameters.Add("sn", TypeCode.String, ViewState["seno"].ToString());
                //SDS_NR.DataBind();
                //System.Data.DataView dv = (DataView)SDS_NR.Select(new DataSourceSelectArguments());

                #region --- query ---
                DataTable dt = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.Text;

                    sqlCmd.CommandText = @"select tr_status  from treaty_requisition where  tr_seno = @sn ";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@sn", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                        sqlDA.Fill(dt);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion
                DataView dv = dt.DefaultView;
                if (dv.Count >= 1)
                {
                    if (dv[0]["tr_status"].ToString() == "E")
                    {
                        string script = "<script language='javascript'>alert('申請單重複送簽！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                    }
                    else
                    {
                        DoSaveDraft("2");
                        //SDS_NR.SelectParameters.Clear();
                        //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                        //SDS_NR.SelectCommand = "esp_TreatyApply_ecp_gen_preflow_special";
                        //SDS_NR.SelectParameters.Add("tr_seno", ViewState["seno"].ToString());
                        //SDS_NR.SelectParameters.Add("empno", ViewState["empno"].ToString());
                        //System.Data.DataView dvx = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                        #region --- query ---
                        DataTable dtx = new DataTable();
                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.StoredProcedure;

                            sqlCmd.CommandText = @"esp_TreatyApply_ecp_gen_preflow_special";

                            // --- 避免匯出查詢過久而當掉 --- //
                            sqlCmd.CommandTimeout = 0;

                            sqlCmd.Parameters.Clear();
                            sqlCmd.Parameters.AddWithValue("@tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));

                            try
                            {
                                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                                sqlDA.Fill(dtx);

                            }
                            catch (Exception ex)
                            {
                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );

                                oRCM.ErrorExceptionDataToDB(logMail);

                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }

                        #endregion
                        DataView dvx = dtx.DefaultView;
                        if ((dv.Count >= 1))
                        {
                            if (dvx[0][0].ToString() != "Pass")
                            {
                                string pGUID = Guid.NewGuid().ToString();

                                try
                                {
                                    string script = @"<script language='javascript'>seno_ECP();</script>";
                                    Page.ClientScript.RegisterStartupScript(this.GetType(), "seno_ECP", script, false);
                                    //return;
                                }
                                catch (Exception ex)
                                {
                                    #region --- modify ---
                                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                                    {
                                        SqlCommand sqlCmd = new SqlCommand();
                                        sqlCmd.Connection = sqlConn;
                                        sqlCmd.CommandType = CommandType.Text;

                                        sqlCmd.CommandText = @"update treaty_requisition set tr_ecp_Treaty01_guid='' ,tr_status='2' where  tr_seno=@seno  ";

                                        sqlCmd.CommandTimeout = 0;
                                        sqlCmd.Parameters.Clear();
                                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                                        try
                                        {
                                            sqlConn.Open();
                                            sqlCmd.ExecuteNonQuery();
                                        }
                                        catch (Exception exNR)
                                        {

                                            // --- 執行異常通報 --- //
                                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                                Request,
                                                Response,
                                                exNR
                                                );

                                            oRCM.ErrorExceptionDataToDB(logMail);

                                        }
                                        finally
                                        {
                                            sqlConn.Close();
                                        }
                                    }

                                    #endregion

                                    SqlCommand oCmd = new SqlCommand();
                                    oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                                    oCmd.CommandText = "esp_TreatyApply_ecpFail_Mail";
                                    oCmd.CommandType = CommandType.StoredProcedure;
                                    oCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                                    SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                                    DataSet dvf = new DataSet();
                                    oda.Fill(dvf, "myTable");


                                    string script = "<script language='javascript'>window.alert('送出簽核失敗,請重新發送!');</script>";
                                    ClientScript.RegisterStartupScript(this.GetType(), "n3", script);
                                }
                            }
                            else
                            {
                                DoSaveDraft("3");
                                //SDS_NR.SelectParameters.Clear();
                                //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                                //SDS_NR.SelectCommand = "esp_TreatyApplyToTreatyCase";//送出承辦
                                //SDS_NR.SelectParameters.Add("requisition_seno", ViewState["seno"].ToString());
                                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                                //{
                                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                                //}
                                //SDS_NR.DataBind();
                                //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());


                                SqlCommand oCmd = new SqlCommand();
                                oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
                                oCmd.CommandText = "esp_TreatyApplyToTreatyCase";
                                oCmd.CommandType = CommandType.StoredProcedure;
                                oCmd.Parameters.AddWithValue("@requisition_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                                SqlDataAdapter oda = new SqlDataAdapter(oCmd);
                                DataSet ds_actno = new DataSet();
                                oda.Fill(ds_actno, "myTable");
                                DataView dv_actno = ds_actno.Tables["myTable"].DefaultView;
                                if (dv_actno.Count >= 1)
                                {
                                }
                                string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
                            }
                        }
                    }
                }
                else
                {
                    string script = "<script language='javascript'>window.alert('送出簽核創單失敗!');</script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "n3", script);
                }
            }
            else
            {
                DoSaveDraft("3");
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyApplyToTreatyCase";//送出承辦
                //SDS_NR.SelectParameters.Add("requisition_seno", ViewState["seno"].ToString());
                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                //{
                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_NR.DataBind();
                //System.Data.DataView dv_actno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                //if (dv_actno.Count >= 1)
                //{
                //}

                #region --- query ---
                DataTable dtactno = new DataTable();
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;

                    sqlCmd.CommandText = @"esp_TreatyApplyToTreatyCase";

                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;

                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@requisition_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

                    try
                    {
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                        sqlDA.Fill(dtactno);

                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);

                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }

                #endregion

                DataView dv_actno = dtactno.DefaultView;
                if (dv_actno.Count >= 1)
                {
                }

                string script = "<script language='javascript'>alert('申請單送出成功！');location.href='./TreatyApply_View.aspx?seno=" + Server.HtmlEncode(ViewState["seno"].ToString()) + "';</script>";
                ClientScript.RegisterStartupScript(this.GetType(), "n1", script);
            }
        }
        Response.Cache.SetCacheability(HttpCacheability.NoCache);
        HttpContext.Current.Response.Cache.SetNoServerCaching();
        HttpContext.Current.Response.Cache.SetNoStore();
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserDelete")
        {
            h_compno.Value = h_compno.Value.Replace(e.CommandArgument.ToString().Trim(), "");
            BindData_Customer();
        }
    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        LinkButton LB = (LinkButton)e.Row.FindControl("LB_del");
        if (LB != null)
            LB.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");
    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            string str_file_url = "";
            string str_filename = "";
            //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyApply_file_modify";
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            //FileInfo fi = new FileInfo(str_file_url.Replace("/", "").Replace("..", ""));
            //if (fi.Exists)
            //{
            //fi.Delete();
            Treaty_log(ViewState["seno"].ToString(), "檔案刪除", str_file_url, ViewState["xIP"].ToString(), "treaty\\TreatyApply_modify.aspx");
            //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_gv_file.DeleteParameters.Clear();
            //SDS_gv_file.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_gv_file.DeleteCommand = SQLInjectionReplaceAll("esp_TreatyApply_file_modify");
            //SDS_gv_file.DeleteParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_gv_file.DeleteParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_gv_file.DeleteParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_gv_file.DeleteParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_gv_file.DeleteParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_gv_file.DeleteParameters.Add("mode", SQLInjectionReplaceAll("Del"));
            //SDS_gv_file.DeleteParameters.Add("fid", e.CommandArgument.ToString());
            //SDS_gv_file.Delete();

            #region --- modify ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "Del");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));


                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            //}
            BindData_file();
        }
        if (e.CommandName == "xEdit")
        {
            BindData_file();
        }
        if (e.CommandName == "xDownload")
        {
            string str_file_url = "";
            string str_filename = "";
            //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = "esp_TreatyApply_file_modify";
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;

                sqlCmd.CommandText = @"esp_TreatyApply_file_modify";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));

                try
                {
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                    sqlDA.Fill(dt);

                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_file_url, ViewState["xIP"].ToString(), "TreatyApply_modify.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                //Response.WriteFile(str_file_url.Replace("/", "").Replace("..", ""));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //ImageButton ib = (ImageButton)e.Row.FindControl("btnDelete");
            LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
            if (lb_del != null)
            {
                lb_del.Attributes.Add("onclick", "return  confirm('確定要刪除 ?');");

                Label lb_tcdf_no = (Label)e.Row.FindControl("LB_tcdf_no");

                LinkButton lb_edit = (LinkButton)e.Row.FindControl("LB_edit");
                lb_edit.Attributes.Add("onclick", "file_modify(" + lb_tcdf_no.Text + ");");
            }
        }
    }

    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = "esp_treaty_log";
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplaceAll(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplaceAll(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplaceAll(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplaceAll(GetUserIP()));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplaceAll(xApp));
        //SDS_log.Insert();


        #region --- insert ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treaty_log";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));

            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();

            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
    }
    protected void btnDelete2_Click(object sender, EventArgs e)
    {

        //SDS_NR.DeleteParameters.Clear();
        //SDS_NR.DeleteCommandType = SqlDataSourceCommandType.Text;
        //SDS_NR.DeleteCommand = "delete treaty_requisition_oRC where tr_seno=@tr_seno";
        //SDS_NR.DeleteParameters.Add("tr_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_NR.Delete();
        //SDS_NR.DeleteParameters.Clear();
        //SDS_NR.DeleteCommandType = SqlDataSourceCommandType.Text;
        //SDS_NR.DeleteCommand = "delete treaty_requisition where tr_seno=@tr_seno";
        //SDS_NR.DeleteParameters.Add("tr_seno", ViewState["seno"].ToString());
        //SDS_NR.Delete();

        #region --- modify ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"delete treaty_requisition_oRC where tr_seno=@tr_seno
                                   delete treaty_requisition where tr_seno=@tr_seno ";

            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tr_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));



            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {

                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        Response.Redirect("./Search_draft.aspx");
    }
}