<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="insert_loginlog">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="empno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ip" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="datetime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="modulename" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="systemname" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="systemid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="userdeptid" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="insert_loginlogResponse">
        <s:complexType />
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogSoapIn">
    <wsdl:part name="parameters" element="tns:insert_loginlog" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogSoapOut">
    <wsdl:part name="parameters" element="tns:insert_loginlogResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpGetIn" />
  <wsdl:message name="HelloWorldHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogHttpGetIn">
    <wsdl:part name="empno" type="s:string" />
    <wsdl:part name="ip" type="s:string" />
    <wsdl:part name="datetime" type="s:string" />
    <wsdl:part name="modulename" type="s:string" />
    <wsdl:part name="systemname" type="s:string" />
    <wsdl:part name="systemid" type="s:string" />
    <wsdl:part name="userdeptid" type="s:string" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogHttpGetOut" />
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogHttpPostIn">
    <wsdl:part name="empno" type="s:string" />
    <wsdl:part name="ip" type="s:string" />
    <wsdl:part name="datetime" type="s:string" />
    <wsdl:part name="modulename" type="s:string" />
    <wsdl:part name="systemname" type="s:string" />
    <wsdl:part name="systemid" type="s:string" />
    <wsdl:part name="userdeptid" type="s:string" />
  </wsdl:message>
  <wsdl:message name="insert_loginlogHttpPostOut" />
  <wsdl:portType name="ws_loginlogSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <wsdl:input message="tns:insert_loginlogSoapIn" />
      <wsdl:output message="tns:insert_loginlogSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="ws_loginlogHttpGet">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpGetIn" />
      <wsdl:output message="tns:HelloWorldHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <wsdl:input message="tns:insert_loginlogHttpGetIn" />
      <wsdl:output message="tns:insert_loginlogHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="ws_loginlogHttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <wsdl:input message="tns:insert_loginlogHttpPostIn" />
      <wsdl:output message="tns:insert_loginlogHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ws_loginlogSoap" type="tns:ws_loginlogSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <soap:operation soapAction="http://tempuri.org/insert_loginlog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ws_loginlogSoap12" type="tns:ws_loginlogSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <soap12:operation soapAction="http://tempuri.org/insert_loginlog" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ws_loginlogHttpGet" type="tns:ws_loginlogHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <http:operation location="/insert_loginlog" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output />
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ws_loginlogHttpPost" type="tns:ws_loginlogHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="insert_loginlog">
      <http:operation location="/insert_loginlog" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output />
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ws_loginlog">
    <wsdl:port name="ws_loginlogSoap" binding="tns:ws_loginlogSoap">
      <soap:address location="http://itriap5.itri.org.tw/ws_sf000/ws_loginlog.asmx" />
    </wsdl:port>
    <wsdl:port name="ws_loginlogSoap12" binding="tns:ws_loginlogSoap12">
      <soap12:address location="http://itriap5.itri.org.tw/ws_sf000/ws_loginlog.asmx" />
    </wsdl:port>
    <wsdl:port name="ws_loginlogHttpGet" binding="tns:ws_loginlogHttpGet">
      <http:address location="http://itriap5.itri.org.tw/ws_sf000/ws_loginlog.asmx" />
    </wsdl:port>
    <wsdl:port name="ws_loginlogHttpPost" binding="tns:ws_loginlogHttpPost">
      <http:address location="http://itriap5.itri.org.tw/ws_sf000/ws_loginlog.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>