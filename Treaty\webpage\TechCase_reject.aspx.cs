﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TechCase_reject : Treaty.common  
{
    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;

        if (!IsPostBack)
        {
            if (Request.QueryString["tt_seno"] != null)
            {
                if (!IsNumber(Request.QueryString["tt_seno"]) || (Request.QueryString["tt_seno"].Length == 0) || (Request.QueryString["tt_seno"].Length > 7))
                    Response.Redirect("../danger.aspx");
                ViewState["tt_seno"] = Request.QueryString["tt_seno"].ToString();
            }

            if (Request.QueryString["Role2"] != null)
            {
                ViewState["Role2"] = Request.QueryString["Role2"].ToString();
            }
        }
    }
   
    protected void BT_Save_Click(object sender, EventArgs e)
    {
        DoSave();

        SenndMail();

        StringBuilder script = new StringBuilder(@"
<script type='text/javascript'> 

parent.$('#hiReject').val('Y');  
close_win();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
    }

    private void DoSave()
    {
        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_modify";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();

            if (ViewState["Role2"].ToString() == "X")
                sqlCmd.Parameters.AddWithValue("@mode", "reject_X");
            else
                sqlCmd.Parameters.AddWithValue("@mode", "reject_x");

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void SenndMail()
    {
        #region 
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_TechCase_reject_mail";
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();           

            sqlCmd.Parameters.AddWithValue("@tt_seno", oRCM.SQLInjectionReplaceAll(ViewState["tt_seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tt_class", oRCM.SQLInjectionReplaceAll(ViewState["Role2"].ToString()));
            sqlCmd.Parameters.AddWithValue("@note", oRCM.SQLInjectionReplaceAll(txt_note.Text));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
}