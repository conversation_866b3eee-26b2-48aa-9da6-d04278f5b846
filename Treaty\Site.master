﻿<%@ Master Language="C#" AutoEventWireup="true" CodeFile="Site.master.cs" Inherits="Treaty_Site" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=11; IE=10; IE=9; IE=8" />
    <title>議約系統</title>
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/myITRIproject/jquery-ui.css" rel="stylesheet" />
    <link href="css/colorbox.css" rel="stylesheet" />
    <link href="../css/dialogstyle.css" rel="stylesheet" type="text/css" />

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <style type="text/css">
        .cluetip-inner {
            width: 98%
        }

        .td_right {
            text-align: right
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 1px;
            right: 0;
            background: url('../images/colorbox/colorboxcancel.gif') no-repeat;
            z-index: 10;
        }

        #cboxTitle {
            position: absolute;
            top: 0px;
            left: 0;
            text-align: center;
            width: 100%;
            height: 23px;
            color: #FFF;
            font-weight: bold;
            background: url('../images/colorbox/colorboxbarbg.gif') repeat-x top #2d70a4;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
        }

        .empty {
            color: #aaa;
        }

        .ui-cluetip-header, .ui-cluetip-content {
            overflow: auto;
            max-heisht: 4em;
        }

        .mce-content-body body {
            font-size: 16px;
        }

        .auto-style1 {
            width: 180px;
        }

        .auto-style2 {
            width: 11%;
        }

        .givebox {
            word-break: break-all;
        }


        .giveboxbtn:hover {
            background: #8f8f8f;
            color: #fff;
        }

        .giveboxbtn {
            border: 1px solid #8f8f8f;
            color: #5e5e5e;
            border-radius: 5px;
            padding: 1px 10px;
            background: transparent;
            transition: all ease-in-out 0.2s;
            margin-top: 10px;
            display: inline-block;
        }

        .timelinebox {
            border-radius: 10px;
            height: auto;
            overflow: auto;
            text-align: left;
            background-color: rgba(59, 59, 59, 0.1);
            padding: 1em;
            font-size: 12pt;
            width: 95%
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="WrapperBody">
            <div class="WrapperContent">
                <div class="WrapperMain">
                    <div style="margin-left: 15px; margin-top: 25px">
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </div>
                </div>
            </div>
        </div>

    </form>

    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="../Scripts/autoheight.js"></script>
</body>
</html>
