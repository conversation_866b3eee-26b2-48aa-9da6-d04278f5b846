﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;

public partial class Treaty_webpage_TreatyCase2_IP_Find : System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r;
        try
        {
            r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        }
        catch
        {
            return false;
        }
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {

            ViewState["URL"] = "";
            if (Request.QueryString["url"] != null)
                ViewState["URL"] = Uri.UnescapeDataString(Request.QueryString["url"].ToString());

            ViewState["sortorder"] = "asc";
            ViewState["sortField"] = "p40_patentno";
            BindDDL();
            Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
        }
    }

    protected void BindDDL()
    {
        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;

            sqlCmd.CommandText = @"SELECT org_orgcd AS orgcd, org_orgcd +'-' + org_abbr_chnm2 AS orgcd_name FROM common..orgcod WHERE (org_status = 'A') ";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_org.DataSource = dt;
                DDL_org.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


    }
    protected void Binddata(string str_sortField, string str_sort)
    {

        //SGV_search.PageIndex = 0;
        //SDS_search.SelectParameters.Clear();
        //SDS_search.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_search.SelectCommand = "esp_TreatyCase2_IP_find";
        //SDS_search.SelectParameters.Add("year",TB_year.Text );
        //SDS_search.SelectParameters.Add("org",DDL_org.SelectedValue );
        //SDS_search.SelectParameters.Add("keyword",TB_KW.Text );
        //SDS_search.SelectParameters.Add("sortField", str_sortField);
        //SDS_search.SelectParameters.Add("sortDir", str_sort);
        //for (int i = 0; i < this.SDS_search.SelectParameters.Count; i++)
        //{
        //    SDS_search.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_search.DataBind();
        //SGV_search.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase2_IP_find";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@year", oRCM.SQLInjectionReplaceAll(TB_year.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@org", oRCM.SQLInjectionReplaceAll(DDL_org.SelectedValue));
            sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll(TB_KW.Text.Trim()));
            sqlCmd.Parameters.AddWithValue("@sortField", oRCM.SQLInjectionReplaceAll(str_sortField));
            sqlCmd.Parameters.AddWithValue("@sortDir", oRCM.SQLInjectionReplaceAll(str_sort));

            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_search.DataSource = dt;
                SGV_search.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion


    }
    protected void SGV_cop_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }

    protected void SGV_search_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "UserView")
        {
            Session["Find_IP"] = e.CommandArgument.ToString();
            string script = "<script language='javascript'>close_win();</script>";
            ClientScript.RegisterStartupScript(this.GetType(), "n1", script);

        }
    }
    protected void SGV_search_PageIndexChanged(object sender, EventArgs e)
    {
        SGV_search.DataBind();
    }
    protected void SGV_search_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        SGV_search.PageIndex = e.NewPageIndex;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
    protected void SGV_search_Sorting(object sender, GridViewSortEventArgs e)
    {

        ViewState["sortField"] = e.SortExpression;
        if (ViewState["sortField"] == null)
            ViewState["sortField"] = e.SortExpression;
        else
        {
            if (ViewState["sortorder"].ToString() == "asc")
                ViewState["sortorder"] = "desc";
            else
                ViewState["sortorder"] = "asc";
        }
        SGV_search.PageIndex = 0;
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());

    }
    protected void SGV_search_RowCreated(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.Header) //如果是表頭
        {
            foreach (TableCell MyHeader in e.Row.Cells) //對每一格      
            {

                if (MyHeader.HasControls())
                {
                    if (((LinkButton)MyHeader.Controls[0]).CommandArgument == SGV_search.SortExpression)
                    {

                        System.Web.UI.WebControls.Image ig_sort = new System.Web.UI.WebControls.Image();
                        if (SGV_search.SortDirection == SortDirection.Ascending) //依排序方向加入箭號
                            ig_sort.ImageUrl = "../images/sort-up.gif";
                        else
                            ig_sort.ImageUrl = "../images/sort-down.gif";
                        //MyHeader.Controls.Add(new LiteralControl("↑"));
                        MyHeader.Controls.Add(ig_sort);
                    }

                }
            }

        }
    }
    protected void BT_search_Click(object sender, EventArgs e)
    {
        Binddata(ViewState["sortField"].ToString(), ViewState["sortorder"].ToString());
    }
}