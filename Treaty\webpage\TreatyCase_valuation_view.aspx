﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_valuation_view.aspx.cs" Inherits="TreatyCase_valuation_view" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>案件資訊</title>
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <link rel="stylesheet" type="text/css" href="../css/colorbox.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <script type="text/javascript" src="../Scripts/jquery.colorbox-min.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">
            <table style="margin-left: 15px; margin-top: 25px; width: 800px">
                <tr>
                    <td align="right">參考價：</td>
                    <td>
                        <asp:Label ID="TB_參考價" runat="server"></asp:Label>
                        元，
                           盡職調查結果： 
                                    <asp:DropDownList ID="DDL_盡職調查結果" runat="server" Enabled="false">
                                        <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                                        <asp:ListItem Value="1">綠</asp:ListItem>
                                        <asp:ListItem Value="5">黃</asp:ListItem>
                                        <asp:ListItem Value="9">紅</asp:ListItem>
                                    </asp:DropDownList>
                        歷程版本： 
                              <asp:DropDownList ID="DDL_SeqSn" runat="server" Width="80px" DataTextField="版本" DataValueField="版本" AutoPostBack="True" OnSelectedIndexChanged="DDL_SeqSn_SelectedIndexChanged"></asp:DropDownList>
                        <%--<asp:SqlDataSource ID="SDS_DDL_SeqSn" runat="server" />--%>
                    </td>
                </tr>
                <tr>
                    <td align="right">底價：</td>
                    <td>
                        <asp:Label ID="TB_底價" runat="server"></asp:Label>
                        元
                        ，<asp:CheckBox ID="CB_底價_無" runat="server" Text="無　說明：" AutoPostBack="true" Enabled="false" />
                        <asp:TextBox ID="TB_底價_無_說明" runat="server" Width="360px" Height="30px" TextMode="MultiLine" Visible="false" ReadOnly="true"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">第三方鑑價：</td>
                    <td>
                        <asp:Label ID="TB_第三方鑑價" runat="server"></asp:Label>
                        元
                        ，<asp:CheckBox ID="CB_第三方鑑價_無" runat="server" Text="無　說明：" Enabled="false" />
                        <asp:TextBox ID="TB_第三方鑑價_無_說明" runat="server" Width="360px" Height="30px" TextMode="MultiLine" Visible="false" ReadOnly="true"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">其他說明：</td>
                    <td>
                        <asp:TextBox ID="TB_其他說明" runat="server" Width="500px" Height="40px" TextMode="MultiLine" ReadOnly="true"></asp:TextBox>
                    </td>
                </tr>
            </table>
            <asp:Literal ID="LT_審查" runat="server"></asp:Literal>
        </span>
        <%--<asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
    </form>
</body>
</html>
