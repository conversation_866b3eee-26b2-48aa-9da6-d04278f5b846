﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;

public partial class web_ret_employee : System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class employee
    {
        private string Commonkey;
        private string com_empno;
        private string com_cname;
        private string com_telext;
        private string com_orgcd;
        private string com_deptcd;
        private string com_deptid;
        private string com_dept_name;
        private string com_mailadd;
        private string com_orgName;
        public string c_Commonkey
        {
            get { return Commonkey; }
            set { Commonkey = value; }
        }

        public string c_com_empno
        {
            get { return com_empno; }
            set { com_empno = value; }
        }

        public string c_com_cname
        {
            get { return com_cname; }
            set { com_cname = value; }
        }
        public string c_com_telext
        {
            get { return com_telext; }
            set { com_telext = value; }
        }

        public string c_com_orgcd
        {
            get { return com_orgcd; }
            set { com_orgcd = value; }
        }
        public string c_com_deptcd
        {
            get { return com_deptcd; }
            set { com_deptcd = value; }
        }
        public string c_com_deptid
        {
            get { return com_deptid; }
            set { com_deptid = value; }
        }

        public string c_com_dept_name
        {
            get { return com_dept_name; }
            set { com_dept_name = value; }
        }
        public string c_com_mailadd
        {
            get { return com_mailadd; }
            set { com_mailadd = value; }
        }
        public string c_com_orgName
        {
            get { return com_orgName; }
            set { com_orgName = value; }
        }
    }
   
    protected void Page_Load(object sender, System.EventArgs e)
    {
        employee u = new employee();
        string str_kw = "";
        int int_danger = 0;

        if (Request.QueryString["keyword"] != null)
        {
            str_kw = Server.UrlDecode(Request.QueryString["keyword"]);
            if (Base64.danger_word_all(str_kw) == "1")
                int_danger++;  //有危險字眼        
        }
        else
        {
            if (Session["empno"] == null)
            {
                int_danger++;  //有危險字眼
            }
            else
            {
                str_kw = Session["empno"].ToString();
                Session["empno"] = null;
            }
        }

        if (int_danger >0)
        {   //有危險字眼
            u.c_com_empno = "";
            u.c_com_cname = "error0";
            u.c_com_telext = "";
            u.c_com_orgcd = "";
            u.c_com_deptcd = "";
            u.c_com_deptid = "";
            u.c_com_dept_name = "";
            u.c_com_mailadd = "";
        }
        else 
        { 
            //SqlCommand oCmd = new SqlCommand();
            //string strSQL = " select  com_empno ,com_cname,com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where (  com_empno like '%" + str_kw + "%' or  com_cname   like '%" + str_kw + "%'  )and com_depcd='N' ";
            //oCmd.CommandText = strSQL;
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_Contract"].ConnectionString);
            //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            //oda.Fill(ds);

            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"select  com_empno ,com_cname,com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where (  com_empno like @keyword or  com_cname like @keyword  )and com_depcd='N' ";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@keyword", oRCM.SQLInjectionReplaceAll('%' + str_kw + '%'));

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(ds);

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_Contract"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion
            if (ds.Tables[0].DefaultView.Count ==1)
            {
                u.c_com_empno = Server.HtmlEncode(ds.Tables[0].Rows[0][0].ToString().Trim());
                u.c_com_cname = Server.HtmlEncode(ds.Tables[0].Rows[0][1].ToString().Trim());
                u.c_com_telext = Server.HtmlEncode(ds.Tables[0].Rows[0][2].ToString().Replace("\"", "\\\""));
                u.c_com_orgcd = Server.HtmlEncode(ds.Tables[0].Rows[0][3].ToString().Trim());
                u.c_com_deptcd = Server.HtmlEncode(ds.Tables[0].Rows[0][4].ToString().Trim());
                u.c_com_deptid = Server.HtmlEncode(ds.Tables[0].Rows[0][5].ToString().Trim());
                u.c_com_dept_name = Server.HtmlEncode(ds.Tables[0].Rows[0][7].ToString().Trim().Replace("\"", "\\\""));
                u.c_com_mailadd = Server.HtmlEncode(ds.Tables[0].Rows[0][6].ToString().Trim());
                u.c_com_orgName = Server.HtmlEncode(ds.Tables[0].Rows[0]["orgName"].ToString().Replace("\"", "\\\""));

            }
            if (ds.Tables[0].DefaultView.Count == 0)
            {
                u.c_com_empno = "";
                u.c_com_cname = "error0";
                u.c_com_telext = "";
                u.c_com_orgcd = "";
                u.c_com_deptcd = "";
                u.c_com_deptid = "";
                u.c_com_dept_name = "";
                u.c_com_mailadd = "";
                u.c_com_orgName = "";
            }
            if (ds.Tables[0].DefaultView.Count > 1)
            {
                u.c_com_empno = "";
                u.c_com_cname = "error2";
                u.c_com_telext = "";
                u.c_com_orgcd = "";
                u.c_com_deptcd = "";
                u.c_com_deptid = "";
                u.c_com_dept_name = "";
                u.c_com_mailadd = "";
                u.c_com_orgName = "";
            }
        }
             
        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            if (CallBackFunction.Length > 50)
                Response.Redirect("../danger.aspx");
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        Response.Write(j);  //輸出JSONP的內容
    }


}