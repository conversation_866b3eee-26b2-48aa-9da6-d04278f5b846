﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class calendar : System.Web.UI.UserControl
{
    //boolean NowDate = true;
   
    protected void Page_Load(object sender, EventArgs e)
    {
        timeTb.Text = timeHdTb.Text;
        String s = isImgTrigger ? "1" : "0"; //TextBox不可編輯則有Icon
        String s2 = isOnlyDate ? "1" : "0";  //顯示日期與時間? 1=顯示日期 0=顯示日期與時間
        timeTb.ReadOnly = isReadOnly;
        string scriptStr = string.Format("if(typeof timePicker === 'function'){{timePicker('#{0}', '{1}', '{2}', '#{3}');}}", timeTb.ClientID, s, s2, timeHdTb.ClientID);
        Page.ClientScript.RegisterStartupScript(GetType(), "timePickerScr" + timeTb.ClientID, scriptStr, true);
         
    }

    //取得選取結果
    public String getTime()
    {
        return timeTb.Text;
    }

    public String getTbClientID()
    {
        return timeTb.ClientID;
    }


    public bool isReadOnly
    {
        get;
        set;
    }

    //TextBox是否可編輯
    public Boolean isEnable
    {
        get;
        set;      
    }

    //是否用icon開窗
    public Boolean isImgTrigger
    {
        get;
        set;      
    }

    //是否只顯示日期功能
    public Boolean isOnlyDate
    {
        get;
        set;        
    }

    public string Date
    {
        get
        {
            if (timeHdTb.Text.Trim() == "" || timeHdTb.Text.Trim() == null)
                return "";
            else
                return timeHdTb.Text.Replace("/", "");
        }

        set
        {
            timeTb.Text = value;
            timeHdTb.Text = value;
        }
    }

  
    //public bool NowDate
    //{
    //    set
    //    {
    //        nowdate = value;
    //        if (value)
    //            timeTb.Text = DateTime.Now.ToString("yyyy/MM/dd");//預設為今天日期
    //        else
    //            timeTb.Text = "";
    //    }
    //}

    public string DateID
    {
        get
        {
            return timeTb.ClientID;
        }
    }
}