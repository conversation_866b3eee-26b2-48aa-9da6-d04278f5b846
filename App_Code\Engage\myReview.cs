﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for myReview
	/// </summary>
	public class myReview : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private long _seqsn;
		private string _empno;
		private string _empname;

		private int _ver;
		private string _flow_type;

		//審查入口-查詢條件
		private string _srh_doc_type = string.Empty;
		private string _srh_status = string.Empty;
		private string _srh_examdate1 = string.Empty;
		private string _srh_examdate2 = string.Empty;
		private string _srh_signdate1 = string.Empty;
		private string _srh_signdate2 = string.Empty;
		private string _srh_examemp = string.Empty;
		private string _srh_planerempno = string.Empty;
		private string _srh_promoempno = string.Empty;
		private string _srh_execdept = string.Empty;
		private string _srh_login_id = string.Empty;

		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 洽案流水號
		/// </summary>
		public long Seqsn
		{
			get { return _seqsn; }
			set { _seqsn = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		public int Ver
		{
			get { return _ver; }
			set { _ver = value; }
		}

		/// <summary>
		/// 類別說明: 規劃構想審查, {CA:至審查畫面, CB:送審}
		/// </summary>
		public string Flow_Type
		{
			get { return _flow_type; }
			set { _flow_type = value; }
		}

		public string ecr1_id;
		public string ecr1_status;
		public string ecr1_send_examemp;
		public string ecr1_send_examname;
		public string ecr1_send_examdate;
		public string ecr1_comadjmemo;
		public string ecr1_comadjemp;
		public string ecr1_comadjname;


		public string ecr2_id;
		public string ecr2_examempno;
		public string ecr2_examname;
		public string ecr2_memo;
		public string ecr2_result;

		#region search condtion, 審查入口-查詢條件

		public string srh_doc_type
		{
			get { return _srh_doc_type; }
			set { _srh_doc_type = value; }
		}

		public string srh_status
		{
			get { return _srh_status; }
			set { _srh_status = value; }
		}

		public string srh_examdate1
		{
			get { return _srh_examdate1; }
			set { _srh_examdate1 = value; }
		}

		public string srh_examdate2
		{
			get { return _srh_examdate2; }
			set { _srh_examdate2 = value; }
		}

		public string srh_signdate1
		{
			get { return _srh_signdate1; }
			set { _srh_signdate1 = value; }
		}

		public string srh_signdate2
		{
			get { return _srh_signdate2; }
			set { _srh_signdate2 = value; }
		}

		public string srh_examemp
		{
			get { return _srh_examemp; }
			set { _srh_examemp = value; }
		}

		public string srh_planerempno
		{
			get { return _srh_planerempno; }
			set { _srh_planerempno = value; }
		}

		public string srh_promoempno
		{
			get { return _srh_promoempno; }
			set { _srh_promoempno = value; }
		}

		public string srh_execdept
		{
			get { return _srh_execdept; }
			set { _srh_execdept = value; }
		}

		public string srh_login_id
		{
			get { return _srh_login_id; }
			set { _srh_login_id = value; }
		}
		#endregion

		#endregion

		public myReview() { }

		#region 審查 - 歷史版次
		/// <summary>
		/// 審查 - 歷史版次
		/// </summary>
		/// <returns></returns>
		public DataTable GetCostReview1_History()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT ecr1_seqsn, ecr1_ver,ecr1_send_examname
		,convert(varchar, convert(datetime,ecr1_send_examdate), 111) as ecr1_send_examdate
		,ecr1_comadjmemo
  FROM engage_his..engage_cost_review1 
  WHERE ecr1_seqsn=@seqsn ORDER BY ecr1_ver 
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region Cost_Review1
		public DataTable GetCostReview1()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
IF @ver=0
BEGIN
	SELECT *,
		(SELECT CASE WHEN eb_pc_flag = '2' THEN N'規劃構想/成本訂價' ELSE N'成本訂價' END FROM engage_base WHERE eb_seqsn=@seqsn) as doc_name
	  FROM engage_cost_review1 
	  WHERE ecr1_seqsn=@seqsn
END
ELSE
BEGIN
	SELECT *,
		(SELECT CASE WHEN eb_pc_flag = '2' THEN N'規劃構想/成本訂價' ELSE N'成本訂價' END FROM engage_base WHERE eb_seqsn=@seqsn) as doc_name
	  FROM v_engage_cost_review1_all 
	  WHERE ecr1_seqsn=@seqsn AND ecr1_ver=@ver
END 
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 成本定價-送審,新增
		/// </summary>
		/// <returns></returns>
		public bool Insert_costReview1()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	UPDATE engage_cost_review1 SET
		ecr1_send_examemp=@send_examemp,ecr1_send_examname=@send_examname,
		ecr1_send_examdate=convert(varchar, getdate(), 112)
	WHERE ecr1_seqsn=@seqsn AND ecr1_status=0
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@send_examemp", ecr1_send_examemp);
			oCmd.Parameters.AddWithValue("@send_examname", ecr1_send_examname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool Update_costReview1()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
UPDATE engage_cost_review1 SET 
	ecr1_status='2',
	ecr1_comadjmemo=@comadjmemo,
	ecr1_comadjemp=@comadjemp,
	ecr1_comadjname=@comadjname,
	ecr1_comadjdate=convert(varchar, getdate(), 112)
  WHERE ecr1_seqsn=@seqsn

UPDATE engage_base SET eb_execstatus='J5' WHERE eb_seqsn=@seqsn AND eb_execstatus<='J5'
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@comadjmemo", ecr1_comadjmemo);
			oCmd.Parameters.AddWithValue("@comadjemp", ecr1_comadjemp);
			oCmd.Parameters.AddWithValue("@comadjname", ecr1_comadjname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region Cost_Review2

		public DataTable GetCostReview2_All()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
IF @ver=0
BEGIN
	SELECT ecr2_id, ecr2_ver, ecr2_examempno, ecr2_examname, ecr2_memo, CONVERT(varchar, convert(datetime, case when ecr2_signdate='' then null else ecr2_signdate end), 111) as ecr2_signdate
			,ecr2_result ,case ecr2_result when '0' then '同意' when '1' then '不同意' else '' end as ecr2_result_desc 
	FROM engage_cost_review2 WHERE ecr2_seqsn=@seqsn
	order by ecr2_examempno
END
ELSE
BEGIN
	SELECT ecr2_id, ecr2_ver, ecr2_examname, ecr2_memo, CONVERT(varchar, convert(datetime, case when ecr2_signdate='' then null else ecr2_signdate end), 111) as ecr2_signdate
		,case ecr2_result when '0' then '同意' when '1' then '不同意' else '' end as ecr2_result_desc 
	FROM v_engage_cost_review2_all WHERE ecr2_seqsn=@seqsn AND ecr2_ver=@ver
	order by ecr2_examempno
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 成本定價審查意見,新增、修改
		/// </summary>
		/// <returns></returns>
		public bool Insert_costReview2()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if not exists(select * from engage_cost_review2 where ecr2_id = @ecr2_id)
begin
	declare @ver int
	select @ver=ecr1_ver from engage_cost_review1 where ecr1_seqsn=@seqsn

	insert into engage_cost_review2 
		(ecr2_seqsn, ecr2_ver, ecr2_examempno, ecr2_examname)
	  select 
		@seqsn, @ver, @ecr2_examempno, @ecr2_examname
end
else
begin
	update engage_cost_review2 set
		ecr2_examempno=@ecr2_examempno,
		ecr2_examname=@ecr2_examname
	  where ecr2_id = @ecr2_id
end

update engage_cost set
	cost_moddate=CONVERT(VARCHAR(8), GETDATE(), 112)
	,cost_modempno=@empno
	,cost_modname=@empname
  where cost_seqsn=@seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ecr2_id", ecr2_id);
			oCmd.Parameters.AddWithValue("@ecr2_examempno", ecr2_examempno);
			oCmd.Parameters.AddWithValue("@ecr2_examname", ecr2_examname);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		/// <summary>
		/// 成本定價審查意見刪除
		/// </summary>
		/// <returns></returns>
		public bool Delete_costReview2()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_cost_review2 WHERE ecr2_id= @ecr2_id
";
			oCmd.Parameters.AddWithValue("@ecr2_id", ecr2_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public bool UpdateCostAuditDetail()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
Update engage_cost_review2 SET 
	ecr2_memo=@memo, ecr2_signdate=convert(varchar,getdate(),112), ecr2_result=@result 
  WHERE ecr2_seqsn=@seqsn AND ecr2_examempno=@examempno
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@examempno", ecr2_examempno);
			oCmd.Parameters.AddWithValue("@result", ecr2_result);
			oCmd.Parameters.AddWithValue("@memo", ecr2_memo);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 成本估算審查, 狀態更新. StatusUpdateReview()
		/// <summary>
		/// 成本估算審查時共同呼叫,帶入參數(1.類別,2.seqno,);0:成功/ 1:失敗,傳回訊息 @msg
		/// </summary>
		/// <returns></returns>
		public string StatusUpdateReview()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_status_update_review";

			oCmd.Parameters.AddWithValue("@flow_type", _flow_type);
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			msg.Direction = ParameterDirection.Output;

			this.Execute(oCmd, CommandType.StoredProcedure);

			return msg.Value.ToString();
		}
		#endregion

		#region EngageReviewSendmail()
		/// <summary>
		/// 提供規劃構想及成本訂價審查時,呼叫 email
		/// </summary>
		/// <returns></returns>
		public bool EngageReviewSendmail(string type)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @ver int
select @ver=ecr1_ver from engage_cost_review1 where ecr1_seqsn=@seqsn
exec [pr_engagae_review_sendmail] @flow_type, @seqsn, @ver 
";

			oCmd.Parameters.AddWithValue("@flow_type", type);
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 是否還有審查人尚未完成審查
		/// <summary>
		/// 是否還有審查人尚未完成審查
		/// </summary>
		/// <param name="type"></param>
		/// <returns></returns>
		public bool IsCostReviewersNotFinish()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select count(*) from engage_cost_review1
join engage_cost_review2 on ecr1_seqsn=ecr2_seqsn and isnull(ecr2_signdate,'')='' 
where ecr1_seqsn=@seqsn and isnull(ecr1_comadjdate,'')=''
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				string retValue = this.getTopOne(oCmd, CommandType.Text);
				success = (retValue != "0");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion


		#region 取得審查入口, ReviewPortal

		/// <summary>
		/// 取得審查入口, ReviewPortal
		/// </summary>
		/// <returns></returns>
		public DataTable GetEngageReviewPortal()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_review_portal";

			oCmd.Parameters.AddWithValue("@doc_type", "C");
			oCmd.Parameters.AddWithValue("@status", _srh_status);
			oCmd.Parameters.AddWithValue("@examdate1", _srh_examdate1);
			oCmd.Parameters.AddWithValue("@examdate2", _srh_examdate2);
			oCmd.Parameters.AddWithValue("@signdate1", _srh_signdate1);
			oCmd.Parameters.AddWithValue("@signdate2", _srh_signdate2);
			oCmd.Parameters.AddWithValue("@examemp", _srh_examemp);
			oCmd.Parameters.AddWithValue("@planerempno", _srh_planerempno);
			oCmd.Parameters.AddWithValue("@promoempno", _srh_promoempno);
			oCmd.Parameters.AddWithValue("@execdept", _srh_execdept);
			oCmd.Parameters.AddWithValue("@login_id", _srh_login_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		#endregion

	}
}