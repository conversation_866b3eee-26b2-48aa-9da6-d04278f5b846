/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[17],{395:function(ia,y,e){function fa(){return!1}function x(e,f,h){if(!(f in aa))return!0;f=aa[f];for(var n=0;n<f.length;n++){var r=e;var w=f[n];var x=h;if(w.name in r){var y="",z=!1;r=r[w.name];switch(w.type){case "s":y="String";z=Object(ea.isString)(r);break;case "a":y="Array";z=Object(ea.isArray)(r);break;case "n":y="Number";z=Object(ea.isNumber)(r)&&Object(ea.isFinite)(r);break;case "o":y="Object",z=Object(ea.isObject)(r)&&!Object(ea.isArray)(r)}z||
x.reject('Expected response field "'+w.name+'" to have type '+y);w=z}else x.reject('Response missing field "'+w.name+'"'),w=!1;if(!w)return!1}return!0}e.r(y);var ha=e(1),ea=e(0);e.n(ea);var da=e(2);ia=e(48);var ba=e(32),w=e(414),z=e(84),r=e(316),h=e(38),f=e(142),n=function(){function e(){this.request=this.result=null;this.state=0;var e=this;e.promise=new Promise(function(f,h){e.resolve=function(){if(0===e.state||4===e.state)e.state=1,e.result=arguments[0],f.apply(null,arguments)};e.reject=function(){if(0===
e.state||4===e.state)e.state=2,h.apply(null,arguments)}})}e.prototype.ar=function(){return 1===(this.state&1)};e.prototype.Z7=function(){return 2===(this.state&2)};e.prototype.Wh=function(){return!this.Z7()&&!this.ar()};e.prototype.z7=function(){return 4===(this.state&4)};e.prototype.TH=function(){this.state|=4};return e}(),ca=function(){function e(){this.Oq={};this.yb=[]}e.prototype.pop=function(){var e=this.yb.pop();this.Oq[e.key]=void 0;return e};e.prototype.push=function(e,f){f={key:e,data:f};
this.yb.push(f);this.Oq[e]=f.data};e.prototype.contains=function(e){return!!this.Oq[e]};e.prototype.get=function(e){return this.Oq[e]};e.prototype.set=function(e,f){var h=this;this.Oq[e]=f;this.yb.forEach(function(f,n){f.key===e&&(h.yb[n]=f)})};e.prototype.remove=function(e){var f=this;this.Oq[e]=void 0;this.yb.forEach(function(h,n){h.key===e&&f.yb.splice(n,1)})};e.prototype.length=function(){return this.yb.length};return e}(),aa={pages:[{name:"pages",type:"a"}],pdf:[{name:"url",type:"s"}],docmod:[{name:"url",
type:"s"},{name:"rID",type:"s"}],health:[],tiles:[{name:"z",type:"n"},{name:"rID",type:"n"},{name:"tiles",type:"a"},{name:"size",type:"n"}],cAnnots:[{name:"annots",type:"a"}],annots:[{name:"url",type:"s"},{name:"name",type:"s"}],image:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],text:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],ApString2Xod:[{name:"url",type:"s"},{name:"rID",type:"s"}]};e=function(){function e(e,f,r){var x=this;this.tI=this.fN=!1;this.ug=
this.UB=this.hs=this.AG=this.JD=this.dl=null;this.Nk=new n;this.In=new n;this.wy=!1;this.We=this.ae=this.be=this.Qe=null;this.lf=[];this.bz=[];this.cache={};this.timeStamp=0;this.pg=[];this.fi=[];this.DE=null;this.SM=!1;this.ZS=this.id=null;this.oG=this.mP=fa;this.gh=0;this.EF=!1;this.GQ=1;this.ai={};this.Kp=0;this.Dr=new ca;f.endsWith("/")||(f+="/");r=r||{};this.fN=r.disableWebsockets||!1;this.tI=r.singleServerMode||!1;null!=r.customQueryParameters&&Object(h.b)("wvsQueryParameters",r.customQueryParameters);
f.endsWith("blackbox/")||(f+="blackbox/");this.dl=r.uploadData||null;this.hs=r.uriData||null;this.JD=r.cacheKey||null;this.AG=r.officeOptions||null;this.ue=f;this.kE=e;this.Im(!0);this.Zp=(new w.a(f,null,this.Tg())).W3(!this.fN,function(e){x.p9(e)},function(){return null},function(){x.wy=!1},function(){x.nba()})}e.prototype.a1=function(){var e=this;return new Promise(function(f,h){var n=new XMLHttpRequest;n.open("GET",e.ue+"ck");n.withCredentials=e.Tg();n.onreadystatechange=function(){n.readyState===
XMLHttpRequest.DONE&&(200===n.status?f():h())};n.send()})};e.prototype.tca=function(e){this.mP=e||fa;this.oG=fa};e.prototype.b0=function(){this.ES();return this.Zp.Rn(!0)};e.prototype.ES=function(){Object(ha.b)(this,void 0,void 0,function(){return Object(ha.d)(this,function(e){switch(e.label){case 0:return this.In=new n,this.Nk=new n,this.wy=!1,this.id=null,this.SM=!1,[4,this.a1()];case 1:return e.ea(),[2]}})})};e.prototype.nba=function(){this.mP();this.ES();this.Qe&&(this.Qe.Wh()?this.zg(this.Qe.request):
this.Qe.ar()&&this.oG(this.Qe.result.url,"pdf")&&(this.Qe=null,this.DS()));this.We&&this.We.Wh()&&this.zg(this.We.request);this.be&&this.be.Wh()?this.zg(this.be.request):this.ae&&this.ae.Wh()&&this.aP();var e;for(e=0;e<this.pg.length;e++)this.pg[e]&&(this.pg[e].Wh()?this.zg(this.pg[e].request):this.pg[e].ar()&&this.oG(this.pg[e].result.url,"image")&&(this.pg[e]=null,this.cB(Object(ea.uniqueId)(),e)));for(e=0;e<this.fi.length;e++)this.fi[e]&&this.fi[e].Wh()&&!this.fi[e].z7()&&this.zg(this.fi[e].request);
for(e=0;e<this.lf.length;e++)this.lf[e]&&this.lf[e].Wh()&&this.zg(this.lf[e].request)};e.prototype.i7=function(){return this.wy?Promise.resolve():(this.wy=!0,this.timeStamp=Date.now(),this.Zp.Qz())};e.prototype.nea=function(){var e=this,h,n,r,w,x;return new Promise(function(y,z){if(e.dl)h=new FormData,h.append("file",e.dl.fileHandle,e.dl.fileHandle.name),n=e.dl.loadCallback,w="upload",r=e.dl.extension;else if(e.hs)h={uri:e.hs.uri,lla:e.hs.shareId},h=Object.keys(h).map(function(e){return e+"="+(h[e]?
encodeURIComponent(h[e]):"")}).join("&"),x="application/x-www-form-urlencoded; charset=UTF-8",n=e.hs.loadCallback,w="url",r=e.hs.extension;else{y();return}var aa=new XMLHttpRequest,ca=Object(ba.h)(e.ue,"AuxUpload");ca=Object(f.a)(ca,{type:w,ext:r});aa.open("POST",ca);aa.withCredentials=e.Tg();x&&aa.setRequestHeader("Content-Type",x);aa.addEventListener("load",function(){if(aa.readyState===aa.DONE&&200===aa.status){var f=JSON.parse(aa.response);e.kE=f.uri;n(f);y(f)}});aa.addEventListener("error",function(){z(aa.statusText+
" "+JSON.stringify(aa))});e.dl&&null!=e.dl.onProgress&&(aa.upload.onprogress=function(f){e.dl.onProgress(f)});aa.send(h)})};e.prototype.dba=function(e){this.password=e||null;this.Nk.ar()||(this.Nk=new n,this.zg({t:"pages"}));return this.Nk.promise};e.prototype.Dv=function(e){this.DE=e||null;this.Nk.ar()||this.zg({t:"pages"});return this.Nk.promise};e.prototype.Et=function(e){e=Object.assign(e,{uri:encodeURIComponent(this.kE)});this.DE&&(e.ext=this.DE);this.ug&&(e.c=this.ug);this.password&&(e.pswd=
this.password);this.JD&&(e.cacheKey=this.JD);this.AG&&(e.officeOptions=this.AG);return e};e.prototype.Lba=function(){0<this.Dr.length()&&10>=this.Kp&&this.Mba(this.Dr.pop().data)};e.prototype.E_=function(e){0<this.Dr.length()&&this.Dr.contains(e)&&this.Dr.remove(e)};e.prototype.zg=function(e){e=this.Et(e);this.Zp.send(e)};e.prototype.US=function(e,f){10<this.Kp?this.Dr.push(e,f):(this.Kp++,e=this.Et(f),this.Zp.send(e))};e.prototype.Mba=function(e){this.Kp++;e=this.Et(e);this.Zp.send(e)};e.prototype.dk=
function(e){return e};e.prototype.lP=function(e){this.tI&&e?Object(da.j)("Server failed health check. Single server mode ignoring check."):!this.mha&&e&&3>=this.gh?(this.EF=!0,this.Zp.Rn()):3<this.gh&&(this.tI=!0)};e.prototype.p9=function(e){var r=this,w=e.data,y=e.err,aa=e.t;switch(aa){case "upload":y?this.oea.reject(y):this.oea.resolve("Success");break;case "pages":y?this.Nk.reject(y):x(w,aa,this.Nk)&&this.Nk.resolve(w);break;case "config":if(y)this.In.reject(y);else if(x(w,aa,this.In)){this.lP(w.unhealthy);
w.id&&(this.id=w.id);if(w.auth){var ba=Object(h.a)("wvsQueryParameters");ba.auth=w.auth;Object(h.b)("wvsQueryParameters",ba)}w.serverVersion&&(this.UB=w.serverVersion,Object(da.h)("[WebViewer Server] server version: "+this.UB));w.serverID?(this.gh=w.serverID===this.ZS&&this.EF?this.gh+1:0,this.ZS=w.serverID):this.gh=0;this.EF=!1;this.In.resolve(w)}break;case "health":y?this.In.reject(y):x(w,aa,this.In)&&this.lP(w.unhealthy);break;case "pdf":w.url=Object(f.a)(this.ue+"../"+encodeURI(w.url));y?this.Qe.reject(y):
x(w,aa,this.Qe)&&this.Qe.resolve(w);break;case "ApString2Xod":w.url=Object(f.a)(this.ue+"../data/"+encodeURI(w.url));y?this.ai[w.rID].reject(y):x(w,aa,this.ai[w.rID])&&this.ai[w.rID].resolve(w);break;case "docmod":w.url=Object(f.a)(this.ue+"../"+encodeURI(w.url));y?this.ai[w.rID].reject(y):x(w,aa,this.Qe)&&this.ai[w.rID].resolve(w);break;case "xod":if(y)this.be&&this.be.Wh()&&this.be.reject(y),this.ae&&this.ae.Wh()&&this.ae.reject(y);else if(w.notFound)w.noCreate||this.be&&this.be.Wh()&&this.be.resolve(w),
this.ae&&this.ae.Wh()&&this.ae.resolve(w);else{w.url&&(w.url=Object(f.a)(this.ue+"../"+encodeURI(w.url)));if(!this.ae||this.ae.ar())this.ae=new n,this.ae.request={t:"xod",noCreate:!0};this.be||(this.be=new n,this.be.request={t:"xod"});this.ae.resolve(w);this.be.resolve(w)}break;case "cAnnots":ba=this.We;if(y)ba.reject(y);else if(x(w,aa,ba)){ba.TH();var ca=[],fa=w.annots;w=function(e){var h=fa[e].s,n=fa[e].e,w=ha.ue+"../"+encodeURI(fa[e].xfdf),x="true"===fa[e].hasAppearance?Object(f.a)(w+".xodapp"):
null,y=Object(ea.range)(h,n+1);ca[e]={range:y,promise:new Promise(function(e,h){var n=new XMLHttpRequest;n.open("GET",Object(f.a)(w));n.responseType="text";n.withCredentials=r.Tg();n.addEventListener("load",function(){n.readyState===n.DONE&&200===n.status&&e({jp:n.response,Wj:x,range:y})});n.addEventListener("error",function(){h(n.statusText+" "+JSON.stringify(n))});n.send()})}};var ha=this;for(y=0;y<fa.length;y++)w(y);ba.resolve(ca)}break;case "annots":if(y)this.We.reject(y);else if(x(w,aa,this.We)){this.We.TH();
var ia=new XMLHttpRequest;ba=this.ue+"../"+encodeURI(w.url);var ja=w.hasAppearance?Object(f.a)(ba+".xodapp"):null;ia.open("GET",Object(f.a)(ba));ia.responseType="text";ia.withCredentials=this.Tg();ia.addEventListener("load",function(){ia.readyState===ia.DONE&&200===ia.status&&r.We.resolve({jp:ia.response,Wj:ja})});ia.addEventListener("error",function(){r.We.reject(ia.statusText+" "+JSON.stringify(ia))});ia.send()}break;case "image":this.Kp--;var ka=this.pg[w.p];y?ka.promise.reject(y):x(w,aa,ka)&&
(ka.result=w,ka.result.url=Object(f.a)(this.ue+"../"+encodeURI(ka.result.url)),ka.resolve(ka.result));break;case "tiles":this.Kp--;ka=w.rID;ba=this.lf[ka];this.lf[ka]=null;this.bz.push(ka);if(y)ba.reject(y);else if(x(w,aa,ba)){for(y=0;y<w.tiles.length;y++)w.tiles[y]=Object(f.a)(this.ue+"../"+encodeURI(w.tiles[y]));ba.resolve(w)}break;case "text":ka=this.fi[w.p];if(y)ka.reject(y);else if(x(w,aa,ka)){ka.TH();var Fa=new XMLHttpRequest;w=Object(f.a)(this.ue+"../"+encodeURI(w.url));Fa.open("GET",w);Fa.withCredentials=
this.Tg();Fa.addEventListener("load",function(){Fa.readyState===Fa.DONE&&200===Fa.status&&(ka.result=JSON.parse(Fa.response),ka.resolve(ka.result))});Fa.addEventListener("error",function(e){ka.reject(Fa.statusText+" "+JSON.stringify(e))});Fa.send()}break;case "progress":"loading"===w.t&&this.trigger(z.a.Events.DOCUMENT_LOADING_PROGRESS,[w.bytes,w.total])}this.Lba();!aa&&e.echo&&e&&"apstring2xod"===e.echo.t&&(e=e.echo.reqID)&&(2<=parseInt(this.UB,10)?this.ai[e].reject("Message unhandled by server"):
this.ai[e].reject())};e.prototype.F4=function(){return Object(ha.b)(this,void 0,void 0,function(){return Object(ha.d)(this,function(e){switch(e.label){case 0:return[4,this.i7()];case 1:return e.ea(),[2,this.In.promise]}})})};e.prototype.k4=function(e){for(var h=this,r=new XMLHttpRequest,w=Object(f.a)(this.ue+"aul",{id:this.id}),x=new FormData,y={},z=0;z<e.body.length;z++){var aa=e.body[z];y[aa.id]=aa.yD.w+";"+aa.yD.h;x.append(aa.id,aa.yD.dataString)}e={t:"apstring2xod",reqID:this.GQ++,parts:y};var ba=
this.Et(e);x.append("msg",JSON.stringify(ba));this.ai[ba.reqID]=new n;r.open("POST",w);r.withCredentials=this.Tg;w=new Promise(function(e,f){r.onreadystatechange=function(){4===r.readyState&&(200===r.status?e():f("An error occurred while sending down appearance strings to the server"))}});r.send(x);return w.then(function(){return h.ai[ba.reqID].promise})};e.prototype.d0=function(){var e=this.UB.split("-")[0].split("."),f=["1","5","9"];if(3!==e.length)throw Error("Invalid WVS version length.");if(3!==
f.length)throw Error("Invalid version length.");for(var h=0;h<e.length;++h){if(f.length===h||e[h]>f[h])return-1;if(e[h]!==f[h])return 1}return 0};e.prototype.Fn=function(){return 0>=this.d0()};e.prototype.NE=function(){this.We||(this.We=new n,this.Fn()?this.We.request={t:"cAnnots"}:this.We.request={t:"annots"},this.zg(this.We.request));return this.We.promise};e.prototype.cB=function(e,f){this.pg[f]||(this.pg[f]=new n,this.pg[f].request={t:"image",p:f},this.US(e,this.pg[f].request));return this.pg[f].promise};
e.prototype.eba=function(e){this.fi[e]||(this.fi[e]=new n,this.fi[e].request={t:"text",p:e},this.zg(this.fi[e].request));return this.fi[e].promise};e.prototype.fba=function(e,f,h,r,w){var x=this.lf.length;this.bz.length&&(x=this.bz.pop());this.lf[x]=new n;this.lf[x].request={t:"tiles",p:f,z:h,r:r,size:w,rID:x};this.US(e,this.lf[x].request);return this.lf[x].promise};e.prototype.DS=function(){this.Qe||(this.Qe=new n,this.Qe.request={t:"pdf"},this.SM?this.Qe.resolve({url:this.kE}):this.zg(this.Qe.request));
return this.Qe.promise};e.prototype.DO=function(e){var h=this,r=new XMLHttpRequest,w=Object(f.a)(this.ue+"aul",{id:this.id}),x=new FormData,y={};e.annots&&(y.annots="xfdf");e.watermark&&(y.watermark="png");e.redactions&&(y.redactions="redact");y={t:"docmod",reqID:this.GQ++,parts:y};e.print&&(y.print=!0);var z=this.Et(y);x.append("msg",JSON.stringify(z));return Promise.all([e.annots,e.watermark,e.redactions].map(function(e){return Promise.resolve(e)})).then(function(e){var f=e[0],y=e[1];e=e[2];f&&
x.append("annots",f);y&&x.append("watermark",y);e&&x.append("redactions",e);h.ai[z.reqID]=new n;r.open("POST",w);r.withCredentials=h.Tg;f=new Promise(function(e,f){r.onreadystatechange=function(){4===r.readyState&&(200===r.status?e():f("An error occurred while sending down annotation data to the server"))}});r.send(x);return f.then(function(){return h.ai[z.reqID].promise})})};e.prototype.aP=function(){this.ae||(this.ae=new n,this.ae.request={t:"xod",noCreate:!0},this.zg(this.ae.request));return this.ae.promise};
e.prototype.gba=function(){this.be||(this.be=new n,this.be.request={t:"xod"},this.zg(this.be.request));return this.be.promise};e.prototype.Em=function(){return!0};e.prototype.request=function(){};e.prototype.YR=function(){};e.prototype.abort=function(){for(var e=0;e<this.lf.length;e++)this.lf[e]&&(this.lf[e].resolve(null),this.lf[e]=null,this.bz.push(e));this.close()};e.prototype.mB=function(e){this.ug=this.ug||{};this.ug.headers=e};e.prototype.Im=function(e){this.ug=this.ug||{};this.ug.internal=
this.ug.internal||{};this.ug.internal.withCredentials=e};e.prototype.Tg=function(){return this.ug&&this.ug.internal?this.ug.internal.withCredentials:null};e.prototype.getFileData=function(){return Promise.reject()};return e}();Object(ia.a)(e);Object(r.a)(e);Object(r.b)(e);y["default"]=e},414:function(ia,y,e){var fa=e(1),x=e(2),ha=e(32),ea=e(38),da=e(142),ba=e(103),w=function(){function e(e,f,n,r,w,x){void 0===n&&(n=null);void 0===r&&(r=null);void 0===w&&(w=null);void 0===x&&(x=null);this.oQ=!1;this.gh=
0;this.eM=this.Fea(e);this.url=f?this.eM+"/"+f:this.eM+"/ws";this.gE=n;this.gv=r;this.Ct=w;this.BH=x}e.prototype.Fea=function(e){var f=e.indexOf("://"),h="ws://";0>f?f=0:(5===f&&(h="wss://"),f+=3);var r=e.lastIndexOf("/");0>r&&(r=e.length);return h+e.slice(f,r)};e.prototype.send=function(e){this.Sm.readyState===WebSocket.CLOSED||this.oQ||this.Sm.send(JSON.stringify(e))};e.prototype.Qz=function(){return Object(fa.b)(this,void 0,void 0,function(){var e,f=this;return Object(fa.d)(this,function(){e=Object(ea.a)("wvsQueryParameters");
e.bcid=Object(ha.i)(8);Object(ea.b)("wvsQueryParameters",e);return[2,new Promise(function(e,h){var n=Object(da.a)(f.url);f.Sm=new WebSocket(n);f.Sm.onopen=function(){f.gv&&f.gv();e()};f.Sm.onerror=function(e){f.oQ=!0;h(e)};f.Sm.onclose=function(e){var n=e.code;return Object(fa.b)(f,void 0,void 0,function(){var e=this;return Object(fa.d)(this,function(f){switch(f.label){case 0:return this.Ct&&this.Ct(),3E3===n?[3,3]:8>this.gh++?[4,new Promise(function(f){return setTimeout(function(){return Object(fa.b)(e,
void 0,void 0,function(){return Object(fa.d)(this,function(e){switch(e.label){case 0:return this.BH(),[4,this.Qz()];case 1:return e.ea(),f(),[2]}})})},3E3)})]:[3,2];case 1:return f.ea(),[3,3];case 2:h(ba.a),f.label=3;case 3:return[2]}})})};f.Sm.onmessage=function(e){e&&e.data&&(e=JSON.parse(e.data),e.hb?f.send({hb:!0}):e.end?close():f.gE(e))}})]})})};e.prototype.Rn=function(e){void 0===e&&(e=!1);this.gh=0;e?this.Sm.close(3E3):this.Sm.close();return Promise.resolve()};return e}(),z=function(){function e(e,
f,n,r,w,x,y){void 0===r&&(r=null);void 0===w&&(w=null);void 0===x&&(x=null);void 0===y&&(y=null);this.gh=this.bB=this.id=0;this.wu=!1;this.request=null;e=this.I$(e);this.url=f?e+"/"+f+"pf":e+"/pf";this.OB=n;this.gE=r;this.gv=w;this.Ct=x;this.BH=y}e.prototype.I$=function(e){var f=e.lastIndexOf("/");0>f&&(f=e.length);return e.slice(0,f)};e.prototype.O0=function(e){e=e.split("\n");for(e[e.length-1]&&e.pop();0<e.length&&3>e[e.length-1].length;)"]"===e.pop()&&(this.id=0);0<e.length&&3>e[0].length&&e.shift();
for(var f=0;f<e.length;++f)e[f].endsWith(",")&&(e[f]=e[f].substr(0,e[f].length-1));return e};e.prototype.MS=function(){return Object(fa.b)(this,void 0,void 0,function(){var e=this;return Object(fa.d)(this,function(f){switch(f.label){case 0:return 8>this.gh++?[4,new Promise(function(f){return setTimeout(function(){e.BH();e.Qz();f()},3E3)})]:[3,2];case 1:f.ea(),f.label=2;case 2:return[2]}})})};e.prototype.K$=function(e){Object(fa.b)(this,void 0,void 0,function(){var f,h;return Object(fa.d)(this,function(n){switch(n.label){case 0:f=
null,h=0,n.label=1;case 1:if(!(h<e.length))return[3,6];f=JSON.parse(e[h]);if(!f)return[3,5];if(!f.end)return[3,2];close();return[3,5];case 2:if(!f.id||Number(f.id)===this.id)return[3,4];Object(x.j)("Reconnecting, new server detected");this.Rn();return[4,this.MS()];case 3:return n.ea(),[3,5];case 4:f.hb&&Number(f.id)===this.id?this.send({hb:!0}):this.wu||this.gE(f),n.label=5;case 5:return++h,[3,1];case 6:return[2]}})})};e.prototype.m9=function(e){Object(fa.b)(this,void 0,void 0,function(){var f,h,
r;return Object(fa.d)(this,function(n){switch(n.label){case 0:if(!(3<=e.readyState))return[3,2];try{f=e.responseText.length}catch(ja){return Object(x.h)("caught exception"),[2]}if(0<f)try{h=this.O0(e.responseText),0===this.id&&0<h.length&&(r=JSON.parse(h.shift()),this.id=r.id,this.gh=0),this.K$(h)}catch(ja){}return this.wu?[3,2]:[4,this.NN()];case 1:n.ea(),n.label=2;case 2:return[2]}})})};e.prototype.NN=function(){return Object(fa.b)(this,void 0,void 0,function(){var e=this;return Object(fa.d)(this,
function(){return[2,new Promise(function(f,h){function n(){return Object(fa.b)(e,void 0,void 0,function(){return Object(fa.d)(this,function(e){switch(e.label){case 0:h(),this.Rn(),e.label=1;case 1:return this.wu&&8>this.gh?[4,this.MS()]:[3,3];case 2:return e.ea(),[3,1];case 3:return[2]}})})}e.request=new XMLHttpRequest;e.request.withCredentials=e.OB;var r=Object(da.a)(e.url,0!==e.id?{id:String(e.id),uc:String(e.bB)}:{uc:String(e.bB)});e.bB++;e.request.open("GET",r,!0);e.request.setRequestHeader("Cache-Control",
"no-cache");e.request.setRequestHeader("X-Requested-With","XMLHttpRequest");e.request.onreadystatechange=function(){e.m9(e.request)};e.request.addEventListener("error",n);e.request.addEventListener("timeout",n);e.request.addEventListener("load",function(){e.gv&&e.gv();f()});e.request.send()})]})})};e.prototype.Qz=function(){var e=Object(ea.a)("wvsQueryParameters");e.bcid=Object(ha.i)(8);Object(ea.b)("wvsQueryParameters",e);this.bB=this.id=0;this.wu=!1;return this.NN()};e.prototype.send=function(e){var f=
this,h=new XMLHttpRequest;h.withCredentials=this.OB;var r=Object(da.a)(this.url,{id:String(this.id)}),w=new FormData;w.append("data",JSON.stringify(e));h.addEventListener("error",function(){f.Rn()});h.open("POST",r);h.setRequestHeader("X-Requested-With","XMLHttpRequest");h.send(w)};e.prototype.Rn=function(){this.id=0;this.wu=!0;this.Ct&&this.Ct();this.request.abort();return Promise.resolve()};return e}();ia=function(){function e(e,f,n){this.BM=e;this.target=f;this.OB=n}e.prototype.W3=function(e,f,
n,r,x){void 0===e&&(e=!0);void 0===f&&(f=null);void 0===n&&(n=null);void 0===r&&(r=null);void 0===x&&(x=null);return e?new w(this.BM,this.target,f,n,r,x):new z(this.BM,this.target,this.OB,f,n,r,x)};return e}();y.a=ia}}]);}).call(this || window)
