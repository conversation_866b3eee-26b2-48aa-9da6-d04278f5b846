﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

namespace Engage
{
	/// <summary>
	/// Summary description for mySign
	/// </summary>
	public class mySign : Engage.mySQLHelper
	{
		#region 私有變數

		private string _errorMessage;
		private string _returnMessage;

		private long _seqsn;
		private string _empno;
		private string _empname;

		private int _esc_ver;
		#endregion

		#region 公有屬性
		/// <summary>
		/// 錯誤訊息
		/// </summary>
		public string ErrorMessage
		{
			get { return _errorMessage; }
			set { _errorMessage = value; }
		}

		/// <summary>
		/// 執行 SP 後，回傳的訊息
		/// </summary>
		public string ReturnMessage
		{
			get { return _returnMessage; }
			set { _returnMessage = value; }
		}

		/// <summary>
		/// 洽案流水號
		/// </summary>
		public long Seqsn
		{
			get { return _seqsn; }
			set { _seqsn = value; }
		}

		/// <summary>
		/// 登入人員
		/// </summary>
		public string EmpNo
		{
			get { return _empno; }
			set { _empno = value; }
		}

		public string EmpName
		{
			get { return _empname; }
			set { _empname = value; }
		}

		/// <summary>
		/// 契約簽辦的版次
		/// </summary>
		public int Sign_ver
		{
			get { return _esc_ver; }
			set { _esc_ver = value; }
		}

		public string sign_memo_type = "";
		public string sign_memo = "";

		public engage_signcont iSign = new engage_signcont();
		public engage_signitem1 iSignItem1 = new engage_signitem1();
		public engage_signitem1_proc iSignItem1_proc = new engage_signitem1_proc();

		#region signitem1

		private string _esi1_id = string.Empty;
		private string _esi1_ver = string.Empty;
		private string _esi1_tc_seno = string.Empty;
		private string _esi1_contno = string.Empty;
		private string _esi1_contname = string.Empty;
		private string _esil_conttype = string.Empty;
		private string _esi1_contsdate = string.Empty;
		private string _esi1_contedate = string.Empty;
		private string _esi1_presigndate = string.Empty;
		private string _esi1_actualsigndate = string.Empty;
		private string _esi1_docno = string.Empty;
		private string _esi1_signstatus = string.Empty;
		private string _esi1_tsfee = "0";
		private string _esi1_tspay = "0";
		private string _esi1_tafbyfixfee = "0";
		private string _esi1_tafbyfixfee_stock = "0";
		private string _esi1_tpfbyfixfee = "0";
		private string _esi1_tpfbyfixfee_stock = "0";
		private string _esi1_pafbyfixfee = "0";
		private string _esi1_pafbyfixfee_stock = "0";
		private string _esi1_ppfbyfixfee = "0";
		private string _esi1_ppfbyfixfee_stock = "0";
		private string _esi1_tpfbynonfixfee = "0";
		private string _esi1_tpfbynonfixfee_stock = "0";
		private string _esi1_ppfbynonfixfee = "0";
		private string _esi1_ppfbynonfixfee_stock = "0";
		private string _esi1_cooperfee = "0";
		private string _esi1_agencyfee = "0";
		private string _esi1_checked = "0";
		private string _esi1_web_flag = "N";
		private string _esi1_web_date = "";

		public string esi1_id
		{
			get { return _esi1_id; }
			set { _esi1_id = value; }
		}
		public string esi1_ver
		{
			get { return _esi1_ver; }
			set { _esi1_ver = value; }
		}
		public string esi1_tc_seno
		{
			get { return _esi1_tc_seno; }
			set { _esi1_tc_seno = value; }
		}
		public string esi1_contno
		{
			get { return _esi1_contno; }
			set { _esi1_contno = value; }
		}
		public string esi1_contname
		{
			get { return _esi1_contname; }
			set { _esi1_contname = value; }
		}
		public string esil_conttype
		{
			get { return _esil_conttype; }
			set { _esil_conttype = value; }
		}
		public string esi1_contsdate
		{
			get { return _esi1_contsdate; }
			set { _esi1_contsdate = value; }
		}
		public string esi1_contedate
		{
			get { return _esi1_contedate; }
			set { _esi1_contedate = value; }
		}
		public string esi1_presigndate
		{
			get { return _esi1_presigndate; }
			set { _esi1_presigndate = value; }
		}
		public string esi1_actualsigndate
		{
			get { return _esi1_actualsigndate; }
			set { _esi1_actualsigndate = value; }
		}
		public string esi1_docno
		{
			get { return _esi1_docno; }
			set { _esi1_docno = value; }
		}
		public string esi1_signstatus
		{
			get { return _esi1_signstatus; }
			set { _esi1_signstatus = value; }
		}
		public string esi1_tsfee
		{
			get { return _esi1_tsfee; }
			set { _esi1_tsfee = value; }
		}
		public string esi1_tspay
		{
			get { return _esi1_tspay; }
			set { _esi1_tspay = value; }
		}
		public string esi1_tafbyfixfee
		{
			get { return _esi1_tafbyfixfee; }
			set { _esi1_tafbyfixfee = value; }
		}
		public string esi1_tafbyfixfee_stock
		{
			get { return _esi1_tafbyfixfee_stock; }
			set { _esi1_tafbyfixfee_stock = value; }
		}
		public string esi1_tpfbyfixfee
		{
			get { return _esi1_tpfbyfixfee; }
			set { _esi1_tpfbyfixfee = value; }
		}
		public string esi1_tpfbyfixfee_stock
		{
			get { return _esi1_tpfbyfixfee_stock; }
			set { _esi1_tpfbyfixfee_stock = value; }
		}
		public string esi1_pafbyfixfee
		{
			get { return _esi1_pafbyfixfee; }
			set { _esi1_pafbyfixfee = value; }
		}
		public string esi1_pafbyfixfee_stock
		{
			get { return _esi1_pafbyfixfee_stock; }
			set { _esi1_pafbyfixfee_stock = value; }
		}
		public string esi1_ppfbyfixfee
		{
			get { return _esi1_ppfbyfixfee; }
			set { _esi1_ppfbyfixfee = value; }
		}
		public string esi1_ppfbyfixfee_stock
		{
			get { return _esi1_ppfbyfixfee_stock; }
			set { _esi1_ppfbyfixfee_stock = value; }
		}
		public string esi1_tpfbynonfixfee
		{
			get { return _esi1_tpfbynonfixfee; }
			set { _esi1_tpfbynonfixfee = value; }
		}
		public string esi1_tpfbynonfixfee_stock
		{
			get { return _esi1_tpfbynonfixfee_stock; }
			set { _esi1_tpfbynonfixfee_stock = value; }
		}
		public string esi1_ppfbynonfixfee
		{
			get { return _esi1_ppfbynonfixfee; }
			set { _esi1_ppfbynonfixfee = value; }
		}
		public string esi1_ppfbynonfixfee_stock
		{
			get { return _esi1_ppfbynonfixfee_stock; }
			set { _esi1_ppfbynonfixfee_stock = value; }
		}
		public string esi1_cooperfee
		{
			get { return _esi1_cooperfee; }
			set { _esi1_cooperfee = value; }
		}
		public string esi1_agencyfee
		{
			get { return _esi1_agencyfee; }
			set { _esi1_agencyfee = value; }
		}
		public string esi1_checked
		{
			get { return _esi1_checked; }
			set { _esi1_checked = value; }
		}

		public string esi1_web_flag
		{
			get { return _esi1_web_flag; }
			set { _esi1_web_flag = value; }
		}

		public string esi1_web_date
		{
			get { return _esi1_web_date; }
			set { _esi1_web_date = value; }
		}

		public string esc_comefrom = "1";
		public string esi1_process = "";
		public string esi1_process_memo = "";

		public string esi1_ipb = "";
		public decimal esi1_ipbi_percent = 0;
		public decimal esi1_ipbc_percent = 0;
		public string esi1_ipb_other_desc = "";

        public string esi1_unlimit_liability = "";
        public string esi1_unlimit_liability_memo = "";


        /// <summary>
        /// 權利金一次計價說明現金
        /// </summary>
        public string esi1_memo7 = "";

		/// <summary>
		/// 權利金一次計價說明股票
		/// </summary>
		public string esi1_memo8 = "";

		public string esi1_treaty_flag = "";
		public string esi1_treaty_result = "";
		public string esi1_risk = "";
		public string esi1_exec_reason = "";
		public string esi1_tc_seno_orignal = "";
		public string esi1_cm_id = "";

		#region 契約簽辦檔之草約-「無賠償上限契約」處理與風險評估

		public string proc_id = "";
		public string proc_esi1_id = "";
		public string proc_date = "";
		public string proc_itri_empno = "";
		public string proc_itri_empname = "";
		public string proc_cust = "";
		public string proc_cust_tel = "";
		public string proc_memo = "";
		
		#endregion

		#endregion

		#region 契約簽辦-成本估算檔(授權金／權利金一次計價項目)
		public string cost4_id = "";
		public string cost4_tc_seno = "";
		public string cost4_resultfrom = "";
		public string cost4_tafbyfixfee = "";
		public string cost4_tpfbyfixfee = "";
		public string cost4_pafbyfixfee = "";
		public string cost4_ppfbyfixfee = "";
		public string cost4_cash_total = "";
		public string cost4_tafbyfixfe_stock = "";
		public string cost4_tpfbyfixfee_stock = "";
		public string cost4_pafbyfixfee_stock = "";
		public string cost4_ppfbyfixfe_stock = "";
		public string cost4_stock_total = "";
		public string cost4_stock_value_total = "";
		public decimal cost4_stock_price = 10;

		#endregion

		#region 契約簽辦-成本估算檔(授權金／權利金非一次計價項目)
		public string cost6_id = "";
		public string cost6_seqsn = "";
		public string cost6_ver = "";
		public string cost6_tc_seno = "";
		public string cost6_type = "";
		public string cost6_resultfrom = "";
		public string cost6_gatherway = "";
		public string cost6_year = "";
		public string cost6_gather = "";
		public string cost6_estimfee = "";
		public string cost6_memo2 = "";
		#endregion


		#region signitem2 契約簽辦檔之草約相關文件
		/// <summary>
		/// 契約簽辦檔之草約相關文件 - 識別號
		/// </summary>
		public string esi2_id;
		/// <summary>
		/// 契約簽辦檔之草約相關文件 - 送簽版
		/// </summary>
		public string esi2_ver;
		/// <summary>
		/// 契約簽辦檔之草約相關文件 - 文件類型, {P規劃構想、NA規劃書電子檔文件、C成本訂價}
		/// </summary>
		public string esi2_doctype;
		/// <summary>
		/// 契約簽辦檔之草約相關文件 - 文件版次
		/// </summary>
		public string esi2_docver;
		/// <summary>
		/// 規劃構想版本(ep_ver), 規劃書附檔的版本(ea_id), 成本訂價版本(cost_ver)
		/// </summary>
		public string esi2_attdocid;
		/// <summary>
		/// 是否選取
		/// </summary>
		public string esi2_checked;
		#endregion

		#region signitem4 會簽對象和部門
		/// <summary>
		/// 會簽對象和部門 - 識別號
		/// </summary>
		public string esi4_id;
		/// <summary>
		/// 會簽對象和部門 - 會簽對象類別
		/// </summary>
		public string esi4_signtype;
		/// <summary>
		/// 會簽對象和部門 - 會簽對象工號
		/// </summary>
		public string esi4_signempno;
		/// <summary>
		/// 會簽對象姓名
		/// </summary>
		public string esi4_signname;
		/// <summary>
		/// 會簽部門
		/// </summary>
		public string esi4_signdept;
		/// <summary>
		/// 簽核結果
		/// </summary>
		public string esi4_result;
		/// <summary>
		/// 簽核意見
		/// </summary>
		public string esi4_comment;
		/// <summary>
		/// 簽核日期
		/// </summary>
		public string esi4_signdate;

		#endregion

		#region signitem5 分發對象和部門
		/// <summary>
		/// 分發對象和部門 - 識別號
		/// </summary>
		public string esi5_id;
		public string esi5_sendtoempno;
		public string esi5_sendtoname;
		public string esi5_sendtodept;
		public string esi5_nodelete;
		#endregion

		public string esc_src_confirm;
		public string esc_src_modify_memo;
		public string tcs_code;
		public string tcs_other_memo;

		public string esc_mod_reason;
		public string esc_cont_make_type;

        public string esc_signempno_final;
        public string esc_signname_final;

        #endregion

        public mySign()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		/// <summary>
		/// 取得契約簽辦明細
		/// </summary>
		/// <returns></returns>
		public DataTable GetDetail()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_select_by_seqsn";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		/// <summary>
		/// 取得契約簽辦版本
		/// </summary>
		/// <returns></returns>
		public int GetVer()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SELECT esc_ver FROM engage_signcont WHERE esc_seqsn = @seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try { 
				string data = this.getTopOne(oCmd, CommandType.Text);
				int ver = 0;
				if (data != string.Empty)
				{
					ver = int.Parse(data);
				}
				return ver;
			}
			catch
			{
				return 0;
			}
			
		}

		/// <summary>
		/// 取得簽辦種類, 新簽契約／契約修訂
		/// </summary>
		/// <returns></returns>
		public string GetSignClass()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if exists (select  treaty_case.tc_amend  
			from  engage_base 
			INNER JOIN	treaty_case ON engage_base.eb_year = treaty_case.tc_year AND engage_base.eb_orgcd = treaty_case.tc_orgcd AND engage_base.eb_class = treaty_case.tc_class AND engage_base.eb_sn = treaty_case.tc_sn
			where  eb_seqsn=@seqsn and ISNULL(treaty_case.tc_amend, 0)>0)
	select '契約修訂' as SignClass
else
	select '新簽契約' as SignClass
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			string data = this.getTopOne(oCmd, CommandType.Text);
			return data;
		}

		#region 本次簽辦草約之核定權責
		/// <summary>
		/// 本次簽辦草約之核定權責, sp:[pr_engage_sign_apply_approve]
		/// </summary>
		/// <returns></returns>
		//public bool Get_SignApplyApprove()
		//{
		//	bool success = false;
		//	SqlCommand oCmd = new SqlCommand();
		//	oCmd.CommandText = @"pr_engage_sign_apply_approve";

		//	oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

		//	SqlParameter msg = oCmd.Parameters.Add("@approvelist", SqlDbType.NVarChar, 50);
		//	msg.Direction = ParameterDirection.Output;

		//	try
		//	{
		//		this.Execute(oCmd, CommandType.StoredProcedure);
		//		_returnMessage = msg.Value.ToString();
		//		success = true;
		//	}
		//	catch (Exception ex)
		//	{
		//		_errorMessage = ex.Message;
		//	}
		//	return success;
		//}

		/// <summary>
		/// 本次簽辦草約之核定權責, SP: [pr_engage_sign_sRC_DSP]
		/// </summary>
		/// <returns></returns>
		public DataTable Get_SignCont_sRC()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_sRC_DSP";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@eg_ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		/// <summary>
		/// 報院條件確認、調整報院條件說明
		/// </summary>
		/// <returns></returns>
		public bool SignCont_sRC_Modify()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
Update engage_signcont SET
		esc_src_confirm = @esc_src_confirm,
		esc_src_modify_memo = @esc_src_modify_memo,
		esc_src_modempno = @empno,
		esc_src_modname = @empname,
		esc_src_moddate = convert(varchar, getdate(), 112)
  where esc_seqsn=@seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esc_src_confirm", esc_src_confirm);
			oCmd.Parameters.AddWithValue("@esc_src_modify_memo", esc_src_modify_memo);
			oCmd.Parameters.AddWithValue("@empno", EmpNo);
			oCmd.Parameters.AddWithValue("@empname", EmpName);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 報院條件新增
		/// </summary>
		/// <returns></returns>
		public bool SignCont_sRC_Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @esc_ver int, @esc_src_ver int
select @esc_ver=esc_ver, @esc_src_ver=esc_src_ver from engage_signcont where esc_seqsn=@seqsn 

insert into engage_signcont_sRC
	(ess_seqsn, ess_ver, ess_src_ver, ess_src_val, ess_src_other_memo)
select 
	@seqsn, @esc_ver, @esc_src_ver, @src_val, @src_other_memo
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@src_val", tcs_code);
			oCmd.Parameters.AddWithValue("@src_other_memo", tcs_other_memo);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 報院條件刪除
		/// </summary>
		/// <returns></returns>
		public bool SignCont_sRC_Delete()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete from engage_signcont_sRC where ess_seqsn=@seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 契約簽辦說明 - 儲存
		/// <summary>
		/// 契約簽辦說明 - 儲存
		/// </summary>
		/// <returns></returns>
		public bool SignContSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SET XACT_ABORT ON
BEGIN TRAN

UPDATE engage_signcont SET
	esc_signcontempno=@esc_signcontempno,esc_signcontname=@esc_signcontname,esc_signcontreason=@esc_signcontreason,
	esc_moneytype=@esc_moneytype,esc_moneyrate=@esc_moneyrate,
	esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=convert(varchar,getdate(),112), 
	esc_signtype=@esc_signtype
WHERE esc_seqsn=@seqsn

--簽核方式為[紙本簽核]時 不適用一段式簽核條件,系統自動調整
if @esc_signtype='1' and exists(select eb_pc_flag from engage_base where eb_seqsn=@seqsn and eb_pc_flag='3')
BEGIN
	update engage_base set eb_pc_flag='2' where eb_seqsn=@seqsn
END

COMMIT TRAN
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esc_modempno", _empno);
			oCmd.Parameters.AddWithValue("@esc_modname", _empname);
			oCmd.Parameters.AddWithValue("@esc_signcontempno", iSign.esc_signcontempno);
			oCmd.Parameters.AddWithValue("@esc_signcontname", iSign.esc_signcontname);
			oCmd.Parameters.AddWithValue("@esc_signcontreason", iSign.esc_signcontreason);
			oCmd.Parameters.AddWithValue("@esc_moneytype", iSign.esc_moneytype);
			oCmd.Parameters.AddWithValue("@esc_moneyrate", iSign.esc_moneyrate);
			oCmd.Parameters.AddWithValue("@esc_signtype", iSign.esc_signtype);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 簽核結果追蹤
		/// <summary>
		/// 簽核結果追蹤
		/// </summary>
		/// <returns></returns>
		public bool SignOpinionSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
SET XACT_ABORT ON
BEGIN TRAN

UPDATE engage_signcont SET
	esc_signdate=@esc_signdate,esc_signresult=@esc_signresult,esc_signopinion=@esc_signopinion,
    esc_signempno_final=@esc_signempno_final,esc_signname_final=@esc_signname_final,
	esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=convert(varchar,getdate(),112)
WHERE esc_seqsn=@seqsn

--檢查進度是否「小於NE」及「簽核結果[同意簽約:01]或[有條件修改後同意:03]」時，將洽案進度更新為契約簽辦完成(NE)
UPDATE engage_base SET
	eb_execstatus='NE'
WHERE eb_seqsn=@seqsn AND @esc_signdate<>'' AND eb_execstatus<'NE' AND @esc_signresult IN ('01','03')

COMMIT TRAN
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esc_modempno", _empno);
			oCmd.Parameters.AddWithValue("@esc_modname", _empname);
			oCmd.Parameters.AddWithValue("@esc_signdate", iSign.esc_signdate);
			oCmd.Parameters.AddWithValue("@esc_signresult", iSign.esc_signresult);
			oCmd.Parameters.AddWithValue("@esc_signopinion", iSign.esc_signopinion);
            oCmd.Parameters.AddWithValue("@esc_signempno_final", esc_signempno_final);
            oCmd.Parameters.AddWithValue("@esc_signname_final", esc_signname_final);

            try
            {
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

        /// <summary>
        /// 簽核結果追蹤 - 發「簽核結果完成」EG08通知信
        /// </summary>
        /// <returns></returns>
        public bool SignOpinionSendMail()
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"pr_webmail_sign_sendemp_eg08";

            oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

            try
            {
                this.Execute(oCmd, CommandType.StoredProcedure);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
        #endregion

        #region 契約簽辦檔之草約資訊, engage_signitem1

        /// <summary>
        /// 契約簽辦檔之草約資訊
        /// </summary>
        /// <returns></returns>
        public DataTable GetSignItem1BySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @planno nvarchar(15)
SELECT @planno=eb_year+eb_orgcd+eb_class+eb_sn+'A01' from engage_base where eb_seqsn=@seqsn

DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN			
	SELECT		esc_comefrom,
				CASE esc_comefrom 
					when '1' then tc_planno + tc_ver + tc_seqsn
					when '2' then @planno
				END as esi1_contno,
				parent.*,
				(select sub1.tc_planno + sub1.tc_ver + sub1.tc_seqsn from v_engage_sign_choose_treaty_case sub1 where sub1.tc_seno=esi1_tc_seqno_orignal) as esi1_contno_orignal,
				code_conttype.esi1_conttype_name,
				isnull(esi1_total,0) AS totalmoney,
				code_valuedesc AS signstatusdesc,
				treaty.*
				,CONVERT(varchar, convert(datetime,esi1_contsdate), 111) as esi1_contsdate_slash
				,CONVERT(varchar, convert(datetime,esi1_contedate), 111) as esi1_contedate_slash
				,CONVERT(varchar, convert(datetime,esi1_presigndate), 111) as esi1_presigndate_slash
				,CONVERT(varchar, convert(datetime,esi1_actualsigndate), 111) as esi1_actualsigndate_slash
				,CONVERT(varchar, convert(datetime,esi1_web_date), 111) as esi1_web_date_slash
	FROM		engage_signitem1 parent
	INNER JOIN	engage_signcont ON esc_seqsn=esi1_seqsn
	LEFT OUTER JOIN
        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
         FROM treaty_code_table
         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	WHERE		esi1_seqsn=@seqsn
	order by esi1_contno

END
ELSE
BEGIN
	SELECT		esc_comefrom,
				CASE esc_comefrom 
					when '1' then tc_planno + tc_ver + tc_seqsn
					when '2' then @planno
				END as esi1_contno,
				parent.*,
				(select sub1.tc_planno + sub1.tc_ver + sub1.tc_seqsn from v_engage_sign_choose_treaty_case sub1 where sub1.tc_seno=esi1_tc_seqno_orignal) as esi1_contno_orignal,
				code_conttype.esi1_conttype_name,
				isnull(esi1_total,0) AS totalmoney,
				code_valuedesc AS signstatusdesc,
				treaty.*
				,CONVERT(varchar, convert(datetime,esi1_contsdate), 111) as esi1_contsdate_slash
				,CONVERT(varchar, convert(datetime,esi1_contedate), 111) as esi1_contedate_slash
				,CONVERT(varchar, convert(datetime,esi1_presigndate), 111) as esi1_presigndate_slash
				,CONVERT(varchar, convert(datetime,esi1_actualsigndate), 111) as esi1_actualsigndate_slash
				,CONVERT(varchar, convert(datetime,esi1_web_date), 111) as esi1_web_date_slash
	FROM		engage_his.dbo.engage_signitem1 parent  
	INNER JOIN	engage_his.dbo.engage_signcont ON esc_seqsn=esi1_seqsn and esc_ver=esi1_ver
	LEFT OUTER JOIN
        (SELECT code_subtype, subtype_desc AS esi1_conttype_name
         FROM treaty_code_table
         WHERE (code_type = '10')) code_conttype ON code_conttype.code_subtype=esil_conttype
	LEFT OUTER JOIN engage_codetbl ON code_type='053' and code_enabled=1 and code_value=esi1_signstatus
	LEFT OUTER JOIN v_engage_sign_choose_treaty_case treaty ON treaty.tc_seno = esi1_tc_seno AND esc_comefrom='1'
	WHERE		esi1_seqsn=@seqsn and esi1_ver=@ver
	order by esi1_contno
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 挑選草約
		/// </summary>
		/// <returns></returns>
		public DataTable GetSignItem1ChooseContBySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @planno nvarchar(15)
SELECT @planno=eb_year+eb_orgcd+eb_class+eb_sn from engage_base where eb_seqsn=@seqsn

SELECT		treaty.*,
			tc_planno + tc_ver + tc_seqsn AS esi1_contno,
			esi1_id,
			esi1_presigndate,
			esi1_checked,
			isnull(esi1_total,0) AS totalmoney
FROM v_engage_sign_choose_treaty_case treaty
LEFT OUTER JOIN engage_signitem1 parent ON esi1_seqsn = @seqsn 
			AND esi1_tc_seno = treaty.tc_seno
			AND (select esc_comefrom from engage_signcont where esc_seqsn=@seqsn)='1'
WHERE tc_planno=@planno
order by tc_planno,tc_seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool SignItem1Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_signitem1_choosecontract";

			SqlParameter[] parms = {   
									new SqlParameter("@seqsn",		_seqsn),
									new SqlParameter("@esi1_tc_seno",iSignItem1.esi1_tc_seno),
									new SqlParameter("@conttype",	iSignItem1.esil_conttype),
									new SqlParameter("@contname",	iSignItem1.esi1_contname),
									new SqlParameter("@contsdate",	iSignItem1.esi1_contsdate),
									new SqlParameter("@contedate",	iSignItem1.esi1_contedate),
									new SqlParameter("@comefrom",	iSignItem1.esi1_checked)
								};

			oCmd.Parameters.AddRange(parms);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem1Update()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_signitem1_update";

			#region parms
			SqlParameter[] parms = {   
									   new SqlParameter("@esi1_id",				esi1_id),
									   new SqlParameter("@contname",			esi1_contname),
									   new SqlParameter("@contsdate",			esi1_contsdate),
									   new SqlParameter("@contedate",			esi1_contedate),
									   new SqlParameter("@presigndate",			esi1_presigndate),
									   new SqlParameter("@tsfee",				esi1_tsfee==""?"0":esi1_tsfee),
									   new SqlParameter("@tspay",				esi1_tspay==""?"0":esi1_tspay),
									   new SqlParameter("@tafbyfixfee",			esi1_tafbyfixfee==""?"0":esi1_tafbyfixfee),
									   new SqlParameter("@tafbyfixfee_stock",	esi1_tafbyfixfee_stock==""?"0":esi1_tafbyfixfee_stock),
									   new SqlParameter("@tpfbyfixfee",			esi1_tpfbyfixfee==""?"0":esi1_tpfbyfixfee),
									   new SqlParameter("@tpfbyfixfee_stock",	esi1_tpfbyfixfee_stock==""?"0":esi1_tpfbyfixfee_stock),
									   new SqlParameter("@pafbyfixfee",			esi1_pafbyfixfee==""?"0":esi1_pafbyfixfee),
									   new SqlParameter("@pafbyfixfee_stock",	esi1_pafbyfixfee_stock==""?"0":esi1_pafbyfixfee_stock),
									   new SqlParameter("@ppfbyfixfee",			esi1_ppfbyfixfee==""?"0":esi1_ppfbyfixfee),
									   new SqlParameter("@ppfbyfixfee_stock",	esi1_ppfbyfixfee_stock==""?"0":esi1_ppfbyfixfee_stock),
									   new SqlParameter("@tpfbynonfixfee",		esi1_tpfbynonfixfee==""?"0":esi1_tpfbynonfixfee),
									   new SqlParameter("@tpfbynonfixfee_stock",esi1_tpfbynonfixfee_stock==""?"0":esi1_tpfbynonfixfee_stock),
									   new SqlParameter("@ppfbynonfixfee",		esi1_ppfbynonfixfee==""?"0":esi1_ppfbynonfixfee),
									   new SqlParameter("@ppfbynonfixfee_stock",esi1_ppfbynonfixfee_stock==""?"0":esi1_ppfbynonfixfee_stock),
									   new SqlParameter("@cooperfee",			esi1_cooperfee==""?"0":esi1_cooperfee),
									   new SqlParameter("@agencyfee",			esi1_agencyfee==""?"0":esi1_agencyfee),
									   new SqlParameter("@process",				esi1_process),
									   new SqlParameter("@process_memo",		esi1_process_memo),
									   new SqlParameter("@web_flag",		    esi1_web_flag),
									   new SqlParameter("@web_date",		    esi1_web_date),
									   new SqlParameter("@ipb",					esi1_ipb),
									   new SqlParameter("@ipbi_percent",		esi1_ipbi_percent),
									   new SqlParameter("@ipbc_percent",		esi1_ipbc_percent),
									   new SqlParameter("@ipb_other_desc",		esi1_ipb_other_desc),
                                       new SqlParameter("@unlimit_liability",   esi1_unlimit_liability),
                                       new SqlParameter("@unlimit_liability_memo", esi1_unlimit_liability_memo)
                                   };
			#endregion

			oCmd.Parameters.AddRange(parms);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem1DeleteById()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE FROM engage_sign_costdept4 WHERE cost4_id IN (
	select cost4_id from engage_signitem1 join engage_sign_costdept4 on cost4_seqsn=esi1_seqsn and cost4_ver=esi1_ver and cost4_tc_seno=esi1_tc_seno where esi1_id = @esi1_id
)
DELETE FROM engage_sign_costdept6 WHERE cost6_id IN (
	select cost6_id from engage_signitem1 join engage_sign_costdept6 on cost6_seqsn=esi1_seqsn and cost6_ver=esi1_ver and cost6_tc_seno=esi1_tc_seno where esi1_id = @esi1_id
)
DELETE FROM engage_attfile1 WHERE ea_id IN (
	select ea_id from engage_signitem1 JOIN engage_attfile1 ON ea_seqsn=esi1_seqsn and ea_ver=esi1_ver where ea_filetype='NH' and esi1_id = @esi1_id
)
DELETE FROM engage_signitem1 WHERE esi1_id=@esi1_id
		";

			oCmd.Parameters.AddWithValue("@esi1_id", iSignItem1.esi1_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 原草約編號」取得方式由 前一版 engage_signitem1 挑選
		/// </summary>
		/// <returns></returns>
		public DataTable GetSignItem1ContnoPrever()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select cur.esi1_id,cur.esi1_tc_seqno_orignal,cur.esi1_cm_id
		,case when cur.esi1_tc_seqno_orignal=treaty.tc_seno then '1' else '0' end as bChecked 
		,his.esi1_ver as pre_esi1_ver
		,his.esi1_presigndate as pre_esi1_presigndate
		,tc_planno + tc_ver + tc_seqsn AS esi1_contno
		,treaty.*
  from engage_signitem1 cur 
  join engage_his.dbo.engage_signitem1 his ON his.esi1_seqsn=cur.esi1_seqsn and his.esi1_ver=(cur.esi1_ver-1)
  join v_engage_sign_choose_treaty_case treaty on tc_seno=his.esi1_tc_seno
  where cur.esi1_id=@esi1_id
";

			oCmd.Parameters.AddWithValue("@esi1_id", _esi1_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 修約原因, 主項原因
		/// </summary>
		/// <param name="esi1_id"></param>
		/// <returns></returns>
		public DataTable GetContModReasonType1()
		{
			#region SQL
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @esi1_cm_id int
select @esi1_cm_id=esi1_cm_id from engage_signitem1 where esi1_id=@esi1_id

SELECT rtrim(code_value) code_value, rtrim(code_valuedesc) code_valuedesc, cm_id, cmd_modremark --,*
	FROM contractDB..c_codetbl
	left join contractDB..c_contmod_detail on cmd_modtype='1' and cmd_modreasonitem = code_value and cm_id = @esi1_cm_id
	WHERE code_type = '113' AND code_value <> 'DX'	--過濾掉{DX:舊系統技轉約}
	ORDER BY code_order
";
			#endregion

			oCmd.Parameters.AddWithValue("@esi1_id", iSignItem1.esi1_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 修約原因, 細項原因
		/// </summary>
		/// <param name="esi1_id"></param>
		/// <returns></returns>
		public DataTable GetContModReasonType2()
		{
			#region SQL
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @esi1_cm_id int
select @esi1_cm_id=esi1_cm_id from engage_signitem1 where esi1_id=@esi1_id

SELECT rtrim(code_value) code_value, rtrim(code_valuedesc) code_valuedesc, cm_id, cmd_modremark --,*
	FROM contractDB..c_codetbl
	left join contractDB..c_contmod_detail on cmd_modtype='2' and cmd_modreasonitem = code_value and cm_id = @esi1_cm_id
	WHERE code_type = '114'
	ORDER BY code_order
";
			#endregion

			oCmd.Parameters.AddWithValue("@esi1_id", iSignItem1.esi1_id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 修約原因 - 存檔
		/// </summary>
		/// <param name="esi1_id"></param>
		/// <param name="tc_seno"></param>
		/// <param name="modtype1list"></param>
		/// <param name="modtype2list"></param>
		/// <param name="othersremark"></param>
		/// <param name="keyinempno"></param>
		/// <returns></returns>
		public bool SignItem1ContModReasonSave(string esi1_id, string tc_seno, string modtype1list, string modtype2list,
					string othersremark, string keyinempno)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_signitem1_contmodreason";

			oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);
			oCmd.Parameters.AddWithValue("@tc_seno", tc_seno);
			oCmd.Parameters.AddWithValue("@modtype1list", modtype1list);
			oCmd.Parameters.AddWithValue("@modtype2list", modtype2list);
			oCmd.Parameters.AddWithValue("@othersremark", othersremark);
			oCmd.Parameters.AddWithValue("@keyinempno", keyinempno);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 立案至契約簽辦一段式簽核完成
		/// </summary>
		/// <param name="seqsn"></param>
		/// <returns></returns>
		public bool Exec_pcflag_verify()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_base_pcflag_verify";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 50);
			msg.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				_returnMessage = msg.Value.ToString();
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 『負擔無賠償上限契約』處理與風險評估
		/// </summary>
		/// <returns></returns>
		public bool SignItem1UpdateForProc()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_signitem1 set 
	esi1_treaty_flag=@esi1_treaty_flag
	,esi1_treaty_result=@esi1_treaty_result
	,esi1_risk=@esi1_risk
	,esi1_exec_reason=@esi1_exec_reason
where esi1_id=@esi1_id
";
			oCmd.Parameters.AddWithValue("@esi1_id", _esi1_id);
			oCmd.Parameters.AddWithValue("@esi1_treaty_flag", esi1_treaty_flag);
			oCmd.Parameters.AddWithValue("@esi1_treaty_result", esi1_treaty_result);
			oCmd.Parameters.AddWithValue("@esi1_risk", esi1_risk);
			oCmd.Parameters.AddWithValue("@esi1_exec_reason", esi1_exec_reason);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 取得議約歷程的列表
		/// </summary>
		/// <returns></returns>
		public DataTable GetSignItem1_proc()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	select * from engage_signitem1_proc where proc_esi1_id=@esi1_id
end
else
begin
	select * from engage_his.dbo.engage_signitem1_proc where proc_esi1_id=@esi1_id and proc_ver=@ver
end
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esi1_id", esi1_id);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 議約歷程的新增
		/// </summary>
		/// <returns></returns>
		public bool SignItem1_procInsert()
		{
			#region SQL
			string SQL = string.Format(@"
if not exists (select * from engage_signitem1_proc where proc_id=@proc_id)
begin
	--取得目前的版次
	declare @ver int
	select @ver=esi1_ver from engage_signitem1 where esi1_id=@proc_esi1_id

	insert into engage_signitem1_proc 
		(proc_esi1_id,proc_esi1_seqsn,proc_ver,proc_date,proc_itri_empno,proc_itri_empname,proc_cust,proc_cust_tel,proc_memo)
	values
		(@proc_esi1_id,@proc_esi1_seqsn,@ver,@proc_date,@proc_itri_empno,@proc_itri_empname,@proc_cust,@proc_cust_tel,@proc_memo)
end
else
begin
	update engage_signitem1_proc set
		proc_date=@proc_date
		,proc_itri_empno=@proc_itri_empno
		,proc_itri_empname=@proc_itri_empname
		,proc_cust=@proc_cust
		,proc_cust_tel=@proc_cust_tel
		,proc_memo=@proc_memo
	where proc_id=@proc_id
end
 ");
			#endregion

			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = SQL;

			SqlParameter[] parms = {
									   new SqlParameter("@proc_id", proc_id)
									   ,new SqlParameter("@proc_esi1_id", proc_esi1_id)
									   ,new SqlParameter("@proc_esi1_seqsn", _seqsn)
									   ,new SqlParameter("@proc_date", proc_date)
									   ,new SqlParameter("@proc_itri_empno", proc_itri_empno)
									   ,new SqlParameter("@proc_itri_empname", proc_itri_empname)
									   ,new SqlParameter("@proc_cust", proc_cust)
									   ,new SqlParameter("@proc_cust_tel", proc_cust_tel)
									   ,new SqlParameter("@proc_memo", proc_memo)
								   };

			if (string.IsNullOrWhiteSpace(proc_id))
				parms[0].Value = 0;
			
			oCmd.Parameters.AddRange(parms);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 議約歷程的刪除
		/// </summary>
		/// <returns></returns>
		public bool SignItem1_procDelete()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
		delete from engage_signitem1_proc where proc_id=@proc_id
		";

			oCmd.Parameters.AddWithValue("@proc_id", proc_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 洽案報院條件--產生engage_signcont_sRC
        /// 契約簽辦挑選草約後執行本程式
		/// </summary>
		/// <returns></returns>
		public bool Sign_sRC_set()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_sRC_set";

			oCmd.Parameters.AddWithValue("@seqsn", Seqsn);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 契約簽辦-草約資訊-一次計價 engage_sign_costdept4
		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-查詢
		/// </summary>
		/// <param name="strSeqsn"></param>
		/// <param name="strTcSeno"></param>
		/// <param name="strVer"></param>
		/// <returns></returns>
		public DataTable DraftOneList()
		{

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_DraftOne";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@tc_seno", esi1_tc_seno);
			oCmd.Parameters.AddWithValue("@ver", Sign_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-取得單筆資料
		/// </summary>
		/// <param name="strSeqsn"></param>
		/// <param name="strTcSeno"></param>
		/// <param name="strVer"></param>
		/// <returns></returns>
		public DataTable DraftOneQuery(string id)
		{

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
select cost.*,tb.code_valuedesc as resultfrom,s1.esi1_memo7,s1.esi1_memo8  from engage_sign_costdept4 cost
left join (SELECT * from contract.dbo.cont_codetbl where code_type='012' AND code_enabled = 1) tb
on cost.cost4_resultfrom = tb.code_value
left join engage_signitem1 s1
on cost.cost4_seqsn=s1.esi1_seqsn and isnull(cost.cost4_tc_seno,0)=isnull(s1.esi1_tc_seno,0)
where cost4_id=@id 
";

			oCmd.Parameters.AddWithValue("@id", id);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-新增
		/// </summary>
		/// <returns></returns>
		public bool DraftOneInsert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
			declare @esc_ver int
			select @esc_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn 

			insert into engage_sign_costdept4 
				(cost4_seqsn,cost4_tc_seno,cost4_ver,cost4_resultfrom,cost4_tafbyfixfee,cost4_tpfbyfixfee,cost4_pafbyfixfee,cost4_ppfbyfixfee,cost4_cash_total,
				 cost4_tafbyfixfe_stock,cost4_tpfbyfixfee_stock,cost4_pafbyfixfee_stock,cost4_ppfbyfixfe_stock,cost4_stock_total,cost4_stock_value_total,cost4_stock_price) 
			values 
				(@seqsn,@cost4_tc_seno,@esc_ver,@cost4_resultfrom,@cost4_tafbyfixfee,@cost4_tpfbyfixfee,@cost4_pafbyfixfee,@cost4_ppfbyfixfee,@cost4_cash_total,
				 @cost4_tafbyfixfe_stock,@cost4_tpfbyfixfee_stock,@cost4_pafbyfixfee_stock,@cost4_ppfbyfixfe_stock,@cost4_stock_total,@cost4_stock_value_total,@cost4_stock_price)

			exec pr_engage_sign_syn_allmoney @seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost4_tc_seno", cost4_tc_seno);
			oCmd.Parameters.AddWithValue("@cost4_resultfrom", cost4_resultfrom);
			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfee", cost4_tafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee", cost4_tpfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee", cost4_pafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfee", cost4_ppfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_cash_total", cost4_cash_total);

			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfe_stock", cost4_tafbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee_stock", cost4_tpfbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee_stock", cost4_pafbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfe_stock", cost4_ppfbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_stock_total", cost4_stock_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_value_total", cost4_stock_value_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_price", cost4_stock_price);

			oCmd.Parameters.AddWithValue("@cash_memo", esi1_memo7);
			oCmd.Parameters.AddWithValue("@stock_memo", esi1_memo8);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-單筆修改
		/// </summary>
		/// <returns></returns>
		public bool DraftOneUpdate()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
			update engage_sign_costdept4 set
				cost4_resultfrom=@cost4_resultfrom,
				cost4_tafbyfixfee=@cost4_tafbyfixfee,
				cost4_tpfbyfixfee=@cost4_tpfbyfixfee,
				cost4_pafbyfixfee=@cost4_pafbyfixfee,
				cost4_ppfbyfixfee=@cost4_ppfbyfixfee,
				cost4_cash_total=@cost4_cash_total,
				cost4_tafbyfixfe_stock=@cost4_tafbyfixfe_stock,
				cost4_tpfbyfixfee_stock=@cost4_tpfbyfixfee_stock,
				cost4_pafbyfixfee_stock=@cost4_pafbyfixfee_stock,
				cost4_ppfbyfixfe_stock=@cost4_ppfbyfixfe_stock,
				cost4_stock_total=@cost4_stock_total,
				cost4_stock_value_total=@cost4_stock_value_total,
				cost4_stock_price=@cost4_stock_price 
			where cost4_id=@cost4_id

			exec pr_engage_sign_syn_allmoney @seqsn
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost4_id", cost4_id);
			oCmd.Parameters.AddWithValue("@cost4_tc_seno", cost4_tc_seno);
			oCmd.Parameters.AddWithValue("@cost4_resultfrom", cost4_resultfrom);
			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfee", cost4_tafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee", cost4_tpfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee", cost4_pafbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfee", cost4_ppfbyfixfee);
			oCmd.Parameters.AddWithValue("@cost4_cash_total", cost4_cash_total);

			oCmd.Parameters.AddWithValue("@cost4_tafbyfixfe_stock", cost4_tafbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_tpfbyfixfee_stock", cost4_tpfbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_pafbyfixfee_stock", cost4_pafbyfixfee_stock);
			oCmd.Parameters.AddWithValue("@cost4_ppfbyfixfe_stock", cost4_ppfbyfixfe_stock);
			oCmd.Parameters.AddWithValue("@cost4_stock_total", cost4_stock_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_value_total", cost4_stock_value_total);
			oCmd.Parameters.AddWithValue("@cost4_stock_price", cost4_stock_price);

			oCmd.Parameters.AddWithValue("@cash_memo", esi1_memo7);
			oCmd.Parameters.AddWithValue("@stock_memo", esi1_memo8);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-刪除單筆資料
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public bool DraftOneDelete(string id)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
	declare @seqsn bigint;
	select @seqsn=cost4_seqsn from engage_sign_costdept4 where cost4_id=@id;
	delete engage_sign_costdept4 where cost4_id=@id;
	exec pr_engage_sign_syn_allmoney @seqsn;
";

			oCmd.Parameters.AddWithValue("@id", id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-一次計價-帶入預設值(帶成本定價資料到契約簽辦)
		/// </summary>
		/// <param name="seqsn"></param>
		/// <param name="tc_seno"></param>
		public bool DraftOneLoadDefault()
		{
			#region SQL
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"pr_engage_sign_costdept4_default");
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@tc_seno", esi1_tc_seno);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-同步契約總金額
		/// </summary>
		/// <returns></returns>
		public bool DraftSynTotalMoney()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_syn_allmoney";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}


		/// <summary>
		/// 契約簽辦-草約資訊-非一次計價-查詢
		/// </summary>
		/// <param name="strSeqsn"></param>
		/// <param name="strTcSeno"></param>
		/// <param name="strVer"></param>
		/// <returns></returns>
		public DataTable DraftNoOneList()
		{

			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_DraftNOOne";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@tc_seno", esi1_tc_seno);
			oCmd.Parameters.AddWithValue("@ver", Sign_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-非一次計價-單筆新增
		/// </summary>
		/// <returns></returns>
		public bool DraftNoOneInsert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
			declare @esc_ver int
			select @esc_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn 

			insert into engage_sign_costdept6 
				(cost6_seqsn, cost6_ver, cost6_tc_seno, cost6_type, cost6_resultfrom, cost6_gatherway, cost6_year, cost6_gather, cost6_estimfee,cost6_memo2) 
            values 
				(@seqsn, @esc_ver, @cost6_tc_seno, @cost6_type, @cost6_resultfrom, @cost6_gatherway, @cost6_year, @cost6_gather, @cost6_estimfee,@cost6_memo2)
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost6_tc_seno", cost6_tc_seno);
			oCmd.Parameters.AddWithValue("@cost6_type", cost6_type);
			oCmd.Parameters.AddWithValue("@cost6_resultfrom", cost6_resultfrom);
			oCmd.Parameters.AddWithValue("@cost6_gatherway", cost6_gatherway);
			oCmd.Parameters.AddWithValue("@cost6_year", cost6_year);
			oCmd.Parameters.AddWithValue("@cost6_gather", cost6_gather);
			oCmd.Parameters.AddWithValue("@cost6_estimfee", cost6_estimfee);
			oCmd.Parameters.AddWithValue("@cost6_memo2", cost6_memo2);
			
			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-非一次計價-單筆修改
		/// </summary>
		/// <returns></returns>
		public bool DraftNoOneUpdate()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
			update engage_sign_costdept6 set
				cost6_type=@cost6_type,
				cost6_resultfrom=@cost6_resultfrom,
				cost6_gatherway=@cost6_gatherway,
				cost6_year=@cost6_year,
				cost6_gather=@cost6_gather,
				cost6_estimfee=@cost6_estimfee,
				cost6_memo2=@cost6_memo2
			where cost6_id=@cost6_id
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@cost6_id", cost6_id);
			oCmd.Parameters.AddWithValue("@cost6_type", cost6_type);
			oCmd.Parameters.AddWithValue("@cost6_resultfrom", cost6_resultfrom);
			oCmd.Parameters.AddWithValue("@cost6_gatherway", cost6_gatherway);
			oCmd.Parameters.AddWithValue("@cost6_year", cost6_year);
			oCmd.Parameters.AddWithValue("@cost6_gather", cost6_gather);
			oCmd.Parameters.AddWithValue("@cost6_estimfee", cost6_estimfee);
			oCmd.Parameters.AddWithValue("@cost6_memo2", cost6_memo2);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-非一次計價-刪除單筆資料
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public bool DraftNoOneDelete(string id)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
delete engage_sign_costdept6 where cost6_id=@id";

			oCmd.Parameters.AddWithValue("@id", id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 契約簽辦-草約資訊-非一次計價-帶入預設值(帶成本定價資料到契約簽辦)
		/// </summary>
		/// <param name="seqsn"></param>
		/// <param name="tc_seno"></param>
		public bool DraftNoOneLoadDefault()
		{
			#region SQL
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = string.Format(@"pr_engage_sign_costdept6_default");
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@tc_seno", esi1_tc_seno);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region 契約簽辦-草約資訊-一次計價, 更新-現金說明、股票說明
		/// <summary>
		/// 更新-現金說明、股票說明
		/// </summary>
		/// <returns></returns>
		public bool Update_signmemo()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
if @sign_memo_type = 'memo7'
begin
	update engage_signitem1 set esi1_memo7=@sign_memo where esi1_id=@esi1_id
	update engage_signcont set esc_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), esc_modempno=@empno, esc_modname=@empname where esc_seqsn=@seqsn
end
else if @sign_memo_type = 'memo8'
begin
	update engage_signitem1 set esi1_memo8=@sign_memo where esi1_id=@esi1_id
	update engage_signcont set esc_moddate=CONVERT(VARCHAR(8), GETDATE(), 112), esc_modempno=@empno, esc_modname=@empname where esc_seqsn=@seqsn
end
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esi1_id", _esi1_id);
			oCmd.Parameters.AddWithValue("@sign_memo_type", sign_memo_type);
			oCmd.Parameters.AddWithValue("@sign_memo", sign_memo);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region engage_signitem2

		public DataTable GetSignItem2BySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	SELECT		doc.*,
				esi2_id, esi2_ver, esi2_checked
	FROM		dbo.v_engage_sign_doc doc LEFT OUTER JOIN
				dbo.engage_signitem2 ON 
					esi2_seqsn = doc.doc_seqsn AND 
					esi2_doctype = doc.doc_type AND 
					esi2_attdocid = doc.doc_attdocid
	WHERE		doc_seqsn = @seqsn
END
ELSE
BEGIN
	SELECT		doc.*,
				esi2_id, esi2_ver, esi2_checked
	FROM		dbo.v_engage_sign_doc doc LEFT OUTER JOIN
				engage_his.dbo.engage_signitem2 ON
					esi2_seqsn = doc.doc_seqsn AND 
					esi2_doctype = doc.doc_type AND 
					esi2_attdocid = doc.doc_attdocid AND
					esi2_ver = @ver
	WHERE		doc_seqsn = @seqsn
END
 ";
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool SignItem2Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @nCount int, @ver int
SELECT @nCount=COUNT(esi2_id) FROM engage_signitem2 WHERE esi2_id=@item_id

if @nCount = 0
BEGIN
	SELECT @ver=ISNULL(esc_ver,1) FROM engage_signcont WHERE esc_seqsn=@seqsn

	INSERT INTO engage_signitem2
		(esi2_seqsn,esi2_ver,esi2_doctype,esi2_attdocid,esi2_checked,esi2_docver)
	VALUES
		(@seqsn,@ver,@doctype,@attdocid,@checked,@docver)
END
ELSE
BEGIN
	UPDATE engage_signitem2 SET
		esi2_doctype=@doctype,
		esi2_attdocid=@attdocid,
		esi2_checked=@checked,
		esi2_docver=@docver
	WHERE esi2_id=@item_id
END
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@item_id", esi2_id);
			oCmd.Parameters.AddWithValue("@doctype", esi2_doctype);
			oCmd.Parameters.AddWithValue("@attdocid", esi2_attdocid);
			oCmd.Parameters.AddWithValue("@checked", esi2_checked);
			oCmd.Parameters.AddWithValue("@docver", esi2_docver);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem2DeleteById()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_signitem2 WHERE esi2_id=@item_id
";
			oCmd.Parameters.AddWithValue("@item_id", esi2_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		
		#endregion

		#region engage_signitem3

		public DataTable GetSignItem3BySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	SELECT		ea_id, ea_ver, ea_doc, ea_uploaddate, ea_filename,
				ea_keyinempno, ea_keyinempname
	FROM		engage_attfile2 
	JOIN		engage_signcont ON esc_seqsn = ea_seqsn AND esc_ver = ea_ver
	WHERE		ea_seqsn = @seqsn AND ea_filetype = 'NG'

END
ELSE
BEGIN
	SELECT		ea_id, ea_ver, ea_doc, ea_uploaddate, ea_filename,
				ea_keyinempno, ea_keyinempname
	FROM		engage_attfile2 
	WHERE		ea_seqsn = @seqsn AND ea_ver = @ver AND ea_filetype = 'NG'
END
 ";
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool SignItem3Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @id bigint

-- NG: 契約簽辦其他附件檔
IF @filetype = 'NG'
BEGIN
	INSERT INTO engage_attfile2
		(ea_seqsn,ea_ver,ea_filetype,ea_doc,ea_uploaddate,ea_filename,ea_filetxt,ea_keyinempno,ea_keyinempname)
	SELECT
		@seqsn,esc_ver,@filetype,@ea_doc,getdate(),@ea_filename,NULL,@ea_keyinempno,@ea_keyinempname
	FROM engage_signcont WHERE esc_seqsn=@seqsn

	SELECT @id=@@identity

	update engage_attfile2 SET ea_filename=ea_filetype+'-'+CAST(ea_seqsn AS varchar)+'-'+CAST(ea_id AS varchar)+'-'+ea_filename WHERE ea_id=@id

	SELECT @ea_id=ea_id,@ea_filename=ea_filename FROM engage_attfile2 WHERE ea_id=@id
END
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			//oCmd.Parameters.AddWithValue("@filetype", esi2_id);
			//oCmd.Parameters.AddWithValue("@ea_doc", esi2_doctype);
			//oCmd.Parameters.AddWithValue("@ea_filename", esi2_attdocid);
			//oCmd.Parameters.AddWithValue("@ea_filetxt", esi2_checked);
			//oCmd.Parameters.AddWithValue("@ea_keyinempno", esi2_docver);
			//oCmd.Parameters.AddWithValue("@ea_keyinempname", esi2_docver);
			//oCmd.Parameters.AddWithValue("@ea_id", esi2_docver);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem3DeleteById(string id)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_attfile2 WHERE ea_id=@item_id
";
			oCmd.Parameters.AddWithValue("@item_id", id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

        #region engage_signitem3

		public DataTable GetSingOpinionFilesBySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	SELECT		ea_id, ea_ver, ea_doc, CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, ea_filename,
				ea_keyinempno, ea_keyinempname
	FROM		engage_attfile2 
	JOIN		engage_signcont ON esc_seqsn = ea_seqsn AND esc_ver = ea_ver
	WHERE		ea_seqsn = @seqsn AND ea_filetype = 'FC'

END
ELSE
BEGIN
	SELECT		ea_id, ea_ver, ea_doc, CONVERT(varchar, ea_uploaddate, 111) as ea_uploaddate, ea_filename,
				ea_keyinempno, ea_keyinempname
	FROM		engage_attfile2 
	WHERE		ea_seqsn = @seqsn AND ea_ver = @ver AND ea_filetype = 'FC'
END
 ";
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
		#endregion

		#region engage_signitem4
		
		public DataTable GetSignItem4BySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	--exec [pr_engage_ecp_for_signitem4] @seqsn 

	SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	FROM		engage_signitem4 item4
	left JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	left JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	JOIN		engage_signcont ON esi4_seqsn = esc_seqsn and esi4_ver = esc_ver
	WHERE		esi4_seqsn = @seqsn and ISNULL(esi4_eztype,'')=''
    ORDER BY esi4_id
END
ELSE
BEGIN
	SELECT		item4.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	FROM		engage_his.dbo.engage_signitem4 item4
	left JOIN		common.dbo.depcod ON esi4_signdept = dep_deptid
	left JOIN		common.dbo.orgcod ON dep_orgcd = org_orgcd
	JOIN		engage_his.dbo.engage_signcont ON esi4_seqsn = esc_seqsn and esi4_ver = esc_ver
	WHERE		esi4_seqsn = @seqsn AND esi4_ver = @ver and ISNULL(esi4_eztype,'')='' 
    ORDER BY esi4_id
END
 ";
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool SignItem4Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @nCount int, @ver int
SELECT @nCount=COUNT(esi4_id) FROM engage_signitem4 WHERE esi4_id=@item_id
SELECT @ver=ISNULL(esc_ver,1) FROM engage_signcont WHERE esc_seqsn=@seqsn

if @nCount = 0
BEGIN
	INSERT INTO engage_signitem4
		(esi4_seqsn,esi4_ver,esi4_signtype,esi4_signempno,esi4_signname,esi4_signdept)
	VALUES
		(@seqsn,@ver,@signtype,@signempno,@signname,@signdept)
END
ELSE
BEGIN
	UPDATE engage_signitem4 SET
		esi4_signtype=@signtype,
		esi4_signempno=@signempno,
		esi4_signname=@signname,
		esi4_signdept=@signdept
	WHERE esi4_id=@item_id
END 
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@item_id", esi4_id);
			oCmd.Parameters.AddWithValue("@signtype", esi4_signtype);
			oCmd.Parameters.AddWithValue("@signempno", esi4_signempno);
			oCmd.Parameters.AddWithValue("@signname", esi4_signname);
			oCmd.Parameters.AddWithValue("@signdept", esi4_signdept);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem4DeleteById()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_signitem4 WHERE esi4_id=@item_id
";
			oCmd.Parameters.AddWithValue("@item_id", esi4_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem4_CheckForeignCustBySeqsn(string seqsn)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_signitem4_checkforeigncust";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				_returnMessage = this.getTopOne(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#endregion

		#region engage_signitem5 

		public DataTable GetSignItem5BySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"
DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN
	if not exists(select * from engage_signitem5 where esi5_seqsn=@seqsn)
	begin
		exec [pr_engage_sign_default] @seqsn
	end

	SELECT		item5.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	FROM		engage_signitem5 item5
	JOIN		common.dbo.depcod ON esi5_sendtodept=dep_deptid
	JOIN		common.dbo.orgcod ON dep_orgcd=org_orgcd
	WHERE		esi5_seqsn=@seqsn 
END
ELSE
BEGIN
	SELECT		item5.*, dep_deptname AS deptname, org_abbr_chnm1 AS orgname
	FROM		engage_his.dbo.engage_signitem5 item5
	JOIN		common.dbo.depcod ON esi5_sendtodept=dep_deptid
	JOIN		common.dbo.orgcod ON dep_orgcd=org_orgcd
	WHERE		esi5_seqsn=@seqsn AND esi5_ver=@ver 
END
 ";
			#endregion

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		public bool SignItem5Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @nCount int, @ver int
SELECT @nCount=COUNT(esi5_id) FROM engage_signitem5 WHERE esi5_id=@item_id

SET XACT_ABORT ON
BEGIN TRAN

IF @nCount = 0
BEGIN
	SELECT @ver=ISNULL(esc_ver,1) FROM engage_signcont WHERE esc_seqsn=@seqsn

	if @sendtoempno = ''
	BEGIN
		INSERT INTO engage_signitem5
		SELECT distinct @seqsn,@ver,DOC_MAIL2,com_cname,@sendtodept,0
		FROM v_DOC_MAIL
		WHERE DOC_MAIL1=@sendtodept
	END
	ELSE
	BEGIN
		INSERT INTO engage_signitem5
			(esi5_seqsn,esi5_ver,esi5_sendtoempno,esi5_sendtoname,esi5_sendtodept,esi5_nodelete)
		VALUES
			(@seqsn,@ver,@sendtoempno,@sendtoname,@sendtodept,0)
	END
END
ELSE
BEGIN
	IF @sendtoempno = ''
	BEGIN
		SELECT @sendtoempno=DOC_MAIL2,@sendtoname=com_cname
		FROM v_DOC_MAIL
		WHERE DOC_MAIL1=@sendtodept
	END
	
	UPDATE engage_signitem5 SET
		esi5_sendtoempno=@sendtoempno,
		esi5_sendtoname=@sendtoname,
		esi5_sendtodept=@sendtodept
	WHERE esi5_id=@item_id
END

COMMIT TRAN
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@item_id", esi5_id);
			oCmd.Parameters.AddWithValue("@sendtoempno", esi5_sendtoempno);
			oCmd.Parameters.AddWithValue("@sendtoname", esi5_sendtoname);
			oCmd.Parameters.AddWithValue("@sendtodept", esi5_sendtodept);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool SignItem5DeleteById()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DELETE engage_signitem5 WHERE esi5_id=@item_id
";
			oCmd.Parameters.AddWithValue("@item_id", esi5_id);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		
		#endregion


		#region 新增新版次
		/// <summary>
		/// 新增新版次, sp:[pr_engagesign_edit_ver]
		/// </summary>
		/// <param name="bIsCopy"></param>
		/// <returns></returns>
		public bool EngageSignUpgradeVersion(bool bIsCopy)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engagesign_edit_ver";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@iscopy", ((bIsCopy) ? "1" : "0"));

			SqlParameter msg = oCmd.Parameters.Add("@msg", SqlDbType.NVarChar, 100);
			msg.Direction = ParameterDirection.Output;

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				_returnMessage = msg.Value.ToString();
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 契約簽辦回復前一版次
		/// <summary>
		/// 契約簽辦回復前一版次
		/// </summary>
		/// <param name="M"></param>
		public bool EngageSignRollbackVersion()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engagesign_rollback_ver";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 更新最後列印日期
		/// <summary>
		/// 更新最後列印日期
		/// </summary>
		/// <param name="M"></param>
		public bool SignContUpdateLastPrintDate()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"

DECLARE @max_ver int
select @max_ver=esc_ver from engage_signcont where esc_seqsn=@seqsn

if @ver = 0 or @ver = @max_ver
BEGIN			
	if (select isnull(esc_lastprintdate,'') from engage_signcont where esc_seqsn=@seqsn)=''
    begin
		UPDATE engage_signcont SET
			esc_lastprintdate=CONVERT(varchar(8),getdate(),112),
			esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=CONVERT(varchar(8),getdate(),112)
		WHERE esc_seqsn=@seqsn
	end
	else
	begin
		UPDATE engage_signcont SET			
			esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=CONVERT(varchar(8),getdate(),112)
		WHERE esc_seqsn=@seqsn
	end
	
	--檢查進度是否等於（J9、L1），如果是則將洽案進度更新為契約簽辦中(NC), ※需轉紙本才需變更進度 20100105 加此條件
	if(select isnull(esc_signtype,'') from engage_signcont where esc_seqsn=@seqsn)='1'
	begin
		UPDATE engage_base SET
			eb_execstatus='NC'
		WHERE eb_seqsn=@seqsn AND (eb_execstatus='J9' or eb_execstatus='L1')
	end
END
ELSE
BEGIN
	if (select isnull(esc_lastprintdate,'') from engage_his.dbo.engage_signcont where esc_seqsn=@seqsn)=''
	begin
		UPDATE engage_his.dbo.engage_signcont SET
			esc_lastprintdate=CONVERT(varchar(8),getdate(),112),
			esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=CONVERT(varchar(8),getdate(),112)
		WHERE esc_seqsn=@seqsn AND esc_ver=@ver
	end
	else
	begin
		UPDATE engage_his.dbo.engage_signcont SET			
			esc_modempno=@esc_modempno,esc_modname=@esc_modname,esc_moddate=CONVERT(varchar(8),getdate(),112)
		WHERE esc_seqsn=@seqsn AND esc_ver=@ver
	end
END

";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);
			oCmd.Parameters.AddWithValue("@esc_modempno", _empno);
			oCmd.Parameters.AddWithValue("@esc_modname", _empname);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 歷次簽辦單
		/// <summary>
		/// 歷次簽辦單
		/// </summary>
		/// <param name="seqsn"></param>
		/// <param name="condition"></param>
		/// <returns></returns>
		public DataTable GetSignContHistoryBySeqsn()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText =string.Format(@"
	SELECT		esc_seqsn,esc_ver
				, convert(varchar,convert(datetime,esc_lastprintdate),111) as esc_lastprintdate
				, esc_signcontname
				,convert(varchar,convert(datetime,esc_signdate),111) as esc_signdate
				,esc_signresult
				,code_valuedesc as esc_signresult_desc
	FROM		engage_his.dbo.engage_signcont
	Left join	engage_codetbl on code_type='123' and esc_signresult=code_value
	WHERE		esc_seqsn=@seqsn
	order by	esc_ver
	");

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}
        #endregion

        /// <summary>
        /// 重要提醒訊息
        /// </summary>
        /// <param name="formtype">EG06 or EG08</param>
        /// <returns></returns>
        public DataTable GetSignNotice(string formtype)
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_sign_notice";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
            oCmd.Parameters.AddWithValue("@formtype", formtype);

            DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			return dt;
		}

		#region 契約簽辦修改
		/// <summary>
		/// 契約簽辦修改
		/// </summary>
		/// <returns></returns>
		public bool SignModReasonSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_signcont set esc_mod_reason=@esc_mod_reason where esc_seqsn=@seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esc_mod_reason", esc_mod_reason);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
		#endregion

		#region 歷次契約簽辦電子表單(EG06)

		public DataTable GetEG06_ECP_list()
		{
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
exec [pr_engage_EG06_ecp_list] @seqsn, @ver
";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", _esc_ver);

			DataTable dt = this.getDataTable(oCmd, CommandType.Text);
			return dt;
		}

		#endregion


		/// <summary>
		/// 報院案件 and EG06 簽核同意,可以執行[報院審查前修改], 填寫完簽辦修改說明後可重新編輯契約簽辦頁面.
		/// </summary>
		/// <returns></returns>
		public bool SignEcpEG06Reopen()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_ecp_EG06_reopen";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 【查詢議約資訊】及【詳細內容】, 設定權限給議約系統相關人員
		/// </summary>
		/// <returns></returns>
		public bool SetTreatyRightForNewCase()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
declare @planno nvarchar(11) 
select @planno=eb_year+eb_orgcd+eb_class+eb_sn from engage_base where eb_seqsn=@seqsn
exec  esp_treaty_eng_gpi_base_right_NewCase @planno
			";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			this.Execute(oCmd, CommandType.Text);
			success = true;
			return success;
		}

		/// <summary>
		/// 清除時間戳記
		/// </summary>
		/// <returns></returns>
		public bool ClearTranTimestamp()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"update engage_signitem1 set esi1_tran_empno=null, esi1_tran_timestamp=null, esi1_doc_date=null, esi1_doc_empno=null where esi1_id = @esi1_id";
			oCmd.Parameters.AddWithValue("@esi1_id", _esi1_id);

			this.Execute(oCmd, CommandType.Text);
			success = true;
			return success;
		}

		/// <summary>
		/// 修正契約書電子檔
		/// </summary>
		/// <returns></returns>
		public bool SetContCanDo()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_DOC_reset_timestamp";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);

			this.Execute(oCmd, CommandType.StoredProcedure);
			success = true;
			return success;
		}

		/// <summary>
		/// 契約書製作別: 1:本院, 2:客戶
		/// </summary>
		/// <returns></returns>
		public bool ContMakeTypeSave()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
update engage_signcont set esc_cont_make_type = @esc_cont_make_type where esc_seqsn = @seqsn
";
			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@esc_cont_make_type", esc_cont_make_type);

			try
			{
				this.Execute(oCmd, CommandType.Text);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		#region 契約書用印清單
		/// <summary>
		/// 契約書用印清單,查詢入口頁面
		/// </summary>
		/// <returns></returns>
		public DataTable GetDocTimestampEntry(string srh_orgcd, string srh_processType, string srh_sysType, string srh_verType,
							string srh_verdate1, string srh_verdate2, string srh_planno, string srh_keyword,
                            string srh_non_stamp_date1, string srh_non_stamp_date2, string srh_non_stamp,
                            string sortExpression, int pageIndex, int pageSize, string srh_TimestampNo,ref int rowCount)
		{
			SqlCommand oCmd = new SqlCommand();
			#region SQL
			oCmd.CommandText = @"pr_engage_doc_timestamp_query";
			#endregion

			oCmd.Parameters.AddWithValue("@srh_orgcd", srh_orgcd);
			oCmd.Parameters.AddWithValue("@srh_processType", srh_processType);
			oCmd.Parameters.AddWithValue("@srh_sysType", srh_sysType);
			oCmd.Parameters.AddWithValue("@srh_verType", srh_verType);
			oCmd.Parameters.AddWithValue("@srh_verdate1", srh_verdate1);
			oCmd.Parameters.AddWithValue("@srh_verdate2", srh_verdate2);
			oCmd.Parameters.AddWithValue("@srh_planno", srh_planno);
			oCmd.Parameters.AddWithValue("@srh_keyword", srh_keyword);
            oCmd.Parameters.AddWithValue("@srh_non_stamp_date1", srh_non_stamp_date1);
            oCmd.Parameters.AddWithValue("@srh_non_stamp_date2", srh_non_stamp_date2);
            oCmd.Parameters.AddWithValue("@srh_non_stamp", srh_non_stamp);

            oCmd.Parameters.AddWithValue("@sortExpression", sortExpression);
			oCmd.Parameters.AddWithValue("@pageIndex", pageIndex);
			oCmd.Parameters.AddWithValue("@pageSize", pageSize);
            oCmd.Parameters.AddWithValue("@srh_TimestampNo", srh_TimestampNo);
            SqlParameter param1 = new SqlParameter("@rowCount", SqlDbType.Int);
			param1.Direction = ParameterDirection.Output;
			oCmd.Parameters.Add(param1);

			DataTable dt = this.getDataTable(oCmd, CommandType.StoredProcedure);
			rowCount = (int)param1.Value;
			return dt;
		}

		/// <summary>
		/// 取得「契約書用印清單」的權限
		/// </summary>
		/// <returns></returns>
		public bool GetDocTimestampRole(string empno)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"
DECLARE @grpnm varchar(15)
exec dbo.filter_GP @empno, @grpnm OUTPUT
--select @grpnm
if @grpnm = 'sys_adm' or exists(SELECT empno FROM engage_ibc_empno WHERE (dept = N'文書用印') and empno = @empno)
	select '1'
else
	select '0'
";
			oCmd.Parameters.AddWithValue("@empno", empno);

			try
			{
				string tmp = this.getTopOne(oCmd, CommandType.Text);
				success = tmp.Equals("1");
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 「契約書用印」新增
		/// </summary>
		/// <returns></returns>
		public bool DocTimestamp_Insert()
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_DOC_timestamp";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", 0);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@cmdtype", "A");
			oCmd.Parameters.AddWithValue("@tran_timestamp", "");

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		public bool DocTimestamp_DocVerifySave(string tran_timestamp)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_DOC_timestamp";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", Sign_ver);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@cmdtype", "C");
			oCmd.Parameters.AddWithValue("@tran_timestamp", tran_timestamp);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 時間戳記,回復人員
		/// </summary>
		/// <param name="tran_timestamp"></param>
		/// <returns></returns>
		public bool DocTimestamp_DocUndoSave(string tran_timestamp)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_DOC_timestamp";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", Sign_ver);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@cmdtype", "U");
			oCmd.Parameters.AddWithValue("@tran_timestamp", tran_timestamp);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}

		/// <summary>
		/// 時間戳記,再修約註記人員
		/// </summary>
		/// <param name="tran_timestamp"></param>
		/// <returns></returns>
		public bool DocTimestamp_DocMarkSave(string tran_timestamp)
		{
			bool success = false;
			SqlCommand oCmd = new SqlCommand();
			oCmd.CommandText = @"pr_engage_DOC_timestamp";

			oCmd.Parameters.AddWithValue("@seqsn", _seqsn);
			oCmd.Parameters.AddWithValue("@ver", Sign_ver);
			oCmd.Parameters.AddWithValue("@empno", _empno);
			oCmd.Parameters.AddWithValue("@empname", _empname);
			oCmd.Parameters.AddWithValue("@cmdtype", "M");
			oCmd.Parameters.AddWithValue("@tran_timestamp", tran_timestamp);

			try
			{
				this.Execute(oCmd, CommandType.StoredProcedure);
				success = true;
			}
			catch (Exception ex)
			{
				_errorMessage = ex.Message;
			}
			return success;
		}
        #endregion

        #region GetConttype1Flag
        /// <summary>
        /// 傳入 @seqsn, 該計畫欲啟動 報院預覽流程與送送簽【EG08送簽】，回傳主導負責的單位代碼。
        /// </summary>
        /// <returns></returns>
        public string GetConttype1Flag()
        {
            SqlCommand oCmd = new SqlCommand();
            #region SQL
            oCmd.CommandText = @"pr_engage_conttype1_flag";
            #endregion

            oCmd.Parameters.AddWithValue("@seqsn", this.Seqsn);
            SqlParameter param1 = new SqlParameter("@type1_flag", SqlDbType.VarChar, 2);
            param1.Direction = ParameterDirection.Output;
            oCmd.Parameters.Add(param1);

            this.Execute(oCmd, CommandType.StoredProcedure);
            
            return param1.Value.ToString();
        }
        #endregion

        public bool DocTimestamp_ConfirmNonStamp(string tran_timestamp, string confirm_non_stamp)
        {
            bool success = false;
            SqlCommand oCmd = new SqlCommand();
            oCmd.CommandText = @"
if @confirm_non_stamp = 'Y'
begin
    update engage_doc_timestamp set 
		    confirm_non_stamp = @confirm_non_stamp
		    ,confirm_non_stamp_date = getdate()
		    ,confirm_non_stamp_empno = @empno
	    where tran_timestamp = @tran_timestamp
end
else
begin
    update engage_doc_timestamp set 
		    confirm_non_stamp = null
		    ,confirm_non_stamp_date = null
		    ,confirm_non_stamp_empno = null
	    where tran_timestamp = @tran_timestamp
end
";
            oCmd.Parameters.AddWithValue("@tran_timestamp", tran_timestamp);
            oCmd.Parameters.AddWithValue("@confirm_non_stamp", confirm_non_stamp);
            oCmd.Parameters.AddWithValue("@empno", _empno);

            try
            {
                this.Execute(oCmd, CommandType.Text);
                success = true;
            }
            catch (Exception ex)
            {
                _errorMessage = ex.Message;
            }
            return success;
        }
    }

}