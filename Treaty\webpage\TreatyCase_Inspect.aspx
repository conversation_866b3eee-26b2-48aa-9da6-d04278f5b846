﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="TreatyCase_Inspect.aspx.cs" Inherits="Treaty_webpage_TreatyCase_Inspect" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>案件審查</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <link rel="stylesheet" type="text/css" href="../css/dialogstyle.css" />
    <link rel="stylesheet" type="text/css" href="../css/myITRIproject/jquery-ui.css" />
    <script type="text/javascript" src="../Scripts/jquery-3.2.1.js"></script>
    <link rel="stylesheet" type="text/css" href="../Style/colorbox.css" />

    <script type="text/javascript" src="../Scripts/jquery.uploadfile.min.js"></script>
    <script type="text/javascript">
        function close_win() {
            alert("審查完成!");
            parent.$.fn.colorbox.close();
        }

    </script>
    <style type="text/css">
        .td_right {
            text-align: right;
        }

        .td_left {
            text-align: left;
        }

        .TB_ReadOnly {
            background-color: rgb(236, 233, 216);
        }

        #colorbox #cboxClose {
            top: 0;
            right: 0;
        }

        #cboxLoadedContent {
            margin-top: 5px;
            margin-bottom: 0;
        }

        .empty {
            color: #aaa;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <span class="stripeMe">

            <table style="margin-left: 15px; margin-top: 25px">
                <tr>
                    <td class="td_right">簽核狀態：</td>
                    <td>
                        <asp:Label ID="LB_inspectName" runat="server" Text="Label" Visible="false"></asp:Label>
                        <asp:RadioButtonList ID="RB_inspect_flag" runat="server" RepeatDirection="Horizontal" Width="150px" BorderWidth="0" Height="30px" AutoPostBack="True" OnSelectedIndexChanged="RB_inspect_flag_SelectedIndexChanged">
                            <asp:ListItem Value="1" Selected="True">同意</asp:ListItem>
                            <asp:ListItem Value="2">不同意</asp:ListItem>
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">簽核意見：</td>
                    <td>
                        <asp:TextBox ID="TB_Inspect" runat="server" Width="544px" Height="64px" TextMode="MultiLine"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td class="td_right">新增審查人：</td>
                    <td>
                        <asp:DropDownList ID="DDL_AssignInspect" runat="server" DataTextField="empName" DataValueField="empNo" Height="20px" Width="145px" AppendDataBoundItems="True">
                            <asp:ListItem Value="">   --請選擇--  </asp:ListItem>
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td class="td_right" colspan="2">
                        <asp:Button ID="BT_Save" runat="server" Text="存檔" class="genbtnS" OnClick="BT_Save_Click" /></td>
                </tr>
            </table>
            <%-- <asp:SqlDataSource ID="SDS_SC" runat="server" ConnectionString="<%$ ConnectionStrings:CS_treaty %>" />--%>
        </span>
    </form>
</body>
</html>
