<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EGCreate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="pGUID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sysid" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="secureid" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EGCreateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EGCreateResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="EGCreateSoapIn">
    <wsdl:part name="parameters" element="tns:EGCreate" />
  </wsdl:message>
  <wsdl:message name="EGCreateSoapOut">
    <wsdl:part name="parameters" element="tns:EGCreateResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EGCreateHttpPostIn">
    <wsdl:part name="pGUID" type="s:string" />
    <wsdl:part name="sysid" type="s:string" />
    <wsdl:part name="secureid" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EGCreateHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="EG08Soap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EGCreate">
      <wsdl:input message="tns:EGCreateSoapIn" />
      <wsdl:output message="tns:EGCreateSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="EG08HttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EGCreate">
      <wsdl:input message="tns:EGCreateHttpPostIn" />
      <wsdl:output message="tns:EGCreateHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="EG08Soap" type="tns:EG08Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EGCreate">
      <soap:operation soapAction="http://tempuri.org/EGCreate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EG08Soap12" type="tns:EG08Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EGCreate">
      <soap12:operation soapAction="http://tempuri.org/EGCreate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EG08HttpPost" type="tns:EG08HttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EGCreate">
      <http:operation location="/EGCreate" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="EG08">
    <wsdl:port name="EG08Soap" binding="tns:EG08Soap">
      <soap:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG08.asmx" />
    </wsdl:port>
    <wsdl:port name="EG08Soap12" binding="tns:EG08Soap12">
      <soap12:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG08.asmx" />
    </wsdl:port>
    <wsdl:port name="EG08HttpPost" binding="tns:EG08HttpPost">
      <http:address location="http://flow.itri.org.tw/ECPWeb/WebServiceCust/EG/EG08.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>