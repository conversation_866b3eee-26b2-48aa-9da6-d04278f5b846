﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Engage;
using System.Data;
using System.Text;

public partial class Engage_Bulletin_BoardViewTop5 : CommonPage
{
	protected string systype = "";
	protected string sysName = string.Empty;
	protected bool bIsHasAuthority = false;

	protected void Page_Load(object sender, EventArgs e)
	{
		//抓取系統別
		if (Request["sys"] == null || Request["sys"].Length > 10)
		{
			this.Alert_CloseColorbox("系統別參數錯誤");
			return;
		}

        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();

        string UserIDStr = sso.empNo + "_" + sso.empName + "_" + sso.empOrgname + "_" + sso.empOrgcd + sso.empDeptcd;
        lit_Matomo.Text = this.getMatomo(UserIDStr);

        systype = Request["sys"].ToString().ToUpper();
		bIsHasAuthority = GetPermission();

		if (!IsPostBack)
		{
			BindData();
		}
	}
	/// <summary>
	/// 判斷是否具有權限, {取得FAQ的系統負責人、業務人員},呼叫 myBulletin.GetFAQSysOnwer() Method.
	/// </summary>
	private bool GetPermission()
	{
		bool flag = false;
		myBulletin dal = new myBulletin();
		dal.BB_sys = systype;
		DataTable dt = dal.GetFAQSysOnwer();
		if (dt != null && dt.Rows.Count > 0)
		{
			DataRow row = dt.Rows[0];
			sysName = row["is_infosys_name"].ToString();

			flag = (row["is_pm_empno"].ToString().Equals(sso.empNo) ||
					row["is_ischarge_empno"].ToString().Equals(sso.empNo) ||
					row["is_mgr_empno"].ToString().Equals(sso.empNo) ||
					row["is_charge_empno"].ToString().Equals(sso.empNo));
		}
		return flag;
	}
	void BindData()
	{
		myBulletin dal = new myBulletin();
		dal.BB_sys = systype;
		dal.BB_type = "1";
		dal.EmpNo = sso.empNo;
		dal.BulletinWindowShowing();

		if (dal.ShowingMode.Equals("S"))
		{
			//停機公告
			DataTable dt = dal.BulletinGetAll();
			DataView dv = new DataView(dt, "BB_service_down='Y'", "", DataViewRowState.CurrentRows);
			if (dv.Count > 0)
			{
				div_content.InnerHtml = dv[0]["BB_content"].ToString();

				//附件檔
				dal.BB_id = long.Parse(dv[0]["BB_id"].ToString());
				DataTable dtFile = dal.BulletinAttfileGetByMasterId();
				repFile.DataSource = dtFile;
				repFile.DataBind();
			}

			pnl_normal.Visible = false;
			pnl_serviceDown.Visible = true;
			btn_manage2.Visible = bIsHasAuthority;
		}
		else
		{
			//訊息公告，Top 5 則
			DataTable dt = dal.BulletinGetTop5();
			Rep1.DataSource = dt;
			Rep1.DataBind();
			Rep1.Visible = (dt.Rows.Count > 0);
			lbl_NoData.Visible = (dt.Rows.Count == 0);

			dal.bs_sys = systype;
			dal.bs_empno = sso.empNo;
			bool bHasExists = dal.BulletinSetHasExists();
			cb.Checked = bHasExists;
			btn_manage1.Visible = bIsHasAuthority;
		}
	}
	protected void Rep1_ItemDataBound(object sender, System.Web.UI.WebControls.RepeaterItemEventArgs e)
	{
		if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
		{
			ImageButton btn_more = e.Item.FindControl("btn_more") as ImageButton;
			if (btn_more != null)
			{
				btn_more.Visible = (e.Item.ItemIndex == 4);
			}
		}
	}
	protected void cb_CheckedChanged(object sender, EventArgs e)
	{
		myBulletin dal = new myBulletin();
		dal.bs_sys = systype;
		dal.bs_empno = sso.empNo;
		if (cb.Checked)
			dal.BulletinSetInsert();
		else
			dal.BulletinSetDelete();
	}

	#region Repeater Event, 附件檔

	protected void repFile_ItemCommand(object source, System.Web.UI.WebControls.RepeaterCommandEventArgs e)
	{
		myBulletin dal = new myBulletin();
		if (e.CommandName == "OpenFile")
		{
            if (Session["isPC"] != null)
            {
                if ((string)Session["isPC"] == "true")
                {
                    //PC 可供下載
                    try
                    {
                        dal.ba_id = Int64.Parse(e.CommandArgument.ToString());
                        DataTable dt = dal.BulletinAttfileGetById();
                        DataRow row = null;
                        if (dt == null && dt.Rows.Count == 0)
                        {
                            throw new Exception("檔案不存在，請洽系統管理員");
                        }
                        row = dt.Rows[0];

                        // Check physical directory is exists.
                        string path = string.Format("{0}\\{1}\\{2}-{3}", this.GetConfigString("FilePathString"), systype, Server.HtmlEncode(row["ba_id"].ToString()), Server.HtmlEncode(row["ba_filename"].ToString()));
                        string filename = row["ba_filename"].ToString();

                        Response.Clear();
                        Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(filename, Encoding.UTF8));
                        Response.WriteFile(path);
                        Response.Flush();
                        //Response.End();
                    }
                    catch
                    {
                        Response.Clear();
                        Response.ClearHeaders();
                        Response.Write(" 檔案不存在，請洽系統管理員！");
                        Response.End();
                    }
                }
                else
                {
                    // Mobile不可下載 ";
                    Response.Redirect("~/Engage/Shared/Error.aspx?err=mobile");
                    return;
                }
            }
            else
            {
                // Session Null 狀況不給下載
                // 全都不可下載 ";
                Response.Redirect("~/Engage/Shared/Error.aspx?err=mobile");
                return;
            }
		}
	}

	#endregion

	protected void btn_view_Click(object sender, EventArgs e)
	{
		LinkButton btn = (LinkButton)sender;
		string url = string.Format("{0}?sys={1}&id={2}", ResolveClientUrl("https://amps.itri.org.tw/Engage/Bulletin/BoardView.aspx"), systype, btn.CommandArgument);
		string script = string.Format(@"
					$(document).ready(function () {{ 
						parent.doCloseBulletin('{0}'); 
					}});", url);
		ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
	}
	protected void btn_more_Click(object sender, ImageClickEventArgs e)
	{
		string url = string.Format("{0}?sys={1}", ResolveClientUrl("https://amps.itri.org.tw/Engage/Bulletin/BoardList.aspx"), systype);
		string script = string.Format(@"
					$(document).ready(function () {{ 
						parent.doCloseBulletin('{0}'); 
					}});", url);
		ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
	}
	protected void btn_manage_Click(object sender, EventArgs e)
	{
		string url = string.Format("{0}?sys={1}", ResolveClientUrl("https://amps.itri.org.tw/Engage/Bulletin/BoardList.aspx"), systype);
		string script = string.Format(@"
					$(document).ready(function () {{ 
						parent.doCloseBulletin('{0}'); 
					}});", url);
		ScriptManager.RegisterStartupScript(this.Page, GetType(), "msg", script, true);
	}
}