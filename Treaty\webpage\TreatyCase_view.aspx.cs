﻿using Aspose.Pdf;
using Engage;
using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TreatyCase_view : System.Web.UI.Page
{
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    private string IIf(bool Expression, string TruePart, string FalsePart)
    {
        string ReturnValue = Expression == true ? TruePart : FalsePart;
        return ReturnValue;
    }
    private void ConvertSqlParametersEmptyStringToNull(SqlDataSource dataSource, bool isNull, string mode)
    {
        if (mode == "Select")
        {
            foreach (Parameter parameter in dataSource.SelectParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Insert")
        {
            foreach (Parameter parameter in dataSource.InsertParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Updat")
        {
            foreach (Parameter parameter in dataSource.UpdateParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
        if (mode == "Delete")
        {
            foreach (Parameter parameter in dataSource.DeleteParameters)
                parameter.ConvertEmptyStringToNull = isNull;
        }
    }
    #region 根據案件編號，取得要顯示的按鈕文字
    public string GetEngageNDAText(string strCaseNo)
    {
        string strResult = string.Empty;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return "";

        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳空字串
        {

            case "A":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國外無收入資訊";
                break;
            case "N":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視洽案資訊";
                break;
            case "M":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視NDA資訊";
                break;
            case "F":
                strResult = "<img src='../images/icon-1301.gif' border='0'/>檢視國內無收入資訊";
                break;
            case "R":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視標案資訊";
                break;
            case "C":
                strResult = "<img src='../images/icon-1301.gif' border='0' />檢視工服資訊";
                break;

            default:
                strResult = "";
                break;
        }
        return strResult;
    }
    #endregion

    #region 根據案件編號，取得是否要顯示按鈕
    public bool GetEngageNDAVisible(string strCaseNo)
    {
        bool bResult = false;
        if (strCaseNo.Trim().Length == 0)//如果傳進來的案件編號是空白，則回傳空白字串
            return false;
        btnEngage.Text = GetEngageNDAText(strCaseNo);
        switch (strCaseNo.Trim().Substring(6, 1))//如果按件類別不是 N-洽案、M-NDA、U-國外契約，則回傳false	
        {
            case "A":
                bResult = true;
                break;
            case "N":
                bResult = true;
                break;
            case "M":
                bResult = true;
                break;
            //case "U":
            //    bResult = true;
            //    break;
            case "R":
                bResult = true;
                break;
            case "C":
                bResult = true;
                break;
            case "F":
                bResult = true;
                break;


            default:
                bResult = false;
                break;
        }
        return bResult;
    }
    #endregion

    #region 根據案件編號，取得串到Engage、NDA、國外契約的URL連結
    protected void btnEngage_Click(object sender, EventArgs e)
    {
        string strCaseNo = txtComplexNo.Text.Trim();
        string strCaseNo_C = txtOldContno.Text.Trim();
        //抓取 Web.Config 關於 Engage、NDA、國外契約的URL設定檔
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["EngageURL"].ToString();
        string strNDA_Path = System.Configuration.ConfigurationManager.AppSettings["NDAURL"].ToString();
        string strUN_Path = System.Configuration.ConfigurationManager.AppSettings["UNURL"].ToString();
        string strON_Path = System.Configuration.ConfigurationManager.AppSettings["ONURL"].ToString();
        string strC_Path = System.Configuration.ConfigurationManager.AppSettings["CURL"].ToString();
        string strWinOpen = string.Empty; //宣告開窗的URL字串
        string script = "";
        switch (ViewState["tr_class"].ToString())
        {
            case "N": //洽案/Engage/Base/caseBase.aspx?contno=xxxxx
                strWinOpen = string.Format("{0}/Engage/Base/caseBase.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "R": //標案
                strWinOpen = string.Format("{0}/GPI/BaseData/Case.aspx?contno={1}", strEngage_Path, strCaseNo.Substring(0, 11));
                break;

            case "M": // NDA
                      // strWinOpen = string.Format("{0}/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-",""));
                strWinOpen = string.Format("{0}/NDA/WebPage/nda_BaseView.aspx?nbcontno={1}", strNDA_Path, strCaseNo.Replace("-", ""));
                break;

            case "A": // 國外契約   norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af <https://webdev5.itri.org.tw/norcont/norcont/WebPage/norcont_BaseView.aspx?seqsn=11339bc2-2e14-4a8c-8f56-85f3e11d06af> 
                strWinOpen = string.Format("{0}/WebPage/norcont_BaseView.aspx?contno={1}", strUN_Path, strCaseNo.Replace("-", ""));
                break;
            case "F": // 國內契約  
                strWinOpen = string.Format("{0}/Webpage/norcontIN_baseView.aspx?contno={1}", strON_Path, strCaseNo.Replace("-", ""));
                break;
            case "C": // 工服
                strWinOpen = string.Format("{0}/WebPageIndus/IndustryEditReadOnly.aspx?contno={1}", strC_Path, strCaseNo_C);
                break;

        }
        script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        //script = @" <script> alert('" + strWinOpen + "'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);
        BindData();
    }
    #endregion

    public string GetUserIP()
    {
        string strIP = String.Empty;
        HttpRequest httpReq = HttpContext.Current.Request;
        //test for non-standard proxy server designations of client's IP
        if (httpReq.ServerVariables["HTTP_CLIENT_IP"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_CLIENT_IP"].ToString();
        }
        else if (httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        {
            strIP = httpReq.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        }
        //test for host address reported by the server
        else if
        (
        //if exists
        (httpReq.UserHostAddress.Length != 0)
        &&
        //and if not localhost IPV6 or localhost name
        ((httpReq.UserHostAddress != "::1") || (httpReq.UserHostAddress != "localhost"))
        )
        {
            strIP = httpReq.UserHostAddress;
        }
        //finally, if all else fails, get the IP from a web scrape of another server
        else
        {
            WebRequest request = WebRequest.Create("http://checkip.dyndns.org/");
            using (WebResponse response = request.GetResponse())
            using (StreamReader sr = new StreamReader(response.GetResponseStream()))
            {
                strIP = sr.ReadToEnd();
            }
            //scrape ip from the html
            int i1 = strIP.IndexOf("Address:") + 9;
            int i2 = strIP.LastIndexOf("</body>");
            strIP = strIP.Substring(i1, i2 - i1);
        }
        return strIP;
    }
    //public string SQLInjectionReplace(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​
    //public string SQLInjectionReplaceAll(string inputString)
    //{
    //    //20131217:Hugo(add), SQL Injection 使用規則運算式過濾特殊字元及部份關鍵字
    //    //參考的網址, http://renjin.blogspot.tw/2008/05/sql-injection-attacks-by-example.html
    //    //return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //    return Regex.Replace(inputString, @"\b(exec(ute)?|select|update|insert|delete|drop|create)\b|[;']|(-{2})|(/\*.*\*/)", string.Empty, RegexOptions.IgnoreCase);
    //}​


    protected void Page_Load(object sender, EventArgs e)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        ViewState["empno"] = ssoUser.empNo;
        ViewState["計價進度"] = "";
        //txt_px_name.Attributes.Add("onChange", "Find_empno_kw('txt_px_name',2);");
        txt_promoter_name.Attributes.Add("onChange", "Find_empno_kw('txt_promoter_name',1);");
        ClientScript.GetPostBackEventReference(new PostBackOptions(this.SGV_company));
        //cb_conttype_b0.Checked = false;
        //cb_conttype_b0.Enabled = false;
        //cb_conttype_b1.Checked = false;
        //cb_conttype_b1.Enabled = false;
        //cb_conttype_d4.Checked = false;
        //cb_conttype_d4.Enabled = false;
        //cb_conttype_d5.Checked = false;
        //cb_conttype_d5.Enabled = false;
        //cb_conttype_d7.Checked = false;
        //cb_conttype_d7.Enabled = false;

        cb_conttype_b0.Attributes.Add("disabled", "disabled");
        cb_conttype_b1.Attributes.Add("disabled", "disabled");
        cb_conttype_d4.Attributes.Add("disabled", "disabled");
        cb_conttype_d5.Attributes.Add("disabled", "disabled");
        cb_conttype_d7.Attributes.Add("disabled", "disabled");
        cb_conttype_m.Attributes.Add("disabled", "disabled");
        cb_conttype_c.Attributes.Add("disabled", "disabled");
        rb_conttype_uo.Attributes.Add("disabled", "disabled");
        rb_conttype_ui.Attributes.Add("disabled", "disabled");
        rb_conttype_uo.Attributes.Add("disabled", "disabled");
        rb_conttype_other.Attributes.Add("disabled", "disabled");
        CB_技術授權.Attributes.Add("disabled", "disabled");
        CB_專利授權.Attributes.Add("disabled", "disabled");
        CB_技術與專利授權.Attributes.Add("disabled", "disabled");
        CB_全球.Attributes.Add("disabled", "disabled");
        CB_陸港澳.Attributes.Add("disabled", "disabled");
        CB_特定區域.Attributes.Add("disabled", "disabled");
        CB_韓國.Attributes.Add("disabled", "disabled");
        CB_技術讓與.Attributes.Add("disabled", "disabled");

        ClientScript.GetPostBackEventReference(new PostBackOptions(this.txt_name));

        if (!IsPostBack)
        {
            if (lb_Subtitle.Text == String.Empty)
            {
                Breadcrumb myBreadcrumb = new Breadcrumb();
                lb_Subtitle.Text = myBreadcrumb.Breadcrumbs_string("~/sys_BreadcrumbsFile.xml", "Y");
            }
            if (Request.QueryString["contno"] != null)
            {
                if (!IsNatural_Number(Request.QueryString["contno"].Replace("-", "")) || (Request.QueryString["contno"].Length > 15))
                    Response.Redirect("../danger.aspx");
                ViewState["contno"] = Request.QueryString["contno"].ToString();


                //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SDS_NR.SelectParameters.Clear();
                //SDS_NR.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.SelectCommand = "esp_TreatyCase_contnoToseno";
                //SDS_NR.SelectParameters.Add("contno", SQLInjectionReplaceAll(ViewState["contno"].ToString()));
                //for (int i = 0; i < this.SDS_NR.SelectParameters.Count; i++)
                //{
                //    SDS_NR.SelectParameters[i].ConvertEmptyStringToNull = false;
                //}
                //SDS_NR.DataBind();
                //System.Data.DataView dv_contnoToseno = (DataView)SDS_NR.Select(new DataSourceSelectArguments());
                DataTable dt = new DataTable();
                #region --- query ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;
                    sqlCmd.CommandText = @"esp_TreatyCase_contnoToseno";
                    // --- 避免匯出查詢過久而當掉 --- //
                    sqlCmd.CommandTimeout = 0;
                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@contno", oRCM.SQLInjectionReplaceAll(ViewState["contno"].ToString()));
                    try
                    {
                        sqlConn.Open();
                        SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                        dt = new DataTable();
                        sqlDA.Fill(dt);
                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);
                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
                DataView dv_contnoToseno = dt.DefaultView;
                if (dv_contnoToseno.Count >= 1)
                {
                    if (dv_contnoToseno[0][0].ToString() == "")
                        Response.Redirect("../danger.aspx");
                    else
                    {
                        ViewState["seno"] = dv_contnoToseno[0][0].ToString();
                    }
                }
            }
            if (Request["seno"] != null)//設定為編輯狀態
            {
                int j = 0;
                if (!(int.TryParse(Request["seno"], out j)))
                    Response.Redirect("../danger.aspx");
                ViewState["seno"] = Request["seno"];
            }

            if (ViewState["seno"] == null)
                Response.Redirect("../danger.aspx");

            ViewState["empNo"] = ssoUser.empNo;
            ViewState["tc_degree"] = "";

            //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString; 
            //SDS_auth.SelectParameters.Clear();
            //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_auth.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_Auth");
            //SDS_auth.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_auth.SelectParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
            //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
            //{
            //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_auth.DataBind();
            //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt_base = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_Auth";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt_base = new DataTable();
                    sqlDA.Fill(dt_base);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion

            DataView dv_auth = dt_base.DefaultView;
            if (dv_auth.Count >= 1)
            {
                string str_auth = dv_auth[0][0].ToString();
                if (str_auth == "X")
                    Response.Redirect("../NoAuthRight.aspx");
                ViewState["auth"] = dv_auth[0][0].ToString();
                ViewState["SYS"] = dv_auth[0][1].ToString();
                ViewState["Module"] = dv_auth[0][2].ToString();
            }
            Treaty_log(ViewState["seno"].ToString(), "檢視承辦單", "", ViewState["seno"].ToString(), "treaty\\TreatyCase_view.aspx");
            BindData();
            BindDDL_SeqSn();
            BindNewVer();
            BindData_file();
            BindInspect();
            BindDefer();


            DDL_SeqSn.SelectedValue = ViewState["seno"].ToString();
            LT_tratycase_info.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_info.aspx?seno=" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "' >案件紀錄</a>";
            LT_infoHandel.Text = "<a class='iterm_dymanic_caseInfo' rel='./TreatyCase_infoHandel.aspx?seno=" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次承辦人資訊</a>";
            // LT_historyRecord.Text = "<a class='iterm_dymanic_historyRecord' rel='./TreatyCase_historyRecord.aspx?seno=" + ViewState["seno"].ToString() + "' >歷次修改紀錄</a>";
            txtComplexNo.Attributes.Add("readOnly", "readonly");
            txtOrgAbbrName.Attributes.Add("readOnly", "readonly");
            txt_req_dept.Attributes.Add("readOnly", "readonly");
            txt_contsdate.Attributes.Add("readOnly", "readonly");
            txt_contedate.Attributes.Add("readOnly", "readonly");
            BT_End.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
            BT_End1.Attributes.Add("onclick", "return  confirm('確定要結案 ?');");
            bt_reject.Attributes.Add("onclick", "return  confirm('確定要退回?\\n  <<退回前請知會法務人員>> '   );");
            IB_newVer.Attributes.Add("onclick", "doNewCase('" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "','" + DDL_SeqSn.SelectedItem.Text + "');");
            bt_cancle.Attributes.Add("onclick", "treatyCancle(" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + ");");
            btnDelete.Attributes.Add("onclick", "return  confirm('確定要刪除此編號所有 版件 次 ?');");
            BT_FileUp.Attributes.Add("onclick", "treaty_fileup();");
            BT_AddInspect.Attributes.Add("onclick", "Add_Inspect(" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + ");");
            //if (ViewState["tr_seno"] != null)
            //    BT_trfileup.Attributes.Add("onclick", "treaty_trfileup('" + txtComplexNo.Text + "','" + ViewState["tr_seno"] .ToString()+ "');");

            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            ViewState["isPC"] = "false";
            Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", " <script>reflash_topic('Client', p);</script> ");
            //ScriptManager.RegisterStartupScript(this.Page, GetType(), "platformCheck", "reflash_topic('Client', p);");
            if (ViewState["Module"].ToString() == "V")
            {
                btnDelete.Visible = false;
            }
        }

        if (Request.Params.Get("__EVENTTARGET") == "Client")
        {
            string p = Request.Params.Get("__EVENTARGUMENT").ToString();
            bool isWin, isMac, isLinux, isUnix = false;
            isWin = p.IndexOf("Win") > -1;  //Windows : Win32、Win16
            isMac = p.IndexOf("Mac") > -1;  //MacOS: MacIntel、Macintosh、MacPPC、Mac68K
            isUnix = p.IndexOf("X11") > -1; //Unix
            isLinux = p.IndexOf("Linux") > -1; //Linux: Linux x86_64、Linux x86_64 X11、Linux ppc64

            //Linux 要多加判斷排除，因為行動裝置Android 系統的Platform參數會是 
            //Linux armv7l、Linux armv8l、Linux aarch64、Linux i686(both Chrome on ChromeOS or Linux x86-64)
            if (p.IndexOf("Linux a") > -1 || p.IndexOf("Linux i") > -1)
            {
                isLinux = false;
            }
            if (isWin || isMac || isLinux || isUnix)
            {
                ViewState["isPC"] = "true";
            }
            else
            {
                ViewState["isPC"] = "false";
            }
        }

        #region postback
        if (IsPostBack)
            Bind_sRC_init(ViewState["tr_class"].ToString());

        //lb_keyin_date.Text = DateTime.Now.ToString("yyyyMMdd"); //建檔日期
        if (Request.Params.Get("__EVENTTARGET") == "company_renew")
        {
            BindData_Customer();
        }
        if (Request.Params.Get("__EVENTTARGET") == "file_renew")
        {
            BindData_file();
        }
        if (Request.Params.Get("__EVENTTARGET") == "Inspect_renew")
        {
            //BindInspect();
            //BindData();
            Response.Redirect("./Search_inspect.aspx");
        }
        if (Request.Params.Get("__EVENTTARGET") == "case_renew")
        {
            BindData();
            BindInspect();
        }

        if (Request.Params.Get("__EVENTTARGET") == "valuation_renew")
        {
            Bind計價();
        }

        #endregion        

        setParamFileOpen();
    }

    /// <summary>
    /// 給檔案比對值
    /// </summary>
    void setParamFileOpen()
    {
        SSOUtil.SSOLoginUser sso = new SSOUtil.SSOLoginUser();
        sso.GetEmpInfo();
        //BT_OpenFileCompare.Attributes.Add("key", HttpUtility.UrlEncode("empno=" + sso.empNo + "&議約流水號=" + ViewState["seno"].ToString() + "&議約編號=" + DDL_SeqSn.SelectedValue));
        BT_OpenFileCompare.Attributes.Add("key", HttpUtility.UrlEncode("empno=" + sso.empNo + "&seno=" + ViewState["seno"].ToString() + "&actcontno=" + DDL_SeqSn.SelectedValue));
    }
    private void BindContMoneyType()
    {

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"SELECT code_subtype,subtype_desc FROM treaty_code_table  WHERE code_type='20' and enable='1' order by display_order  ";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        ddlContMoneyType.DataSource = dt;
        ddlContMoneyType.DataTextField = "subtype_desc";
        ddlContMoneyType.DataValueField = "code_subtype";
        ddlContMoneyType.DataBind();
    }
    private void BindData_Customer()
    {
        //SDS_company.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;    
        //SDS_company.SelectParameters.Clear();
        //SDS_company.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_company.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_MultiCustomer_List_by_NOs") ;
        //SDS_company.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_company.SelectParameters.Add("customers", SQLInjectionReplaceAll( this.h_compno.Value.ToString()));
        //SDS_company.SelectParameters.Add("簽約金額", SQLInjectionReplaceAll(ViewState["簽約金額"].ToString()));
        //SDS_company.SelectParameters.Add("mode", SQLInjectionReplaceAll( "view"));
        //SDS_company.DataBind();
        //SGV_company.DataBind();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_MultiCustomer_List_by_NOs";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@customers", oRCM.SQLInjectionReplaceAll(this.h_compno.Value.ToString()));
            sqlCmd.Parameters.AddWithValue("@簽約金額", oRCM.SQLInjectionReplaceAll(ViewState["簽約金額"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view");
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                SGV_company.DataSource = dt;
                SGV_company.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

    }
    public void BindData()
    {

        //SqlCommand oCmd_1 = new SqlCommand();
        //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        //oCmd_1.CommandText = "esp_TreatyCase";
        //oCmd_1.CommandType = CommandType.StoredProcedure;
        //oCmd_1.Parameters.AddWithValue("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("view"));
        //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        //DataSet ds_1 = new DataSet();
        //oda_1.Fill(ds_1, "myTable");
        //DataView dv = ds_1.Tables[0].DefaultView;

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@mode", "view");
            try
            {
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;

        ViewState["簽約金額"] = "0";
        if (dv.Count > 0)
        {
            string str_tr_year = Server.HtmlEncode(dv[0]["tc_year"].ToString().Trim());
            string str_tr_orgcd = Server.HtmlEncode(dv[0]["tc_orgcd"].ToString().Trim());
            string str_tr_class = Server.HtmlEncode(dv[0]["tc_class"].ToString().Trim());
            ViewState["tr_class"] = str_tr_class;
            ViewState["tc_degree"] = dv[0]["tc_degree"].ToString().Trim();
            LT_計價.Text = Server.HtmlEncode(dv[0]["計價"].ToString().Trim());
            if (dv[0]["計價版件次"].ToString().Trim() != "")
                LT_計價版件次.Text = "計價版件次:" + Server.HtmlEncode(dv[0]["計價版件次"].ToString().Trim());
            if ((str_tr_class == "T") && (ViewState["SYS"].ToString().IndexOf("ADM") >= 1))
                btnDelete.Visible = true;

            string str_tr_sn = Server.HtmlEncode(dv[0]["tc_sn"].ToString().Trim());
            string str_tr_ver = Server.HtmlEncode(dv[0]["tc_ver"].ToString().Trim());
            string str_tr_seqsn = Server.HtmlEncode(dv[0]["tc_seqsn"].ToString().Trim());
            ViewState["contno"] = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn;//洽案&標案 號碼
            txtComplexNo.Text = Server.HtmlEncode(string.Format("{0}{1}{2}{3}{4}-{5}", str_tr_year, str_tr_orgcd, str_tr_class, str_tr_sn, str_tr_ver, str_tr_seqsn));//案號
            string str_actcontno = str_tr_year + str_tr_orgcd + str_tr_class + str_tr_sn + str_tr_ver + str_tr_seqsn;
            txtOldContno.Text = Server.HtmlEncode(dv[0]["tc_old_contno"].ToString().Trim());

            btnEngage.Visible = GetEngageNDAVisible(str_actcontno);

            switch (dv[0]["tc_language"].ToString().Trim())
            {
                case "0": LB_language.Text = "其他"; break;
                case "1": LB_language.Text = "中文"; break;
                case "2": LB_language.Text = "英文"; break;
            }

            //#region 需求單位及部門
            //SqlDataSource SDS_emp = new SqlDataSource();
            //SDS_emp.ConnectionString = ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            ////SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno in( select tr_promoter_no from  treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn+rtrim(tr_ver)+tr_seqsn ='" + str_actcontno + "' )";
            //SDS_emp.SelectCommand = " select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno ='" + SQLInjectionReplaceAll(dv[0]["tc_promoter_no"].ToString().Trim() )+ "'  ";
            //SDS_emp.DataBind();
            //System.Data.DataView dv_emp = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
            //if (dv_emp.Count >= 1)
            //{
            //    txt_req_dept.Text = dv_emp[0]["com_deptid"].ToString().Trim();
            //    txt_promoter_name.Text = dv_emp[0]["com_cname"].ToString().Trim();
            //    txt_promoter_empno.Value = dv_emp[0]["com_empno"].ToString().Trim();
            //    txtTel.Text = dv_emp[0]["com_telext"].ToString().Trim();
            //    txtOrgAbbrName.Text = dv_emp[0]["orgName"].ToString().Trim();
            //    ViewState["com_orgcd"]= dv_emp[0]["com_orgcd"].ToString().Trim();
            //}
            //#endregion

            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"select  rtrim(com_empno) com_empno ,rtrim(com_cname) com_cname,rtrim(com_telext) com_telext,com_orgcd,com_deptcd,com_deptid,com_mailadd,(select dep_deptname from common..depcod where dep_deptid =com_deptid )dept_name ,(select org_abbr_chnm2 from common..orgcod where org_orgcd=com_orgcd) orgName  from common..comper where com_empno =@empno";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(Server.HtmlEncode(dv[0]["tc_promoter_no"].ToString()).Trim()));
                try
                {
                    sqlConn.Open();
                    DataTable dt_emp = new DataTable();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt_emp);
                    DataView dv_emp = dt_emp.DefaultView;
                    if (dv_emp.Count >= 1)
                    {
                        txt_req_dept.Text = Server.HtmlEncode(dv_emp[0]["com_deptid"].ToString().Trim());
                        txt_promoter_name.Text = Server.HtmlEncode(dv_emp[0]["com_cname"].ToString().Trim());
                        txt_promoter_empno.Value = Server.HtmlEncode(dv_emp[0]["com_empno"].ToString().Trim());
                        txtTel.Text = Server.HtmlEncode(dv_emp[0]["com_telext"].ToString().Trim());
                        txtOrgAbbrName.Text = Server.HtmlEncode(dv_emp[0]["orgName"].ToString().Trim());
                        ViewState["com_orgcd"] = Server.HtmlEncode(dv_emp[0]["com_orgcd"].ToString().Trim());
                    }
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion

            txt_name.Text = Server.HtmlEncode(dv[0]["tc_name"].ToString().Trim());//洽案（契約名稱）

            #region 契約預估金額
            BindContMoneyType();
            ddlContMoneyType.SelectedValue = IIf(dv[0]["tc_money_type"].ToString().Trim() == "", "TWD", dv[0]["tc_money_type"].ToString().Trim());
            LB_ContMoneyType.Text = System.Web.HttpUtility.HtmlEncode(ddlContMoneyType.SelectedItem.Text);
            txtContMoney.Text = Server.HtmlEncode(string.Format("{0:n}", decimal.Parse(dv[0]["tc_money"].ToString().Trim())));
            TB_money_rate.Text = Server.HtmlEncode(dv[0]["tc_money_rate"].ToString().Trim());
            ViewState["簽約金額"] = (dv[0]["tc_money"].ToString().Trim() == "0" ? 0 : decimal.Parse(dv[0]["tc_money"].ToString().Trim()))
                                  * (dv[0]["tc_money_rate"].ToString().Trim() == "" ? 1 : decimal.Parse(dv[0]["tc_money_rate"].ToString().Trim()));
            #endregion

            #region 客戶
            h_compno.Value = dv[0]["tc_compidno_all"].ToString().Trim().Replace("㊣", ",");//簽約對象(多)
            BindData_Customer();
            #endregion
            #region 案件性質
            switch (dv[0]["tc_class"].ToString().Trim())
            {
                case "A":
                    #region A
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_uo.Checked = true;
                    #endregion
                    break;
                case "C":
                    #region C
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    cb_conttype_c.Checked = true;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_uo.Checked = false;
                    #endregion
                    break;

                case "M":
                    #region M
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Checked = true;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    if (dv[0]["tc_case_flag"].ToString().Trim() == "1")
                        lb_standar_flag.Visible = true;
                    #endregion
                    break;
                case "N":
                    #region N
                    if (dv[0]["tc_conttype_b0"].ToString().Trim() == "1")
                        cb_conttype_b0.Checked = true;

                    if (dv[0]["tc_conttype_b1"].ToString().Trim() == "1")
                        cb_conttype_b1.Checked = true;

                    if (dv[0]["tc_conttype_d4"].ToString().Trim() == "1")
                        cb_conttype_d4.Checked = true;

                    if (dv[0]["tc_conttype_d5"].ToString().Trim() == "1")
                        cb_conttype_d5.Checked = true;

                    if (dv[0]["tc_conttype_d7"].ToString().Trim() == "1")
                        cb_conttype_d7.Checked = true;

                    if (dv[0]["tc_conttype_ns"].ToString().Trim() == "1")
                        cb_conttype_ns.Checked = true;

                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    if (dv[0]["tc_amend"].ToString().Trim().Trim() != "0")
                    {
                        lb_Amend_Show.Visible = true;
                        spanContractEdit.Visible = true;
                        rbl_amend.SelectedValue = dv[0]["tc_amend"].ToString().Trim().Trim();
                        LB_amend.Text = System.Web.HttpUtility.HtmlEncode(rbl_amend.SelectedItem.Text);
                        txtamend_other_desc.Text = dv[0]["tc_amend_other_desc"].ToString().Trim().Trim();
                    }
                    #endregion
                    break;
                case "R":
                    #region R
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = true;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;
                case "S":
                    #region S
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_bd.Checked = true;
                    rb_conttype_other.Enabled = false;
                    #endregion
                    break;
                case "T":

                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Checked = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_other.Enabled = false;
                    rb_conttype_other.Checked = true;
                    PL_CoPromoter.Visible = true;
                    //h_px_empno.Value = dv[0]["tc_promoter_no_other"].ToString().Trim();

                    //#region T
                    //SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  tr_promoter_no_other   FROM treaty_requisition  where tr_seno  = '" + SQLInjectionReplaceAll(dv[0]["tr_seno"].ToString() )+ "' )";
                    //SDS_emp.DataBind();
                    //System.Data.DataView dv_pno = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
                    //if (dv_pno.Count >= 1)
                    //{
                    //        LB_adm.Text = dv_pno[0]["cname"].ToString().Trim();
                    //}

                    //if (dv[0]["tc_org_adm"].ToString() == "1")
                    //{
                    //    SDS_emp.SelectCommand = "select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = '" + SQLInjectionReplaceAll(str_tr_orgcd )+ "' )";
                    //    SDS_emp.DataBind();
                    //    System.Data.DataView dv_px = (DataView)SDS_emp.Select(new DataSourceSelectArguments());
                    //    if (dv_px.Count >= 1)
                    //    {
                    //        if (dv[0]["tc_org_adm"].ToString().Trim() != "")
                    //        {
                    //            PH_rb_adm.Visible = true;
                    //            LB_adm_text.Text = dv_px[0]["cname"].ToString().Trim();
                    //        }
                    //    }
                    //}
                    //#endregion
                    #region --- query ---
                    DataTable dt2 = new DataTable();
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.Text;
                        sqlCmd.CommandText = @"select rtrim(com_cname) cname   from common..comper where com_empno in( SELECT  tr_promoter_no_other   FROM treaty_requisition  where tr_seno =@seno)";
                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;
                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(Server.HtmlEncode(dv[0]["tr_seno"].ToString().Trim())));
                        try
                        {
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            sqlDA.Fill(dt2);
                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );

                            oRCM.ErrorExceptionDataToDB(logMail);
                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }
                    #endregion
                    DataView dv_pno = dt2.DefaultView;
                    if (dv_pno.Count >= 1)
                    {
                        LB_adm.Text = Server.HtmlEncode(dv_pno[0]["cname"].ToString().Trim());
                    }

                    if (dv[0]["tc_org_adm"].ToString() == "1")
                    {
                        #region --- query ---
                        DataTable dt3 = new DataTable();
                        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                        {
                            SqlCommand sqlCmd = new SqlCommand();
                            sqlCmd.Connection = sqlConn;
                            sqlCmd.CommandType = CommandType.Text;
                            sqlCmd.CommandText = @"select rtrim(com_cname)cname   from common..comper where com_empno in(SELECT  adm_empno  FROM  treaty_buztbl_adm_other  where adm_org = @org)";
                            // --- 避免匯出查詢過久而當掉 --- //
                            sqlCmd.CommandTimeout = 0;
                            sqlCmd.Parameters.Clear();
                            sqlCmd.Parameters.AddWithValue("@org", oRCM.SQLInjectionReplaceAll(str_tr_orgcd));
                            try
                            {
                                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                                sqlDA.Fill(dt3);
                            }
                            catch (Exception ex)
                            {
                                // --- 執行異常通報 --- //
                                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                    Request,
                                    Response,
                                    ex
                                    );
                                oRCM.ErrorExceptionDataToDB(logMail);
                            }
                            finally
                            {
                                sqlConn.Close();
                            }
                        }
                        #endregion
                        DataView dv_px = dt3.DefaultView;
                        if (dv_px.Count >= 1)
                        {
                            if (dv[0]["tc_org_adm"].ToString().Trim() != "")
                            {
                                PH_rb_adm.Visible = true;
                                LB_adm_text.Text = Server.HtmlEncode(dv_px[0]["cname"].ToString().Trim());
                            }
                        }
                    }
                    break;
                case "F":
                    #region F
                    cb_conttype_b0.Enabled = false;
                    cb_conttype_b1.Enabled = false;
                    cb_conttype_d4.Enabled = false;
                    cb_conttype_d5.Enabled = false;
                    cb_conttype_d7.Enabled = false;
                    cb_conttype_rb.Enabled = false;
                    cb_conttype_m.Enabled = false;
                    cb_conttype_c.Enabled = false;
                    rb_conttype_uo.Enabled = false;
                    rb_conttype_ui.Enabled = false;
                    rb_conttype_ui.Checked = true;

                    #endregion
                    break;

            }
            BindContType(dv[0]["tc_conttype"].ToString().Trim());
            #endregion

            #region 契約期間
            txt_contsdate.Text = Server.HtmlEncode(dv[0]["tc_contsdate"].ToString().Trim()).Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_contsdate"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            txt_contedate.Text = Server.HtmlEncode(dv[0]["tc_contedate"].ToString().Trim()).Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_contedate"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            txt_confirm_date.Text = Server.HtmlEncode(dv[0]["tc_confirm_date"].ToString()).Trim().Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_confirm_date"].ToString()).Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";
            #endregion

            Bind_sRC(str_tr_class, dv[0]["tc_sRC_ver"].ToString().Trim());
            ViewState["ver"] = dv[0]["tc_sRC_ver"].ToString().Trim();
            if (dv[0]["tc_otherrequire_ver"].ToString().Trim() == "1")
            {
                PL_olderVer.Visible = true;
                PL_olderVer2.Visible = true;
                #region 智權歸屬
                switch (dv[0]["tc_ipb"].ToString())
                {
                    case "0":
                        rb_ipb_itri.Checked = true;
                        break;
                    case "1":
                        rb_ipb_coparcenary.Checked = true;
                        txt_ipbi_percent.Text = Server.HtmlEncode(dv[0]["tc_ipbi_percent"].ToString().Trim());
                        txt_ipbc_percent.Text = Server.HtmlEncode(dv[0]["tc_ipbc_percent"].ToString().Trim());
                        break;
                    case "2":
                        rb_ipb_customer.Checked = true;
                        break;
                    case "3":
                        rb_ipb_other.Checked = true;
                        txt_ipb_other_desc.Text = Server.HtmlEncode(dv[0]["tc_ipb_other_desc"].ToString().Trim());
                        break;
                }
                #endregion
                txtSignReason.Text = Server.HtmlEncode(dv[0]["tc_sign_reason"].ToString().Trim());
                txt_ip_apply.Text = Server.HtmlEncode(dv[0]["tc_ip_apply"].ToString().Trim());
                txt_income_divvy.Text = Server.HtmlEncode(dv[0]["tc_income_divvy"].ToString().Trim());
                #region 責任範圍
                switch (dv[0]["tc_duty"].ToString())
                {
                    case "0":
                        rb_duty_plain.Checked = true;
                        txt_duty_plain_budget.Text = Server.HtmlEncode(dv[0]["tc_duty_plain_budget"].ToString().Trim());
                        break;
                    case "1":
                        rb_duty_capital.Checked = true;
                        txt_duty_capitalsum.Text = Server.HtmlEncode(dv[0]["tc_duty_capitalsum"].ToString().Trim());
                        break;
                    case "2":
                        rb_duty_assumpsit.Checked = true;
                        break;
                    case "3":
                        rb_duty_other.Checked = true;
                        txt_duty_other_desc.Text = Server.HtmlEncode(dv[0]["tc_duty_other_desc"].ToString().Trim());
                        break;
                }
                #endregion
            }
            else
            {
                PL_olderVer.Visible = false;
                PL_olderVer2.Visible = false;
            }
            #region 其他需求
            Bind_oRC(ViewState["seno"].ToString(), dv[0]["tc_otherrequire_ver"].ToString().Trim());
            #endregion

            if (dv[0]["tc_degree"].ToString().Trim() == "C")
            {
                PH_degree_C.Visible = true;
                txtRequestCancel.Text = Server.HtmlEncode(dv[0]["tc_request_cancel"].ToString().Trim());
            }

            lb_assign_name.Text = Server.HtmlEncode(dv[0]["tc_assign_name"].ToString());//分案主管
            lb_assign_date.Text = Server.HtmlEncode(dv[0]["tc_assign_date"].ToString().Trim()).Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_assign_date"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";  //分案日期
            lb_handle_name.Text = Server.HtmlEncode(dv[0]["tc_handle_name"].ToString());//法務承辦人姓名
            lb_handle_empno.Text = Server.HtmlEncode(dv[0]["tc_handle_empno"].ToString());//法務承辦人工號
            lb_handle_ext.Text = Server.HtmlEncode(dv[0]["tc_handle_ext"].ToString());//法務承辦人分機
            lb_expect_close_date.Text = Server.HtmlEncode(dv[0]["tc_prefinish_date"].ToString().Trim()).Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_prefinish_date"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";//預估完成日
            if (dv[0]["tc_case_closedate"].ToString().Trim() == "")
                lb_case_closedate.Text = "";
            else
                lb_case_closedate.Text = Server.HtmlEncode(dv[0]["tc_case_closedate"].ToString()).Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_case_closedate"].ToString().Trim()), "yyyyMMdd", null).ToString("yyyy/MM/dd") : "";//需求結件日期
            lb_process_date.Text = Server.HtmlEncode(dv[0]["tc_process_date"].ToString());//處理天數
            if (dv[0]["tc_degree"].ToString().Trim() != "Z")//如果狀態不是結件，不秀
            {
                if (lb_process_date.Text.Trim() == "0")
                    lb_process_date.Text = "";
                if (lb_contract_count.Text.Trim() == "0")
                    lb_contract_count.Text = "";
            }
            else
                txt_betsum.Text = dv[0]["tc_betsum"].ToString().Trim();//   Server.HtmlEncode(dv[0]["tc_betsum"].ToString().Trim());

            lb_contract_count.Text = dv[0]["tc_contract_count"].ToString(); //產出契約數
            if (!((dv[0]["tc_degree"].ToString().Trim() == "Z") || (dv[0]["tc_degree"].ToString().Trim() == "C")))
            {
                if ((str_tr_ver + str_tr_seqsn == "A01") && (ViewState["auth"].ToString() == "W" || ViewState["auth"].ToString() == "A"))
                {
                    bt_reject.Visible = true;
                    txt_reject.Visible = true;
                }

                //if (dv[0]["tc_promoter_no"].ToString().Trim() == ViewState["empNo"].ToString()  )
                //  bt_cancle.Visible = true;

            }
            if (((dv[0]["tc_degree"].ToString().Trim() == "Z") || (dv[0]["tc_degree"].ToString().Trim() == "C")))
            {
                //BT_Print_Excel.Visible = true;
                //BT_Print_Tag.Visible = true;
                BT_Print.Visible = true;
                //BT_Print_Excel1.Visible = true;
                //BT_Print_Tag1.Visible = true;
                //BT_Print1.Visible = true;
                BT_Print_source.Visible = true;
                BT_Satisf.Visible = false;
                BT_Satisf.Attributes.Add("onclick", "SatisfView('" + Server.HtmlEncode(ViewState["seno"].ToString()) + "');");
                BT_Satisf2.Visible = false;
                BT_Satisf2.Attributes.Add("onclick", "SatisfView('" + Server.HtmlEncode(ViewState["seno"].ToString()) + "');");
                BT_AddInspect.Visible = false;
            }
            if ((ViewState["SYS"].ToString().IndexOf("LAW") >= 0) && (dv[0]["tc_degree"].ToString().Trim() == "Z"))
                BT_FileUp.Visible = true; //結案檔案上傳

            if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0)
                BT_OpenFileCompare.Visible = true;

            if ((ViewState["auth"].ToString() == "R" || ViewState["SYS"].ToString().IndexOf("ADM") >= 1) && (dv[0]["tc_degree"].ToString().Trim() == "0"))
            {
                //BT_trfileup.Visible = true;
                ViewState["tr_seno"] = dv[0]["tr_seno"].ToString().Trim();
                //BT_trfileup.Attributes.Add("onclick", "treaty_trfileup('" + str_actcontno + "','" + dv[0]["tr_seno"].ToString().Trim() + "');");
            }
            //else
            //    BT_trfileup.Visible = false;

            if ((ViewState["auth"].ToString() == "W" || ViewState["SYS"].ToString().IndexOf("ADM") >= 1) && (dv[0]["tc_degree"].ToString().Trim() == "0" || dv[0]["tc_degree"].ToString().Trim() == "8"))
            {
                if ((dv[0]["tc_status"].ToString() == "5") || (dv[0]["tc_status"].ToString() == "7") || (dv[0]["tc_status"].ToString() == "9"))
                {
                    //SDS_auth.SelectParameters.Clear();
                    //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                    //SDS_auth.SelectCommand = SQLInjectionReplaceAll( "esp_treatyCase_Inspect_getUnInspect");
                    //SDS_auth.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()) );
                    //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                    //{
                    //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                    //}
                    //SDS_auth.DataBind();
                    //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());
                    #region --- query ---
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        DataTable dtSource = new DataTable();
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;
                        sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";
                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;
                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        try
                        {
                            sqlConn.Open();
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dtSource = new DataTable();
                            sqlDA.Fill(dtSource);
                            DataView dv_auth = dtSource.DefaultView;
                            if (dv_auth.Count >= 1)
                            {
                                if (dv_auth[0][0].ToString() == "0")
                                {
                                    BT_End.Visible = true; //沒有審查人 || 審查人都審查完
                                    BT_End1.Visible = true; //沒有審查人 || 審查人都審查完
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );
                            oRCM.ErrorExceptionDataToDB(logMail);
                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }
                    #endregion

                    if (dv[0]["tc_degree"].ToString().Trim() != "8")
                    {
                        btEdit.Visible = true;
                        btEdit2.Visible = true;
                        string script_alert = "<script language='javascript'> $('#btEdit').validationEngine('showPrompt', '★可執行案件編輯!','','',true);$('#btEdit').click(function () { $('#btEdit').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "bxtEdit", script_alert);
                        string script_alert2 = "<script language='javascript'> $('#btEdit2').validationEngine('showPrompt', '★可執行案件編輯!','','',true);$('#btEdit').click(function () { $('#btEdit').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "bxtEdit2", script_alert2);
                    }
                    else
                    {
                        string script_alert = "<script language='javascript'> $('#BT_End').validationEngine('showPrompt', '★請執行結案通知!','','',true); $('#BT_End').click(function () { $('#BT_End').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "BxT_End", script_alert);
                        string script_alert1 = "<script language='javascript'> $('#BT_End1').validationEngine('showPrompt', '★請執行結案通知!','','',true); $('#BT_End1').click(function () { $('#BT_End1').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                        ClientScript.RegisterStartupScript(this.GetType(), "BxT_End1", script_alert1);

                    }
                    bt_cancle.Visible = true;
                }
                if ((str_tr_class == "T") && (ViewState["SYS"].ToString().IndexOf("ADM") >= 1))
                    btnDelete.Visible = true;

            }

            if ((ViewState["auth"].ToString() == "R") && ((dv[0]["tc_status"].ToString() == "5") || (dv[0]["tc_status"].ToString() == "7") || (dv[0]["tc_status"].ToString() == "9")))
            {
                bt_cancle.Visible = true;
            }

            if ((ViewState["SYS"].ToString().IndexOf("ADM") >= 1) && ((dv[0]["tc_status"].ToString() == "5") || (dv[0]["tc_status"].ToString() == "7") || (dv[0]["tc_status"].ToString() == "9")))
            {
                bt_cancle.Visible = true;
            }

            if ((dv[0]["tc_assign_empno"].ToString() == ViewState["empNo"].ToString() || ViewState["auth"].ToString() == "A") && (dv[0]["tc_degree"].ToString().Trim() != "Z"))
            {
                if ((dv[0]["tc_degree"].ToString().Trim() == "Z") || (dv[0]["tc_degree"].ToString().Trim() == "4"))
                {
                    BT_caseAssign0.Visible = false;
                    BT_caseAssign.Visible = false;
                }

                else
                {
                    BT_caseAssign0.Visible = true;
                    BT_caseAssign.Visible = true;
                }

                BT_caseAssign0.Attributes.Add("onclick", "treatyCaseAssign(" + ViewState["seno"].ToString() + ");");
                BT_caseAssign.Attributes.Add("onclick", "treatyCaseAssign(" + ViewState["seno"].ToString() + ");");
                if (dv[0]["tc_status"].ToString().Trim() == "3")
                {
                    string script_alert = "<script language='javascript'> $('#BT_caseAssign').validationEngine('showPrompt', '★請執行案件指派!','','',true); $('#BT_caseAssign').click(function () { $('#BT_caseAssign').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "BxT_caseAssign", script_alert);
                    string script_alert0 = "<script language='javascript'> $('#BT_caseAssign0').validationEngine('showPrompt', '★請執行案件指派!','','',true); $('#BT_caseAssign0').click(function () { $('#BT_caseAssign0').validationEngine('hide'); });  $('.formErrorContent,.blackPopup,.formErrorArrow div').validationEngine().css({background : '#4b41dd'}); </script>";
                    ClientScript.RegisterStartupScript(this.GetType(), "BxT_caseAssign0", script_alert0);
                }
            }

            if ((dv[0]["tc_status"].ToString().Trim() == "5") && (dv[0]["tc_handle_empno"].ToString() == ViewState["empNo"].ToString()))
            {   //當法務承辦人進來檢視案件時,案件狀態由 5:待法務讀取 --> 7:處理中
                //SDS_NR.UpdateParameters.Clear();
                //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.UpdateCommand = SQLInjectionReplaceAll("esp_TreatyCase_status_update");
                //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplace( ViewState["seno"].ToString()));
                //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplace( dv[0]["tc_degree"].ToString().Trim()));
                //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplace( "7"));
                //SDS_NR.Update();
                #region --- modify ---

                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;
                    sqlCmd.CommandText = @"esp_TreatyCase_status_update";
                    sqlCmd.CommandTimeout = 0;
                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@tc_degree", oRCM.SQLInjectionReplaceAll(dv[0]["tc_degree"].ToString().Trim()));
                    sqlCmd.Parameters.AddWithValue("@tc_status", "7");
                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );
                        oRCM.ErrorExceptionDataToDB(logMail);
                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
            }

            LT_historyRecord.Text = "";
            if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0 || ViewState["SYS"].ToString().IndexOf("HGroup") >= 0)
            {
                LT_historyRecord.Text = "<a class='iterm_dymanic_historyRecord' rel='./TreatyCase_historyRecord.aspx?seno=" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + "' >歷次修改紀錄</a>";
                txt_betsum.Text = dv[0]["tc_betsum"].ToString().Trim();
                PL_tc_manage_note.Visible = true;
                PL_Inspect.Visible = true;
                PH_是否計價.Visible = true;
                PH_法務內部資訊.Visible = true;
                if ((dv[0]["tc_degree"].ToString().Trim() == "4")) //審查中
                {
                    btEdit.Visible = false;
                    bt_reject.Visible = false;
                    txt_reject.Visible = false;

                    //SDS_auth.SelectParameters.Clear();
                    //SDS_auth.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
                    //SDS_auth.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_Inspect_getUser");
                    //SDS_auth.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()) );
                    //for (int i = 0; i < this.SDS_auth.SelectParameters.Count; i++)
                    //{
                    //    SDS_auth.SelectParameters[i].ConvertEmptyStringToNull = false;
                    //}
                    //SDS_auth.DataBind();
                    //System.Data.DataView dv_auth = (DataView)SDS_auth.Select(new DataSourceSelectArguments());

                    #region --- query ---
                    DataTable dtSource = new DataTable();
                    using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                    {
                        SqlCommand sqlCmd = new SqlCommand();
                        sqlCmd.Connection = sqlConn;
                        sqlCmd.CommandType = CommandType.StoredProcedure;
                        sqlCmd.CommandText = @"esp_TreatyCase_Inspect_getUser";
                        // --- 避免匯出查詢過久而當掉 --- //
                        sqlCmd.CommandTimeout = 0;
                        sqlCmd.Parameters.Clear();
                        sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                        try
                        {
                            sqlConn.Open();
                            SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                            dtSource = new DataTable();
                            sqlDA.Fill(dtSource);


                        }
                        catch (Exception ex)
                        {
                            // --- 執行異常通報 --- //
                            RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                                ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                                Request,
                                Response,
                                ex
                                );
                            oRCM.ErrorExceptionDataToDB(logMail);
                        }
                        finally
                        {
                            sqlConn.Close();
                        }
                    }
                    #endregion
                    DataView dv_auth = dtSource.DefaultView;
                    if (dv_auth.Count > 0)
                    {
                        if ((ViewState["empNo"].ToString().Trim() == dv_auth[0][0].ToString().Trim()))
                        {
                            BT_Inspect.Visible = true;
                            BT_Inspect2.Visible = true;
                            string script_alert = "<script language='javascript'> $('#BT_Inspect').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#BT_Inspect').click(function () { $('#BT_Inspect').validationEngine('hide'); })</script>";
                            ClientScript.RegisterStartupScript(this.GetType(), "BxT_Inspect", script_alert);
                            string script_alert2 = "<script language='javascript'> $('#BT_Inspect2').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#BT_Inspect2').click(function () { $('#BT_Inspect2').validationEngine('hide'); })</script>";
                            ClientScript.RegisterStartupScript(this.GetType(), "BxT_Inspect2", script_alert2);

                            BT_Inspect.Attributes.Add("onclick", "treaty_Inspect(" + ViewState["seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
                            BT_Inspect2.Attributes.Add("onclick", "treaty_Inspect(" + ViewState["seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
                        }
                        else
                        {
                            BT_Inspect.Visible = false;
                            BT_Inspect2.Visible = false;
                        }
                        if (ViewState["SYS"].ToString() == "LAW_ADM")
                        {
                            BT_Inspect.Visible = true;
                            BT_Inspect2.Visible = true;
                            string script_alert = "<script language='javascript'> $('#BT_Inspect').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#BT_Inspect').click(function () { $('#BT_Inspect').validationEngine('hide'); })</script>";
                            ClientScript.RegisterStartupScript(this.GetType(), "BxT_Inspect", script_alert);
                            string script_alert2 = "<script language='javascript'> $('#BT_Inspect2').validationEngine('showPrompt', '★請執行案件審查!','','',true); $('#BT_Inspect2').click(function () { $('#BT_Inspect2').validationEngine('hide'); })</script>";
                            ClientScript.RegisterStartupScript(this.GetType(), "BxT_Inspect2", script_alert2);

                            BT_Inspect.Attributes.Add("onclick", "treaty_Inspect(" + ViewState["seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
                            BT_Inspect2.Attributes.Add("onclick", "treaty_Inspect(" + ViewState["seno"].ToString() + "," + dv_auth[0][1].ToString() + " )");
                        }
                    }
                }
                else
                {
                    BT_Inspect.Visible = false;
                    BT_Inspect2.Visible = false;
                }
            }

            Bind_cop(ViewState["seno"].ToString());
            txtManageNote.Text = dv[0]["tc_manage_note"].ToString().Trim();
            //SDS_DDL_degree.SelectCommand = "exec esp_treatyCase_codetable  '' ,'08' ";
            //SDS_DDL_degree.DataBind();
            //DDL_Degree.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                DataTable dt_ddl = new DataTable();
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"exec esp_treatyCase_codetable  '' ,'08' ";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt_ddl);
                    DDL_Degree.DataSource = dt_ddl;
                    DDL_Degree.DataBind();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
            DDL_Degree.SelectedValue = dv[0]["tc_degree"].ToString().Trim();//進度
            LT_L_Degree.Text = System.Web.HttpUtility.HtmlEncode(DDL_Degree.SelectedItem.Text);

            if (dv[0]["tc_send_datetime"].ToString().Trim().Length > 0)//送件日期
            {
                DateTime dTime = DateTime.Parse(dv[0]["tc_send_datetime"].ToString().Trim());
                lb_send_date.Text = dTime.ToString("yyyy/MM/dd");
            }
            lb_modify_emp_name.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_name"].ToString());//修改人
            lb_modify_emp_no.Text = Server.HtmlEncode(dv[0]["tc_modify_emp_no"].ToString());//修改工號
            lb_modify_date.Text = Server.HtmlEncode(dv[0]["tc_modify_date"].ToString()).Trim().Length > 0 ? DateTime.ParseExact(Server.HtmlEncode(dv[0]["tc_modify_date"].ToString()).Trim(), "yyyyMMdd", null).ToString("yyyy/MM/dd") : ""; //修改日期
            if (Request.ServerVariables["HTTP_VIA"] != null)
            {
                ViewState["xIP"] = Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
            }
            else
            {
                ViewState["xIP"] = Request.ServerVariables["REMOTE_ADDR"].ToString();
            }
            Treaty_log(ViewState["seno"].ToString(), "檢視承辦單", "", "", "treaty\\TreatyCase_View.aspx");
            if (dv[0]["tc_擬約幫手"].ToString() == "1")
            {
                LT_擬約幫手.Text = "<font color='red'><b>【擬約幫手】</b></font>";
            }
            if (dv[0]["tc_公文編號"].ToString() != "")
            {
                LK_公文文號.Text = dv[0]["tc_公文編號"].ToString();
                LK_公文文號.Attributes.Add("onclick", "doc_attach('" + Server.HtmlEncode(dv[0]["tc_公文編號"].ToString()) + "');");
                LK_公文文號.Visible = true;
            }
            else
            {
                LK_公文文號.Visible = false;
            }
            if (dv[0]["tc_急件"].ToString().Trim() == "1")
            {
                CB_急件.Checked = true;
                TB_急件原因.Text = dv[0]["tc_急件原因"].ToString();
            }

            if (dv[0]["tc_成果有特殊限制者"].ToString().Trim() == "1")
            {
                CB_成果有特殊限制者.Checked = true;
                TB_成果有特殊限制者_說明.Text = Server.HtmlEncode(dv[0]["tc_成果有特殊限制者_說明"].ToString().Trim());
            }
            if (dv[0]["tc_特殊費用負擔"].ToString().Trim() == "1")
            {
                CB_特殊費用負擔.Checked = true;
                TB_特殊費用負擔原因.Text = Server.HtmlEncode(dv[0]["tc_特殊費用負擔原因"].ToString());
                TB_特殊費用負擔原因.Visible = true;
            }
            else
                TB_特殊費用負擔原因.Visible = false;


            //20240624
            if (dv[0]["tc_核心關鍵技術"].ToString().Trim() == "1")
            {
                PL_關鍵技術項目.Visible = true;
                LB_核心關鍵技術_列管迄日.Text = Server.HtmlEncode(dv[0]["tc_核心關鍵技術_列管迄日"].ToString().Trim());
                TB_核心關鍵技術_說明.Text = Server.HtmlEncode(dv[0]["tc_核心關鍵技術_說明"].ToString().Trim());
            }
            else
                PL_關鍵技術項目.Visible =false;

            //20210901
            if (dv[0]["tc_技術授權"].ToString().Trim() == "1")
            {
                CB_技術授權.Checked = true;
            }
            if (dv[0]["tc_專利授權"].ToString().Trim() == "1")
            {
                CB_專利授權.Checked = true;
            }
            if (dv[0]["tc_技術與專利授權"].ToString().Trim() == "1")
            {
                CB_技術與專利授權.Checked = true;
            }
            if (dv[0]["tc_全球"].ToString().Trim() == "1")
            {
                CB_全球.Checked = true;
            }
            if (dv[0]["tc_陸港澳"].ToString().Trim() == "1")
            {
                CB_陸港澳.Checked = true;
            }
            if (dv[0]["tc_特定區域"].ToString().Trim() == "1")
            {
                CB_特定區域.Checked = true;
            }
            if (dv[0]["tc_韓國"].ToString().Trim() == "1")
            {
                CB_韓國.Checked = true;
            }
            if (dv[0]["tc_技術讓與"].ToString().Trim() == "1")
            {
                CB_技術讓與.Checked = true;
            }
            if (dv[0]["tc_計價"].ToString().Trim() == "Y" || dv[0]["tc_計價"].ToString().Trim() == "C")
            {
                DDL_計價.SelectedValue = Server.HtmlEncode(dv[0]["tc_計價"].ToString().Trim());
                LB_計價說明.Text = Server.HtmlEncode(dv[0]["tc_計價說明"].ToString());
                if (ViewState["Module"].ToString().IndexOf("V") >= 0)
                    Bind計價();
                if (ViewState["Module"].ToString().IndexOf("I") >= 0 && dv[0]["tc_計價"].ToString().Trim() == "Y")
                    Bind計價();

            }

            if (ViewState["Module"].ToString() == "I" && dv[0]["tc_計價"].ToString().Trim() == "C")
            {
                PL_Inspect_計價.Visible = false;
            }
        }
        else
        {
            Response.Redirect("../danger.aspx");
        }
        //ds_1.Dispose();
        //oCmd_1.Dispose();
        //oda_1.Dispose();
    }
    public void BindNewVer()
    {
        if ((ViewState["tr_class"].ToString() == "M") || (ViewState["tr_class"].ToString() == "A") || (ViewState["tr_class"].ToString() == "F"))
            IB_newVer.Visible = false;
        else
        {

            //SDS_ver.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString; 
            //SDS_ver.SelectParameters.Clear();
            //SDS_ver.SelectCommandType = SqlDataSourceCommandType.Text;
            //SDS_ver.SelectCommand = " select count(tr_seno) from treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn =@contno and tr_status in ('2','E') ";
            //SDS_ver.SelectParameters.Add("contno", SQLInjectionReplaceAll(DDL_SeqSn.SelectedItem.ToString().Substring(0, 11)));
            //for (int i = 0; i < this.SDS_ver.SelectParameters.Count; i++)
            //{
            //    SDS_ver.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //this.SDS_ver.DataBind();
            //System.Data.DataView dvR = (DataView)SDS_ver.Select(new DataSourceSelectArguments());

            DataTable dtSource = new DataTable();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = " select count(tr_seno) from treaty_requisition where tr_year+tr_orgcd+tr_class+tr_sn =@contno and tr_status in ('2','E') ";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@contno", oRCM.SQLInjectionReplaceAll(DDL_SeqSn.SelectedItem.ToString().Substring(0, 11)));
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dtSource = new DataTable();
                    sqlDA.Fill(dtSource);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion

            DataView dvR = dtSource.DefaultView;
            if (dvR.Count >= 1)
            {
                if (dvR[0][0].ToString() == "0")
                    IB_newVer.Visible = true;
                else
                    IB_newVer.Visible = false;
            }
        }
    }

    private void BindData_file()
    {

        //SDS_gv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  
        //SDS_gv_file.SelectParameters.Clear();
        //SDS_gv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_gv_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_files");
        //SDS_gv_file.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_gv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString().Trim()));
        //if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0)
        //    SDS_gv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("Edit"));
        //SDS_gv_file.DataBind();
        //gv_doc_file.DataBind();

        #region --- query ---

        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_files";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString().Trim()));
            if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0)
                sqlCmd.Parameters.AddWithValue("@mode", "Edit");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_doc_file.DataSource = dt;
                gv_doc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

    }
    private void BindContType(string strContType)
    {
        string strCondition = "";
        #region 取得目前的案件性質條件
        if (cb_conttype_b0.Checked)
            strCondition += "B0,";

        if (cb_conttype_b1.Checked)
            strCondition += "B1,";

        if (cb_conttype_d4.Checked)
            strCondition += "D4,";

        if (cb_conttype_d5.Checked)
            strCondition += "D5,";

        if (cb_conttype_d7.Checked)
            strCondition += "D7,";
        if (cb_conttype_ns.Checked)
            strCondition += "NS,";
        if (cb_conttype_rb.Checked)
            strCondition += "RB,";
        if (cb_conttype_m.Checked)
            strCondition += "ND,";
        if (rb_conttype_uo.Checked || rb_conttype_ui.Checked)
            strCondition += "UN,";
        if (rb_conttype_bd.Checked)
            strCondition += "BD,";
        if (rb_conttype_other.Checked)
            strCondition += "OT,";

        if (strCondition.Length > 0)
            strCondition = strCondition.Substring(0, strCondition.Length - 1);

        #endregion

        //SDS_ContType.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  
        //SDS_ContType.SelectCommand = "exec esp_treatyCase_codetable  '" + SQLInjectionReplaceAll(strCondition) + "' ,'10' ";
        //    SDS_ContType.DataBind();
        //    ddlContType.DataBind();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"exec esp_treatyCase_codetable  '" + oRCM.SQLInjectionReplaceAll(strCondition) + "' ,'10' ";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                ddlContType.DataSource = dt;
                ddlContType.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        #region 如果有指定 ContType,則將指定的 ContType 選取
        if (strContType.Length > 0)
        {
            foreach (ListItem it in ddlContType.Items)
            {
                if (it.Value.Trim() == strContType)
                    it.Selected = true;
                else
                    it.Selected = false;
            }
            LB_ContType.Text = System.Web.HttpUtility.HtmlEncode(ddlContType.SelectedItem.Text);
        }
        #endregion
    }
    private void BindInspect()
    {
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  SQLInjectionReplaceAll(
        //SDS_Inspect.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;   
        //SDS_Inspect.SelectParameters.Clear();
        //SDS_Inspect.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect.SelectCommand = SQLInjectionReplaceAll("esp_treatyCase_Inspect");
        //SDS_Inspect.SelectParameters.Add("seno", SQLInjectionReplaceAll( ViewState["seno"].ToString()));
        //SDS_Inspect.DataBind();
        //GV_Inspect.DataBind(); 
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treatyCase_Inspect";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Inspect.DataSource = dt;
                GV_Inspect.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }

    private void BindDDL_SeqSn()
    {
        //SDS_DDL_SeqSn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_DDL_SeqSn.SelectParameters.Clear();
        //SDS_DDL_SeqSn.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_DDL_SeqSn.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_senoTree") ;
        //SDS_DDL_SeqSn.SelectParameters.Add("seno", SQLInjectionReplace( ViewState["seno"].ToString()));
        //SDS_DDL_SeqSn.DataBind();
        //DDL_SeqSn.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_senoTree";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                DDL_SeqSn.DataSource = dt;
                DDL_SeqSn.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
    protected void DDL_SeqSn_SelectedIndexChanged(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyCase_view.aspx?seno=" + DDL_SeqSn.SelectedValue);
    }
    private void BindDefer()
    {
        //SDS_Defer.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Defer.SelectParameters.Clear();
        //SDS_Defer.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Defer.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_Defer");
        //SDS_Defer.SelectParameters.Add("seno", SQLInjectionReplace( ViewState["seno"].ToString()));
        //SDS_Defer.DataBind();
        //GV_Defer.DataBind();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_Defer";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Defer.DataSource = dt;
                GV_Defer.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
    private void Bind_sRC_init(string str_class)
    {
        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = SQLInjectionReplaceAll("esp_traetyApplyCase_sRC");
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(str_class));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(ViewState["ver"].ToString()));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_traetyApplyCase_sRC";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(ViewState["ver"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count >= 1)
        {
            ArrayList my報院條件 = new ArrayList();
            ArrayList my報院條件說明 = new ArrayList();
            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif'  class='itemhint'></td><td colspan='3' class='lineheight03'>";
            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_x.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        CBL_x.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        my報院條件.Add(CBL_x.ID);
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_y.Attributes["value"] = dv[sRC_count]["tcs_code"].ToString();
                        CBL_y.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        my報院條件.Add(CBL_y.ID);
                        TextBox tb = new TextBox();
                        tb.ID = Server.HtmlEncode("TB_" + dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 40;
                        tb.Width = 555;
                        tb.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        my報院條件說明.Add(tb.ID);
                        break;
                }
                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }
            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
    }
    private void Bind_sRC(string str_class, string str_ver)
    {
        Plh_Dynax_sRC.Controls.Clear();
        ArrayList my報院條件 = new ArrayList();
        ArrayList my報院條件說明 = new ArrayList();
        ArrayList my報院條件s = new ArrayList();
        ArrayList my報院條件說明s = new ArrayList();

        //SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_sRC.SelectParameters.Clear();
        //SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_sRC.SelectCommand = SQLInjectionReplaceAll("esp_treatyCase_sRc_modify") ;
        //SDS_sRC.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
        //SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("svalue", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("sdoc", SQLInjectionReplaceAll("0"));
        //SDS_sRC.SelectParameters.Add("stype", SQLInjectionReplaceAll("List"));
        //for (int i = 0; i <  SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        // SDS_sRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treatyCase_sRc_modify";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(ViewState["tr_class"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", "0");
            sqlCmd.Parameters.AddWithValue("@svalue", "0");
            sqlCmd.Parameters.AddWithValue("@sdoc", "0");
            sqlCmd.Parameters.AddWithValue("@stype", "List");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                my報院條件s.Add(dvR[i]["tcsRC_val"].ToString());
                if (dvR[i]["tcsRC_val"].ToString().IndexOf("T") != -1)
                    my報院條件說明s.Add(dvR[i]["tcsRC_val"].ToString() + "©" + dvR[i]["tcsRC_desc"].ToString());
            }
        }

        // SDS_sRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        // SDS_sRC.SelectParameters.Clear();
        // SDS_sRC.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        // SDS_sRC.SelectCommand = "esp_traetyApplyCase_sRC";
        // SDS_sRC.SelectParameters.Add("class", SQLInjectionReplaceAll(str_class));
        // SDS_sRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_sRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_sRC.DataBind();
        //System.Data.DataView dv = (DataView)SDS_sRC.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt_src = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_traetyApplyCase_sRC";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@class", oRCM.SQLInjectionReplaceAll(str_class));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt_src);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dv = dt_src.DefaultView;
        if (dv.Count >= 1)
        {
            Literal LB_trs = new Literal();
            LB_trs.Text = "<tr><td align='right'><div class='font-title titlebackicon'>報院特殊條件</div><img  src='../images/tooltiphint.gif'  class='itemhint'></td><td colspan='3' class='lineheight03'>";
            Plh_Dynax_sRC.Controls.Add(LB_trs);
            for (int sRC_count = 0; sRC_count < dv.Count; sRC_count++)
            {
                switch (dv[sRC_count]["tcs_codeCheck"].ToString())
                {
                    case "0":
                        Literal LB_title = new Literal();
                        LB_title.Text = "<b>" + System.Web.HttpUtility.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString()) + "</b>";
                        Plh_Dynax_sRC.Controls.Add(LB_title);
                        break;
                    case "1":
                        CheckBox CBL_x = new CheckBox();
                        CBL_x.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_x.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_x.Attributes["value"] = oRCM.SQLInjectionReplaceAll(dv[sRC_count]["tcs_code"].ToString());
                        CBL_x.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(CBL_x);
                        foreach (string obj in my報院條件s)
                        {
                            //if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                            //    CBL_x.Checked = true;

                            if (obj==dv[sRC_count]["tcs_code"].ToString() )
                                CBL_x.Checked = true;
                        }
                        my報院條件.Add(CBL_x.ID);
                        break;
                    case "Z":
                        CheckBox CBL_y = new CheckBox();
                        CBL_y.ID = Server.HtmlEncode("CBL_" + dv[sRC_count]["tcs_code"].ToString() + "_" + dv[sRC_count]["tcs_codeCheck"].ToString());
                        CBL_y.Text = Server.HtmlEncode(dv[sRC_count]["tcs_codeName"].ToString());
                        CBL_y.Attributes["value"] = oRCM.SQLInjectionReplaceAll(dv[sRC_count]["tcs_code"].ToString());
                        Plh_Dynax_sRC.Controls.Add(CBL_y);
                        CBL_y.Enabled = false;
                        my報院條件.Add(CBL_y.ID);
                        foreach (string obj in my報院條件s)
                        {
                            if (obj.IndexOf(dv[sRC_count]["tcs_code"].ToString()) >= 0)
                                CBL_y.Checked = true;
                        }
                        TextBox tb = new TextBox();
                        tb.ID = Server.HtmlEncode("TB_" + dv[sRC_count]["tcs_code"].ToString());
                        tb.TextMode = TextBoxMode.MultiLine;
                        tb.Height = 40;
                        tb.Width = 555;
                        tb.Enabled = false;
                        Plh_Dynax_sRC.Controls.Add(tb);
                        my報院條件說明.Add(tb.ID);
                        foreach (string str_obj in my報院條件說明s)
                        {
                            if (str_obj.Split('©')[0].ToString() == dv[sRC_count]["tcs_code"].ToString())
                                tb.Text = str_obj.Split('©')[1].ToString();
                        }
                        break;
                }

                Literal LB_br1 = new Literal();
                LB_br1.Text = "<br />";
                Plh_Dynax_sRC.Controls.Add(LB_br1);
            }

            Literal LB_tre = new Literal();
            LB_tre.Text = "</td></tr>";
            Plh_Dynax_sRC.Controls.Add(LB_tre);
            ViewState["my報院條件"] = my報院條件;
            ViewState["my報院條件說明"] = my報院條件說明;
        }
    }
    private void Bind_oRC(string str_seno, string str_ver)
    {
        if (str_ver == "2")
            PH_oRC_new.Visible = true;
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_oRC.SelectParameters.Clear();
        //SDS_oRC.SelectCommandType = SqlDataSourceCommandType.Text;
        //SDS_oRC.SelectCommand = " select * from treaty_case_oRC where tc_seno=@seno and tcoRC_ver=@ver";
        //SDS_oRC.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_oRC.SelectParameters.Add("ver", SQLInjectionReplaceAll(str_ver));
        //for (int i = 0; i < this.SDS_sRC.SelectParameters.Count; i++)
        //{
        //    SDS_oRC.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_oRC.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_oRC.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.Text;
            sqlCmd.CommandText = @"select * from treaty_case_oRC where tc_seno=@seno and tcoRC_ver=@ver";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@ver", oRCM.SQLInjectionReplaceAll(str_ver));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            for (int i = 0; i < dvR.Count; i++)
            {
                switch (dvR[i]["tcoRC_val"].ToString())
                {
                    case "1":
                        rb_other_1.Checked = true;
                        txt_otherrequire_contno.Text = Server.HtmlEncode(dvR[i]["tcoRC_desc1"].ToString());
                        TB_otherrequire_handle_name.Text = Server.HtmlEncode(dvR[i]["tcoRC_desc2"].ToString());
                        break;
                    case "2":
                        rb_other_2.Checked = true;
                        txt_otherrequire_asked_name.Text = dvR[i]["tcoRC_desc1"].ToString();
                        break;
                    case "3":
                        rb_other_3.Checked = true;
                        break;
                    case "4":
                        rb_other_4.Checked = true;
                        break;
                    case "T":
                        rb_other_T.Checked = true;
                        txt_otherrequire_desc.Text = dvR[i]["tcoRC_desc1"].ToString();
                        break;
                }
            }
        }
    }
    private void Bind_cop(string str_seno)
    {
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_cop.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_cop.SelectParameters.Clear();
        //SDS_cop.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_cop.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_cop");
        //SDS_cop.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //for (int i = 0; i < this.SDS_cop.SelectParameters.Count; i++)
        //{
        //    SDS_cop.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //this.SDS_cop.DataBind();
        //System.Data.DataView dvR = (DataView)SDS_cop.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_cop";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dvR = dt.DefaultView;
        if (dvR.Count >= 1)
        {
            lb_cop.Text = Server.HtmlEncode(dvR[0][0].ToString());
        }
        else
            lb_cop.Text = "";

    }
    private void Bind_計價承辦名單()
    {
        if (DDL_計價承辦人.Items.Count < 2)
        {
            //SDS_計價承辦人.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_計價承辦人.SelectParameters.Clear();
            //SDS_計價承辦人.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_計價承辦人.SelectCommand = "esp_TreatyCase_valuation";
            //SDS_計價承辦人.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            //SDS_計價承辦人.SelectParameters.Add("mode", SQLInjectionReplaceAll("valuation_h"));
            //ConvertSqlParametersEmptyStringToNull(SDS_Inspect_value, false, "Select");
            //SDS_計價承辦人.DataBind();
            ////System.Data.DataView dv_auth = (DataView)SDS_計價承辦人.Select(new DataSourceSelectArguments());
            //DDL_計價承辦人.DataBind();
            #region --- query ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_valuation";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "valuation_h");
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    DataTable dt = new DataTable();
                    sqlDA.Fill(dt);
                    DDL_計價承辦人.DataSource = dt;
                    DDL_計價承辦人.DataBind();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
        }
    }
    private void Bind計價()
    {
        SqlCommand oCmd_1 = new SqlCommand();
        oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd_1.CommandText = "esp_TreatyCase_valuation";
        oCmd_1.CommandType = CommandType.StoredProcedure;
        oCmd_1.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        oCmd_1.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd_1.Parameters.AddWithValue("版本", "0");
        oCmd_1.Parameters.AddWithValue("mode", "view");
        SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
        DataSet ds_1 = new DataSet();
        oda_1.Fill(ds_1, "myTable");
        DataView dv = ds_1.Tables[0].DefaultView;
        if (dv.Count >= 1)
        {

            if ((ViewState["tc_degree"].ToString() == "C" || ViewState["tc_degree"].ToString() == "Z") && !(dv[0]["進度"].ToString() == "Z" || dv[0]["進度"].ToString() == "C"))
            {
                if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0 && ViewState["Module"].ToString() == "IV")
                {
                    BT_計價取消.Visible = true;
                }
            }

            ViewState["計價進度"] = dv[0]["進度"].ToString().Trim();
            LB_承辦人.Text = Server.HtmlEncode(dv[0]["承辦人"].ToString().Trim());
            if ((dv[0]["進度"].ToString() == "0" || dv[0]["進度"].ToString() == "1") && (ViewState["Module"].ToString() == "IV" || ViewState["SYS"].ToString() == "HGroup_ADM" || ViewState["empno"].ToString() == dv[0]["指派人"].ToString().Trim()))
            {
                PH_指派.Visible = true;
                Bind_計價承辦名單();
                DDL_計價承辦人.SelectedValue = dv[0]["承辦人_empno"].ToString().Trim();
                LB_承辦人.Visible = false;
            }
            else
            {
                PH_指派.Visible = false;
            }
            DDL_計價進度.SelectedValue = dv[0]["進度"].ToString().Trim();

            if (dv[0]["進度"].ToString().Trim() == "0" && dv[0]["承辦人_empno"].ToString().Trim() == ViewState["empno"].ToString())
            {
                Handel_計價();
                DDL_計價進度.SelectedValue = "1";
            }
            TB_參考價.Text = Server.HtmlEncode(string.Format("{0:#,##0}", Convert.ToInt64(Convert.ToDouble(IIf(dv[0]["參考價"].ToString().Trim() != "", dv[0]["參考價"].ToString().Trim(), "0")))));
            DDL_盡職調查結果.SelectedValue = dv[0]["盡職調查結果"].ToString().Trim();
            TB_底價.Text = Server.HtmlEncode(string.Format("{0:#,##0}", Convert.ToInt64(Convert.ToDouble(IIf(dv[0]["底價"].ToString().Trim() != "", dv[0]["底價"].ToString().Trim(), "0")))));
            if (dv[0]["底價_無"].ToString().Trim() == "1")
            {
                CB_底價_無.Checked = true;
                TB_底價_無_說明.Text = dv[0]["底價_無_說明"].ToString().Trim();
                TB_底價_無_說明.Visible = true;
            }
            else
            {
                CB_底價_無.Checked = false;
                TB_底價_無_說明.Text = "";
                TB_底價_無_說明.Visible = false;
            }

            TB_第三方鑑價.Text = Server.HtmlEncode(string.Format("{0:#,##0}", Convert.ToInt64(Convert.ToDouble(IIf(dv[0]["第三方鑑價"].ToString().Trim() != "", dv[0]["第三方鑑價"].ToString().Trim(), "0")))));
            if (dv[0]["第三方鑑價_無"].ToString().Trim() == "1")
            {
                CB_第三方鑑價_無.Checked = true;
                TB_第三方鑑價_無_說明.Text = dv[0]["第三方鑑價_無_說明"].ToString().Trim();
                TB_第三方鑑價_無_說明.Visible = true;
            }
            else
            {
                CB_第三方鑑價_無.Checked = false;
                TB_第三方鑑價_無_說明.Text = "";
                TB_第三方鑑價_無_說明.Visible = false;
            }
            TB_其他說明.Text = dv[0]["其他說明"].ToString().Trim();
            //LT_審查.Text = dv[0]["審查資訊"].ToString().Trim();
            BT_計價.Attributes.Add("onclick", "value_edit(" + ViewState["seno"].ToString() + ");");

            if (ViewState["SYS"].ToString().IndexOf("LAW") >= 0 || ViewState["SYS"].ToString().IndexOf("HGroup") >= 0)
            {
                if (ViewState["Module"].ToString().IndexOf("I") >= 0 && (dv[0]["進度"].ToString().Trim() == "Z"))
                {
                    PL_Inspect_計價.Visible = true;
                    if (ViewState["Module"].ToString() == "I") //法務人員不能維護計價資料 & 計價完畢才檢視
                    {
                        BT_計價.Visible = false;
                        BT_計價審查.Visible = false;
                    }
                }
                if (ViewState["Module"].ToString().IndexOf("V") >= 0)
                {
                    PL_Inspect_法務.Visible = true;
                    PL_Inspect_計價.Visible = true;
                    if (ViewState["Module"].ToString() == "V") //計價人員不能維護審查資料
                    {
                        BT_Inspect.Visible = false;
                        BT_Inspect2.Visible = false;
                        btEdit.Visible = false;
                        btEdit2.Visible = false;
                        BT_End1.Visible = false;
                        BT_End.Visible = false;
                        BT_AddInspect.Visible = false;
                        bt_cancle.Visible = false;
                        IB_newVer.Visible = false;
                    }

                    BT_計價.Attributes.Add("onclick", "Valuation_modify(" + ViewState["seno"].ToString() + ");");
                    BT_計價審查.Attributes.Add("onclick", "Valuation_inspect(" + ViewState["seno"].ToString() + ");");
                    if (dv[0]["進度"].ToString() == "3")
                    {
                        if (ViewState["SYS"].ToString() == "HGroup_ADM" || ViewState["Module"].ToString() == "IV")
                            BT_計價審查.Visible = true;
                        else
                        {
                            if (dv[0]["審查人"].ToString() == ViewState["empno"].ToString())
                                BT_計價審查.Visible = true;
                            else
                                BT_計價審查.Visible = false;
                        }
                    }
                    else
                    {
                        BT_計價審查.Visible = false;
                    }
                    if ((dv[0]["進度"].ToString() == "0" || dv[0]["進度"].ToString() == "1" || dv[0]["進度"].ToString() == "2" || dv[0]["進度"].ToString() == "U") &&
                         (dv[0]["承辦人_empno"].ToString() == ViewState["empno"].ToString() || ViewState["SYS"].ToString() == "HGroup_ADM" || ViewState["Module"].ToString() == "IV")
                       )
                    {
                        BT_計價.Visible = true;
                    }
                    else
                    {
                        BT_計價.Visible = false;
                    }
                }
            }
            if ((dv[0]["進度"].ToString().Trim() == "Z") || (dv[0]["進度"].ToString().Trim() == "C"))
            {
                BT_計價.Visible = false;
                BT_計價審查.Visible = false;
            }
            if (dv[0]["版本"].ToString().Trim() == "1")
            {
                PH_計價歷程.Visible = false;
            }
            else
            {
                LT_計價歷程.Text = "<a class='ajax_vmesg'  href='javascript:Valuation_history(" + System.Web.HttpUtility.HtmlEncode(ViewState["seno"].ToString()) + ")'>計價歷次紀錄</a>";
                PH_計價歷程.Visible = true;
            }
        }
        ds_1.Dispose();
        oCmd_1.Dispose();
        oda_1.Dispose();
        BindInspect_value();
        BindData_vfile();
    }
    private void BindInspect_value()
    {
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;  SQLInjectionReplaceAll(
        //SDS_Inspect_value.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Inspect_value.SelectParameters.Clear();
        //SDS_Inspect_value.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect_value.SelectCommand = "esp_TreatyCase_valuation";
        //SDS_Inspect_value.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_Inspect_value.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        //SDS_Inspect_value.SelectParameters.Add("mode", SQLInjectionReplaceAll("list_inspect"));
        //ConvertSqlParametersEmptyStringToNull(SDS_Inspect_value, false, "Select");
        //SDS_Inspect_value.DataBind();
        //GV_Inspect_value.DataBind();

        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_valuation";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
            sqlCmd.Parameters.AddWithValue("@mode", "list_inspect");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                GV_Inspect_value.DataSource = dt;
                GV_Inspect_value.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
    private void BindData_vfile()
    {
        //SDS_vgv_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_vgv_file.SelectParameters.Clear();
        //SDS_vgv_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_vgv_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_valuation");
        //SDS_vgv_file.SelectParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_vgv_file.SelectParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
        //SDS_vgv_file.SelectParameters.Add("mode", SQLInjectionReplaceAll("file_view"));
        //ConvertSqlParametersEmptyStringToNull(SDS_vgv_file, false, "Select");
        //SDS_vgv_file.DataBind();
        //gv_vdoc_file.DataBind();
        #region --- query ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_valuation";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString().Trim()));
            sqlCmd.Parameters.AddWithValue("@mode", "file_view");
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                DataTable dt = new DataTable();
                sqlDA.Fill(dt);
                gv_vdoc_file.DataSource = dt;
                gv_vdoc_file.DataBind();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
    private void Handel_計價()
    {
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_TreatyCase_valuation";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd.Parameters.AddWithValue("mode", "change_degree");
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();
    }
    protected void HecUpdate_計價(object sender, System.EventArgs e)
    {
        int int_err = 0;
        if (DDL_計價承辦人.SelectedValue == "")
        {
            int_err++;
            string script_alertN = " $('#DDL_計價承辦人').validationEngine('showPrompt', '★必選','','',true); $('#DDL_計價承辦人').click(function () { $('#DDL_計價承辦人').validationEngine('hide'); });";
            Page.ClientScript.RegisterStartupScript(Page.GetType(), "#DDL_計價承辦人", script_alertN, true);
        }

        if (int_err == 0)
        {
            SqlCommand oCmd = new SqlCommand();
            oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            oCmd.CommandText = "esp_TreatyCase_valuation";
            oCmd.CommandType = CommandType.StoredProcedure;
            oCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            oCmd.Parameters.AddWithValue("新增承辦人", oRCM.SQLInjectionReplaceAll(DDL_計價承辦人.SelectedValue));
            oCmd.Parameters.AddWithValue("mode", "case_assign");
            SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            DataSet ds = new DataSet();
            oda.Fill(ds, "myTable");
            oCmd.Dispose();
            oda.Dispose();
            Page.ClientScript.RegisterStartupScript(Page.GetType(), "xxx", "alert('已指派承辦人!!');", true);
            Bind計價();
        }
    }
    public bool CheckDateTimeType(string txtDateStart)
    {
        if (String.IsNullOrEmpty(txtDateStart))
        {
            return false;
        }
        else
        {
            try
            {
                DateTime t1 = DateTime.Parse(txtDateStart);
                return true;  //返回真
            }
            catch
            {
                return false;
            }
        }
    }
    public static bool IsNumber(string strNumber)
    {
        System.Text.RegularExpressions.Regex r = new System.Text.RegularExpressions.Regex(@"^\d+(\.)?\d*$");
        return r.IsMatch(strNumber);
    }
    public bool IsNatural_Number(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^[A-Za-z0-9]+$");
        return reg1.IsMatch(str);
    }
    public bool Isfloat(string str)
    {
        System.Text.RegularExpressions.Regex reg1 = new System.Text.RegularExpressions.Regex(@"^\\d+(\\.\\d+)?$");
        return reg1.IsMatch(str);
    }
    protected void SGV_company_RowCommand(object sender, GridViewCommandEventArgs e)
    {

    }
    protected void SGV_company_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_company");
        if (LB != null)
            LB.Text = "<a class='ajax_mesg_comp' onclick='CompanyInfo(\"" + LB.Text.ToString() + "\");' >" + LB.Text.ToString() + "</a>";

    }
    protected void gv_doc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {

        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";

            //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_log.SelectParameters.Clear();
            //SDS_log.SelectCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
            //SDS_log.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_file_modify");
            //SDS_log.SelectParameters.Add("req_id", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("fd_name", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("filetxt", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("file_url", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("empno", SQLInjectionReplaceAll(""));
            //SDS_log.SelectParameters.Add("mode", SQLInjectionReplaceAll("view"));
            //SDS_log.SelectParameters.Add("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //for (int i = 0; i < this.SDS_log.SelectParameters.Count; i++)
            //{
            //    SDS_log.SelectParameters[i].ConvertEmptyStringToNull = false;
            //}
            //SDS_log.DataBind();
            //System.Data.DataView dv = (DataView)SDS_log.Select(new DataSourceSelectArguments());

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_file_modify";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@req_id", "");
                sqlCmd.Parameters.AddWithValue("@fd_name", "");
                sqlCmd.Parameters.AddWithValue("@filetxt", "");
                sqlCmd.Parameters.AddWithValue("@file_url", "");
                sqlCmd.Parameters.AddWithValue("@empno", "");
                sqlCmd.Parameters.AddWithValue("@mode", "view");
                sqlCmd.Parameters.AddWithValue("@fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim().Replace("/", "");
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            }
            if (str_file_url != "")
            {
                Treaty_log(ViewState["seno"].ToString(), "檔案下載", str_filename, ViewState["xIP"].ToString(), "treaty\\TreatyCase_View.aspx");
                Response.Clear();
                Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                //Response.WriteFile(System.Web.HttpUtility.HtmlEncode(str_file_url.Replace("/", "").Replace("..", "")));
                Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                Response.Flush();
                Response.End();
            }
        }
    }
    protected void gv_doc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lb_inspect = (Label)e.Row.FindControl("LB_inspect");
            if (lb_inspect != null)
            {
                if (lb_inspect.Text == "0")
                    lb_inspect.Text = "";
                else
                    lb_inspect.Text = "V";
            }
        }
    }
    public void Treaty_log(string xID, string txtResult, string txtMeno, string xIP, string xApp)
    {
        SSOUtil.SSOLoginUser ssoUser = new SSOUtil.SSOLoginUser();
        ssoUser.GetEmpInfo();
        //SDS_log.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_log.InsertParameters.Clear();
        //SDS_log.InsertCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_log.InsertCommand = SQLInjectionReplaceAll( "esp_treaty_log");
        //SDS_log.InsertParameters.Add("seno", SQLInjectionReplaceAll(xID));
        //SDS_log.InsertParameters.Add("empno", SQLInjectionReplaceAll(ssoUser.empNo));
        //SDS_log.InsertParameters.Add("empName", SQLInjectionReplaceAll(ssoUser.empName.Trim()));
        //SDS_log.InsertParameters.Add("txtResult", SQLInjectionReplaceAll(txtResult));
        //SDS_log.InsertParameters.Add("txt_meno", SQLInjectionReplaceAll(txtMeno));
        //SDS_log.InsertParameters.Add("xIP", SQLInjectionReplaceAll(GetUserIP()));
        //SDS_log.InsertParameters.Add("xApp", SQLInjectionReplaceAll(xApp));
        //SDS_log.Insert();
        #region --- insert ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_treaty_log";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(xID));
            sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ssoUser.empNo));
            sqlCmd.Parameters.AddWithValue("@empName", oRCM.SQLInjectionReplaceAll(ssoUser.empName.Trim()));
            sqlCmd.Parameters.AddWithValue("@txtResult", oRCM.SQLInjectionReplaceAll(txtResult));
            sqlCmd.Parameters.AddWithValue("@txt_meno", oRCM.SQLInjectionReplaceAll(txtMeno));
            sqlCmd.Parameters.AddWithValue("@xIP", oRCM.SQLInjectionReplaceAll(GetUserIP()));
            sqlCmd.Parameters.AddWithValue("@xApp", oRCM.SQLInjectionReplaceAll(xApp));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
    }
    protected void btEdit_Click(object sender, EventArgs e)
    {
        Response.Redirect("./TreatyCase_modify.aspx?seno=" + ViewState["seno"].ToString());
    }
    protected void btnDelete_Click(object sender, EventArgs e)
    {
        //SDS_auth.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_auth.DeleteParameters.Clear();
        //SDS_auth.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.StoredProcedure;
        //SDS_auth.DeleteCommand = SQLInjectionReplaceAll("esp_TreatyCase_Delete");
        //SDS_auth.DeleteParameters.Add("caseno", SQLInjectionReplaceAll(DDL_SeqSn.SelectedItem.Text.Replace("-", "")));
        //SDS_auth.Delete();

        #region --- delete ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_Delete";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@caseno", oRCM.SQLInjectionReplaceAll(DDL_SeqSn.SelectedItem.Text.Replace("-", "")));
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        StringBuilder script = new StringBuilder("<script type='text/javascript'> DeleteCase();</script>");
        ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);

    }
    protected void GV_Inspect_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_Istatus");
        if (LB != null)
            switch (LB.Text.Trim())
            {
                case "0":
                    LB.Text = "";
                    break;
                case "1":
                    LB.Text = "同意";
                    break;
                case "2":
                    LB.Text = "退回";
                    break;
            }
        LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
        if (lb_del != null)
        {
            if (ViewState["SYS"].ToString().IndexOf("ADM") >= 1)
            {
                if ((ViewState["tc_degree"].ToString().Trim() == "Z") || (ViewState["tc_degree"].ToString().Trim() == "C"))
                    lb_del.Visible = false;
                else
                    lb_del.Visible = true;
            }
            if (ViewState["SYS"].ToString() == "HGroup_ADM")
                lb_del.Visible = false;
        }
    }

    protected void GV_Inspect_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //SDS_Inspect.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;   
            //SDS_Inspect.DeleteParameters.Clear();
            //SDS_Inspect.DeleteCommandType = System.Web.UI.WebControls.SqlDataSourceCommandType.Text;
            //SDS_Inspect.DeleteCommand = " delete treaty_case_inspect where tci_no=@tci_no";
            //SDS_Inspect.DeleteParameters.Add("tci_no", SQLInjectionReplaceAll( e.CommandArgument.ToString()));
            //SDS_Inspect.Delete();
            #region --- delete ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;
                sqlCmd.CommandText = @"delete treaty_case_inspect where tci_no=@tci_no";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tci_no", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindInspect();
        }
    }

    protected void GV_Inspect_value_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Literal LB = (Literal)e.Row.FindControl("LB_Istatus");

        LinkButton lb_del = (LinkButton)e.Row.FindControl("LB_del");
        if (lb_del != null)
        {
            if ((ViewState["SYS"].ToString().IndexOf("ADM") >= 1) || (ViewState["SYS"].ToString().IndexOf("HGroup") >= 1))
            {
                if ((ViewState["計價進度"].ToString().Trim() == "Z") || (ViewState["計價進度"].ToString().Trim() == "C"))
                    lb_del.Visible = false;
                else
                {

                    if (LB != null)
                        switch (LB.Text.Trim())
                        {
                            case "同意":
                                lb_del.Visible = false;
                                break;
                            case "不同意":
                                lb_del.Visible = false;
                                break;
                            default:
                                lb_del.Visible = true;
                                break;
                        }
                }
                if (ViewState["Module"].ToString() == "V") //計價人員沒權限刪除法務審查人
                {
                    lb_del.Visible = false;
                    BT_AddInspect.Visible = false;
                }
            }
        }
    }
    protected void GV_Inspect_value_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDelete")
        {
            //SDS_Inspect_value.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_Inspect_value.DeleteParameters.Clear();
            //SDS_Inspect_value.DeleteCommandType = SqlDataSourceCommandType.StoredProcedure;
            //SDS_Inspect_value.DeleteCommand = SQLInjectionReplaceAll("esp_TreatyCase_valuation");
            //SDS_Inspect_value.DeleteParameters.Add("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_Inspect_value.DeleteParameters.Add("tcii_seno", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //SDS_Inspect_value.DeleteParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            //SDS_Inspect_value.DeleteParameters.Add("mode", SQLInjectionReplaceAll("del_inspect"));
            //ConvertSqlParametersEmptyStringToNull(SDS_Inspect_value, false, "Delete");
            #region --- modify ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_valuation";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@tcii_seno", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@mode", "del_inspect");
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion

            StringBuilder script = new StringBuilder("<script type='text/javascript'> alert('已刪除審查人!');</script>");
            ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            BindInspect_value();
        }
    }

    protected void gv_vdoc_file_RowCommand(object sender, GridViewCommandEventArgs e)
    {
        if (e.CommandName == "xDownload")
        {
            if (oRCM.IsPC(Request) == false)
            {
                Response.Redirect("../DownloadFail.aspx");
            }
            string str_file_url = "";
            string str_filename = "";

            //SqlCommand oCmd_1 = new SqlCommand();
            //oCmd_1.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
            //oCmd_1.CommandText = "esp_TreatyCase_valuation";
            //oCmd_1.CommandType = CommandType.StoredProcedure;
            //oCmd_1.Parameters.AddWithValue("tc_seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //oCmd_1.Parameters.AddWithValue("empno", SQLInjectionReplaceAll(ViewState["empno"].ToString()));
            //oCmd_1.Parameters.AddWithValue("fid", SQLInjectionReplaceAll(e.CommandArgument.ToString()));
            //oCmd_1.Parameters.AddWithValue("mode", SQLInjectionReplaceAll("file_Download"));
            //SqlDataAdapter oda_1 = new SqlDataAdapter(oCmd_1);
            //DataSet ds_1 = new DataSet();
            //oda_1.Fill(ds_1, "myTable");
            //DataView dv = ds_1.Tables[0].DefaultView;
            //if (dv.Count >= 1)
            //{
            //    str_file_url = dv[0]["tcdf_url"].ToString().Trim();
            //    str_filename = dv[0]["tcdf_filename"].ToString().Trim();
            //    if (str_file_url != "")
            //    {
            //        Response.Clear();
            //        Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
            //        Response.WriteFile(str_file_url);
            //        Response.Flush();
            //        Response.End();
            //    }
            //}
            //ds_1.Dispose();
            //oCmd_1.Dispose();
            //oda_1.Dispose();

            #region --- query ---
            DataTable dt = new DataTable();
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_valuation";
                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
                sqlCmd.Parameters.AddWithValue("fid", oRCM.SQLInjectionReplaceAll(e.CommandArgument.ToString()));
                sqlCmd.Parameters.AddWithValue("mode", "file_Download");
                try
                {
                    sqlConn.Open();
                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    dt = new DataTable();
                    sqlDA.Fill(dt);
                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion
            DataView dv = dt.DefaultView;
            if (dv.Count >= 1)
            {
                str_file_url = dv[0]["tcdf_url"].ToString().Trim();
                str_filename = dv[0]["tcdf_filename"].ToString().Trim();
                if (str_file_url != "")
                {
                    Response.Clear();
                    Response.AppendHeader("Content-Disposition", "attachment; filename=" + HttpUtility.UrlEncode(str_filename, Encoding.UTF8));
                    Response.WriteFile(Server.HtmlDecode(Server.HtmlEncode(str_file_url)));
                    Response.Flush();
                    Response.End();
                }
            }
        }
    }

    protected void gv_vdoc_file_RowDataBound(object sender, GridViewRowEventArgs e)
    {

    }

    protected void bt_reject_Click(object sender, EventArgs e)
    {
        if (txt_reject.Text != "")
        {
            //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
            //SDS_NR.UpdateParameters.Clear();
            //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.Text;
            //SDS_NR.UpdateCommand = "exec esp_TreatyCase_reject_mail @seno ,@resion ";
            //SDS_NR.UpdateParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            //SDS_NR.UpdateParameters.Add("resion", SQLInjectionReplaceAll(txt_reject.Text));
            //SDS_NR.Update();
            #region --- modify ---
            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.StoredProcedure;
                sqlCmd.CommandText = @"esp_TreatyCase_reject_mail";
                sqlCmd.CommandTimeout = 0;
                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                sqlCmd.Parameters.AddWithValue("@resion", oRCM.SQLInjectionReplaceAll(txt_reject.Text.Trim()));
                try
                {
                    sqlConn.Open();
                    sqlCmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );
                    oRCM.ErrorExceptionDataToDB(logMail);
                }
                finally
                {
                    sqlConn.Close();
                }
            }
            #endregion

            Treaty_log(ViewState["seno"].ToString(), "退件", "", ViewState["seno"].ToString(), ViewState["empNo"].ToString());
            Response.Redirect("./TreatyApply_view.aspx?contno=" + txtComplexNo.Text.Replace("-", ""));
        }
        else
        {
            string script_alert = "<script language='javascript'>  alert('請填寫退件原因!'); </script>";
            ClientScript.RegisterStartupScript(this.GetType(), "bx_reject", script_alert);
        }
    }
    protected void bt_cancle_Click(object sender, EventArgs e)
    {
        //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_NR.UpdateParameters.Clear();
        //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_NR.UpdateCommand = SQLInjectionReplaceAll("esp_TreatyCase_status_update") ;
        //SDS_NR.UpdateParameters.Add("tc_seno", SQLInjectionReplaceAll( ViewState["seno"].ToString()));
        //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplaceAll( "C"));
        //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplaceAll( "Z"));
        //SDS_NR.Update();

        #region --- modify ---
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_status_update";
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            sqlCmd.Parameters.AddWithValue("@tc_degree", "C");
            sqlCmd.Parameters.AddWithValue("@tc_status", "Z");
            try
            {
                sqlConn.Open();
                sqlCmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion

        Treaty_log(ViewState["seno"].ToString(), "需求取消", "", ViewState["seno"].ToString(), ViewState["empNo"].ToString());
        BindData();
    }
    protected void BT_Print_Click(object sender, EventArgs e)
    {
        //string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];  
        //string url = string.Format("{0}/rptTreaty&rc:ToolBar=false&rs:ClearSession=true&rs:Format=pdf&tc_seno={1}", strRSPath, (string)ViewState["seno"]);
        //Response.Redirect(url, true);

        string 議約編號 = "", 議約名稱 = "", 單位 = "", 部門 = "", 單位承辦人 = "", 契約語文 = "", 簽約對象 = "", 案件性質 = "", 契約性質 = "", 契約時間 = "", 契約金額 = "", 簽約原由與目的 = "", 法務承辦人 = "", 工號 = "", 分機 = "", 送件日期 = "", 分案日期 = "", 需求結案日 = "", betsum = " ", 法務承辦人分機 = "";
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_report.SelectParameters.Clear();
        //SDS_report.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_report.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_report");
        //SDS_report.SelectParameters.Add("seno", SQLInjectionReplaceAll( ViewState["seno"].ToString()));
        //SDS_report.DataBind();
        //System.Data.DataView dv = (DataView)SDS_report.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_TreatyCase_report";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;

            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count > 0)
        {
            議約編號 = dv[0]["議約編號"].ToString().Trim();
            議約名稱 = dv[0]["議約名稱"].ToString().Trim();
            單位 = dv[0]["單位"].ToString().Trim();
            部門 = dv[0]["部門"].ToString().Trim();
            單位承辦人 = dv[0]["單位承辦人"].ToString().Trim();
            契約語文 = dv[0]["契約語文"].ToString().Trim();
            議約名稱 = dv[0]["議約名稱"].ToString().Trim();
            簽約對象 = dv[0]["簽約對象"].ToString().Trim();
            案件性質 = dv[0]["案件性質"].ToString().Trim();
            契約性質 = dv[0]["契約性質"].ToString().Trim();
            契約時間 = dv[0]["契約時間"].ToString().Trim();
            契約金額 = dv[0]["契約金額"].ToString().Trim();
            簽約原由與目的 = dv[0]["簽約原由與目的"].ToString().Trim();
            法務承辦人 = dv[0]["法務承辦人"].ToString().Trim();
            工號 = dv[0]["工號"].ToString().Trim();
            分機 = dv[0]["分機"].ToString().Trim();
            送件日期 = dv[0]["送件日期"].ToString().Trim();
            分案日期 = dv[0]["分案日期"].ToString().Trim();
            需求結案日 = dv[0]["需求結案日"].ToString().Trim();
            法務承辦人分機 = dv[0]["法務承辦人分機"].ToString().Trim();

            betsum = dv[0]["betsum"].ToString().Trim();
        }

        asposeWord myword = new asposeWord(string.Format("{0}.docx", "議約承辦資料"));
        Aspose.Words.DocumentBuilder db = myword.getDocumentBuilder();
        Aspose.Words.Document doc = myword.getDocument();
        doc.MailMerge.Execute(new string[] { "議約編號", "議約名稱", "單位", "部門", "單位承辦人", "契約語文", "議約名稱", "簽約對象", "案件性質", "契約性質", "契約時間", "契約金額", "簽約原由與目的", "法務承辦人", "工號", "分機", "送件日期", "分案日期", "需求結案日", "法務承辦人分機" },
                              new object[] { 議約編號, 議約名稱, 單位, 部門, 單位承辦人, 契約語文, 議約名稱, 簽約對象, 案件性質, 契約性質, 契約時間, 契約金額, 簽約原由與目的, 法務承辦人, 工號, 分機, 送件日期, 分案日期, 需求結案日, 法務承辦人分機 });

        db.MoveToDocumentStart();
        myword.insertHTML("betsum", betsum);

        //SDS_report_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_report_file.SelectParameters.Clear();
        //SDS_report_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_report_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_report_file");
        //SDS_report_file.SelectParameters.Add("seno", SQLInjectionReplace( ViewState["seno"].ToString()));
        //System.Data.DataView dv_emp = (DataView)SDS_report_file.Select(new DataSourceSelectArguments());
        //DataTable dt = ((DataView)SDS_report_file.Select(DataSourceSelectArguments.Empty)).Table;

        DataTable dt_file = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_report_file";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                dt = new DataTable();
                sqlDA.Fill(dt_file);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        dt_file.TableName = "檔案";
        doc.MailMerge.CleanupOptions = Aspose.Words.Reporting.MailMergeCleanupOptions.RemoveUnusedFields;
        doc.MailMerge.CleanupOptions = Aspose.Words.Reporting.MailMergeCleanupOptions.RemoveUnusedRegions;
        doc.MailMerge.ExecuteWithRegions(dt_file);

        saveFile(string.Format("{0}.pdf", 議約編號), myword.exportPDF());
    }
    protected void BT_Print_source_Click(object sender, EventArgs e)
    {
        string 議約編號 = "", 議約名稱 = "", 單位 = "", 部門 = "", 單位承辦人 = "", 契約語文 = "", 簽約對象 = "", 案件性質 = "", 契約性質 = "", 契約時間 = "", 契約金額 = "", 簽約原由與目的 = "", 法務承辦人 = "", 工號 = "", 分機 = "", 送件日期 = "", 分案日期 = "", 需求結案日 = "", betsum = " ", 法務承辦人分機 = "";
        //.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_report.SelectParameters.Clear();
        //SDS_report.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_report.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_report");
        //SDS_report.SelectParameters.Add("seno", SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        //SDS_report.DataBind();
        //System.Data.DataView dv = (DataView)SDS_report.Select(new DataSourceSelectArguments());
        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_report";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }
        #endregion
        DataView dv = dt.DefaultView;
        if (dv.Count > 0)
        {
            議約編號 = dv[0]["議約編號"].ToString().Trim();
            議約名稱 = dv[0]["議約名稱"].ToString().Trim();
            單位 = dv[0]["單位"].ToString().Trim();
            部門 = dv[0]["部門"].ToString().Trim();
            單位承辦人 = dv[0]["單位承辦人"].ToString().Trim();
            契約語文 = dv[0]["契約語文"].ToString().Trim();
            議約名稱 = dv[0]["議約名稱"].ToString().Trim();
            簽約對象 = dv[0]["簽約對象"].ToString().Trim();
            案件性質 = dv[0]["案件性質"].ToString().Trim();
            契約性質 = dv[0]["契約性質"].ToString().Trim();
            契約時間 = dv[0]["契約時間"].ToString().Trim();
            契約金額 = dv[0]["契約金額"].ToString().Trim();
            簽約原由與目的 = dv[0]["簽約原由與目的"].ToString().Trim();
            法務承辦人 = dv[0]["法務承辦人"].ToString().Trim();
            工號 = dv[0]["工號"].ToString().Trim();
            分機 = dv[0]["分機"].ToString().Trim();
            送件日期 = dv[0]["送件日期"].ToString().Trim();
            分案日期 = dv[0]["分案日期"].ToString().Trim();
            需求結案日 = dv[0]["需求結案日"].ToString().Trim();
            法務承辦人分機 = dv[0]["法務承辦人分機"].ToString().Trim();

            betsum = dv[0]["法務承辦人意見彙整"].ToString().Trim();
        }

        asposeWord myword = new asposeWord(string.Format("{0}.docx", "議約承辦資料原稿"));
        Aspose.Words.DocumentBuilder db = myword.getDocumentBuilder();
        Aspose.Words.Document doc = myword.getDocument();
        doc.MailMerge.Execute(new string[] { "議約編號", "議約名稱", "單位", "部門", "單位承辦人", "契約語文", "議約名稱", "簽約對象", "案件性質", "契約性質", "契約時間", "契約金額", "簽約原由與目的", "法務承辦人", "工號", "分機", "送件日期", "分案日期", "需求結案日", "法務承辦人分機" },
                              new object[] { 議約編號, 議約名稱, 單位, 部門, 單位承辦人, 契約語文, 議約名稱, 簽約對象, 案件性質, 契約性質, 契約時間, 契約金額, 簽約原由與目的, 法務承辦人, 工號, 分機, 送件日期, 分案日期, 需求結案日, 法務承辦人分機 });

        db.MoveToDocumentStart();
        myword.insertHTML("betsum", betsum);
        //SDS_report_file.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_report_file.SelectParameters.Clear();
        //SDS_report_file.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_report_file.SelectCommand = SQLInjectionReplaceAll("esp_TreatyCase_report_file");
        //SDS_report_file.SelectParameters.Add("seno", SQLInjectionReplace(ViewState["seno"].ToString()));
        //System.Data.DataView dv_emp = (DataView)SDS_report_file.Select(new DataSourceSelectArguments());
        //DataTable dt_file = ((DataView)SDS_report_file.Select(DataSourceSelectArguments.Empty)).Table;

        #region --- query ---
        DataTable dt_file = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;
            sqlCmd.CommandText = @"esp_TreatyCase_report_file";
            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                sqlDA.Fill(dt_file);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );
                oRCM.ErrorExceptionDataToDB(logMail);
            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion

        dt_file.TableName = "檔案";
        doc.MailMerge.CleanupOptions = Aspose.Words.Reporting.MailMergeCleanupOptions.RemoveUnusedFields;
        doc.MailMerge.CleanupOptions = Aspose.Words.Reporting.MailMergeCleanupOptions.RemoveUnusedRegions;
        doc.MailMerge.ExecuteWithRegions(dt_file);
        saveFile(string.Format("{0}.pdf", 議約編號), myword.exportPDF());

    }
    private void saveFile(string fileName, MemoryStream ms)
    {
        fileName = Server.HtmlEncode(fileName);
        string contenttype = "";
        string filetype = fileName.Split('.')[1].ToLower();

        if (filetype == "docx")
            contenttype = "Application/msword";
        else if (filetype == "pdf")
            contenttype = "Application/pdf";

        Response.Clear();
        Response.ContentType = contenttype;
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("UTF-8");

        fileName = Server.UrlPathEncode(fileName).Replace("/", "").Replace("..", "");
        string strContentDisposition = String.Format("{0}; filename=\"{1}\"", "attachment", fileName);
        fileName = System.Web.HttpUtility.UrlEncode(fileName, System.Text.Encoding.UTF8);
        Response.AddHeader("Content-Disposition", strContentDisposition);

        Response.Buffer = true;
        ms.WriteTo(Response.OutputStream);
        ms.Close();
        Response.End();
    }
    protected void BT_Print_Tag_Click(object sender, EventArgs e)
    {
        string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
        string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=image&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["seno"]);
        Response.Redirect(url, true);
    }
    protected void BT_Print_Excel_Click(object sender, EventArgs e)
    {
        string strRSPath = System.Web.Configuration.WebConfigurationManager.AppSettings["ReportServicePath"];
        string url = string.Format("{0}/rptTreatyCustCatalog&rc:ToolBar=false&rs:Format=excel&rc:Parameters=false&rs:ClearSession=true&tc_seno={1}&casetype=1", strRSPath, (string)ViewState["seno"]);
        Response.Redirect(url, true);
    }
    protected void BT_End_Click(object sender, EventArgs e)
    {
        //SDS_Inspect_count.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
        //SDS_Inspect_count.SelectParameters.Clear();
        //SDS_Inspect_count.SelectCommandType = SqlDataSourceCommandType.StoredProcedure;
        //SDS_Inspect_count.SelectCommand = SQLInjectionReplaceAll("esp_treatyCase_Inspect_getUnInspect") ;
        //SDS_Inspect_count.SelectParameters.Add("seno", SQLInjectionReplaceAll( ViewState["seno"].ToString()));
        //for (int i = 0; i < this.SDS_Inspect_count.SelectParameters.Count; i++)
        //{
        //    SDS_Inspect_count.SelectParameters[i].ConvertEmptyStringToNull = false;
        //}
        //SDS_Inspect_count.DataBind();
        //System.Data.DataView dv_count = (DataView)SDS_Inspect_count.Select(new DataSourceSelectArguments());

        #region --- query ---
        DataTable dt = new DataTable();
        using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
        {
            SqlCommand sqlCmd = new SqlCommand();
            sqlCmd.Connection = sqlConn;
            sqlCmd.CommandType = CommandType.StoredProcedure;

            sqlCmd.CommandText = @"esp_treatyCase_Inspect_getUnInspect";

            // --- 避免匯出查詢過久而當掉 --- //
            sqlCmd.CommandTimeout = 0;
            sqlCmd.Parameters.Clear();
            sqlCmd.Parameters.AddWithValue("@seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));

            try
            {
                sqlConn.Open();
                SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);

                sqlDA.Fill(dt);
            }
            catch (Exception ex)
            {
                // --- 執行異常通報 --- //
                RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                    ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                    Request,
                    Response,
                    ex
                    );

                oRCM.ErrorExceptionDataToDB(logMail);

            }
            finally
            {
                sqlConn.Close();
            }
        }

        #endregion
        DataView dv_count = dt.DefaultView;

        if (dv_count.Count >= 1)
        {
            if (dv_count[0][0].ToString() != "0")
            {
                StringBuilder script = new StringBuilder("<script type='text/javascript'>alert('還有審查人未審查,不能結案!');</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }
            else
            {
                //SDS_NR.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString;
                //SDS_NR.UpdateParameters.Clear();
                //SDS_NR.UpdateCommandType = SqlDataSourceCommandType.StoredProcedure;
                //SDS_NR.UpdateCommand = SQLInjectionReplaceAll("esp_TreatyCase_status_update");
                //SDS_NR.UpdateParameters.Add("tc_seno",   SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                //SDS_NR.UpdateParameters.Add("tc_degree", SQLInjectionReplaceAll("Z"));
                //SDS_NR.UpdateParameters.Add("tc_status", SQLInjectionReplaceAll("Z"));
                //SDS_NR.UpdateParameters.Add("empno", SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                //SDS_NR.Update();
                #region --- modify ---
                using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString))
                {
                    SqlCommand sqlCmd = new SqlCommand();
                    sqlCmd.Connection = sqlConn;
                    sqlCmd.CommandType = CommandType.StoredProcedure;
                    sqlCmd.CommandText = @"esp_TreatyCase_status_update";
                    sqlCmd.CommandTimeout = 0;
                    sqlCmd.Parameters.Clear();
                    sqlCmd.Parameters.AddWithValue("@tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
                    sqlCmd.Parameters.AddWithValue("@tc_degree", "Z");
                    sqlCmd.Parameters.AddWithValue("@tc_status", "Z");
                    sqlCmd.Parameters.AddWithValue("@empno", oRCM.SQLInjectionReplaceAll(ViewState["empNo"].ToString()));
                    try
                    {
                        sqlConn.Open();
                        sqlCmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {

                        // --- 執行異常通報 --- //
                        RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                            ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString,
                            Request,
                            Response,
                            ex
                            );

                        oRCM.ErrorExceptionDataToDB(logMail);
                    }
                    finally
                    {
                        sqlConn.Close();
                    }
                }
                #endregion
                Treaty_log(ViewState["seno"].ToString(), "結案", "", "", "treaty\\TreatyCase_View.aspx");
                StringBuilder script = new StringBuilder("<script type='text/javascript'>EndCase(" + ViewState["seno"].ToString() + ")</script>");
                ScriptManager.RegisterStartupScript(Page, Page.GetType(), "msg", script.ToString(), false);
            }
        }
    }
    protected void LB_request_Click(object sender, EventArgs e)
    {
        string strEngage_Path = System.Configuration.ConfigurationManager.AppSettings["TreatyURL"].ToString();
        string strWinOpen = string.Format("{0}/treaty/webpage/TreatyApply_view.aspx?contno={1}", strEngage_Path, txtComplexNo.Text);
        string script = @" <script> window.open('" + strWinOpen + "', '_blank', 'toolbar=yes, scrollbars=yes, resizable=yes, top=50, left=50, width=900, height=600'); </script>";
        Page.ClientScript.RegisterStartupScript(this.GetType(), "N_case", script);

    }
    protected void LB_廠商編號_Click(object sender, EventArgs e)
    {
        LinkButton CompInfo = sender as LinkButton;
        string Compno = CompInfo.Attributes["Compno"].Trim();
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfo('{0}'); </script> ", Compno), false);

    }
    protected void IBx_廠商編號_Click(object sender, EventArgs e)
    {
        ImageButton CompInfo = sender as ImageButton;
        string Compno = CompInfo.Attributes["Compno"].Trim();
        ScriptManager.RegisterStartupScript(this, this.GetType(), string.Empty, string.Format(@" <script type='text/javascript'> showCompInfoDialog('{0}'); </script> ", Compno), false);

    }
    protected void BT_計價取消_Click(object sender, EventArgs e)
    {
        SqlCommand oCmd = new SqlCommand();
        oCmd.Connection = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["CS_treaty"].ConnectionString);
        oCmd.CommandText = "esp_TreatyCase_valuation";
        oCmd.CommandType = CommandType.StoredProcedure;
        oCmd.Parameters.AddWithValue("tc_seno", oRCM.SQLInjectionReplaceAll(ViewState["seno"].ToString()));
        oCmd.Parameters.AddWithValue("empno", oRCM.SQLInjectionReplaceAll(ViewState["empno"].ToString()));
        oCmd.Parameters.AddWithValue("mode", "case_cancle");
        SqlDataAdapter oda = new SqlDataAdapter(oCmd);
        DataSet ds = new DataSet();
        oda.Fill(ds, "myTable");
        oCmd.Dispose();
        oda.Dispose();
    }
}