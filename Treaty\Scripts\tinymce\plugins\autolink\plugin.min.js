/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.0 (2021-10-11)
 */
!function(){"use strict";function o(e,t){var n;return t<0&&(t=0),3!==e.nodeType||(n=e.data.length)<t&&(t=n),t}function p(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setStart(t,o(t,n)):e.setStartBefore(t)}function A(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setEnd(t,o(t,n)):e.setEndAfter(t)}function i(e,t,n){var o,i,r,a,d,s,l=e.getParam("autolink_pattern",v),f=e.getParam("default_link_target",!1);if("A"!==e.selection.getNode().tagName){var c=e.selection.getRng().cloneRange();if(c.startOffset<5){if(!(a=c.endContainer.previousSibling)){if(!c.endContainer.firstChild||!c.endContainer.firstChild.nextSibling)return;a=c.endContainer.firstChild.nextSibling}if(p(c,a,d=a.length),A(c,a,d),c.endOffset<5)return;o=c.endOffset,i=a}else{if(3!==(i=c.endContainer).nodeType&&i.firstChild){for(;3!==i.nodeType&&i.firstChild;)i=i.firstChild;3===i.nodeType&&(p(c,i,0),A(c,i,i.nodeValue.length))}o=1===c.endOffset?2:c.endOffset-1-t}for(var g,u=o;p(c,i,2<=o?o-2:0),A(c,i,1<=o?o-1:0),--o," "!==(s=c.toString())&&""!==s&&160!==s.charCodeAt(0)&&0<=o-2&&s!==n;);(g=c.toString())===n||" "===g||160===g.charCodeAt(0)?(p(c,i,o),A(c,i,u),o+=1):(0===c.startOffset?p(c,i,0):p(c,i,o),A(c,i,u)),h=c.toString(),/[?!,.;:]/.test(h.charAt(h.length-1))&&A(c,i,u-1);var h,C,m,y,k=(h=c.toString().trim()).match(l),w=e.getParam("link_default_protocol","http","string");k&&((m=C=k[0]).length>=(y="www.").length&&m.substr(0,0+y.length)===y?C=w+"://"+C:-1===C.indexOf("@")||/^([A-Za-z][A-Za-z\d.+-]*:\/\/)|mailto:/.test(C)||(C="mailto:"+C),r=e.selection.getBookmark(),e.selection.setRng(c),e.execCommand("createlink",!1,C),!1!==f&&e.dom.setAttrib(e.selection.getNode(),"target",f),e.selection.moveToBookmark(r),e.nodeChanged())}}var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),r=tinymce.util.Tools.resolve("tinymce.Env"),v=new RegExp("^"+/(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)[A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*(?::\d+)?(?:\/(?:[-+~=.,%()\/\w]*[-+~=%()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?/g.source+"$","i");e.add("autolink",function(e){var t,n;(t=e).on("keydown",function(e){13===e.keyCode&&i(t,-1,"")}),r.browser.isIE()?t.on("focus",function(){if(!n){n=!0;try{t.execCommand("AutoUrlDetect",!1,!0)}catch(e){}}}):(t.on("keypress",function(e){41===e.keyCode&&i(t,-1,"(")}),t.on("keyup",function(e){32===e.keyCode&&i(t,0,"")}))})}();