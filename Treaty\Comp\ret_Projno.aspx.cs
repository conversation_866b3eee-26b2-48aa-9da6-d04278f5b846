﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Configuration;
using System.Data;
using System.Xml;
using Newtonsoft.Json;


public partial class web_ret_proj : System.Web.UI.Page
{

    // --- 加入共用 Function Class --- //
    internal RemoveCheckMax oRCM = new RemoveCheckMax();
    public class Proj
    {
        private string orgcd;
        private string pojno;
        private string pojcname;
        private string pojdept;       //執行部門
        private string pojleader;     //主持人
        private string pojleaderempno;//主持人工號
        private string pojleadertel;  //主持人電話
        private string profitctr;     // 利潤中心
        private string begdate_8;     // 預算動支起始日
        private string enddate_8;     // 預算動支結束日
        private string pojamt;        //  合約金額
        private string nocontact;     //  聯絡人工號
        private string nmcontact;     //  聯絡人名稱
        private string nocontact_tel; //  聯絡人電話
        private string nocochrg;      //  協同主持人工號
        private string nmcochrg;      //  協同主持人
        private string cochrg_tel;    //  協同主持人電話
        private string taxcd;         //  應稅/免稅
        private string conttype2;

        public string c_orgcd
        {
            get { return orgcd; }
            set { orgcd = value; }
        }
        public string c_pojno
        {
            get { return pojno; }
            set { pojno = value; }
        }
        public string c_pojcname
        {
            get { return pojcname; }
            set { pojcname = value; }
        }
        public string c_pojdept
        {
            get { return pojdept; }
            set { pojdept = value; }
        }
        public string c_pojleader
        {
            get { return pojleader; }
            set { pojleader = value; }
        }
        public string c_pojleaderempno
        {
            get { return pojleaderempno; }
            set { pojleaderempno = value; }
        }
        public string c_pojleadertel
        {
            get { return pojleadertel; }
            set { pojleadertel = value; }
        }
        public string c_profitctr     // 利潤中心
        {
            get { return profitctr; }
            set { profitctr = value; }
        }
        public string c_begdate_8     // 預算動支起始日
        {
            get { return begdate_8; }
            set { begdate_8 = value; }
        }
        public string c_enddate_8      // 預算動支結束日
        {
            get { return enddate_8; }
            set { enddate_8 = value; }
        }
        public string c_pojamt        //  合約金額
        {
            get { return pojamt; }
            set { pojamt = value; }
        }
        public string c_nocontact     //  聯絡人工號
        {
            get { return nocontact; }
            set { nocontact = value; }
        }
        public string c_nmcontact     //  聯絡人名稱
        {
            get { return nmcontact; }
            set { nmcontact = value; }
        }
        public string c_nocontact_tel  //  聯絡人電話
        {
            get { return nocontact_tel; }
            set { nocontact_tel = value; }
        }
        public string c_nocochrg       //  協同主持人工號
        {
            get { return nocochrg; }
            set { nocochrg = value; }
        }
        public string c_nmcochrg       //  協同主持人
        {
            get { return nmcochrg; }
            set { nmcochrg = value; }
        }
        public string c_cochrg_tel    //  協同主持人電話
        {
            get { return cochrg_tel; }
            set { cochrg_tel = value; }
        }
        public string c_taxcd        //  應稅/免稅
        {
            get { return taxcd; }
            set { taxcd = value; }
        }
        public string c_conttype2
        {
            get { return conttype2; }
            set { conttype2 = value; }
        }


    }
    protected void Page_Load(object sender, System.EventArgs e)
    {
        Proj u = new Proj();
        SqlCommand oCmd = new SqlCommand();
        if (Session["projno"] != null)
        {
            //string strSQL = @" SELECT top 1 s20_orgcd , s20_pojno , s20_pojcname,s20_pjdept,s20_noinchrg,s20_nminchrg,s20_inchrg_tel,s20_profitctr,s20_begdate_8,s20_enddate_8,s20_pojamt,s20_nocontact,s20_nmcontact,s20_nocontact_tel,s20_nocochrg,s20_nmcochrg,s20_cochrg_tel,s20_taxcd,p_conttype2  FROM Vprs020 where s20_pojno = @pojno ";
            //oCmd.Parameters.AddWithValue("@pojno", Session["projno"].ToString());
            //oCmd.CommandText = strSQL;
            //oCmd.Connection = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString);
            //SqlDataAdapter oda = new SqlDataAdapter(oCmd);
            //DataSet ds = new DataSet();
            //oda.Fill(ds);

            DataSet ds = new DataSet();
            #region --- query ---

            using (SqlConnection sqlConn = new SqlConnection(ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString))
            {
                SqlCommand sqlCmd = new SqlCommand();
                sqlCmd.Connection = sqlConn;
                sqlCmd.CommandType = CommandType.Text;

                sqlCmd.CommandText = @"SELECT top 1 s20_orgcd , s20_pojno , s20_pojcname,s20_pjdept,s20_noinchrg,s20_nminchrg,s20_inchrg_tel,s20_profitctr,s20_begdate_8,s20_enddate_8,s20_pojamt,s20_nocontact,s20_nmcontact,s20_nocontact_tel,s20_nocochrg,s20_nmcochrg,s20_cochrg_tel,s20_taxcd,p_conttype2  FROM v_prs020 where s20_pojno = @pojno";

                // --- 避免匯出查詢過久而當掉 --- //
                sqlCmd.CommandTimeout = 0;

                sqlCmd.Parameters.Clear();
                sqlCmd.Parameters.AddWithValue("@pojno", oRCM.SQLInjectionReplaceAll(Session["projno"].ToString()));

                try
                {
                    sqlConn.Open();

                    SqlDataAdapter sqlDA = new SqlDataAdapter(sqlCmd);
                    sqlDA.Fill(ds);

                }
                catch (Exception ex)
                {

                    // --- 執行異常通報 --- //
                    RemoveCheckMax.ErrorLogMail logMail = new RemoveCheckMax.ErrorLogMail(
                        ConfigurationManager.ConnectionStrings["ConnString"].ConnectionString,
                        Request,
                        Response,
                        ex
                        );

                    oRCM.ErrorExceptionDataToDB(logMail);

                }
                finally
                {
                    sqlConn.Close();
                }
            }

            #endregion            
            if (ds.Tables[0].DefaultView.Count == 1)
            {
                u.c_orgcd = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_orgcd"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojno = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_pojno"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojcname = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_pojcname"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojdept = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_pjdept"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojleaderempno = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_noinchrg"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojleader = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nminchrg"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojleadertel = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_inchrg_tel"].ToString().Trim().Replace("\"", "\\\""));

                u.c_profitctr = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_profitctr"].ToString().Trim().Replace("\"", "\\\""));
                u.c_begdate_8 = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_begdate_8"].ToString().Trim().Replace("\"", "\\\""));
                u.c_enddate_8 = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_enddate_8"].ToString().Trim().Replace("\"", "\\\""));
                u.c_pojamt = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_pojamt"].ToString().Trim().Replace("\"", "\\\""));
                u.c_nocontact = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nocontact"].ToString().Trim().Replace("\"", "\\\""));
                u.c_nmcontact = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nmcontact"].ToString().Trim().Replace("\"", "\\\""));
                u.c_nocontact_tel = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nocontact_tel"].ToString().Trim().Replace("\"", "\\\""));
                u.c_nocochrg = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nocochrg"].ToString().Trim().Replace("\"", "\\\""));
                u.c_nmcochrg = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_nmcochrg"].ToString().Trim().Replace("\"", "\\\""));
                u.c_cochrg_tel = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_cochrg_tel"].ToString().Trim().Replace("\"", "\\\""));
                u.c_taxcd = Server.HtmlEncode(ds.Tables[0].Rows[0]["s20_taxcd"].ToString().Trim().Replace("\"", "\\\""));
                u.c_conttype2 = Server.HtmlEncode(ds.Tables[0].Rows[0]["p_conttype2"].ToString().Trim().Replace("\"", "\\\""));
            }
            if (ds.Tables[0].DefaultView.Count == 0)
            {
                u.c_orgcd = "";
                u.c_pojno = "error0";
                u.c_pojcname = "";
            }
            if (ds.Tables[0].DefaultView.Count > 1)
            {
                u.c_orgcd = "";
                u.c_pojno = "error2";
                u.c_pojcname = "";
            }
        }
        else
        {
            u.c_orgcd = "";
            u.c_pojno = "error0";
            u.c_pojcname = "";
        }
        string j = JsonConvert.SerializeObject(u);//透過JSON.NET將物件轉為JSON格式
        if (!string.IsNullOrEmpty(Request.QueryString["callback"]))  //判斷是否有傳入callback的function名稱
        {
            string CallBackFunction = Server.HtmlEncode(Request.QueryString["callback"]);
            j = CallBackFunction + "(" + j + ");";      //傳回的內容加上呼叫callback的function
        }
        Response.Write(j);  //輸出JSONP的內容
    }

}